﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m5CF7F52D86C5B13E9D6018E08EBFC9FEE68FFD12 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mC1B4DF333E4618654773D73CC5CCF6106BC4FE2C (void);
extern void AnchorsChangedEventUnit_get_hookName_mD996B5E207C2889FE47E181D78FEF149398426A9 (void);
extern void AnchorsChangedEventUnit_AssignArguments_m899A2B6F52D8EB9D919CF8FC138FF8DF052AF403 (void);
extern void AnchorsChangedEventUnit__ctor_mB3BF0403E6002D1FDA58EFB7F2921E8C07215414 (void);
extern void CameraFrameReceivedEventUnit_get_frameEventArgs_m2CA34906D7D15DDD541EC38B7862B6CB0AB500E8 (void);
extern void CameraFrameReceivedEventUnit_set_frameEventArgs_m9D477159407F84279F1EF8E7619B871AA4F542FE (void);
extern void CameraFrameReceivedEventUnit_get_hookName_m622599602E2C6D5C5B718B2B08DDAA3563C704C3 (void);
extern void CameraFrameReceivedEventUnit_get_MessageListenerType_mDE66642906108E26851B7CC208C8BCE367B24AEA (void);
extern void CameraFrameReceivedEventUnit_Definition_m3BA0B54D3F778C2FF3AAA37693C69E54A5836DE2 (void);
extern void CameraFrameReceivedEventUnit_AssignArguments_m3632CC04DC5E4651074CDF8353FF93DFA5E62330 (void);
extern void CameraFrameReceivedEventUnit__ctor_m49D54CB2470E3CB3D522BD5ACA7E13FF47E6CBF6 (void);
extern void EnvironmentProbesChangedEventUnit_get_hookName_m42A4B87F47C4FC4364CA15F859298E8FAF076116 (void);
extern void EnvironmentProbesChangedEventUnit_AssignArguments_m199E25B95E3DC8D3A4E2A3205959366BA8B6B7C6 (void);
extern void EnvironmentProbesChangedEventUnit__ctor_m618569E9356D0FAC231385EF74091BD7EF889BE6 (void);
extern void FacesChangedEventUnit_get_hookName_m25C307FFC3651366618356692EBB21454D2802D8 (void);
extern void FacesChangedEventUnit_AssignArguments_mBD69A01EEA5552FF75E62DA4DE628CD7B0C3FB81 (void);
extern void FacesChangedEventUnit__ctor_m33A7C04F6D09F5980669FA57262F17B9AB16E722 (void);
extern void FaceUpdatedEventUnit_get_faceOut_m4C064F7BE0FDC5D28BAB8C72683F60B5399B091D (void);
extern void FaceUpdatedEventUnit_set_faceOut_mA1675257B969683B3E210624EFADE5F0F24A4811 (void);
extern void FaceUpdatedEventUnit_get_hookName_mC91F19ECC90B14DFE7A12AFC261B7904E875F79E (void);
extern void FaceUpdatedEventUnit_get_MessageListenerType_m9569E3DA99C317D7979CA8E3DDF3AA80D717601E (void);
extern void FaceUpdatedEventUnit_Definition_mF72B08663AB57D86B075994731416C5B4B787138 (void);
extern void FaceUpdatedEventUnit_AssignArguments_m3090FCDC05B47F2147891071BE013E615994E067 (void);
extern void FaceUpdatedEventUnit__ctor_m3B3B46D3477D89D51EE72D0A636B5D982AC94E93 (void);
extern void HumanBodiesChangedEventUnit_get_hookName_m67E5122D4D2D723303916F0C72BCA472CAF92768 (void);
extern void HumanBodiesChangedEventUnit_AssignArguments_mDCA3EF5F7512E95706B68E4DD9F8DB9D253FD1B4 (void);
extern void HumanBodiesChangedEventUnit__ctor_m67A7804169D1D1C464DE511BB251D2D1852C3C03 (void);
extern void ParticipantsChangedEventUnit_get_hookName_mA4CDFD4FD4E062B9967AED9A88C976959565C5A6 (void);
extern void ParticipantsChangedEventUnit_AssignArguments_m39BEEE8BBB7557918AC369100EEBED8441FC3948 (void);
extern void ParticipantsChangedEventUnit__ctor_m3ACA8D1E560935861985B00AA5BEADC1852420C6 (void);
extern void PlanesChangedEventUnit_get_hookName_m48C3C8CD9DE2A31ADDD018D9BC38E58D4A861CDA (void);
extern void PlanesChangedEventUnit_AssignArguments_m8920B6D4EFA5405677DD7E120F03079EA92C479D (void);
extern void PlanesChangedEventUnit__ctor_mE065B63E0CE8FEE5F3FAF6751A9FA5081694E9A6 (void);
extern void PointCloudsChangedEventUnit_get_hookName_m361A47DC7CE10FBC6D4817EB3C49317C335F2DFE (void);
extern void PointCloudsChangedEventUnit_AssignArguments_mAA9FA32A721AA48E7643E8EAFE99C859AE4A0E22 (void);
extern void PointCloudsChangedEventUnit__ctor_m1FE03A5858ADDCF6CA0A326677C183993B91C121 (void);
extern void SessionStateChangedEventUnit_get_sessionStateOut_m0A6C8851E8BE80C79AEFAE297A6B8A47F68445B9 (void);
extern void SessionStateChangedEventUnit_set_sessionStateOut_mA37AE57139361EFD0003F0FF225B9A917359F04C (void);
extern void SessionStateChangedEventUnit_get_hookName_mAEFA9468C32ABAA500B9EA29659785F58AAED656 (void);
extern void SessionStateChangedEventUnit_StartListening_mCD5FDDF66624175226E58047C04636B183F6E640 (void);
extern void SessionStateChangedEventUnit_StopListening_mF5443BFC057623A8AB81E12C257FACB4771EBCB8 (void);
extern void SessionStateChangedEventUnit_HandleStateChange_m59A2A9D8AE5794385BC8A62BFA35EEF1183F4770 (void);
extern void SessionStateChangedEventUnit_Definition_m13B3E0C78A229D7390013899679766F1F3D04A6A (void);
extern void SessionStateChangedEventUnit_AssignArguments_m112E323BB24EABC6CF7D933169F9A5FEA88F3B56 (void);
extern void SessionStateChangedEventUnit__ctor_m88A0475A1E868228B52D4994A322018EAA9BC80B (void);
extern void TrackedImagesChangedEventUnit_get_hookName_m71D20EC82716ADA07A7A4E12FF86561556111BEC (void);
extern void TrackedImagesChangedEventUnit_AssignArguments_m99F331B5C10B48D05E6243C433A29FF8C2945EF7 (void);
extern void TrackedImagesChangedEventUnit__ctor_mD97F8007E5D7D9FB4765DE62295E0646A63C85EF (void);
extern void TrackedObjectsChangedEventUnit_get_hookName_m4CE8FAAAF5FD10E9B4C8B99D9576083C0F11DE6E (void);
extern void TrackedObjectsChangedEventUnit_AssignArguments_m5D3602536AD4FF039FDAC2A7F9CD3AFBC50F5ED0 (void);
extern void TrackedObjectsChangedEventUnit__ctor_mDBAAA2924F415BB174398367F96C61794310E52C (void);
extern void ARAnchorManagerListener_OnTrackablesChanged_m4334EFEB7FF640C2029A8567B7F33FEA2F6012BF (void);
extern void ARAnchorManagerListener_RegisterTrackablesChangedDelegate_mB98A79FE77937C7B254E99A4DB9280987DF96A3C (void);
extern void ARAnchorManagerListener_UnregisterTrackablesChangedDelegate_mD523B3EE5EBC73350C2121DC8CDC326FAB330B3C (void);
extern void ARAnchorManagerListener__ctor_m3446119DC76812D6B10825B9E3DF3F54F59F210B (void);
extern void ARCameraManagerListener_OnEnable_m3FCCA525327452EC89BEB0B6192E785675354BBD (void);
extern void ARCameraManagerListener_OnDisable_mE4E3D8277DFBA681900BD3B3F9F2335C2DC7BA27 (void);
extern void ARCameraManagerListener_OnFrameReceived_mC4DF9EC830D8147C5E4604BBE32DD5D289C7E901 (void);
extern void ARCameraManagerListener__ctor_mAC77E9D72A839372BB3B36491951FBC01AF001E8 (void);
extern void AREnvironmentProbeManagerListener_OnTrackablesChanged_mFFE922C02832EEF6AC8F645762C471B3BEF8B93D (void);
extern void AREnvironmentProbeManagerListener_RegisterTrackablesChangedDelegate_m872353C32A0321432B32E69E1BC8FF679B046962 (void);
extern void AREnvironmentProbeManagerListener_UnregisterTrackablesChangedDelegate_m7D1DB80EA456F3BEA9A0D7A4184467B8925478E3 (void);
extern void AREnvironmentProbeManagerListener__ctor_mDDB6426B47643A97BB37711BA5C7031FDDDD9171 (void);
extern void ARFaceListener_OnFaceUpdated_mEB4873976DAAC601F816A95E0E36C93CC6C43D51 (void);
extern void ARFaceListener_OnEnable_mFB7CEACFC165E077636A31D574F41CAEFB2737A1 (void);
extern void ARFaceListener_OnDisable_m97C2530EC7E803706F1E64CFCCEBAE565D439525 (void);
extern void ARFaceListener__ctor_mF6F04669987369D3F349D4E6817E9E84A1C6EC12 (void);
extern void ARFaceManagerListener_OnTrackablesChanged_m5F42EC3570B5D34BAE69E833677980E334D97E84 (void);
extern void ARFaceManagerListener_RegisterTrackablesChangedDelegate_m1C8CED37ADBCCDE1125F632BCC683C6DF4210E47 (void);
extern void ARFaceManagerListener_UnregisterTrackablesChangedDelegate_m98D01C89E58172CBA368330001F3805AB37D2014 (void);
extern void ARFaceManagerListener__ctor_m5FAC77D48D54B77D1AF6767F9CC1A9FD8C47579D (void);
extern void ARHumanBodyManagerListener_OnTrackablesChanged_mC8B24E64F660D8C4F29C2567E233795C7905E8D8 (void);
extern void ARHumanBodyManagerListener_RegisterTrackablesChangedDelegate_mC4B68B78F1C8A14D91BB47766F0D9CFBC3E76665 (void);
extern void ARHumanBodyManagerListener_UnregisterTrackablesChangedDelegate_mF252B1724CB1EE3FB8C778EA76B178D1189D9A6F (void);
extern void ARHumanBodyManagerListener__ctor_m1C30D9B89578DFD325B5670B6864480FEA735595 (void);
extern void ARParticipantManagerListener_OnTrackablesChanged_mFBD305D70AFFBB9ADDE54456B499CEE28B7586F0 (void);
extern void ARParticipantManagerListener_RegisterTrackablesChangedDelegate_m4299A76DBAD136E3E3008B0E13A5C59986C70F25 (void);
extern void ARParticipantManagerListener_UnregisterTrackablesChangedDelegate_mCF7B6C59B0BE204CF23E8AD061B1FB5B8A91FA4A (void);
extern void ARParticipantManagerListener__ctor_m49FD1669DE44469CC5E5588DFB5673EB92D35D66 (void);
extern void ARPlaneManagerListener_OnTrackablesChanged_mB2C63B0FEA0C321CE64E042C4CC659F3858F7406 (void);
extern void ARPlaneManagerListener_RegisterTrackablesChangedDelegate_m41E2E4D926F04FA4902C694C68832508349C47AF (void);
extern void ARPlaneManagerListener_UnregisterTrackablesChangedDelegate_m8E80A7CE9E87E8FCB787B341E5F1B34D8AFC8B0A (void);
extern void ARPlaneManagerListener__ctor_mC0BA10E19292774E8F44C7BAB23725C169A0F5BD (void);
extern void ARPointCloudManagerListener_OnTrackablesChanged_m37F4B0894E58642522DD88A5B70B2D6425D171F2 (void);
extern void ARPointCloudManagerListener_RegisterTrackablesChangedDelegate_m8FDD10C3610B31F4C6755847ABFF706E74EEA786 (void);
extern void ARPointCloudManagerListener_UnregisterTrackablesChangedDelegate_m211D23F127AE987796DD35083C3BBDB15ED63928 (void);
extern void ARPointCloudManagerListener__ctor_mAC662B594114A7F330A910B4DF50C7D489B0C0D8 (void);
extern void ARTrackedImageManagerListener_OnTrackablesChanged_mB7342F42759F03B7495C3863A66339D21A50B229 (void);
extern void ARTrackedImageManagerListener_RegisterTrackablesChangedDelegate_m012C380AFF5DD189E5AD3C059C3783B6AE369DF2 (void);
extern void ARTrackedImageManagerListener_UnregisterTrackablesChangedDelegate_m47ABAAD0DD04A2519A59F2F0F98C01BDB4D8DD98 (void);
extern void ARTrackedImageManagerListener__ctor_m80CAF9FEAA38FDC8ADF8789E1DEE5F51E899D556 (void);
extern void ARTrackedObjectManagerListener_OnTrackablesChanged_m63FB84E742A86FFC4D163A0B17B8989491A51EE6 (void);
extern void ARTrackedObjectManagerListener_RegisterTrackablesChangedDelegate_m23EA19D74EBB109B2B74AF9DB9D8597FEA16A0E7 (void);
extern void ARTrackedObjectManagerListener_UnregisterTrackablesChangedDelegate_m95CD86B38632CF9A9E1C50D027E3727F90ED7EF3 (void);
extern void ARTrackedObjectManagerListener__ctor_mF863E16AC8DDCE13BD309673BD24A4B10EDD5AA7 (void);
extern void GetAnchorsUnit__ctor_mE0D0675CC37CA1DA87C83FC0BA153B0E0462B4D2 (void);
extern void GetEnvironmentProbesUnit__ctor_m99D12CCEB247C2FA0DF000C258C9D001B1596C63 (void);
extern void GetFacesUnit__ctor_mAED50CDA1039DE45158B1F90E10AD3B9C2270121 (void);
extern void GetHumanBodiesUnit__ctor_m91BE6AF7CFBE7B218BB781F65F17E88224DA4CFF (void);
extern void GetParticipantsUnit__ctor_m0D8567DF8B8285D04B1A590EEF7E70346015A416 (void);
extern void GetPlanesUnit__ctor_mD0526AD4E7F72FEEACEB9502815B5FA3CC989966 (void);
extern void GetPointCloudsUnit__ctor_mCCE4B499918A3416C73072C13E9F7FCBC37B5261 (void);
extern void GetRaycastsUnit__ctor_mE784813F0C8D8392C75C397DED11DC88B52CF77E (void);
extern void GetTrackedImagesUnit__ctor_m2007F922FF32CD8AB54CB65F3FE4A6B30934A808 (void);
extern void GetTrackedObjectsUnit__ctor_m7178E4143A9C53B511F82A696C70672E0C88160A (void);
extern void SessionStateSwitchUnit_get_flowIn_m667548B27056B2F6F27B63DFCF4412BBAE6637A5 (void);
extern void SessionStateSwitchUnit_set_flowIn_mD8F159479E3884269A7D2057D7F3E8C52B2923D5 (void);
extern void SessionStateSwitchUnit_get_sessionStateIn_m4E966BABF18BE40C7178158088CC8DEA31893939 (void);
extern void SessionStateSwitchUnit_set_sessionStateIn_m39EB7D50DFEE0306E0C4F690E06D8B6D246B1AFB (void);
extern void SessionStateSwitchUnit_get_none_mCDA812C5D868B12D04C020ED27ED0F886EE0963E (void);
extern void SessionStateSwitchUnit_set_none_mE058B6C1A72B0BD0DC6BAEA629EC6C8A55BD273C (void);
extern void SessionStateSwitchUnit_get_unsupported_m911986D3ADF69617D9E6DFFB367926BBA3639439 (void);
extern void SessionStateSwitchUnit_set_unsupported_m683621DAFF0725F51FAB5D0FB9CB63F5731E53A1 (void);
extern void SessionStateSwitchUnit_get_checkingAvailability_m3EF1098F57D30B08B8C7587F9D94B64A4E650ED9 (void);
extern void SessionStateSwitchUnit_set_checkingAvailability_m0D10A7BFD1348E9694B381482E90F5974E3CF246 (void);
extern void SessionStateSwitchUnit_get_needsInstall_mDBE283471C7F3B8C8A1D6962DAFECBC81CC7071E (void);
extern void SessionStateSwitchUnit_set_needsInstall_m6C03848DA29361417CBE17AAC6EF641AE4946A95 (void);
extern void SessionStateSwitchUnit_get_installing_mAB1C0C05D8CAD09DE7EA9177806621D1263E120F (void);
extern void SessionStateSwitchUnit_set_installing_m91BC0E0D467AB4CDE4A9E87F43ABA98874D7BAF4 (void);
extern void SessionStateSwitchUnit_get_ready_m5FDBB155FE257620F68167B4A7A1DEEB597491D2 (void);
extern void SessionStateSwitchUnit_set_ready_m4CF3C56F9F24CDCFE9678F22BBAA988BF7AA6FD3 (void);
extern void SessionStateSwitchUnit_get_sessionInitializing_mDAB4A059DA69D91C4A7E3ED9B207A31F64040746 (void);
extern void SessionStateSwitchUnit_set_sessionInitializing_mB3A2FF70C33664AFF792B183A2E39252DDFDACB2 (void);
extern void SessionStateSwitchUnit_get_sessionTracking_m46B500F7B3F7276E269EC893E52EE71478554365 (void);
extern void SessionStateSwitchUnit_set_sessionTracking_mA270750450E8D31E271811C785AE730FB61AACF0 (void);
extern void SessionStateSwitchUnit_Definition_m51005D8BC9A2A714696B3D883AB3391DEFB9477D (void);
extern void SessionStateSwitchUnit_ProcessFlow_m57E488DEC7E1E2F26E074FB49BB7F19DA905C410 (void);
extern void SessionStateSwitchUnit__ctor_m93E299F5D4B3285000E037D77A034C454C564F68 (void);
static Il2CppMethodPointer s_methodPointers[157] = 
{
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m5CF7F52D86C5B13E9D6018E08EBFC9FEE68FFD12,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mC1B4DF333E4618654773D73CC5CCF6106BC4FE2C,
	AnchorsChangedEventUnit_get_hookName_mD996B5E207C2889FE47E181D78FEF149398426A9,
	AnchorsChangedEventUnit_AssignArguments_m899A2B6F52D8EB9D919CF8FC138FF8DF052AF403,
	AnchorsChangedEventUnit__ctor_mB3BF0403E6002D1FDA58EFB7F2921E8C07215414,
	CameraFrameReceivedEventUnit_get_frameEventArgs_m2CA34906D7D15DDD541EC38B7862B6CB0AB500E8,
	CameraFrameReceivedEventUnit_set_frameEventArgs_m9D477159407F84279F1EF8E7619B871AA4F542FE,
	CameraFrameReceivedEventUnit_get_hookName_m622599602E2C6D5C5B718B2B08DDAA3563C704C3,
	CameraFrameReceivedEventUnit_get_MessageListenerType_mDE66642906108E26851B7CC208C8BCE367B24AEA,
	CameraFrameReceivedEventUnit_Definition_m3BA0B54D3F778C2FF3AAA37693C69E54A5836DE2,
	CameraFrameReceivedEventUnit_AssignArguments_m3632CC04DC5E4651074CDF8353FF93DFA5E62330,
	CameraFrameReceivedEventUnit__ctor_m49D54CB2470E3CB3D522BD5ACA7E13FF47E6CBF6,
	EnvironmentProbesChangedEventUnit_get_hookName_m42A4B87F47C4FC4364CA15F859298E8FAF076116,
	EnvironmentProbesChangedEventUnit_AssignArguments_m199E25B95E3DC8D3A4E2A3205959366BA8B6B7C6,
	EnvironmentProbesChangedEventUnit__ctor_m618569E9356D0FAC231385EF74091BD7EF889BE6,
	FacesChangedEventUnit_get_hookName_m25C307FFC3651366618356692EBB21454D2802D8,
	FacesChangedEventUnit_AssignArguments_mBD69A01EEA5552FF75E62DA4DE628CD7B0C3FB81,
	FacesChangedEventUnit__ctor_m33A7C04F6D09F5980669FA57262F17B9AB16E722,
	FaceUpdatedEventUnit_get_faceOut_m4C064F7BE0FDC5D28BAB8C72683F60B5399B091D,
	FaceUpdatedEventUnit_set_faceOut_mA1675257B969683B3E210624EFADE5F0F24A4811,
	FaceUpdatedEventUnit_get_hookName_mC91F19ECC90B14DFE7A12AFC261B7904E875F79E,
	FaceUpdatedEventUnit_get_MessageListenerType_m9569E3DA99C317D7979CA8E3DDF3AA80D717601E,
	FaceUpdatedEventUnit_Definition_mF72B08663AB57D86B075994731416C5B4B787138,
	FaceUpdatedEventUnit_AssignArguments_m3090FCDC05B47F2147891071BE013E615994E067,
	FaceUpdatedEventUnit__ctor_m3B3B46D3477D89D51EE72D0A636B5D982AC94E93,
	HumanBodiesChangedEventUnit_get_hookName_m67E5122D4D2D723303916F0C72BCA472CAF92768,
	HumanBodiesChangedEventUnit_AssignArguments_mDCA3EF5F7512E95706B68E4DD9F8DB9D253FD1B4,
	HumanBodiesChangedEventUnit__ctor_m67A7804169D1D1C464DE511BB251D2D1852C3C03,
	ParticipantsChangedEventUnit_get_hookName_mA4CDFD4FD4E062B9967AED9A88C976959565C5A6,
	ParticipantsChangedEventUnit_AssignArguments_m39BEEE8BBB7557918AC369100EEBED8441FC3948,
	ParticipantsChangedEventUnit__ctor_m3ACA8D1E560935861985B00AA5BEADC1852420C6,
	PlanesChangedEventUnit_get_hookName_m48C3C8CD9DE2A31ADDD018D9BC38E58D4A861CDA,
	PlanesChangedEventUnit_AssignArguments_m8920B6D4EFA5405677DD7E120F03079EA92C479D,
	PlanesChangedEventUnit__ctor_mE065B63E0CE8FEE5F3FAF6751A9FA5081694E9A6,
	PointCloudsChangedEventUnit_get_hookName_m361A47DC7CE10FBC6D4817EB3C49317C335F2DFE,
	PointCloudsChangedEventUnit_AssignArguments_mAA9FA32A721AA48E7643E8EAFE99C859AE4A0E22,
	PointCloudsChangedEventUnit__ctor_m1FE03A5858ADDCF6CA0A326677C183993B91C121,
	SessionStateChangedEventUnit_get_sessionStateOut_m0A6C8851E8BE80C79AEFAE297A6B8A47F68445B9,
	SessionStateChangedEventUnit_set_sessionStateOut_mA37AE57139361EFD0003F0FF225B9A917359F04C,
	SessionStateChangedEventUnit_get_hookName_mAEFA9468C32ABAA500B9EA29659785F58AAED656,
	SessionStateChangedEventUnit_StartListening_mCD5FDDF66624175226E58047C04636B183F6E640,
	SessionStateChangedEventUnit_StopListening_mF5443BFC057623A8AB81E12C257FACB4771EBCB8,
	SessionStateChangedEventUnit_HandleStateChange_m59A2A9D8AE5794385BC8A62BFA35EEF1183F4770,
	SessionStateChangedEventUnit_Definition_m13B3E0C78A229D7390013899679766F1F3D04A6A,
	SessionStateChangedEventUnit_AssignArguments_m112E323BB24EABC6CF7D933169F9A5FEA88F3B56,
	SessionStateChangedEventUnit__ctor_m88A0475A1E868228B52D4994A322018EAA9BC80B,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	TrackedImagesChangedEventUnit_get_hookName_m71D20EC82716ADA07A7A4E12FF86561556111BEC,
	TrackedImagesChangedEventUnit_AssignArguments_m99F331B5C10B48D05E6243C433A29FF8C2945EF7,
	TrackedImagesChangedEventUnit__ctor_mD97F8007E5D7D9FB4765DE62295E0646A63C85EF,
	TrackedObjectsChangedEventUnit_get_hookName_m4CE8FAAAF5FD10E9B4C8B99D9576083C0F11DE6E,
	TrackedObjectsChangedEventUnit_AssignArguments_m5D3602536AD4FF039FDAC2A7F9CD3AFBC50F5ED0,
	TrackedObjectsChangedEventUnit__ctor_mDBAAA2924F415BB174398367F96C61794310E52C,
	ARAnchorManagerListener_OnTrackablesChanged_m4334EFEB7FF640C2029A8567B7F33FEA2F6012BF,
	ARAnchorManagerListener_RegisterTrackablesChangedDelegate_mB98A79FE77937C7B254E99A4DB9280987DF96A3C,
	ARAnchorManagerListener_UnregisterTrackablesChangedDelegate_mD523B3EE5EBC73350C2121DC8CDC326FAB330B3C,
	ARAnchorManagerListener__ctor_m3446119DC76812D6B10825B9E3DF3F54F59F210B,
	ARCameraManagerListener_OnEnable_m3FCCA525327452EC89BEB0B6192E785675354BBD,
	ARCameraManagerListener_OnDisable_mE4E3D8277DFBA681900BD3B3F9F2335C2DC7BA27,
	ARCameraManagerListener_OnFrameReceived_mC4DF9EC830D8147C5E4604BBE32DD5D289C7E901,
	ARCameraManagerListener__ctor_mAC77E9D72A839372BB3B36491951FBC01AF001E8,
	AREnvironmentProbeManagerListener_OnTrackablesChanged_mFFE922C02832EEF6AC8F645762C471B3BEF8B93D,
	AREnvironmentProbeManagerListener_RegisterTrackablesChangedDelegate_m872353C32A0321432B32E69E1BC8FF679B046962,
	AREnvironmentProbeManagerListener_UnregisterTrackablesChangedDelegate_m7D1DB80EA456F3BEA9A0D7A4184467B8925478E3,
	AREnvironmentProbeManagerListener__ctor_mDDB6426B47643A97BB37711BA5C7031FDDDD9171,
	ARFaceListener_OnFaceUpdated_mEB4873976DAAC601F816A95E0E36C93CC6C43D51,
	ARFaceListener_OnEnable_mFB7CEACFC165E077636A31D574F41CAEFB2737A1,
	ARFaceListener_OnDisable_m97C2530EC7E803706F1E64CFCCEBAE565D439525,
	ARFaceListener__ctor_mF6F04669987369D3F349D4E6817E9E84A1C6EC12,
	ARFaceManagerListener_OnTrackablesChanged_m5F42EC3570B5D34BAE69E833677980E334D97E84,
	ARFaceManagerListener_RegisterTrackablesChangedDelegate_m1C8CED37ADBCCDE1125F632BCC683C6DF4210E47,
	ARFaceManagerListener_UnregisterTrackablesChangedDelegate_m98D01C89E58172CBA368330001F3805AB37D2014,
	ARFaceManagerListener__ctor_m5FAC77D48D54B77D1AF6767F9CC1A9FD8C47579D,
	ARHumanBodyManagerListener_OnTrackablesChanged_mC8B24E64F660D8C4F29C2567E233795C7905E8D8,
	ARHumanBodyManagerListener_RegisterTrackablesChangedDelegate_mC4B68B78F1C8A14D91BB47766F0D9CFBC3E76665,
	ARHumanBodyManagerListener_UnregisterTrackablesChangedDelegate_mF252B1724CB1EE3FB8C778EA76B178D1189D9A6F,
	ARHumanBodyManagerListener__ctor_m1C30D9B89578DFD325B5670B6864480FEA735595,
	ARParticipantManagerListener_OnTrackablesChanged_mFBD305D70AFFBB9ADDE54456B499CEE28B7586F0,
	ARParticipantManagerListener_RegisterTrackablesChangedDelegate_m4299A76DBAD136E3E3008B0E13A5C59986C70F25,
	ARParticipantManagerListener_UnregisterTrackablesChangedDelegate_mCF7B6C59B0BE204CF23E8AD061B1FB5B8A91FA4A,
	ARParticipantManagerListener__ctor_m49FD1669DE44469CC5E5588DFB5673EB92D35D66,
	ARPlaneManagerListener_OnTrackablesChanged_mB2C63B0FEA0C321CE64E042C4CC659F3858F7406,
	ARPlaneManagerListener_RegisterTrackablesChangedDelegate_m41E2E4D926F04FA4902C694C68832508349C47AF,
	ARPlaneManagerListener_UnregisterTrackablesChangedDelegate_m8E80A7CE9E87E8FCB787B341E5F1B34D8AFC8B0A,
	ARPlaneManagerListener__ctor_mC0BA10E19292774E8F44C7BAB23725C169A0F5BD,
	ARPointCloudManagerListener_OnTrackablesChanged_m37F4B0894E58642522DD88A5B70B2D6425D171F2,
	ARPointCloudManagerListener_RegisterTrackablesChangedDelegate_m8FDD10C3610B31F4C6755847ABFF706E74EEA786,
	ARPointCloudManagerListener_UnregisterTrackablesChangedDelegate_m211D23F127AE987796DD35083C3BBDB15ED63928,
	ARPointCloudManagerListener__ctor_mAC662B594114A7F330A910B4DF50C7D489B0C0D8,
	ARTrackedImageManagerListener_OnTrackablesChanged_mB7342F42759F03B7495C3863A66339D21A50B229,
	ARTrackedImageManagerListener_RegisterTrackablesChangedDelegate_m012C380AFF5DD189E5AD3C059C3783B6AE369DF2,
	ARTrackedImageManagerListener_UnregisterTrackablesChangedDelegate_m47ABAAD0DD04A2519A59F2F0F98C01BDB4D8DD98,
	ARTrackedImageManagerListener__ctor_m80CAF9FEAA38FDC8ADF8789E1DEE5F51E899D556,
	ARTrackedObjectManagerListener_OnTrackablesChanged_m63FB84E742A86FFC4D163A0B17B8989491A51EE6,
	ARTrackedObjectManagerListener_RegisterTrackablesChangedDelegate_m23EA19D74EBB109B2B74AF9DB9D8597FEA16A0E7,
	ARTrackedObjectManagerListener_UnregisterTrackablesChangedDelegate_m95CD86B38632CF9A9E1C50D027E3727F90ED7EF3,
	ARTrackedObjectManagerListener__ctor_mF863E16AC8DDCE13BD309673BD24A4B10EDD5AA7,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	GetAnchorsUnit__ctor_mE0D0675CC37CA1DA87C83FC0BA153B0E0462B4D2,
	GetEnvironmentProbesUnit__ctor_m99D12CCEB247C2FA0DF000C258C9D001B1596C63,
	GetFacesUnit__ctor_mAED50CDA1039DE45158B1F90E10AD3B9C2270121,
	GetHumanBodiesUnit__ctor_m91BE6AF7CFBE7B218BB781F65F17E88224DA4CFF,
	GetParticipantsUnit__ctor_m0D8567DF8B8285D04B1A590EEF7E70346015A416,
	GetPlanesUnit__ctor_mD0526AD4E7F72FEEACEB9502815B5FA3CC989966,
	GetPointCloudsUnit__ctor_mCCE4B499918A3416C73072C13E9F7FCBC37B5261,
	GetRaycastsUnit__ctor_mE784813F0C8D8392C75C397DED11DC88B52CF77E,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	GetTrackedImagesUnit__ctor_m2007F922FF32CD8AB54CB65F3FE4A6B30934A808,
	GetTrackedObjectsUnit__ctor_m7178E4143A9C53B511F82A696C70672E0C88160A,
	SessionStateSwitchUnit_get_flowIn_m667548B27056B2F6F27B63DFCF4412BBAE6637A5,
	SessionStateSwitchUnit_set_flowIn_mD8F159479E3884269A7D2057D7F3E8C52B2923D5,
	SessionStateSwitchUnit_get_sessionStateIn_m4E966BABF18BE40C7178158088CC8DEA31893939,
	SessionStateSwitchUnit_set_sessionStateIn_m39EB7D50DFEE0306E0C4F690E06D8B6D246B1AFB,
	SessionStateSwitchUnit_get_none_mCDA812C5D868B12D04C020ED27ED0F886EE0963E,
	SessionStateSwitchUnit_set_none_mE058B6C1A72B0BD0DC6BAEA629EC6C8A55BD273C,
	SessionStateSwitchUnit_get_unsupported_m911986D3ADF69617D9E6DFFB367926BBA3639439,
	SessionStateSwitchUnit_set_unsupported_m683621DAFF0725F51FAB5D0FB9CB63F5731E53A1,
	SessionStateSwitchUnit_get_checkingAvailability_m3EF1098F57D30B08B8C7587F9D94B64A4E650ED9,
	SessionStateSwitchUnit_set_checkingAvailability_m0D10A7BFD1348E9694B381482E90F5974E3CF246,
	SessionStateSwitchUnit_get_needsInstall_mDBE283471C7F3B8C8A1D6962DAFECBC81CC7071E,
	SessionStateSwitchUnit_set_needsInstall_m6C03848DA29361417CBE17AAC6EF641AE4946A95,
	SessionStateSwitchUnit_get_installing_mAB1C0C05D8CAD09DE7EA9177806621D1263E120F,
	SessionStateSwitchUnit_set_installing_m91BC0E0D467AB4CDE4A9E87F43ABA98874D7BAF4,
	SessionStateSwitchUnit_get_ready_m5FDBB155FE257620F68167B4A7A1DEEB597491D2,
	SessionStateSwitchUnit_set_ready_m4CF3C56F9F24CDCFE9678F22BBAA988BF7AA6FD3,
	SessionStateSwitchUnit_get_sessionInitializing_mDAB4A059DA69D91C4A7E3ED9B207A31F64040746,
	SessionStateSwitchUnit_set_sessionInitializing_mB3A2FF70C33664AFF792B183A2E39252DDFDACB2,
	SessionStateSwitchUnit_get_sessionTracking_m46B500F7B3F7276E269EC893E52EE71478554365,
	SessionStateSwitchUnit_set_sessionTracking_mA270750450E8D31E271811C785AE730FB61AACF0,
	SessionStateSwitchUnit_Definition_m51005D8BC9A2A714696B3D883AB3391DEFB9477D,
	SessionStateSwitchUnit_ProcessFlow_m57E488DEC7E1E2F26E074FB49BB7F19DA905C410,
	SessionStateSwitchUnit__ctor_m93E299F5D4B3285000E037D77A034C454C564F68,
};
static const int32_t s_InvokerIndices[157] = 
{
	11834,
	7776,
	7656,
	3405,
	7776,
	7656,
	6213,
	7656,
	7656,
	7776,
	3406,
	7776,
	7656,
	3407,
	7776,
	7656,
	3409,
	7776,
	7656,
	6213,
	7656,
	7656,
	7776,
	3408,
	7776,
	7656,
	3410,
	7776,
	7656,
	3411,
	7776,
	7656,
	3412,
	7776,
	7656,
	3413,
	7776,
	7656,
	6213,
	7656,
	6213,
	6213,
	11559,
	7776,
	3430,
	7776,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	7656,
	3414,
	7776,
	7656,
	3415,
	7776,
	6053,
	6213,
	6213,
	7776,
	7776,
	7776,
	6054,
	7776,
	6058,
	6213,
	6213,
	7776,
	6059,
	7776,
	7776,
	7776,
	6060,
	6213,
	6213,
	7776,
	6061,
	6213,
	6213,
	7776,
	6065,
	6213,
	6213,
	7776,
	6067,
	6213,
	6213,
	7776,
	6068,
	6213,
	6213,
	7776,
	6075,
	6213,
	6213,
	7776,
	6076,
	6213,
	6213,
	7776,
	0,
	0,
	0,
	0,
	0,
	7776,
	7776,
	7776,
	7776,
	7776,
	7776,
	7776,
	7776,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	7776,
	7776,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7776,
	5461,
	7776,
};
static const Il2CppTokenRangePair s_rgctxIndices[3] = 
{
	{ 0x02000011, { 0, 10 } },
	{ 0x0200001F, { 10, 7 } },
	{ 0x02000028, { 17, 32 } },
};
extern const uint32_t g_rgctx_TrackablesChangedEventUnit_8_t62B4A69D9B353867FB4A0C99ACC3F761A166880A;
extern const uint32_t g_rgctx_TListener_tE039B788A82827A96FA78F4370B78312C86F2F04;
extern const uint32_t g_rgctx_GameObjectEventUnit_1_Definition_m6AD4334B9EA12D4C24047F22B7AC7AA36FF949E8;
extern const uint32_t g_rgctx_Unit_ValueOutput_TisList_1_t6DABDF4D9E1E7E1BC4C76B6E277EF1B57F8ABDBD_m2EFB259AC7BA733B0186140979E201FDF7276166;
extern const uint32_t g_rgctx_TrackablesChangedEventUnit_8_set_added_mCA4CBCFA56192AF9A813FF7335B98DC2E5077D5D;
extern const uint32_t g_rgctx_TrackablesChangedEventUnit_8_set_updated_mB1CE36B4A614CA9AD3B61FD241CDB3B26C7D83C9;
extern const uint32_t g_rgctx_TrackablesChangedEventUnit_8_set_removed_mF96DB8AE5AC1BB3B5F9E991CFCF0D8CCA25BBFE8;
extern const uint32_t g_rgctx_GameObjectEventUnit_1__ctor_mDBFAD879D653144C9CF8548E90AC150039833173;
extern const uint32_t g_rgctx_GameObjectEventUnit_1_tE5AD283B3F3A40683B24229D1B947C17CC3F9CD8;
extern const uint32_t g_rgctx_EventUnit_1_t91F304D008EE521B7DCD47E29C968819BCAA212F;
extern const uint32_t g_rgctx_Component_GetComponent_TisTManager_tB88E6A95EA2CA02351B1E67F7F0DE3F69FC229AB_m9328EA722D7B2A7745D4D116FACABC89577393BB;
extern const uint32_t g_rgctx_TManager_tB88E6A95EA2CA02351B1E67F7F0DE3F69FC229AB;
extern const uint32_t g_rgctx_TrackableManagerListener_1_t542C3B950C36C85E419CFA141F2881C228A64789;
extern const uint32_t g_rgctx_Object_FindObjectOfType_TisTManager_tB88E6A95EA2CA02351B1E67F7F0DE3F69FC229AB_m9B84FF79E9CF571ED9D1D26F56BFCB3CC2D0731F;
extern const uint32_t g_rgctx_TManager_tB88E6A95EA2CA02351B1E67F7F0DE3F69FC229AB;
extern const uint32_t g_rgctx_TrackableManagerListener_1_RegisterTrackablesChangedDelegate_mACF04CA4D1408A7363376E3B93B3AA00E1970D6A;
extern const uint32_t g_rgctx_TrackableManagerListener_1_UnregisterTrackablesChangedDelegate_mA82D32D3E5A1692AD949683E1E0A89A979856F0B;
extern const uint32_t g_rgctx_GetTrackablesUnit_6_tEE2C73799D0C0F48253DD43D9AEE94B6324D75F6;
extern const uint32_t g_rgctx_GetTrackablesUnit_6_ProcessFlow_mD304F1A35B636367CA7AA5FBF151D13316CE8F84;
extern const uint32_t g_rgctx_GetTrackablesUnit_6_set_flowIn_mB7E707EA30AA8C673A08B28C503E57A4B3424A1D;
extern const uint32_t g_rgctx_GetTrackablesUnit_6_set_flowOut_m1D5E9053BCB32AD3650DE7F334ED6091F88584C9;
extern const uint32_t g_rgctx_Unit_ValueInput_TisTManager_tE52B904A629D7B17290EC33346B15F644F95BB6E_m5BDBE14FBBE010D6CAE008DDF6E26977043F2B22;
extern const uint32_t g_rgctx_GetTrackablesUnit_6_set_managerIn_mC22350A358F398801957CAB6D7DEFA1C68642C01;
extern const uint32_t g_rgctx_Unit_ValueInput_TisList_1_t842B2AAFC43EA7B6DA7D1EFCFCC8F09AF8013DC2_m441E7F401153A7F2BD292F01E8EE133CC7C00A51;
extern const uint32_t g_rgctx_GetTrackablesUnit_6_set_trackablesIn_mD3753EA4720614180C25B28E86988E0B7AEA5C62;
extern const uint32_t g_rgctx_Unit_ValueOutput_TisList_1_t842B2AAFC43EA7B6DA7D1EFCFCC8F09AF8013DC2_m6D522231A0554FC81592CE36010C206E74144A2A;
extern const uint32_t g_rgctx_GetTrackablesUnit_6_set_trackablesOut_m2465E276F25DE1ACFFADB3909F6BE71C425DA9E7;
extern const uint32_t g_rgctx_GetTrackablesUnit_6_get_managerIn_m54F332CA9BE16A945111915F0B94E6F29BFFCDFE;
extern const uint32_t g_rgctx_GetTrackablesUnit_6_get_flowIn_m2CCD9F9B29F8B2C0042CD81D0738FF9028658F20;
extern const uint32_t g_rgctx_GetTrackablesUnit_6_get_trackablesIn_mB766B82FEA4B89D617DF873CBB11781CDDA4DBC0;
extern const uint32_t g_rgctx_GetTrackablesUnit_6_get_flowOut_mAA978C348D5DDEF7F465221B321F737D8D55AF4F;
extern const uint32_t g_rgctx_GetTrackablesUnit_6_get_trackablesOut_m0A6A7C9CBCDFB2D0D1A35C4BBF50BBE6C2302E37;
extern const uint32_t g_rgctx_ARTrackableManager_5_t32DA1DBF648E6CF255C1E86517B485AD5D0898EB;
extern const uint32_t g_rgctx_Object_FindObjectOfType_TisARTrackableManager_5_t32DA1DBF648E6CF255C1E86517B485AD5D0898EB_mB7BB255AF8A415E113F8271E41E8B3EB79244F88;
extern const uint32_t g_rgctx_TManager_tE52B904A629D7B17290EC33346B15F644F95BB6E;
extern const uint32_t g_rgctx_GetTrackablesUnit_6_tEE2C73799D0C0F48253DD43D9AEE94B6324D75F6;
extern const uint32_t g_rgctx_List_1_t842B2AAFC43EA7B6DA7D1EFCFCC8F09AF8013DC2;
extern const uint32_t g_rgctx_List_1__ctor_mBCCC597C861B1641122F1201AEE901FECB824BB3;
extern const uint32_t g_rgctx_List_1_Clear_m7C827A3C8B8BAEB92ECF9047FE2A28B624D27B27;
extern const uint32_t g_rgctx_ARTrackableManager_5_get_trackables_m0BA547D9198688CD6A190CE70EF162CF45D78BD5;
extern const uint32_t g_rgctx_TrackableCollection_1_tFF79B292E18FBC76F70D3FBCC291E6CFF9A5902A;
extern const uint32_t g_rgctx_TrackableCollection_1_GetEnumerator_mA4BEA2F3BA42494FE727759189F40BE7D08410F6;
extern const uint32_t g_rgctx_TrackableCollection_1_tFF79B292E18FBC76F70D3FBCC291E6CFF9A5902A;
extern const uint32_t g_rgctx_Enumerator_tCE0C00F73A82649AFB86F3E9A70EF2D3C42DB0A5;
extern const uint32_t g_rgctx_Enumerator_get_Current_mA6B61F23E059423815ACB88A1326B2892EC554D0;
extern const uint32_t g_rgctx_Enumerator_tCE0C00F73A82649AFB86F3E9A70EF2D3C42DB0A5;
extern const uint32_t g_rgctx_TTrackable_tD560FFF947137207AEC683B5210B33B6AF08E1A6;
extern const uint32_t g_rgctx_List_1_Add_m7A798A9FCED2F37F21D9CCB228D2A353F2530820;
extern const uint32_t g_rgctx_Enumerator_MoveNext_mD29D2F386D00F8C48D0166D8F313DB69E39D6F5D;
static const Il2CppRGCTXDefinition s_rgctxValues[49] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TrackablesChangedEventUnit_8_t62B4A69D9B353867FB4A0C99ACC3F761A166880A },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TListener_tE039B788A82827A96FA78F4370B78312C86F2F04 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObjectEventUnit_1_Definition_m6AD4334B9EA12D4C24047F22B7AC7AA36FF949E8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Unit_ValueOutput_TisList_1_t6DABDF4D9E1E7E1BC4C76B6E277EF1B57F8ABDBD_m2EFB259AC7BA733B0186140979E201FDF7276166 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TrackablesChangedEventUnit_8_set_added_mCA4CBCFA56192AF9A813FF7335B98DC2E5077D5D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TrackablesChangedEventUnit_8_set_updated_mB1CE36B4A614CA9AD3B61FD241CDB3B26C7D83C9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TrackablesChangedEventUnit_8_set_removed_mF96DB8AE5AC1BB3B5F9E991CFCF0D8CCA25BBFE8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObjectEventUnit_1__ctor_mDBFAD879D653144C9CF8548E90AC150039833173 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_GameObjectEventUnit_1_tE5AD283B3F3A40683B24229D1B947C17CC3F9CD8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EventUnit_1_t91F304D008EE521B7DCD47E29C968819BCAA212F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Component_GetComponent_TisTManager_tB88E6A95EA2CA02351B1E67F7F0DE3F69FC229AB_m9328EA722D7B2A7745D4D116FACABC89577393BB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TManager_tB88E6A95EA2CA02351B1E67F7F0DE3F69FC229AB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TrackableManagerListener_1_t542C3B950C36C85E419CFA141F2881C228A64789 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Object_FindObjectOfType_TisTManager_tB88E6A95EA2CA02351B1E67F7F0DE3F69FC229AB_m9B84FF79E9CF571ED9D1D26F56BFCB3CC2D0731F },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TManager_tB88E6A95EA2CA02351B1E67F7F0DE3F69FC229AB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TrackableManagerListener_1_RegisterTrackablesChangedDelegate_mACF04CA4D1408A7363376E3B93B3AA00E1970D6A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TrackableManagerListener_1_UnregisterTrackablesChangedDelegate_mA82D32D3E5A1692AD949683E1E0A89A979856F0B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_GetTrackablesUnit_6_tEE2C73799D0C0F48253DD43D9AEE94B6324D75F6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GetTrackablesUnit_6_ProcessFlow_mD304F1A35B636367CA7AA5FBF151D13316CE8F84 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GetTrackablesUnit_6_set_flowIn_mB7E707EA30AA8C673A08B28C503E57A4B3424A1D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GetTrackablesUnit_6_set_flowOut_m1D5E9053BCB32AD3650DE7F334ED6091F88584C9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Unit_ValueInput_TisTManager_tE52B904A629D7B17290EC33346B15F644F95BB6E_m5BDBE14FBBE010D6CAE008DDF6E26977043F2B22 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GetTrackablesUnit_6_set_managerIn_mC22350A358F398801957CAB6D7DEFA1C68642C01 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Unit_ValueInput_TisList_1_t842B2AAFC43EA7B6DA7D1EFCFCC8F09AF8013DC2_m441E7F401153A7F2BD292F01E8EE133CC7C00A51 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GetTrackablesUnit_6_set_trackablesIn_mD3753EA4720614180C25B28E86988E0B7AEA5C62 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Unit_ValueOutput_TisList_1_t842B2AAFC43EA7B6DA7D1EFCFCC8F09AF8013DC2_m6D522231A0554FC81592CE36010C206E74144A2A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GetTrackablesUnit_6_set_trackablesOut_m2465E276F25DE1ACFFADB3909F6BE71C425DA9E7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GetTrackablesUnit_6_get_managerIn_m54F332CA9BE16A945111915F0B94E6F29BFFCDFE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GetTrackablesUnit_6_get_flowIn_m2CCD9F9B29F8B2C0042CD81D0738FF9028658F20 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GetTrackablesUnit_6_get_trackablesIn_mB766B82FEA4B89D617DF873CBB11781CDDA4DBC0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GetTrackablesUnit_6_get_flowOut_mAA978C348D5DDEF7F465221B321F737D8D55AF4F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GetTrackablesUnit_6_get_trackablesOut_m0A6A7C9CBCDFB2D0D1A35C4BBF50BBE6C2302E37 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ARTrackableManager_5_t32DA1DBF648E6CF255C1E86517B485AD5D0898EB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Object_FindObjectOfType_TisARTrackableManager_5_t32DA1DBF648E6CF255C1E86517B485AD5D0898EB_mB7BB255AF8A415E113F8271E41E8B3EB79244F88 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TManager_tE52B904A629D7B17290EC33346B15F644F95BB6E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_GetTrackablesUnit_6_tEE2C73799D0C0F48253DD43D9AEE94B6324D75F6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t842B2AAFC43EA7B6DA7D1EFCFCC8F09AF8013DC2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1__ctor_mBCCC597C861B1641122F1201AEE901FECB824BB3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Clear_m7C827A3C8B8BAEB92ECF9047FE2A28B624D27B27 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ARTrackableManager_5_get_trackables_m0BA547D9198688CD6A190CE70EF162CF45D78BD5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TrackableCollection_1_tFF79B292E18FBC76F70D3FBCC291E6CFF9A5902A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TrackableCollection_1_GetEnumerator_mA4BEA2F3BA42494FE727759189F40BE7D08410F6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TrackableCollection_1_tFF79B292E18FBC76F70D3FBCC291E6CFF9A5902A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_tCE0C00F73A82649AFB86F3E9A70EF2D3C42DB0A5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_get_Current_mA6B61F23E059423815ACB88A1326B2892EC554D0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_tCE0C00F73A82649AFB86F3E9A70EF2D3C42DB0A5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TTrackable_tD560FFF947137207AEC683B5210B33B6AF08E1A6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Add_m7A798A9FCED2F37F21D9CCB228D2A353F2530820 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_MoveNext_mD29D2F386D00F8C48D0166D8F313DB69E39D6F5D },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_XR_ARFoundation_VisualScripting_CodeGenModule;
const Il2CppCodeGenModule g_Unity_XR_ARFoundation_VisualScripting_CodeGenModule = 
{
	"Unity.XR.ARFoundation.VisualScripting.dll",
	157,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	3,
	s_rgctxIndices,
	49,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
