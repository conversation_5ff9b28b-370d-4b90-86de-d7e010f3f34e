%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &1
MonoBehaviour:
  m_ObjectHideFlags: 61
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d79cb9ecc0d4a6d428ab98a681a33897, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  timeAreaShownRange: {x: -3.364902, y: 201.14777}
  trackScale: 1
  playRangeEnabled: 0
  windowTime: 0
  verticalScroll: 0
  sequencerHeaderWidth: 257.14288
  m_TimeAreaPlayRange:
    start: 0
    end: 0
  m_Keys:
  - {fileID: 986102987895668041, guid: 2687a1c33e1a38e43a003fc5e4a520ee, type: 2}
  - {fileID: 7484622086273806838, guid: 2687a1c33e1a38e43a003fc5e4a520ee, type: 2}
  - {fileID: 6752443907315554061, guid: 2687a1c33e1a38e43a003fc5e4a520ee, type: 2}
  - {fileID: 6260040523322518536, guid: 2687a1c33e1a38e43a003fc5e4a520ee, type: 2}
  - {fileID: 4943941276641490649, guid: 2687a1c33e1a38e43a003fc5e4a520ee, type: 2}
  - {fileID: 5119018925624099739, guid: 2687a1c33e1a38e43a003fc5e4a520ee, type: 2}
  - {fileID: 6912949860735766758, guid: 2687a1c33e1a38e43a003fc5e4a520ee, type: 2}
  - {fileID: -2649047815270269827, guid: 2687a1c33e1a38e43a003fc5e4a520ee, type: 2}
  - {fileID: -3812879016391245527, guid: 2687a1c33e1a38e43a003fc5e4a520ee, type: 2}
  m_Vals:
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
  m_Version: 1
  deprecated_timeAreaPlayRange: {x: 0, y: 0}
