using System.Collections;
using UnityEngine;

public class MaterialAlphaAnimator : MonoBehaviour
{
    /// <summary>
    /// Animates the alpha value of a material over time.
    /// </summary>
    /// <param name="material">The material to animate</param>
    /// <param name="startAlpha">Starting alpha value (0-1)</param>
    /// <param name="endAlpha">Target alpha value (0-1)</param>
    /// <param name="duration">Duration of the animation in seconds</param>
    /// <param name="onComplete">Optional callback when animation completes</param>
    /// <returns>Coroutine handle that can be used to stop the animation</returns>
    public static Coroutine AnimateMaterialAlpha(Material material, float startAlpha, float endAlpha, float duration, System.Action onComplete = null)
    {
        // Find a MonoBehaviour to run the coroutine
        var runner = FindObjectOfType<MaterialAlphaAnimator>();
        if (runner == null)
        {
            // Create a new GameObject with this component if one doesn't exist
            var go = new GameObject("MaterialAlphaAnimator");
            runner = go.AddComponent<MaterialAlphaAnimator>();
        }

        return runner.StartCoroutine(runner.AnimateMaterialAlphaRoutine(material, startAlpha, endAlpha, duration, onComplete));
    }

    private IEnumerator AnimateMaterialAlphaRoutine(Material material, float startAlpha, float endAlpha, float duration, System.Action onComplete)
    {
        
        material.SetFloat("_Mode", 3); // Transparent mode
        material.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
        material.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
        material.SetInt("_ZWrite", 0);
        material.DisableKeyword("_ALPHATEST_ON");
        material.EnableKeyword("_ALPHABLEND_ON");
        material.DisableKeyword("_ALPHAPREMULTIPLY_ON");
        material.renderQueue = 3000;

  
        Color color = material.color;
        color.a = startAlpha;
        material.color = color;

        float elapsedTime = 0f;

        while (elapsedTime < duration)
        {
           
            float t = elapsedTime / duration;
            color.a = Mathf.Lerp(startAlpha, endAlpha, t);

       
            material.color = color;

    
            yield return null;

    
            elapsedTime += Time.deltaTime;
        }
        color.a = endAlpha;
        material.color = color;
        onComplete?.Invoke();
    }
}