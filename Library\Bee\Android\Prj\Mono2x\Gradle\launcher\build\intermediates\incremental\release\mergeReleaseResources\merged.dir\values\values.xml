<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="staticSplashScreenBackgroundColor">#231F20</color>
    <item name="unitySurfaceView" type="id"/>
    <string name="FreeformWindowOrientation_landscape"/>
    <string name="FreeformWindowOrientation_portrait"/>
    <string name="FreeformWindowSize_maximize"/>
    <string name="FreeformWindowSize_phone"/>
    <string name="FreeformWindowSize_tablet"/>
    <string name="__arcore_cancel">Cancel</string>
    <string name="__arcore_continue">Continue</string>
    <string name="__arcore_install_app">This application requires the latest version of Google Play Services for AR.</string>
    <string name="__arcore_install_feature">This feature requires the latest version of Google Play Services for AR.</string>
    <string name="__arcore_installing">Installing Google Play Services for AR…</string>
    <string name="app_name">SurAnim</string>
    <string name="game_view_content_description">Game view</string>
    <style name="BaseUnityTheme" parent="android:Theme.Holo.Light.NoActionBar.Fullscreen">
</style>
    <style name="UnityThemeSelector" parent="BaseUnityTheme">
	<item name="android:windowBackground">@android:color/black</item>
</style>
    <style name="UnityThemeSelector.Translucent" parent="@style/UnityThemeSelector">
    <item name="android:windowIsTranslucent">true</item>
    <item name="android:windowBackground">@android:color/transparent</item>
</style>
</resources>