using UnityEngine;
using System;
using System.Collections;

namespace AnimationUtility
{
    /// <summary>
    /// A reusable class to play animations on game objects with Animator components.
    /// </summary>
    public class AnimationPlayer : MonoBehaviour
    {
        [SerializeField] private Animator animator;

        private void Awake()
        {
            // If animator is not assigned in the inspector, try to get it from this game object
            if (animator == null)
            {
                animator = GetComponent<Animator>();

                if (animator == null)
                {
                    Debug.LogWarning("AnimationPlayer: No Animator component found on " + gameObject.name);
                }
            }
        }

        /// <summary>
        /// Play an animation by name.
        /// </summary>
        /// <param name="animationName">The name of the animation state.</param>
        /// <param name="crossFadeTime">Time to blend from the current state to the new state (0 for instant).</param>
        /// <param name="layer">The layer to play the animation on (-1 for default).</param>
        /// <returns>True if animation was found and played, false otherwise.</returns>
        public bool PlayAnimation(string animationName, float crossFadeTime = 0.25f, int layer = -1)
        {
            if (animator == null)
            {
                Debug.LogError("AnimationPlayer: Cannot play animation. Animator is null on " + gameObject.name);
                return false;
            }

            if (string.IsNullOrEmpty(animationName))
            {
                Debug.LogError("AnimationPlayer: Animation name is null or empty");
                return false;
            }

            // Check if animation exists in controller
            if (!AnimationExists(animationName))
            {
                Debug.LogError($"AnimationPlayer: Animation '{animationName}' not found in Animator on {gameObject.name}");
                return false;
            }

            if (crossFadeTime > 0)
            {
                animator.CrossFade(animationName, crossFadeTime, layer);
            }
            else
            {
                animator.Play(animationName, layer);
            }

            return true;
        }

        /// <summary>
        /// Play an animation and invoke a callback when it completes.
        /// </summary>
        /// <param name="animationName">The name of the animation state.</param>
        /// <param name="onComplete">Callback to invoke when animation completes.</param>
        /// <param name="crossFadeTime">Time to blend from the current state to the new state.</param>
        /// <param name="layer">The layer to play the animation on (-1 for default).</param>
        public void PlayAnimationWithCallback(string animationName, Action onComplete, float crossFadeTime = 0.25f, int layer = -1)
        {
            if (PlayAnimation(animationName, crossFadeTime, layer))
            {
                StartCoroutine(WaitForAnimationComplete(animationName, layer, onComplete));
            }
        }

        /// <summary>
        /// Checks if an animation exists in the Animator controller.
        /// </summary>
        /// <param name="animationName">The name of the animation to check.</param>
        /// <returns>True if animation exists, false otherwise.</returns>
        public bool AnimationExists(string animationName)
        {
            if (animator == null)
                return false;

            // Try to get hash of animation name
            int hash = Animator.StringToHash(animationName);

            // Check if animation exists using the RuntimeAnimatorController
            var controller = animator.runtimeAnimatorController;
            var clips = controller.animationClips;

            foreach (var clip in clips)
            {
                if (clip.name == animationName)
                    return true;
            }

            return false;
        }

        /// <summary>
        /// Sets a trigger parameter in the animator.
        /// </summary>
        /// <param name="triggerName">The name of the trigger parameter.</param>
        public void SetTrigger(string triggerName)
        {
            if (animator != null)
            {
                animator.SetTrigger(triggerName);
            }
            else
            {
                Debug.LogError("AnimationPlayer: Cannot set trigger. Animator is null on " + gameObject.name);
            }
        }

        /// <summary>
        /// Sets a boolean parameter in the animator.
        /// </summary>
        /// <param name="paramName">The name of the bool parameter.</param>
        /// <param name="value">The value to set.</param>
        public void SetBool(string paramName, bool value)
        {
            if (animator != null)
            {
                animator.SetBool(paramName, value);
            }
            else
            {
                Debug.LogError("AnimationPlayer: Cannot set bool. Animator is null on " + gameObject.name);
            }
        }

        /// <summary>
        /// Sets a float parameter in the animator.
        /// </summary>
        /// <param name="paramName">The name of the float parameter.</param>
        /// <param name="value">The value to set.</param>
        public void SetFloat(string paramName, float value)
        {
            if (animator != null)
            {
                animator.SetFloat(paramName, value);
            }
            else
            {
                Debug.LogError("AnimationPlayer: Cannot set float. Animator is null on " + gameObject.name);
            }
        }

        /// <summary>
        /// Sets an integer parameter in the animator.
        /// </summary>
        /// <param name="paramName">The name of the int parameter.</param>
        /// <param name="value">The value to set.</param>
        public void SetInteger(string paramName, int value)
        {
            if (animator != null)
            {
                animator.SetInteger(paramName, value);
            }
            else
            {
                Debug.LogError("AnimationPlayer: Cannot set integer. Animator is null on " + gameObject.name);
            }
        }

        /// <summary>
        /// Waits for an animation to complete and then invokes a callback.
        /// </summary>
        private IEnumerator WaitForAnimationComplete(string animationName, int layer, Action onComplete)
        {
            // Wait for the animation to start playing
            yield return null;

            // Get info about the animation state
            AnimatorStateInfo stateInfo;
            float normalizedTime;

            do
            {
                stateInfo = animator.GetCurrentAnimatorStateInfo(layer);
                normalizedTime = stateInfo.normalizedTime;

                // Check if we're playing the intended animation
                bool isPlayingTargetAnimation = stateInfo.IsName(animationName);

                if (!isPlayingTargetAnimation)
                {
                    // Animation was interrupted
                    yield break;
                }

                yield return null;
            }
            while (normalizedTime < 1.0f);

            // Animation complete, invoke callback
            onComplete?.Invoke();
        }
    }
}