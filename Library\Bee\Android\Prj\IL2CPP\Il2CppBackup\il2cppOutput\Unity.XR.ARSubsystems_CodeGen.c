﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"


extern const RuntimeMethod* XRCpuImage_OnAsyncConversionComplete_mDC3A0C88A34909C9D08E4BE7E94C8E27E2BB3D3C_RuntimeMethod_var;



extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mFDE4670C010E535DF7E9E35FED1FF1D1A4DF7075 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m12AA9072DC7FEC76FC9C799662796AAE2BB8345A (void);
extern void XRAnchor_get_defaultValue_mF68ABF2D0EC8B54DD8D5333FCD56EEF14A985A9A (void);
extern void XRAnchor__ctor_mDD8A7F48E03A25972AA93D2C89C1D773635CA15B (void);
extern void XRAnchor__ctor_m3C8D3F14E6CD1FC66CB5E27096253B81BAA4C75C (void);
extern void XRAnchor_get_trackableId_m0F50E81D0152D0BA4152EF9B66F648EF9FC664AE (void);
extern void XRAnchor_get_pose_m2347783C1262EEFBC0B817EF0357FA4BB4BF053F (void);
extern void XRAnchor_get_trackingState_m6124A26C36CA93C25C57548576CB00C1F496ED83 (void);
extern void XRAnchor_get_nativePtr_mC0551FA7E8DB8A0DA1EAE02D9B0BFD9D47389C26 (void);
extern void XRAnchor_get_sessionId_m719628E8A58027C75FF2CEA3345DC41200FB5F76 (void);
extern void XRAnchor_GetHashCode_mEFA5E37600C1A0B56F911227326704C17C3B5400 (void);
extern void XRAnchor_Equals_m8F3408527C2CF86CF0A09AE74BF790F8E60ED6F1 (void);
extern void XRAnchor_Equals_mFD12F373615A9015CB110787F6FF06CDAAC1433F (void);
extern void XRAnchor_op_Equality_m0404B51B34ADBD243B8E92F976964CB76E79223F (void);
extern void XRAnchor_op_Inequality_m0C41AFDC68E1B648652C01E4BFF271854705A3F1 (void);
extern void XRAnchor__cctor_m089D3BC26EBFD0569C11B81E4DEAE752346441FE (void);
extern void XRAnchorSubsystem__ctor_m7475F45C8D0D2B0E0FE0B91E45A03A0F6541138D (void);
extern void XRAnchorSubsystem_GetChanges_m75942CEB1452CCBC366E8AAC9FE1A747B54FE439 (void);
extern void XRAnchorSubsystem_TryAddAnchor_m17C838AABA88AE000FC4170ADE72C009DCB14EF5 (void);
extern void XRAnchorSubsystem_TryAttachAnchor_m01E619A03158783EFC56D65258DCC19B5D422BAD (void);
extern void XRAnchorSubsystem_TryRemoveAnchor_m88DEBF2B32CF1B4C49581D1ABAC95652741EE4ED (void);
extern void Provider_TryAddAnchor_mAD1617DCFF200E647967B17A3BE5292D2BE0840B (void);
extern void Provider_TryAttachAnchor_m52CBF550B170FA6F50DDC6705A9AC3F28B4FE175 (void);
extern void Provider_TryRemoveAnchor_m245D945B15FC5D7657D9E05198F0EE60275E5216 (void);
extern void Provider__ctor_mEC1254B41F2C452FD31DE2DF6F9D8AA5A4E4FB94 (void);
extern void XRAnchorSubsystemDescriptor_get_supportsTrackableAttachments_mAA02843053726C4551483D799FBDBCD00AFDDF31 (void);
extern void XRAnchorSubsystemDescriptor_Create_m81AF7F8FB3993C02C7AB93B292F6D7C65D8050AC (void);
extern void XRAnchorSubsystemDescriptor__ctor_m09A98B336838C4CF7BCFE08EB3AF49BFDA9AFF8C (void);
extern void Cinfo_get_id_mFF66DF9642403D18476451C30BD5F2FC89CAF8B1 (void);
extern void Cinfo_set_id_m6344F3700C1D743D82AB9E74925F7687925734A6 (void);
extern void Cinfo_get_providerType_m2D11E14B5E4C48474C00C579D4C2F5F45970D70B (void);
extern void Cinfo_set_providerType_m0E291C22B0B2CF634024F32ECA2E424F18157ACF (void);
extern void Cinfo_get_subsystemTypeOverride_mD1DEE5FBF656FDF4BF5E28B60C04F53B6D146C3B (void);
extern void Cinfo_set_subsystemTypeOverride_mD5C5CAAC167444FE1BD48C1A16AA904E8DFF7052 (void);
extern void Cinfo_get_subsystemImplementationType_m9F7B46950CE7957D33333EBBBAA65B27407831FA (void);
extern void Cinfo_set_subsystemImplementationType_m97E16A222C068284404DA8CC6C828D34E2F15265 (void);
extern void Cinfo_get_supportsTrackableAttachments_m2BA4A0E85635C4D85059CDF713EE7FC21F80DBF4 (void);
extern void Cinfo_set_supportsTrackableAttachments_m58F12783B1EF42ED420CC99E0585FA6ED046C4AD (void);
extern void Cinfo_GetHashCode_mE063F9FDF568ECC85F4D30998CB9A071A82C3859 (void);
extern void Cinfo_Equals_m11F554099FC7163A8405E15A238CD1084BCCB65E (void);
extern void Cinfo_Equals_mD8F45C05DFDE73ABDD26DC002B6F0B1506149F6D (void);
extern void Cinfo_op_Equality_mE41AF60B60A6867D61C42400994FF54D1F12AB29 (void);
extern void Cinfo_op_Inequality_m4B20D281F19BB98507567B20742E775B138594DA (void);
extern void ARRenderingUtils_get_useLegacyRenderPipeline_m5995F66A625E303025AF9841E9598510CAC457DD (void);
extern void XRCameraConfiguration_get_width_mCEA441DFABEDE3E552A2D4452508BCE923B6C3C6 (void);
extern void XRCameraConfiguration_get_height_m9130BF72BE684B67C2100DD1624AF851E42B81A8 (void);
extern void XRCameraConfiguration_get_resolution_m8EB20C15322147BCA971867F394BC0E0EDCB5A0D (void);
extern void XRCameraConfiguration_get_framerate_m3BFA6E6FB947828EDC20AC9CED31391634F5EB6F (void);
extern void XRCameraConfiguration_get_depthSensorSupported_m05B25DB3D4E83E385BE419109288B77684532A53 (void);
extern void XRCameraConfiguration_get_nativeConfigurationHandle_mD9C92AE35660E0441A296301336578BA11313155 (void);
extern void XRCameraConfiguration__ctor_m6A41DA8E8540120FABB6436C2DCB07B6BC520281 (void);
extern void XRCameraConfiguration__ctor_m2943E6AD678C2106CF20EA8CA967F2EAB15303FB (void);
extern void XRCameraConfiguration__ctor_m0C8EC1223917D880B58850B06E1DC4902F269C4C (void);
extern void XRCameraConfiguration_ToString_mD69E2A39496C4B88A7089AF64A13057585A6F159 (void);
extern void XRCameraConfiguration_GetHashCode_mE50DD8C034ED9415443191DF89F044B05510CEB7 (void);
extern void XRCameraConfiguration_Equals_m26024336DA6F68CDCBF7916F6B6BF690DF152FA5 (void);
extern void XRCameraConfiguration_Equals_mCFE381E6FB6B3650DCAB79FB6894DA8BB511A708 (void);
extern void XRCameraConfiguration_op_Equality_m8B0FCADB92F8DA1699B6A669DC7FB3EFB375792A (void);
extern void XRCameraConfiguration_op_Inequality_mE02244AF68C740BB185D8D5A8AD941D63BD9B76C (void);
extern void XRCameraFrame_get_timestampNs_m93571F53415C7DC6195854F3297E95D2E55A4DFB (void);
extern void XRCameraFrame_get_averageBrightness_mD1106801D777BFB9EE60FDE5DE194EBACEFB6071 (void);
extern void XRCameraFrame_get_averageColorTemperature_m29B8FBE0061F8895678D3C4DA5BAB7BDBE154D4E (void);
extern void XRCameraFrame_get_colorCorrection_m00236A30115F91E2696EAAAF6F1CDF9CA0F83354 (void);
extern void XRCameraFrame_get_projectionMatrix_mDE497D5208A1D08226B6B6C7521F53125E6EB9BD (void);
extern void XRCameraFrame_get_displayMatrix_m221E85929B55C0B8F6AB494FF27CC3200A80F287 (void);
extern void XRCameraFrame_get_trackingState_mA6E95E5F574FC6506C0F602E430C42763797779A (void);
extern void XRCameraFrame_get_nativePtr_m4DED9CE0A7333F6A1E5C4932A6C98A8A0DD9E62D (void);
extern void XRCameraFrame_get_properties_m0C853765A7C76148A439A2C275E3687659DD8DFB (void);
extern void XRCameraFrame_get_averageIntensityInLumens_m639F0315B64DA5EC8321609C8673EA14A7263115 (void);
extern void XRCameraFrame_get_exposureDuration_m4D8412C33F590A282E1671AFD89CC543837BD007 (void);
extern void XRCameraFrame_get_exposureOffset_m9683C51CB26F830F5FC5720AD0FD541EC053202E (void);
extern void XRCameraFrame_get_mainLightIntensityLumens_m55B353C41D7A9E00F596D4BAD4813793C9754BE8 (void);
extern void XRCameraFrame_get_mainLightColor_mE96BCE9B4E4E241AF9F6BC758942EB8D4584138C (void);
extern void XRCameraFrame_get_mainLightDirection_mF33683D8BF23ADFB8EBC2D2875C2EDF5922F21B8 (void);
extern void XRCameraFrame_get_ambientSphericalHarmonics_mB62D6BDCC0A0DAAB5C057225A84289502076EFCE (void);
extern void XRCameraFrame_get_cameraGrain_m7FF742DB5555C9D84DCD7937828C612FAACFEEFE (void);
extern void XRCameraFrame_get_noiseIntensity_mA1D17EA6D00D7FF958FFF6A62B99B34B052F2FEC (void);
extern void XRCameraFrame_get_hasTimestamp_mD6AD6768B71946B0643836ACD28BF32876A5E0FF (void);
extern void XRCameraFrame_get_hasAverageBrightness_m8CC4709AA168C8762763837B384B7332FC2B73B0 (void);
extern void XRCameraFrame_get_hasAverageColorTemperature_m163AF5FAD20B5779A28550ED502F5037C4BDB93A (void);
extern void XRCameraFrame_get_hasColorCorrection_mCEB8BC23DF1997AB5DFCD013F56111FB8A8D118E (void);
extern void XRCameraFrame_get_hasProjectionMatrix_m850BCDBFBBD894BF56EEED3A82349A4E4811CC1F (void);
extern void XRCameraFrame_get_hasDisplayMatrix_m7D5DA2AA4F3C83B25714C0FED9EEAE1E51B95959 (void);
extern void XRCameraFrame_get_hasAverageIntensityInLumens_m7E14C289B8D931F55B7A98D5075263E96CE3B4DE (void);
extern void XRCameraFrame_get_hasExposureDuration_m02C1ACB25E72D090C9A56FC158E8D4B0D3C04D50 (void);
extern void XRCameraFrame_get_hasExposureOffset_m6A4048142BD1E59E403F858144092C5F7846CA53 (void);
extern void XRCameraFrame_get_hasMainLightIntensityLumens_mA423D7DEF78D1888AFED8BF17B3E1037C24E469B (void);
extern void XRCameraFrame_get_hasMainLightColor_m07A53E75212D8BA3582613228AC0DACBDDF983FF (void);
extern void XRCameraFrame_get_hasMainLightDirection_m67DFB7C0DAD130D98290130131EDC4BA62818B5E (void);
extern void XRCameraFrame_get_hasAmbientSphericalHarmonics_m45F02EFE6E47FC9B9AEE4F1B6AEC4F9E7FF1F92A (void);
extern void XRCameraFrame_get_hasCameraGrain_mC37056CCCDBEFD620038107A078B6A39F61D99AE (void);
extern void XRCameraFrame_get_hasNoiseIntensity_m076641BB06432F1F27EFD353B6E7116B098BE4B7 (void);
extern void XRCameraFrame_get_hasExifData_mB7025B1AA39F37A2DAB00F455D9803096B12DF24 (void);
extern void XRCameraFrame__ctor_m7A19EA8CCC1391BE7463FF9B492863BDA975E90B (void);
extern void XRCameraFrame__ctor_m991480EB7E8C7C3A8C63974AFE3FD8900A2EBC03 (void);
extern void XRCameraFrame_TryGetTimestamp_m60FE1777D7379C288482A23E5A7C5B297F1DDD94 (void);
extern void XRCameraFrame_TryGetAverageBrightness_m1FDC9DCF0A34227DC5ECB78A5E80E614F0063C98 (void);
extern void XRCameraFrame_TryGetAverageColorTemperature_m2737123C8E46EF119C04162FAD8EAA312FA2EF3B (void);
extern void XRCameraFrame_TryGetProjectionMatrix_mC25F35BF17829DBEDA748D61362289E4842B5098 (void);
extern void XRCameraFrame_TryGetDisplayMatrix_mA034AD550B172CACDDED708A12FFBA548E750312 (void);
extern void XRCameraFrame_TryGetAverageIntensityInLumens_mE5FC24C1E3D49FB679137698834F22BF258DD674 (void);
extern void XRCameraFrame_TryGetExifData_m99AAC21B7048242487A8D48DE8D92AC9F4C53AF4 (void);
extern void XRCameraFrame_Equals_mA1542DDF01588CB1AEDFEB763F63684C18B717C8 (void);
extern void XRCameraFrame_Equals_m53FA29D21C4E68E89D59E8904EF7222571C04A50 (void);
extern void XRCameraFrame_op_Equality_m0D5674F05EA2745784FD5D098C1874E0E7C4E708 (void);
extern void XRCameraFrame_op_Inequality_mFB2DA9148F3B10BCFB296FCCE66CD208C05E40BD (void);
extern void XRCameraFrame_GetHashCode_m9392ED676A0D41F404CF03A0C570068AC2832352 (void);
extern void XRCameraFrame_ToString_m4825A21E18219B4B626C1A2CB3EDD006DFCA44AA (void);
extern void XRCameraFrameExifData_get_hasApertureValue_m2C361DC00694AE380ABE97C9A99F6B645CC8F53F (void);
extern void XRCameraFrameExifData_get_hasBrightnessValue_m39C2542ED3A9A2F1BA136FE4EE381DA0C21ED23D (void);
extern void XRCameraFrameExifData_get_hasExposureTime_mF0CE4E1C0E88FF4D3B727E72DA507117A252CC0C (void);
extern void XRCameraFrameExifData_get_hasShutterSpeedValue_m3AC9A6CD82AAC78E5E74D1AA654CBCC0D7F7B2C4 (void);
extern void XRCameraFrameExifData_get_hasExposureBiasValue_m2A069367FABB0671F888CDEDEC473BE1841211E2 (void);
extern void XRCameraFrameExifData_get_hasFNumber_mFCA781B3A5BE10C965D76A608B49CDE619ED46F4 (void);
extern void XRCameraFrameExifData_get_hasFocalLength_m0107A9D19FE993FB4836FB1EA5BF17656F2FDEF0 (void);
extern void XRCameraFrameExifData_get_hasFlash_m6CFD79284C340A886C21336708FE674F6EF80254 (void);
extern void XRCameraFrameExifData_get_hasColorSpace_m8B09CAAB1A79D588F04F3D0E26F42261B87EFE7F (void);
extern void XRCameraFrameExifData_get_hasPhotographicSensitivity_m8F78AC08D6DFE157D1B1D40D1D06517CAF6FFB1A (void);
extern void XRCameraFrameExifData_get_hasMeteringMode_m62016BF52989FE5849139AFB087F6C0244517D7C (void);
extern void XRCameraFrameExifData_get_nativePtr_mCB1987D3B4CABB2C972BE7D4E92365763550783E (void);
extern void XRCameraFrameExifData__ctor_mC632ECB5B132C6297A8641A5061024B71F850242 (void);
extern void XRCameraFrameExifData_TryGetApertureValue_m4F59F7DEF4992F533DE48D18DBBB0A01EB0E684C (void);
extern void XRCameraFrameExifData_TryGetBrightnessValue_m3C19A37A3534F7E451759E89578260FADBC9B14F (void);
extern void XRCameraFrameExifData_TryGetExposureTime_m46BBCA6CA64E217ED60F3D878983314A59C6D5FA (void);
extern void XRCameraFrameExifData_TryGetShutterSpeedValue_mC955FAA772EC0893B3D06E4ECEA8EE80023FE429 (void);
extern void XRCameraFrameExifData_TryGetExposureBiasValue_m52A2F65945A75DAC6F0BD013EB94DF65D5A6BF0C (void);
extern void XRCameraFrameExifData_TryGetFNumber_m7512F213AD901E6FCDCDF7FD9A582AB322F6063D (void);
extern void XRCameraFrameExifData_TryGetFocalLength_m0109D48BD14F7E853514F66ACD493DD1657EC138 (void);
extern void XRCameraFrameExifData_TryGetFlash_m8BE2090E79E851BA4961115DC320CEB3B6A2A4AB (void);
extern void XRCameraFrameExifData_TryGetColorSpace_mDD98A093493C3E1659125B797F0E8C49E2C25E76 (void);
extern void XRCameraFrameExifData_TryGetPhotographicSensitivity_m215A7867030DC8738CFDF104E0CE9DA2569A6307 (void);
extern void XRCameraFrameExifData_TryGetMeteringMode_m3DE1E2935F15F9F91899A58E0585C3CB1E7BFE34 (void);
extern void XRCameraFrameExifData_get_hasAnyProperties_m608D0509C48BB26408E3B58ADE152ED372101624 (void);
extern void XRCameraFrameExifData_Equals_mE16880FA32EC214A99E76AA88BC65988E6A61919 (void);
extern void XRCameraFrameExifData_Equals_m55925A51CE7F4B028CF9B77393BD8FCCB56F7302 (void);
extern void XRCameraFrameExifData_op_Equality_m3497247DF5744B5F4CBC6F73C0A93D7A600B5235 (void);
extern void XRCameraFrameExifData_op_Inequality_mF774C01339682698B7BACA01AEEF2A6650E8FD69 (void);
extern void XRCameraFrameExifData_GetHashCode_m3FB9ECCBAE6452B72A6B167F589098899819AFAA (void);
extern void XRCameraFrameExifData_ToString_m9200B453787D807084089B68F941C9B768A8D374 (void);
extern void XRCameraIntrinsics_get_focalLength_m9B19B7C0AF4CDAF1C8BA121C20BE8A80A7DF778D (void);
extern void XRCameraIntrinsics_get_principalPoint_m677A9880F319E54576353AD01EF0936317E1D83D (void);
extern void XRCameraIntrinsics_get_resolution_mDC07EA111909E8903F1B89577FA2A6BF8FB14D52 (void);
extern void XRCameraIntrinsics__ctor_mA7F2F4A7709FC7DA6E9560367A08C28374365020 (void);
extern void XRCameraIntrinsics_Equals_m7C6C306C554F5F2A69E5CB831FB2C38F7A252866 (void);
extern void XRCameraIntrinsics_Equals_m81F681CB1C13344784F9B2DD6ACE032F2C9A06AE (void);
extern void XRCameraIntrinsics_op_Equality_mFD411F2F4CA10EBCF11DFCECE0E687FB5C3473CB (void);
extern void XRCameraIntrinsics_op_Inequality_m0394B23AFD6845446CADBEA312E54D70A72BAB90 (void);
extern void XRCameraIntrinsics_GetHashCode_mEC06B793ED903AC34149EAA935C66284947CDF63 (void);
extern void XRCameraIntrinsics_ToString_m3C7131BDF5882D7F01DA3EC7A7544A16E7F6A783 (void);
extern void XRCameraParams_get_zNear_mECA80F2D2C74318641F94031BB7964DD06ABEA75 (void);
extern void XRCameraParams_set_zNear_m13DFECBAE558037DEBE998F3EFF2E1C6372EE6E0 (void);
extern void XRCameraParams_get_zFar_mDF023917C3AD6AA2C909A2295219F34B085638DA (void);
extern void XRCameraParams_set_zFar_mA528373BCB66A9DE2A393512B883B932AB02D600 (void);
extern void XRCameraParams_get_screenWidth_m791F2E175953698508D73BF5B38087BA66875FDA (void);
extern void XRCameraParams_set_screenWidth_mA367A9BD005F2F052549E9B509F4D41F276CA021 (void);
extern void XRCameraParams_get_screenHeight_m560E3D0692A29242E6E137CA8895C3754E8A7745 (void);
extern void XRCameraParams_set_screenHeight_m7F6C7A3B7F3D7AEBE074A19FF20EF3DDACB79DE9 (void);
extern void XRCameraParams_get_screenOrientation_m0EC129A67B19D30348027E60C9A6C982DBC89D3A (void);
extern void XRCameraParams_set_screenOrientation_m9AA6D552ED0B67E9560CDF2C775FC27AA7A83A07 (void);
extern void XRCameraParams_Equals_m5C32A8D9FE83014E8A424C2D09688635E88A86B0 (void);
extern void XRCameraParams_Equals_mD063C934A21CE21B40F834E0C90AFF645A236CD6 (void);
extern void XRCameraParams_op_Equality_mBF89D93B1308F6832CF32F04CD8A2AD0D1AB4FF8 (void);
extern void XRCameraParams_op_Inequality_m659EE8FE4FD6A1ED934D78C8314763D26607C496 (void);
extern void XRCameraParams_GetHashCode_m1F7C2E3CC02169BDEC645B0CE0E540BB1FE1FCD5 (void);
extern void XRCameraParams_ToString_mFD8C6218C724EAAF2F9A953CBB08AA3BBF67DB64 (void);
extern void XRCameraSubsystem_get_currentCamera_mDF05518674B6B2670F8D1C4935E3A217A253F23F (void);
extern void XRCameraSubsystem_get_requestedCamera_m083BB1AB28B6688AF0288319A512C001FB1DE563 (void);
extern void XRCameraSubsystem_set_requestedCamera_m41B07B375D09940FD64D00A524676BFBEECE3743 (void);
extern void XRCameraSubsystem_get_currentCameraBackgroundRenderingMode_m683149B9AFDCDCBE8A77BDCCE2C00FB28CCBE001 (void);
extern void XRCameraSubsystem_get_requestedCameraBackgroundRenderingMode_m56DC4757DC2879EF6CDE27C0C15F62A6DD4945F7 (void);
extern void XRCameraSubsystem_set_requestedCameraBackgroundRenderingMode_mA90F7E36243E2F47695F2AAF67DB8DE33FFB9866 (void);
extern void XRCameraSubsystem_get_supportedCameraBackgroundRenderingMode_mBF24A55AEA6D0373F34D920A9C3D49F6B710B51C (void);
extern void XRCameraSubsystem_get_autoFocusEnabled_mBBD1CBAE8AA82A03319FC2A63C7E60C779D510B1 (void);
extern void XRCameraSubsystem_get_autoFocusRequested_mAAA29953704501756BA8C2F2D89CEC7C50409463 (void);
extern void XRCameraSubsystem_set_autoFocusRequested_m1E4DF6EAB729F7F33E7FA0FC22DDFEFF01621D1F (void);
extern void XRCameraSubsystem_get_currentLightEstimation_m4D1383D9B31058CD35AEA3EA516128A33008D75F (void);
extern void XRCameraSubsystem_get_requestedLightEstimation_m752067094CD340F41677378C95964CF917BC035E (void);
extern void XRCameraSubsystem_set_requestedLightEstimation_mC26C254AAFF86EF0566097155BD192D8B83E7906 (void);
extern void XRCameraSubsystem_get_cameraMaterial_m72EF1ABC5722AF08B9558BAA648128D30EDA409F (void);
extern void XRCameraSubsystem_get_permissionGranted_mA3BCBF249E8BFBC8BD5D04C4610254E105D41038 (void);
extern void XRCameraSubsystem_get_invertCulling_m02A291BD6E8007DB0194B976901890AFF3BB9E52 (void);
extern void XRCameraSubsystem_get_currentConfiguration_m11C191A5186B39689AC1B56D4984C496C4A6AD68 (void);
extern void XRCameraSubsystem_set_currentConfiguration_m2D5F8AD3C0CD8BF214BEB6EC0BC2214D8C19EE4C (void);
extern void XRCameraSubsystem__ctor_m1BE10CD8CF719A3400CFEF6AD97D8E025CA91504 (void);
extern void XRCameraSubsystem_GetTextureDescriptors_mBA520669443C18378EF60F9C4359CB540021BB95 (void);
extern void XRCameraSubsystem_OnBeforeBackgroundRender_m32E2330EDF383ED56B8517A3492C7338A034C102 (void);
extern void XRCameraSubsystem_TryGetIntrinsics_m9760F1024CF37C40BDD55BCDF05AA890989FBE65 (void);
extern void XRCameraSubsystem_GetConfigurations_m1DEEFD0803F83FCEE290A5DF70A0B421CECB8EAE (void);
extern void XRCameraSubsystem_TryGetLatestFrame_m0C289061CF62517D75F72DF57CDAB1B1DCEF1B58 (void);
extern void XRCameraSubsystem_GetMaterialKeywords_mCCA840FB3CC02F9AC53264AD51319FBD123BF120 (void);
extern void XRCameraSubsystem_TryGetLatestImage_m3817BC7E9C193CAF5E89C71CF5FE65D78EE0356E (void);
extern void XRCameraSubsystem_TryAcquireLatestCpuImage_m68B67E969F8C47578A2C795B0F7F863B453F0F5A (void);
extern void XRCameraSubsystem_Register_mABE4E80E5EB7E46A991A68B8D13FA333A1FFA031 (void);
extern void Provider_get_cpuImageApi_m29409A35FC66CBB351613BCA11B5FB4CBE34E4FE (void);
extern void Provider_get_cameraMaterial_mAF01432424E8C359F13EDAC37BC017D4CA800ACE (void);
extern void Provider_get_permissionGranted_m8D05057122F2BBC110DD9AD9A5FB66E0312AF7C9 (void);
extern void Provider_get_invertCulling_m5025907EE0C401AB84F9C69F4B4720D1B2FE0D94 (void);
extern void Provider_get_currentCamera_m8530CF31483EFC1C3258E16CA3ADAAC4EFAE3DE8 (void);
extern void Provider_get_requestedCamera_mB8CEFB334B24A23B0874F271FB9B37DCB31C26A4 (void);
extern void Provider_set_requestedCamera_m47BE9F53F40D3C42E3B833CC80284E6D2800D766 (void);
extern void Provider_get_autoFocusEnabled_m9F658CC090005D6F724B6D0A96E93B6D3F3F5774 (void);
extern void Provider_get_autoFocusRequested_mE38DE01AD3FF2729846B8FD792319D439EB4A774 (void);
extern void Provider_set_autoFocusRequested_m4516914B85E2D883A12F0C525A59E9D3FF3B9D64 (void);
extern void Provider_get_currentLightEstimation_m5401CCF4ADDDAD2C8F5E6091D5061D49B0BD9B8E (void);
extern void Provider_get_requestedLightEstimation_m7341D9AA92C5509B4CA1EEBB0D4D9278F38D0D04 (void);
extern void Provider_set_requestedLightEstimation_mEB829A0CE9D43CF8E4ED455C838F3A48FFA63355 (void);
extern void Provider_get_currentConfiguration_m0BDEF4F581B6343B544FC0C9D9B859DEA95A71B2 (void);
extern void Provider_set_currentConfiguration_mC964C94FB6C439A55027A6EEBCC981E6B9AD9102 (void);
extern void Provider_get_currentBackgroundRenderingMode_mCAAC7D83E29EA6CE1E99B4D5A4EA7F967CA08D73 (void);
extern void Provider_get_requestedBackgroundRenderingMode_mA4BB4A4DBD2BDB9E383B05021FFCFBC2B282C244 (void);
extern void Provider_set_requestedBackgroundRenderingMode_m7616B5F4D072F24B0ABAC20BB9F6E1967CD454BD (void);
extern void Provider_get_supportedBackgroundRenderingMode_m8AAEBBBAD356DB94F8D79F0B9EB1BDF983279D40 (void);
extern void Provider_Start_m4CD2850BF6C807410FA212456000E8B7D2A1C6D8 (void);
extern void Provider_Stop_m4CB61ECB8BA1E70EF743BA35E2A5EB2B03B9D64E (void);
extern void Provider_Destroy_m8392AF597AD7FE60AC7BA5CBE3DC0996E6AB677C (void);
extern void Provider_TryGetFrame_mECA5AA7D54F5FCAF960E81AD40A1EDC94379ED42 (void);
extern void Provider_TryGetIntrinsics_m111DCBBE57EFDFF6C6C66F311A20CAFB2F38D2BA (void);
extern void Provider_GetConfigurations_m43EDF5C2E292648F155EFAADA6B58677D4180F3A (void);
extern void Provider_GetTextureDescriptors_m40D6B5D3886EFA8284D14CD5FD67FA122CAB0976 (void);
extern void Provider_GetMaterialKeywords_m957D06365DD9D0925CBE170AEF4EAFA4262EF945 (void);
extern void Provider_TryAcquireLatestCpuImage_mEA8D19453751CDE916559C07E4EF7D2A6A623258 (void);
extern void Provider_CreateCameraMaterial_mEABBEF06EDE2ABD42F8DEDD94D84B0324F80C9AE (void);
extern void Provider_OnBeforeBackgroundRender_mDC6772B81F39571941F561DC85FC7007E083909C (void);
extern void Provider__ctor_mC30CBDE9E605D4DD1F82355FBC92F02272A9D132 (void);
extern void XRCameraSubsystemCinfo_get_id_mDCD0C107058AEA702A80B8E305F262CAB8E07FD0 (void);
extern void XRCameraSubsystemCinfo_set_id_m24A04B94756616FBA387977AF0F6A894D4DC5BCE (void);
extern void XRCameraSubsystemCinfo_get_providerType_mFBE1614FF701AC94FE53078962B455A355EA45A7 (void);
extern void XRCameraSubsystemCinfo_set_providerType_mFE0D3D8FEAF0FFEEA66D0E2C7CABB36944EAD484 (void);
extern void XRCameraSubsystemCinfo_get_subsystemTypeOverride_m5CD01638A223E6C620ADDDDBA05F7539ED5229C4 (void);
extern void XRCameraSubsystemCinfo_set_subsystemTypeOverride_m82AD4886D0CE8C4D762AC459630CBE13D024FCD2 (void);
extern void XRCameraSubsystemCinfo_get_implementationType_mCD8363878940A476D3A3D5CD999A01ED736529F7 (void);
extern void XRCameraSubsystemCinfo_set_implementationType_m8A37CCAA77FD7B184244763108178DC60BA69D0E (void);
extern void XRCameraSubsystemCinfo_get_supportsAverageBrightness_m694C685E738909EABF44140E672CB8176649D7E6 (void);
extern void XRCameraSubsystemCinfo_set_supportsAverageBrightness_mD5F49B41F00DBC4C531FB5593E572E6A67B1EE77 (void);
extern void XRCameraSubsystemCinfo_get_supportsAverageColorTemperature_mDCCD0A414E292EAD5FB817C6AC16DE8AF8C7D076 (void);
extern void XRCameraSubsystemCinfo_set_supportsAverageColorTemperature_mBF21A51F1C4ED4A72AF54588CF9E08DDC92A213B (void);
extern void XRCameraSubsystemCinfo_get_supportsAverageIntensityInLumens_mD46BA194C3AC26510694D649673015A450D0F019 (void);
extern void XRCameraSubsystemCinfo_set_supportsAverageIntensityInLumens_m7FEF058FFE0C0B54E91A8FC8500F88C17B294743 (void);
extern void XRCameraSubsystemCinfo_get_supportsCameraGrain_m9D63DE92F3FD22536481F75DC9092F3526EB9FF2 (void);
extern void XRCameraSubsystemCinfo_set_supportsCameraGrain_m3E5D929246B89F7B2EFE1F7E75B9A20C67A556C9 (void);
extern void XRCameraSubsystemCinfo_get_supportsColorCorrection_m8BEA88F615A8CD5FC068D9F7BE8039C68A110A15 (void);
extern void XRCameraSubsystemCinfo_set_supportsColorCorrection_m5E9520636C4268DFAFB9E84E5901FD65E14BBB7F (void);
extern void XRCameraSubsystemCinfo_get_supportsDisplayMatrix_m33AECE0011BFF7206E0F6A99C3C32ACD28DF09B0 (void);
extern void XRCameraSubsystemCinfo_set_supportsDisplayMatrix_m0AF4D70253EF5AC5124C747D6F1AF39B6E199B53 (void);
extern void XRCameraSubsystemCinfo_get_supportsProjectionMatrix_mD62E482D66D23D02FBF1591EFE8CBF6AB2B7AA60 (void);
extern void XRCameraSubsystemCinfo_set_supportsProjectionMatrix_m5A4C42AD394D5FEA20DB5FE1C045634929839B7A (void);
extern void XRCameraSubsystemCinfo_get_supportsTimestamp_m53905529FC20BB4B064986AC5E6586DAF996148F (void);
extern void XRCameraSubsystemCinfo_set_supportsTimestamp_m46561CEC2016CA165B4E725395C0E8836C0B46F3 (void);
extern void XRCameraSubsystemCinfo_get_supportsCameraConfigurations_m107FD5D148D109EDDE9345754995ACF01D7A3F67 (void);
extern void XRCameraSubsystemCinfo_set_supportsCameraConfigurations_m5425AF5D348E1918644909C45ABA7220D2A8B92F (void);
extern void XRCameraSubsystemCinfo_get_supportsCameraImage_m3C11104CBEE0AF690F3A4F9729F78F48E1065970 (void);
extern void XRCameraSubsystemCinfo_set_supportsCameraImage_mC9312D97D6F2508F8692EF4C40909CBA55F6D769 (void);
extern void XRCameraSubsystemCinfo_get_supportsFocusModes_m0B2A62A1F5A5603B2BE190A5B5AB0703842E6B51 (void);
extern void XRCameraSubsystemCinfo_set_supportsFocusModes_m7A495A132ED5160BF69E69CAAA132F83319A3191 (void);
extern void XRCameraSubsystemCinfo_get_supportsFaceTrackingAmbientIntensityLightEstimation_mA6C68E53BB8242F222104A33EA99EF7AFF754A9B (void);
extern void XRCameraSubsystemCinfo_set_supportsFaceTrackingAmbientIntensityLightEstimation_m17EC791181F76254B286308B213F19E085DE36D9 (void);
extern void XRCameraSubsystemCinfo_get_supportsFaceTrackingHDRLightEstimation_m28390AEBE6EBE78D410C0CB9D8D7DBE237D8DEFF (void);
extern void XRCameraSubsystemCinfo_set_supportsFaceTrackingHDRLightEstimation_m022FA258FE1F3E9D1D94D02985C3459298093D3A (void);
extern void XRCameraSubsystemCinfo_get_supportsWorldTrackingAmbientIntensityLightEstimation_m85AD9C10C625555A50D39FE3FE5B75E4DFEAC0A4 (void);
extern void XRCameraSubsystemCinfo_set_supportsWorldTrackingAmbientIntensityLightEstimation_m5F43C3317A17DAFA6ACF9550069FB10F8EB50300 (void);
extern void XRCameraSubsystemCinfo_get_supportsWorldTrackingHDRLightEstimation_mACCAED13DF9023738352CB5C89D5CBCF01B46899 (void);
extern void XRCameraSubsystemCinfo_set_supportsWorldTrackingHDRLightEstimation_mF04D46841BB7C8BEF8A17300F6F12491A8AC895C (void);
extern void XRCameraSubsystemCinfo_get_supportsExifData_m594585E6727EED9B24E835F2E14109565327F680 (void);
extern void XRCameraSubsystemCinfo_set_supportsExifData_mBAA63CA8656661FF0D35EB93605744BF7ADCA0B7 (void);
extern void XRCameraSubsystemCinfo_Equals_mED29F3CB627AF187AC3CB817FC3DC7905B0228A0 (void);
extern void XRCameraSubsystemCinfo_Equals_m6D8C4679C25B3C93C0BAC84C2E8214DC861285F3 (void);
extern void XRCameraSubsystemCinfo_op_Equality_m2221466839FF5329486081E62F5C2EC619F01063 (void);
extern void XRCameraSubsystemCinfo_op_Inequality_m0FDA17789BE28772ACE584DF0D0348C2E08110D1 (void);
extern void XRCameraSubsystemCinfo_GetHashCode_m9AD75EE7E43274694277A4703099621467E69114 (void);
extern void XRCameraSubsystemDescriptor__ctor_m82691F2759363796E9011E462546B4148C2D0BF1 (void);
extern void XRCameraSubsystemDescriptor_get_supportsAverageBrightness_m394F0A5371B43E4A53DF4AAF18EC2CB3A3BA30E4 (void);
extern void XRCameraSubsystemDescriptor_set_supportsAverageBrightness_m85E4E5A8B48C865C09FD14FA2DA245172F82B5C9 (void);
extern void XRCameraSubsystemDescriptor_get_supportsAverageColorTemperature_m45A35D2620A0744755E9073E1E8CC96EDF8992F6 (void);
extern void XRCameraSubsystemDescriptor_set_supportsAverageColorTemperature_m90107A708A0D68DC9B400DF24DAED2365A5024EB (void);
extern void XRCameraSubsystemDescriptor_get_supportsAverageIntensityInLumens_m15A6095FCF7EE2C44DA00C8A7EAA0FB185F6148C (void);
extern void XRCameraSubsystemDescriptor_set_supportsAverageIntensityInLumens_m8CD28D5D10E6A269D79E2308FC675AF4FDA28313 (void);
extern void XRCameraSubsystemDescriptor_get_supportsCameraGrain_mA7563A6FB9661F093C972DBF54BC9A3F372624F7 (void);
extern void XRCameraSubsystemDescriptor_set_supportsCameraGrain_m3C600B4A99E724820D2FAF3F08DA2C56B0D70D4F (void);
extern void XRCameraSubsystemDescriptor_get_supportsColorCorrection_m380D4EFA54BD40435F3EC0D4811C17600F60FA3B (void);
extern void XRCameraSubsystemDescriptor_set_supportsColorCorrection_m1242C61D6537FCEA26482B5D74DB1943F7E2326F (void);
extern void XRCameraSubsystemDescriptor_get_supportsDisplayMatrix_m9AA5FE7C2A422C5B7C85EEB1DEF1E9058D2ECA11 (void);
extern void XRCameraSubsystemDescriptor_set_supportsDisplayMatrix_m5589A27DB1C3D5A896E9683287DAF5957B8B110D (void);
extern void XRCameraSubsystemDescriptor_get_supportsProjectionMatrix_mD16AFD308BF077947336064405B66A3762AE0844 (void);
extern void XRCameraSubsystemDescriptor_set_supportsProjectionMatrix_m533035E1EA87892B36B8BD1FCA6DD12C48125F0B (void);
extern void XRCameraSubsystemDescriptor_get_supportsTimestamp_mB83389AB4E0CCEB67856DF32DA1EDEC64E5A2DEA (void);
extern void XRCameraSubsystemDescriptor_set_supportsTimestamp_m2077AC114ABD729DB39DB4DB648D54BFA7842C9D (void);
extern void XRCameraSubsystemDescriptor_get_supportsCameraConfigurations_mDA577F21C255F11827B6226688C41D1DDE20A753 (void);
extern void XRCameraSubsystemDescriptor_set_supportsCameraConfigurations_m94AFD663BE701A019D04CC7E070361F713053B94 (void);
extern void XRCameraSubsystemDescriptor_get_supportsCameraImage_m4C81161C7C5D5873D7673681206B1B8B0D158F50 (void);
extern void XRCameraSubsystemDescriptor_set_supportsCameraImage_m1AC168FAB7E1344E8EBCE561A8A2C5E0FD8D3456 (void);
extern void XRCameraSubsystemDescriptor_get_supportsFocusModes_m629008B6904FEC5721E3BB251E549D74553981C2 (void);
extern void XRCameraSubsystemDescriptor_set_supportsFocusModes_m898E4A424D88ECA8BDC410BC7DD4DA8ADADC3469 (void);
extern void XRCameraSubsystemDescriptor_get_supportsFaceTrackingAmbientIntensityLightEstimation_m32CA41294F34BA9A20D90F8AA1204DA923C72337 (void);
extern void XRCameraSubsystemDescriptor_set_supportsFaceTrackingAmbientIntensityLightEstimation_mB285F66614F9E813114F14E03D6ACF0C713B061C (void);
extern void XRCameraSubsystemDescriptor_get_supportsFaceTrackingHDRLightEstimation_mA892EB29FFD62B3C49E5EA4834CF5C27DE39824F (void);
extern void XRCameraSubsystemDescriptor_set_supportsFaceTrackingHDRLightEstimation_m21A59BE7013A7C47829CDC98879B536C8D168628 (void);
extern void XRCameraSubsystemDescriptor_get_supportsWorldTrackingAmbientIntensityLightEstimation_m1F47D027047EBB365C69BE50F442BEFEE5A5CA94 (void);
extern void XRCameraSubsystemDescriptor_set_supportsWorldTrackingAmbientIntensityLightEstimation_m5514636E6A56C6528D0E411F9FDAEAA23BB35D9E (void);
extern void XRCameraSubsystemDescriptor_get_supportsWorldTrackingHDRLightEstimation_mF08EE18BA151F1D274A100ECD32045427C76EB83 (void);
extern void XRCameraSubsystemDescriptor_set_supportsWorldTrackingHDRLightEstimation_m98835252EB322907CE7E1C1214F7A125C81570B8 (void);
extern void XRCameraSubsystemDescriptor_get_supportsExifData_m968A05AC795750EFA35B4CDA5CF4B448B0896068 (void);
extern void XRCameraSubsystemDescriptor_set_supportsExifData_mC70AF5A20E0694668614C5EA974422E3A8DEF257 (void);
extern void XRCameraSubsystemDescriptor_Create_m5F5E106B8F6CB7B780CD1CF7EE800AA59A43BCF2 (void);
extern void Configuration_get_descriptor_m3C4973351367EA0BD9E48DA1E2201D8803BA8D1E (void);
extern void Configuration_set_descriptor_mBB8354A895DDAD560293EEF81BFFDB4CB30070F0 (void);
extern void Configuration_get_features_m704F372E940AF1DB435C1EBFF8E48EAD4E8B3776 (void);
extern void Configuration_set_features_m9F397F777C9593646918ECB4AF778336900ED3EC (void);
extern void Configuration__ctor_m4D712D942AEBEF0DA6B5687C1D9CD4E24F0ED4AE (void);
extern void Configuration_GetHashCode_m19DCAAF7939DB5DAAF29A2A4E994D41F66FB73D2 (void);
extern void Configuration_Equals_mFC36BD166DE654A704096918BDA1FE9E34A7B7E6 (void);
extern void Configuration_Equals_m8D6DE5FC0FAD2DD34D2F3CEF1738FC3A2F131A91 (void);
extern void Configuration_op_Equality_mF1F31AFFDE2EB88637CA97D3F8D2DC788C0A96C8 (void);
extern void Configuration_op_Inequality_m02D4E5E82910A6C7BBCEC7B3ED5C6DA8B5608010 (void);
extern void ConfigurationChooser__ctor_mBA387FADB0244DB6C71741AB9DE75E881B15B803 (void);
extern void ConfigurationDescriptor_get_identifier_m858F4B730002C1823D283460115DA65C6A46BCB6 (void);
extern void ConfigurationDescriptor_get_capabilities_m6A4EF4C0E0FE3671E8564EF13BA2A5B4264CF938 (void);
extern void ConfigurationDescriptor_get_rank_mEDFBF5E2173FA84A0695BB01A6A40860794F6FA8 (void);
extern void ConfigurationDescriptor__ctor_m79BD6295C5A725B6B65CA3A4281EC801C12B2C41 (void);
extern void ConfigurationDescriptor_HexString_mA5D97CE4BCD0DD66455BB9BE281302136382BCD5 (void);
extern void ConfigurationDescriptor_ToString_m20EA191A42A1855B5E97CD8949F6AE5B9ACBDF65 (void);
extern void ConfigurationDescriptor_GetHashCode_mAD2765B79FFD1806DEA8D927D928C496AAADB411 (void);
extern void ConfigurationDescriptor_Equals_mC5F92BBF22292A48CAD47A31EF13F3D5A0DC4091 (void);
extern void ConfigurationDescriptor_Equals_m4FAAC4A13BF03211A9C3EB66F65FB48BE334A611 (void);
extern void ConfigurationDescriptor_op_Equality_mF2D570C2A6B27299C7DA2656A99471085D37A572 (void);
extern void ConfigurationDescriptor_op_Inequality_m6D2C9B33FEC965851D3277704072A6E03E6C6EB2 (void);
extern void DefaultConfigurationChooser_ChooseConfiguration_mBFF0C082E3C3A36847A725E3C44102C60298DC63 (void);
extern void DefaultConfigurationChooser__ctor_mDFBE2A3915F886FE4D79CD514F550A10CCC6A90C (void);
extern void FeatureExtensions_Any_m4AFD1CF424DDB83A30DBB1C78A58F5DC81308573 (void);
extern void FeatureExtensions_All_m23416D7BEA5829E61EBCF25D41C82B9190E19D11 (void);
extern void FeatureExtensions_None_m92BF4CEBE40BF1721C9B28F085B7C7B1D7080794 (void);
extern void FeatureExtensions_Union_m28DDE32C51A4F6B39DCDCCB431D962639EF9696E (void);
extern void FeatureExtensions_Intersection_m81F55D33EE5F952118B65F491B3A26C531A2C0F1 (void);
extern void FeatureExtensions_SetDifference_mA9D5A50B2BF9B5C66047D6152384DC14D5BBCE9F (void);
extern void FeatureExtensions_SymmetricDifference_m875C129D61A47A27FA8E5DBD32C5CBCBEF6ECB7D (void);
extern void FeatureExtensions_SetEnabled_m52E127CF05A9ED2AF02F3775040A9073741E9C7E (void);
extern void FeatureExtensions_Cameras_m726A6A7FE0A234C11F8FCF7DD3A4AA94E3ADBC44 (void);
extern void FeatureExtensions_TrackingModes_m98FFB5EE942CA939B0F4F06A425673A4081287B7 (void);
extern void FeatureExtensions_LightEstimation_mB23FB857088B2902CF6ACC21DCEBA3995D8F3038 (void);
extern void FeatureExtensions_WithoutCameraOrTracking_mCDEF4FCD59CC8C1688E0BE66376EE64583B6E0DF (void);
extern void FeatureExtensions_LowestBit_m0E549BA3822C6732458DAB421C673B7D774047DF (void);
extern void FeatureExtensions_ToStringList_m7961C38D97DCDD6FB5EAC9AB77104F97D5304214 (void);
extern void FeatureExtensions_Count_m4115A16C8A0123EFF727DA3A198C09F2B1B327A8 (void);
extern void XRCpuImage_get_dimensions_m49AF06CB1BDF89E7C9EC343D3260BD73ECABF414 (void);
extern void XRCpuImage_set_dimensions_m67B3C05A3CA2F0CED5B4E1808967FF2BF77AED86 (void);
extern void XRCpuImage_get_width_m176240EBEBBD41DC5AEF33F945C88E9492370AFA (void);
extern void XRCpuImage_get_height_m139489AD26B264FA46EE5659258BBF9C6584E5E9 (void);
extern void XRCpuImage_get_planeCount_mEDCBE71D55CCC9FDA1B3ED951306875283E37B6B (void);
extern void XRCpuImage_set_planeCount_m51DC647BC967DE5E565AA4ACF66B5B86FE380B13 (void);
extern void XRCpuImage_get_format_mB777BBC485ED5A88CD78536F78F43E9795DEEE20 (void);
extern void XRCpuImage_set_format_mBC167A4F6985102169436A10C58AF5EBC17B4C1F (void);
extern void XRCpuImage_get_timestamp_mA80E146875C26B8F319B283C20A6BD499AD55B90 (void);
extern void XRCpuImage_set_timestamp_m7FF97B03D5A4506993F8119BCB4BC47B185AA8D1 (void);
extern void XRCpuImage_get_valid_mFF799BC0D09BF35BD3AEC063FF5558EE2EB6766F (void);
extern void XRCpuImage__ctor_m06AE81550FF74789CD8D66ABBA9B2F3D9D060612 (void);
extern void XRCpuImage_FormatSupported_m15D60F33E5EB00039BA41B9C61AE114C1AA6B40A (void);
extern void XRCpuImage_GetPlane_m0C2A7BE6FE964FCF5A82273AA9DA6C135648721B (void);
extern void XRCpuImage_GetConvertedDataSize_mC7AF0A096D1FF758D3E086D3D43F778E9257D4BE (void);
extern void XRCpuImage_GetConvertedDataSize_m1A292AE01390513BEA935CC4C19A0F8FD52341DE (void);
extern void XRCpuImage_Convert_m04EB3992B85AEB87D03C5626EFD0A9C0158AC9CB (void);
extern void XRCpuImage_Convert_mFE71425F0E4FD4ADB839551590FE9728BA037EE1 (void);
extern void XRCpuImage_ConvertAsync_mECB96371D5F7C49A4E995B285F9FC02FE4109814 (void);
extern void XRCpuImage_ConvertAsync_m881B8A7A185F454AB34151D858CA0AE8A54F2949 (void);
extern void XRCpuImage_OnAsyncConversionComplete_mDC3A0C88A34909C9D08E4BE7E94C8E27E2BB3D3C (void);
extern void XRCpuImage_ValidateNativeHandleAndThrow_mA1A3B64DF91A003BDB013FC04111945F03853395 (void);
extern void XRCpuImage_ValidateConversionParamsAndThrow_m39B351E15FD65E6969933F0EBE42CA63DD090E72 (void);
extern void XRCpuImage_Dispose_m80B8CA56700DD5EB8A5613AA42F6F389D86A746B (void);
extern void XRCpuImage_GetHashCode_m4C976024EE9CFFDBE53682B8307FD0819F42E31C (void);
extern void XRCpuImage_Equals_mE00DCB100FC7743E62959883CBAF479ADEDCBAC3 (void);
extern void XRCpuImage_Equals_m0BD02471E5A85EFF5F078CE0ACEDD4F969B66AB2 (void);
extern void XRCpuImage_op_Equality_m155699D9F3137F2779D9746546CFDC64418CC85A (void);
extern void XRCpuImage_op_Inequality_m40C7681B3CD49484468D82E6990D1971E7F5B461 (void);
extern void XRCpuImage_ToString_mA4BF5B6A1D341098584B0EC3E51D324092AE98ED (void);
extern void XRCpuImage__cctor_m5EC261DAD69867C3BF19DCD92E72124ECBBC5E38 (void);
extern void Api_TryGetPlane_m6504F578CC422286093D4F66162407E8456990B1 (void);
extern void Api_TryGetConvertedDataSize_m33799E987ADF63F4B2C430ACECA42A1F3349E375 (void);
extern void Api_TryConvert_m2E4CE2B72ABB72C6D33BDB4FF5EB41AA8C9FDA1F (void);
extern void Api_ConvertAsync_m0F80B09CA4E70E682AE5863099ED353169B71BA6 (void);
extern void Api_NativeHandleValid_mDED64890874D05C8BADBDC831744F26712DE6116 (void);
extern void Api_TryGetAsyncRequestData_m6E9232C49B5D920C6BC3A8DA1CE050E4FC603B9E (void);
extern void Api_ConvertAsync_m125EC0A9F2ED20CDBFC87B8CD98F07F1FDF424FE (void);
extern void Api_DisposeImage_m5FD9A543A7EE8CBD5C7B19FE660143B667959FC8 (void);
extern void Api_DisposeAsyncRequest_mE3984202489CBC2A185CB634D3F92F67A33E5E96 (void);
extern void Api_GetAsyncRequestStatus_m505F65B4B84651CBC7CF070AA11980797D269C83 (void);
extern void Api_FormatSupported_m78D64CFE730EB2DFF3B70E6564A6A3230C5A5CA0 (void);
extern void Api__ctor_mFD77CE4DCDE143DFBF11744359F52058E9550F9D (void);
extern void OnImageRequestCompleteDelegate__ctor_m75183C0CE806605022429FD507627EE3EEF6D14B (void);
extern void OnImageRequestCompleteDelegate_Invoke_mB43B2DD8D5CC0863FF2A8A871D27A1AA2A3E5B37 (void);
extern void OnImageRequestCompleteDelegate_BeginInvoke_mC22A9776507508A3807BD3A17FEF6AC9F01F77B8 (void);
extern void OnImageRequestCompleteDelegate_EndInvoke_m744BF9BFE054522989EFE70441D818F76CEAA6BD (void);
extern void AsyncConversion_get_conversionParams_m467C52D7FD3E87614FA34FCB630EDD89289F12B0 (void);
extern void AsyncConversion_set_conversionParams_m5144DBB33F2D0003BF4E2ED884B9D8AA4EE89071 (void);
extern void AsyncConversion_get_status_mB3A873407AF4E202A39758599DCEE52BEC196E2A (void);
extern void AsyncConversion__ctor_mEEE052FCAD2BF2E9FBF78C829C0A11A6D5CD5ABD (void);
extern void AsyncConversion_Dispose_m81B54378570A2C9C1009618A0380E5C204DD2AC6 (void);
extern void AsyncConversion_GetHashCode_mF14F2C21ADCA19EB7300B2F07B59B2758CE0195D (void);
extern void AsyncConversion_Equals_m0D926BE22C3B6333F413DFB6E051021428C6D3D5 (void);
extern void AsyncConversion_Equals_m84273ECB152DB76244DFD3BF9BC2AB648682F4A0 (void);
extern void AsyncConversion_op_Equality_m372106330CD6036C3438EF5A04935D667BFA4E54 (void);
extern void AsyncConversion_op_Inequality_m1D69E9441AA22C440D64B666DC23FE5D9B897B54 (void);
extern void AsyncConversion_ToString_m59F4615A570F43A086B43067B37CCF382BEE5225 (void);
extern void ConversionParams_get_inputRect_m59986429062905012283B892A6EE2DAD88A810FC (void);
extern void ConversionParams_set_inputRect_m7965864AED4C5176D58F3766D6BBB35DFF7BC903 (void);
extern void ConversionParams_get_outputDimensions_m6295F96DCE9B406AB6D79E8CD86A6FF388CF5035 (void);
extern void ConversionParams_set_outputDimensions_m97EC09EE536EA456A18894311BF75AC9D5A90A3B (void);
extern void ConversionParams_get_outputFormat_m8CD52ADADE8FFE505A90E02D9BD6C7D9EE1C8715 (void);
extern void ConversionParams_set_outputFormat_mA82EA0ECC19D14AECBA318B9B485D08CFB46A1F4 (void);
extern void ConversionParams_get_transformation_m46ADA14AEDC98630828D5DCE19F1905233627CE7 (void);
extern void ConversionParams_set_transformation_mBCE73B14CCE8A31A258C6B8F6104446A2D495A0F (void);
extern void ConversionParams__ctor_m2EA9FC7BD411FA61269B314ACD03174F5BB96273 (void);
extern void ConversionParams_GetHashCode_m15B2EFDD22B43B4201646E49BCB9155F67D5A12A (void);
extern void ConversionParams_Equals_mD0A055A44755C75EFF2B09B2FD7C9C50D057020B (void);
extern void ConversionParams_Equals_mDC60F1518FE83109D22DB3A1606C82930B16356D (void);
extern void ConversionParams_op_Equality_m0A9298A5F5C511132C2CDE6DF114EE57868FA2E3 (void);
extern void ConversionParams_op_Inequality_m1E628FB6BF9975EE888EF0141A663C26C448A6F5 (void);
extern void ConversionParams_ToString_mB11C93DA6E5D57F694BC5D64E5567958A647557C (void);
extern void Plane_get_rowStride_m5461CF97009BA5CB09931F85D9C4E11BB298E01F (void);
extern void Plane_set_rowStride_m03DE76183744D782EAB661F50D8191DA9CB34A31 (void);
extern void Plane_get_pixelStride_m78990A3DB8530B302D4B138E92BFEFF6F6F8D5E0 (void);
extern void Plane_set_pixelStride_m26005BF1B5C79A0B816D37C53CF5A34BA3499CFC (void);
extern void Plane_get_data_m8A88D9DDDAB3081E788B3DCF7DE314D2E672B15D (void);
extern void Plane_set_data_mFDEC268CEEE5FEBFC54FE823BEC6B3DCC4DB182E (void);
extern void Plane__ctor_m9563F685C69A49502C62A7EA4F1F64FCC392A485 (void);
extern void Plane_GetHashCode_m81E44303AC89B3792D4238BEFF767D459D72FDD1 (void);
extern void Plane_Equals_m05599C5BA1316FF0667B8D3752DC2464E559A24E (void);
extern void Plane_Equals_mD97496D640121AA88AF730F5DDE9F1ED6582842A (void);
extern void Plane_op_Equality_mE9A7A4F2F95E182708D46301CA610E6802A2D0A8 (void);
extern void Plane_op_Inequality_mBDAE6B00116CC3DD27F637345A54730E25B57325 (void);
extern void Plane_ToString_m0844EFBBF3A11852B21C58FCBC543A554E838EE2 (void);
extern void Cinfo_get_dataPtr_m0865701DF77079918906809E61CCF8C080120AB1 (void);
extern void Cinfo_get_dataLength_mF704FE891CD1628CF48C8434DF1CD5C461A7EE86 (void);
extern void Cinfo_get_rowStride_m3CB25349C2380F5FC9022EB25A1FCF95C1498513 (void);
extern void Cinfo_get_pixelStride_m5A3C2E9C12F194F7237EF96FC12E319928E14A6E (void);
extern void Cinfo__ctor_mF1859C21D692CA1783BF64CDD8C45BB1984C427F (void);
extern void Cinfo_Equals_m8F4B3A7591D02605076B062B37312733D533D3AA (void);
extern void Cinfo_Equals_mA464FE0F8B24D6AACEE40E5A290572D4ABBE1333 (void);
extern void Cinfo_op_Equality_m1FA11367A4662FF019702F76F0AF2736862D8EAF (void);
extern void Cinfo_op_Inequality_m605AE7A7205120773F7D94208019138D4CD11858 (void);
extern void Cinfo_GetHashCode_mDD348F8626D5B49ED6EC593263AFD20A21329F08 (void);
extern void Cinfo_ToString_m6CC828632F333B765128A31434D3DFF040F90754 (void);
extern void Cinfo_get_nativeHandle_m63F2835811F1DCFF2EE1AEBF8A8F7A1ADA1FD7E0 (void);
extern void Cinfo_get_dimensions_m7B777F060E825839302EA722B35E1BBB4E402D2A (void);
extern void Cinfo_get_planeCount_m5D077F0399217E11C6A11378F5D08D86CC5CEA7F (void);
extern void Cinfo_get_timestamp_mFFA3FA7E91717B748F6159B7E78FBFE2290F4E97 (void);
extern void Cinfo_get_format_m0F7BD9189DB7D30D44882FF3F53EF2FFBD05C7DE (void);
extern void Cinfo__ctor_mB5890536579096BE14554ED96E868003C9A2CC5C (void);
extern void Cinfo_Equals_mEEFFAFF7E0FE0F0445AF0F96F66D5D68DF1BC3E1 (void);
extern void Cinfo_Equals_mBA9B7A52398AB07042615E27D6AC30542F9EF124 (void);
extern void Cinfo_op_Equality_mD8D46F2B59A546B6EF434C09F523E5A4AAFB2323 (void);
extern void Cinfo_op_Inequality_m67A4C5321E6B7BB1134746D68E682CD4FE379426 (void);
extern void Cinfo_GetHashCode_m26AA585D94FCF87B154728D81E40295716B37B03 (void);
extern void Cinfo_ToString_m47CB9E0B83E1E5C5CA1517D982A5E6FF4A9F05A3 (void);
extern void XRCpuImageFormatExtensions_AsTextureFormat_mEDB46C7DB6DE6C62926ECD7FE9EBAE000A4F7E18 (void);
extern void XRCpuImageFormatExtensions_ToXRCpuImageFormat_mFF86DA414D59D76AEB1D7D90BE52614F6F60F6CD (void);
extern void XREnvironmentProbe_get_defaultValue_m50BD745C4928AE4328C53906D672D5E2F4B37B85 (void);
extern void XREnvironmentProbe__ctor_m756BDCC73762A50BDAF24FD4F430D8F8EA18869D (void);
extern void XREnvironmentProbe_get_trackableId_m7B20AFD8D153397E7270F72C81B32043DA83C57F (void);
extern void XREnvironmentProbe_set_trackableId_mCAD11E54A600B26FDC6D546A15F5E13030605EE4 (void);
extern void XREnvironmentProbe_get_scale_m7C53FA5C36BD5616CCF2EDC543C260FD381BCB64 (void);
extern void XREnvironmentProbe_set_scale_m230AA2EF5AE396A8E5A43FE809BEEF811CE68302 (void);
extern void XREnvironmentProbe_get_pose_m56C2FCB790DC220FAE0339EFC82055360984CAF0 (void);
extern void XREnvironmentProbe_set_pose_m1A1776C7D4A99F29708883F081A54936BC46A4B0 (void);
extern void XREnvironmentProbe_get_size_m92A310E405DC33AFF0968D0B7C17BDB8D039A1B0 (void);
extern void XREnvironmentProbe_set_size_m191D7253226516A2BBC83D0DD28A154FAD2F3C33 (void);
extern void XREnvironmentProbe_get_textureDescriptor_mD514443491B53FCBC49AD477CC5C7C6084543FAD (void);
extern void XREnvironmentProbe_set_textureDescriptor_mAE07E8E52FD3D564E1366EC75E0B1EB80A1A43B0 (void);
extern void XREnvironmentProbe_get_trackingState_m4051D90D37D33EC33534368B64E5C85EA1888C83 (void);
extern void XREnvironmentProbe_set_trackingState_m8AC9F0BB01B0B26935D09B5A723680E658A3A196 (void);
extern void XREnvironmentProbe_get_nativePtr_m0C6C620B2D3C20FBE8AEE478EBEA0006E8E7FB40 (void);
extern void XREnvironmentProbe_set_nativePtr_m75CED6350AB93167C23B4E1A837879BCEB6A7AAC (void);
extern void XREnvironmentProbe_Equals_m891BD688A67E6AF40E4DE164936AFC6D59762AF0 (void);
extern void XREnvironmentProbe_Equals_m76FC4B88F469A7E33C17E4F9A59DBEBDF1A66745 (void);
extern void XREnvironmentProbe_op_Equality_mD7BD953290CF2F6877E43A59034537B7E51C1621 (void);
extern void XREnvironmentProbe_op_Inequality_m6439F11CC4F98DF61A14DEDAE538F7FA8A243E21 (void);
extern void XREnvironmentProbe_GetHashCode_mC8C8046B5523D71CADA65C3D38232925243CEA86 (void);
extern void XREnvironmentProbe_ToString_mBD160B7DBD096BB94201C93B1821FF73728C3E4F (void);
extern void XREnvironmentProbe_ToString_mFB69B6A7B36CD0B02B4283AEFF6CEFAA72EE8DB2 (void);
extern void XREnvironmentProbe__cctor_m96971D1EF22003241CE8C7D3859CDB667DC74057 (void);
extern void XREnvironmentProbeSubsystem__ctor_m5F2239415E12E56B1546F56C19E071ED2324ABF5 (void);
extern void XREnvironmentProbeSubsystem_get_automaticPlacementRequested_m0FADAEC1FEB2F7FD60A441AEB0B61841055CDE06 (void);
extern void XREnvironmentProbeSubsystem_set_automaticPlacementRequested_m5A612C8C22C3130F4C3684C78B20BE53871916D8 (void);
extern void XREnvironmentProbeSubsystem_get_automaticPlacementEnabled_m63BFBF6DD86556B87FE65173F947ED6EABD02668 (void);
extern void XREnvironmentProbeSubsystem_get_environmentTextureHDRRequested_mDFB6AA21C54DED1462E883E484B280F9D1725E04 (void);
extern void XREnvironmentProbeSubsystem_set_environmentTextureHDRRequested_m09413D30EC3C646DC22F8979EF34446034500048 (void);
extern void XREnvironmentProbeSubsystem_get_environmentTextureHDREnabled_m3784A8D3027A8EFFAD34A7FC85DAFD31576BCDDF (void);
extern void XREnvironmentProbeSubsystem_GetChanges_m7507B8AA278398E93F42DFA8CEC09240508F0C6B (void);
extern void XREnvironmentProbeSubsystem_TryAddEnvironmentProbe_m177BFC3E369E1F422BF9A0393324220BD60243D4 (void);
extern void XREnvironmentProbeSubsystem_RemoveEnvironmentProbe_mE940508A973468A3EF5916E3AEBE4236A8FC71B8 (void);
extern void XREnvironmentProbeSubsystem_Register_m0422985B0B805F5AB3B59B8AA91DBD229A06D74C (void);
extern void Provider_get_automaticPlacementRequested_m31751920D7587F49A5B53180EB9D43A4F11EC6DD (void);
extern void Provider_set_automaticPlacementRequested_m2309B486A1845A040EC6BC5E821B48091A736B52 (void);
extern void Provider_get_automaticPlacementEnabled_m51ACF2B2A9C8AB6E9F16FCEA6D6A44B108EA2F13 (void);
extern void Provider_get_environmentTextureHDRRequested_m93AE2DE32EA5E8C880137AC7B10C51F9AABFECFE (void);
extern void Provider_set_environmentTextureHDRRequested_m323DA868046B0E7806EB611A44C3CAF4996CD936 (void);
extern void Provider_get_environmentTextureHDREnabled_m53742D23697A648A200DFD6CF4A3288CBD2E4FD2 (void);
extern void Provider_TryAddEnvironmentProbe_mA6CAC6C36EB0D500270F11E086CAECFC62B4F805 (void);
extern void Provider_RemoveEnvironmentProbe_m653E5DEA379458A4CD9793EF60F85EC36E93AFED (void);
extern void Provider__ctor_m1439531E68FE8334150A68D79E0E1DF9F55DB64B (void);
extern void XREnvironmentProbeSubsystemCinfo_get_id_m590E5DEBA8C344FFEA51F351F99DEFFD703DC57F (void);
extern void XREnvironmentProbeSubsystemCinfo_set_id_m0157B51BEBA22C10D835283A2E342811FB3904E0 (void);
extern void XREnvironmentProbeSubsystemCinfo_get_providerType_mBC6F352EF89BED518336375D44A5D9CADDE464C1 (void);
extern void XREnvironmentProbeSubsystemCinfo_set_providerType_m053C0551B3059F11B606708ED8C6B3DA9C4CF2F2 (void);
extern void XREnvironmentProbeSubsystemCinfo_get_subsystemTypeOverride_m5A1A5D3ADB4E4304029B3F1688CEBFAB4E4C8F7E (void);
extern void XREnvironmentProbeSubsystemCinfo_set_subsystemTypeOverride_m89507B15871CD2E8F093EF084176BC98CAD3D1E4 (void);
extern void XREnvironmentProbeSubsystemCinfo_get_implementationType_m6C1B50EAB87465FE798443F1C2803F1A437EEC3E (void);
extern void XREnvironmentProbeSubsystemCinfo_set_implementationType_m09326FDF5E9024E0AA37F89388F8BDD4AB40E600 (void);
extern void XREnvironmentProbeSubsystemCinfo_get_supportsManualPlacement_mFA2560E13D32A8228105D6F205E109DEDE0BFB37 (void);
extern void XREnvironmentProbeSubsystemCinfo_set_supportsManualPlacement_mE948D3D31063A4015F18BABCD4475E2CEDD00E2C (void);
extern void XREnvironmentProbeSubsystemCinfo_get_supportsRemovalOfManual_mE1E3D58FB734841B3B1E885592C84A2748953847 (void);
extern void XREnvironmentProbeSubsystemCinfo_set_supportsRemovalOfManual_m9784D47E8903EC1BEFA722994586FF633BB09327 (void);
extern void XREnvironmentProbeSubsystemCinfo_get_supportsAutomaticPlacement_m01E383AF1BDC43E0755B0C99BF838698B2FAD67C (void);
extern void XREnvironmentProbeSubsystemCinfo_set_supportsAutomaticPlacement_mD728F648E5A75DC074D9718432F07D0D460A289D (void);
extern void XREnvironmentProbeSubsystemCinfo_get_supportsRemovalOfAutomatic_m5C05042091803872B2B5FE45C791A728F92B5A1A (void);
extern void XREnvironmentProbeSubsystemCinfo_set_supportsRemovalOfAutomatic_mBD5F44750877ADEFD8C6207F0B30D0FE15B70526 (void);
extern void XREnvironmentProbeSubsystemCinfo_get_supportsEnvironmentTexture_m7B872B5FE1B9517D9626E269E09B7C4C16E06A06 (void);
extern void XREnvironmentProbeSubsystemCinfo_set_supportsEnvironmentTexture_m724298B12B4DDEFC4C1274A551E04299A040374D (void);
extern void XREnvironmentProbeSubsystemCinfo_get_supportsEnvironmentTextureHDR_m7AC20590F176681AD486ACC8C9A0189209B1C35E (void);
extern void XREnvironmentProbeSubsystemCinfo_set_supportsEnvironmentTextureHDR_mEED10741684E8045104833D1A58A1A02612FBE4B (void);
extern void XREnvironmentProbeSubsystemCinfo_Equals_m4297DA57D68A4D83010A2D370092CB925B89CEA0 (void);
extern void XREnvironmentProbeSubsystemCinfo_Equals_m8AE52E39A349B7A5E21C624AE22C4A1078E8C327 (void);
extern void XREnvironmentProbeSubsystemCinfo_op_Equality_m77A5296B22E757B33E660AF0416911EFE7835224 (void);
extern void XREnvironmentProbeSubsystemCinfo_op_Inequality_m4CD5804188D309BC1C7949E12D649B121CF28437 (void);
extern void XREnvironmentProbeSubsystemCinfo_GetHashCode_m66A5517BE16EE35C50CBD336B9B2928760E07121 (void);
extern void XREnvironmentProbeSubsystemDescriptor__ctor_m2E853F070162B34D7425CE9C22A076DF327D5820 (void);
extern void XREnvironmentProbeSubsystemDescriptor_get_supportsManualPlacement_m129575B9630FCCF68A6B6074DA7DB8B1722BB152 (void);
extern void XREnvironmentProbeSubsystemDescriptor_set_supportsManualPlacement_mD57435B5702FA32561338DB692DCC27DBD3230D9 (void);
extern void XREnvironmentProbeSubsystemDescriptor_get_supportsRemovalOfManual_m1C15402FEC435594A1E9DC9CD0AF63628734E190 (void);
extern void XREnvironmentProbeSubsystemDescriptor_set_supportsRemovalOfManual_m193D875922BA821AC499A40B594FB6E64258EAC7 (void);
extern void XREnvironmentProbeSubsystemDescriptor_get_supportsAutomaticPlacement_m88925DCB0DBEA8BFD4A53848FDA8B54652A0FBA0 (void);
extern void XREnvironmentProbeSubsystemDescriptor_set_supportsAutomaticPlacement_mEFAF3BBDC48427B01157B77F87ED6F31A7007E52 (void);
extern void XREnvironmentProbeSubsystemDescriptor_get_supportsRemovalOfAutomatic_m1F8BC49551168C85ECE1489B3ECD80C84745A737 (void);
extern void XREnvironmentProbeSubsystemDescriptor_set_supportsRemovalOfAutomatic_mAF9042500D67C183A50991C5BE60B9B7D1FC88D4 (void);
extern void XREnvironmentProbeSubsystemDescriptor_get_supportsEnvironmentTexture_m410DFF88B4D7C596792083C2EB75CB97E0E6E673 (void);
extern void XREnvironmentProbeSubsystemDescriptor_set_supportsEnvironmentTexture_m23B7A468AFC6959FB83913D7DDE2F85AF2D9EF5D (void);
extern void XREnvironmentProbeSubsystemDescriptor_get_supportsEnvironmentTextureHDR_m61D26031FAC8D6E14EFE637F4E6F86D036069AF8 (void);
extern void XREnvironmentProbeSubsystemDescriptor_set_supportsEnvironmentTextureHDR_m8DAD6E95E4BC1FCFC780273EE59D32B49299A23E (void);
extern void XREnvironmentProbeSubsystemDescriptor_Create_m45D62D5B4B259C964A8F836CD4E33DF492949F1B (void);
extern void XRFace_get_defaultValue_m3C57FCE26ABDB16951CB5F35758D33DEFD545535 (void);
extern void XRFace_get_trackableId_m9FC29FB643FFBAB989AB8179F57CDB52D14737B3 (void);
extern void XRFace_get_pose_m1625DED173751F25873C4BB6650238A195CD04EE (void);
extern void XRFace_get_trackingState_m7D5C3002DCB9FC01F7C1BE66D3F1092281E847FB (void);
extern void XRFace_get_nativePtr_mCE3681420B25EA0AE4B5FA1687310DF7D49C0899 (void);
extern void XRFace_get_leftEyePose_m5406913BE94DA663C80EA8C555EEC1439C0ADAE3 (void);
extern void XRFace_get_rightEyePose_m276AD0EBDCD8B62AAAAA2A33920E2FF1415E769D (void);
extern void XRFace_get_fixationPoint_m2628733EA6C1FEEAC047347DBA08A602B7C88429 (void);
extern void XRFace_Equals_m6E2D8C6F4F57BB604AA31EEEAEB06BB64EBFC299 (void);
extern void XRFace_GetHashCode_mC17A1126F3ADDDB95C12C3E908353704DCCB14D0 (void);
extern void XRFace_op_Equality_m5464082D6B0C53CD94DCD5606EB9CC3DED0CB7BD (void);
extern void XRFace_op_Inequality_m8C052FECD0C719C494CF4BDF4DDA2DE76ACCDCCB (void);
extern void XRFace_Equals_mC82B627F3AA8A164D6AE1A999A5BCB55DD2E2C51 (void);
extern void XRFace__cctor_mB74D8288B8CDC23DC133DCB1D7D248CB5D6135BC (void);
extern void XRFaceMesh_Resize_mD9373FB138642F70F4068345A64B49B64B1A3830 (void);
extern void XRFaceMesh_get_vertices_m8B133063FC373FD34B8ECBEE696B3462DC65277E (void);
extern void XRFaceMesh_get_normals_m37A411662D1051785AFC6807E3BBEC0E2B3BB61B (void);
extern void XRFaceMesh_get_indices_m2658965B1B99DF1CF00154D791B580AE71CB136D (void);
extern void XRFaceMesh_get_uvs_m71BF16345717D8B5D8F41C571A8D3152337E0A28 (void);
extern void XRFaceMesh_Dispose_m02478E536865BA52126039CCAE5B62E5DE58AECF (void);
extern void XRFaceMesh_GetHashCode_mE6F88C5914358332601C00E22FE0A34A137EC982 (void);
extern void XRFaceMesh_Equals_mDE9CF3DB2761831C9E9A72B6C2C3EB1D6D155D6F (void);
extern void XRFaceMesh_ToString_mEA1FF45022C6E287675E27526448295468B2884B (void);
extern void XRFaceMesh_Equals_m56870D4CC9E4BC2D1839D5DEFA77A062C29C97A4 (void);
extern void XRFaceMesh_op_Equality_m6570A0752401DB202626ECD242755553E7E8D830 (void);
extern void XRFaceMesh_op_Inequality_m00FCFC7717BCA976025B49B6CBFF7FD546DBE18A (void);
extern void XRFaceSubsystem__ctor_m31F1B0D8DDB368C87E5691F6C114E926155ED9D5 (void);
extern void XRFaceSubsystem_get_requestedMaximumFaceCount_mC3A28A767AD50117492E460C3BC9D85A6F83FD2E (void);
extern void XRFaceSubsystem_set_requestedMaximumFaceCount_m0A4830582BC9F31DC033223A5BDA8621C3E4A191 (void);
extern void XRFaceSubsystem_get_currentMaximumFaceCount_m3404BDB08E8809A87E742DB785078C1E319009C3 (void);
extern void XRFaceSubsystem_get_supportedFaceCount_mF679BF9CF3EAFC512E8381E433757848DA3C63D4 (void);
extern void XRFaceSubsystem_GetChanges_mE2721F6C40D20AC9D39428E6D9C5A359C9D841A2 (void);
extern void XRFaceSubsystem_GetFaceMesh_mC4137FDF495ED6737AC1B49DAD1BE6D9E6C514AB (void);
extern void Provider_GetFaceMesh_m27AC65B0454F7C6464E634A6DC3BEC57C2530844 (void);
extern void Provider_get_supportedFaceCount_m1F62AB853CC71F49711F106C855467DBFF39DD68 (void);
extern void Provider_get_requestedMaximumFaceCount_mE04DBAD7160414261334D9474D09AF8628A4BDBB (void);
extern void Provider_set_requestedMaximumFaceCount_m333E04847D0544E2DAB8A60BC48A7E663E2169DE (void);
extern void Provider_get_currentMaximumFaceCount_m3BAF54FE1CD288F6D7D18BDA5A4966A9C74DE3AC (void);
extern void Provider__ctor_m41CCFF923C16D17CC1297FE5FF752330D8CD1BBD (void);
extern void FaceSubsystemParams_get_id_m31A6CBE37287374259BEF3B328FB00B1ED871D6A (void);
extern void FaceSubsystemParams_set_id_m963078C97310E91B43D52A4A682D6A4DC0D9A40A (void);
extern void FaceSubsystemParams_get_providerType_mAC8A9B7FF24C751EF95C8B06D71746709FBAED0F (void);
extern void FaceSubsystemParams_set_providerType_m96D22AB9770398D6C371502A95F86A6F45E17C5D (void);
extern void FaceSubsystemParams_get_subsystemTypeOverride_m15AFA4BED668BB995597E91557337E9B30994467 (void);
extern void FaceSubsystemParams_set_subsystemTypeOverride_m113E1A7A0F27FE1A56FF92504E6BDC2FD287846E (void);
extern void FaceSubsystemParams_get_subsystemImplementationType_mB85CD541326A5FBDE4378093FA3735066DBDEFED (void);
extern void FaceSubsystemParams_set_subsystemImplementationType_m3A131EEBF975AA6ADF3A39ABC3ED0980C8D7E024 (void);
extern void FaceSubsystemParams_get_supportsFacePose_m68CEF716DBA9D0D75EDA10A87A64A5827C78CA59 (void);
extern void FaceSubsystemParams_set_supportsFacePose_mCBD1B88CB04F00F212C4B5B8A11821DDCC5C8353 (void);
extern void FaceSubsystemParams_get_supportsFaceMeshVerticesAndIndices_mB1D5078180B153FE76FCA509905D0A79D575689B (void);
extern void FaceSubsystemParams_set_supportsFaceMeshVerticesAndIndices_m8783686211AB86E509EE6538E3CFC9A6CF82BF9D (void);
extern void FaceSubsystemParams_get_supportsFaceMeshUVs_m3734F66B4ADBD0A39FBCAEB927A4409023D6164A (void);
extern void FaceSubsystemParams_set_supportsFaceMeshUVs_m151C1B6BE0BA832416557E90493F04DCF97E6809 (void);
extern void FaceSubsystemParams_get_supportsFaceMeshNormals_m194410916CDF0F71EBF2AD9C419155D68F9969B0 (void);
extern void FaceSubsystemParams_set_supportsFaceMeshNormals_m0664A1AD48A3B41681B9680F7773D1849FA1450A (void);
extern void FaceSubsystemParams_get_supportsEyeTracking_mC254E7AD7AD126742CC7CFDFDB944CD2AC0C0FBD (void);
extern void FaceSubsystemParams_set_supportsEyeTracking_mFEB7DACFE5B6DA3EB37AED2C3428A1CEE0E0DBDE (void);
extern void FaceSubsystemParams_Equals_mD22C227C324E8205B6ACA8F6C625C62F58224D3A (void);
extern void FaceSubsystemParams_Equals_m4AA3EAA9779EDA380AD89EA9BA7E1865B0BE6017 (void);
extern void FaceSubsystemParams_GetHashCode_m4C9D85CB7B820FC7E57A5B214D85EDDBD6D5499D (void);
extern void FaceSubsystemParams_op_Equality_m13414161D1A8AA42F9363CFCA8AB45F5A1C6E0CE (void);
extern void FaceSubsystemParams_op_Inequality_m826226EB56D41F48F3A65AB7E15654497B33B6DA (void);
extern void XRFaceSubsystemDescriptor__ctor_m94769C800013224A0EB1531F51F1E6155853517D (void);
extern void XRFaceSubsystemDescriptor_get_supportsFacePose_mC2C1FCA19B4B87A6A52DC4D56ED8F53668DA1C67 (void);
extern void XRFaceSubsystemDescriptor_get_supportsFaceMeshVerticesAndIndices_mBC51769DDD38F8B2407F919BD7B7ED5A3E1D492A (void);
extern void XRFaceSubsystemDescriptor_get_supportsFaceMeshUVs_m8E05155346A7295AA7D53E143032198A42E1CE61 (void);
extern void XRFaceSubsystemDescriptor_get_supportsFaceMeshNormals_mD4E28B10A6F8C9041AF63A9233314610ED3E6A28 (void);
extern void XRFaceSubsystemDescriptor_get_supportsEyeTracking_mF482F5E345E3A9435DA290AC6DD1D28470602039 (void);
extern void XRFaceSubsystemDescriptor_Create_m0B282B6C4D3AD46F02C2D5AA2900949E4C5D272D (void);
extern void GuidUtil_Compose_m58AA1AA0AF27A23B64937C72023C6F72D4C8DD40 (void);
extern void GuidUtil_Decompose_m71DD3DADA4E88DB6C02C2A143DF01870F08BA910 (void);
extern void HashCodeUtil_Combine_m98169BF154323DEFC91DB146CDD3CE7550B6AD36 (void);
extern void HashCodeUtil_ReferenceHash_m2DB64625F0287C798373FE7D45AA20B43AC3EDA5 (void);
extern void HashCodeUtil_Combine_mF01D6438A25333A5530D4658D11A9F0BC988011A (void);
extern void HashCodeUtil_Combine_m6E8EC4EC047F80C102AEE35681D328C78A3DCE55 (void);
extern void HashCodeUtil_Combine_m0CA248D97B33A8A3DD5AD8456D090619CCD63FFA (void);
extern void HashCodeUtil_Combine_mAD5A58AE27677DC59EC7E9AE41FAF43AF414C506 (void);
extern void HashCodeUtil_Combine_m3B3273BD5CFEFD2D09635E1B69281B0ECD9819FB (void);
extern void HashCodeUtil_Combine_mCB03954826AD9D732D76C80D546E33DF5FC1C7A8 (void);
extern void HashCodeUtil_Combine_mBA82260101D33A951BAD66953407B18C697104B7 (void);
extern void HashCodeUtil_Combine_m0646726224C8EB6504327A535A238220D1DF2454 (void);
extern void HelpURLAttribute__ctor_mA0360803CCDDE8CAD65704493A1CC93513AE5421 (void);
extern void HelpURLAttribute__ctor_mCB9A72AA2EE39BF4C3741AE604073A8F513449CC (void);
extern void XRHumanBody_get_trackableId_m7CC5B8BB5179303ED1424ACDC46FBFA16C30B2FD (void);
extern void XRHumanBody_set_trackableId_mCE04EA8307BC1B6670AE915575E5297103620E87 (void);
extern void XRHumanBody_get_pose_mE154F73E48997BDB6828FE12D7116E93E4D4BBCF (void);
extern void XRHumanBody_set_pose_m036F9C1AB8DA4836D85CF15256C0FF6C83E8B712 (void);
extern void XRHumanBody_get_estimatedHeightScaleFactor_m455E9FD1B289BA71C5FEE2A67D72EEE10727246B (void);
extern void XRHumanBody_set_estimatedHeightScaleFactor_m857381C931D5F597AE28A4BD96E8225DE2250693 (void);
extern void XRHumanBody_get_trackingState_mE53C1B287B5BD8E021FCAC0E4550C0D551C0F79A (void);
extern void XRHumanBody_set_trackingState_m470B4AD877F377DB7B85F736D80EC5FB2AD39187 (void);
extern void XRHumanBody_get_nativePtr_mBD7FABEADEC1EA20A472626430176AC6681C50E2 (void);
extern void XRHumanBody_set_nativePtr_m140ED78793BB10C9126C5539804291CE69F00381 (void);
extern void XRHumanBody_get_defaultValue_mA19E33981C9E5F90F388C4660197FD156FB90037 (void);
extern void XRHumanBody_Equals_mED06668B3B016A173D38A33D8D4CC24691A90CF1 (void);
extern void XRHumanBody_Equals_mC6FA42C2E907195A60B2CB8A6230462762C6B003 (void);
extern void XRHumanBody_op_Equality_mEC866169B1FEABD6F33A862D063F8C7D895017DF (void);
extern void XRHumanBody_op_Inequality_mFCE9FB29ACAD78B15AE6E742DB7780490B4F0B64 (void);
extern void XRHumanBody_GetHashCode_m44E8812541CCF52BB596A789A350C77CF32B8B06 (void);
extern void XRHumanBody__cctor_m1A4FC9E83BBA384D4CA8500953B02717C6952507 (void);
extern void XRHumanBodyJoint_get_index_m3AD361AAD68A37A0EC5490A716FA0F0D5AC6D386 (void);
extern void XRHumanBodyJoint_get_parentIndex_m4DA1B768A618B7AE553D67CC82F6B2545B8F2FBA (void);
extern void XRHumanBodyJoint_get_localScale_m9A7DDC16FAEB5CFF269393403A92C375CE8387B6 (void);
extern void XRHumanBodyJoint_get_localPose_m5330B565E89F7276A497ED8E94DAA288A352FDD2 (void);
extern void XRHumanBodyJoint_get_anchorScale_m01EC3D9B0020D0BFBCDF9ADD26149F6D9E6D87C0 (void);
extern void XRHumanBodyJoint_get_anchorPose_mC409FE9C6F4CFD14C156977B59096FA4340EE61E (void);
extern void XRHumanBodyJoint_get_tracked_mC8DA59028CFA50982FD6E319736F0C93EA097899 (void);
extern void XRHumanBodyJoint__ctor_mF74F3B39077D10EED35A34F3DBF7C217CA1D8753 (void);
extern void XRHumanBodyJoint_Equals_m7DFBAA24024C04E8A38A962862BA744F9A515AE5 (void);
extern void XRHumanBodyJoint_Equals_m59EDC2A704F17057288266550340CCB7FE041680 (void);
extern void XRHumanBodyJoint_op_Equality_m0FD5727DC1875198D103673CDDDB6DE07024178B (void);
extern void XRHumanBodyJoint_op_Inequality_mCE4D62DAF1C3EC1B4C76EEB0A9603FB7C9988E17 (void);
extern void XRHumanBodyJoint_GetHashCode_mC37463DF2B57FF4BA22AD008F91AF061E30575EF (void);
extern void XRHumanBodyJoint_ToString_mE909C8943965A053938EFE3B7DC365673632F899 (void);
extern void XRHumanBodyJoint_ToString_m814AEF251F6D72B22EE7DE358A422C638FF6D089 (void);
extern void XRHumanBodyPose2DJoint_get_index_m63E7D2C639973B20B8721BD412441AB87F32C626 (void);
extern void XRHumanBodyPose2DJoint_get_parentIndex_m74403971AC59748A0FC11187E701F8EF835A97F5 (void);
extern void XRHumanBodyPose2DJoint_get_position_m4A5CE8370D7E1DEB0B2CC27487182A0776AAC8E8 (void);
extern void XRHumanBodyPose2DJoint_get_tracked_mCC6E1D56159DA4501534E47AB27D4EA05AA3FCF8 (void);
extern void XRHumanBodyPose2DJoint__ctor_m2BE9BC97DB9FA4C84623810D624196524B9F488A (void);
extern void XRHumanBodyPose2DJoint_Equals_m7023E676891F764891104A57CD41D77BE31360F4 (void);
extern void XRHumanBodyPose2DJoint_Equals_mE8F361B51A58F789BC559B550AA5CA08691A88E1 (void);
extern void XRHumanBodyPose2DJoint_op_Equality_mD209C91BE915D0CE621211339876F0488A4A994A (void);
extern void XRHumanBodyPose2DJoint_op_Inequality_mD8781E62E15A4ACDE6D17FF63720ADAE7D9E9EC1 (void);
extern void XRHumanBodyPose2DJoint_GetHashCode_m9CA16CABF11BB7137978E5D8EE83FACBF7D1622F (void);
extern void XRHumanBodyPose2DJoint_ToString_m487FA2ED54B8FC493572D75FAFC68BA40B4FEFC4 (void);
extern void XRHumanBodyPose2DJoint_ToString_m7BBC96E86E94C35680E22AB7CED6215407E48CBB (void);
extern void XRHumanBodySubsystem_get_pose2DRequested_m0C32BCD5FFE0A8B0E73F3FCF14A2A753591E210F (void);
extern void XRHumanBodySubsystem_set_pose2DRequested_m460913D673A796B4DD88615ABD5B38F4E27F826F (void);
extern void XRHumanBodySubsystem_get_pose2DEnabled_mD5ABCA377E364969A38D6F79006E66E1C1B66DB2 (void);
extern void XRHumanBodySubsystem_get_pose3DRequested_m3521DFF8C79CEDCCE1D0B065F5B11B3AFA0C8DB8 (void);
extern void XRHumanBodySubsystem_set_pose3DRequested_m3176AB51B24C37E5FB496D9392B40524838063B3 (void);
extern void XRHumanBodySubsystem_get_pose3DEnabled_m1BA00660787FE2183616989A231AFF6E9AF37E90 (void);
extern void XRHumanBodySubsystem_get_pose3DScaleEstimationRequested_mB4BE08E33AB7699B610592422BDB2F8DD8F576E3 (void);
extern void XRHumanBodySubsystem_set_pose3DScaleEstimationRequested_mA9AD2D8A5C6B099CB883D92BC7631CA2F6DE83C4 (void);
extern void XRHumanBodySubsystem_get_pose3DScaleEstimationEnabled_m245F93DDD5D738E8E3550FBCA43A76A2DB8B0F81 (void);
extern void XRHumanBodySubsystem__ctor_mE0AE161D7A1E01F8580E4E8D243FE988A9392BA0 (void);
extern void XRHumanBodySubsystem_GetChanges_m7B7DB503B66CFD7F266DEEA73EB7CB94C1AFE4FE (void);
extern void XRHumanBodySubsystem_GetSkeleton_m65F0C477C539F78BFD68A44C28583DF418A78335 (void);
extern void XRHumanBodySubsystem_GetHumanBodyPose2DJoints_m4547D3DC143DC3CC014F4BEE18632AC09F1108E5 (void);
extern void XRHumanBodySubsystem_Register_m7319B25B82CA84BF8F9578ED96263AF51700A9EF (void);
extern void Provider_get_pose2DRequested_mA429171D14BB5AC1A71E6097F571AC6C91AE3CCF (void);
extern void Provider_set_pose2DRequested_m8C377DC5C76047E8C41634B62F0A5E8E7B5E7C0E (void);
extern void Provider_get_pose2DEnabled_mD4930EED264FBBF745606EADF3DED61BCB93312C (void);
extern void Provider_get_pose3DRequested_m0BC6D27B9E26FCBA148B0C590A81021501F9BEA7 (void);
extern void Provider_set_pose3DRequested_m61BF14CBFEBE3C1A4F02E5DA65129612BFF3D276 (void);
extern void Provider_get_pose3DEnabled_mA6832343C5BF65920FED4255644ACCABD900B4EA (void);
extern void Provider_get_pose3DScaleEstimationRequested_m9CA25E3E91C8C17D5CFC774866A1C0D8DE6852A2 (void);
extern void Provider_set_pose3DScaleEstimationRequested_mB5EB887504541EFBD4E9F502521494A2C9CCB675 (void);
extern void Provider_get_pose3DScaleEstimationEnabled_mC56F62D5ECE673B5F8D0DF5B768D26E7FF7F60C0 (void);
extern void Provider_GetSkeleton_mFAC0DCEF7349747BF264D2484280054B0FF0253C (void);
extern void Provider_GetHumanBodyPose2DJoints_m10725EBA1EB33455CC0798AFA2562453BF1726F7 (void);
extern void Provider__ctor_mCA2547E3C5B7461FAB29AE27A5B0934B9221BBF1 (void);
extern void XRHumanBodySubsystemCinfo_get_id_m2B4E8095B5AE6AF6E64FB7B56DBD795989A862F8 (void);
extern void XRHumanBodySubsystemCinfo_set_id_m58CBD535224B7049AC8AC9D82305E1D8BBF90084 (void);
extern void XRHumanBodySubsystemCinfo_get_providerType_m71F869C8A2B8C6AC2BB7D0C3842549886608BD19 (void);
extern void XRHumanBodySubsystemCinfo_set_providerType_m228A78E8403BF03833D556B3EE0637291DC46615 (void);
extern void XRHumanBodySubsystemCinfo_get_subsystemTypeOverride_m1AD3DA828683BFEC871F5E62C5B552E27F47A009 (void);
extern void XRHumanBodySubsystemCinfo_set_subsystemTypeOverride_m847719A54A861DE6FE06E9DAFA1485E6184D2909 (void);
extern void XRHumanBodySubsystemCinfo_get_implementationType_m3CD85264DFA467891430738097EBB79174BA518C (void);
extern void XRHumanBodySubsystemCinfo_set_implementationType_m8001903267263EED38F960C2E1BD5A53CE64C798 (void);
extern void XRHumanBodySubsystemCinfo_get_supportsHumanBody2D_m93255214CEECB1C656D88ACFFF73D736D81FD8BD (void);
extern void XRHumanBodySubsystemCinfo_set_supportsHumanBody2D_m7E4750E667A695B158E006DDC6F74C56542CBE0E (void);
extern void XRHumanBodySubsystemCinfo_get_supportsHumanBody3D_m133406910A68DE814A0A335A59285098FEE70C19 (void);
extern void XRHumanBodySubsystemCinfo_set_supportsHumanBody3D_m02D0D700619D9CB7D96D027EFE3EC41D47F81F8A (void);
extern void XRHumanBodySubsystemCinfo_get_supportsHumanBody3DScaleEstimation_m81A73CFB3B8232EF098B2CDF96F25687AC19845C (void);
extern void XRHumanBodySubsystemCinfo_set_supportsHumanBody3DScaleEstimation_m5F982C883E1918068DA02BA87C0069BF379E147F (void);
extern void XRHumanBodySubsystemCinfo_Equals_m99F61D9E0CFA86A7171F27A236AD8CD2FD7BF08A (void);
extern void XRHumanBodySubsystemCinfo_Equals_mE22CDDCA260523EF651BD2B23C1F66F11BB95C0A (void);
extern void XRHumanBodySubsystemCinfo_op_Equality_m7E2037783CDD8A42F384186AFA53DE86F99DC1B1 (void);
extern void XRHumanBodySubsystemCinfo_op_Inequality_m3D4731ACACD907F94AD1ADC28A8C68E991D023EB (void);
extern void XRHumanBodySubsystemCinfo_GetHashCode_mF9F242A2F525ED4246171238CE9B5A7FD24C63FE (void);
extern void XRHumanBodySubsystemDescriptor__ctor_mFFA3B29932D6CD50B30E50BAF903818AB6C6ED44 (void);
extern void XRHumanBodySubsystemDescriptor_get_supportsHumanBody2D_m7FD5572388D3E9EF931691F0267B3779B219F9DE (void);
extern void XRHumanBodySubsystemDescriptor_set_supportsHumanBody2D_m426CB15FE9C68540815E13FCCF40128F6BDD6ECF (void);
extern void XRHumanBodySubsystemDescriptor_get_supportsHumanBody3D_mD98F673993E12554F127E2FEF129C75146282BCB (void);
extern void XRHumanBodySubsystemDescriptor_set_supportsHumanBody3D_mB481745B000D50942570BC1A718AF119A729543A (void);
extern void XRHumanBodySubsystemDescriptor_get_supportsHumanBody3DScaleEstimation_mB422A877EBB97EB9FD1F846C52296A332012C24A (void);
extern void XRHumanBodySubsystemDescriptor_set_supportsHumanBody3DScaleEstimation_mD01ACAA2F3F335B5059B7613AC309D1525BAFAAB (void);
extern void XRHumanBodySubsystemDescriptor_Create_m6A81C133B714D6942AEEA5D98E8516657040CF8C (void);
extern void AddReferenceImageJobState__ctor_mC0CCEC53FEB86CE2B9560D06DE28919ADB2440E2 (void);
extern void AddReferenceImageJobState_get_jobHandle_m02E9565D08C8156E799D1B852C14707856E6B12E (void);
extern void AddReferenceImageJobState_AsIntPtr_m8C97E68E09387D512B5A2D921841B3E0FCF44CC0 (void);
extern void AddReferenceImageJobState_op_Explicit_mDA81B41DC7C894ADDEDC4CEE9E1CC0C8DAA0951F (void);
extern void AddReferenceImageJobState_get_status_mDF8FE0C1BC9407AD9EAA821DE78B76599455A25F (void);
extern void AddReferenceImageJobState_ToString_m89383245617B4E89FF1CA2FF897917062CD663A7 (void);
extern void AddReferenceImageJobState_GetHashCode_m6EABAC53399090ADFD2932E561BA0FA12EA63DC0 (void);
extern void AddReferenceImageJobState_Equals_mCFA105DAC305C1B3B34F0C7D0D856F3671356D37 (void);
extern void AddReferenceImageJobState_Equals_mD0EE6BB78CB7601C9E1AC6C297417B6E4AE70502 (void);
extern void AddReferenceImageJobState_op_Equality_m1F3798DA01CACD11DC90B5CA739749EE2240C609 (void);
extern void AddReferenceImageJobState_op_Inequality_m9E0E28070CEE1EF9170AA3D3438915E5C5014F60 (void);
extern void AddReferenceImageJobStatusExtensions_IsPending_mDA71DBDE3C6E5530CCB0B0E92DBD0D3A95CB0509 (void);
extern void AddReferenceImageJobStatusExtensions_IsComplete_m56FFD233E085F1001E21085AB820FB749814AA22 (void);
extern void AddReferenceImageJobStatusExtensions_IsError_m03EA2A197B6FE37D50B07B88BD12E6243D3B0C97 (void);
extern void AddReferenceImageJobStatusExtensions_IsSuccess_m5170D9DA6B4AEDFC80BC1A55F99B7EB0578FA0DD (void);
extern void MutableRuntimeReferenceImageLibrary_CreateAddJobState_m0D3F0C269DC4388E00B628A01982BA04609380C0 (void);
extern void MutableRuntimeReferenceImageLibrary_GetAddReferenceImageJobStatus_m4109C2DCB5D54098102658A5A31393E0D67E588B (void);
extern void MutableRuntimeReferenceImageLibrary_get_supportsValidation_mE0890A57BF3E61D7226931E190A093605EA47E61 (void);
extern void MutableRuntimeReferenceImageLibrary_ScheduleAddImageWithValidationJobImpl_mA1984E4EBDB747C162F5F7F2DC47AD42A3FA2932 (void);
extern void MutableRuntimeReferenceImageLibrary_ScheduleAddImageWithValidationJob_m98B12E578EA2A76A4F37D531DFD4BB6E2875A65C (void);
extern void MutableRuntimeReferenceImageLibrary_ScheduleAddImageJob_m01B3B1C04C75107A6800A43CECD36AD126839D3E (void);
extern void MutableRuntimeReferenceImageLibrary_ValidateAndThrow_mB1ACF908016596526B97FDF497DC9A258D2573BD (void);
extern void MutableRuntimeReferenceImageLibrary_GetSupportedTextureFormatAt_m52CBAAD841E5758ED8038440563347BF2EBD0DB8 (void);
extern void MutableRuntimeReferenceImageLibrary_IsTextureFormatSupported_m8FC142D8D5B1652E8AFDD49BD1EED8A6FE3BB955 (void);
extern void MutableRuntimeReferenceImageLibrary_GetEnumerator_m12161C68A1C63E5F42F3F69E12DE6D8B24C6502B (void);
extern void MutableRuntimeReferenceImageLibrary_GenerateNewGuid_m426233E3031BEA508FB52E4DA78DB1BCCB4D8CA7 (void);
extern void MutableRuntimeReferenceImageLibrary__ctor_m4B24CE8934BC39705050E5C69C2641ADA47561EF (void);
extern void Enumerator__ctor_m25C351F3CA22AFB104CE79D00CFF851C7E247ECE (void);
extern void Enumerator_MoveNext_mF3DBBFA17313E104979A8A4F7CD5F111C352AF67 (void);
extern void Enumerator_get_Current_mCAFA85FE5DFA6D3AF14AE3E0BD39A478B00D5F03 (void);
extern void Enumerator_Dispose_m4CBA500DF0A5E65FA1BECA0D61521C960884E06B (void);
extern void Enumerator_GetHashCode_m687007D953BFC4902A3A0115F0E55CA3EBEB1DFE (void);
extern void Enumerator_Equals_m6831767F67C100E80B4C5BCDC1980E8453298DF9 (void);
extern void Enumerator_Equals_m9D822B4C42050C4A121A8CDC1174F2A5824015BB (void);
extern void Enumerator_op_Equality_m293B7442D4BE33E92E21DEA6EEDB19987BE97298 (void);
extern void Enumerator_op_Inequality_m70013614EC7DE83A9C173EE0EBF37D724BBF8ED6 (void);
extern void RuntimeReferenceImageLibrary_get_Item_m02C9E57C775AEC269FCDAA1F51CDC1142CF50ED7 (void);
extern void RuntimeReferenceImageLibrary__ctor_m1357708CB4C54A390D991C4C103B6F7D89A2E376 (void);
extern void XRImageTrackingSubsystem__ctor_mBE6E7CD58CD9742BEE873E1D3AEC19BC1337EDA3 (void);
extern void XRImageTrackingSubsystem_OnStart_m4FE3881F6EF815EC15B872CE0638E32D523633D0 (void);
extern void XRImageTrackingSubsystem_OnStop_m4EFCBBF37ABFCB43D22F3D8B9B136FB46CD50E88 (void);
extern void XRImageTrackingSubsystem_get_imageLibrary_mE8D15BAA0C750529680799A27597C4B5F61EBBC8 (void);
extern void XRImageTrackingSubsystem_set_imageLibrary_m43AB62E6D41F3AF16AF633843C190D35575EB97B (void);
extern void XRImageTrackingSubsystem_CreateRuntimeLibrary_m1E7DAA2B60E911BFD4F9F27EABAC6FE44C617B9F (void);
extern void XRImageTrackingSubsystem_GetChanges_mFF556E643419EAE8E625953B65DE35DBDDC17A7C (void);
extern void XRImageTrackingSubsystem_get_requestedMaxNumberOfMovingImages_m297CA442EB359393CD73A53ED4BB535376BB8A72 (void);
extern void XRImageTrackingSubsystem_set_requestedMaxNumberOfMovingImages_mE6898D8A250E46CD9CAE48EE63E3624AB7184F1D (void);
extern void XRImageTrackingSubsystem_get_currentMaxNumberOfMovingImages_mF52B4940D32D6802A8B80EB4261B728D50C068EE (void);
extern void Provider_get_requestedMaxNumberOfMovingImages_m67846B1C5D09A22934AEC73B741547234A7D4638 (void);
extern void Provider_set_requestedMaxNumberOfMovingImages_mC40B4B4BB3069626401C77833D10CD59976A0533 (void);
extern void Provider_get_currentMaxNumberOfMovingImages_m0AA32BF1665E9619205B1E0643885CCC8E23EE8A (void);
extern void Provider__ctor_mC1DE32F65A2B47BE70F69F6396F787AF33263138 (void);
extern void XRImageTrackingSubsystemDescriptor_get_supportsMovingImages_m478CCC3CDCB620998AA0A2D7F27A72596BB9504E (void);
extern void XRImageTrackingSubsystemDescriptor_get_requiresPhysicalImageDimensions_mA2CD89187C55BDFB3C1E70F6AC67C5EC0097D54A (void);
extern void XRImageTrackingSubsystemDescriptor_get_supportsMutableLibrary_m613CD1AFBAB9E8D7BF9A9679AC40B80D0F0923E1 (void);
extern void XRImageTrackingSubsystemDescriptor_get_supportsImageValidation_mD917947032A835ECEA655D1AA6F3DE6644C35615 (void);
extern void XRImageTrackingSubsystemDescriptor_Create_m1785D4B66CF15D2AEE0A952A83DE873E27CB89E5 (void);
extern void XRImageTrackingSubsystemDescriptor__ctor_m5B3591C4EC408C76FE8AA5F6F80A17BDA83B7332 (void);
extern void Cinfo_get_id_m575543324B2C5A08D4C786C63370AE71BED07969 (void);
extern void Cinfo_set_id_m26F70E551A2F6B517FB5F5C3E5EE4C129FA7BF42 (void);
extern void Cinfo_get_providerType_mBE30E1C47EDAB073ED2660418CA3E29DA5F0CC9B (void);
extern void Cinfo_set_providerType_m4EEFA53693D860609EE4E84FBBD7411BCF38C7D8 (void);
extern void Cinfo_get_subsystemTypeOverride_m1ECE580A62D07D72403739C3B0A3402066CA2939 (void);
extern void Cinfo_set_subsystemTypeOverride_m92548450D405AAF6961BFAD7DFBA20AC0F258A16 (void);
extern void Cinfo_get_subsystemImplementationType_m6D5DFEBCB48B3C0A9574AF76063A4E4C0BEE4D9C (void);
extern void Cinfo_set_subsystemImplementationType_m38DB94BDE396C4375A2384D92893106166598BD3 (void);
extern void Cinfo_get_supportsMovingImages_mF03413F05E6DA7C176CC49907024D17F3CA8CDD4 (void);
extern void Cinfo_set_supportsMovingImages_m8A9EC55903324606F3A00A1B9E0BA4A7F9FBA636 (void);
extern void Cinfo_get_requiresPhysicalImageDimensions_m75BC1904E82964AAB2D4878CDC01A5DA91BCEF8B (void);
extern void Cinfo_set_requiresPhysicalImageDimensions_mEDA76B05F4AADE1843195F0C011BFAD5A10179CD (void);
extern void Cinfo_get_supportsMutableLibrary_mEED5E973B93B77E2EB889635CF0BFBED0F3AAD65 (void);
extern void Cinfo_set_supportsMutableLibrary_mE18B618EF5F3EC11E1AB460302D88A92091DE9E1 (void);
extern void Cinfo_get_supportsImageValidation_m4A509134A5D26AAEFA77EC28D48802E48D98AF35 (void);
extern void Cinfo_set_supportsImageValidation_mCD49CB40A3C9FDC8340016FC0C6D4CA3AC71A98B (void);
extern void Cinfo_GetHashCode_m17D577D55135627B8C8B252E8694CE37D2DCDC8A (void);
extern void Cinfo_Equals_m1B1870077B045D565BC51101AB7C0F66734249FC (void);
extern void Cinfo_Equals_m6FB5DA5B648EE40F20736E915C5E64C13A4D312D (void);
extern void Cinfo_op_Equality_m9FBBD01BF13BA038217E369440ABB260380D56C4 (void);
extern void Cinfo_op_Inequality_m4956F7FD40A7DA13D192EF708205EDD21E630851 (void);
extern void XRReferenceImage__ctor_mCD536BB9053D7775175E0A8AE51BBF026AB06765 (void);
extern void XRReferenceImage_get_guid_m6BEA9888191B7528B60F98EE03C9DBB2B9B8ADEE (void);
extern void XRReferenceImage_get_textureGuid_m70BB73989E26562E2B37F8C272F14F2D06659615 (void);
extern void XRReferenceImage_get_specifySize_m571D71A02EF695A72121AAA086F8B52323E4E4A5 (void);
extern void XRReferenceImage_get_size_mF44BF21ADEFBB155BFD8043E1067057219EC59F2 (void);
extern void XRReferenceImage_get_width_mB6465C498B58CD9093F2FF5EA55DAC8F0E7580A9 (void);
extern void XRReferenceImage_get_height_m422FF3F85DE70E492B7FBABC02277DF6BD76DCD8 (void);
extern void XRReferenceImage_get_name_mF1BE1E54AD911D48445B7DDEF2E27EA01E1E73BB (void);
extern void XRReferenceImage_get_texture_mEC132411644C747C782F41A32A97C95B306D0891 (void);
extern void XRReferenceImage_ToString_mA4374950A18DB316C790DD07F2485A385CE7F3D3 (void);
extern void XRReferenceImage_GetHashCode_m4A2F5EA86EF5B9CDF39516FABD5E378D779B1BA0 (void);
extern void XRReferenceImage_Equals_m1FACD89998C2C9ED6E65DDEE6C1466AE7CC4537E (void);
extern void XRReferenceImage_Equals_m6EA6760F9A443A324475B1E442AFA83C84F06D08 (void);
extern void XRReferenceImage_op_Equality_m2440893B8883316E543B06AC59E930CB347A623C (void);
extern void XRReferenceImage_op_Inequality_m7196D768B1E195E76B4988E35E649C667459E2FD (void);
extern void XRReferenceImageLibrary_get_count_m4ACB1E3776B461ABA415E4F849367E7117ACEA52 (void);
extern void XRReferenceImageLibrary_get_dataStore_mDB5DF12926E7B9F0B02C43C376C23CA99A371F64 (void);
extern void XRReferenceImageLibrary_GetEnumerator_m65502536EB273B7DA0BF38C6D5E110444FC4C5A8 (void);
extern void XRReferenceImageLibrary_System_Collections_Generic_IEnumerableU3CUnityEngine_XR_ARSubsystems_XRReferenceImageU3E_GetEnumerator_m2F52BF78FDE6328CA88D2FAA24B0774C4A3090FA (void);
extern void XRReferenceImageLibrary_System_Collections_IEnumerable_GetEnumerator_m1894805A8F33668DAC4F1BF4B1E957D561622E4A (void);
extern void XRReferenceImageLibrary_get_Item_mD4F21310DD927098ECA31AD41D783D6A3EC462D1 (void);
extern void XRReferenceImageLibrary_indexOf_m222BC612D54C3F68B2E892B440FB1D8B863DA85C (void);
extern void XRReferenceImageLibrary_get_guid_m9302574FCBD28C3BCB849BD719A4F64040E1AD1F (void);
extern void XRReferenceImageLibrary_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_m15E6D2138688FF724E2D117996EC22519FC66A07 (void);
extern void XRReferenceImageLibrary_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_m6C268E3C10F7576AA809DDE8833E9C9E262E67CB (void);
extern void XRReferenceImageLibrary__ctor_mF1DAFFAB5E205482C40A4980F585E585D958EAD5 (void);
extern void XRTrackedImage__ctor_mC69E3D11AAD1CF838BC8575A0E476C296B4B1F3F (void);
extern void XRTrackedImage_get_defaultValue_m956437B8BAA793667C1008981EC550646ED6E6C8 (void);
extern void XRTrackedImage_get_trackableId_m9EA6E15BEF6777E27B50A4903E0069AC04ED6405 (void);
extern void XRTrackedImage_get_sourceImageId_mAAAA675839747EA6AF8A903E461F0D198CFAFDBF (void);
extern void XRTrackedImage_get_pose_m24132085AC8CCE5762C01ECCC1C264A36E77FD69 (void);
extern void XRTrackedImage_get_size_m57847CD4307A9A560D358981700B8722D8A02438 (void);
extern void XRTrackedImage_get_trackingState_m059B99A670B142384AE772376780095877CA72F9 (void);
extern void XRTrackedImage_get_nativePtr_m2ECEAC93477008FB415D4A388ACAA4A9DB6E1892 (void);
extern void XRTrackedImage_GetHashCode_m3900E50D96F2687C63C8F78C9BEA6E469FAE5E2C (void);
extern void XRTrackedImage_Equals_m95C7E1338C9CD5F37EE9D6452AE5820D2BC87FB7 (void);
extern void XRTrackedImage_Equals_m54F9B4F5CC42200E927B5025274C6E765264C9BF (void);
extern void XRTrackedImage_op_Equality_mD09103E710741129A768B52ED46CC02FD61B19C6 (void);
extern void XRTrackedImage_op_Inequality_m3C80CEF4076826592BA9619B4BB23870CB26ECEF (void);
extern void XRTrackedImage__cctor_mFFD6CBB4C4AA72F753B6C48B94A7441D2072777E (void);
extern void HandheldARInputDevice_get_devicePosition_mEA94A0592CC39ADECED0B21A3B015B0C3F217894 (void);
extern void HandheldARInputDevice_set_devicePosition_mFACF712435D25DBC04E7595C073EB2FFB60938EB (void);
extern void HandheldARInputDevice_get_deviceRotation_m839A043DBB843829F25EE4F120D43C3C82C30892 (void);
extern void HandheldARInputDevice_set_deviceRotation_mFF98E66E587EB92247C73A5EDF34317014BB15F1 (void);
extern void HandheldARInputDevice_FinishSetup_m35BA4889F0CA64587C4146B741D8875BFE46352C (void);
extern void HandheldARInputDevice__ctor_m6CD9A2A4F215809FADC75915093A7DDA5E9C226D (void);
extern void XRObjectTrackingSubsystem__ctor_m027CD01BAA28162FEDC4F05360276DECB849AED8 (void);
extern void XRObjectTrackingSubsystem_OnStart_m6AA3A8E01FA852C5BA2F9AEE3E0F887891B98B77 (void);
extern void XRObjectTrackingSubsystem_get_library_mC003D13AE65E56F10539C28052F951C14AC350F3 (void);
extern void XRObjectTrackingSubsystem_set_library_m30A908CBA0E9977DAB269FCED74DCC34A814455F (void);
extern void XRObjectTrackingSubsystem_OnStop_mC5E0DF3A2D7B96B75B9885FB9F775870E69DBB67 (void);
extern void XRObjectTrackingSubsystem_GetChanges_m631751ED540DD35A674349C7632F17A291026B9E (void);
extern void Provider_set_library_m666803BABC136EA0218540480C235908CA5889F3 (void);
extern void Provider__ctor_mC22FB91A82746D919AC450D89D423A6EA16979F4 (void);
extern void XRObjectTrackingSubsystemDescriptor_get_capabilities_m64189B01B6E0C598E33BF8A0D01B72B22C16E0C0 (void);
extern void XRObjectTrackingSubsystemDescriptor_set_capabilities_m03520AF26BDBA2B74F084EC8042DD765380EB644 (void);
extern void XRObjectTrackingSubsystemDescriptor__ctor_m2177FCC9E1AFC3E173E078744766BFA8FF45BE5B (void);
extern void Capabilities_Equals_m60B932F4020B4C1D938F76F8B143AAD76901C48C (void);
extern void Capabilities_Equals_mC78F86790EF9479F76FC84B4E7F74E2E2C07D249 (void);
extern void Capabilities_GetHashCode_mE4E2BB398DA4790DD8E2D0FCA8477062537CF432 (void);
extern void Capabilities_op_Equality_m7D0232338B3F936539274AC270DE2F44E76B14F8 (void);
extern void Capabilities_op_Inequality_m5B3091610FD835694E696E890736906D5F264710 (void);
extern void XRReferenceObject__ctor_mBAE1DECE98351EEFC3B058C143FECED732A58B71 (void);
extern void XRReferenceObject_AddEntry_m83F55C34FB2B5294EFA94E936676E3A3401E07BA (void);
extern void XRReferenceObject_get_name_m30CA572092D7E0DD1D7028A84BB0F5999A92D8FD (void);
extern void XRReferenceObject_get_guid_m96423410888B4CB9712D1A064CF874B5191A49D1 (void);
extern void XRReferenceObject_FindEntry_mFF9C29CCCDF7BA08B307B787DF83EF8F27B6017C (void);
extern void XRReferenceObject_OnAddToLibrary_mF06627EC55B52C25A701E1E00D4F941C70432E42 (void);
extern void XRReferenceObject_Equals_m464CFD79821FAA617E311B82ADA20968A248F511 (void);
extern void XRReferenceObject_GetHashCode_m8ACBD77E6AAF860411D1A1135C4838667F1FB77D (void);
extern void XRReferenceObject_Equals_m3606543B9EF155334F0E71FFE4A550EF5DA0957C (void);
extern void XRReferenceObject_op_Equality_mB4DB4F0DA8E037BA2DF05FC199FEFB7918764C38 (void);
extern void XRReferenceObject_op_Inequality_mC779363F7EA4DF578712AD7E005665AF522CF756 (void);
extern void XRReferenceObjectEntry_OnAddToLibrary_m70E3885E0F675B4F264FCAD2AD689CD829F45926 (void);
extern void XRReferenceObjectEntry__ctor_mCB4A1F3AFBC34A15AB2CC0A7BB4E5B45000DB16C (void);
extern void XRReferenceObjectLibrary_get_count_m0A1A32B348E4877179DE880F72673C40A9922F30 (void);
extern void XRReferenceObjectLibrary_GetEnumerator_m35A1D24DF9038C2FC3F6487EDA7B7E7D86161765 (void);
extern void XRReferenceObjectLibrary_System_Collections_Generic_IEnumerableU3CUnityEngine_XR_ARSubsystems_XRReferenceObjectU3E_GetEnumerator_m442782D1B9280AA958CCD052A7D34C6CB3856CC4 (void);
extern void XRReferenceObjectLibrary_System_Collections_IEnumerable_GetEnumerator_mD5618BEF4160559F4C9B4839DE55C89DD57E441B (void);
extern void XRReferenceObjectLibrary_get_Item_mF60B78377A1290320912A635694360F08A6579F9 (void);
extern void XRReferenceObjectLibrary_get_guid_m3113594B42592622CB48872DAE9CFF9BC503B2C2 (void);
extern void XRReferenceObjectLibrary_indexOf_m54B8E63E60BA6B3EE1A1D2EBB476FD51ED4FCC35 (void);
extern void XRReferenceObjectLibrary_IndexOf_m93685C344CC5829D3ADA00FB7D588595428E1D9F (void);
extern void XRReferenceObjectLibrary_Add_m30393A868ADFDA60503BFC7F6C9106FD40E5AE52 (void);
extern void XRReferenceObjectLibrary__ctor_mE7411882F35FF8E6FDC74CBB4E3A6F691ADBD4F7 (void);
extern void XRTrackedObject_get_defaultValue_mC50260EEA6810D7C99F6A65A6DD35BD462A26638 (void);
extern void XRTrackedObject_get_trackableId_mE8CA173C4D77E4910C47CD5A3DBEA7570CCA69F8 (void);
extern void XRTrackedObject_get_pose_mC8BBDFCC19D9FAF22FA0484E58C5BB1114C929C7 (void);
extern void XRTrackedObject_get_trackingState_m7D1D9DD436ECB10D02D1413AB660ACBC23D60E89 (void);
extern void XRTrackedObject_get_nativePtr_m804A89CA593F513109FCBC04A4FB2C16505F388C (void);
extern void XRTrackedObject_get_referenceObjectGuid_m5E5FEE2B90403C2F75252A6DF4BD9436A7927FD2 (void);
extern void XRTrackedObject__ctor_m9916083096F5B1A8034C7450D07AA9192CDE7BDC (void);
extern void XRTrackedObject_Equals_m4D9D7A47BBD40F713CA964F4109DFD1AFB225D7A (void);
extern void XRTrackedObject_GetHashCode_m9E4ECD0C61FC65EF59F51AD6930540BDF65D2CE3 (void);
extern void XRTrackedObject_op_Equality_mADC613427816017D84B7AEBEBA5781AC1FF037E5 (void);
extern void XRTrackedObject_op_Inequality_m8AA5134578A1872CBEEEBB681CF789FCD41052B5 (void);
extern void XRTrackedObject_Equals_mEA3B08014486E3DF12E17FDCA8D1FAE067FF8BEA (void);
extern void XRTrackedObject__cctor_m81F130691CFF4A57AEA186D70CA16284D2C2F9B7 (void);
extern void EnvironmentDepthModeExtension_Enabled_m82D050848451FF9CA7F7491FE1243091B4F5C391 (void);
extern void SegmentationDepthModeExtension_Enabled_mE436D9333AC945C4CEF2121001360C19A5FCFA87 (void);
extern void SegmentationStencilModeExtension_Enabled_mBD3C16DBA3951DB0A975B9EC8E4EAED9D182D027 (void);
extern void XROcclusionSubsystem_get_requestedHumanStencilMode_mEB05499AF0F84F62FBF38A21B0CC721290436DE2 (void);
extern void XROcclusionSubsystem_set_requestedHumanStencilMode_m49451B1709E24F803E7E2BEE1EC50AAFA3DE3AC3 (void);
extern void XROcclusionSubsystem_get_currentHumanStencilMode_m128A8322B2160F8087302AC6AEA2123F5D0BD708 (void);
extern void XROcclusionSubsystem_get_requestedHumanDepthMode_m5717AA8084D53E5679252BF409A0813C6DA7117F (void);
extern void XROcclusionSubsystem_set_requestedHumanDepthMode_m5F644A70E20B294E9F5CE17B17715855733ABF37 (void);
extern void XROcclusionSubsystem_get_currentHumanDepthMode_m664A81974FA236426C74B71978EEB718AA6B7F15 (void);
extern void XROcclusionSubsystem_get_requestedEnvironmentDepthMode_mE10379901B371DE2D3101B0C0B70CE3A04703BB6 (void);
extern void XROcclusionSubsystem_set_requestedEnvironmentDepthMode_mF15D23AFD9CE80BC9D49C15C9FD4C7406BFA1140 (void);
extern void XROcclusionSubsystem_get_currentEnvironmentDepthMode_mEB2572194995692EF2953ED4F4F1A2926054D51F (void);
extern void XROcclusionSubsystem_get_environmentDepthTemporalSmoothingRequested_mAB4DEE3A6F2F32E466DE912B98E8638365BBD491 (void);
extern void XROcclusionSubsystem_set_environmentDepthTemporalSmoothingRequested_mB49EE42E0E68422E0BFD07774AF191DF921CC89C (void);
extern void XROcclusionSubsystem_get_environmentDepthTemporalSmoothingEnabled_mDBB09B1740A54904F1E8CB734F42C1277C9187D0 (void);
extern void XROcclusionSubsystem_get_requestedOcclusionPreferenceMode_mD16E1325C05F61D49B3E9E254B34751258AFB48E (void);
extern void XROcclusionSubsystem_set_requestedOcclusionPreferenceMode_m256CEB52B2162D428ACDB8031E5D3FE9E62C3E20 (void);
extern void XROcclusionSubsystem_get_currentOcclusionPreferenceMode_mF26316112165324B11B50A2F40E1CA1B6851200C (void);
extern void XROcclusionSubsystem__ctor_m3D4915FA8E43BA7BD96CB3B9D4E4377E3833C189 (void);
extern void XROcclusionSubsystem_TryGetHumanStencil_mD2FFD71CB444CA2F5C0DD059F78EBC8C084563DF (void);
extern void XROcclusionSubsystem_TryAcquireHumanStencilCpuImage_m8310E58DA83757ED46F9953EE66C50968D022883 (void);
extern void XROcclusionSubsystem_TryGetHumanDepth_mE0C4446D29599251D41C78CBA0E14C9D0D620DF5 (void);
extern void XROcclusionSubsystem_TryAcquireHumanDepthCpuImage_m2775C08F3043DFC6DD780E7361CE0B226848BA88 (void);
extern void XROcclusionSubsystem_TryGetEnvironmentDepth_m401276ABEA03CB060E5220DF61E5DB93AAE539B0 (void);
extern void XROcclusionSubsystem_TryAcquireEnvironmentDepthCpuImage_m2A0E8CF20892BC54DAF118E7BE5158DBED8C7103 (void);
extern void XROcclusionSubsystem_TryAcquireRawEnvironmentDepthCpuImage_m0F7B57B62CFFE1AA86A991AAA36E1AA65EDDCB97 (void);
extern void XROcclusionSubsystem_TryAcquireSmoothedEnvironmentDepthCpuImage_m0E2FBDD491C658A0F35AC4AA8E71946F7B984F89 (void);
extern void XROcclusionSubsystem_TryGetEnvironmentDepthConfidence_m7665C1DB56B8A41659BCCF7F93DBFCC6C42225DC (void);
extern void XROcclusionSubsystem_TryAcquireEnvironmentDepthConfidenceCpuImage_mDEB7DD6583B814A66D6D4ED8F216A6C8461939A1 (void);
extern void XROcclusionSubsystem_GetTextureDescriptors_mD667D3C1F1C019B70E7B1B37131DBCF5A3962FF3 (void);
extern void XROcclusionSubsystem_GetMaterialKeywords_m2139A79542D673AE59B6F4D0F3829027792AB150 (void);
extern void XROcclusionSubsystem_Register_m734F3D1F3186CFCF3F60A14BDA8A2906BB4EC2CA (void);
extern void Provider_get_requestedHumanStencilMode_m02D0E05F66EEB135680476AD4EC02D4B2794EF9E (void);
extern void Provider_set_requestedHumanStencilMode_m2C054B14D026BAA9253619D1008CCC90813129AE (void);
extern void Provider_get_currentHumanStencilMode_m420FA0B1871114E7449879089880D30E0097AF88 (void);
extern void Provider_get_requestedHumanDepthMode_m1A6A24D866AF602986F0CAE91118E94F1BD567C5 (void);
extern void Provider_set_requestedHumanDepthMode_mB20690D0279BBF6E8326EDA78F76B89F617BD7B3 (void);
extern void Provider_get_currentHumanDepthMode_m057D9E76208F1A01AC2E5E3FCCCDEBCC6553B25C (void);
extern void Provider_get_requestedEnvironmentDepthMode_m0652674FBE2080EF267BF1A1E2CF85C601F8DBE0 (void);
extern void Provider_set_requestedEnvironmentDepthMode_m9F873C36A3DE66E23322B9F009FBCC72BC5355D9 (void);
extern void Provider_get_currentEnvironmentDepthMode_m63B399BC6600EACB3BF5E5DF7A2218DAEF9BD71D (void);
extern void Provider_get_environmentDepthTemporalSmoothingRequested_m89320DDAF30F855C21E6A2958C3E54E488D4F7AE (void);
extern void Provider_set_environmentDepthTemporalSmoothingRequested_m67FB296501D21757E9620951F787BB64DAA43248 (void);
extern void Provider_get_environmentDepthTemporalSmoothingEnabled_mF5A5DEB423E20A8D6623C6C5D59A7BAB33B6B105 (void);
extern void Provider_get_requestedOcclusionPreferenceMode_mAF6706610847D5B9E241E88A1465EFDE70A5DC9C (void);
extern void Provider_set_requestedOcclusionPreferenceMode_mC3935B4D950080DAB0EC013E2C5670ED45C5A456 (void);
extern void Provider_get_currentOcclusionPreferenceMode_mB1E452A0FA4BA3B8EF138F43C76219365750C7FF (void);
extern void Provider_TryGetHumanStencil_m6196011447FE5DF70B523186981744EFA71256A5 (void);
extern void Provider_TryAcquireHumanStencilCpuImage_mFC2EDA1E1EF465E4B50AE1947FAC5959182FC99C (void);
extern void Provider_get_humanStencilCpuImageApi_mC79E875A9D2FC1E06E8A6E927BA2C31FD07995D5 (void);
extern void Provider_TryGetHumanDepth_mD898071B7B422E264EEADFE7C99D802B10696AA0 (void);
extern void Provider_TryAcquireHumanDepthCpuImage_m7385F63DBA5724F1A33A58D189B07EE94A9BBA68 (void);
extern void Provider_get_humanDepthCpuImageApi_m880F91E737CF90D36A272FE9DFAE99CF9E5534D4 (void);
extern void Provider_TryGetEnvironmentDepth_mFF3B8C72028987673D5079C935332A6E005C357F (void);
extern void Provider_TryAcquireEnvironmentDepthCpuImage_m35551DC93914ADDCBE8A7F3EE21BF2753DA085BD (void);
extern void Provider_TryAcquireRawEnvironmentDepthCpuImage_m57D3628D7EE8A1D2E81E027637BC5BD3DC4AA606 (void);
extern void Provider_TryAcquireSmoothedEnvironmentDepthCpuImage_m6790C57B075FC05D095DA3FF6202EF41B9A6EE08 (void);
extern void Provider_get_environmentDepthCpuImageApi_m01271E261AB308C5996B4C64C3C6C559C5BE7EAC (void);
extern void Provider_TryGetEnvironmentDepthConfidence_m7F357AB09577179B0ACB3804018D29371CE07037 (void);
extern void Provider_TryAcquireEnvironmentDepthConfidenceCpuImage_m097B6ECA156BFD8D303FCA344DC1D048F60D09E4 (void);
extern void Provider_get_environmentDepthConfidenceCpuImageApi_mF94BCCC60E8F21B0DF7BC901A64339E669FC1995 (void);
extern void Provider_GetTextureDescriptors_mCEB2B41F247F9287871D2FCB36DCD3B1900D9297 (void);
extern void Provider_GetMaterialKeywords_mA35506435187EC4672F50E5BC87F1C30798E48D7 (void);
extern void Provider__ctor_m50964AC0676294C1CDEF2B3C8E05657DE364ED2A (void);
extern void XROcclusionSubsystemCinfo_get_id_mF11E38C57E4AB8E81F9E7875A0A41D04A19C4039 (void);
extern void XROcclusionSubsystemCinfo_set_id_mF8B41D7F5FACF940467D57208BC03DDD89D9B7A8 (void);
extern void XROcclusionSubsystemCinfo_get_providerType_m98D7D72FF4C0B36F28D6E39BC498E43691AE718B (void);
extern void XROcclusionSubsystemCinfo_set_providerType_m5D9D3B330216EAB023F4B17F4853D5A612B07380 (void);
extern void XROcclusionSubsystemCinfo_get_subsystemTypeOverride_mAE0D5036913033AB1D45B54C10047F180648BD3E (void);
extern void XROcclusionSubsystemCinfo_set_subsystemTypeOverride_mA0E976FA9B7955BD224DF93F7AB9AEA883779563 (void);
extern void XROcclusionSubsystemCinfo_get_implementationType_mC1121AB1278E4F86B951FBD0B2EAD85D81A2AB45 (void);
extern void XROcclusionSubsystemCinfo_set_implementationType_m0D5D7F0B926679A2195C01F183280029ADC525BB (void);
extern void XROcclusionSubsystemCinfo_get_supportsHumanSegmentationStencilImage_mB151BF0F40B3C9D9D2DE26318FD219FEF0C2AB9D (void);
extern void XROcclusionSubsystemCinfo_set_supportsHumanSegmentationStencilImage_mE4E442080331134DC255F46E5342D25FF48CB666 (void);
extern void XROcclusionSubsystemCinfo_get_humanSegmentationStencilImageSupportedDelegate_m77677BDADCDA75FD77F97A942FA6B29706500292 (void);
extern void XROcclusionSubsystemCinfo_set_humanSegmentationStencilImageSupportedDelegate_mB0F746AC0CBE2CC986B43CA50873FF91D3D9860F (void);
extern void XROcclusionSubsystemCinfo_get_supportsHumanSegmentationDepthImage_mB73625A00528D80575D712BE5621FF9219E3B6E9 (void);
extern void XROcclusionSubsystemCinfo_set_supportsHumanSegmentationDepthImage_m3D99453F58EEFC98739B052C19B05A4FD5341926 (void);
extern void XROcclusionSubsystemCinfo_get_humanSegmentationDepthImageSupportedDelegate_m67F62406957D42F2EF689DE57FD0074C3DAF2BD5 (void);
extern void XROcclusionSubsystemCinfo_set_humanSegmentationDepthImageSupportedDelegate_m4AB47FCB92617E34DC2C66699B26346112E5C145 (void);
extern void XROcclusionSubsystemCinfo_get_environmentDepthTemporalSmoothingSupportedDelegate_mD84F3F4F9DFCFC8C4CE86F9276EEA5ACA2392D1D (void);
extern void XROcclusionSubsystemCinfo_set_environmentDepthTemporalSmoothingSupportedDelegate_m6B67C82AEA73E79B7CD03F9912746C6B52C27949 (void);
extern void XROcclusionSubsystemCinfo_get_queryForSupportsEnvironmentDepthImage_mA29F2AB5C4AA073556B074CFD5A88D3345D596B3 (void);
extern void XROcclusionSubsystemCinfo_set_queryForSupportsEnvironmentDepthImage_m4BFFF1A117B8452E731F6EC8A4B3CBBFAA0F0B9D (void);
extern void XROcclusionSubsystemCinfo_get_environmentDepthImageSupportedDelegate_m540B191F5215CDD7A1DEC6E370065AE8C9ADE75C (void);
extern void XROcclusionSubsystemCinfo_set_environmentDepthImageSupportedDelegate_m18E4C878D993208EB9EEA9D667CA5C88E4E1D4BE (void);
extern void XROcclusionSubsystemCinfo_get_queryForSupportsEnvironmentDepthConfidenceImage_mA3552C9DF07B609155051A3A80FA30AFDE2D3D1F (void);
extern void XROcclusionSubsystemCinfo_set_queryForSupportsEnvironmentDepthConfidenceImage_m4717AAF7FA480A374DF1357BFDD2D9A256CF5117 (void);
extern void XROcclusionSubsystemCinfo_get_environmentDepthConfidenceImageSupportedDelegate_m562A6E788BE3D0CCD49F0DC2DC698CFAB87DDA6C (void);
extern void XROcclusionSubsystemCinfo_set_environmentDepthConfidenceImageSupportedDelegate_m0F91D5C5B63DEFD91FE9A00EF95B45ED9711461A (void);
extern void XROcclusionSubsystemCinfo_Equals_mEFD9C210D7814A35FFE675EBD2EE91E6A3856623 (void);
extern void XROcclusionSubsystemCinfo_Equals_m232468ACCEB9CD8E20E096E270EEC1B262013812 (void);
extern void XROcclusionSubsystemCinfo_op_Equality_m6C7001873AE986BD36170221DFC13BE143054E6F (void);
extern void XROcclusionSubsystemCinfo_op_Inequality_m7472697C65E462057EBFB8A694021E037634CCA2 (void);
extern void XROcclusionSubsystemCinfo_GetHashCode_m672CF4A97241C59DE78E46E2D41245EACC13B09E (void);
extern void XROcclusionSubsystemDescriptor__ctor_mBBF9386E58133376619E4EF143BDA4826E0B5153 (void);
extern void XROcclusionSubsystemDescriptor_get_supportsHumanSegmentationStencilImage_mE5760E70F08587F5CD918184E394C675DADB106B (void);
extern void XROcclusionSubsystemDescriptor_get_humanSegmentationStencilImageSupported_mFD38298D6EE716F04F941DDC4970DE420C19BEC8 (void);
extern void XROcclusionSubsystemDescriptor_get_supportsHumanSegmentationDepthImage_m912EC7337D203DDFA386290C81F1DFEEF9166B11 (void);
extern void XROcclusionSubsystemDescriptor_get_humanSegmentationDepthImageSupported_m4B58731FE66946A339E57D07B4970286F3FAC61C (void);
extern void XROcclusionSubsystemDescriptor_get_supportsEnvironmentDepthImage_m28E39D10619376C8447AFE6782E7790278E4B6F5 (void);
extern void XROcclusionSubsystemDescriptor_get_environmentDepthImageSupported_mCD6A87F2AE78B41341FABE82E85BB0331CF8433A (void);
extern void XROcclusionSubsystemDescriptor_get_environmentDepthTemporalSmoothingSupported_mFCB0CBE3BAD8C25FEE42FEE7EA9B8BB8A85649F6 (void);
extern void XROcclusionSubsystemDescriptor_get_supportsEnvironmentDepthConfidenceImage_m677676680648DC59997D716033762038B3AEA846 (void);
extern void XROcclusionSubsystemDescriptor_get_environmentDepthConfidenceImageSupported_mEAA745B711055E3D0A07857DA2A5C58A8E301160 (void);
extern void XROcclusionSubsystemDescriptor_Create_m4DFED9463984990C7091D9E6B4502BA2F2DE84B2 (void);
extern void XRParticipant__ctor_mB90C6BDD46B876A4500C55B8CC4C5397AF98B4B6 (void);
extern void XRParticipant_get_defaultParticipant_m809E05F58B936737F69747E3EEB89A0E5D4F7D72 (void);
extern void XRParticipant_get_trackableId_mA33D6F01E0C98B53E73D3AE91E9D27637D3EDCF1 (void);
extern void XRParticipant_get_pose_m68A36D0B3837325D073CCB92A93F600A2D535B7B (void);
extern void XRParticipant_get_trackingState_m65687E606627A087623C9937E49EDE133C5EDD89 (void);
extern void XRParticipant_get_nativePtr_m55B683C801F0EDB9EBBCE21A31B507469838D09A (void);
extern void XRParticipant_get_sessionId_m266D8DE3F178F3EFF1ACBC50578F67E2152006AE (void);
extern void XRParticipant_GetHashCode_mBDFD1ECFEF61A85D9A8BF3857AA0B6220B4A25F1 (void);
extern void XRParticipant_Equals_mED9407D8D4A118CC1E4B2591A3C3884E3FD74708 (void);
extern void XRParticipant_Equals_mA78A4E55D2F314EA48933FB16F3AE557819DCC9E (void);
extern void XRParticipant_op_Equality_m306B50C24D65C1902DC67351F5EFD7D5D0D935C0 (void);
extern void XRParticipant_op_Inequality_mDFD74D9170ED0372EDB83F2E5724FAA5AF761E51 (void);
extern void XRParticipant__cctor_mD3418557AE6CE7D2D556BC3F89132164CB63B66E (void);
extern void XRParticipantSubsystem__ctor_m26C054CC2A79665F15F8A717792E63FF4853DB62 (void);
extern void XRParticipantSubsystem_GetChanges_m9EFFFACBD3016F3C5B27CA9228D4B787B64EEB0E (void);
extern void Provider__ctor_m497F40010C1641F42595ADDE4DFD8F6E5D4D814E (void);
extern void XRParticipantSubsystemDescriptor_get_capabilities_m19ECB9CEC93B1CF942893787981A0E33213E07E1 (void);
extern void XRParticipantSubsystemDescriptor_set_capabilities_m12B7B396F6D2824F6D33E84B2C7C9D46874E6AA7 (void);
extern void XRParticipantSubsystemDescriptor__ctor_m1629BC38262CF3786A0C4A765B0653D3038D4DB3 (void);
extern void BoundedPlane_get_defaultValue_m8231738F569F3ABD6A5A5697B1293C3A75F47D31 (void);
extern void BoundedPlane__ctor_m95C41A6B0DB95A2636683BE716E9F92A0465EF87 (void);
extern void BoundedPlane_get_trackableId_m7AA7FD63EA8F8A903300EFDF15616315ACFDA8AA (void);
extern void BoundedPlane_get_subsumedById_m27EFD2CAFFDCF6560D445115D596F23094F612B9 (void);
extern void BoundedPlane_get_pose_mE6F416B0C7519EDA0D1AE8D8BD4D627E4CEA96CC (void);
extern void BoundedPlane_get_center_m3BB7635D2137C7C414FC682EBE0CB5E1F8D3F7D3 (void);
extern void BoundedPlane_get_extents_m60341CDB176C9039D5B88B2F52534D356E11F400 (void);
extern void BoundedPlane_get_size_m2645C0FF517DF15F381B268DF6366F4D14381DC8 (void);
extern void BoundedPlane_get_alignment_m4E43582A7059AE23DD506878BCF164C61422DBEF (void);
extern void BoundedPlane_get_trackingState_mC294F13F8F79D53F8F04D8FB4E160B092BA6A577 (void);
extern void BoundedPlane_get_nativePtr_mE8E2608856FE4327913A38005F4A8590D65A43E7 (void);
extern void BoundedPlane_get_classification_m4EA9556C440097648A87D3AB7EC433776468A725 (void);
extern void BoundedPlane_get_width_m634AD1BAD468FF96CBFC5786A4CB8A9747737E96 (void);
extern void BoundedPlane_get_height_mB72E46326D1B3DAA5EF67D7FC65D58ECF02FEB5E (void);
extern void BoundedPlane_get_normal_m219E5CB840E4DEE8ECC168F2E5BC3FA6AD5E3DCA (void);
extern void BoundedPlane_get_plane_mC4E55F965A895DDD4EB960BEB612185DE21FD9AC (void);
extern void BoundedPlane_GetCorners_mA9013A95E8FF0830A83791567377903D4D4ED8A8 (void);
extern void BoundedPlane_ToString_mA4FBDD41FC676DB2C2EEB22DA2E624099EF06ADA (void);
extern void BoundedPlane_Equals_mE1A074D048C20E980CB7016FAFA7EDFDA52DB15D (void);
extern void BoundedPlane_GetHashCode_m8C684989A748253B2A3772BCAA87D8758FB98941 (void);
extern void BoundedPlane_op_Equality_mF4E866CA90FDDDF9842CC3C673A858C537E466E8 (void);
extern void BoundedPlane_op_Inequality_mB52419B2984E8267DBC6EB2810637182CC4709EB (void);
extern void BoundedPlane_Equals_m1F738CE040A5D498E41B35521109A3FFBEB7196D (void);
extern void BoundedPlane__cctor_mFBFFC643A005EC73B9D042217D880CD0121D8E91 (void);
extern void PlaneAlignmentExtensions_IsHorizontal_m05AF9996C1C4916E75371E655105DE73DD5A11F1 (void);
extern void PlaneAlignmentExtensions_IsVertical_mEDE1ACA365DBF00018B3C0ADCCDD657DBDE9AA0B (void);
extern void XRPlaneSubsystem__ctor_mDE29D107C740A5CB086A7159DCC17E567A666603 (void);
extern void XRPlaneSubsystem_get_requestedPlaneDetectionMode_m303F2743295F6A35D586107BA463A80D445398F5 (void);
extern void XRPlaneSubsystem_set_requestedPlaneDetectionMode_m35162C34975D7AF454CF0D077029BA9DA32284B8 (void);
extern void XRPlaneSubsystem_get_currentPlaneDetectionMode_m45E78CABE7A117E62B2AA2A3F92D05A6158CE936 (void);
extern void XRPlaneSubsystem_GetChanges_m180B0681AF9853EB236AD62835F92E90F5E1908C (void);
extern void XRPlaneSubsystem_GetBoundary_mF1638B7F45F420D6A56A25D5749A91AF6C46B961 (void);
extern void Provider_GetBoundary_m85479D05365E9665B11E24CADDC45AFDC9A43158 (void);
extern void Provider_get_requestedPlaneDetectionMode_m44A491A20B7336C2783B01835473BF1032570135 (void);
extern void Provider_set_requestedPlaneDetectionMode_mEC2045B55C836A6D80F35FDC31682FB6A2F00E3F (void);
extern void Provider_get_currentPlaneDetectionMode_m72405CAD5E46C4CBD1DF9BABEDE6F7D3CF2F162E (void);
extern void Provider__ctor_m54E6412FCE1B1A4CB7852D23AC643A9698BC4001 (void);
extern void XRPlaneSubsystemDescriptor_get_supportsHorizontalPlaneDetection_m445A5130F29EA2A2AF125632097618241D12AA93 (void);
extern void XRPlaneSubsystemDescriptor_get_supportsVerticalPlaneDetection_m29CDC06AFE8368431E0092990D7148043D13996C (void);
extern void XRPlaneSubsystemDescriptor_get_supportsArbitraryPlaneDetection_m7B057BEE25216B24BC2648D0191D3A3F88A142D8 (void);
extern void XRPlaneSubsystemDescriptor_get_supportsBoundaryVertices_m591A2527B7E06AF0AB1C46197EA9E8C21BF76206 (void);
extern void XRPlaneSubsystemDescriptor_get_supportsClassification_m634BD5268FC0BB4BCE42396067B1C03E48D4F03F (void);
extern void XRPlaneSubsystemDescriptor_Create_m436344F4D6E1681E25A6EB110E13B4B78D63FF59 (void);
extern void XRPlaneSubsystemDescriptor__ctor_m73149431D0358E0258082B14FA2EB05F49CED36B (void);
extern void Cinfo_get_id_m3C9491FE6D19662F5CDE221328F86862621B6DE2 (void);
extern void Cinfo_set_id_mCADBFEB62A645F33A8FE7684CE21D11A837FB6F9 (void);
extern void Cinfo_get_providerType_m43F95297A90490AFA397FC61B990A33F74259EF5 (void);
extern void Cinfo_set_providerType_m75AA9217739457DA075AB764BF440217E87A1126 (void);
extern void Cinfo_get_subsystemTypeOverride_m30B55A83F149F3B304AAAD85FE3E80BFCD75552D (void);
extern void Cinfo_set_subsystemTypeOverride_m8880B201EBF541B726CA629EF2DBA762E5ACB010 (void);
extern void Cinfo_get_subsystemImplementationType_m87A964B3827007A58DEAF8966B51575BA7687D6D (void);
extern void Cinfo_set_subsystemImplementationType_m63ACA332E759D120AB82AA3FE630512846B04E4E (void);
extern void Cinfo_get_supportsHorizontalPlaneDetection_m25246A60EFD930C7AC4C50A950E910E7716C9315 (void);
extern void Cinfo_set_supportsHorizontalPlaneDetection_m73E4DE44A091E9B268214E732EFA29174703EEF1 (void);
extern void Cinfo_get_supportsVerticalPlaneDetection_mEA496CC68069CCAD03DBC1B57F53DDA57D56A8BF (void);
extern void Cinfo_set_supportsVerticalPlaneDetection_m989F6D7C88D39981D1DD342DEC887E9DB3E44AF3 (void);
extern void Cinfo_get_supportsArbitraryPlaneDetection_m7E19D041E3828651646769D594B9647149B5A0F4 (void);
extern void Cinfo_set_supportsArbitraryPlaneDetection_m423FEFB76FB81C496A35BB358EF04592C8EE9C10 (void);
extern void Cinfo_get_supportsBoundaryVertices_m75615CA66C3E0020B75915F8426FE6B2B475BEDD (void);
extern void Cinfo_set_supportsBoundaryVertices_m96752ABD368822EE7EE393F7AE3AAE631A4C3657 (void);
extern void Cinfo_get_supportsClassification_m2596CCB90308DA90A90177C91854DDFBF18F464A (void);
extern void Cinfo_set_supportsClassification_mB1E8AAC1F2A7D511C960C6606364C87EEA1A221D (void);
extern void Cinfo_Equals_m8A992E8675D4C2A5FCF7FCD7714CD1DBD734FEC0 (void);
extern void Cinfo_Equals_m2B155451B272C1E8954EDA6D6DFD1C151408D393 (void);
extern void Cinfo_GetHashCode_mC8813973E6CB5AB8D267B6D76693B6F96C006BF9 (void);
extern void Cinfo_op_Equality_m2035A48D9DF9261BE14129744779020E33E25051 (void);
extern void Cinfo_op_Inequality_mA601145904E7851A476968928F6B25607E95842A (void);
extern void XRDepthSubsystem_GetChanges_m81D786135FAEEC4A43074CFB86E9BA58EA920E87 (void);
extern void XRDepthSubsystem_GetPointCloudData_mE49B5DA3833CD9B6E4460AA3AF73E5C5A3415A4F (void);
extern void XRDepthSubsystem__ctor_m781CA41BFE991D20A8F23A03E05CACBDB2E451D0 (void);
extern void Provider_Start_m9A61AAD67B739507B482B56606675A8826F7F6DD (void);
extern void Provider_Stop_m13FFAEDC52F1EA5792B5BCE1ACF767852A1C2C57 (void);
extern void Provider_Destroy_mF86E96DC1B73E57A3F2DF68207CF33472DABA831 (void);
extern void Provider__ctor_m8ADEDD82C49DE76D1FD3EABAAA6B536D2AC09863 (void);
extern void XRDepthSubsystemDescriptor__ctor_m4C00884FC883BD3C3985937F0186BA730D41733F (void);
extern void XRDepthSubsystemDescriptor_get_supportsFeaturePoints_m06674E4C86120E43DBCAE5CB2A6F3D46FF88F0C9 (void);
extern void XRDepthSubsystemDescriptor_set_supportsFeaturePoints_mD4237C54980FE39C9CBCF4BCA044119B16602A98 (void);
extern void XRDepthSubsystemDescriptor_get_supportsUniqueIds_m1730C29B6B06CE71AFD0CCE23FA4A0E099B8773F (void);
extern void XRDepthSubsystemDescriptor_set_supportsUniqueIds_mBF587447AADB9D724EEA4CA1BEECF0215356CF81 (void);
extern void XRDepthSubsystemDescriptor_get_supportsConfidence_m03816F0629E7D1A948EAB7DCF52B6B52E656EE9D (void);
extern void XRDepthSubsystemDescriptor_set_supportsConfidence_m38F6838582304E5A8BED0FCC7A6C8FCAEE607595 (void);
extern void XRDepthSubsystemDescriptor_RegisterDescriptor_m16624F379AFCDFBF8AC973453AD716B5994F51C2 (void);
extern void Cinfo_get_providerType_m9A765D29AAB5D3BB7C85A54C8B5BD5ACF0A34834 (void);
extern void Cinfo_set_providerType_mF68F31F518ED15570B01758D651590F2A79ADA50 (void);
extern void Cinfo_get_subsystemTypeOverride_m9E130C961C57B8F9DEE5CB775E5BAC1E82FE9ACC (void);
extern void Cinfo_set_subsystemTypeOverride_mF477DEE9A66BA9E979AE210507B158E20DB7F897 (void);
extern void Cinfo_get_supportsFeaturePoints_mDA5E397852EFECCFB9D72BEF50D8149389AF32A0 (void);
extern void Cinfo_set_supportsFeaturePoints_m9AB5B37930ED13405E788D882B2ED496A8A003A4 (void);
extern void Cinfo_get_supportsConfidence_mD64AB8201EF5FED46B5961D3E20A501074F442BA (void);
extern void Cinfo_set_supportsConfidence_m8455406D7A9D0EA2B0600FA6D952CF4E7B169AA2 (void);
extern void Cinfo_get_supportsUniqueIds_m0CD91E9193EA5454EBE851570155F301B1B4E499 (void);
extern void Cinfo_set_supportsUniqueIds_m0C526CF6938A8220903C47BD11AC9F6A1B1E86F6 (void);
extern void Cinfo_get_capabilities_m75D2555477E50E9EE792D385FB7E178EF121362C (void);
extern void Cinfo_set_capabilities_m057D002DC3F65F4A194653D7724131E7DE20D852 (void);
extern void Cinfo_Equals_m45095412211AFF025FFA31098667F7CF82BF2999 (void);
extern void Cinfo_Equals_mDA07D4DDCD91815E6748EC1C73EAB4CA9C911921 (void);
extern void Cinfo_GetHashCode_mD5232DFA01B322FD842CE7B2440D51800770C3A9 (void);
extern void Cinfo_op_Equality_m75ABE2EF81D2B69BD93B140971EF4400CF820101 (void);
extern void Cinfo_op_Inequality_mE917E35619B50FF269A7D869332169B59033739C (void);
extern void XRPointCloud_get_defaultValue_m5610F421A5FD68490D3355BFE988423D0E816943 (void);
extern void XRPointCloud__ctor_m7689BEC3D1FFA90CE83BD510C993768CCA777688 (void);
extern void XRPointCloud_get_trackableId_m3AFB6026E205E26C8B7A3209696F566FB686144D (void);
extern void XRPointCloud_get_pose_m0A8AC4386A388238F4910916CDD3D1B936DB8A51 (void);
extern void XRPointCloud_get_trackingState_m2CD370D0D6A2A920AC1637D94BDD3BCC5DB8945D (void);
extern void XRPointCloud_get_nativePtr_m606D5A1327EAF4D7A23811FE9D90BB8A027D5B11 (void);
extern void XRPointCloud_GetHashCode_m96702297D45AEC469D7D3106FBE47F36B50F9688 (void);
extern void XRPointCloud_Equals_m401E9050FB531805238DF8BCBB06491E23A763B0 (void);
extern void XRPointCloud_Equals_m1A2A56D489C6CFB2DA66238FA843A972E5B2341D (void);
extern void XRPointCloud_op_Equality_mDFC0F2DA174092EB34DF0B6D80DDCBDE2DB9F07D (void);
extern void XRPointCloud_op_Inequality_m01FF6576FC299EE71FBCAEBA76638A99469F1E13 (void);
extern void XRPointCloud__cctor_m54A8BB40B68A4C48D30FF9E7F25EBC9D1424C76C (void);
extern void XRPointCloudData_get_positions_m6B1843590E0A5A94DBA711BF1FBA3A64E39A00A5 (void);
extern void XRPointCloudData_set_positions_mDE6F539B73AEA3C49189F4210F9D01094A02F14D (void);
extern void XRPointCloudData_get_confidenceValues_m4553186D87BC21D13B0B5AC3542BDFE6CFDA15FA (void);
extern void XRPointCloudData_set_confidenceValues_m2541483932A4753B91B3038EF869340A1B949355 (void);
extern void XRPointCloudData_get_identifiers_m86966DF55A38D54A4284AE08D1EBE95F95F80203 (void);
extern void XRPointCloudData_set_identifiers_mA6E3D5E038C89FE4187BE6F57CA92822F0A0CB9B (void);
extern void XRPointCloudData_Dispose_m761F04E465F85CB79EC6BDFFBAB3A348CCB02F75 (void);
extern void XRPointCloudData_GetHashCode_m1787DE4A09656A3F814952590544879379129FB2 (void);
extern void XRPointCloudData_Equals_mA86AEB4AE413BE16DB87461174F31B9ECE87EDED (void);
extern void XRPointCloudData_ToString_mE72394A861C3A87F2DA161BAE348FF5E5A0E8052 (void);
extern void XRPointCloudData_Equals_mE40F5EEEE84C1953A50F3E08BE1C17D3A965B970 (void);
extern void XRPointCloudData_op_Equality_m85B4FDE8642BACAC997A229E3BD4B2B9FBA336DF (void);
extern void XRPointCloudData_op_Inequality_m0E69D9AA98D46A4F6EE90F489B66A4368F5D805C (void);
extern void XRPointCloudSubsystem_GetChanges_m5F38A4B92BA7227A6C18AFE5AC44915847666096 (void);
extern void XRPointCloudSubsystem_GetPointCloudData_mCCC6565794A8749B2F35390E05D91C80981ED75A (void);
extern void XRPointCloudSubsystem__ctor_m4F9FD9C780D1FC9E1E32F77B0CC4CA64785F7F80 (void);
extern void Provider_Start_mB5DD7354C6FA33D63959274BA1AA66513686B0B2 (void);
extern void Provider_Stop_m6D87F9001DFFFA0B7668D291A7C24543F0CF454D (void);
extern void Provider_Destroy_m1D726AD579499CE85671C2716F0015BBEB2C47C0 (void);
extern void Provider__ctor_mEFF8084B718E4A6BAB9D089DF3E4FBE7F1100379 (void);
extern void XRPointCloudSubsystemDescriptor__ctor_mD877FAE2757F9913E54D45FF145380FDB87341E8 (void);
extern void XRPointCloudSubsystemDescriptor_get_supportsFeaturePoints_mEE4BCF13E8E2F044496C843E7DA5861A79ED0D5A (void);
extern void XRPointCloudSubsystemDescriptor_set_supportsFeaturePoints_m09AFC1EA8C3210687BC4DDCC2036A239E1390A30 (void);
extern void XRPointCloudSubsystemDescriptor_get_supportsUniqueIds_m1A0E1BF0868020AA85400BF74FED3B49746B2B11 (void);
extern void XRPointCloudSubsystemDescriptor_set_supportsUniqueIds_m3A103C2FE5DAECF6CA633A20A41BF639E7F84AAE (void);
extern void XRPointCloudSubsystemDescriptor_get_supportsConfidence_m6979C820823ABFC942BB60C8B2D48FFCF7F8ABF6 (void);
extern void XRPointCloudSubsystemDescriptor_set_supportsConfidence_m11B3ED216AA69344F83EC87577F51D76053490DE (void);
extern void XRPointCloudSubsystemDescriptor_RegisterDescriptor_m0AA697D49E654962617609C4BBA9FFA8122E708F (void);
extern void Cinfo_get_providerType_mDDD1F34666705A5BB5B8ED6BD6A76D3449F35323 (void);
extern void Cinfo_set_providerType_mC05B07E51AB9C1D9876C0B38F185CE8A8CAB3ACF (void);
extern void Cinfo_get_subsystemTypeOverride_m67FF1505BC8E11A5A8B8AD478D581703ACC11AFB (void);
extern void Cinfo_set_subsystemTypeOverride_m242BF043654209565488CEE2A0ACB93CE23E9C72 (void);
extern void Cinfo_get_supportsFeaturePoints_m21644904ECC661B186FACE0FD45BA53FCBE67C29 (void);
extern void Cinfo_set_supportsFeaturePoints_m9B0684F80A0FFF829E28C477E0E49AF60C1DDC15 (void);
extern void Cinfo_get_supportsConfidence_m24690BFED822D8034981F9ABF2C02361C071798D (void);
extern void Cinfo_set_supportsConfidence_m7EE730D229E3187CE6DC3E9FB2E31FA32D1A29D8 (void);
extern void Cinfo_get_supportsUniqueIds_m955E4A28D276A9459A9496C92C242F6BD429E236 (void);
extern void Cinfo_set_supportsUniqueIds_mA096C2070124D20D28C7009BA35B23918DC974A0 (void);
extern void Cinfo_get_capabilities_mE55D2E7B6B53FB7350832A08A1D626E2E3855090 (void);
extern void Cinfo_set_capabilities_m975B6EDB8EB063F712A26448245859759A35D7CE (void);
extern void Cinfo_Equals_mB548ABCC0365E9FF78A36324ED2F1128B3836FF7 (void);
extern void Cinfo_Equals_m0305229B15DB2B5DF7EEEB4C8D28883DC35A9588 (void);
extern void Cinfo_GetHashCode_m4D75518C4E9BED4C7BF4508CAD92A03D5D1A77A7 (void);
extern void Cinfo_op_Equality_m64C002CA5C4FA2ECD7A4AC24C76A5F80DB357636 (void);
extern void Cinfo_op_Inequality_mDF48CEB51F3076BA8EB386B31D50800B54F3BC04 (void);
extern void XRRaycast_get_defaultValue_m37D4F9D561AA6422EEB957A49FDC8B9E1669A48E (void);
extern void XRRaycast_get_trackableId_mA844E950A9862ABA13C47395893C18A55C9117AB (void);
extern void XRRaycast_get_pose_mADE80A4AABEFCCCB8297186D7E836EAE5B730F73 (void);
extern void XRRaycast_get_trackingState_mDBA1DEB482B9346E44263E8B2201C1D8AF919B09 (void);
extern void XRRaycast_get_nativePtr_m79D980249D35343744B394D7238F8A8FB943D484 (void);
extern void XRRaycast_get_distance_m0B11F8743558DCA40C4E724ECAB8E4DD5ECFFD2B (void);
extern void XRRaycast_get_hitTrackableId_m54245AC20302081DF8658019AA0261DEE1E7D6FE (void);
extern void XRRaycast__ctor_m0ACF53702D817AC34FD8C21F2C01EF7A8F592F9D (void);
extern void XRRaycast_GetHashCode_m94E4A6BDC4CD5E875F40777E273D1E9CD37D54A6 (void);
extern void XRRaycast_Equals_m7F141CB415FF28341035CBD2B32037DC80469575 (void);
extern void XRRaycast_Equals_m2A00EBA5AD411F5BFF724BB7D60175FAE69F8D74 (void);
extern void XRRaycast_op_Equality_mDE1E41B9C70FE2800AB9FDF02F2BCEDAA4F02729 (void);
extern void XRRaycast_op_Inequality_mB5CAFFF098AE2CC1F9B37CFC3A3150E3990F3A0B (void);
extern void XRRaycast__cctor_m88C247D8041D39ED011C71BD40B6F42310EE48DB (void);
extern void XRRaycastHit_get_defaultValue_m330E18C6389B68E95AB5CFF26531D5CF5DE6E090 (void);
extern void XRRaycastHit_get_trackableId_m8B92C0F8977D274743D9388DEB7DCEBCC88E7325 (void);
extern void XRRaycastHit_set_trackableId_mA41CAE66DB4E6054512F496DABE4C15B6217FA30 (void);
extern void XRRaycastHit_get_pose_m3B8D69B763A39178CB583948B4E08255FE9A633E (void);
extern void XRRaycastHit_set_pose_m26D8C795FDFF7DEE86AB77BC5F0A0B6405150AD4 (void);
extern void XRRaycastHit_get_distance_m7098B7C90D22697CA37FBBDF50A4109AD055CA80 (void);
extern void XRRaycastHit_set_distance_m93182B0265D3D34E9D1730860A5B39F515EA729D (void);
extern void XRRaycastHit_get_hitType_m30A8013E847E6B2B70A9511B522099C03102E933 (void);
extern void XRRaycastHit_set_hitType_m89FAB9AF35A52F7CA3F997AE1494EB92B60CA997 (void);
extern void XRRaycastHit__ctor_mEFB9D7632D78C282C02A913F1E4A2F7866C6B641 (void);
extern void XRRaycastHit_GetHashCode_m7C9DBAE43B929D3D4BBFF37E15E4E01143BC4A6B (void);
extern void XRRaycastHit_Equals_m319801A0EFB8A841B3B7E6197BB612780698759A (void);
extern void XRRaycastHit_Equals_mE45E36906807C4F3C5E28C1F54228142D444DA0A (void);
extern void XRRaycastHit_op_Equality_m25E9118AC9D0C585769B0A01BB4A0BA09E8355DC (void);
extern void XRRaycastHit_op_Inequality_mF0338A41AC3A74E7A0D417552EDD60E87287DA31 (void);
extern void XRRaycastHit__cctor_m5484BC89F33E0E0E1C846CB135139037484009D8 (void);
extern void XRRaycastSubsystem__ctor_m883CA69818A86683F768E24C30896A03AB231744 (void);
extern void XRRaycastSubsystem_GetChanges_mAAF6A88E1636E9143D46216A137FC40065756B36 (void);
extern void XRRaycastSubsystem_TryAddRaycast_mD0D3CCD94C1EAF43E5DA8130DD35CB2B95785DEF (void);
extern void XRRaycastSubsystem_TryAddRaycast_m8B4904E8E52037689CC8A28E952BC532DAC13B8B (void);
extern void XRRaycastSubsystem_RemoveRaycast_mD95EEE6441B70215E92C6FE47B8B586F61F5E331 (void);
extern void XRRaycastSubsystem_Raycast_m8F80A2A2DB7028A18192426BC8A12C1AEE8BAE12 (void);
extern void XRRaycastSubsystem_Raycast_mA1E217F2C1B58EDE213F651AD6F966E77D67F688 (void);
extern void Provider_Start_m1857D20E067E44CF3BAD3E96A368619BAB6474FA (void);
extern void Provider_Stop_m1B4BADF1B98C07455D3BEEEB7A22FD8444904952 (void);
extern void Provider_Destroy_mB9445EA71155D4A8A69DA2E3A929FDAF5C4BB3B3 (void);
extern void Provider_TryAddRaycast_mF21620620C50F046E545FBC7BD33B411C466D0A5 (void);
extern void Provider_TryAddRaycast_m42D31584099F01A3FC95D884A08C23BAA7515143 (void);
extern void Provider_RemoveRaycast_m255F5FAC2D9A0E406FDE819156EBC7C11B23DF94 (void);
extern void Provider_GetChanges_m62ED7DA58CBC28C0C63EDFF72C9C6B7421A3B2B5 (void);
extern void Provider_Raycast_mB206D5960A72ECCC6BC44AA91719306F790C4854 (void);
extern void Provider_Raycast_mDB7696CAE66690693DA105DDC40B319A327807E6 (void);
extern void Provider__ctor_mA6631023676667A3E88702E327B8CCC05F58F678 (void);
extern void XRRaycastSubsystemDescriptor_get_supportsViewportBasedRaycast_m9B041E253DF77C3584BC482D253746AB8A0B4FDB (void);
extern void XRRaycastSubsystemDescriptor_set_supportsViewportBasedRaycast_m1860360556D5F4BBD3B242C93D8C92B3F8FA8D90 (void);
extern void XRRaycastSubsystemDescriptor_get_supportsWorldBasedRaycast_m06932061F9A99D5B2EDF87E420811B985EBD0F82 (void);
extern void XRRaycastSubsystemDescriptor_set_supportsWorldBasedRaycast_m2A57C6AF5B8A21E2780A2CDC288F2917DB4BC590 (void);
extern void XRRaycastSubsystemDescriptor_get_supportedTrackableTypes_mBE54DE9E3A97E78E7EDA4351C259382694864549 (void);
extern void XRRaycastSubsystemDescriptor_set_supportedTrackableTypes_m99EBD94098B7BD499391301B0099EC9381D32558 (void);
extern void XRRaycastSubsystemDescriptor_get_supportsTrackedRaycasts_mF4C11D10940AC7D4625AF00094029C4288997BB7 (void);
extern void XRRaycastSubsystemDescriptor_set_supportsTrackedRaycasts_m9C99240C24C64818B63A368C025689018E8C4044 (void);
extern void XRRaycastSubsystemDescriptor_RegisterDescriptor_m5F967AE129CABAC92A8774272B77B97D5D52F5E9 (void);
extern void XRRaycastSubsystemDescriptor__ctor_m95E810A79E27BB9FE0CC927BCBDEE2BFC5A0B011 (void);
extern void Cinfo_get_id_m14E2737CF1E90C961F8D5B282C17E125018668E0 (void);
extern void Cinfo_set_id_m962E07A26F49D8C32DAEFEC4F9E0F79EBC128533 (void);
extern void Cinfo_get_providerType_mB4A72CE35BDA5CCC57B2CCC3E1D88672D9E59021 (void);
extern void Cinfo_set_providerType_m5D183591B4A3ECA1EC2AA9015C02DCD1EB1F076F (void);
extern void Cinfo_get_subsystemTypeOverride_m473983CC8B06F42BC47E92AA261EC2CCBEBFC292 (void);
extern void Cinfo_set_subsystemTypeOverride_m20C9DAD8677611ACD22689DA249911EEDE46EBF7 (void);
extern void Cinfo_get_subsystemImplementationType_mF1AF41E2F764A5BF0AF6A35C54C27DE25809AED9 (void);
extern void Cinfo_set_subsystemImplementationType_m4A20D7E4790C43392F4F82D6B9FAC1D2759F744C (void);
extern void Cinfo_get_supportsViewportBasedRaycast_mBAE62868186C81B758E7B9B87F3C67F6C1586700 (void);
extern void Cinfo_set_supportsViewportBasedRaycast_mC7DB604D2E288A145177EC66F8C1DAA50F38693F (void);
extern void Cinfo_get_supportsWorldBasedRaycast_m6D9743F420975015239E33D6641CA95A092D501D (void);
extern void Cinfo_set_supportsWorldBasedRaycast_m05D5BC11896AED796A6E237BBB98FA4EF3CF17A4 (void);
extern void Cinfo_get_supportedTrackableTypes_mA59CFA06B5968E2DF45075C6C40E0A776CC19488 (void);
extern void Cinfo_set_supportedTrackableTypes_m319BC9C9EB554C35CD40B791917A603D63BEEE0A (void);
extern void Cinfo_get_supportsTrackedRaycasts_mF9B448767ADE954E8357D686935ACCF3208DB1B8 (void);
extern void Cinfo_set_supportsTrackedRaycasts_m7E65DAEB9ED0CB4C4C83DB751C1547145949AA41 (void);
extern void Cinfo_GetHashCode_m6CC66C9C4BA7904DF1E9E9D7A3C74DC8D6A6C1BA (void);
extern void Cinfo_Equals_m4D9FFB9CFA4DF03E9AF2763D3E91926896FCD64D (void);
extern void Cinfo_ToString_m3022468C4555B097321DCDB08B4079DA430EBB3A (void);
extern void Cinfo_Equals_mD2E403B4E791DCE658297F8CA484149EB2D5F5B8 (void);
extern void Cinfo_op_Equality_m000A059C7529B861CDD3CEA698B9E25147554158 (void);
extern void Cinfo_op_Inequality_m558E90C0C00847084F18453CE73079ED4B2EF7C2 (void);
extern void ScopedProfiler__ctor_m652B5689DE1A3C3EF7D12801DA27FA3B40E4412F (void);
extern void ScopedProfiler__ctor_m1F4C2F43E028839CDD9B09EB51402C6F706431B5 (void);
extern void ScopedProfiler_Dispose_m7B646405B4E52CC4677329D3B860BE9C17A9DAC4 (void);
extern void SerializableGuid__ctor_m0F2435157FEC8427E91A7D0DD39960BADE7209F0 (void);
extern void SerializableGuid_get_empty_m4E3F843DBDDCC5A4B19A19FBDF2F9B53EEAA0073 (void);
extern void SerializableGuid_get_guid_mC9C573E5730B2B18F6DFA80F0BCFD1A097C362B3 (void);
extern void SerializableGuid_GetHashCode_mC33B7B6D908B3A62767C19B331620784F1998D07 (void);
extern void SerializableGuid_Equals_mEB4A1B39DD600CB499AC43BF60A3BD206A1EFD71 (void);
extern void SerializableGuid_ToString_m4FB29C69FF91DC2020A96C3C83FE1B60F9C73047 (void);
extern void SerializableGuid_ToString_m66A8E16F22314214DECE08D94A189101A421603E (void);
extern void SerializableGuid_ToString_m514BCF03CE14CE663D9ECC9616DD28453334BE96 (void);
extern void SerializableGuid_Equals_m7096244EB28310B3CB17CD79EE7068768C6AB4F7 (void);
extern void SerializableGuid_op_Equality_mC3A84DAC77870811207A9D06CF5DF9C145EF400F (void);
extern void SerializableGuid_op_Inequality_m563816F2887AEBAB0CD1F8DDFD08282B1DB4B3AF (void);
extern void SerializableGuid__cctor_m99C1CBC863F8F315793500688464404564D5722B (void);
extern void SessionAvailabilityExtensions_IsSupported_m9105265F71A68B18269095FFE362CD24148F6E7C (void);
extern void SessionAvailabilityExtensions_IsInstalled_m5AA4AE58BC0BA7C30E77B1B06038BED076ABFFDB (void);
extern void XRSessionSubsystem_get_nativePtr_m412275A9382FB5E0105A798037F322FF92DBB46E (void);
extern void XRSessionSubsystem_get_sessionId_m7A0FCE3B70A2E1F9C1732E0285EDFECFB8C30685 (void);
extern void XRSessionSubsystem_GetAvailabilityAsync_m30808B871A7C1BC1BAC7EE85B0F36DB4422769BF (void);
extern void XRSessionSubsystem_InstallAsync_m7E278495740EB305D92109D0FFE0895B774D3DA7 (void);
extern void XRSessionSubsystem__ctor_mB926772E21C65B0CBCE36FD4610CEC1B677999C8 (void);
extern void XRSessionSubsystem_Reset_m7413F6A15AA7A69B40F473129325FC71593ABAA6 (void);
extern void XRSessionSubsystem_DetermineConfiguration_m0A5C3F6A14CB8F2D74D5162CCCD5FBD3D51F58A5 (void);
extern void XRSessionSubsystem_Update_m25DA803713B0800B1C39326D71817305C6A38749 (void);
extern void XRSessionSubsystem_get_currentConfiguration_m1AB222F75B6C1645DB3405CB9AE18740EE66F0E5 (void);
extern void XRSessionSubsystem_set_currentConfiguration_m4299FA74038DD6293A125B425179E4C948D09917 (void);
extern void XRSessionSubsystem_get_requestedFeatures_m2193DB34D2CC44D20701580C9FA062EA111CFCE3 (void);
extern void XRSessionSubsystem_GetConfigurationDescriptors_m437FE62BDE0C6F37AC5A75C1A11674AB403147F2 (void);
extern void XRSessionSubsystem_OnApplicationPause_mE4BBAAC4555EA3AAF9B8113406DC920054A1307D (void);
extern void XRSessionSubsystem_OnApplicationResume_mCECA16418E8ECAFFCAEDE50D80364B4709A0D488 (void);
extern void XRSessionSubsystem_get_trackingState_mC2FE654BEB0240C5C3FF85E31DA35E52F85DB550 (void);
extern void XRSessionSubsystem_get_requestedTrackingMode_m9C8B343227EE42F5ED9980A26008BBEFA101D49F (void);
extern void XRSessionSubsystem_set_requestedTrackingMode_m8BBE750492C85A5D35063E246E1F090D5DF73026 (void);
extern void XRSessionSubsystem_get_currentTrackingMode_mD059B4FBD0D07A428470FF2677002EA51AA2CFB5 (void);
extern void XRSessionSubsystem_get_configurationChooser_mF3975E98A8A317F63FF3D89CF3E5865EDE47E760 (void);
extern void XRSessionSubsystem_set_configurationChooser_m65A4004DB9BA7A2C132DCBA01AD6804509ED5B6E (void);
extern void XRSessionSubsystem_get_notTrackingReason_m78C6CAA0D4570B7E410C1D375A3CC0AEEC1AFE5A (void);
extern void XRSessionSubsystem_get_matchFrameRateEnabled_m0D73F1F3C3B72ACBA97EDA22A62E69C727D2EDB7 (void);
extern void XRSessionSubsystem_get_matchFrameRateRequested_m6344C601746F6822B56E362F1FC4C4ABE15D22A4 (void);
extern void XRSessionSubsystem_set_matchFrameRateRequested_m778FF485C34682FE76BCA4871EB4D1D2F5AF73F1 (void);
extern void XRSessionSubsystem_get_frameRate_m08D6A4F0F598E99A26747827F0630CFDB922A4E3 (void);
extern void XRSessionSubsystem__cctor_mDDDD57BFB0E53C4F4D4E49175E2A8202BC118372 (void);
extern void Provider_Start_m6E563F958F1D15385E35A74FC8A82E6AEA46343E (void);
extern void Provider_Stop_m333D402B548DA93070E850B93D06EC4F1FB07026 (void);
extern void Provider_Update_mED507841F7B7D096FC116F775A144293B59E783A (void);
extern void Provider_Update_m7DA03CBB0EBA3A2798DF9681CBDDBCA0AE224C85 (void);
extern void Provider_get_requestedFeatures_m10A009D1F22281A8E1BDEA2156B6DF78C03BA855 (void);
extern void Provider_get_requestedTrackingMode_m7B7AC5BD9BAEB3B75E96FCC308EDB19523AF2834 (void);
extern void Provider_set_requestedTrackingMode_m8E0E2EBDB0E20C7E4B4DDD4BFCEDF3C8009BA2E3 (void);
extern void Provider_get_currentTrackingMode_m65B8B630C321A5B41ACDC2F01D0DE2FF111CE638 (void);
extern void Provider_GetConfigurationDescriptors_m66DEAAC3E52B4D710710989049669C7DC7B6D643 (void);
extern void Provider_Destroy_m8A97A126A24DE21208CC6C23BDA3C462F7DD0311 (void);
extern void Provider_Reset_mC3F1E5323D153985545F3EDDF20DA73703468FC2 (void);
extern void Provider_OnApplicationPause_m8FB7F1B59209A522883E89CCFD925A67198E9C90 (void);
extern void Provider_OnApplicationResume_m744FBE7BF974465A14E7ABD2EBAD3EC7E588B117 (void);
extern void Provider_get_nativePtr_mB998A97B2BFEC1BB38F64D7B507FA0A1FB9C1B67 (void);
extern void Provider_GetAvailabilityAsync_m0CC77C1C614055744ADFBB72CA4297D4E51A0EE9 (void);
extern void Provider_InstallAsync_mC7CC25E00D2B78EF1D9B8615ADF663BDA2DB2419 (void);
extern void Provider_get_trackingState_m0203345C74B979CB55AC990DA824AD8DE0CA1ADB (void);
extern void Provider_get_notTrackingReason_m1D74C45C3CB3C617E7AB3D79C31F0B77CE7ECA32 (void);
extern void Provider_get_sessionId_m34F4CBEF73037CC97575A15ED53526D5030CCC93 (void);
extern void Provider_get_matchFrameRateEnabled_m99E9CB391AC8D0CCFEBB9591C6725B2B06E2C5B9 (void);
extern void Provider_get_matchFrameRateRequested_m227999FA334E01B18CCB64CEDE9683FB6055B6C1 (void);
extern void Provider_set_matchFrameRateRequested_m7D3DF9134FA202F846C13A322D4E895801E1B19A (void);
extern void Provider_get_frameRate_m3D2F14E59249D30B755798C8C0732B0CEFA059E3 (void);
extern void Provider__ctor_m43661C1B8B6373D456C0CDA35F96AACBAB01290A (void);
extern void XRSessionSubsystemDescriptor_get_supportsInstall_m3EE93DA405D5C59B746B621FC30D4CC182BB1E53 (void);
extern void XRSessionSubsystemDescriptor_set_supportsInstall_m6BF049FADBC0C98964ED4DC227535BB27BBDA6B7 (void);
extern void XRSessionSubsystemDescriptor_get_supportsMatchFrameRate_m516AB9E3CDBE8002D4EBD036EDFD787C46C5FF26 (void);
extern void XRSessionSubsystemDescriptor_set_supportsMatchFrameRate_m7E8D8FF23A84B20582BF51BEF07DCDFF325ACDF8 (void);
extern void XRSessionSubsystemDescriptor_RegisterDescriptor_m93A3C60962B96C0AC75288931A21E2D2C0CF3895 (void);
extern void XRSessionSubsystemDescriptor__ctor_mB1B365F156769946A415E3A37A4FDDE167D5D5B7 (void);
extern void Cinfo_get_supportsInstall_mEEEDF86E5DE1B7515989BAECAF2F1714A327B720 (void);
extern void Cinfo_set_supportsInstall_mA4CF39BDB54C42BC8CBD401F6A77353B59EB074F (void);
extern void Cinfo_get_supportsMatchFrameRate_mD86213A3EA6096133BF355DDDC55823027B48B7C (void);
extern void Cinfo_set_supportsMatchFrameRate_mC501E193696EC21EF655C7B789CABFE2D7D2B3E4 (void);
extern void Cinfo_get_id_m20097DC1BBE19C329FD6F99312B6B5CD14678921 (void);
extern void Cinfo_set_id_m7ABF4746E3D8D5C9F5E845638AB861FFBF665594 (void);
extern void Cinfo_get_providerType_m89ED3FB1640690CFD1735E14FB7834A2BF9203EC (void);
extern void Cinfo_set_providerType_m2B27139CECCAF21A444D7891124893D11B7B6484 (void);
extern void Cinfo_get_subsystemTypeOverride_m5A1888DCB068C90C7C5197246602893767C5A2B7 (void);
extern void Cinfo_set_subsystemTypeOverride_m5DD40EFEE5ADFC4A6624E1EF7345F63D66027423 (void);
extern void Cinfo_get_subsystemImplementationType_m1E7C284EE29C67952C8742FD257B3BA09F84D8B3 (void);
extern void Cinfo_set_subsystemImplementationType_m116B6D5857F561E8119812F01089CDDC08AFF3CA (void);
extern void Cinfo_GetHashCode_m2D7B3F90910D284E6D8696F3E187E61436768107 (void);
extern void Cinfo_Equals_m0C051CCCCA3A44F0C851768FE1CDB49DAC6D82B6 (void);
extern void Cinfo_Equals_m053CC1A9E8D4DBF1FAFACF66083B615EF36CA572 (void);
extern void Cinfo_op_Equality_m7F2BC979F66951435B1D4904A6EB885A0F1D0020 (void);
extern void Cinfo_op_Inequality_mDB236737C959482D1C58D01820447D302C320FB8 (void);
extern void XRSessionUpdateParams_get_screenOrientation_m5BD0BD187D579592C71665C78BB09685F08BB23C (void);
extern void XRSessionUpdateParams_set_screenOrientation_m95E8C1C9AEEFCD0577AE4605645FA8CD8F1D6B9B (void);
extern void XRSessionUpdateParams_get_screenDimensions_mED2BC29E3B820C5CF96ED275DFA172B23EA52119 (void);
extern void XRSessionUpdateParams_set_screenDimensions_mC99924339E008CEFCF202EC394463F00DF0DA4B3 (void);
extern void XRSessionUpdateParams_GetHashCode_m735A861B2C2718DBF5588467EC76FC6EC77EFE8D (void);
extern void XRSessionUpdateParams_Equals_mE2FA6A03BEBC662F543FA73D25561369FAEE7EAF (void);
extern void XRSessionUpdateParams_ToString_m82D102D9405D9B5FCA96E55074C982F225287D80 (void);
extern void XRSessionUpdateParams_Equals_mEC4D21B1DFB2DB2327FCE21B43C144DD2003828C (void);
extern void XRSessionUpdateParams_op_Equality_mF18D3DF08FBD72740EB348A62B5077A71261B650 (void);
extern void XRSessionUpdateParams_op_Inequality_mC310D93D46B42674C8EBF7938945E4AC7B7000D6 (void);
extern void TrackableId_get_invalidId_mDAEC47FD9C1E08B9D5752DA8F185E7A783DBE494 (void);
extern void TrackableId_get_subId1_m1F4296FEADE76DF0379F20BCBD94E807E2EF021F (void);
extern void TrackableId_set_subId1_mFA12049C24961BC2FE7D41A2D0FE30DF4B3F39D2 (void);
extern void TrackableId_get_subId2_m53BAB4D373B736E473381B24CB608EEF666BA24E (void);
extern void TrackableId_set_subId2_mB7AA91412C0731CF59A8CC24CF75012D3C77C76D (void);
extern void TrackableId__ctor_mB12C56ADDEFA44578A429DDA57A6C78B833B41F5 (void);
extern void TrackableId__ctor_m75F2739A83A25E2B7C34DE87E85187F79A4C86AF (void);
extern void TrackableId_ToString_m4BE1AD91726751D994E6FB864B231BE5D7D3F85F (void);
extern void TrackableId_GetHashCode_m6150BF091C3C17A84021CC18B443D5C297F89537 (void);
extern void TrackableId_Equals_m67C98169A04DB96CCEBC08A05B3FF9544B52C3E5 (void);
extern void TrackableId_Equals_m7263BB158392C4F1B57BEE16D4F1FBBCF01E4A6E (void);
extern void TrackableId_op_Equality_m9E51E31C58CA710A1BD2E3AE1D2286E2FE5B3529 (void);
extern void TrackableId_op_Inequality_mBEED941F67FCF17377C6F01882CBA551B47246D4 (void);
extern void TrackableId__cctor_m0233247C572757642543FD7F9D7AFFA432EC916F (void);
extern void XRCpuImageAsyncConversionStatusExtensions_IsDone_m7AD981DD5E7F21BC4ADAD35955B9EBB1C52825EA (void);
extern void XRCpuImageAsyncConversionStatusExtensions_IsError_m699680413B1419513A165D4BAB6481512B5A5E50 (void);
extern void XRTextureDescriptor_get_nativeTexture_m1E27C0E1DC11DDC6139178509EE91B8DF54DBAD4 (void);
extern void XRTextureDescriptor_set_nativeTexture_mE5EF6CBBBE13191EF65501EC9A45C2F64964B27D (void);
extern void XRTextureDescriptor_get_width_m570472F03994BC63F21751414105A2E0C112DBF2 (void);
extern void XRTextureDescriptor_set_width_mA5D674B5378CB5B8AADD7A93E027CBF4BD27A37C (void);
extern void XRTextureDescriptor_get_height_mC0B37241C24FA883E2594B9411080EDF654E3E01 (void);
extern void XRTextureDescriptor_set_height_mCD63667233B39883DF1E431446ED926AC3AF3992 (void);
extern void XRTextureDescriptor_get_mipmapCount_m4B2ED0D6EBE06AD86E356203B4AB5DE3807C1D31 (void);
extern void XRTextureDescriptor_set_mipmapCount_m7B8AAB937C5157B15A280672BC5C105FAF30D7E8 (void);
extern void XRTextureDescriptor_get_format_mA745AA87046D4FE4846C11B8285B980FF6DDDD1A (void);
extern void XRTextureDescriptor_set_format_mAB9FB1797A83CC68AC222A861C185FE2F8035058 (void);
extern void XRTextureDescriptor_get_propertyNameId_mF5A620F0DAEE746BDD293DB7F02909FB5404DCC1 (void);
extern void XRTextureDescriptor_set_propertyNameId_m4D99BAF8AF884D653834D29D124F106A4AD7189D (void);
extern void XRTextureDescriptor_get_valid_mBEE2CC268CC8773618BAB7794118746E235A6761 (void);
extern void XRTextureDescriptor_get_depth_m5885EBF7D767C918B1483D63D1B11EE60D939E7D (void);
extern void XRTextureDescriptor_set_depth_mD62E28995B11B8631C2DF7B02416A2D310F35C49 (void);
extern void XRTextureDescriptor_get_dimension_mAEB2447102404A845F9B20317A2AB82B956E4A12 (void);
extern void XRTextureDescriptor_set_dimension_m75DC4703441BF9E812D18C0DFBF0A9839A52554B (void);
extern void XRTextureDescriptor__ctor_m32EAA2098F51625289A1BFEFFAC002BA9F274ACF (void);
extern void XRTextureDescriptor_hasIdenticalTextureMetadata_mB4DA1A4CFF4ABB66F8FF3AF1F310E60BA1B3F872 (void);
extern void XRTextureDescriptor_Reset_m1BE8024830BA7AFB94AAD01731FDB449DD12A01F (void);
extern void XRTextureDescriptor_Equals_m4931F85C225CAC63EC71FBCE246204E244B6CA2B (void);
extern void XRTextureDescriptor_Equals_m42127F01DF3CDEA1F38CF07E6057E8AD9E6F4570 (void);
extern void XRTextureDescriptor_op_Equality_m01CB132C4DCE6AF54CEDD38CD672D4B6EC539C34 (void);
extern void XRTextureDescriptor_op_Inequality_mE62A303B6BBF10B27B1A15796892656B4271225A (void);
extern void XRTextureDescriptor_GetHashCode_mFEB456F0A0985232D0E342B8F10669149F190012 (void);
extern void XRTextureDescriptor_ToString_m452F36D253986001921C5F627E67E2452D685493 (void);
static Il2CppMethodPointer s_methodPointers[1474] = 
{
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mFDE4670C010E535DF7E9E35FED1FF1D1A4DF7075,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m12AA9072DC7FEC76FC9C799662796AAE2BB8345A,
	XRAnchor_get_defaultValue_mF68ABF2D0EC8B54DD8D5333FCD56EEF14A985A9A,
	XRAnchor__ctor_mDD8A7F48E03A25972AA93D2C89C1D773635CA15B,
	XRAnchor__ctor_m3C8D3F14E6CD1FC66CB5E27096253B81BAA4C75C,
	XRAnchor_get_trackableId_m0F50E81D0152D0BA4152EF9B66F648EF9FC664AE,
	XRAnchor_get_pose_m2347783C1262EEFBC0B817EF0357FA4BB4BF053F,
	XRAnchor_get_trackingState_m6124A26C36CA93C25C57548576CB00C1F496ED83,
	XRAnchor_get_nativePtr_mC0551FA7E8DB8A0DA1EAE02D9B0BFD9D47389C26,
	XRAnchor_get_sessionId_m719628E8A58027C75FF2CEA3345DC41200FB5F76,
	XRAnchor_GetHashCode_mEFA5E37600C1A0B56F911227326704C17C3B5400,
	XRAnchor_Equals_m8F3408527C2CF86CF0A09AE74BF790F8E60ED6F1,
	XRAnchor_Equals_mFD12F373615A9015CB110787F6FF06CDAAC1433F,
	XRAnchor_op_Equality_m0404B51B34ADBD243B8E92F976964CB76E79223F,
	XRAnchor_op_Inequality_m0C41AFDC68E1B648652C01E4BFF271854705A3F1,
	XRAnchor__cctor_m089D3BC26EBFD0569C11B81E4DEAE752346441FE,
	XRAnchorSubsystem__ctor_m7475F45C8D0D2B0E0FE0B91E45A03A0F6541138D,
	XRAnchorSubsystem_GetChanges_m75942CEB1452CCBC366E8AAC9FE1A747B54FE439,
	XRAnchorSubsystem_TryAddAnchor_m17C838AABA88AE000FC4170ADE72C009DCB14EF5,
	XRAnchorSubsystem_TryAttachAnchor_m01E619A03158783EFC56D65258DCC19B5D422BAD,
	XRAnchorSubsystem_TryRemoveAnchor_m88DEBF2B32CF1B4C49581D1ABAC95652741EE4ED,
	NULL,
	Provider_TryAddAnchor_mAD1617DCFF200E647967B17A3BE5292D2BE0840B,
	Provider_TryAttachAnchor_m52CBF550B170FA6F50DDC6705A9AC3F28B4FE175,
	Provider_TryRemoveAnchor_m245D945B15FC5D7657D9E05198F0EE60275E5216,
	Provider__ctor_mEC1254B41F2C452FD31DE2DF6F9D8AA5A4E4FB94,
	XRAnchorSubsystemDescriptor_get_supportsTrackableAttachments_mAA02843053726C4551483D799FBDBCD00AFDDF31,
	XRAnchorSubsystemDescriptor_Create_m81AF7F8FB3993C02C7AB93B292F6D7C65D8050AC,
	XRAnchorSubsystemDescriptor__ctor_m09A98B336838C4CF7BCFE08EB3AF49BFDA9AFF8C,
	Cinfo_get_id_mFF66DF9642403D18476451C30BD5F2FC89CAF8B1,
	Cinfo_set_id_m6344F3700C1D743D82AB9E74925F7687925734A6,
	Cinfo_get_providerType_m2D11E14B5E4C48474C00C579D4C2F5F45970D70B,
	Cinfo_set_providerType_m0E291C22B0B2CF634024F32ECA2E424F18157ACF,
	Cinfo_get_subsystemTypeOverride_mD1DEE5FBF656FDF4BF5E28B60C04F53B6D146C3B,
	Cinfo_set_subsystemTypeOverride_mD5C5CAAC167444FE1BD48C1A16AA904E8DFF7052,
	Cinfo_get_subsystemImplementationType_m9F7B46950CE7957D33333EBBBAA65B27407831FA,
	Cinfo_set_subsystemImplementationType_m97E16A222C068284404DA8CC6C828D34E2F15265,
	Cinfo_get_supportsTrackableAttachments_m2BA4A0E85635C4D85059CDF713EE7FC21F80DBF4,
	Cinfo_set_supportsTrackableAttachments_m58F12783B1EF42ED420CC99E0585FA6ED046C4AD,
	Cinfo_GetHashCode_mE063F9FDF568ECC85F4D30998CB9A071A82C3859,
	Cinfo_Equals_m11F554099FC7163A8405E15A238CD1084BCCB65E,
	Cinfo_Equals_mD8F45C05DFDE73ABDD26DC002B6F0B1506149F6D,
	Cinfo_op_Equality_mE41AF60B60A6867D61C42400994FF54D1F12AB29,
	Cinfo_op_Inequality_m4B20D281F19BB98507567B20742E775B138594DA,
	ARRenderingUtils_get_useLegacyRenderPipeline_m5995F66A625E303025AF9841E9598510CAC457DD,
	XRCameraConfiguration_get_width_mCEA441DFABEDE3E552A2D4452508BCE923B6C3C6,
	XRCameraConfiguration_get_height_m9130BF72BE684B67C2100DD1624AF851E42B81A8,
	XRCameraConfiguration_get_resolution_m8EB20C15322147BCA971867F394BC0E0EDCB5A0D,
	XRCameraConfiguration_get_framerate_m3BFA6E6FB947828EDC20AC9CED31391634F5EB6F,
	XRCameraConfiguration_get_depthSensorSupported_m05B25DB3D4E83E385BE419109288B77684532A53,
	XRCameraConfiguration_get_nativeConfigurationHandle_mD9C92AE35660E0441A296301336578BA11313155,
	XRCameraConfiguration__ctor_m6A41DA8E8540120FABB6436C2DCB07B6BC520281,
	XRCameraConfiguration__ctor_m2943E6AD678C2106CF20EA8CA967F2EAB15303FB,
	XRCameraConfiguration__ctor_m0C8EC1223917D880B58850B06E1DC4902F269C4C,
	XRCameraConfiguration_ToString_mD69E2A39496C4B88A7089AF64A13057585A6F159,
	XRCameraConfiguration_GetHashCode_mE50DD8C034ED9415443191DF89F044B05510CEB7,
	XRCameraConfiguration_Equals_m26024336DA6F68CDCBF7916F6B6BF690DF152FA5,
	XRCameraConfiguration_Equals_mCFE381E6FB6B3650DCAB79FB6894DA8BB511A708,
	XRCameraConfiguration_op_Equality_m8B0FCADB92F8DA1699B6A669DC7FB3EFB375792A,
	XRCameraConfiguration_op_Inequality_mE02244AF68C740BB185D8D5A8AD941D63BD9B76C,
	XRCameraFrame_get_timestampNs_m93571F53415C7DC6195854F3297E95D2E55A4DFB,
	XRCameraFrame_get_averageBrightness_mD1106801D777BFB9EE60FDE5DE194EBACEFB6071,
	XRCameraFrame_get_averageColorTemperature_m29B8FBE0061F8895678D3C4DA5BAB7BDBE154D4E,
	XRCameraFrame_get_colorCorrection_m00236A30115F91E2696EAAAF6F1CDF9CA0F83354,
	XRCameraFrame_get_projectionMatrix_mDE497D5208A1D08226B6B6C7521F53125E6EB9BD,
	XRCameraFrame_get_displayMatrix_m221E85929B55C0B8F6AB494FF27CC3200A80F287,
	XRCameraFrame_get_trackingState_mA6E95E5F574FC6506C0F602E430C42763797779A,
	XRCameraFrame_get_nativePtr_m4DED9CE0A7333F6A1E5C4932A6C98A8A0DD9E62D,
	XRCameraFrame_get_properties_m0C853765A7C76148A439A2C275E3687659DD8DFB,
	XRCameraFrame_get_averageIntensityInLumens_m639F0315B64DA5EC8321609C8673EA14A7263115,
	XRCameraFrame_get_exposureDuration_m4D8412C33F590A282E1671AFD89CC543837BD007,
	XRCameraFrame_get_exposureOffset_m9683C51CB26F830F5FC5720AD0FD541EC053202E,
	XRCameraFrame_get_mainLightIntensityLumens_m55B353C41D7A9E00F596D4BAD4813793C9754BE8,
	XRCameraFrame_get_mainLightColor_mE96BCE9B4E4E241AF9F6BC758942EB8D4584138C,
	XRCameraFrame_get_mainLightDirection_mF33683D8BF23ADFB8EBC2D2875C2EDF5922F21B8,
	XRCameraFrame_get_ambientSphericalHarmonics_mB62D6BDCC0A0DAAB5C057225A84289502076EFCE,
	XRCameraFrame_get_cameraGrain_m7FF742DB5555C9D84DCD7937828C612FAACFEEFE,
	XRCameraFrame_get_noiseIntensity_mA1D17EA6D00D7FF958FFF6A62B99B34B052F2FEC,
	XRCameraFrame_get_hasTimestamp_mD6AD6768B71946B0643836ACD28BF32876A5E0FF,
	XRCameraFrame_get_hasAverageBrightness_m8CC4709AA168C8762763837B384B7332FC2B73B0,
	XRCameraFrame_get_hasAverageColorTemperature_m163AF5FAD20B5779A28550ED502F5037C4BDB93A,
	XRCameraFrame_get_hasColorCorrection_mCEB8BC23DF1997AB5DFCD013F56111FB8A8D118E,
	XRCameraFrame_get_hasProjectionMatrix_m850BCDBFBBD894BF56EEED3A82349A4E4811CC1F,
	XRCameraFrame_get_hasDisplayMatrix_m7D5DA2AA4F3C83B25714C0FED9EEAE1E51B95959,
	XRCameraFrame_get_hasAverageIntensityInLumens_m7E14C289B8D931F55B7A98D5075263E96CE3B4DE,
	XRCameraFrame_get_hasExposureDuration_m02C1ACB25E72D090C9A56FC158E8D4B0D3C04D50,
	XRCameraFrame_get_hasExposureOffset_m6A4048142BD1E59E403F858144092C5F7846CA53,
	XRCameraFrame_get_hasMainLightIntensityLumens_mA423D7DEF78D1888AFED8BF17B3E1037C24E469B,
	XRCameraFrame_get_hasMainLightColor_m07A53E75212D8BA3582613228AC0DACBDDF983FF,
	XRCameraFrame_get_hasMainLightDirection_m67DFB7C0DAD130D98290130131EDC4BA62818B5E,
	XRCameraFrame_get_hasAmbientSphericalHarmonics_m45F02EFE6E47FC9B9AEE4F1B6AEC4F9E7FF1F92A,
	XRCameraFrame_get_hasCameraGrain_mC37056CCCDBEFD620038107A078B6A39F61D99AE,
	XRCameraFrame_get_hasNoiseIntensity_m076641BB06432F1F27EFD353B6E7116B098BE4B7,
	XRCameraFrame_get_hasExifData_mB7025B1AA39F37A2DAB00F455D9803096B12DF24,
	XRCameraFrame__ctor_m7A19EA8CCC1391BE7463FF9B492863BDA975E90B,
	XRCameraFrame__ctor_m991480EB7E8C7C3A8C63974AFE3FD8900A2EBC03,
	XRCameraFrame_TryGetTimestamp_m60FE1777D7379C288482A23E5A7C5B297F1DDD94,
	XRCameraFrame_TryGetAverageBrightness_m1FDC9DCF0A34227DC5ECB78A5E80E614F0063C98,
	XRCameraFrame_TryGetAverageColorTemperature_m2737123C8E46EF119C04162FAD8EAA312FA2EF3B,
	XRCameraFrame_TryGetProjectionMatrix_mC25F35BF17829DBEDA748D61362289E4842B5098,
	XRCameraFrame_TryGetDisplayMatrix_mA034AD550B172CACDDED708A12FFBA548E750312,
	XRCameraFrame_TryGetAverageIntensityInLumens_mE5FC24C1E3D49FB679137698834F22BF258DD674,
	XRCameraFrame_TryGetExifData_m99AAC21B7048242487A8D48DE8D92AC9F4C53AF4,
	XRCameraFrame_Equals_mA1542DDF01588CB1AEDFEB763F63684C18B717C8,
	XRCameraFrame_Equals_m53FA29D21C4E68E89D59E8904EF7222571C04A50,
	XRCameraFrame_op_Equality_m0D5674F05EA2745784FD5D098C1874E0E7C4E708,
	XRCameraFrame_op_Inequality_mFB2DA9148F3B10BCFB296FCCE66CD208C05E40BD,
	XRCameraFrame_GetHashCode_m9392ED676A0D41F404CF03A0C570068AC2832352,
	XRCameraFrame_ToString_m4825A21E18219B4B626C1A2CB3EDD006DFCA44AA,
	XRCameraFrameExifData_get_hasApertureValue_m2C361DC00694AE380ABE97C9A99F6B645CC8F53F,
	XRCameraFrameExifData_get_hasBrightnessValue_m39C2542ED3A9A2F1BA136FE4EE381DA0C21ED23D,
	XRCameraFrameExifData_get_hasExposureTime_mF0CE4E1C0E88FF4D3B727E72DA507117A252CC0C,
	XRCameraFrameExifData_get_hasShutterSpeedValue_m3AC9A6CD82AAC78E5E74D1AA654CBCC0D7F7B2C4,
	XRCameraFrameExifData_get_hasExposureBiasValue_m2A069367FABB0671F888CDEDEC473BE1841211E2,
	XRCameraFrameExifData_get_hasFNumber_mFCA781B3A5BE10C965D76A608B49CDE619ED46F4,
	XRCameraFrameExifData_get_hasFocalLength_m0107A9D19FE993FB4836FB1EA5BF17656F2FDEF0,
	XRCameraFrameExifData_get_hasFlash_m6CFD79284C340A886C21336708FE674F6EF80254,
	XRCameraFrameExifData_get_hasColorSpace_m8B09CAAB1A79D588F04F3D0E26F42261B87EFE7F,
	XRCameraFrameExifData_get_hasPhotographicSensitivity_m8F78AC08D6DFE157D1B1D40D1D06517CAF6FFB1A,
	XRCameraFrameExifData_get_hasMeteringMode_m62016BF52989FE5849139AFB087F6C0244517D7C,
	XRCameraFrameExifData_get_nativePtr_mCB1987D3B4CABB2C972BE7D4E92365763550783E,
	XRCameraFrameExifData__ctor_mC632ECB5B132C6297A8641A5061024B71F850242,
	XRCameraFrameExifData_TryGetApertureValue_m4F59F7DEF4992F533DE48D18DBBB0A01EB0E684C,
	XRCameraFrameExifData_TryGetBrightnessValue_m3C19A37A3534F7E451759E89578260FADBC9B14F,
	XRCameraFrameExifData_TryGetExposureTime_m46BBCA6CA64E217ED60F3D878983314A59C6D5FA,
	XRCameraFrameExifData_TryGetShutterSpeedValue_mC955FAA772EC0893B3D06E4ECEA8EE80023FE429,
	XRCameraFrameExifData_TryGetExposureBiasValue_m52A2F65945A75DAC6F0BD013EB94DF65D5A6BF0C,
	XRCameraFrameExifData_TryGetFNumber_m7512F213AD901E6FCDCDF7FD9A582AB322F6063D,
	XRCameraFrameExifData_TryGetFocalLength_m0109D48BD14F7E853514F66ACD493DD1657EC138,
	XRCameraFrameExifData_TryGetFlash_m8BE2090E79E851BA4961115DC320CEB3B6A2A4AB,
	XRCameraFrameExifData_TryGetColorSpace_mDD98A093493C3E1659125B797F0E8C49E2C25E76,
	XRCameraFrameExifData_TryGetPhotographicSensitivity_m215A7867030DC8738CFDF104E0CE9DA2569A6307,
	XRCameraFrameExifData_TryGetMeteringMode_m3DE1E2935F15F9F91899A58E0585C3CB1E7BFE34,
	XRCameraFrameExifData_get_hasAnyProperties_m608D0509C48BB26408E3B58ADE152ED372101624,
	XRCameraFrameExifData_Equals_mE16880FA32EC214A99E76AA88BC65988E6A61919,
	XRCameraFrameExifData_Equals_m55925A51CE7F4B028CF9B77393BD8FCCB56F7302,
	XRCameraFrameExifData_op_Equality_m3497247DF5744B5F4CBC6F73C0A93D7A600B5235,
	XRCameraFrameExifData_op_Inequality_mF774C01339682698B7BACA01AEEF2A6650E8FD69,
	XRCameraFrameExifData_GetHashCode_m3FB9ECCBAE6452B72A6B167F589098899819AFAA,
	XRCameraFrameExifData_ToString_m9200B453787D807084089B68F941C9B768A8D374,
	XRCameraIntrinsics_get_focalLength_m9B19B7C0AF4CDAF1C8BA121C20BE8A80A7DF778D,
	XRCameraIntrinsics_get_principalPoint_m677A9880F319E54576353AD01EF0936317E1D83D,
	XRCameraIntrinsics_get_resolution_mDC07EA111909E8903F1B89577FA2A6BF8FB14D52,
	XRCameraIntrinsics__ctor_mA7F2F4A7709FC7DA6E9560367A08C28374365020,
	XRCameraIntrinsics_Equals_m7C6C306C554F5F2A69E5CB831FB2C38F7A252866,
	XRCameraIntrinsics_Equals_m81F681CB1C13344784F9B2DD6ACE032F2C9A06AE,
	XRCameraIntrinsics_op_Equality_mFD411F2F4CA10EBCF11DFCECE0E687FB5C3473CB,
	XRCameraIntrinsics_op_Inequality_m0394B23AFD6845446CADBEA312E54D70A72BAB90,
	XRCameraIntrinsics_GetHashCode_mEC06B793ED903AC34149EAA935C66284947CDF63,
	XRCameraIntrinsics_ToString_m3C7131BDF5882D7F01DA3EC7A7544A16E7F6A783,
	XRCameraParams_get_zNear_mECA80F2D2C74318641F94031BB7964DD06ABEA75,
	XRCameraParams_set_zNear_m13DFECBAE558037DEBE998F3EFF2E1C6372EE6E0,
	XRCameraParams_get_zFar_mDF023917C3AD6AA2C909A2295219F34B085638DA,
	XRCameraParams_set_zFar_mA528373BCB66A9DE2A393512B883B932AB02D600,
	XRCameraParams_get_screenWidth_m791F2E175953698508D73BF5B38087BA66875FDA,
	XRCameraParams_set_screenWidth_mA367A9BD005F2F052549E9B509F4D41F276CA021,
	XRCameraParams_get_screenHeight_m560E3D0692A29242E6E137CA8895C3754E8A7745,
	XRCameraParams_set_screenHeight_m7F6C7A3B7F3D7AEBE074A19FF20EF3DDACB79DE9,
	XRCameraParams_get_screenOrientation_m0EC129A67B19D30348027E60C9A6C982DBC89D3A,
	XRCameraParams_set_screenOrientation_m9AA6D552ED0B67E9560CDF2C775FC27AA7A83A07,
	XRCameraParams_Equals_m5C32A8D9FE83014E8A424C2D09688635E88A86B0,
	XRCameraParams_Equals_mD063C934A21CE21B40F834E0C90AFF645A236CD6,
	XRCameraParams_op_Equality_mBF89D93B1308F6832CF32F04CD8A2AD0D1AB4FF8,
	XRCameraParams_op_Inequality_m659EE8FE4FD6A1ED934D78C8314763D26607C496,
	XRCameraParams_GetHashCode_m1F7C2E3CC02169BDEC645B0CE0E540BB1FE1FCD5,
	XRCameraParams_ToString_mFD8C6218C724EAAF2F9A953CBB08AA3BBF67DB64,
	XRCameraSubsystem_get_currentCamera_mDF05518674B6B2670F8D1C4935E3A217A253F23F,
	XRCameraSubsystem_get_requestedCamera_m083BB1AB28B6688AF0288319A512C001FB1DE563,
	XRCameraSubsystem_set_requestedCamera_m41B07B375D09940FD64D00A524676BFBEECE3743,
	XRCameraSubsystem_get_currentCameraBackgroundRenderingMode_m683149B9AFDCDCBE8A77BDCCE2C00FB28CCBE001,
	XRCameraSubsystem_get_requestedCameraBackgroundRenderingMode_m56DC4757DC2879EF6CDE27C0C15F62A6DD4945F7,
	XRCameraSubsystem_set_requestedCameraBackgroundRenderingMode_mA90F7E36243E2F47695F2AAF67DB8DE33FFB9866,
	XRCameraSubsystem_get_supportedCameraBackgroundRenderingMode_mBF24A55AEA6D0373F34D920A9C3D49F6B710B51C,
	XRCameraSubsystem_get_autoFocusEnabled_mBBD1CBAE8AA82A03319FC2A63C7E60C779D510B1,
	XRCameraSubsystem_get_autoFocusRequested_mAAA29953704501756BA8C2F2D89CEC7C50409463,
	XRCameraSubsystem_set_autoFocusRequested_m1E4DF6EAB729F7F33E7FA0FC22DDFEFF01621D1F,
	XRCameraSubsystem_get_currentLightEstimation_m4D1383D9B31058CD35AEA3EA516128A33008D75F,
	XRCameraSubsystem_get_requestedLightEstimation_m752067094CD340F41677378C95964CF917BC035E,
	XRCameraSubsystem_set_requestedLightEstimation_mC26C254AAFF86EF0566097155BD192D8B83E7906,
	XRCameraSubsystem_get_cameraMaterial_m72EF1ABC5722AF08B9558BAA648128D30EDA409F,
	XRCameraSubsystem_get_permissionGranted_mA3BCBF249E8BFBC8BD5D04C4610254E105D41038,
	XRCameraSubsystem_get_invertCulling_m02A291BD6E8007DB0194B976901890AFF3BB9E52,
	XRCameraSubsystem_get_currentConfiguration_m11C191A5186B39689AC1B56D4984C496C4A6AD68,
	XRCameraSubsystem_set_currentConfiguration_m2D5F8AD3C0CD8BF214BEB6EC0BC2214D8C19EE4C,
	XRCameraSubsystem__ctor_m1BE10CD8CF719A3400CFEF6AD97D8E025CA91504,
	XRCameraSubsystem_GetTextureDescriptors_mBA520669443C18378EF60F9C4359CB540021BB95,
	XRCameraSubsystem_OnBeforeBackgroundRender_m32E2330EDF383ED56B8517A3492C7338A034C102,
	XRCameraSubsystem_TryGetIntrinsics_m9760F1024CF37C40BDD55BCDF05AA890989FBE65,
	XRCameraSubsystem_GetConfigurations_m1DEEFD0803F83FCEE290A5DF70A0B421CECB8EAE,
	XRCameraSubsystem_TryGetLatestFrame_m0C289061CF62517D75F72DF57CDAB1B1DCEF1B58,
	XRCameraSubsystem_GetMaterialKeywords_mCCA840FB3CC02F9AC53264AD51319FBD123BF120,
	XRCameraSubsystem_TryGetLatestImage_m3817BC7E9C193CAF5E89C71CF5FE65D78EE0356E,
	XRCameraSubsystem_TryAcquireLatestCpuImage_m68B67E969F8C47578A2C795B0F7F863B453F0F5A,
	XRCameraSubsystem_Register_mABE4E80E5EB7E46A991A68B8D13FA333A1FFA031,
	Provider_get_cpuImageApi_m29409A35FC66CBB351613BCA11B5FB4CBE34E4FE,
	Provider_get_cameraMaterial_mAF01432424E8C359F13EDAC37BC017D4CA800ACE,
	Provider_get_permissionGranted_m8D05057122F2BBC110DD9AD9A5FB66E0312AF7C9,
	Provider_get_invertCulling_m5025907EE0C401AB84F9C69F4B4720D1B2FE0D94,
	Provider_get_currentCamera_m8530CF31483EFC1C3258E16CA3ADAAC4EFAE3DE8,
	Provider_get_requestedCamera_mB8CEFB334B24A23B0874F271FB9B37DCB31C26A4,
	Provider_set_requestedCamera_m47BE9F53F40D3C42E3B833CC80284E6D2800D766,
	Provider_get_autoFocusEnabled_m9F658CC090005D6F724B6D0A96E93B6D3F3F5774,
	Provider_get_autoFocusRequested_mE38DE01AD3FF2729846B8FD792319D439EB4A774,
	Provider_set_autoFocusRequested_m4516914B85E2D883A12F0C525A59E9D3FF3B9D64,
	Provider_get_currentLightEstimation_m5401CCF4ADDDAD2C8F5E6091D5061D49B0BD9B8E,
	Provider_get_requestedLightEstimation_m7341D9AA92C5509B4CA1EEBB0D4D9278F38D0D04,
	Provider_set_requestedLightEstimation_mEB829A0CE9D43CF8E4ED455C838F3A48FFA63355,
	Provider_get_currentConfiguration_m0BDEF4F581B6343B544FC0C9D9B859DEA95A71B2,
	Provider_set_currentConfiguration_mC964C94FB6C439A55027A6EEBCC981E6B9AD9102,
	Provider_get_currentBackgroundRenderingMode_mCAAC7D83E29EA6CE1E99B4D5A4EA7F967CA08D73,
	Provider_get_requestedBackgroundRenderingMode_mA4BB4A4DBD2BDB9E383B05021FFCFBC2B282C244,
	Provider_set_requestedBackgroundRenderingMode_m7616B5F4D072F24B0ABAC20BB9F6E1967CD454BD,
	Provider_get_supportedBackgroundRenderingMode_m8AAEBBBAD356DB94F8D79F0B9EB1BDF983279D40,
	Provider_Start_m4CD2850BF6C807410FA212456000E8B7D2A1C6D8,
	Provider_Stop_m4CB61ECB8BA1E70EF743BA35E2A5EB2B03B9D64E,
	Provider_Destroy_m8392AF597AD7FE60AC7BA5CBE3DC0996E6AB677C,
	Provider_TryGetFrame_mECA5AA7D54F5FCAF960E81AD40A1EDC94379ED42,
	Provider_TryGetIntrinsics_m111DCBBE57EFDFF6C6C66F311A20CAFB2F38D2BA,
	Provider_GetConfigurations_m43EDF5C2E292648F155EFAADA6B58677D4180F3A,
	Provider_GetTextureDescriptors_m40D6B5D3886EFA8284D14CD5FD67FA122CAB0976,
	Provider_GetMaterialKeywords_m957D06365DD9D0925CBE170AEF4EAFA4262EF945,
	Provider_TryAcquireLatestCpuImage_mEA8D19453751CDE916559C07E4EF7D2A6A623258,
	Provider_CreateCameraMaterial_mEABBEF06EDE2ABD42F8DEDD94D84B0324F80C9AE,
	Provider_OnBeforeBackgroundRender_mDC6772B81F39571941F561DC85FC7007E083909C,
	Provider__ctor_mC30CBDE9E605D4DD1F82355FBC92F02272A9D132,
	XRCameraSubsystemCinfo_get_id_mDCD0C107058AEA702A80B8E305F262CAB8E07FD0,
	XRCameraSubsystemCinfo_set_id_m24A04B94756616FBA387977AF0F6A894D4DC5BCE,
	XRCameraSubsystemCinfo_get_providerType_mFBE1614FF701AC94FE53078962B455A355EA45A7,
	XRCameraSubsystemCinfo_set_providerType_mFE0D3D8FEAF0FFEEA66D0E2C7CABB36944EAD484,
	XRCameraSubsystemCinfo_get_subsystemTypeOverride_m5CD01638A223E6C620ADDDDBA05F7539ED5229C4,
	XRCameraSubsystemCinfo_set_subsystemTypeOverride_m82AD4886D0CE8C4D762AC459630CBE13D024FCD2,
	XRCameraSubsystemCinfo_get_implementationType_mCD8363878940A476D3A3D5CD999A01ED736529F7,
	XRCameraSubsystemCinfo_set_implementationType_m8A37CCAA77FD7B184244763108178DC60BA69D0E,
	XRCameraSubsystemCinfo_get_supportsAverageBrightness_m694C685E738909EABF44140E672CB8176649D7E6,
	XRCameraSubsystemCinfo_set_supportsAverageBrightness_mD5F49B41F00DBC4C531FB5593E572E6A67B1EE77,
	XRCameraSubsystemCinfo_get_supportsAverageColorTemperature_mDCCD0A414E292EAD5FB817C6AC16DE8AF8C7D076,
	XRCameraSubsystemCinfo_set_supportsAverageColorTemperature_mBF21A51F1C4ED4A72AF54588CF9E08DDC92A213B,
	XRCameraSubsystemCinfo_get_supportsAverageIntensityInLumens_mD46BA194C3AC26510694D649673015A450D0F019,
	XRCameraSubsystemCinfo_set_supportsAverageIntensityInLumens_m7FEF058FFE0C0B54E91A8FC8500F88C17B294743,
	XRCameraSubsystemCinfo_get_supportsCameraGrain_m9D63DE92F3FD22536481F75DC9092F3526EB9FF2,
	XRCameraSubsystemCinfo_set_supportsCameraGrain_m3E5D929246B89F7B2EFE1F7E75B9A20C67A556C9,
	XRCameraSubsystemCinfo_get_supportsColorCorrection_m8BEA88F615A8CD5FC068D9F7BE8039C68A110A15,
	XRCameraSubsystemCinfo_set_supportsColorCorrection_m5E9520636C4268DFAFB9E84E5901FD65E14BBB7F,
	XRCameraSubsystemCinfo_get_supportsDisplayMatrix_m33AECE0011BFF7206E0F6A99C3C32ACD28DF09B0,
	XRCameraSubsystemCinfo_set_supportsDisplayMatrix_m0AF4D70253EF5AC5124C747D6F1AF39B6E199B53,
	XRCameraSubsystemCinfo_get_supportsProjectionMatrix_mD62E482D66D23D02FBF1591EFE8CBF6AB2B7AA60,
	XRCameraSubsystemCinfo_set_supportsProjectionMatrix_m5A4C42AD394D5FEA20DB5FE1C045634929839B7A,
	XRCameraSubsystemCinfo_get_supportsTimestamp_m53905529FC20BB4B064986AC5E6586DAF996148F,
	XRCameraSubsystemCinfo_set_supportsTimestamp_m46561CEC2016CA165B4E725395C0E8836C0B46F3,
	XRCameraSubsystemCinfo_get_supportsCameraConfigurations_m107FD5D148D109EDDE9345754995ACF01D7A3F67,
	XRCameraSubsystemCinfo_set_supportsCameraConfigurations_m5425AF5D348E1918644909C45ABA7220D2A8B92F,
	XRCameraSubsystemCinfo_get_supportsCameraImage_m3C11104CBEE0AF690F3A4F9729F78F48E1065970,
	XRCameraSubsystemCinfo_set_supportsCameraImage_mC9312D97D6F2508F8692EF4C40909CBA55F6D769,
	XRCameraSubsystemCinfo_get_supportsFocusModes_m0B2A62A1F5A5603B2BE190A5B5AB0703842E6B51,
	XRCameraSubsystemCinfo_set_supportsFocusModes_m7A495A132ED5160BF69E69CAAA132F83319A3191,
	XRCameraSubsystemCinfo_get_supportsFaceTrackingAmbientIntensityLightEstimation_mA6C68E53BB8242F222104A33EA99EF7AFF754A9B,
	XRCameraSubsystemCinfo_set_supportsFaceTrackingAmbientIntensityLightEstimation_m17EC791181F76254B286308B213F19E085DE36D9,
	XRCameraSubsystemCinfo_get_supportsFaceTrackingHDRLightEstimation_m28390AEBE6EBE78D410C0CB9D8D7DBE237D8DEFF,
	XRCameraSubsystemCinfo_set_supportsFaceTrackingHDRLightEstimation_m022FA258FE1F3E9D1D94D02985C3459298093D3A,
	XRCameraSubsystemCinfo_get_supportsWorldTrackingAmbientIntensityLightEstimation_m85AD9C10C625555A50D39FE3FE5B75E4DFEAC0A4,
	XRCameraSubsystemCinfo_set_supportsWorldTrackingAmbientIntensityLightEstimation_m5F43C3317A17DAFA6ACF9550069FB10F8EB50300,
	XRCameraSubsystemCinfo_get_supportsWorldTrackingHDRLightEstimation_mACCAED13DF9023738352CB5C89D5CBCF01B46899,
	XRCameraSubsystemCinfo_set_supportsWorldTrackingHDRLightEstimation_mF04D46841BB7C8BEF8A17300F6F12491A8AC895C,
	XRCameraSubsystemCinfo_get_supportsExifData_m594585E6727EED9B24E835F2E14109565327F680,
	XRCameraSubsystemCinfo_set_supportsExifData_mBAA63CA8656661FF0D35EB93605744BF7ADCA0B7,
	XRCameraSubsystemCinfo_Equals_mED29F3CB627AF187AC3CB817FC3DC7905B0228A0,
	XRCameraSubsystemCinfo_Equals_m6D8C4679C25B3C93C0BAC84C2E8214DC861285F3,
	XRCameraSubsystemCinfo_op_Equality_m2221466839FF5329486081E62F5C2EC619F01063,
	XRCameraSubsystemCinfo_op_Inequality_m0FDA17789BE28772ACE584DF0D0348C2E08110D1,
	XRCameraSubsystemCinfo_GetHashCode_m9AD75EE7E43274694277A4703099621467E69114,
	XRCameraSubsystemDescriptor__ctor_m82691F2759363796E9011E462546B4148C2D0BF1,
	XRCameraSubsystemDescriptor_get_supportsAverageBrightness_m394F0A5371B43E4A53DF4AAF18EC2CB3A3BA30E4,
	XRCameraSubsystemDescriptor_set_supportsAverageBrightness_m85E4E5A8B48C865C09FD14FA2DA245172F82B5C9,
	XRCameraSubsystemDescriptor_get_supportsAverageColorTemperature_m45A35D2620A0744755E9073E1E8CC96EDF8992F6,
	XRCameraSubsystemDescriptor_set_supportsAverageColorTemperature_m90107A708A0D68DC9B400DF24DAED2365A5024EB,
	XRCameraSubsystemDescriptor_get_supportsAverageIntensityInLumens_m15A6095FCF7EE2C44DA00C8A7EAA0FB185F6148C,
	XRCameraSubsystemDescriptor_set_supportsAverageIntensityInLumens_m8CD28D5D10E6A269D79E2308FC675AF4FDA28313,
	XRCameraSubsystemDescriptor_get_supportsCameraGrain_mA7563A6FB9661F093C972DBF54BC9A3F372624F7,
	XRCameraSubsystemDescriptor_set_supportsCameraGrain_m3C600B4A99E724820D2FAF3F08DA2C56B0D70D4F,
	XRCameraSubsystemDescriptor_get_supportsColorCorrection_m380D4EFA54BD40435F3EC0D4811C17600F60FA3B,
	XRCameraSubsystemDescriptor_set_supportsColorCorrection_m1242C61D6537FCEA26482B5D74DB1943F7E2326F,
	XRCameraSubsystemDescriptor_get_supportsDisplayMatrix_m9AA5FE7C2A422C5B7C85EEB1DEF1E9058D2ECA11,
	XRCameraSubsystemDescriptor_set_supportsDisplayMatrix_m5589A27DB1C3D5A896E9683287DAF5957B8B110D,
	XRCameraSubsystemDescriptor_get_supportsProjectionMatrix_mD16AFD308BF077947336064405B66A3762AE0844,
	XRCameraSubsystemDescriptor_set_supportsProjectionMatrix_m533035E1EA87892B36B8BD1FCA6DD12C48125F0B,
	XRCameraSubsystemDescriptor_get_supportsTimestamp_mB83389AB4E0CCEB67856DF32DA1EDEC64E5A2DEA,
	XRCameraSubsystemDescriptor_set_supportsTimestamp_m2077AC114ABD729DB39DB4DB648D54BFA7842C9D,
	XRCameraSubsystemDescriptor_get_supportsCameraConfigurations_mDA577F21C255F11827B6226688C41D1DDE20A753,
	XRCameraSubsystemDescriptor_set_supportsCameraConfigurations_m94AFD663BE701A019D04CC7E070361F713053B94,
	XRCameraSubsystemDescriptor_get_supportsCameraImage_m4C81161C7C5D5873D7673681206B1B8B0D158F50,
	XRCameraSubsystemDescriptor_set_supportsCameraImage_m1AC168FAB7E1344E8EBCE561A8A2C5E0FD8D3456,
	XRCameraSubsystemDescriptor_get_supportsFocusModes_m629008B6904FEC5721E3BB251E549D74553981C2,
	XRCameraSubsystemDescriptor_set_supportsFocusModes_m898E4A424D88ECA8BDC410BC7DD4DA8ADADC3469,
	XRCameraSubsystemDescriptor_get_supportsFaceTrackingAmbientIntensityLightEstimation_m32CA41294F34BA9A20D90F8AA1204DA923C72337,
	XRCameraSubsystemDescriptor_set_supportsFaceTrackingAmbientIntensityLightEstimation_mB285F66614F9E813114F14E03D6ACF0C713B061C,
	XRCameraSubsystemDescriptor_get_supportsFaceTrackingHDRLightEstimation_mA892EB29FFD62B3C49E5EA4834CF5C27DE39824F,
	XRCameraSubsystemDescriptor_set_supportsFaceTrackingHDRLightEstimation_m21A59BE7013A7C47829CDC98879B536C8D168628,
	XRCameraSubsystemDescriptor_get_supportsWorldTrackingAmbientIntensityLightEstimation_m1F47D027047EBB365C69BE50F442BEFEE5A5CA94,
	XRCameraSubsystemDescriptor_set_supportsWorldTrackingAmbientIntensityLightEstimation_m5514636E6A56C6528D0E411F9FDAEAA23BB35D9E,
	XRCameraSubsystemDescriptor_get_supportsWorldTrackingHDRLightEstimation_mF08EE18BA151F1D274A100ECD32045427C76EB83,
	XRCameraSubsystemDescriptor_set_supportsWorldTrackingHDRLightEstimation_m98835252EB322907CE7E1C1214F7A125C81570B8,
	XRCameraSubsystemDescriptor_get_supportsExifData_m968A05AC795750EFA35B4CDA5CF4B448B0896068,
	XRCameraSubsystemDescriptor_set_supportsExifData_mC70AF5A20E0694668614C5EA974422E3A8DEF257,
	XRCameraSubsystemDescriptor_Create_m5F5E106B8F6CB7B780CD1CF7EE800AA59A43BCF2,
	Configuration_get_descriptor_m3C4973351367EA0BD9E48DA1E2201D8803BA8D1E,
	Configuration_set_descriptor_mBB8354A895DDAD560293EEF81BFFDB4CB30070F0,
	Configuration_get_features_m704F372E940AF1DB435C1EBFF8E48EAD4E8B3776,
	Configuration_set_features_m9F397F777C9593646918ECB4AF778336900ED3EC,
	Configuration__ctor_m4D712D942AEBEF0DA6B5687C1D9CD4E24F0ED4AE,
	Configuration_GetHashCode_m19DCAAF7939DB5DAAF29A2A4E994D41F66FB73D2,
	Configuration_Equals_mFC36BD166DE654A704096918BDA1FE9E34A7B7E6,
	Configuration_Equals_m8D6DE5FC0FAD2DD34D2F3CEF1738FC3A2F131A91,
	Configuration_op_Equality_mF1F31AFFDE2EB88637CA97D3F8D2DC788C0A96C8,
	Configuration_op_Inequality_m02D4E5E82910A6C7BBCEC7B3ED5C6DA8B5608010,
	NULL,
	ConfigurationChooser__ctor_mBA387FADB0244DB6C71741AB9DE75E881B15B803,
	ConfigurationDescriptor_get_identifier_m858F4B730002C1823D283460115DA65C6A46BCB6,
	ConfigurationDescriptor_get_capabilities_m6A4EF4C0E0FE3671E8564EF13BA2A5B4264CF938,
	ConfigurationDescriptor_get_rank_mEDFBF5E2173FA84A0695BB01A6A40860794F6FA8,
	ConfigurationDescriptor__ctor_m79BD6295C5A725B6B65CA3A4281EC801C12B2C41,
	ConfigurationDescriptor_HexString_mA5D97CE4BCD0DD66455BB9BE281302136382BCD5,
	ConfigurationDescriptor_ToString_m20EA191A42A1855B5E97CD8949F6AE5B9ACBDF65,
	ConfigurationDescriptor_GetHashCode_mAD2765B79FFD1806DEA8D927D928C496AAADB411,
	ConfigurationDescriptor_Equals_mC5F92BBF22292A48CAD47A31EF13F3D5A0DC4091,
	ConfigurationDescriptor_Equals_m4FAAC4A13BF03211A9C3EB66F65FB48BE334A611,
	ConfigurationDescriptor_op_Equality_mF2D570C2A6B27299C7DA2656A99471085D37A572,
	ConfigurationDescriptor_op_Inequality_m6D2C9B33FEC965851D3277704072A6E03E6C6EB2,
	DefaultConfigurationChooser_ChooseConfiguration_mBFF0C082E3C3A36847A725E3C44102C60298DC63,
	DefaultConfigurationChooser__ctor_mDFBE2A3915F886FE4D79CD514F550A10CCC6A90C,
	FeatureExtensions_Any_m4AFD1CF424DDB83A30DBB1C78A58F5DC81308573,
	FeatureExtensions_All_m23416D7BEA5829E61EBCF25D41C82B9190E19D11,
	FeatureExtensions_None_m92BF4CEBE40BF1721C9B28F085B7C7B1D7080794,
	FeatureExtensions_Union_m28DDE32C51A4F6B39DCDCCB431D962639EF9696E,
	FeatureExtensions_Intersection_m81F55D33EE5F952118B65F491B3A26C531A2C0F1,
	FeatureExtensions_SetDifference_mA9D5A50B2BF9B5C66047D6152384DC14D5BBCE9F,
	FeatureExtensions_SymmetricDifference_m875C129D61A47A27FA8E5DBD32C5CBCBEF6ECB7D,
	FeatureExtensions_SetEnabled_m52E127CF05A9ED2AF02F3775040A9073741E9C7E,
	FeatureExtensions_Cameras_m726A6A7FE0A234C11F8FCF7DD3A4AA94E3ADBC44,
	FeatureExtensions_TrackingModes_m98FFB5EE942CA939B0F4F06A425673A4081287B7,
	FeatureExtensions_LightEstimation_mB23FB857088B2902CF6ACC21DCEBA3995D8F3038,
	FeatureExtensions_WithoutCameraOrTracking_mCDEF4FCD59CC8C1688E0BE66376EE64583B6E0DF,
	FeatureExtensions_LowestBit_m0E549BA3822C6732458DAB421C673B7D774047DF,
	FeatureExtensions_ToStringList_m7961C38D97DCDD6FB5EAC9AB77104F97D5304214,
	FeatureExtensions_Count_m4115A16C8A0123EFF727DA3A198C09F2B1B327A8,
	XRCpuImage_get_dimensions_m49AF06CB1BDF89E7C9EC343D3260BD73ECABF414,
	XRCpuImage_set_dimensions_m67B3C05A3CA2F0CED5B4E1808967FF2BF77AED86,
	XRCpuImage_get_width_m176240EBEBBD41DC5AEF33F945C88E9492370AFA,
	XRCpuImage_get_height_m139489AD26B264FA46EE5659258BBF9C6584E5E9,
	XRCpuImage_get_planeCount_mEDCBE71D55CCC9FDA1B3ED951306875283E37B6B,
	XRCpuImage_set_planeCount_m51DC647BC967DE5E565AA4ACF66B5B86FE380B13,
	XRCpuImage_get_format_mB777BBC485ED5A88CD78536F78F43E9795DEEE20,
	XRCpuImage_set_format_mBC167A4F6985102169436A10C58AF5EBC17B4C1F,
	XRCpuImage_get_timestamp_mA80E146875C26B8F319B283C20A6BD499AD55B90,
	XRCpuImage_set_timestamp_m7FF97B03D5A4506993F8119BCB4BC47B185AA8D1,
	XRCpuImage_get_valid_mFF799BC0D09BF35BD3AEC063FF5558EE2EB6766F,
	XRCpuImage__ctor_m06AE81550FF74789CD8D66ABBA9B2F3D9D060612,
	XRCpuImage_FormatSupported_m15D60F33E5EB00039BA41B9C61AE114C1AA6B40A,
	XRCpuImage_GetPlane_m0C2A7BE6FE964FCF5A82273AA9DA6C135648721B,
	XRCpuImage_GetConvertedDataSize_mC7AF0A096D1FF758D3E086D3D43F778E9257D4BE,
	XRCpuImage_GetConvertedDataSize_m1A292AE01390513BEA935CC4C19A0F8FD52341DE,
	XRCpuImage_Convert_m04EB3992B85AEB87D03C5626EFD0A9C0158AC9CB,
	XRCpuImage_Convert_mFE71425F0E4FD4ADB839551590FE9728BA037EE1,
	XRCpuImage_ConvertAsync_mECB96371D5F7C49A4E995B285F9FC02FE4109814,
	XRCpuImage_ConvertAsync_m881B8A7A185F454AB34151D858CA0AE8A54F2949,
	XRCpuImage_OnAsyncConversionComplete_mDC3A0C88A34909C9D08E4BE7E94C8E27E2BB3D3C,
	XRCpuImage_ValidateNativeHandleAndThrow_mA1A3B64DF91A003BDB013FC04111945F03853395,
	XRCpuImage_ValidateConversionParamsAndThrow_m39B351E15FD65E6969933F0EBE42CA63DD090E72,
	XRCpuImage_Dispose_m80B8CA56700DD5EB8A5613AA42F6F389D86A746B,
	XRCpuImage_GetHashCode_m4C976024EE9CFFDBE53682B8307FD0819F42E31C,
	XRCpuImage_Equals_mE00DCB100FC7743E62959883CBAF479ADEDCBAC3,
	XRCpuImage_Equals_m0BD02471E5A85EFF5F078CE0ACEDD4F969B66AB2,
	XRCpuImage_op_Equality_m155699D9F3137F2779D9746546CFDC64418CC85A,
	XRCpuImage_op_Inequality_m40C7681B3CD49484468D82E6990D1971E7F5B461,
	XRCpuImage_ToString_mA4BF5B6A1D341098584B0EC3E51D324092AE98ED,
	XRCpuImage__cctor_m5EC261DAD69867C3BF19DCD92E72124ECBBC5E38,
	Api_TryGetPlane_m6504F578CC422286093D4F66162407E8456990B1,
	Api_TryGetConvertedDataSize_m33799E987ADF63F4B2C430ACECA42A1F3349E375,
	Api_TryConvert_m2E4CE2B72ABB72C6D33BDB4FF5EB41AA8C9FDA1F,
	Api_ConvertAsync_m0F80B09CA4E70E682AE5863099ED353169B71BA6,
	Api_NativeHandleValid_mDED64890874D05C8BADBDC831744F26712DE6116,
	Api_TryGetAsyncRequestData_m6E9232C49B5D920C6BC3A8DA1CE050E4FC603B9E,
	Api_ConvertAsync_m125EC0A9F2ED20CDBFC87B8CD98F07F1FDF424FE,
	Api_DisposeImage_m5FD9A543A7EE8CBD5C7B19FE660143B667959FC8,
	Api_DisposeAsyncRequest_mE3984202489CBC2A185CB634D3F92F67A33E5E96,
	Api_GetAsyncRequestStatus_m505F65B4B84651CBC7CF070AA11980797D269C83,
	Api_FormatSupported_m78D64CFE730EB2DFF3B70E6564A6A3230C5A5CA0,
	Api__ctor_mFD77CE4DCDE143DFBF11744359F52058E9550F9D,
	OnImageRequestCompleteDelegate__ctor_m75183C0CE806605022429FD507627EE3EEF6D14B,
	OnImageRequestCompleteDelegate_Invoke_mB43B2DD8D5CC0863FF2A8A871D27A1AA2A3E5B37,
	OnImageRequestCompleteDelegate_BeginInvoke_mC22A9776507508A3807BD3A17FEF6AC9F01F77B8,
	OnImageRequestCompleteDelegate_EndInvoke_m744BF9BFE054522989EFE70441D818F76CEAA6BD,
	AsyncConversion_get_conversionParams_m467C52D7FD3E87614FA34FCB630EDD89289F12B0,
	AsyncConversion_set_conversionParams_m5144DBB33F2D0003BF4E2ED884B9D8AA4EE89071,
	AsyncConversion_get_status_mB3A873407AF4E202A39758599DCEE52BEC196E2A,
	AsyncConversion__ctor_mEEE052FCAD2BF2E9FBF78C829C0A11A6D5CD5ABD,
	NULL,
	AsyncConversion_Dispose_m81B54378570A2C9C1009618A0380E5C204DD2AC6,
	AsyncConversion_GetHashCode_mF14F2C21ADCA19EB7300B2F07B59B2758CE0195D,
	AsyncConversion_Equals_m0D926BE22C3B6333F413DFB6E051021428C6D3D5,
	AsyncConversion_Equals_m84273ECB152DB76244DFD3BF9BC2AB648682F4A0,
	AsyncConversion_op_Equality_m372106330CD6036C3438EF5A04935D667BFA4E54,
	AsyncConversion_op_Inequality_m1D69E9441AA22C440D64B666DC23FE5D9B897B54,
	AsyncConversion_ToString_m59F4615A570F43A086B43067B37CCF382BEE5225,
	ConversionParams_get_inputRect_m59986429062905012283B892A6EE2DAD88A810FC,
	ConversionParams_set_inputRect_m7965864AED4C5176D58F3766D6BBB35DFF7BC903,
	ConversionParams_get_outputDimensions_m6295F96DCE9B406AB6D79E8CD86A6FF388CF5035,
	ConversionParams_set_outputDimensions_m97EC09EE536EA456A18894311BF75AC9D5A90A3B,
	ConversionParams_get_outputFormat_m8CD52ADADE8FFE505A90E02D9BD6C7D9EE1C8715,
	ConversionParams_set_outputFormat_mA82EA0ECC19D14AECBA318B9B485D08CFB46A1F4,
	ConversionParams_get_transformation_m46ADA14AEDC98630828D5DCE19F1905233627CE7,
	ConversionParams_set_transformation_mBCE73B14CCE8A31A258C6B8F6104446A2D495A0F,
	ConversionParams__ctor_m2EA9FC7BD411FA61269B314ACD03174F5BB96273,
	ConversionParams_GetHashCode_m15B2EFDD22B43B4201646E49BCB9155F67D5A12A,
	ConversionParams_Equals_mD0A055A44755C75EFF2B09B2FD7C9C50D057020B,
	ConversionParams_Equals_mDC60F1518FE83109D22DB3A1606C82930B16356D,
	ConversionParams_op_Equality_m0A9298A5F5C511132C2CDE6DF114EE57868FA2E3,
	ConversionParams_op_Inequality_m1E628FB6BF9975EE888EF0141A663C26C448A6F5,
	ConversionParams_ToString_mB11C93DA6E5D57F694BC5D64E5567958A647557C,
	Plane_get_rowStride_m5461CF97009BA5CB09931F85D9C4E11BB298E01F,
	Plane_set_rowStride_m03DE76183744D782EAB661F50D8191DA9CB34A31,
	Plane_get_pixelStride_m78990A3DB8530B302D4B138E92BFEFF6F6F8D5E0,
	Plane_set_pixelStride_m26005BF1B5C79A0B816D37C53CF5A34BA3499CFC,
	Plane_get_data_m8A88D9DDDAB3081E788B3DCF7DE314D2E672B15D,
	Plane_set_data_mFDEC268CEEE5FEBFC54FE823BEC6B3DCC4DB182E,
	Plane__ctor_m9563F685C69A49502C62A7EA4F1F64FCC392A485,
	Plane_GetHashCode_m81E44303AC89B3792D4238BEFF767D459D72FDD1,
	Plane_Equals_m05599C5BA1316FF0667B8D3752DC2464E559A24E,
	Plane_Equals_mD97496D640121AA88AF730F5DDE9F1ED6582842A,
	Plane_op_Equality_mE9A7A4F2F95E182708D46301CA610E6802A2D0A8,
	Plane_op_Inequality_mBDAE6B00116CC3DD27F637345A54730E25B57325,
	Plane_ToString_m0844EFBBF3A11852B21C58FCBC543A554E838EE2,
	Cinfo_get_dataPtr_m0865701DF77079918906809E61CCF8C080120AB1,
	Cinfo_get_dataLength_mF704FE891CD1628CF48C8434DF1CD5C461A7EE86,
	Cinfo_get_rowStride_m3CB25349C2380F5FC9022EB25A1FCF95C1498513,
	Cinfo_get_pixelStride_m5A3C2E9C12F194F7237EF96FC12E319928E14A6E,
	Cinfo__ctor_mF1859C21D692CA1783BF64CDD8C45BB1984C427F,
	Cinfo_Equals_m8F4B3A7591D02605076B062B37312733D533D3AA,
	Cinfo_Equals_mA464FE0F8B24D6AACEE40E5A290572D4ABBE1333,
	Cinfo_op_Equality_m1FA11367A4662FF019702F76F0AF2736862D8EAF,
	Cinfo_op_Inequality_m605AE7A7205120773F7D94208019138D4CD11858,
	Cinfo_GetHashCode_mDD348F8626D5B49ED6EC593263AFD20A21329F08,
	Cinfo_ToString_m6CC828632F333B765128A31434D3DFF040F90754,
	Cinfo_get_nativeHandle_m63F2835811F1DCFF2EE1AEBF8A8F7A1ADA1FD7E0,
	Cinfo_get_dimensions_m7B777F060E825839302EA722B35E1BBB4E402D2A,
	Cinfo_get_planeCount_m5D077F0399217E11C6A11378F5D08D86CC5CEA7F,
	Cinfo_get_timestamp_mFFA3FA7E91717B748F6159B7E78FBFE2290F4E97,
	Cinfo_get_format_m0F7BD9189DB7D30D44882FF3F53EF2FFBD05C7DE,
	Cinfo__ctor_mB5890536579096BE14554ED96E868003C9A2CC5C,
	Cinfo_Equals_mEEFFAFF7E0FE0F0445AF0F96F66D5D68DF1BC3E1,
	Cinfo_Equals_mBA9B7A52398AB07042615E27D6AC30542F9EF124,
	Cinfo_op_Equality_mD8D46F2B59A546B6EF434C09F523E5A4AAFB2323,
	Cinfo_op_Inequality_m67A4C5321E6B7BB1134746D68E682CD4FE379426,
	Cinfo_GetHashCode_m26AA585D94FCF87B154728D81E40295716B37B03,
	Cinfo_ToString_m47CB9E0B83E1E5C5CA1517D982A5E6FF4A9F05A3,
	XRCpuImageFormatExtensions_AsTextureFormat_mEDB46C7DB6DE6C62926ECD7FE9EBAE000A4F7E18,
	XRCpuImageFormatExtensions_ToXRCpuImageFormat_mFF86DA414D59D76AEB1D7D90BE52614F6F60F6CD,
	XREnvironmentProbe_get_defaultValue_m50BD745C4928AE4328C53906D672D5E2F4B37B85,
	XREnvironmentProbe__ctor_m756BDCC73762A50BDAF24FD4F430D8F8EA18869D,
	XREnvironmentProbe_get_trackableId_m7B20AFD8D153397E7270F72C81B32043DA83C57F,
	XREnvironmentProbe_set_trackableId_mCAD11E54A600B26FDC6D546A15F5E13030605EE4,
	XREnvironmentProbe_get_scale_m7C53FA5C36BD5616CCF2EDC543C260FD381BCB64,
	XREnvironmentProbe_set_scale_m230AA2EF5AE396A8E5A43FE809BEEF811CE68302,
	XREnvironmentProbe_get_pose_m56C2FCB790DC220FAE0339EFC82055360984CAF0,
	XREnvironmentProbe_set_pose_m1A1776C7D4A99F29708883F081A54936BC46A4B0,
	XREnvironmentProbe_get_size_m92A310E405DC33AFF0968D0B7C17BDB8D039A1B0,
	XREnvironmentProbe_set_size_m191D7253226516A2BBC83D0DD28A154FAD2F3C33,
	XREnvironmentProbe_get_textureDescriptor_mD514443491B53FCBC49AD477CC5C7C6084543FAD,
	XREnvironmentProbe_set_textureDescriptor_mAE07E8E52FD3D564E1366EC75E0B1EB80A1A43B0,
	XREnvironmentProbe_get_trackingState_m4051D90D37D33EC33534368B64E5C85EA1888C83,
	XREnvironmentProbe_set_trackingState_m8AC9F0BB01B0B26935D09B5A723680E658A3A196,
	XREnvironmentProbe_get_nativePtr_m0C6C620B2D3C20FBE8AEE478EBEA0006E8E7FB40,
	XREnvironmentProbe_set_nativePtr_m75CED6350AB93167C23B4E1A837879BCEB6A7AAC,
	XREnvironmentProbe_Equals_m891BD688A67E6AF40E4DE164936AFC6D59762AF0,
	XREnvironmentProbe_Equals_m76FC4B88F469A7E33C17E4F9A59DBEBDF1A66745,
	XREnvironmentProbe_op_Equality_mD7BD953290CF2F6877E43A59034537B7E51C1621,
	XREnvironmentProbe_op_Inequality_m6439F11CC4F98DF61A14DEDAE538F7FA8A243E21,
	XREnvironmentProbe_GetHashCode_mC8C8046B5523D71CADA65C3D38232925243CEA86,
	XREnvironmentProbe_ToString_mBD160B7DBD096BB94201C93B1821FF73728C3E4F,
	XREnvironmentProbe_ToString_mFB69B6A7B36CD0B02B4283AEFF6CEFAA72EE8DB2,
	XREnvironmentProbe__cctor_m96971D1EF22003241CE8C7D3859CDB667DC74057,
	XREnvironmentProbeSubsystem__ctor_m5F2239415E12E56B1546F56C19E071ED2324ABF5,
	XREnvironmentProbeSubsystem_get_automaticPlacementRequested_m0FADAEC1FEB2F7FD60A441AEB0B61841055CDE06,
	XREnvironmentProbeSubsystem_set_automaticPlacementRequested_m5A612C8C22C3130F4C3684C78B20BE53871916D8,
	XREnvironmentProbeSubsystem_get_automaticPlacementEnabled_m63BFBF6DD86556B87FE65173F947ED6EABD02668,
	XREnvironmentProbeSubsystem_get_environmentTextureHDRRequested_mDFB6AA21C54DED1462E883E484B280F9D1725E04,
	XREnvironmentProbeSubsystem_set_environmentTextureHDRRequested_m09413D30EC3C646DC22F8979EF34446034500048,
	XREnvironmentProbeSubsystem_get_environmentTextureHDREnabled_m3784A8D3027A8EFFAD34A7FC85DAFD31576BCDDF,
	XREnvironmentProbeSubsystem_GetChanges_m7507B8AA278398E93F42DFA8CEC09240508F0C6B,
	XREnvironmentProbeSubsystem_TryAddEnvironmentProbe_m177BFC3E369E1F422BF9A0393324220BD60243D4,
	XREnvironmentProbeSubsystem_RemoveEnvironmentProbe_mE940508A973468A3EF5916E3AEBE4236A8FC71B8,
	XREnvironmentProbeSubsystem_Register_m0422985B0B805F5AB3B59B8AA91DBD229A06D74C,
	Provider_get_automaticPlacementRequested_m31751920D7587F49A5B53180EB9D43A4F11EC6DD,
	Provider_set_automaticPlacementRequested_m2309B486A1845A040EC6BC5E821B48091A736B52,
	Provider_get_automaticPlacementEnabled_m51ACF2B2A9C8AB6E9F16FCEA6D6A44B108EA2F13,
	Provider_get_environmentTextureHDRRequested_m93AE2DE32EA5E8C880137AC7B10C51F9AABFECFE,
	Provider_set_environmentTextureHDRRequested_m323DA868046B0E7806EB611A44C3CAF4996CD936,
	Provider_get_environmentTextureHDREnabled_m53742D23697A648A200DFD6CF4A3288CBD2E4FD2,
	Provider_TryAddEnvironmentProbe_mA6CAC6C36EB0D500270F11E086CAECFC62B4F805,
	Provider_RemoveEnvironmentProbe_m653E5DEA379458A4CD9793EF60F85EC36E93AFED,
	NULL,
	Provider__ctor_m1439531E68FE8334150A68D79E0E1DF9F55DB64B,
	XREnvironmentProbeSubsystemCinfo_get_id_m590E5DEBA8C344FFEA51F351F99DEFFD703DC57F,
	XREnvironmentProbeSubsystemCinfo_set_id_m0157B51BEBA22C10D835283A2E342811FB3904E0,
	XREnvironmentProbeSubsystemCinfo_get_providerType_mBC6F352EF89BED518336375D44A5D9CADDE464C1,
	XREnvironmentProbeSubsystemCinfo_set_providerType_m053C0551B3059F11B606708ED8C6B3DA9C4CF2F2,
	XREnvironmentProbeSubsystemCinfo_get_subsystemTypeOverride_m5A1A5D3ADB4E4304029B3F1688CEBFAB4E4C8F7E,
	XREnvironmentProbeSubsystemCinfo_set_subsystemTypeOverride_m89507B15871CD2E8F093EF084176BC98CAD3D1E4,
	XREnvironmentProbeSubsystemCinfo_get_implementationType_m6C1B50EAB87465FE798443F1C2803F1A437EEC3E,
	XREnvironmentProbeSubsystemCinfo_set_implementationType_m09326FDF5E9024E0AA37F89388F8BDD4AB40E600,
	XREnvironmentProbeSubsystemCinfo_get_supportsManualPlacement_mFA2560E13D32A8228105D6F205E109DEDE0BFB37,
	XREnvironmentProbeSubsystemCinfo_set_supportsManualPlacement_mE948D3D31063A4015F18BABCD4475E2CEDD00E2C,
	XREnvironmentProbeSubsystemCinfo_get_supportsRemovalOfManual_mE1E3D58FB734841B3B1E885592C84A2748953847,
	XREnvironmentProbeSubsystemCinfo_set_supportsRemovalOfManual_m9784D47E8903EC1BEFA722994586FF633BB09327,
	XREnvironmentProbeSubsystemCinfo_get_supportsAutomaticPlacement_m01E383AF1BDC43E0755B0C99BF838698B2FAD67C,
	XREnvironmentProbeSubsystemCinfo_set_supportsAutomaticPlacement_mD728F648E5A75DC074D9718432F07D0D460A289D,
	XREnvironmentProbeSubsystemCinfo_get_supportsRemovalOfAutomatic_m5C05042091803872B2B5FE45C791A728F92B5A1A,
	XREnvironmentProbeSubsystemCinfo_set_supportsRemovalOfAutomatic_mBD5F44750877ADEFD8C6207F0B30D0FE15B70526,
	XREnvironmentProbeSubsystemCinfo_get_supportsEnvironmentTexture_m7B872B5FE1B9517D9626E269E09B7C4C16E06A06,
	XREnvironmentProbeSubsystemCinfo_set_supportsEnvironmentTexture_m724298B12B4DDEFC4C1274A551E04299A040374D,
	XREnvironmentProbeSubsystemCinfo_get_supportsEnvironmentTextureHDR_m7AC20590F176681AD486ACC8C9A0189209B1C35E,
	XREnvironmentProbeSubsystemCinfo_set_supportsEnvironmentTextureHDR_mEED10741684E8045104833D1A58A1A02612FBE4B,
	XREnvironmentProbeSubsystemCinfo_Equals_m4297DA57D68A4D83010A2D370092CB925B89CEA0,
	XREnvironmentProbeSubsystemCinfo_Equals_m8AE52E39A349B7A5E21C624AE22C4A1078E8C327,
	XREnvironmentProbeSubsystemCinfo_op_Equality_m77A5296B22E757B33E660AF0416911EFE7835224,
	XREnvironmentProbeSubsystemCinfo_op_Inequality_m4CD5804188D309BC1C7949E12D649B121CF28437,
	XREnvironmentProbeSubsystemCinfo_GetHashCode_m66A5517BE16EE35C50CBD336B9B2928760E07121,
	XREnvironmentProbeSubsystemDescriptor__ctor_m2E853F070162B34D7425CE9C22A076DF327D5820,
	XREnvironmentProbeSubsystemDescriptor_get_supportsManualPlacement_m129575B9630FCCF68A6B6074DA7DB8B1722BB152,
	XREnvironmentProbeSubsystemDescriptor_set_supportsManualPlacement_mD57435B5702FA32561338DB692DCC27DBD3230D9,
	XREnvironmentProbeSubsystemDescriptor_get_supportsRemovalOfManual_m1C15402FEC435594A1E9DC9CD0AF63628734E190,
	XREnvironmentProbeSubsystemDescriptor_set_supportsRemovalOfManual_m193D875922BA821AC499A40B594FB6E64258EAC7,
	XREnvironmentProbeSubsystemDescriptor_get_supportsAutomaticPlacement_m88925DCB0DBEA8BFD4A53848FDA8B54652A0FBA0,
	XREnvironmentProbeSubsystemDescriptor_set_supportsAutomaticPlacement_mEFAF3BBDC48427B01157B77F87ED6F31A7007E52,
	XREnvironmentProbeSubsystemDescriptor_get_supportsRemovalOfAutomatic_m1F8BC49551168C85ECE1489B3ECD80C84745A737,
	XREnvironmentProbeSubsystemDescriptor_set_supportsRemovalOfAutomatic_mAF9042500D67C183A50991C5BE60B9B7D1FC88D4,
	XREnvironmentProbeSubsystemDescriptor_get_supportsEnvironmentTexture_m410DFF88B4D7C596792083C2EB75CB97E0E6E673,
	XREnvironmentProbeSubsystemDescriptor_set_supportsEnvironmentTexture_m23B7A468AFC6959FB83913D7DDE2F85AF2D9EF5D,
	XREnvironmentProbeSubsystemDescriptor_get_supportsEnvironmentTextureHDR_m61D26031FAC8D6E14EFE637F4E6F86D036069AF8,
	XREnvironmentProbeSubsystemDescriptor_set_supportsEnvironmentTextureHDR_m8DAD6E95E4BC1FCFC780273EE59D32B49299A23E,
	XREnvironmentProbeSubsystemDescriptor_Create_m45D62D5B4B259C964A8F836CD4E33DF492949F1B,
	XRFace_get_defaultValue_m3C57FCE26ABDB16951CB5F35758D33DEFD545535,
	XRFace_get_trackableId_m9FC29FB643FFBAB989AB8179F57CDB52D14737B3,
	XRFace_get_pose_m1625DED173751F25873C4BB6650238A195CD04EE,
	XRFace_get_trackingState_m7D5C3002DCB9FC01F7C1BE66D3F1092281E847FB,
	XRFace_get_nativePtr_mCE3681420B25EA0AE4B5FA1687310DF7D49C0899,
	XRFace_get_leftEyePose_m5406913BE94DA663C80EA8C555EEC1439C0ADAE3,
	XRFace_get_rightEyePose_m276AD0EBDCD8B62AAAAA2A33920E2FF1415E769D,
	XRFace_get_fixationPoint_m2628733EA6C1FEEAC047347DBA08A602B7C88429,
	XRFace_Equals_m6E2D8C6F4F57BB604AA31EEEAEB06BB64EBFC299,
	XRFace_GetHashCode_mC17A1126F3ADDDB95C12C3E908353704DCCB14D0,
	XRFace_op_Equality_m5464082D6B0C53CD94DCD5606EB9CC3DED0CB7BD,
	XRFace_op_Inequality_m8C052FECD0C719C494CF4BDF4DDA2DE76ACCDCCB,
	XRFace_Equals_mC82B627F3AA8A164D6AE1A999A5BCB55DD2E2C51,
	XRFace__cctor_mB74D8288B8CDC23DC133DCB1D7D248CB5D6135BC,
	XRFaceMesh_Resize_mD9373FB138642F70F4068345A64B49B64B1A3830,
	XRFaceMesh_get_vertices_m8B133063FC373FD34B8ECBEE696B3462DC65277E,
	XRFaceMesh_get_normals_m37A411662D1051785AFC6807E3BBEC0E2B3BB61B,
	XRFaceMesh_get_indices_m2658965B1B99DF1CF00154D791B580AE71CB136D,
	XRFaceMesh_get_uvs_m71BF16345717D8B5D8F41C571A8D3152337E0A28,
	XRFaceMesh_Dispose_m02478E536865BA52126039CCAE5B62E5DE58AECF,
	XRFaceMesh_GetHashCode_mE6F88C5914358332601C00E22FE0A34A137EC982,
	XRFaceMesh_Equals_mDE9CF3DB2761831C9E9A72B6C2C3EB1D6D155D6F,
	XRFaceMesh_ToString_mEA1FF45022C6E287675E27526448295468B2884B,
	XRFaceMesh_Equals_m56870D4CC9E4BC2D1839D5DEFA77A062C29C97A4,
	XRFaceMesh_op_Equality_m6570A0752401DB202626ECD242755553E7E8D830,
	XRFaceMesh_op_Inequality_m00FCFC7717BCA976025B49B6CBFF7FD546DBE18A,
	NULL,
	XRFaceSubsystem__ctor_m31F1B0D8DDB368C87E5691F6C114E926155ED9D5,
	XRFaceSubsystem_get_requestedMaximumFaceCount_mC3A28A767AD50117492E460C3BC9D85A6F83FD2E,
	XRFaceSubsystem_set_requestedMaximumFaceCount_m0A4830582BC9F31DC033223A5BDA8621C3E4A191,
	XRFaceSubsystem_get_currentMaximumFaceCount_m3404BDB08E8809A87E742DB785078C1E319009C3,
	XRFaceSubsystem_get_supportedFaceCount_mF679BF9CF3EAFC512E8381E433757848DA3C63D4,
	XRFaceSubsystem_GetChanges_mE2721F6C40D20AC9D39428E6D9C5A359C9D841A2,
	XRFaceSubsystem_GetFaceMesh_mC4137FDF495ED6737AC1B49DAD1BE6D9E6C514AB,
	Provider_GetFaceMesh_m27AC65B0454F7C6464E634A6DC3BEC57C2530844,
	NULL,
	Provider_get_supportedFaceCount_m1F62AB853CC71F49711F106C855467DBFF39DD68,
	Provider_get_requestedMaximumFaceCount_mE04DBAD7160414261334D9474D09AF8628A4BDBB,
	Provider_set_requestedMaximumFaceCount_m333E04847D0544E2DAB8A60BC48A7E663E2169DE,
	Provider_get_currentMaximumFaceCount_m3BAF54FE1CD288F6D7D18BDA5A4966A9C74DE3AC,
	Provider__ctor_m41CCFF923C16D17CC1297FE5FF752330D8CD1BBD,
	FaceSubsystemParams_get_id_m31A6CBE37287374259BEF3B328FB00B1ED871D6A,
	FaceSubsystemParams_set_id_m963078C97310E91B43D52A4A682D6A4DC0D9A40A,
	FaceSubsystemParams_get_providerType_mAC8A9B7FF24C751EF95C8B06D71746709FBAED0F,
	FaceSubsystemParams_set_providerType_m96D22AB9770398D6C371502A95F86A6F45E17C5D,
	FaceSubsystemParams_get_subsystemTypeOverride_m15AFA4BED668BB995597E91557337E9B30994467,
	FaceSubsystemParams_set_subsystemTypeOverride_m113E1A7A0F27FE1A56FF92504E6BDC2FD287846E,
	FaceSubsystemParams_get_subsystemImplementationType_mB85CD541326A5FBDE4378093FA3735066DBDEFED,
	FaceSubsystemParams_set_subsystemImplementationType_m3A131EEBF975AA6ADF3A39ABC3ED0980C8D7E024,
	FaceSubsystemParams_get_supportsFacePose_m68CEF716DBA9D0D75EDA10A87A64A5827C78CA59,
	FaceSubsystemParams_set_supportsFacePose_mCBD1B88CB04F00F212C4B5B8A11821DDCC5C8353,
	FaceSubsystemParams_get_supportsFaceMeshVerticesAndIndices_mB1D5078180B153FE76FCA509905D0A79D575689B,
	FaceSubsystemParams_set_supportsFaceMeshVerticesAndIndices_m8783686211AB86E509EE6538E3CFC9A6CF82BF9D,
	FaceSubsystemParams_get_supportsFaceMeshUVs_m3734F66B4ADBD0A39FBCAEB927A4409023D6164A,
	FaceSubsystemParams_set_supportsFaceMeshUVs_m151C1B6BE0BA832416557E90493F04DCF97E6809,
	FaceSubsystemParams_get_supportsFaceMeshNormals_m194410916CDF0F71EBF2AD9C419155D68F9969B0,
	FaceSubsystemParams_set_supportsFaceMeshNormals_m0664A1AD48A3B41681B9680F7773D1849FA1450A,
	FaceSubsystemParams_get_supportsEyeTracking_mC254E7AD7AD126742CC7CFDFDB944CD2AC0C0FBD,
	FaceSubsystemParams_set_supportsEyeTracking_mFEB7DACFE5B6DA3EB37AED2C3428A1CEE0E0DBDE,
	FaceSubsystemParams_Equals_mD22C227C324E8205B6ACA8F6C625C62F58224D3A,
	FaceSubsystemParams_Equals_m4AA3EAA9779EDA380AD89EA9BA7E1865B0BE6017,
	FaceSubsystemParams_GetHashCode_m4C9D85CB7B820FC7E57A5B214D85EDDBD6D5499D,
	FaceSubsystemParams_op_Equality_m13414161D1A8AA42F9363CFCA8AB45F5A1C6E0CE,
	FaceSubsystemParams_op_Inequality_m826226EB56D41F48F3A65AB7E15654497B33B6DA,
	XRFaceSubsystemDescriptor__ctor_m94769C800013224A0EB1531F51F1E6155853517D,
	XRFaceSubsystemDescriptor_get_supportsFacePose_mC2C1FCA19B4B87A6A52DC4D56ED8F53668DA1C67,
	XRFaceSubsystemDescriptor_get_supportsFaceMeshVerticesAndIndices_mBC51769DDD38F8B2407F919BD7B7ED5A3E1D492A,
	XRFaceSubsystemDescriptor_get_supportsFaceMeshUVs_m8E05155346A7295AA7D53E143032198A42E1CE61,
	XRFaceSubsystemDescriptor_get_supportsFaceMeshNormals_mD4E28B10A6F8C9041AF63A9233314610ED3E6A28,
	XRFaceSubsystemDescriptor_get_supportsEyeTracking_mF482F5E345E3A9435DA290AC6DD1D28470602039,
	XRFaceSubsystemDescriptor_Create_m0B282B6C4D3AD46F02C2D5AA2900949E4C5D272D,
	GuidUtil_Compose_m58AA1AA0AF27A23B64937C72023C6F72D4C8DD40,
	GuidUtil_Decompose_m71DD3DADA4E88DB6C02C2A143DF01870F08BA910,
	HashCodeUtil_Combine_m98169BF154323DEFC91DB146CDD3CE7550B6AD36,
	HashCodeUtil_ReferenceHash_m2DB64625F0287C798373FE7D45AA20B43AC3EDA5,
	HashCodeUtil_Combine_mF01D6438A25333A5530D4658D11A9F0BC988011A,
	HashCodeUtil_Combine_m6E8EC4EC047F80C102AEE35681D328C78A3DCE55,
	HashCodeUtil_Combine_m0CA248D97B33A8A3DD5AD8456D090619CCD63FFA,
	HashCodeUtil_Combine_mAD5A58AE27677DC59EC7E9AE41FAF43AF414C506,
	HashCodeUtil_Combine_m3B3273BD5CFEFD2D09635E1B69281B0ECD9819FB,
	HashCodeUtil_Combine_mCB03954826AD9D732D76C80D546E33DF5FC1C7A8,
	HashCodeUtil_Combine_mBA82260101D33A951BAD66953407B18C697104B7,
	HashCodeUtil_Combine_m0646726224C8EB6504327A535A238220D1DF2454,
	HelpURLAttribute__ctor_mA0360803CCDDE8CAD65704493A1CC93513AE5421,
	HelpURLAttribute__ctor_mCB9A72AA2EE39BF4C3741AE604073A8F513449CC,
	XRHumanBody_get_trackableId_m7CC5B8BB5179303ED1424ACDC46FBFA16C30B2FD,
	XRHumanBody_set_trackableId_mCE04EA8307BC1B6670AE915575E5297103620E87,
	XRHumanBody_get_pose_mE154F73E48997BDB6828FE12D7116E93E4D4BBCF,
	XRHumanBody_set_pose_m036F9C1AB8DA4836D85CF15256C0FF6C83E8B712,
	XRHumanBody_get_estimatedHeightScaleFactor_m455E9FD1B289BA71C5FEE2A67D72EEE10727246B,
	XRHumanBody_set_estimatedHeightScaleFactor_m857381C931D5F597AE28A4BD96E8225DE2250693,
	XRHumanBody_get_trackingState_mE53C1B287B5BD8E021FCAC0E4550C0D551C0F79A,
	XRHumanBody_set_trackingState_m470B4AD877F377DB7B85F736D80EC5FB2AD39187,
	XRHumanBody_get_nativePtr_mBD7FABEADEC1EA20A472626430176AC6681C50E2,
	XRHumanBody_set_nativePtr_m140ED78793BB10C9126C5539804291CE69F00381,
	XRHumanBody_get_defaultValue_mA19E33981C9E5F90F388C4660197FD156FB90037,
	XRHumanBody_Equals_mED06668B3B016A173D38A33D8D4CC24691A90CF1,
	XRHumanBody_Equals_mC6FA42C2E907195A60B2CB8A6230462762C6B003,
	XRHumanBody_op_Equality_mEC866169B1FEABD6F33A862D063F8C7D895017DF,
	XRHumanBody_op_Inequality_mFCE9FB29ACAD78B15AE6E742DB7780490B4F0B64,
	XRHumanBody_GetHashCode_m44E8812541CCF52BB596A789A350C77CF32B8B06,
	XRHumanBody__cctor_m1A4FC9E83BBA384D4CA8500953B02717C6952507,
	XRHumanBodyJoint_get_index_m3AD361AAD68A37A0EC5490A716FA0F0D5AC6D386,
	XRHumanBodyJoint_get_parentIndex_m4DA1B768A618B7AE553D67CC82F6B2545B8F2FBA,
	XRHumanBodyJoint_get_localScale_m9A7DDC16FAEB5CFF269393403A92C375CE8387B6,
	XRHumanBodyJoint_get_localPose_m5330B565E89F7276A497ED8E94DAA288A352FDD2,
	XRHumanBodyJoint_get_anchorScale_m01EC3D9B0020D0BFBCDF9ADD26149F6D9E6D87C0,
	XRHumanBodyJoint_get_anchorPose_mC409FE9C6F4CFD14C156977B59096FA4340EE61E,
	XRHumanBodyJoint_get_tracked_mC8DA59028CFA50982FD6E319736F0C93EA097899,
	XRHumanBodyJoint__ctor_mF74F3B39077D10EED35A34F3DBF7C217CA1D8753,
	XRHumanBodyJoint_Equals_m7DFBAA24024C04E8A38A962862BA744F9A515AE5,
	XRHumanBodyJoint_Equals_m59EDC2A704F17057288266550340CCB7FE041680,
	XRHumanBodyJoint_op_Equality_m0FD5727DC1875198D103673CDDDB6DE07024178B,
	XRHumanBodyJoint_op_Inequality_mCE4D62DAF1C3EC1B4C76EEB0A9603FB7C9988E17,
	XRHumanBodyJoint_GetHashCode_mC37463DF2B57FF4BA22AD008F91AF061E30575EF,
	XRHumanBodyJoint_ToString_mE909C8943965A053938EFE3B7DC365673632F899,
	XRHumanBodyJoint_ToString_m814AEF251F6D72B22EE7DE358A422C638FF6D089,
	XRHumanBodyPose2DJoint_get_index_m63E7D2C639973B20B8721BD412441AB87F32C626,
	XRHumanBodyPose2DJoint_get_parentIndex_m74403971AC59748A0FC11187E701F8EF835A97F5,
	XRHumanBodyPose2DJoint_get_position_m4A5CE8370D7E1DEB0B2CC27487182A0776AAC8E8,
	XRHumanBodyPose2DJoint_get_tracked_mCC6E1D56159DA4501534E47AB27D4EA05AA3FCF8,
	XRHumanBodyPose2DJoint__ctor_m2BE9BC97DB9FA4C84623810D624196524B9F488A,
	XRHumanBodyPose2DJoint_Equals_m7023E676891F764891104A57CD41D77BE31360F4,
	XRHumanBodyPose2DJoint_Equals_mE8F361B51A58F789BC559B550AA5CA08691A88E1,
	XRHumanBodyPose2DJoint_op_Equality_mD209C91BE915D0CE621211339876F0488A4A994A,
	XRHumanBodyPose2DJoint_op_Inequality_mD8781E62E15A4ACDE6D17FF63720ADAE7D9E9EC1,
	XRHumanBodyPose2DJoint_GetHashCode_m9CA16CABF11BB7137978E5D8EE83FACBF7D1622F,
	XRHumanBodyPose2DJoint_ToString_m487FA2ED54B8FC493572D75FAFC68BA40B4FEFC4,
	XRHumanBodyPose2DJoint_ToString_m7BBC96E86E94C35680E22AB7CED6215407E48CBB,
	XRHumanBodySubsystem_get_pose2DRequested_m0C32BCD5FFE0A8B0E73F3FCF14A2A753591E210F,
	XRHumanBodySubsystem_set_pose2DRequested_m460913D673A796B4DD88615ABD5B38F4E27F826F,
	XRHumanBodySubsystem_get_pose2DEnabled_mD5ABCA377E364969A38D6F79006E66E1C1B66DB2,
	XRHumanBodySubsystem_get_pose3DRequested_m3521DFF8C79CEDCCE1D0B065F5B11B3AFA0C8DB8,
	XRHumanBodySubsystem_set_pose3DRequested_m3176AB51B24C37E5FB496D9392B40524838063B3,
	XRHumanBodySubsystem_get_pose3DEnabled_m1BA00660787FE2183616989A231AFF6E9AF37E90,
	XRHumanBodySubsystem_get_pose3DScaleEstimationRequested_mB4BE08E33AB7699B610592422BDB2F8DD8F576E3,
	XRHumanBodySubsystem_set_pose3DScaleEstimationRequested_mA9AD2D8A5C6B099CB883D92BC7631CA2F6DE83C4,
	XRHumanBodySubsystem_get_pose3DScaleEstimationEnabled_m245F93DDD5D738E8E3550FBCA43A76A2DB8B0F81,
	XRHumanBodySubsystem__ctor_mE0AE161D7A1E01F8580E4E8D243FE988A9392BA0,
	XRHumanBodySubsystem_GetChanges_m7B7DB503B66CFD7F266DEEA73EB7CB94C1AFE4FE,
	XRHumanBodySubsystem_GetSkeleton_m65F0C477C539F78BFD68A44C28583DF418A78335,
	XRHumanBodySubsystem_GetHumanBodyPose2DJoints_m4547D3DC143DC3CC014F4BEE18632AC09F1108E5,
	XRHumanBodySubsystem_Register_m7319B25B82CA84BF8F9578ED96263AF51700A9EF,
	Provider_get_pose2DRequested_mA429171D14BB5AC1A71E6097F571AC6C91AE3CCF,
	Provider_set_pose2DRequested_m8C377DC5C76047E8C41634B62F0A5E8E7B5E7C0E,
	Provider_get_pose2DEnabled_mD4930EED264FBBF745606EADF3DED61BCB93312C,
	Provider_get_pose3DRequested_m0BC6D27B9E26FCBA148B0C590A81021501F9BEA7,
	Provider_set_pose3DRequested_m61BF14CBFEBE3C1A4F02E5DA65129612BFF3D276,
	Provider_get_pose3DEnabled_mA6832343C5BF65920FED4255644ACCABD900B4EA,
	Provider_get_pose3DScaleEstimationRequested_m9CA25E3E91C8C17D5CFC774866A1C0D8DE6852A2,
	Provider_set_pose3DScaleEstimationRequested_mB5EB887504541EFBD4E9F502521494A2C9CCB675,
	Provider_get_pose3DScaleEstimationEnabled_mC56F62D5ECE673B5F8D0DF5B768D26E7FF7F60C0,
	NULL,
	Provider_GetSkeleton_mFAC0DCEF7349747BF264D2484280054B0FF0253C,
	Provider_GetHumanBodyPose2DJoints_m10725EBA1EB33455CC0798AFA2562453BF1726F7,
	Provider__ctor_mCA2547E3C5B7461FAB29AE27A5B0934B9221BBF1,
	XRHumanBodySubsystemCinfo_get_id_m2B4E8095B5AE6AF6E64FB7B56DBD795989A862F8,
	XRHumanBodySubsystemCinfo_set_id_m58CBD535224B7049AC8AC9D82305E1D8BBF90084,
	XRHumanBodySubsystemCinfo_get_providerType_m71F869C8A2B8C6AC2BB7D0C3842549886608BD19,
	XRHumanBodySubsystemCinfo_set_providerType_m228A78E8403BF03833D556B3EE0637291DC46615,
	XRHumanBodySubsystemCinfo_get_subsystemTypeOverride_m1AD3DA828683BFEC871F5E62C5B552E27F47A009,
	XRHumanBodySubsystemCinfo_set_subsystemTypeOverride_m847719A54A861DE6FE06E9DAFA1485E6184D2909,
	XRHumanBodySubsystemCinfo_get_implementationType_m3CD85264DFA467891430738097EBB79174BA518C,
	XRHumanBodySubsystemCinfo_set_implementationType_m8001903267263EED38F960C2E1BD5A53CE64C798,
	XRHumanBodySubsystemCinfo_get_supportsHumanBody2D_m93255214CEECB1C656D88ACFFF73D736D81FD8BD,
	XRHumanBodySubsystemCinfo_set_supportsHumanBody2D_m7E4750E667A695B158E006DDC6F74C56542CBE0E,
	XRHumanBodySubsystemCinfo_get_supportsHumanBody3D_m133406910A68DE814A0A335A59285098FEE70C19,
	XRHumanBodySubsystemCinfo_set_supportsHumanBody3D_m02D0D700619D9CB7D96D027EFE3EC41D47F81F8A,
	XRHumanBodySubsystemCinfo_get_supportsHumanBody3DScaleEstimation_m81A73CFB3B8232EF098B2CDF96F25687AC19845C,
	XRHumanBodySubsystemCinfo_set_supportsHumanBody3DScaleEstimation_m5F982C883E1918068DA02BA87C0069BF379E147F,
	XRHumanBodySubsystemCinfo_Equals_m99F61D9E0CFA86A7171F27A236AD8CD2FD7BF08A,
	XRHumanBodySubsystemCinfo_Equals_mE22CDDCA260523EF651BD2B23C1F66F11BB95C0A,
	XRHumanBodySubsystemCinfo_op_Equality_m7E2037783CDD8A42F384186AFA53DE86F99DC1B1,
	XRHumanBodySubsystemCinfo_op_Inequality_m3D4731ACACD907F94AD1ADC28A8C68E991D023EB,
	XRHumanBodySubsystemCinfo_GetHashCode_mF9F242A2F525ED4246171238CE9B5A7FD24C63FE,
	XRHumanBodySubsystemDescriptor__ctor_mFFA3B29932D6CD50B30E50BAF903818AB6C6ED44,
	XRHumanBodySubsystemDescriptor_get_supportsHumanBody2D_m7FD5572388D3E9EF931691F0267B3779B219F9DE,
	XRHumanBodySubsystemDescriptor_set_supportsHumanBody2D_m426CB15FE9C68540815E13FCCF40128F6BDD6ECF,
	XRHumanBodySubsystemDescriptor_get_supportsHumanBody3D_mD98F673993E12554F127E2FEF129C75146282BCB,
	XRHumanBodySubsystemDescriptor_set_supportsHumanBody3D_mB481745B000D50942570BC1A718AF119A729543A,
	XRHumanBodySubsystemDescriptor_get_supportsHumanBody3DScaleEstimation_mB422A877EBB97EB9FD1F846C52296A332012C24A,
	XRHumanBodySubsystemDescriptor_set_supportsHumanBody3DScaleEstimation_mD01ACAA2F3F335B5059B7613AC309D1525BAFAAB,
	XRHumanBodySubsystemDescriptor_Create_m6A81C133B714D6942AEEA5D98E8516657040CF8C,
	AddReferenceImageJobState__ctor_mC0CCEC53FEB86CE2B9560D06DE28919ADB2440E2,
	AddReferenceImageJobState_get_jobHandle_m02E9565D08C8156E799D1B852C14707856E6B12E,
	AddReferenceImageJobState_AsIntPtr_m8C97E68E09387D512B5A2D921841B3E0FCF44CC0,
	AddReferenceImageJobState_op_Explicit_mDA81B41DC7C894ADDEDC4CEE9E1CC0C8DAA0951F,
	AddReferenceImageJobState_get_status_mDF8FE0C1BC9407AD9EAA821DE78B76599455A25F,
	AddReferenceImageJobState_ToString_m89383245617B4E89FF1CA2FF897917062CD663A7,
	AddReferenceImageJobState_GetHashCode_m6EABAC53399090ADFD2932E561BA0FA12EA63DC0,
	AddReferenceImageJobState_Equals_mCFA105DAC305C1B3B34F0C7D0D856F3671356D37,
	AddReferenceImageJobState_Equals_mD0EE6BB78CB7601C9E1AC6C297417B6E4AE70502,
	AddReferenceImageJobState_op_Equality_m1F3798DA01CACD11DC90B5CA739749EE2240C609,
	AddReferenceImageJobState_op_Inequality_m9E0E28070CEE1EF9170AA3D3438915E5C5014F60,
	AddReferenceImageJobStatusExtensions_IsPending_mDA71DBDE3C6E5530CCB0B0E92DBD0D3A95CB0509,
	AddReferenceImageJobStatusExtensions_IsComplete_m56FFD233E085F1001E21085AB820FB749814AA22,
	AddReferenceImageJobStatusExtensions_IsError_m03EA2A197B6FE37D50B07B88BD12E6243D3B0C97,
	AddReferenceImageJobStatusExtensions_IsSuccess_m5170D9DA6B4AEDFC80BC1A55F99B7EB0578FA0DD,
	NULL,
	NULL,
	NULL,
	MutableRuntimeReferenceImageLibrary_CreateAddJobState_m0D3F0C269DC4388E00B628A01982BA04609380C0,
	MutableRuntimeReferenceImageLibrary_GetAddReferenceImageJobStatus_m4109C2DCB5D54098102658A5A31393E0D67E588B,
	MutableRuntimeReferenceImageLibrary_get_supportsValidation_mE0890A57BF3E61D7226931E190A093605EA47E61,
	MutableRuntimeReferenceImageLibrary_ScheduleAddImageWithValidationJobImpl_mA1984E4EBDB747C162F5F7F2DC47AD42A3FA2932,
	MutableRuntimeReferenceImageLibrary_ScheduleAddImageWithValidationJob_m98B12E578EA2A76A4F37D531DFD4BB6E2875A65C,
	MutableRuntimeReferenceImageLibrary_ScheduleAddImageJob_m01B3B1C04C75107A6800A43CECD36AD126839D3E,
	MutableRuntimeReferenceImageLibrary_ValidateAndThrow_mB1ACF908016596526B97FDF497DC9A258D2573BD,
	NULL,
	MutableRuntimeReferenceImageLibrary_GetSupportedTextureFormatAt_m52CBAAD841E5758ED8038440563347BF2EBD0DB8,
	NULL,
	MutableRuntimeReferenceImageLibrary_IsTextureFormatSupported_m8FC142D8D5B1652E8AFDD49BD1EED8A6FE3BB955,
	MutableRuntimeReferenceImageLibrary_GetEnumerator_m12161C68A1C63E5F42F3F69E12DE6D8B24C6502B,
	MutableRuntimeReferenceImageLibrary_GenerateNewGuid_m426233E3031BEA508FB52E4DA78DB1BCCB4D8CA7,
	MutableRuntimeReferenceImageLibrary__ctor_m4B24CE8934BC39705050E5C69C2641ADA47561EF,
	Enumerator__ctor_m25C351F3CA22AFB104CE79D00CFF851C7E247ECE,
	Enumerator_MoveNext_mF3DBBFA17313E104979A8A4F7CD5F111C352AF67,
	Enumerator_get_Current_mCAFA85FE5DFA6D3AF14AE3E0BD39A478B00D5F03,
	Enumerator_Dispose_m4CBA500DF0A5E65FA1BECA0D61521C960884E06B,
	Enumerator_GetHashCode_m687007D953BFC4902A3A0115F0E55CA3EBEB1DFE,
	Enumerator_Equals_m6831767F67C100E80B4C5BCDC1980E8453298DF9,
	Enumerator_Equals_m9D822B4C42050C4A121A8CDC1174F2A5824015BB,
	Enumerator_op_Equality_m293B7442D4BE33E92E21DEA6EEDB19987BE97298,
	Enumerator_op_Inequality_m70013614EC7DE83A9C173EE0EBF37D724BBF8ED6,
	RuntimeReferenceImageLibrary_get_Item_m02C9E57C775AEC269FCDAA1F51CDC1142CF50ED7,
	NULL,
	NULL,
	RuntimeReferenceImageLibrary__ctor_m1357708CB4C54A390D991C4C103B6F7D89A2E376,
	XRImageTrackingSubsystem__ctor_mBE6E7CD58CD9742BEE873E1D3AEC19BC1337EDA3,
	XRImageTrackingSubsystem_OnStart_m4FE3881F6EF815EC15B872CE0638E32D523633D0,
	XRImageTrackingSubsystem_OnStop_m4EFCBBF37ABFCB43D22F3D8B9B136FB46CD50E88,
	XRImageTrackingSubsystem_get_imageLibrary_mE8D15BAA0C750529680799A27597C4B5F61EBBC8,
	XRImageTrackingSubsystem_set_imageLibrary_m43AB62E6D41F3AF16AF633843C190D35575EB97B,
	XRImageTrackingSubsystem_CreateRuntimeLibrary_m1E7DAA2B60E911BFD4F9F27EABAC6FE44C617B9F,
	XRImageTrackingSubsystem_GetChanges_mFF556E643419EAE8E625953B65DE35DBDDC17A7C,
	XRImageTrackingSubsystem_get_requestedMaxNumberOfMovingImages_m297CA442EB359393CD73A53ED4BB535376BB8A72,
	XRImageTrackingSubsystem_set_requestedMaxNumberOfMovingImages_mE6898D8A250E46CD9CAE48EE63E3624AB7184F1D,
	XRImageTrackingSubsystem_get_currentMaxNumberOfMovingImages_mF52B4940D32D6802A8B80EB4261B728D50C068EE,
	NULL,
	NULL,
	NULL,
	Provider_get_requestedMaxNumberOfMovingImages_m67846B1C5D09A22934AEC73B741547234A7D4638,
	Provider_set_requestedMaxNumberOfMovingImages_mC40B4B4BB3069626401C77833D10CD59976A0533,
	Provider_get_currentMaxNumberOfMovingImages_m0AA32BF1665E9619205B1E0643885CCC8E23EE8A,
	Provider__ctor_mC1DE32F65A2B47BE70F69F6396F787AF33263138,
	XRImageTrackingSubsystemDescriptor_get_supportsMovingImages_m478CCC3CDCB620998AA0A2D7F27A72596BB9504E,
	XRImageTrackingSubsystemDescriptor_get_requiresPhysicalImageDimensions_mA2CD89187C55BDFB3C1E70F6AC67C5EC0097D54A,
	XRImageTrackingSubsystemDescriptor_get_supportsMutableLibrary_m613CD1AFBAB9E8D7BF9A9679AC40B80D0F0923E1,
	XRImageTrackingSubsystemDescriptor_get_supportsImageValidation_mD917947032A835ECEA655D1AA6F3DE6644C35615,
	XRImageTrackingSubsystemDescriptor_Create_m1785D4B66CF15D2AEE0A952A83DE873E27CB89E5,
	XRImageTrackingSubsystemDescriptor__ctor_m5B3591C4EC408C76FE8AA5F6F80A17BDA83B7332,
	Cinfo_get_id_m575543324B2C5A08D4C786C63370AE71BED07969,
	Cinfo_set_id_m26F70E551A2F6B517FB5F5C3E5EE4C129FA7BF42,
	Cinfo_get_providerType_mBE30E1C47EDAB073ED2660418CA3E29DA5F0CC9B,
	Cinfo_set_providerType_m4EEFA53693D860609EE4E84FBBD7411BCF38C7D8,
	Cinfo_get_subsystemTypeOverride_m1ECE580A62D07D72403739C3B0A3402066CA2939,
	Cinfo_set_subsystemTypeOverride_m92548450D405AAF6961BFAD7DFBA20AC0F258A16,
	Cinfo_get_subsystemImplementationType_m6D5DFEBCB48B3C0A9574AF76063A4E4C0BEE4D9C,
	Cinfo_set_subsystemImplementationType_m38DB94BDE396C4375A2384D92893106166598BD3,
	Cinfo_get_supportsMovingImages_mF03413F05E6DA7C176CC49907024D17F3CA8CDD4,
	Cinfo_set_supportsMovingImages_m8A9EC55903324606F3A00A1B9E0BA4A7F9FBA636,
	Cinfo_get_requiresPhysicalImageDimensions_m75BC1904E82964AAB2D4878CDC01A5DA91BCEF8B,
	Cinfo_set_requiresPhysicalImageDimensions_mEDA76B05F4AADE1843195F0C011BFAD5A10179CD,
	Cinfo_get_supportsMutableLibrary_mEED5E973B93B77E2EB889635CF0BFBED0F3AAD65,
	Cinfo_set_supportsMutableLibrary_mE18B618EF5F3EC11E1AB460302D88A92091DE9E1,
	Cinfo_get_supportsImageValidation_m4A509134A5D26AAEFA77EC28D48802E48D98AF35,
	Cinfo_set_supportsImageValidation_mCD49CB40A3C9FDC8340016FC0C6D4CA3AC71A98B,
	Cinfo_GetHashCode_m17D577D55135627B8C8B252E8694CE37D2DCDC8A,
	Cinfo_Equals_m1B1870077B045D565BC51101AB7C0F66734249FC,
	Cinfo_Equals_m6FB5DA5B648EE40F20736E915C5E64C13A4D312D,
	Cinfo_op_Equality_m9FBBD01BF13BA038217E369440ABB260380D56C4,
	Cinfo_op_Inequality_m4956F7FD40A7DA13D192EF708205EDD21E630851,
	XRReferenceImage__ctor_mCD536BB9053D7775175E0A8AE51BBF026AB06765,
	XRReferenceImage_get_guid_m6BEA9888191B7528B60F98EE03C9DBB2B9B8ADEE,
	XRReferenceImage_get_textureGuid_m70BB73989E26562E2B37F8C272F14F2D06659615,
	XRReferenceImage_get_specifySize_m571D71A02EF695A72121AAA086F8B52323E4E4A5,
	XRReferenceImage_get_size_mF44BF21ADEFBB155BFD8043E1067057219EC59F2,
	XRReferenceImage_get_width_mB6465C498B58CD9093F2FF5EA55DAC8F0E7580A9,
	XRReferenceImage_get_height_m422FF3F85DE70E492B7FBABC02277DF6BD76DCD8,
	XRReferenceImage_get_name_mF1BE1E54AD911D48445B7DDEF2E27EA01E1E73BB,
	XRReferenceImage_get_texture_mEC132411644C747C782F41A32A97C95B306D0891,
	XRReferenceImage_ToString_mA4374950A18DB316C790DD07F2485A385CE7F3D3,
	XRReferenceImage_GetHashCode_m4A2F5EA86EF5B9CDF39516FABD5E378D779B1BA0,
	XRReferenceImage_Equals_m1FACD89998C2C9ED6E65DDEE6C1466AE7CC4537E,
	XRReferenceImage_Equals_m6EA6760F9A443A324475B1E442AFA83C84F06D08,
	XRReferenceImage_op_Equality_m2440893B8883316E543B06AC59E930CB347A623C,
	XRReferenceImage_op_Inequality_m7196D768B1E195E76B4988E35E649C667459E2FD,
	XRReferenceImageLibrary_get_count_m4ACB1E3776B461ABA415E4F849367E7117ACEA52,
	XRReferenceImageLibrary_get_dataStore_mDB5DF12926E7B9F0B02C43C376C23CA99A371F64,
	XRReferenceImageLibrary_GetEnumerator_m65502536EB273B7DA0BF38C6D5E110444FC4C5A8,
	XRReferenceImageLibrary_System_Collections_Generic_IEnumerableU3CUnityEngine_XR_ARSubsystems_XRReferenceImageU3E_GetEnumerator_m2F52BF78FDE6328CA88D2FAA24B0774C4A3090FA,
	XRReferenceImageLibrary_System_Collections_IEnumerable_GetEnumerator_m1894805A8F33668DAC4F1BF4B1E957D561622E4A,
	XRReferenceImageLibrary_get_Item_mD4F21310DD927098ECA31AD41D783D6A3EC462D1,
	XRReferenceImageLibrary_indexOf_m222BC612D54C3F68B2E892B440FB1D8B863DA85C,
	XRReferenceImageLibrary_get_guid_m9302574FCBD28C3BCB849BD719A4F64040E1AD1F,
	XRReferenceImageLibrary_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_m15E6D2138688FF724E2D117996EC22519FC66A07,
	XRReferenceImageLibrary_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_m6C268E3C10F7576AA809DDE8833E9C9E262E67CB,
	XRReferenceImageLibrary__ctor_mF1DAFFAB5E205482C40A4980F585E585D958EAD5,
	XRTrackedImage__ctor_mC69E3D11AAD1CF838BC8575A0E476C296B4B1F3F,
	XRTrackedImage_get_defaultValue_m956437B8BAA793667C1008981EC550646ED6E6C8,
	XRTrackedImage_get_trackableId_m9EA6E15BEF6777E27B50A4903E0069AC04ED6405,
	XRTrackedImage_get_sourceImageId_mAAAA675839747EA6AF8A903E461F0D198CFAFDBF,
	XRTrackedImage_get_pose_m24132085AC8CCE5762C01ECCC1C264A36E77FD69,
	XRTrackedImage_get_size_m57847CD4307A9A560D358981700B8722D8A02438,
	XRTrackedImage_get_trackingState_m059B99A670B142384AE772376780095877CA72F9,
	XRTrackedImage_get_nativePtr_m2ECEAC93477008FB415D4A388ACAA4A9DB6E1892,
	XRTrackedImage_GetHashCode_m3900E50D96F2687C63C8F78C9BEA6E469FAE5E2C,
	XRTrackedImage_Equals_m95C7E1338C9CD5F37EE9D6452AE5820D2BC87FB7,
	XRTrackedImage_Equals_m54F9B4F5CC42200E927B5025274C6E765264C9BF,
	XRTrackedImage_op_Equality_mD09103E710741129A768B52ED46CC02FD61B19C6,
	XRTrackedImage_op_Inequality_m3C80CEF4076826592BA9619B4BB23870CB26ECEF,
	XRTrackedImage__cctor_mFFD6CBB4C4AA72F753B6C48B94A7441D2072777E,
	HandheldARInputDevice_get_devicePosition_mEA94A0592CC39ADECED0B21A3B015B0C3F217894,
	HandheldARInputDevice_set_devicePosition_mFACF712435D25DBC04E7595C073EB2FFB60938EB,
	HandheldARInputDevice_get_deviceRotation_m839A043DBB843829F25EE4F120D43C3C82C30892,
	HandheldARInputDevice_set_deviceRotation_mFF98E66E587EB92247C73A5EDF34317014BB15F1,
	HandheldARInputDevice_FinishSetup_m35BA4889F0CA64587C4146B741D8875BFE46352C,
	HandheldARInputDevice__ctor_m6CD9A2A4F215809FADC75915093A7DDA5E9C226D,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	XRObjectTrackingSubsystem__ctor_m027CD01BAA28162FEDC4F05360276DECB849AED8,
	XRObjectTrackingSubsystem_OnStart_m6AA3A8E01FA852C5BA2F9AEE3E0F887891B98B77,
	XRObjectTrackingSubsystem_get_library_mC003D13AE65E56F10539C28052F951C14AC350F3,
	XRObjectTrackingSubsystem_set_library_m30A908CBA0E9977DAB269FCED74DCC34A814455F,
	XRObjectTrackingSubsystem_OnStop_mC5E0DF3A2D7B96B75B9885FB9F775870E69DBB67,
	XRObjectTrackingSubsystem_GetChanges_m631751ED540DD35A674349C7632F17A291026B9E,
	NULL,
	NULL,
	NULL,
	Provider_set_library_m666803BABC136EA0218540480C235908CA5889F3,
	Provider__ctor_mC22FB91A82746D919AC450D89D423A6EA16979F4,
	XRObjectTrackingSubsystemDescriptor_get_capabilities_m64189B01B6E0C598E33BF8A0D01B72B22C16E0C0,
	XRObjectTrackingSubsystemDescriptor_set_capabilities_m03520AF26BDBA2B74F084EC8042DD765380EB644,
	XRObjectTrackingSubsystemDescriptor__ctor_m2177FCC9E1AFC3E173E078744766BFA8FF45BE5B,
	Capabilities_Equals_m60B932F4020B4C1D938F76F8B143AAD76901C48C,
	Capabilities_Equals_mC78F86790EF9479F76FC84B4E7F74E2E2C07D249,
	Capabilities_GetHashCode_mE4E2BB398DA4790DD8E2D0FCA8477062537CF432,
	Capabilities_op_Equality_m7D0232338B3F936539274AC270DE2F44E76B14F8,
	Capabilities_op_Inequality_m5B3091610FD835694E696E890736906D5F264710,
	XRReferenceObject__ctor_mBAE1DECE98351EEFC3B058C143FECED732A58B71,
	XRReferenceObject_AddEntry_m83F55C34FB2B5294EFA94E936676E3A3401E07BA,
	XRReferenceObject_get_name_m30CA572092D7E0DD1D7028A84BB0F5999A92D8FD,
	XRReferenceObject_get_guid_m96423410888B4CB9712D1A064CF874B5191A49D1,
	NULL,
	XRReferenceObject_FindEntry_mFF9C29CCCDF7BA08B307B787DF83EF8F27B6017C,
	XRReferenceObject_OnAddToLibrary_mF06627EC55B52C25A701E1E00D4F941C70432E42,
	XRReferenceObject_Equals_m464CFD79821FAA617E311B82ADA20968A248F511,
	XRReferenceObject_GetHashCode_m8ACBD77E6AAF860411D1A1135C4838667F1FB77D,
	XRReferenceObject_Equals_m3606543B9EF155334F0E71FFE4A550EF5DA0957C,
	XRReferenceObject_op_Equality_mB4DB4F0DA8E037BA2DF05FC199FEFB7918764C38,
	XRReferenceObject_op_Inequality_mC779363F7EA4DF578712AD7E005665AF522CF756,
	XRReferenceObjectEntry_OnAddToLibrary_m70E3885E0F675B4F264FCAD2AD689CD829F45926,
	XRReferenceObjectEntry__ctor_mCB4A1F3AFBC34A15AB2CC0A7BB4E5B45000DB16C,
	XRReferenceObjectLibrary_get_count_m0A1A32B348E4877179DE880F72673C40A9922F30,
	XRReferenceObjectLibrary_GetEnumerator_m35A1D24DF9038C2FC3F6487EDA7B7E7D86161765,
	XRReferenceObjectLibrary_System_Collections_Generic_IEnumerableU3CUnityEngine_XR_ARSubsystems_XRReferenceObjectU3E_GetEnumerator_m442782D1B9280AA958CCD052A7D34C6CB3856CC4,
	XRReferenceObjectLibrary_System_Collections_IEnumerable_GetEnumerator_mD5618BEF4160559F4C9B4839DE55C89DD57E441B,
	XRReferenceObjectLibrary_get_Item_mF60B78377A1290320912A635694360F08A6579F9,
	XRReferenceObjectLibrary_get_guid_m3113594B42592622CB48872DAE9CFF9BC503B2C2,
	XRReferenceObjectLibrary_indexOf_m54B8E63E60BA6B3EE1A1D2EBB476FD51ED4FCC35,
	XRReferenceObjectLibrary_IndexOf_m93685C344CC5829D3ADA00FB7D588595428E1D9F,
	XRReferenceObjectLibrary_Add_m30393A868ADFDA60503BFC7F6C9106FD40E5AE52,
	XRReferenceObjectLibrary__ctor_mE7411882F35FF8E6FDC74CBB4E3A6F691ADBD4F7,
	XRTrackedObject_get_defaultValue_mC50260EEA6810D7C99F6A65A6DD35BD462A26638,
	XRTrackedObject_get_trackableId_mE8CA173C4D77E4910C47CD5A3DBEA7570CCA69F8,
	XRTrackedObject_get_pose_mC8BBDFCC19D9FAF22FA0484E58C5BB1114C929C7,
	XRTrackedObject_get_trackingState_m7D1D9DD436ECB10D02D1413AB660ACBC23D60E89,
	XRTrackedObject_get_nativePtr_m804A89CA593F513109FCBC04A4FB2C16505F388C,
	XRTrackedObject_get_referenceObjectGuid_m5E5FEE2B90403C2F75252A6DF4BD9436A7927FD2,
	XRTrackedObject__ctor_m9916083096F5B1A8034C7450D07AA9192CDE7BDC,
	XRTrackedObject_Equals_m4D9D7A47BBD40F713CA964F4109DFD1AFB225D7A,
	XRTrackedObject_GetHashCode_m9E4ECD0C61FC65EF59F51AD6930540BDF65D2CE3,
	XRTrackedObject_op_Equality_mADC613427816017D84B7AEBEBA5781AC1FF037E5,
	XRTrackedObject_op_Inequality_m8AA5134578A1872CBEEEBB681CF789FCD41052B5,
	XRTrackedObject_Equals_mEA3B08014486E3DF12E17FDCA8D1FAE067FF8BEA,
	XRTrackedObject__cctor_m81F130691CFF4A57AEA186D70CA16284D2C2F9B7,
	EnvironmentDepthModeExtension_Enabled_m82D050848451FF9CA7F7491FE1243091B4F5C391,
	SegmentationDepthModeExtension_Enabled_mE436D9333AC945C4CEF2121001360C19A5FCFA87,
	SegmentationStencilModeExtension_Enabled_mBD3C16DBA3951DB0A975B9EC8E4EAED9D182D027,
	XROcclusionSubsystem_get_requestedHumanStencilMode_mEB05499AF0F84F62FBF38A21B0CC721290436DE2,
	XROcclusionSubsystem_set_requestedHumanStencilMode_m49451B1709E24F803E7E2BEE1EC50AAFA3DE3AC3,
	XROcclusionSubsystem_get_currentHumanStencilMode_m128A8322B2160F8087302AC6AEA2123F5D0BD708,
	XROcclusionSubsystem_get_requestedHumanDepthMode_m5717AA8084D53E5679252BF409A0813C6DA7117F,
	XROcclusionSubsystem_set_requestedHumanDepthMode_m5F644A70E20B294E9F5CE17B17715855733ABF37,
	XROcclusionSubsystem_get_currentHumanDepthMode_m664A81974FA236426C74B71978EEB718AA6B7F15,
	XROcclusionSubsystem_get_requestedEnvironmentDepthMode_mE10379901B371DE2D3101B0C0B70CE3A04703BB6,
	XROcclusionSubsystem_set_requestedEnvironmentDepthMode_mF15D23AFD9CE80BC9D49C15C9FD4C7406BFA1140,
	XROcclusionSubsystem_get_currentEnvironmentDepthMode_mEB2572194995692EF2953ED4F4F1A2926054D51F,
	XROcclusionSubsystem_get_environmentDepthTemporalSmoothingRequested_mAB4DEE3A6F2F32E466DE912B98E8638365BBD491,
	XROcclusionSubsystem_set_environmentDepthTemporalSmoothingRequested_mB49EE42E0E68422E0BFD07774AF191DF921CC89C,
	XROcclusionSubsystem_get_environmentDepthTemporalSmoothingEnabled_mDBB09B1740A54904F1E8CB734F42C1277C9187D0,
	XROcclusionSubsystem_get_requestedOcclusionPreferenceMode_mD16E1325C05F61D49B3E9E254B34751258AFB48E,
	XROcclusionSubsystem_set_requestedOcclusionPreferenceMode_m256CEB52B2162D428ACDB8031E5D3FE9E62C3E20,
	XROcclusionSubsystem_get_currentOcclusionPreferenceMode_mF26316112165324B11B50A2F40E1CA1B6851200C,
	XROcclusionSubsystem__ctor_m3D4915FA8E43BA7BD96CB3B9D4E4377E3833C189,
	XROcclusionSubsystem_TryGetHumanStencil_mD2FFD71CB444CA2F5C0DD059F78EBC8C084563DF,
	XROcclusionSubsystem_TryAcquireHumanStencilCpuImage_m8310E58DA83757ED46F9953EE66C50968D022883,
	XROcclusionSubsystem_TryGetHumanDepth_mE0C4446D29599251D41C78CBA0E14C9D0D620DF5,
	XROcclusionSubsystem_TryAcquireHumanDepthCpuImage_m2775C08F3043DFC6DD780E7361CE0B226848BA88,
	XROcclusionSubsystem_TryGetEnvironmentDepth_m401276ABEA03CB060E5220DF61E5DB93AAE539B0,
	XROcclusionSubsystem_TryAcquireEnvironmentDepthCpuImage_m2A0E8CF20892BC54DAF118E7BE5158DBED8C7103,
	XROcclusionSubsystem_TryAcquireRawEnvironmentDepthCpuImage_m0F7B57B62CFFE1AA86A991AAA36E1AA65EDDCB97,
	XROcclusionSubsystem_TryAcquireSmoothedEnvironmentDepthCpuImage_m0E2FBDD491C658A0F35AC4AA8E71946F7B984F89,
	XROcclusionSubsystem_TryGetEnvironmentDepthConfidence_m7665C1DB56B8A41659BCCF7F93DBFCC6C42225DC,
	XROcclusionSubsystem_TryAcquireEnvironmentDepthConfidenceCpuImage_mDEB7DD6583B814A66D6D4ED8F216A6C8461939A1,
	XROcclusionSubsystem_GetTextureDescriptors_mD667D3C1F1C019B70E7B1B37131DBCF5A3962FF3,
	XROcclusionSubsystem_GetMaterialKeywords_m2139A79542D673AE59B6F4D0F3829027792AB150,
	XROcclusionSubsystem_Register_m734F3D1F3186CFCF3F60A14BDA8A2906BB4EC2CA,
	Provider_get_requestedHumanStencilMode_m02D0E05F66EEB135680476AD4EC02D4B2794EF9E,
	Provider_set_requestedHumanStencilMode_m2C054B14D026BAA9253619D1008CCC90813129AE,
	Provider_get_currentHumanStencilMode_m420FA0B1871114E7449879089880D30E0097AF88,
	Provider_get_requestedHumanDepthMode_m1A6A24D866AF602986F0CAE91118E94F1BD567C5,
	Provider_set_requestedHumanDepthMode_mB20690D0279BBF6E8326EDA78F76B89F617BD7B3,
	Provider_get_currentHumanDepthMode_m057D9E76208F1A01AC2E5E3FCCCDEBCC6553B25C,
	Provider_get_requestedEnvironmentDepthMode_m0652674FBE2080EF267BF1A1E2CF85C601F8DBE0,
	Provider_set_requestedEnvironmentDepthMode_m9F873C36A3DE66E23322B9F009FBCC72BC5355D9,
	Provider_get_currentEnvironmentDepthMode_m63B399BC6600EACB3BF5E5DF7A2218DAEF9BD71D,
	Provider_get_environmentDepthTemporalSmoothingRequested_m89320DDAF30F855C21E6A2958C3E54E488D4F7AE,
	Provider_set_environmentDepthTemporalSmoothingRequested_m67FB296501D21757E9620951F787BB64DAA43248,
	Provider_get_environmentDepthTemporalSmoothingEnabled_mF5A5DEB423E20A8D6623C6C5D59A7BAB33B6B105,
	Provider_get_requestedOcclusionPreferenceMode_mAF6706610847D5B9E241E88A1465EFDE70A5DC9C,
	Provider_set_requestedOcclusionPreferenceMode_mC3935B4D950080DAB0EC013E2C5670ED45C5A456,
	Provider_get_currentOcclusionPreferenceMode_mB1E452A0FA4BA3B8EF138F43C76219365750C7FF,
	Provider_TryGetHumanStencil_m6196011447FE5DF70B523186981744EFA71256A5,
	Provider_TryAcquireHumanStencilCpuImage_mFC2EDA1E1EF465E4B50AE1947FAC5959182FC99C,
	Provider_get_humanStencilCpuImageApi_mC79E875A9D2FC1E06E8A6E927BA2C31FD07995D5,
	Provider_TryGetHumanDepth_mD898071B7B422E264EEADFE7C99D802B10696AA0,
	Provider_TryAcquireHumanDepthCpuImage_m7385F63DBA5724F1A33A58D189B07EE94A9BBA68,
	Provider_get_humanDepthCpuImageApi_m880F91E737CF90D36A272FE9DFAE99CF9E5534D4,
	Provider_TryGetEnvironmentDepth_mFF3B8C72028987673D5079C935332A6E005C357F,
	Provider_TryAcquireEnvironmentDepthCpuImage_m35551DC93914ADDCBE8A7F3EE21BF2753DA085BD,
	Provider_TryAcquireRawEnvironmentDepthCpuImage_m57D3628D7EE8A1D2E81E027637BC5BD3DC4AA606,
	Provider_TryAcquireSmoothedEnvironmentDepthCpuImage_m6790C57B075FC05D095DA3FF6202EF41B9A6EE08,
	Provider_get_environmentDepthCpuImageApi_m01271E261AB308C5996B4C64C3C6C559C5BE7EAC,
	Provider_TryGetEnvironmentDepthConfidence_m7F357AB09577179B0ACB3804018D29371CE07037,
	Provider_TryAcquireEnvironmentDepthConfidenceCpuImage_m097B6ECA156BFD8D303FCA344DC1D048F60D09E4,
	Provider_get_environmentDepthConfidenceCpuImageApi_mF94BCCC60E8F21B0DF7BC901A64339E669FC1995,
	Provider_GetTextureDescriptors_mCEB2B41F247F9287871D2FCB36DCD3B1900D9297,
	Provider_GetMaterialKeywords_mA35506435187EC4672F50E5BC87F1C30798E48D7,
	Provider__ctor_m50964AC0676294C1CDEF2B3C8E05657DE364ED2A,
	XROcclusionSubsystemCinfo_get_id_mF11E38C57E4AB8E81F9E7875A0A41D04A19C4039,
	XROcclusionSubsystemCinfo_set_id_mF8B41D7F5FACF940467D57208BC03DDD89D9B7A8,
	XROcclusionSubsystemCinfo_get_providerType_m98D7D72FF4C0B36F28D6E39BC498E43691AE718B,
	XROcclusionSubsystemCinfo_set_providerType_m5D9D3B330216EAB023F4B17F4853D5A612B07380,
	XROcclusionSubsystemCinfo_get_subsystemTypeOverride_mAE0D5036913033AB1D45B54C10047F180648BD3E,
	XROcclusionSubsystemCinfo_set_subsystemTypeOverride_mA0E976FA9B7955BD224DF93F7AB9AEA883779563,
	XROcclusionSubsystemCinfo_get_implementationType_mC1121AB1278E4F86B951FBD0B2EAD85D81A2AB45,
	XROcclusionSubsystemCinfo_set_implementationType_m0D5D7F0B926679A2195C01F183280029ADC525BB,
	XROcclusionSubsystemCinfo_get_supportsHumanSegmentationStencilImage_mB151BF0F40B3C9D9D2DE26318FD219FEF0C2AB9D,
	XROcclusionSubsystemCinfo_set_supportsHumanSegmentationStencilImage_mE4E442080331134DC255F46E5342D25FF48CB666,
	XROcclusionSubsystemCinfo_get_humanSegmentationStencilImageSupportedDelegate_m77677BDADCDA75FD77F97A942FA6B29706500292,
	XROcclusionSubsystemCinfo_set_humanSegmentationStencilImageSupportedDelegate_mB0F746AC0CBE2CC986B43CA50873FF91D3D9860F,
	XROcclusionSubsystemCinfo_get_supportsHumanSegmentationDepthImage_mB73625A00528D80575D712BE5621FF9219E3B6E9,
	XROcclusionSubsystemCinfo_set_supportsHumanSegmentationDepthImage_m3D99453F58EEFC98739B052C19B05A4FD5341926,
	XROcclusionSubsystemCinfo_get_humanSegmentationDepthImageSupportedDelegate_m67F62406957D42F2EF689DE57FD0074C3DAF2BD5,
	XROcclusionSubsystemCinfo_set_humanSegmentationDepthImageSupportedDelegate_m4AB47FCB92617E34DC2C66699B26346112E5C145,
	XROcclusionSubsystemCinfo_get_environmentDepthTemporalSmoothingSupportedDelegate_mD84F3F4F9DFCFC8C4CE86F9276EEA5ACA2392D1D,
	XROcclusionSubsystemCinfo_set_environmentDepthTemporalSmoothingSupportedDelegate_m6B67C82AEA73E79B7CD03F9912746C6B52C27949,
	XROcclusionSubsystemCinfo_get_queryForSupportsEnvironmentDepthImage_mA29F2AB5C4AA073556B074CFD5A88D3345D596B3,
	XROcclusionSubsystemCinfo_set_queryForSupportsEnvironmentDepthImage_m4BFFF1A117B8452E731F6EC8A4B3CBBFAA0F0B9D,
	XROcclusionSubsystemCinfo_get_environmentDepthImageSupportedDelegate_m540B191F5215CDD7A1DEC6E370065AE8C9ADE75C,
	XROcclusionSubsystemCinfo_set_environmentDepthImageSupportedDelegate_m18E4C878D993208EB9EEA9D667CA5C88E4E1D4BE,
	XROcclusionSubsystemCinfo_get_queryForSupportsEnvironmentDepthConfidenceImage_mA3552C9DF07B609155051A3A80FA30AFDE2D3D1F,
	XROcclusionSubsystemCinfo_set_queryForSupportsEnvironmentDepthConfidenceImage_m4717AAF7FA480A374DF1357BFDD2D9A256CF5117,
	XROcclusionSubsystemCinfo_get_environmentDepthConfidenceImageSupportedDelegate_m562A6E788BE3D0CCD49F0DC2DC698CFAB87DDA6C,
	XROcclusionSubsystemCinfo_set_environmentDepthConfidenceImageSupportedDelegate_m0F91D5C5B63DEFD91FE9A00EF95B45ED9711461A,
	XROcclusionSubsystemCinfo_Equals_mEFD9C210D7814A35FFE675EBD2EE91E6A3856623,
	XROcclusionSubsystemCinfo_Equals_m232468ACCEB9CD8E20E096E270EEC1B262013812,
	XROcclusionSubsystemCinfo_op_Equality_m6C7001873AE986BD36170221DFC13BE143054E6F,
	XROcclusionSubsystemCinfo_op_Inequality_m7472697C65E462057EBFB8A694021E037634CCA2,
	XROcclusionSubsystemCinfo_GetHashCode_m672CF4A97241C59DE78E46E2D41245EACC13B09E,
	XROcclusionSubsystemDescriptor__ctor_mBBF9386E58133376619E4EF143BDA4826E0B5153,
	XROcclusionSubsystemDescriptor_get_supportsHumanSegmentationStencilImage_mE5760E70F08587F5CD918184E394C675DADB106B,
	XROcclusionSubsystemDescriptor_get_humanSegmentationStencilImageSupported_mFD38298D6EE716F04F941DDC4970DE420C19BEC8,
	XROcclusionSubsystemDescriptor_get_supportsHumanSegmentationDepthImage_m912EC7337D203DDFA386290C81F1DFEEF9166B11,
	XROcclusionSubsystemDescriptor_get_humanSegmentationDepthImageSupported_m4B58731FE66946A339E57D07B4970286F3FAC61C,
	XROcclusionSubsystemDescriptor_get_supportsEnvironmentDepthImage_m28E39D10619376C8447AFE6782E7790278E4B6F5,
	XROcclusionSubsystemDescriptor_get_environmentDepthImageSupported_mCD6A87F2AE78B41341FABE82E85BB0331CF8433A,
	XROcclusionSubsystemDescriptor_get_environmentDepthTemporalSmoothingSupported_mFCB0CBE3BAD8C25FEE42FEE7EA9B8BB8A85649F6,
	XROcclusionSubsystemDescriptor_get_supportsEnvironmentDepthConfidenceImage_m677676680648DC59997D716033762038B3AEA846,
	XROcclusionSubsystemDescriptor_get_environmentDepthConfidenceImageSupported_mEAA745B711055E3D0A07857DA2A5C58A8E301160,
	XROcclusionSubsystemDescriptor_Create_m4DFED9463984990C7091D9E6B4502BA2F2DE84B2,
	XRParticipant__ctor_mB90C6BDD46B876A4500C55B8CC4C5397AF98B4B6,
	XRParticipant_get_defaultParticipant_m809E05F58B936737F69747E3EEB89A0E5D4F7D72,
	XRParticipant_get_trackableId_mA33D6F01E0C98B53E73D3AE91E9D27637D3EDCF1,
	XRParticipant_get_pose_m68A36D0B3837325D073CCB92A93F600A2D535B7B,
	XRParticipant_get_trackingState_m65687E606627A087623C9937E49EDE133C5EDD89,
	XRParticipant_get_nativePtr_m55B683C801F0EDB9EBBCE21A31B507469838D09A,
	XRParticipant_get_sessionId_m266D8DE3F178F3EFF1ACBC50578F67E2152006AE,
	XRParticipant_GetHashCode_mBDFD1ECFEF61A85D9A8BF3857AA0B6220B4A25F1,
	XRParticipant_Equals_mED9407D8D4A118CC1E4B2591A3C3884E3FD74708,
	XRParticipant_Equals_mA78A4E55D2F314EA48933FB16F3AE557819DCC9E,
	XRParticipant_op_Equality_m306B50C24D65C1902DC67351F5EFD7D5D0D935C0,
	XRParticipant_op_Inequality_mDFD74D9170ED0372EDB83F2E5724FAA5AF761E51,
	XRParticipant__cctor_mD3418557AE6CE7D2D556BC3F89132164CB63B66E,
	XRParticipantSubsystem__ctor_m26C054CC2A79665F15F8A717792E63FF4853DB62,
	XRParticipantSubsystem_GetChanges_m9EFFFACBD3016F3C5B27CA9228D4B787B64EEB0E,
	NULL,
	Provider__ctor_m497F40010C1641F42595ADDE4DFD8F6E5D4D814E,
	XRParticipantSubsystemDescriptor_get_capabilities_m19ECB9CEC93B1CF942893787981A0E33213E07E1,
	XRParticipantSubsystemDescriptor_set_capabilities_m12B7B396F6D2824F6D33E84B2C7C9D46874E6AA7,
	NULL,
	NULL,
	XRParticipantSubsystemDescriptor__ctor_m1629BC38262CF3786A0C4A765B0653D3038D4DB3,
	BoundedPlane_get_defaultValue_m8231738F569F3ABD6A5A5697B1293C3A75F47D31,
	BoundedPlane__ctor_m95C41A6B0DB95A2636683BE716E9F92A0465EF87,
	BoundedPlane_get_trackableId_m7AA7FD63EA8F8A903300EFDF15616315ACFDA8AA,
	BoundedPlane_get_subsumedById_m27EFD2CAFFDCF6560D445115D596F23094F612B9,
	BoundedPlane_get_pose_mE6F416B0C7519EDA0D1AE8D8BD4D627E4CEA96CC,
	BoundedPlane_get_center_m3BB7635D2137C7C414FC682EBE0CB5E1F8D3F7D3,
	BoundedPlane_get_extents_m60341CDB176C9039D5B88B2F52534D356E11F400,
	BoundedPlane_get_size_m2645C0FF517DF15F381B268DF6366F4D14381DC8,
	BoundedPlane_get_alignment_m4E43582A7059AE23DD506878BCF164C61422DBEF,
	BoundedPlane_get_trackingState_mC294F13F8F79D53F8F04D8FB4E160B092BA6A577,
	BoundedPlane_get_nativePtr_mE8E2608856FE4327913A38005F4A8590D65A43E7,
	BoundedPlane_get_classification_m4EA9556C440097648A87D3AB7EC433776468A725,
	BoundedPlane_get_width_m634AD1BAD468FF96CBFC5786A4CB8A9747737E96,
	BoundedPlane_get_height_mB72E46326D1B3DAA5EF67D7FC65D58ECF02FEB5E,
	BoundedPlane_get_normal_m219E5CB840E4DEE8ECC168F2E5BC3FA6AD5E3DCA,
	BoundedPlane_get_plane_mC4E55F965A895DDD4EB960BEB612185DE21FD9AC,
	BoundedPlane_GetCorners_mA9013A95E8FF0830A83791567377903D4D4ED8A8,
	BoundedPlane_ToString_mA4FBDD41FC676DB2C2EEB22DA2E624099EF06ADA,
	BoundedPlane_Equals_mE1A074D048C20E980CB7016FAFA7EDFDA52DB15D,
	BoundedPlane_GetHashCode_m8C684989A748253B2A3772BCAA87D8758FB98941,
	BoundedPlane_op_Equality_mF4E866CA90FDDDF9842CC3C673A858C537E466E8,
	BoundedPlane_op_Inequality_mB52419B2984E8267DBC6EB2810637182CC4709EB,
	BoundedPlane_Equals_m1F738CE040A5D498E41B35521109A3FFBEB7196D,
	BoundedPlane__cctor_mFBFFC643A005EC73B9D042217D880CD0121D8E91,
	PlaneAlignmentExtensions_IsHorizontal_m05AF9996C1C4916E75371E655105DE73DD5A11F1,
	PlaneAlignmentExtensions_IsVertical_mEDE1ACA365DBF00018B3C0ADCCDD657DBDE9AA0B,
	XRPlaneSubsystem__ctor_mDE29D107C740A5CB086A7159DCC17E567A666603,
	XRPlaneSubsystem_get_requestedPlaneDetectionMode_m303F2743295F6A35D586107BA463A80D445398F5,
	XRPlaneSubsystem_set_requestedPlaneDetectionMode_m35162C34975D7AF454CF0D077029BA9DA32284B8,
	XRPlaneSubsystem_get_currentPlaneDetectionMode_m45E78CABE7A117E62B2AA2A3F92D05A6158CE936,
	XRPlaneSubsystem_GetChanges_m180B0681AF9853EB236AD62835F92E90F5E1908C,
	XRPlaneSubsystem_GetBoundary_mF1638B7F45F420D6A56A25D5749A91AF6C46B961,
	NULL,
	NULL,
	Provider_GetBoundary_m85479D05365E9665B11E24CADDC45AFDC9A43158,
	NULL,
	Provider_get_requestedPlaneDetectionMode_m44A491A20B7336C2783B01835473BF1032570135,
	Provider_set_requestedPlaneDetectionMode_mEC2045B55C836A6D80F35FDC31682FB6A2F00E3F,
	Provider_get_currentPlaneDetectionMode_m72405CAD5E46C4CBD1DF9BABEDE6F7D3CF2F162E,
	Provider__ctor_m54E6412FCE1B1A4CB7852D23AC643A9698BC4001,
	XRPlaneSubsystemDescriptor_get_supportsHorizontalPlaneDetection_m445A5130F29EA2A2AF125632097618241D12AA93,
	XRPlaneSubsystemDescriptor_get_supportsVerticalPlaneDetection_m29CDC06AFE8368431E0092990D7148043D13996C,
	XRPlaneSubsystemDescriptor_get_supportsArbitraryPlaneDetection_m7B057BEE25216B24BC2648D0191D3A3F88A142D8,
	XRPlaneSubsystemDescriptor_get_supportsBoundaryVertices_m591A2527B7E06AF0AB1C46197EA9E8C21BF76206,
	XRPlaneSubsystemDescriptor_get_supportsClassification_m634BD5268FC0BB4BCE42396067B1C03E48D4F03F,
	XRPlaneSubsystemDescriptor_Create_m436344F4D6E1681E25A6EB110E13B4B78D63FF59,
	XRPlaneSubsystemDescriptor__ctor_m73149431D0358E0258082B14FA2EB05F49CED36B,
	Cinfo_get_id_m3C9491FE6D19662F5CDE221328F86862621B6DE2,
	Cinfo_set_id_mCADBFEB62A645F33A8FE7684CE21D11A837FB6F9,
	Cinfo_get_providerType_m43F95297A90490AFA397FC61B990A33F74259EF5,
	Cinfo_set_providerType_m75AA9217739457DA075AB764BF440217E87A1126,
	Cinfo_get_subsystemTypeOverride_m30B55A83F149F3B304AAAD85FE3E80BFCD75552D,
	Cinfo_set_subsystemTypeOverride_m8880B201EBF541B726CA629EF2DBA762E5ACB010,
	Cinfo_get_subsystemImplementationType_m87A964B3827007A58DEAF8966B51575BA7687D6D,
	Cinfo_set_subsystemImplementationType_m63ACA332E759D120AB82AA3FE630512846B04E4E,
	Cinfo_get_supportsHorizontalPlaneDetection_m25246A60EFD930C7AC4C50A950E910E7716C9315,
	Cinfo_set_supportsHorizontalPlaneDetection_m73E4DE44A091E9B268214E732EFA29174703EEF1,
	Cinfo_get_supportsVerticalPlaneDetection_mEA496CC68069CCAD03DBC1B57F53DDA57D56A8BF,
	Cinfo_set_supportsVerticalPlaneDetection_m989F6D7C88D39981D1DD342DEC887E9DB3E44AF3,
	Cinfo_get_supportsArbitraryPlaneDetection_m7E19D041E3828651646769D594B9647149B5A0F4,
	Cinfo_set_supportsArbitraryPlaneDetection_m423FEFB76FB81C496A35BB358EF04592C8EE9C10,
	Cinfo_get_supportsBoundaryVertices_m75615CA66C3E0020B75915F8426FE6B2B475BEDD,
	Cinfo_set_supportsBoundaryVertices_m96752ABD368822EE7EE393F7AE3AAE631A4C3657,
	Cinfo_get_supportsClassification_m2596CCB90308DA90A90177C91854DDFBF18F464A,
	Cinfo_set_supportsClassification_mB1E8AAC1F2A7D511C960C6606364C87EEA1A221D,
	Cinfo_Equals_m8A992E8675D4C2A5FCF7FCD7714CD1DBD734FEC0,
	Cinfo_Equals_m2B155451B272C1E8954EDA6D6DFD1C151408D393,
	Cinfo_GetHashCode_mC8813973E6CB5AB8D267B6D76693B6F96C006BF9,
	Cinfo_op_Equality_m2035A48D9DF9261BE14129744779020E33E25051,
	Cinfo_op_Inequality_mA601145904E7851A476968928F6B25607E95842A,
	XRDepthSubsystem_GetChanges_m81D786135FAEEC4A43074CFB86E9BA58EA920E87,
	XRDepthSubsystem_GetPointCloudData_mE49B5DA3833CD9B6E4460AA3AF73E5C5A3415A4F,
	XRDepthSubsystem__ctor_m781CA41BFE991D20A8F23A03E05CACBDB2E451D0,
	Provider_Start_m9A61AAD67B739507B482B56606675A8826F7F6DD,
	Provider_Stop_m13FFAEDC52F1EA5792B5BCE1ACF767852A1C2C57,
	Provider_Destroy_mF86E96DC1B73E57A3F2DF68207CF33472DABA831,
	NULL,
	NULL,
	Provider__ctor_m8ADEDD82C49DE76D1FD3EABAAA6B536D2AC09863,
	XRDepthSubsystemDescriptor__ctor_m4C00884FC883BD3C3985937F0186BA730D41733F,
	XRDepthSubsystemDescriptor_get_supportsFeaturePoints_m06674E4C86120E43DBCAE5CB2A6F3D46FF88F0C9,
	XRDepthSubsystemDescriptor_set_supportsFeaturePoints_mD4237C54980FE39C9CBCF4BCA044119B16602A98,
	XRDepthSubsystemDescriptor_get_supportsUniqueIds_m1730C29B6B06CE71AFD0CCE23FA4A0E099B8773F,
	XRDepthSubsystemDescriptor_set_supportsUniqueIds_mBF587447AADB9D724EEA4CA1BEECF0215356CF81,
	XRDepthSubsystemDescriptor_get_supportsConfidence_m03816F0629E7D1A948EAB7DCF52B6B52E656EE9D,
	XRDepthSubsystemDescriptor_set_supportsConfidence_m38F6838582304E5A8BED0FCC7A6C8FCAEE607595,
	XRDepthSubsystemDescriptor_RegisterDescriptor_m16624F379AFCDFBF8AC973453AD716B5994F51C2,
	Cinfo_get_providerType_m9A765D29AAB5D3BB7C85A54C8B5BD5ACF0A34834,
	Cinfo_set_providerType_mF68F31F518ED15570B01758D651590F2A79ADA50,
	Cinfo_get_subsystemTypeOverride_m9E130C961C57B8F9DEE5CB775E5BAC1E82FE9ACC,
	Cinfo_set_subsystemTypeOverride_mF477DEE9A66BA9E979AE210507B158E20DB7F897,
	Cinfo_get_supportsFeaturePoints_mDA5E397852EFECCFB9D72BEF50D8149389AF32A0,
	Cinfo_set_supportsFeaturePoints_m9AB5B37930ED13405E788D882B2ED496A8A003A4,
	Cinfo_get_supportsConfidence_mD64AB8201EF5FED46B5961D3E20A501074F442BA,
	Cinfo_set_supportsConfidence_m8455406D7A9D0EA2B0600FA6D952CF4E7B169AA2,
	Cinfo_get_supportsUniqueIds_m0CD91E9193EA5454EBE851570155F301B1B4E499,
	Cinfo_set_supportsUniqueIds_m0C526CF6938A8220903C47BD11AC9F6A1B1E86F6,
	Cinfo_get_capabilities_m75D2555477E50E9EE792D385FB7E178EF121362C,
	Cinfo_set_capabilities_m057D002DC3F65F4A194653D7724131E7DE20D852,
	Cinfo_Equals_m45095412211AFF025FFA31098667F7CF82BF2999,
	Cinfo_Equals_mDA07D4DDCD91815E6748EC1C73EAB4CA9C911921,
	Cinfo_GetHashCode_mD5232DFA01B322FD842CE7B2440D51800770C3A9,
	Cinfo_op_Equality_m75ABE2EF81D2B69BD93B140971EF4400CF820101,
	Cinfo_op_Inequality_mE917E35619B50FF269A7D869332169B59033739C,
	XRPointCloud_get_defaultValue_m5610F421A5FD68490D3355BFE988423D0E816943,
	XRPointCloud__ctor_m7689BEC3D1FFA90CE83BD510C993768CCA777688,
	XRPointCloud_get_trackableId_m3AFB6026E205E26C8B7A3209696F566FB686144D,
	XRPointCloud_get_pose_m0A8AC4386A388238F4910916CDD3D1B936DB8A51,
	XRPointCloud_get_trackingState_m2CD370D0D6A2A920AC1637D94BDD3BCC5DB8945D,
	XRPointCloud_get_nativePtr_m606D5A1327EAF4D7A23811FE9D90BB8A027D5B11,
	XRPointCloud_GetHashCode_m96702297D45AEC469D7D3106FBE47F36B50F9688,
	XRPointCloud_Equals_m401E9050FB531805238DF8BCBB06491E23A763B0,
	XRPointCloud_Equals_m1A2A56D489C6CFB2DA66238FA843A972E5B2341D,
	XRPointCloud_op_Equality_mDFC0F2DA174092EB34DF0B6D80DDCBDE2DB9F07D,
	XRPointCloud_op_Inequality_m01FF6576FC299EE71FBCAEBA76638A99469F1E13,
	XRPointCloud__cctor_m54A8BB40B68A4C48D30FF9E7F25EBC9D1424C76C,
	XRPointCloudData_get_positions_m6B1843590E0A5A94DBA711BF1FBA3A64E39A00A5,
	XRPointCloudData_set_positions_mDE6F539B73AEA3C49189F4210F9D01094A02F14D,
	XRPointCloudData_get_confidenceValues_m4553186D87BC21D13B0B5AC3542BDFE6CFDA15FA,
	XRPointCloudData_set_confidenceValues_m2541483932A4753B91B3038EF869340A1B949355,
	XRPointCloudData_get_identifiers_m86966DF55A38D54A4284AE08D1EBE95F95F80203,
	XRPointCloudData_set_identifiers_mA6E3D5E038C89FE4187BE6F57CA92822F0A0CB9B,
	XRPointCloudData_Dispose_m761F04E465F85CB79EC6BDFFBAB3A348CCB02F75,
	XRPointCloudData_GetHashCode_m1787DE4A09656A3F814952590544879379129FB2,
	XRPointCloudData_Equals_mA86AEB4AE413BE16DB87461174F31B9ECE87EDED,
	XRPointCloudData_ToString_mE72394A861C3A87F2DA161BAE348FF5E5A0E8052,
	XRPointCloudData_Equals_mE40F5EEEE84C1953A50F3E08BE1C17D3A965B970,
	XRPointCloudData_op_Equality_m85B4FDE8642BACAC997A229E3BD4B2B9FBA336DF,
	XRPointCloudData_op_Inequality_m0E69D9AA98D46A4F6EE90F489B66A4368F5D805C,
	XRPointCloudSubsystem_GetChanges_m5F38A4B92BA7227A6C18AFE5AC44915847666096,
	XRPointCloudSubsystem_GetPointCloudData_mCCC6565794A8749B2F35390E05D91C80981ED75A,
	XRPointCloudSubsystem__ctor_m4F9FD9C780D1FC9E1E32F77B0CC4CA64785F7F80,
	Provider_Start_mB5DD7354C6FA33D63959274BA1AA66513686B0B2,
	Provider_Stop_m6D87F9001DFFFA0B7668D291A7C24543F0CF454D,
	Provider_Destroy_m1D726AD579499CE85671C2716F0015BBEB2C47C0,
	NULL,
	NULL,
	Provider__ctor_mEFF8084B718E4A6BAB9D089DF3E4FBE7F1100379,
	XRPointCloudSubsystemDescriptor__ctor_mD877FAE2757F9913E54D45FF145380FDB87341E8,
	XRPointCloudSubsystemDescriptor_get_supportsFeaturePoints_mEE4BCF13E8E2F044496C843E7DA5861A79ED0D5A,
	XRPointCloudSubsystemDescriptor_set_supportsFeaturePoints_m09AFC1EA8C3210687BC4DDCC2036A239E1390A30,
	XRPointCloudSubsystemDescriptor_get_supportsUniqueIds_m1A0E1BF0868020AA85400BF74FED3B49746B2B11,
	XRPointCloudSubsystemDescriptor_set_supportsUniqueIds_m3A103C2FE5DAECF6CA633A20A41BF639E7F84AAE,
	XRPointCloudSubsystemDescriptor_get_supportsConfidence_m6979C820823ABFC942BB60C8B2D48FFCF7F8ABF6,
	XRPointCloudSubsystemDescriptor_set_supportsConfidence_m11B3ED216AA69344F83EC87577F51D76053490DE,
	XRPointCloudSubsystemDescriptor_RegisterDescriptor_m0AA697D49E654962617609C4BBA9FFA8122E708F,
	Cinfo_get_providerType_mDDD1F34666705A5BB5B8ED6BD6A76D3449F35323,
	Cinfo_set_providerType_mC05B07E51AB9C1D9876C0B38F185CE8A8CAB3ACF,
	Cinfo_get_subsystemTypeOverride_m67FF1505BC8E11A5A8B8AD478D581703ACC11AFB,
	Cinfo_set_subsystemTypeOverride_m242BF043654209565488CEE2A0ACB93CE23E9C72,
	Cinfo_get_supportsFeaturePoints_m21644904ECC661B186FACE0FD45BA53FCBE67C29,
	Cinfo_set_supportsFeaturePoints_m9B0684F80A0FFF829E28C477E0E49AF60C1DDC15,
	Cinfo_get_supportsConfidence_m24690BFED822D8034981F9ABF2C02361C071798D,
	Cinfo_set_supportsConfidence_m7EE730D229E3187CE6DC3E9FB2E31FA32D1A29D8,
	Cinfo_get_supportsUniqueIds_m955E4A28D276A9459A9496C92C242F6BD429E236,
	Cinfo_set_supportsUniqueIds_mA096C2070124D20D28C7009BA35B23918DC974A0,
	Cinfo_get_capabilities_mE55D2E7B6B53FB7350832A08A1D626E2E3855090,
	Cinfo_set_capabilities_m975B6EDB8EB063F712A26448245859759A35D7CE,
	Cinfo_Equals_mB548ABCC0365E9FF78A36324ED2F1128B3836FF7,
	Cinfo_Equals_m0305229B15DB2B5DF7EEEB4C8D28883DC35A9588,
	Cinfo_GetHashCode_m4D75518C4E9BED4C7BF4508CAD92A03D5D1A77A7,
	Cinfo_op_Equality_m64C002CA5C4FA2ECD7A4AC24C76A5F80DB357636,
	Cinfo_op_Inequality_mDF48CEB51F3076BA8EB386B31D50800B54F3BC04,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	XRRaycast_get_defaultValue_m37D4F9D561AA6422EEB957A49FDC8B9E1669A48E,
	XRRaycast_get_trackableId_mA844E950A9862ABA13C47395893C18A55C9117AB,
	XRRaycast_get_pose_mADE80A4AABEFCCCB8297186D7E836EAE5B730F73,
	XRRaycast_get_trackingState_mDBA1DEB482B9346E44263E8B2201C1D8AF919B09,
	XRRaycast_get_nativePtr_m79D980249D35343744B394D7238F8A8FB943D484,
	XRRaycast_get_distance_m0B11F8743558DCA40C4E724ECAB8E4DD5ECFFD2B,
	XRRaycast_get_hitTrackableId_m54245AC20302081DF8658019AA0261DEE1E7D6FE,
	XRRaycast__ctor_m0ACF53702D817AC34FD8C21F2C01EF7A8F592F9D,
	XRRaycast_GetHashCode_m94E4A6BDC4CD5E875F40777E273D1E9CD37D54A6,
	XRRaycast_Equals_m7F141CB415FF28341035CBD2B32037DC80469575,
	XRRaycast_Equals_m2A00EBA5AD411F5BFF724BB7D60175FAE69F8D74,
	XRRaycast_op_Equality_mDE1E41B9C70FE2800AB9FDF02F2BCEDAA4F02729,
	XRRaycast_op_Inequality_mB5CAFFF098AE2CC1F9B37CFC3A3150E3990F3A0B,
	XRRaycast__cctor_m88C247D8041D39ED011C71BD40B6F42310EE48DB,
	XRRaycastHit_get_defaultValue_m330E18C6389B68E95AB5CFF26531D5CF5DE6E090,
	XRRaycastHit_get_trackableId_m8B92C0F8977D274743D9388DEB7DCEBCC88E7325,
	XRRaycastHit_set_trackableId_mA41CAE66DB4E6054512F496DABE4C15B6217FA30,
	XRRaycastHit_get_pose_m3B8D69B763A39178CB583948B4E08255FE9A633E,
	XRRaycastHit_set_pose_m26D8C795FDFF7DEE86AB77BC5F0A0B6405150AD4,
	XRRaycastHit_get_distance_m7098B7C90D22697CA37FBBDF50A4109AD055CA80,
	XRRaycastHit_set_distance_m93182B0265D3D34E9D1730860A5B39F515EA729D,
	XRRaycastHit_get_hitType_m30A8013E847E6B2B70A9511B522099C03102E933,
	XRRaycastHit_set_hitType_m89FAB9AF35A52F7CA3F997AE1494EB92B60CA997,
	XRRaycastHit__ctor_mEFB9D7632D78C282C02A913F1E4A2F7866C6B641,
	XRRaycastHit_GetHashCode_m7C9DBAE43B929D3D4BBFF37E15E4E01143BC4A6B,
	XRRaycastHit_Equals_m319801A0EFB8A841B3B7E6197BB612780698759A,
	XRRaycastHit_Equals_mE45E36906807C4F3C5E28C1F54228142D444DA0A,
	XRRaycastHit_op_Equality_m25E9118AC9D0C585769B0A01BB4A0BA09E8355DC,
	XRRaycastHit_op_Inequality_mF0338A41AC3A74E7A0D417552EDD60E87287DA31,
	XRRaycastHit__cctor_m5484BC89F33E0E0E1C846CB135139037484009D8,
	XRRaycastSubsystem__ctor_m883CA69818A86683F768E24C30896A03AB231744,
	XRRaycastSubsystem_GetChanges_mAAF6A88E1636E9143D46216A137FC40065756B36,
	XRRaycastSubsystem_TryAddRaycast_mD0D3CCD94C1EAF43E5DA8130DD35CB2B95785DEF,
	XRRaycastSubsystem_TryAddRaycast_m8B4904E8E52037689CC8A28E952BC532DAC13B8B,
	XRRaycastSubsystem_RemoveRaycast_mD95EEE6441B70215E92C6FE47B8B586F61F5E331,
	XRRaycastSubsystem_Raycast_m8F80A2A2DB7028A18192426BC8A12C1AEE8BAE12,
	XRRaycastSubsystem_Raycast_mA1E217F2C1B58EDE213F651AD6F966E77D67F688,
	Provider_Start_m1857D20E067E44CF3BAD3E96A368619BAB6474FA,
	Provider_Stop_m1B4BADF1B98C07455D3BEEEB7A22FD8444904952,
	Provider_Destroy_mB9445EA71155D4A8A69DA2E3A929FDAF5C4BB3B3,
	Provider_TryAddRaycast_mF21620620C50F046E545FBC7BD33B411C466D0A5,
	Provider_TryAddRaycast_m42D31584099F01A3FC95D884A08C23BAA7515143,
	Provider_RemoveRaycast_m255F5FAC2D9A0E406FDE819156EBC7C11B23DF94,
	Provider_GetChanges_m62ED7DA58CBC28C0C63EDFF72C9C6B7421A3B2B5,
	Provider_Raycast_mB206D5960A72ECCC6BC44AA91719306F790C4854,
	Provider_Raycast_mDB7696CAE66690693DA105DDC40B319A327807E6,
	Provider__ctor_mA6631023676667A3E88702E327B8CCC05F58F678,
	XRRaycastSubsystemDescriptor_get_supportsViewportBasedRaycast_m9B041E253DF77C3584BC482D253746AB8A0B4FDB,
	XRRaycastSubsystemDescriptor_set_supportsViewportBasedRaycast_m1860360556D5F4BBD3B242C93D8C92B3F8FA8D90,
	XRRaycastSubsystemDescriptor_get_supportsWorldBasedRaycast_m06932061F9A99D5B2EDF87E420811B985EBD0F82,
	XRRaycastSubsystemDescriptor_set_supportsWorldBasedRaycast_m2A57C6AF5B8A21E2780A2CDC288F2917DB4BC590,
	XRRaycastSubsystemDescriptor_get_supportedTrackableTypes_mBE54DE9E3A97E78E7EDA4351C259382694864549,
	XRRaycastSubsystemDescriptor_set_supportedTrackableTypes_m99EBD94098B7BD499391301B0099EC9381D32558,
	XRRaycastSubsystemDescriptor_get_supportsTrackedRaycasts_mF4C11D10940AC7D4625AF00094029C4288997BB7,
	XRRaycastSubsystemDescriptor_set_supportsTrackedRaycasts_m9C99240C24C64818B63A368C025689018E8C4044,
	XRRaycastSubsystemDescriptor_RegisterDescriptor_m5F967AE129CABAC92A8774272B77B97D5D52F5E9,
	XRRaycastSubsystemDescriptor__ctor_m95E810A79E27BB9FE0CC927BCBDEE2BFC5A0B011,
	Cinfo_get_id_m14E2737CF1E90C961F8D5B282C17E125018668E0,
	Cinfo_set_id_m962E07A26F49D8C32DAEFEC4F9E0F79EBC128533,
	Cinfo_get_providerType_mB4A72CE35BDA5CCC57B2CCC3E1D88672D9E59021,
	Cinfo_set_providerType_m5D183591B4A3ECA1EC2AA9015C02DCD1EB1F076F,
	Cinfo_get_subsystemTypeOverride_m473983CC8B06F42BC47E92AA261EC2CCBEBFC292,
	Cinfo_set_subsystemTypeOverride_m20C9DAD8677611ACD22689DA249911EEDE46EBF7,
	Cinfo_get_subsystemImplementationType_mF1AF41E2F764A5BF0AF6A35C54C27DE25809AED9,
	Cinfo_set_subsystemImplementationType_m4A20D7E4790C43392F4F82D6B9FAC1D2759F744C,
	Cinfo_get_supportsViewportBasedRaycast_mBAE62868186C81B758E7B9B87F3C67F6C1586700,
	Cinfo_set_supportsViewportBasedRaycast_mC7DB604D2E288A145177EC66F8C1DAA50F38693F,
	Cinfo_get_supportsWorldBasedRaycast_m6D9743F420975015239E33D6641CA95A092D501D,
	Cinfo_set_supportsWorldBasedRaycast_m05D5BC11896AED796A6E237BBB98FA4EF3CF17A4,
	Cinfo_get_supportedTrackableTypes_mA59CFA06B5968E2DF45075C6C40E0A776CC19488,
	Cinfo_set_supportedTrackableTypes_m319BC9C9EB554C35CD40B791917A603D63BEEE0A,
	Cinfo_get_supportsTrackedRaycasts_mF9B448767ADE954E8357D686935ACCF3208DB1B8,
	Cinfo_set_supportsTrackedRaycasts_m7E65DAEB9ED0CB4C4C83DB751C1547145949AA41,
	Cinfo_GetHashCode_m6CC66C9C4BA7904DF1E9E9D7A3C74DC8D6A6C1BA,
	Cinfo_Equals_m4D9FFB9CFA4DF03E9AF2763D3E91926896FCD64D,
	Cinfo_ToString_m3022468C4555B097321DCDB08B4079DA430EBB3A,
	Cinfo_Equals_mD2E403B4E791DCE658297F8CA484149EB2D5F5B8,
	Cinfo_op_Equality_m000A059C7529B861CDD3CEA698B9E25147554158,
	Cinfo_op_Inequality_m558E90C0C00847084F18453CE73079ED4B2EF7C2,
	ScopedProfiler__ctor_m652B5689DE1A3C3EF7D12801DA27FA3B40E4412F,
	ScopedProfiler__ctor_m1F4C2F43E028839CDD9B09EB51402C6F706431B5,
	ScopedProfiler_Dispose_m7B646405B4E52CC4677329D3B860BE9C17A9DAC4,
	NULL,
	NULL,
	NULL,
	NULL,
	SerializableGuid__ctor_m0F2435157FEC8427E91A7D0DD39960BADE7209F0,
	SerializableGuid_get_empty_m4E3F843DBDDCC5A4B19A19FBDF2F9B53EEAA0073,
	SerializableGuid_get_guid_mC9C573E5730B2B18F6DFA80F0BCFD1A097C362B3,
	SerializableGuid_GetHashCode_mC33B7B6D908B3A62767C19B331620784F1998D07,
	SerializableGuid_Equals_mEB4A1B39DD600CB499AC43BF60A3BD206A1EFD71,
	SerializableGuid_ToString_m4FB29C69FF91DC2020A96C3C83FE1B60F9C73047,
	SerializableGuid_ToString_m66A8E16F22314214DECE08D94A189101A421603E,
	SerializableGuid_ToString_m514BCF03CE14CE663D9ECC9616DD28453334BE96,
	SerializableGuid_Equals_m7096244EB28310B3CB17CD79EE7068768C6AB4F7,
	SerializableGuid_op_Equality_mC3A84DAC77870811207A9D06CF5DF9C145EF400F,
	SerializableGuid_op_Inequality_m563816F2887AEBAB0CD1F8DDFD08282B1DB4B3AF,
	SerializableGuid__cctor_m99C1CBC863F8F315793500688464404564D5722B,
	SessionAvailabilityExtensions_IsSupported_m9105265F71A68B18269095FFE362CD24148F6E7C,
	SessionAvailabilityExtensions_IsInstalled_m5AA4AE58BC0BA7C30E77B1B06038BED076ABFFDB,
	XRSessionSubsystem_get_nativePtr_m412275A9382FB5E0105A798037F322FF92DBB46E,
	XRSessionSubsystem_get_sessionId_m7A0FCE3B70A2E1F9C1732E0285EDFECFB8C30685,
	XRSessionSubsystem_GetAvailabilityAsync_m30808B871A7C1BC1BAC7EE85B0F36DB4422769BF,
	XRSessionSubsystem_InstallAsync_m7E278495740EB305D92109D0FFE0895B774D3DA7,
	XRSessionSubsystem__ctor_mB926772E21C65B0CBCE36FD4610CEC1B677999C8,
	XRSessionSubsystem_Reset_m7413F6A15AA7A69B40F473129325FC71593ABAA6,
	XRSessionSubsystem_DetermineConfiguration_m0A5C3F6A14CB8F2D74D5162CCCD5FBD3D51F58A5,
	XRSessionSubsystem_Update_m25DA803713B0800B1C39326D71817305C6A38749,
	XRSessionSubsystem_get_currentConfiguration_m1AB222F75B6C1645DB3405CB9AE18740EE66F0E5,
	XRSessionSubsystem_set_currentConfiguration_m4299FA74038DD6293A125B425179E4C948D09917,
	XRSessionSubsystem_get_requestedFeatures_m2193DB34D2CC44D20701580C9FA062EA111CFCE3,
	XRSessionSubsystem_GetConfigurationDescriptors_m437FE62BDE0C6F37AC5A75C1A11674AB403147F2,
	XRSessionSubsystem_OnApplicationPause_mE4BBAAC4555EA3AAF9B8113406DC920054A1307D,
	XRSessionSubsystem_OnApplicationResume_mCECA16418E8ECAFFCAEDE50D80364B4709A0D488,
	XRSessionSubsystem_get_trackingState_mC2FE654BEB0240C5C3FF85E31DA35E52F85DB550,
	XRSessionSubsystem_get_requestedTrackingMode_m9C8B343227EE42F5ED9980A26008BBEFA101D49F,
	XRSessionSubsystem_set_requestedTrackingMode_m8BBE750492C85A5D35063E246E1F090D5DF73026,
	XRSessionSubsystem_get_currentTrackingMode_mD059B4FBD0D07A428470FF2677002EA51AA2CFB5,
	XRSessionSubsystem_get_configurationChooser_mF3975E98A8A317F63FF3D89CF3E5865EDE47E760,
	XRSessionSubsystem_set_configurationChooser_m65A4004DB9BA7A2C132DCBA01AD6804509ED5B6E,
	XRSessionSubsystem_get_notTrackingReason_m78C6CAA0D4570B7E410C1D375A3CC0AEEC1AFE5A,
	XRSessionSubsystem_get_matchFrameRateEnabled_m0D73F1F3C3B72ACBA97EDA22A62E69C727D2EDB7,
	XRSessionSubsystem_get_matchFrameRateRequested_m6344C601746F6822B56E362F1FC4C4ABE15D22A4,
	XRSessionSubsystem_set_matchFrameRateRequested_m778FF485C34682FE76BCA4871EB4D1D2F5AF73F1,
	XRSessionSubsystem_get_frameRate_m08D6A4F0F598E99A26747827F0630CFDB922A4E3,
	XRSessionSubsystem__cctor_mDDDD57BFB0E53C4F4D4E49175E2A8202BC118372,
	Provider_Start_m6E563F958F1D15385E35A74FC8A82E6AEA46343E,
	Provider_Stop_m333D402B548DA93070E850B93D06EC4F1FB07026,
	Provider_Update_mED507841F7B7D096FC116F775A144293B59E783A,
	Provider_Update_m7DA03CBB0EBA3A2798DF9681CBDDBCA0AE224C85,
	Provider_get_requestedFeatures_m10A009D1F22281A8E1BDEA2156B6DF78C03BA855,
	Provider_get_requestedTrackingMode_m7B7AC5BD9BAEB3B75E96FCC308EDB19523AF2834,
	Provider_set_requestedTrackingMode_m8E0E2EBDB0E20C7E4B4DDD4BFCEDF3C8009BA2E3,
	Provider_get_currentTrackingMode_m65B8B630C321A5B41ACDC2F01D0DE2FF111CE638,
	Provider_GetConfigurationDescriptors_m66DEAAC3E52B4D710710989049669C7DC7B6D643,
	Provider_Destroy_m8A97A126A24DE21208CC6C23BDA3C462F7DD0311,
	Provider_Reset_mC3F1E5323D153985545F3EDDF20DA73703468FC2,
	Provider_OnApplicationPause_m8FB7F1B59209A522883E89CCFD925A67198E9C90,
	Provider_OnApplicationResume_m744FBE7BF974465A14E7ABD2EBAD3EC7E588B117,
	Provider_get_nativePtr_mB998A97B2BFEC1BB38F64D7B507FA0A1FB9C1B67,
	Provider_GetAvailabilityAsync_m0CC77C1C614055744ADFBB72CA4297D4E51A0EE9,
	Provider_InstallAsync_mC7CC25E00D2B78EF1D9B8615ADF663BDA2DB2419,
	Provider_get_trackingState_m0203345C74B979CB55AC990DA824AD8DE0CA1ADB,
	Provider_get_notTrackingReason_m1D74C45C3CB3C617E7AB3D79C31F0B77CE7ECA32,
	Provider_get_sessionId_m34F4CBEF73037CC97575A15ED53526D5030CCC93,
	Provider_get_matchFrameRateEnabled_m99E9CB391AC8D0CCFEBB9591C6725B2B06E2C5B9,
	Provider_get_matchFrameRateRequested_m227999FA334E01B18CCB64CEDE9683FB6055B6C1,
	Provider_set_matchFrameRateRequested_m7D3DF9134FA202F846C13A322D4E895801E1B19A,
	Provider_get_frameRate_m3D2F14E59249D30B755798C8C0732B0CEFA059E3,
	Provider__ctor_m43661C1B8B6373D456C0CDA35F96AACBAB01290A,
	XRSessionSubsystemDescriptor_get_supportsInstall_m3EE93DA405D5C59B746B621FC30D4CC182BB1E53,
	XRSessionSubsystemDescriptor_set_supportsInstall_m6BF049FADBC0C98964ED4DC227535BB27BBDA6B7,
	XRSessionSubsystemDescriptor_get_supportsMatchFrameRate_m516AB9E3CDBE8002D4EBD036EDFD787C46C5FF26,
	XRSessionSubsystemDescriptor_set_supportsMatchFrameRate_m7E8D8FF23A84B20582BF51BEF07DCDFF325ACDF8,
	XRSessionSubsystemDescriptor_RegisterDescriptor_m93A3C60962B96C0AC75288931A21E2D2C0CF3895,
	XRSessionSubsystemDescriptor__ctor_mB1B365F156769946A415E3A37A4FDDE167D5D5B7,
	Cinfo_get_supportsInstall_mEEEDF86E5DE1B7515989BAECAF2F1714A327B720,
	Cinfo_set_supportsInstall_mA4CF39BDB54C42BC8CBD401F6A77353B59EB074F,
	Cinfo_get_supportsMatchFrameRate_mD86213A3EA6096133BF355DDDC55823027B48B7C,
	Cinfo_set_supportsMatchFrameRate_mC501E193696EC21EF655C7B789CABFE2D7D2B3E4,
	Cinfo_get_id_m20097DC1BBE19C329FD6F99312B6B5CD14678921,
	Cinfo_set_id_m7ABF4746E3D8D5C9F5E845638AB861FFBF665594,
	Cinfo_get_providerType_m89ED3FB1640690CFD1735E14FB7834A2BF9203EC,
	Cinfo_set_providerType_m2B27139CECCAF21A444D7891124893D11B7B6484,
	Cinfo_get_subsystemTypeOverride_m5A1888DCB068C90C7C5197246602893767C5A2B7,
	Cinfo_set_subsystemTypeOverride_m5DD40EFEE5ADFC4A6624E1EF7345F63D66027423,
	Cinfo_get_subsystemImplementationType_m1E7C284EE29C67952C8742FD257B3BA09F84D8B3,
	Cinfo_set_subsystemImplementationType_m116B6D5857F561E8119812F01089CDDC08AFF3CA,
	Cinfo_GetHashCode_m2D7B3F90910D284E6D8696F3E187E61436768107,
	Cinfo_Equals_m0C051CCCCA3A44F0C851768FE1CDB49DAC6D82B6,
	Cinfo_Equals_m053CC1A9E8D4DBF1FAFACF66083B615EF36CA572,
	Cinfo_op_Equality_m7F2BC979F66951435B1D4904A6EB885A0F1D0020,
	Cinfo_op_Inequality_mDB236737C959482D1C58D01820447D302C320FB8,
	XRSessionUpdateParams_get_screenOrientation_m5BD0BD187D579592C71665C78BB09685F08BB23C,
	XRSessionUpdateParams_set_screenOrientation_m95E8C1C9AEEFCD0577AE4605645FA8CD8F1D6B9B,
	XRSessionUpdateParams_get_screenDimensions_mED2BC29E3B820C5CF96ED275DFA172B23EA52119,
	XRSessionUpdateParams_set_screenDimensions_mC99924339E008CEFCF202EC394463F00DF0DA4B3,
	XRSessionUpdateParams_GetHashCode_m735A861B2C2718DBF5588467EC76FC6EC77EFE8D,
	XRSessionUpdateParams_Equals_mE2FA6A03BEBC662F543FA73D25561369FAEE7EAF,
	XRSessionUpdateParams_ToString_m82D102D9405D9B5FCA96E55074C982F225287D80,
	XRSessionUpdateParams_Equals_mEC4D21B1DFB2DB2327FCE21B43C144DD2003828C,
	XRSessionUpdateParams_op_Equality_mF18D3DF08FBD72740EB348A62B5077A71261B650,
	XRSessionUpdateParams_op_Inequality_mC310D93D46B42674C8EBF7938945E4AC7B7000D6,
	TrackableId_get_invalidId_mDAEC47FD9C1E08B9D5752DA8F185E7A783DBE494,
	TrackableId_get_subId1_m1F4296FEADE76DF0379F20BCBD94E807E2EF021F,
	TrackableId_set_subId1_mFA12049C24961BC2FE7D41A2D0FE30DF4B3F39D2,
	TrackableId_get_subId2_m53BAB4D373B736E473381B24CB608EEF666BA24E,
	TrackableId_set_subId2_mB7AA91412C0731CF59A8CC24CF75012D3C77C76D,
	TrackableId__ctor_mB12C56ADDEFA44578A429DDA57A6C78B833B41F5,
	TrackableId__ctor_m75F2739A83A25E2B7C34DE87E85187F79A4C86AF,
	TrackableId_ToString_m4BE1AD91726751D994E6FB864B231BE5D7D3F85F,
	TrackableId_GetHashCode_m6150BF091C3C17A84021CC18B443D5C297F89537,
	TrackableId_Equals_m67C98169A04DB96CCEBC08A05B3FF9544B52C3E5,
	TrackableId_Equals_m7263BB158392C4F1B57BEE16D4F1FBBCF01E4A6E,
	TrackableId_op_Equality_m9E51E31C58CA710A1BD2E3AE1D2286E2FE5B3529,
	TrackableId_op_Inequality_mBEED941F67FCF17377C6F01882CBA551B47246D4,
	TrackableId__cctor_m0233247C572757642543FD7F9D7AFFA432EC916F,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	XRCpuImageAsyncConversionStatusExtensions_IsDone_m7AD981DD5E7F21BC4ADAD35955B9EBB1C52825EA,
	XRCpuImageAsyncConversionStatusExtensions_IsError_m699680413B1419513A165D4BAB6481512B5A5E50,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	XRTextureDescriptor_get_nativeTexture_m1E27C0E1DC11DDC6139178509EE91B8DF54DBAD4,
	XRTextureDescriptor_set_nativeTexture_mE5EF6CBBBE13191EF65501EC9A45C2F64964B27D,
	XRTextureDescriptor_get_width_m570472F03994BC63F21751414105A2E0C112DBF2,
	XRTextureDescriptor_set_width_mA5D674B5378CB5B8AADD7A93E027CBF4BD27A37C,
	XRTextureDescriptor_get_height_mC0B37241C24FA883E2594B9411080EDF654E3E01,
	XRTextureDescriptor_set_height_mCD63667233B39883DF1E431446ED926AC3AF3992,
	XRTextureDescriptor_get_mipmapCount_m4B2ED0D6EBE06AD86E356203B4AB5DE3807C1D31,
	XRTextureDescriptor_set_mipmapCount_m7B8AAB937C5157B15A280672BC5C105FAF30D7E8,
	XRTextureDescriptor_get_format_mA745AA87046D4FE4846C11B8285B980FF6DDDD1A,
	XRTextureDescriptor_set_format_mAB9FB1797A83CC68AC222A861C185FE2F8035058,
	XRTextureDescriptor_get_propertyNameId_mF5A620F0DAEE746BDD293DB7F02909FB5404DCC1,
	XRTextureDescriptor_set_propertyNameId_m4D99BAF8AF884D653834D29D124F106A4AD7189D,
	XRTextureDescriptor_get_valid_mBEE2CC268CC8773618BAB7794118746E235A6761,
	XRTextureDescriptor_get_depth_m5885EBF7D767C918B1483D63D1B11EE60D939E7D,
	XRTextureDescriptor_set_depth_mD62E28995B11B8631C2DF7B02416A2D310F35C49,
	XRTextureDescriptor_get_dimension_mAEB2447102404A845F9B20317A2AB82B956E4A12,
	XRTextureDescriptor_set_dimension_m75DC4703441BF9E812D18C0DFBF0A9839A52554B,
	XRTextureDescriptor__ctor_m32EAA2098F51625289A1BFEFFAC002BA9F274ACF,
	XRTextureDescriptor_hasIdenticalTextureMetadata_mB4DA1A4CFF4ABB66F8FF3AF1F310E60BA1B3F872,
	XRTextureDescriptor_Reset_m1BE8024830BA7AFB94AAD01731FDB449DD12A01F,
	XRTextureDescriptor_Equals_m4931F85C225CAC63EC71FBCE246204E244B6CA2B,
	XRTextureDescriptor_Equals_m42127F01DF3CDEA1F38CF07E6057E8AD9E6F4570,
	XRTextureDescriptor_op_Equality_m01CB132C4DCE6AF54CEDD38CD672D4B6EC539C34,
	XRTextureDescriptor_op_Inequality_mE62A303B6BBF10B27B1A15796892656B4271225A,
	XRTextureDescriptor_GetHashCode_mFEB456F0A0985232D0E342B8F10669149F190012,
	XRTextureDescriptor_ToString_m452F36D253986001921C5F627E67E2452D685493,
};
extern void XRAnchor__ctor_mDD8A7F48E03A25972AA93D2C89C1D773635CA15B_AdjustorThunk (void);
extern void XRAnchor__ctor_m3C8D3F14E6CD1FC66CB5E27096253B81BAA4C75C_AdjustorThunk (void);
extern void XRAnchor_get_trackableId_m0F50E81D0152D0BA4152EF9B66F648EF9FC664AE_AdjustorThunk (void);
extern void XRAnchor_get_pose_m2347783C1262EEFBC0B817EF0357FA4BB4BF053F_AdjustorThunk (void);
extern void XRAnchor_get_trackingState_m6124A26C36CA93C25C57548576CB00C1F496ED83_AdjustorThunk (void);
extern void XRAnchor_get_nativePtr_mC0551FA7E8DB8A0DA1EAE02D9B0BFD9D47389C26_AdjustorThunk (void);
extern void XRAnchor_get_sessionId_m719628E8A58027C75FF2CEA3345DC41200FB5F76_AdjustorThunk (void);
extern void XRAnchor_GetHashCode_mEFA5E37600C1A0B56F911227326704C17C3B5400_AdjustorThunk (void);
extern void XRAnchor_Equals_m8F3408527C2CF86CF0A09AE74BF790F8E60ED6F1_AdjustorThunk (void);
extern void XRAnchor_Equals_mFD12F373615A9015CB110787F6FF06CDAAC1433F_AdjustorThunk (void);
extern void Cinfo_get_id_mFF66DF9642403D18476451C30BD5F2FC89CAF8B1_AdjustorThunk (void);
extern void Cinfo_set_id_m6344F3700C1D743D82AB9E74925F7687925734A6_AdjustorThunk (void);
extern void Cinfo_get_providerType_m2D11E14B5E4C48474C00C579D4C2F5F45970D70B_AdjustorThunk (void);
extern void Cinfo_set_providerType_m0E291C22B0B2CF634024F32ECA2E424F18157ACF_AdjustorThunk (void);
extern void Cinfo_get_subsystemTypeOverride_mD1DEE5FBF656FDF4BF5E28B60C04F53B6D146C3B_AdjustorThunk (void);
extern void Cinfo_set_subsystemTypeOverride_mD5C5CAAC167444FE1BD48C1A16AA904E8DFF7052_AdjustorThunk (void);
extern void Cinfo_get_subsystemImplementationType_m9F7B46950CE7957D33333EBBBAA65B27407831FA_AdjustorThunk (void);
extern void Cinfo_set_subsystemImplementationType_m97E16A222C068284404DA8CC6C828D34E2F15265_AdjustorThunk (void);
extern void Cinfo_get_supportsTrackableAttachments_m2BA4A0E85635C4D85059CDF713EE7FC21F80DBF4_AdjustorThunk (void);
extern void Cinfo_set_supportsTrackableAttachments_m58F12783B1EF42ED420CC99E0585FA6ED046C4AD_AdjustorThunk (void);
extern void Cinfo_GetHashCode_mE063F9FDF568ECC85F4D30998CB9A071A82C3859_AdjustorThunk (void);
extern void Cinfo_Equals_m11F554099FC7163A8405E15A238CD1084BCCB65E_AdjustorThunk (void);
extern void Cinfo_Equals_mD8F45C05DFDE73ABDD26DC002B6F0B1506149F6D_AdjustorThunk (void);
extern void XRCameraConfiguration_get_width_mCEA441DFABEDE3E552A2D4452508BCE923B6C3C6_AdjustorThunk (void);
extern void XRCameraConfiguration_get_height_m9130BF72BE684B67C2100DD1624AF851E42B81A8_AdjustorThunk (void);
extern void XRCameraConfiguration_get_resolution_m8EB20C15322147BCA971867F394BC0E0EDCB5A0D_AdjustorThunk (void);
extern void XRCameraConfiguration_get_framerate_m3BFA6E6FB947828EDC20AC9CED31391634F5EB6F_AdjustorThunk (void);
extern void XRCameraConfiguration_get_depthSensorSupported_m05B25DB3D4E83E385BE419109288B77684532A53_AdjustorThunk (void);
extern void XRCameraConfiguration_get_nativeConfigurationHandle_mD9C92AE35660E0441A296301336578BA11313155_AdjustorThunk (void);
extern void XRCameraConfiguration__ctor_m6A41DA8E8540120FABB6436C2DCB07B6BC520281_AdjustorThunk (void);
extern void XRCameraConfiguration__ctor_m2943E6AD678C2106CF20EA8CA967F2EAB15303FB_AdjustorThunk (void);
extern void XRCameraConfiguration__ctor_m0C8EC1223917D880B58850B06E1DC4902F269C4C_AdjustorThunk (void);
extern void XRCameraConfiguration_ToString_mD69E2A39496C4B88A7089AF64A13057585A6F159_AdjustorThunk (void);
extern void XRCameraConfiguration_GetHashCode_mE50DD8C034ED9415443191DF89F044B05510CEB7_AdjustorThunk (void);
extern void XRCameraConfiguration_Equals_m26024336DA6F68CDCBF7916F6B6BF690DF152FA5_AdjustorThunk (void);
extern void XRCameraConfiguration_Equals_mCFE381E6FB6B3650DCAB79FB6894DA8BB511A708_AdjustorThunk (void);
extern void XRCameraFrame_get_timestampNs_m93571F53415C7DC6195854F3297E95D2E55A4DFB_AdjustorThunk (void);
extern void XRCameraFrame_get_averageBrightness_mD1106801D777BFB9EE60FDE5DE194EBACEFB6071_AdjustorThunk (void);
extern void XRCameraFrame_get_averageColorTemperature_m29B8FBE0061F8895678D3C4DA5BAB7BDBE154D4E_AdjustorThunk (void);
extern void XRCameraFrame_get_colorCorrection_m00236A30115F91E2696EAAAF6F1CDF9CA0F83354_AdjustorThunk (void);
extern void XRCameraFrame_get_projectionMatrix_mDE497D5208A1D08226B6B6C7521F53125E6EB9BD_AdjustorThunk (void);
extern void XRCameraFrame_get_displayMatrix_m221E85929B55C0B8F6AB494FF27CC3200A80F287_AdjustorThunk (void);
extern void XRCameraFrame_get_trackingState_mA6E95E5F574FC6506C0F602E430C42763797779A_AdjustorThunk (void);
extern void XRCameraFrame_get_nativePtr_m4DED9CE0A7333F6A1E5C4932A6C98A8A0DD9E62D_AdjustorThunk (void);
extern void XRCameraFrame_get_properties_m0C853765A7C76148A439A2C275E3687659DD8DFB_AdjustorThunk (void);
extern void XRCameraFrame_get_averageIntensityInLumens_m639F0315B64DA5EC8321609C8673EA14A7263115_AdjustorThunk (void);
extern void XRCameraFrame_get_exposureDuration_m4D8412C33F590A282E1671AFD89CC543837BD007_AdjustorThunk (void);
extern void XRCameraFrame_get_exposureOffset_m9683C51CB26F830F5FC5720AD0FD541EC053202E_AdjustorThunk (void);
extern void XRCameraFrame_get_mainLightIntensityLumens_m55B353C41D7A9E00F596D4BAD4813793C9754BE8_AdjustorThunk (void);
extern void XRCameraFrame_get_mainLightColor_mE96BCE9B4E4E241AF9F6BC758942EB8D4584138C_AdjustorThunk (void);
extern void XRCameraFrame_get_mainLightDirection_mF33683D8BF23ADFB8EBC2D2875C2EDF5922F21B8_AdjustorThunk (void);
extern void XRCameraFrame_get_ambientSphericalHarmonics_mB62D6BDCC0A0DAAB5C057225A84289502076EFCE_AdjustorThunk (void);
extern void XRCameraFrame_get_cameraGrain_m7FF742DB5555C9D84DCD7937828C612FAACFEEFE_AdjustorThunk (void);
extern void XRCameraFrame_get_noiseIntensity_mA1D17EA6D00D7FF958FFF6A62B99B34B052F2FEC_AdjustorThunk (void);
extern void XRCameraFrame_get_hasTimestamp_mD6AD6768B71946B0643836ACD28BF32876A5E0FF_AdjustorThunk (void);
extern void XRCameraFrame_get_hasAverageBrightness_m8CC4709AA168C8762763837B384B7332FC2B73B0_AdjustorThunk (void);
extern void XRCameraFrame_get_hasAverageColorTemperature_m163AF5FAD20B5779A28550ED502F5037C4BDB93A_AdjustorThunk (void);
extern void XRCameraFrame_get_hasColorCorrection_mCEB8BC23DF1997AB5DFCD013F56111FB8A8D118E_AdjustorThunk (void);
extern void XRCameraFrame_get_hasProjectionMatrix_m850BCDBFBBD894BF56EEED3A82349A4E4811CC1F_AdjustorThunk (void);
extern void XRCameraFrame_get_hasDisplayMatrix_m7D5DA2AA4F3C83B25714C0FED9EEAE1E51B95959_AdjustorThunk (void);
extern void XRCameraFrame_get_hasAverageIntensityInLumens_m7E14C289B8D931F55B7A98D5075263E96CE3B4DE_AdjustorThunk (void);
extern void XRCameraFrame_get_hasExposureDuration_m02C1ACB25E72D090C9A56FC158E8D4B0D3C04D50_AdjustorThunk (void);
extern void XRCameraFrame_get_hasExposureOffset_m6A4048142BD1E59E403F858144092C5F7846CA53_AdjustorThunk (void);
extern void XRCameraFrame_get_hasMainLightIntensityLumens_mA423D7DEF78D1888AFED8BF17B3E1037C24E469B_AdjustorThunk (void);
extern void XRCameraFrame_get_hasMainLightColor_m07A53E75212D8BA3582613228AC0DACBDDF983FF_AdjustorThunk (void);
extern void XRCameraFrame_get_hasMainLightDirection_m67DFB7C0DAD130D98290130131EDC4BA62818B5E_AdjustorThunk (void);
extern void XRCameraFrame_get_hasAmbientSphericalHarmonics_m45F02EFE6E47FC9B9AEE4F1B6AEC4F9E7FF1F92A_AdjustorThunk (void);
extern void XRCameraFrame_get_hasCameraGrain_mC37056CCCDBEFD620038107A078B6A39F61D99AE_AdjustorThunk (void);
extern void XRCameraFrame_get_hasNoiseIntensity_m076641BB06432F1F27EFD353B6E7116B098BE4B7_AdjustorThunk (void);
extern void XRCameraFrame_get_hasExifData_mB7025B1AA39F37A2DAB00F455D9803096B12DF24_AdjustorThunk (void);
extern void XRCameraFrame__ctor_m7A19EA8CCC1391BE7463FF9B492863BDA975E90B_AdjustorThunk (void);
extern void XRCameraFrame__ctor_m991480EB7E8C7C3A8C63974AFE3FD8900A2EBC03_AdjustorThunk (void);
extern void XRCameraFrame_TryGetTimestamp_m60FE1777D7379C288482A23E5A7C5B297F1DDD94_AdjustorThunk (void);
extern void XRCameraFrame_TryGetAverageBrightness_m1FDC9DCF0A34227DC5ECB78A5E80E614F0063C98_AdjustorThunk (void);
extern void XRCameraFrame_TryGetAverageColorTemperature_m2737123C8E46EF119C04162FAD8EAA312FA2EF3B_AdjustorThunk (void);
extern void XRCameraFrame_TryGetProjectionMatrix_mC25F35BF17829DBEDA748D61362289E4842B5098_AdjustorThunk (void);
extern void XRCameraFrame_TryGetDisplayMatrix_mA034AD550B172CACDDED708A12FFBA548E750312_AdjustorThunk (void);
extern void XRCameraFrame_TryGetAverageIntensityInLumens_mE5FC24C1E3D49FB679137698834F22BF258DD674_AdjustorThunk (void);
extern void XRCameraFrame_TryGetExifData_m99AAC21B7048242487A8D48DE8D92AC9F4C53AF4_AdjustorThunk (void);
extern void XRCameraFrame_Equals_mA1542DDF01588CB1AEDFEB763F63684C18B717C8_AdjustorThunk (void);
extern void XRCameraFrame_Equals_m53FA29D21C4E68E89D59E8904EF7222571C04A50_AdjustorThunk (void);
extern void XRCameraFrame_GetHashCode_m9392ED676A0D41F404CF03A0C570068AC2832352_AdjustorThunk (void);
extern void XRCameraFrame_ToString_m4825A21E18219B4B626C1A2CB3EDD006DFCA44AA_AdjustorThunk (void);
extern void XRCameraFrameExifData_get_hasApertureValue_m2C361DC00694AE380ABE97C9A99F6B645CC8F53F_AdjustorThunk (void);
extern void XRCameraFrameExifData_get_hasBrightnessValue_m39C2542ED3A9A2F1BA136FE4EE381DA0C21ED23D_AdjustorThunk (void);
extern void XRCameraFrameExifData_get_hasExposureTime_mF0CE4E1C0E88FF4D3B727E72DA507117A252CC0C_AdjustorThunk (void);
extern void XRCameraFrameExifData_get_hasShutterSpeedValue_m3AC9A6CD82AAC78E5E74D1AA654CBCC0D7F7B2C4_AdjustorThunk (void);
extern void XRCameraFrameExifData_get_hasExposureBiasValue_m2A069367FABB0671F888CDEDEC473BE1841211E2_AdjustorThunk (void);
extern void XRCameraFrameExifData_get_hasFNumber_mFCA781B3A5BE10C965D76A608B49CDE619ED46F4_AdjustorThunk (void);
extern void XRCameraFrameExifData_get_hasFocalLength_m0107A9D19FE993FB4836FB1EA5BF17656F2FDEF0_AdjustorThunk (void);
extern void XRCameraFrameExifData_get_hasFlash_m6CFD79284C340A886C21336708FE674F6EF80254_AdjustorThunk (void);
extern void XRCameraFrameExifData_get_hasColorSpace_m8B09CAAB1A79D588F04F3D0E26F42261B87EFE7F_AdjustorThunk (void);
extern void XRCameraFrameExifData_get_hasPhotographicSensitivity_m8F78AC08D6DFE157D1B1D40D1D06517CAF6FFB1A_AdjustorThunk (void);
extern void XRCameraFrameExifData_get_hasMeteringMode_m62016BF52989FE5849139AFB087F6C0244517D7C_AdjustorThunk (void);
extern void XRCameraFrameExifData_get_nativePtr_mCB1987D3B4CABB2C972BE7D4E92365763550783E_AdjustorThunk (void);
extern void XRCameraFrameExifData__ctor_mC632ECB5B132C6297A8641A5061024B71F850242_AdjustorThunk (void);
extern void XRCameraFrameExifData_TryGetApertureValue_m4F59F7DEF4992F533DE48D18DBBB0A01EB0E684C_AdjustorThunk (void);
extern void XRCameraFrameExifData_TryGetBrightnessValue_m3C19A37A3534F7E451759E89578260FADBC9B14F_AdjustorThunk (void);
extern void XRCameraFrameExifData_TryGetExposureTime_m46BBCA6CA64E217ED60F3D878983314A59C6D5FA_AdjustorThunk (void);
extern void XRCameraFrameExifData_TryGetShutterSpeedValue_mC955FAA772EC0893B3D06E4ECEA8EE80023FE429_AdjustorThunk (void);
extern void XRCameraFrameExifData_TryGetExposureBiasValue_m52A2F65945A75DAC6F0BD013EB94DF65D5A6BF0C_AdjustorThunk (void);
extern void XRCameraFrameExifData_TryGetFNumber_m7512F213AD901E6FCDCDF7FD9A582AB322F6063D_AdjustorThunk (void);
extern void XRCameraFrameExifData_TryGetFocalLength_m0109D48BD14F7E853514F66ACD493DD1657EC138_AdjustorThunk (void);
extern void XRCameraFrameExifData_TryGetFlash_m8BE2090E79E851BA4961115DC320CEB3B6A2A4AB_AdjustorThunk (void);
extern void XRCameraFrameExifData_TryGetColorSpace_mDD98A093493C3E1659125B797F0E8C49E2C25E76_AdjustorThunk (void);
extern void XRCameraFrameExifData_TryGetPhotographicSensitivity_m215A7867030DC8738CFDF104E0CE9DA2569A6307_AdjustorThunk (void);
extern void XRCameraFrameExifData_TryGetMeteringMode_m3DE1E2935F15F9F91899A58E0585C3CB1E7BFE34_AdjustorThunk (void);
extern void XRCameraFrameExifData_get_hasAnyProperties_m608D0509C48BB26408E3B58ADE152ED372101624_AdjustorThunk (void);
extern void XRCameraFrameExifData_Equals_mE16880FA32EC214A99E76AA88BC65988E6A61919_AdjustorThunk (void);
extern void XRCameraFrameExifData_Equals_m55925A51CE7F4B028CF9B77393BD8FCCB56F7302_AdjustorThunk (void);
extern void XRCameraFrameExifData_GetHashCode_m3FB9ECCBAE6452B72A6B167F589098899819AFAA_AdjustorThunk (void);
extern void XRCameraFrameExifData_ToString_m9200B453787D807084089B68F941C9B768A8D374_AdjustorThunk (void);
extern void XRCameraIntrinsics_get_focalLength_m9B19B7C0AF4CDAF1C8BA121C20BE8A80A7DF778D_AdjustorThunk (void);
extern void XRCameraIntrinsics_get_principalPoint_m677A9880F319E54576353AD01EF0936317E1D83D_AdjustorThunk (void);
extern void XRCameraIntrinsics_get_resolution_mDC07EA111909E8903F1B89577FA2A6BF8FB14D52_AdjustorThunk (void);
extern void XRCameraIntrinsics__ctor_mA7F2F4A7709FC7DA6E9560367A08C28374365020_AdjustorThunk (void);
extern void XRCameraIntrinsics_Equals_m7C6C306C554F5F2A69E5CB831FB2C38F7A252866_AdjustorThunk (void);
extern void XRCameraIntrinsics_Equals_m81F681CB1C13344784F9B2DD6ACE032F2C9A06AE_AdjustorThunk (void);
extern void XRCameraIntrinsics_GetHashCode_mEC06B793ED903AC34149EAA935C66284947CDF63_AdjustorThunk (void);
extern void XRCameraIntrinsics_ToString_m3C7131BDF5882D7F01DA3EC7A7544A16E7F6A783_AdjustorThunk (void);
extern void XRCameraParams_get_zNear_mECA80F2D2C74318641F94031BB7964DD06ABEA75_AdjustorThunk (void);
extern void XRCameraParams_set_zNear_m13DFECBAE558037DEBE998F3EFF2E1C6372EE6E0_AdjustorThunk (void);
extern void XRCameraParams_get_zFar_mDF023917C3AD6AA2C909A2295219F34B085638DA_AdjustorThunk (void);
extern void XRCameraParams_set_zFar_mA528373BCB66A9DE2A393512B883B932AB02D600_AdjustorThunk (void);
extern void XRCameraParams_get_screenWidth_m791F2E175953698508D73BF5B38087BA66875FDA_AdjustorThunk (void);
extern void XRCameraParams_set_screenWidth_mA367A9BD005F2F052549E9B509F4D41F276CA021_AdjustorThunk (void);
extern void XRCameraParams_get_screenHeight_m560E3D0692A29242E6E137CA8895C3754E8A7745_AdjustorThunk (void);
extern void XRCameraParams_set_screenHeight_m7F6C7A3B7F3D7AEBE074A19FF20EF3DDACB79DE9_AdjustorThunk (void);
extern void XRCameraParams_get_screenOrientation_m0EC129A67B19D30348027E60C9A6C982DBC89D3A_AdjustorThunk (void);
extern void XRCameraParams_set_screenOrientation_m9AA6D552ED0B67E9560CDF2C775FC27AA7A83A07_AdjustorThunk (void);
extern void XRCameraParams_Equals_m5C32A8D9FE83014E8A424C2D09688635E88A86B0_AdjustorThunk (void);
extern void XRCameraParams_Equals_mD063C934A21CE21B40F834E0C90AFF645A236CD6_AdjustorThunk (void);
extern void XRCameraParams_GetHashCode_m1F7C2E3CC02169BDEC645B0CE0E540BB1FE1FCD5_AdjustorThunk (void);
extern void XRCameraParams_ToString_mFD8C6218C724EAAF2F9A953CBB08AA3BBF67DB64_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_get_id_mDCD0C107058AEA702A80B8E305F262CAB8E07FD0_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_set_id_m24A04B94756616FBA387977AF0F6A894D4DC5BCE_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_get_providerType_mFBE1614FF701AC94FE53078962B455A355EA45A7_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_set_providerType_mFE0D3D8FEAF0FFEEA66D0E2C7CABB36944EAD484_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_get_subsystemTypeOverride_m5CD01638A223E6C620ADDDDBA05F7539ED5229C4_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_set_subsystemTypeOverride_m82AD4886D0CE8C4D762AC459630CBE13D024FCD2_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_get_implementationType_mCD8363878940A476D3A3D5CD999A01ED736529F7_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_set_implementationType_m8A37CCAA77FD7B184244763108178DC60BA69D0E_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_get_supportsAverageBrightness_m694C685E738909EABF44140E672CB8176649D7E6_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_set_supportsAverageBrightness_mD5F49B41F00DBC4C531FB5593E572E6A67B1EE77_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_get_supportsAverageColorTemperature_mDCCD0A414E292EAD5FB817C6AC16DE8AF8C7D076_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_set_supportsAverageColorTemperature_mBF21A51F1C4ED4A72AF54588CF9E08DDC92A213B_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_get_supportsAverageIntensityInLumens_mD46BA194C3AC26510694D649673015A450D0F019_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_set_supportsAverageIntensityInLumens_m7FEF058FFE0C0B54E91A8FC8500F88C17B294743_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_get_supportsCameraGrain_m9D63DE92F3FD22536481F75DC9092F3526EB9FF2_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_set_supportsCameraGrain_m3E5D929246B89F7B2EFE1F7E75B9A20C67A556C9_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_get_supportsColorCorrection_m8BEA88F615A8CD5FC068D9F7BE8039C68A110A15_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_set_supportsColorCorrection_m5E9520636C4268DFAFB9E84E5901FD65E14BBB7F_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_get_supportsDisplayMatrix_m33AECE0011BFF7206E0F6A99C3C32ACD28DF09B0_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_set_supportsDisplayMatrix_m0AF4D70253EF5AC5124C747D6F1AF39B6E199B53_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_get_supportsProjectionMatrix_mD62E482D66D23D02FBF1591EFE8CBF6AB2B7AA60_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_set_supportsProjectionMatrix_m5A4C42AD394D5FEA20DB5FE1C045634929839B7A_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_get_supportsTimestamp_m53905529FC20BB4B064986AC5E6586DAF996148F_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_set_supportsTimestamp_m46561CEC2016CA165B4E725395C0E8836C0B46F3_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_get_supportsCameraConfigurations_m107FD5D148D109EDDE9345754995ACF01D7A3F67_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_set_supportsCameraConfigurations_m5425AF5D348E1918644909C45ABA7220D2A8B92F_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_get_supportsCameraImage_m3C11104CBEE0AF690F3A4F9729F78F48E1065970_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_set_supportsCameraImage_mC9312D97D6F2508F8692EF4C40909CBA55F6D769_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_get_supportsFocusModes_m0B2A62A1F5A5603B2BE190A5B5AB0703842E6B51_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_set_supportsFocusModes_m7A495A132ED5160BF69E69CAAA132F83319A3191_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_get_supportsFaceTrackingAmbientIntensityLightEstimation_mA6C68E53BB8242F222104A33EA99EF7AFF754A9B_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_set_supportsFaceTrackingAmbientIntensityLightEstimation_m17EC791181F76254B286308B213F19E085DE36D9_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_get_supportsFaceTrackingHDRLightEstimation_m28390AEBE6EBE78D410C0CB9D8D7DBE237D8DEFF_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_set_supportsFaceTrackingHDRLightEstimation_m022FA258FE1F3E9D1D94D02985C3459298093D3A_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_get_supportsWorldTrackingAmbientIntensityLightEstimation_m85AD9C10C625555A50D39FE3FE5B75E4DFEAC0A4_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_set_supportsWorldTrackingAmbientIntensityLightEstimation_m5F43C3317A17DAFA6ACF9550069FB10F8EB50300_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_get_supportsWorldTrackingHDRLightEstimation_mACCAED13DF9023738352CB5C89D5CBCF01B46899_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_set_supportsWorldTrackingHDRLightEstimation_mF04D46841BB7C8BEF8A17300F6F12491A8AC895C_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_get_supportsExifData_m594585E6727EED9B24E835F2E14109565327F680_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_set_supportsExifData_mBAA63CA8656661FF0D35EB93605744BF7ADCA0B7_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_Equals_mED29F3CB627AF187AC3CB817FC3DC7905B0228A0_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_Equals_m6D8C4679C25B3C93C0BAC84C2E8214DC861285F3_AdjustorThunk (void);
extern void XRCameraSubsystemCinfo_GetHashCode_m9AD75EE7E43274694277A4703099621467E69114_AdjustorThunk (void);
extern void Configuration_get_descriptor_m3C4973351367EA0BD9E48DA1E2201D8803BA8D1E_AdjustorThunk (void);
extern void Configuration_set_descriptor_mBB8354A895DDAD560293EEF81BFFDB4CB30070F0_AdjustorThunk (void);
extern void Configuration_get_features_m704F372E940AF1DB435C1EBFF8E48EAD4E8B3776_AdjustorThunk (void);
extern void Configuration_set_features_m9F397F777C9593646918ECB4AF778336900ED3EC_AdjustorThunk (void);
extern void Configuration__ctor_m4D712D942AEBEF0DA6B5687C1D9CD4E24F0ED4AE_AdjustorThunk (void);
extern void Configuration_GetHashCode_m19DCAAF7939DB5DAAF29A2A4E994D41F66FB73D2_AdjustorThunk (void);
extern void Configuration_Equals_mFC36BD166DE654A704096918BDA1FE9E34A7B7E6_AdjustorThunk (void);
extern void Configuration_Equals_m8D6DE5FC0FAD2DD34D2F3CEF1738FC3A2F131A91_AdjustorThunk (void);
extern void ConfigurationDescriptor_get_identifier_m858F4B730002C1823D283460115DA65C6A46BCB6_AdjustorThunk (void);
extern void ConfigurationDescriptor_get_capabilities_m6A4EF4C0E0FE3671E8564EF13BA2A5B4264CF938_AdjustorThunk (void);
extern void ConfigurationDescriptor_get_rank_mEDFBF5E2173FA84A0695BB01A6A40860794F6FA8_AdjustorThunk (void);
extern void ConfigurationDescriptor__ctor_m79BD6295C5A725B6B65CA3A4281EC801C12B2C41_AdjustorThunk (void);
extern void ConfigurationDescriptor_HexString_mA5D97CE4BCD0DD66455BB9BE281302136382BCD5_AdjustorThunk (void);
extern void ConfigurationDescriptor_ToString_m20EA191A42A1855B5E97CD8949F6AE5B9ACBDF65_AdjustorThunk (void);
extern void ConfigurationDescriptor_GetHashCode_mAD2765B79FFD1806DEA8D927D928C496AAADB411_AdjustorThunk (void);
extern void ConfigurationDescriptor_Equals_mC5F92BBF22292A48CAD47A31EF13F3D5A0DC4091_AdjustorThunk (void);
extern void ConfigurationDescriptor_Equals_m4FAAC4A13BF03211A9C3EB66F65FB48BE334A611_AdjustorThunk (void);
extern void XRCpuImage_get_dimensions_m49AF06CB1BDF89E7C9EC343D3260BD73ECABF414_AdjustorThunk (void);
extern void XRCpuImage_set_dimensions_m67B3C05A3CA2F0CED5B4E1808967FF2BF77AED86_AdjustorThunk (void);
extern void XRCpuImage_get_width_m176240EBEBBD41DC5AEF33F945C88E9492370AFA_AdjustorThunk (void);
extern void XRCpuImage_get_height_m139489AD26B264FA46EE5659258BBF9C6584E5E9_AdjustorThunk (void);
extern void XRCpuImage_get_planeCount_mEDCBE71D55CCC9FDA1B3ED951306875283E37B6B_AdjustorThunk (void);
extern void XRCpuImage_set_planeCount_m51DC647BC967DE5E565AA4ACF66B5B86FE380B13_AdjustorThunk (void);
extern void XRCpuImage_get_format_mB777BBC485ED5A88CD78536F78F43E9795DEEE20_AdjustorThunk (void);
extern void XRCpuImage_set_format_mBC167A4F6985102169436A10C58AF5EBC17B4C1F_AdjustorThunk (void);
extern void XRCpuImage_get_timestamp_mA80E146875C26B8F319B283C20A6BD499AD55B90_AdjustorThunk (void);
extern void XRCpuImage_set_timestamp_m7FF97B03D5A4506993F8119BCB4BC47B185AA8D1_AdjustorThunk (void);
extern void XRCpuImage_get_valid_mFF799BC0D09BF35BD3AEC063FF5558EE2EB6766F_AdjustorThunk (void);
extern void XRCpuImage__ctor_m06AE81550FF74789CD8D66ABBA9B2F3D9D060612_AdjustorThunk (void);
extern void XRCpuImage_FormatSupported_m15D60F33E5EB00039BA41B9C61AE114C1AA6B40A_AdjustorThunk (void);
extern void XRCpuImage_GetPlane_m0C2A7BE6FE964FCF5A82273AA9DA6C135648721B_AdjustorThunk (void);
extern void XRCpuImage_GetConvertedDataSize_mC7AF0A096D1FF758D3E086D3D43F778E9257D4BE_AdjustorThunk (void);
extern void XRCpuImage_GetConvertedDataSize_m1A292AE01390513BEA935CC4C19A0F8FD52341DE_AdjustorThunk (void);
extern void XRCpuImage_Convert_m04EB3992B85AEB87D03C5626EFD0A9C0158AC9CB_AdjustorThunk (void);
extern void XRCpuImage_Convert_mFE71425F0E4FD4ADB839551590FE9728BA037EE1_AdjustorThunk (void);
extern void XRCpuImage_ConvertAsync_mECB96371D5F7C49A4E995B285F9FC02FE4109814_AdjustorThunk (void);
extern void XRCpuImage_ConvertAsync_m881B8A7A185F454AB34151D858CA0AE8A54F2949_AdjustorThunk (void);
extern void XRCpuImage_ValidateNativeHandleAndThrow_mA1A3B64DF91A003BDB013FC04111945F03853395_AdjustorThunk (void);
extern void XRCpuImage_ValidateConversionParamsAndThrow_m39B351E15FD65E6969933F0EBE42CA63DD090E72_AdjustorThunk (void);
extern void XRCpuImage_Dispose_m80B8CA56700DD5EB8A5613AA42F6F389D86A746B_AdjustorThunk (void);
extern void XRCpuImage_GetHashCode_m4C976024EE9CFFDBE53682B8307FD0819F42E31C_AdjustorThunk (void);
extern void XRCpuImage_Equals_mE00DCB100FC7743E62959883CBAF479ADEDCBAC3_AdjustorThunk (void);
extern void XRCpuImage_Equals_m0BD02471E5A85EFF5F078CE0ACEDD4F969B66AB2_AdjustorThunk (void);
extern void XRCpuImage_ToString_mA4BF5B6A1D341098584B0EC3E51D324092AE98ED_AdjustorThunk (void);
extern void AsyncConversion_get_conversionParams_m467C52D7FD3E87614FA34FCB630EDD89289F12B0_AdjustorThunk (void);
extern void AsyncConversion_set_conversionParams_m5144DBB33F2D0003BF4E2ED884B9D8AA4EE89071_AdjustorThunk (void);
extern void AsyncConversion_get_status_mB3A873407AF4E202A39758599DCEE52BEC196E2A_AdjustorThunk (void);
extern void AsyncConversion__ctor_mEEE052FCAD2BF2E9FBF78C829C0A11A6D5CD5ABD_AdjustorThunk (void);
extern void AsyncConversion_Dispose_m81B54378570A2C9C1009618A0380E5C204DD2AC6_AdjustorThunk (void);
extern void AsyncConversion_GetHashCode_mF14F2C21ADCA19EB7300B2F07B59B2758CE0195D_AdjustorThunk (void);
extern void AsyncConversion_Equals_m0D926BE22C3B6333F413DFB6E051021428C6D3D5_AdjustorThunk (void);
extern void AsyncConversion_Equals_m84273ECB152DB76244DFD3BF9BC2AB648682F4A0_AdjustorThunk (void);
extern void AsyncConversion_ToString_m59F4615A570F43A086B43067B37CCF382BEE5225_AdjustorThunk (void);
extern void ConversionParams_get_inputRect_m59986429062905012283B892A6EE2DAD88A810FC_AdjustorThunk (void);
extern void ConversionParams_set_inputRect_m7965864AED4C5176D58F3766D6BBB35DFF7BC903_AdjustorThunk (void);
extern void ConversionParams_get_outputDimensions_m6295F96DCE9B406AB6D79E8CD86A6FF388CF5035_AdjustorThunk (void);
extern void ConversionParams_set_outputDimensions_m97EC09EE536EA456A18894311BF75AC9D5A90A3B_AdjustorThunk (void);
extern void ConversionParams_get_outputFormat_m8CD52ADADE8FFE505A90E02D9BD6C7D9EE1C8715_AdjustorThunk (void);
extern void ConversionParams_set_outputFormat_mA82EA0ECC19D14AECBA318B9B485D08CFB46A1F4_AdjustorThunk (void);
extern void ConversionParams_get_transformation_m46ADA14AEDC98630828D5DCE19F1905233627CE7_AdjustorThunk (void);
extern void ConversionParams_set_transformation_mBCE73B14CCE8A31A258C6B8F6104446A2D495A0F_AdjustorThunk (void);
extern void ConversionParams__ctor_m2EA9FC7BD411FA61269B314ACD03174F5BB96273_AdjustorThunk (void);
extern void ConversionParams_GetHashCode_m15B2EFDD22B43B4201646E49BCB9155F67D5A12A_AdjustorThunk (void);
extern void ConversionParams_Equals_mD0A055A44755C75EFF2B09B2FD7C9C50D057020B_AdjustorThunk (void);
extern void ConversionParams_Equals_mDC60F1518FE83109D22DB3A1606C82930B16356D_AdjustorThunk (void);
extern void ConversionParams_ToString_mB11C93DA6E5D57F694BC5D64E5567958A647557C_AdjustorThunk (void);
extern void Plane_get_rowStride_m5461CF97009BA5CB09931F85D9C4E11BB298E01F_AdjustorThunk (void);
extern void Plane_set_rowStride_m03DE76183744D782EAB661F50D8191DA9CB34A31_AdjustorThunk (void);
extern void Plane_get_pixelStride_m78990A3DB8530B302D4B138E92BFEFF6F6F8D5E0_AdjustorThunk (void);
extern void Plane_set_pixelStride_m26005BF1B5C79A0B816D37C53CF5A34BA3499CFC_AdjustorThunk (void);
extern void Plane_get_data_m8A88D9DDDAB3081E788B3DCF7DE314D2E672B15D_AdjustorThunk (void);
extern void Plane_set_data_mFDEC268CEEE5FEBFC54FE823BEC6B3DCC4DB182E_AdjustorThunk (void);
extern void Plane__ctor_m9563F685C69A49502C62A7EA4F1F64FCC392A485_AdjustorThunk (void);
extern void Plane_GetHashCode_m81E44303AC89B3792D4238BEFF767D459D72FDD1_AdjustorThunk (void);
extern void Plane_Equals_m05599C5BA1316FF0667B8D3752DC2464E559A24E_AdjustorThunk (void);
extern void Plane_Equals_mD97496D640121AA88AF730F5DDE9F1ED6582842A_AdjustorThunk (void);
extern void Plane_ToString_m0844EFBBF3A11852B21C58FCBC543A554E838EE2_AdjustorThunk (void);
extern void Cinfo_get_dataPtr_m0865701DF77079918906809E61CCF8C080120AB1_AdjustorThunk (void);
extern void Cinfo_get_dataLength_mF704FE891CD1628CF48C8434DF1CD5C461A7EE86_AdjustorThunk (void);
extern void Cinfo_get_rowStride_m3CB25349C2380F5FC9022EB25A1FCF95C1498513_AdjustorThunk (void);
extern void Cinfo_get_pixelStride_m5A3C2E9C12F194F7237EF96FC12E319928E14A6E_AdjustorThunk (void);
extern void Cinfo__ctor_mF1859C21D692CA1783BF64CDD8C45BB1984C427F_AdjustorThunk (void);
extern void Cinfo_Equals_m8F4B3A7591D02605076B062B37312733D533D3AA_AdjustorThunk (void);
extern void Cinfo_Equals_mA464FE0F8B24D6AACEE40E5A290572D4ABBE1333_AdjustorThunk (void);
extern void Cinfo_GetHashCode_mDD348F8626D5B49ED6EC593263AFD20A21329F08_AdjustorThunk (void);
extern void Cinfo_ToString_m6CC828632F333B765128A31434D3DFF040F90754_AdjustorThunk (void);
extern void Cinfo_get_nativeHandle_m63F2835811F1DCFF2EE1AEBF8A8F7A1ADA1FD7E0_AdjustorThunk (void);
extern void Cinfo_get_dimensions_m7B777F060E825839302EA722B35E1BBB4E402D2A_AdjustorThunk (void);
extern void Cinfo_get_planeCount_m5D077F0399217E11C6A11378F5D08D86CC5CEA7F_AdjustorThunk (void);
extern void Cinfo_get_timestamp_mFFA3FA7E91717B748F6159B7E78FBFE2290F4E97_AdjustorThunk (void);
extern void Cinfo_get_format_m0F7BD9189DB7D30D44882FF3F53EF2FFBD05C7DE_AdjustorThunk (void);
extern void Cinfo__ctor_mB5890536579096BE14554ED96E868003C9A2CC5C_AdjustorThunk (void);
extern void Cinfo_Equals_mEEFFAFF7E0FE0F0445AF0F96F66D5D68DF1BC3E1_AdjustorThunk (void);
extern void Cinfo_Equals_mBA9B7A52398AB07042615E27D6AC30542F9EF124_AdjustorThunk (void);
extern void Cinfo_GetHashCode_m26AA585D94FCF87B154728D81E40295716B37B03_AdjustorThunk (void);
extern void Cinfo_ToString_m47CB9E0B83E1E5C5CA1517D982A5E6FF4A9F05A3_AdjustorThunk (void);
extern void XREnvironmentProbe__ctor_m756BDCC73762A50BDAF24FD4F430D8F8EA18869D_AdjustorThunk (void);
extern void XREnvironmentProbe_get_trackableId_m7B20AFD8D153397E7270F72C81B32043DA83C57F_AdjustorThunk (void);
extern void XREnvironmentProbe_set_trackableId_mCAD11E54A600B26FDC6D546A15F5E13030605EE4_AdjustorThunk (void);
extern void XREnvironmentProbe_get_scale_m7C53FA5C36BD5616CCF2EDC543C260FD381BCB64_AdjustorThunk (void);
extern void XREnvironmentProbe_set_scale_m230AA2EF5AE396A8E5A43FE809BEEF811CE68302_AdjustorThunk (void);
extern void XREnvironmentProbe_get_pose_m56C2FCB790DC220FAE0339EFC82055360984CAF0_AdjustorThunk (void);
extern void XREnvironmentProbe_set_pose_m1A1776C7D4A99F29708883F081A54936BC46A4B0_AdjustorThunk (void);
extern void XREnvironmentProbe_get_size_m92A310E405DC33AFF0968D0B7C17BDB8D039A1B0_AdjustorThunk (void);
extern void XREnvironmentProbe_set_size_m191D7253226516A2BBC83D0DD28A154FAD2F3C33_AdjustorThunk (void);
extern void XREnvironmentProbe_get_textureDescriptor_mD514443491B53FCBC49AD477CC5C7C6084543FAD_AdjustorThunk (void);
extern void XREnvironmentProbe_set_textureDescriptor_mAE07E8E52FD3D564E1366EC75E0B1EB80A1A43B0_AdjustorThunk (void);
extern void XREnvironmentProbe_get_trackingState_m4051D90D37D33EC33534368B64E5C85EA1888C83_AdjustorThunk (void);
extern void XREnvironmentProbe_set_trackingState_m8AC9F0BB01B0B26935D09B5A723680E658A3A196_AdjustorThunk (void);
extern void XREnvironmentProbe_get_nativePtr_m0C6C620B2D3C20FBE8AEE478EBEA0006E8E7FB40_AdjustorThunk (void);
extern void XREnvironmentProbe_set_nativePtr_m75CED6350AB93167C23B4E1A837879BCEB6A7AAC_AdjustorThunk (void);
extern void XREnvironmentProbe_Equals_m891BD688A67E6AF40E4DE164936AFC6D59762AF0_AdjustorThunk (void);
extern void XREnvironmentProbe_Equals_m76FC4B88F469A7E33C17E4F9A59DBEBDF1A66745_AdjustorThunk (void);
extern void XREnvironmentProbe_GetHashCode_mC8C8046B5523D71CADA65C3D38232925243CEA86_AdjustorThunk (void);
extern void XREnvironmentProbe_ToString_mBD160B7DBD096BB94201C93B1821FF73728C3E4F_AdjustorThunk (void);
extern void XREnvironmentProbe_ToString_mFB69B6A7B36CD0B02B4283AEFF6CEFAA72EE8DB2_AdjustorThunk (void);
extern void XREnvironmentProbeSubsystemCinfo_get_id_m590E5DEBA8C344FFEA51F351F99DEFFD703DC57F_AdjustorThunk (void);
extern void XREnvironmentProbeSubsystemCinfo_set_id_m0157B51BEBA22C10D835283A2E342811FB3904E0_AdjustorThunk (void);
extern void XREnvironmentProbeSubsystemCinfo_get_providerType_mBC6F352EF89BED518336375D44A5D9CADDE464C1_AdjustorThunk (void);
extern void XREnvironmentProbeSubsystemCinfo_set_providerType_m053C0551B3059F11B606708ED8C6B3DA9C4CF2F2_AdjustorThunk (void);
extern void XREnvironmentProbeSubsystemCinfo_get_subsystemTypeOverride_m5A1A5D3ADB4E4304029B3F1688CEBFAB4E4C8F7E_AdjustorThunk (void);
extern void XREnvironmentProbeSubsystemCinfo_set_subsystemTypeOverride_m89507B15871CD2E8F093EF084176BC98CAD3D1E4_AdjustorThunk (void);
extern void XREnvironmentProbeSubsystemCinfo_get_implementationType_m6C1B50EAB87465FE798443F1C2803F1A437EEC3E_AdjustorThunk (void);
extern void XREnvironmentProbeSubsystemCinfo_set_implementationType_m09326FDF5E9024E0AA37F89388F8BDD4AB40E600_AdjustorThunk (void);
extern void XREnvironmentProbeSubsystemCinfo_get_supportsManualPlacement_mFA2560E13D32A8228105D6F205E109DEDE0BFB37_AdjustorThunk (void);
extern void XREnvironmentProbeSubsystemCinfo_set_supportsManualPlacement_mE948D3D31063A4015F18BABCD4475E2CEDD00E2C_AdjustorThunk (void);
extern void XREnvironmentProbeSubsystemCinfo_get_supportsRemovalOfManual_mE1E3D58FB734841B3B1E885592C84A2748953847_AdjustorThunk (void);
extern void XREnvironmentProbeSubsystemCinfo_set_supportsRemovalOfManual_m9784D47E8903EC1BEFA722994586FF633BB09327_AdjustorThunk (void);
extern void XREnvironmentProbeSubsystemCinfo_get_supportsAutomaticPlacement_m01E383AF1BDC43E0755B0C99BF838698B2FAD67C_AdjustorThunk (void);
extern void XREnvironmentProbeSubsystemCinfo_set_supportsAutomaticPlacement_mD728F648E5A75DC074D9718432F07D0D460A289D_AdjustorThunk (void);
extern void XREnvironmentProbeSubsystemCinfo_get_supportsRemovalOfAutomatic_m5C05042091803872B2B5FE45C791A728F92B5A1A_AdjustorThunk (void);
extern void XREnvironmentProbeSubsystemCinfo_set_supportsRemovalOfAutomatic_mBD5F44750877ADEFD8C6207F0B30D0FE15B70526_AdjustorThunk (void);
extern void XREnvironmentProbeSubsystemCinfo_get_supportsEnvironmentTexture_m7B872B5FE1B9517D9626E269E09B7C4C16E06A06_AdjustorThunk (void);
extern void XREnvironmentProbeSubsystemCinfo_set_supportsEnvironmentTexture_m724298B12B4DDEFC4C1274A551E04299A040374D_AdjustorThunk (void);
extern void XREnvironmentProbeSubsystemCinfo_get_supportsEnvironmentTextureHDR_m7AC20590F176681AD486ACC8C9A0189209B1C35E_AdjustorThunk (void);
extern void XREnvironmentProbeSubsystemCinfo_set_supportsEnvironmentTextureHDR_mEED10741684E8045104833D1A58A1A02612FBE4B_AdjustorThunk (void);
extern void XREnvironmentProbeSubsystemCinfo_Equals_m4297DA57D68A4D83010A2D370092CB925B89CEA0_AdjustorThunk (void);
extern void XREnvironmentProbeSubsystemCinfo_Equals_m8AE52E39A349B7A5E21C624AE22C4A1078E8C327_AdjustorThunk (void);
extern void XREnvironmentProbeSubsystemCinfo_GetHashCode_m66A5517BE16EE35C50CBD336B9B2928760E07121_AdjustorThunk (void);
extern void XRFace_get_trackableId_m9FC29FB643FFBAB989AB8179F57CDB52D14737B3_AdjustorThunk (void);
extern void XRFace_get_pose_m1625DED173751F25873C4BB6650238A195CD04EE_AdjustorThunk (void);
extern void XRFace_get_trackingState_m7D5C3002DCB9FC01F7C1BE66D3F1092281E847FB_AdjustorThunk (void);
extern void XRFace_get_nativePtr_mCE3681420B25EA0AE4B5FA1687310DF7D49C0899_AdjustorThunk (void);
extern void XRFace_get_leftEyePose_m5406913BE94DA663C80EA8C555EEC1439C0ADAE3_AdjustorThunk (void);
extern void XRFace_get_rightEyePose_m276AD0EBDCD8B62AAAAA2A33920E2FF1415E769D_AdjustorThunk (void);
extern void XRFace_get_fixationPoint_m2628733EA6C1FEEAC047347DBA08A602B7C88429_AdjustorThunk (void);
extern void XRFace_Equals_m6E2D8C6F4F57BB604AA31EEEAEB06BB64EBFC299_AdjustorThunk (void);
extern void XRFace_GetHashCode_mC17A1126F3ADDDB95C12C3E908353704DCCB14D0_AdjustorThunk (void);
extern void XRFace_Equals_mC82B627F3AA8A164D6AE1A999A5BCB55DD2E2C51_AdjustorThunk (void);
extern void XRFaceMesh_Resize_mD9373FB138642F70F4068345A64B49B64B1A3830_AdjustorThunk (void);
extern void XRFaceMesh_get_vertices_m8B133063FC373FD34B8ECBEE696B3462DC65277E_AdjustorThunk (void);
extern void XRFaceMesh_get_normals_m37A411662D1051785AFC6807E3BBEC0E2B3BB61B_AdjustorThunk (void);
extern void XRFaceMesh_get_indices_m2658965B1B99DF1CF00154D791B580AE71CB136D_AdjustorThunk (void);
extern void XRFaceMesh_get_uvs_m71BF16345717D8B5D8F41C571A8D3152337E0A28_AdjustorThunk (void);
extern void XRFaceMesh_Dispose_m02478E536865BA52126039CCAE5B62E5DE58AECF_AdjustorThunk (void);
extern void XRFaceMesh_GetHashCode_mE6F88C5914358332601C00E22FE0A34A137EC982_AdjustorThunk (void);
extern void XRFaceMesh_Equals_mDE9CF3DB2761831C9E9A72B6C2C3EB1D6D155D6F_AdjustorThunk (void);
extern void XRFaceMesh_ToString_mEA1FF45022C6E287675E27526448295468B2884B_AdjustorThunk (void);
extern void XRFaceMesh_Equals_m56870D4CC9E4BC2D1839D5DEFA77A062C29C97A4_AdjustorThunk (void);
extern void FaceSubsystemParams_get_id_m31A6CBE37287374259BEF3B328FB00B1ED871D6A_AdjustorThunk (void);
extern void FaceSubsystemParams_set_id_m963078C97310E91B43D52A4A682D6A4DC0D9A40A_AdjustorThunk (void);
extern void FaceSubsystemParams_get_providerType_mAC8A9B7FF24C751EF95C8B06D71746709FBAED0F_AdjustorThunk (void);
extern void FaceSubsystemParams_set_providerType_m96D22AB9770398D6C371502A95F86A6F45E17C5D_AdjustorThunk (void);
extern void FaceSubsystemParams_get_subsystemTypeOverride_m15AFA4BED668BB995597E91557337E9B30994467_AdjustorThunk (void);
extern void FaceSubsystemParams_set_subsystemTypeOverride_m113E1A7A0F27FE1A56FF92504E6BDC2FD287846E_AdjustorThunk (void);
extern void FaceSubsystemParams_get_subsystemImplementationType_mB85CD541326A5FBDE4378093FA3735066DBDEFED_AdjustorThunk (void);
extern void FaceSubsystemParams_set_subsystemImplementationType_m3A131EEBF975AA6ADF3A39ABC3ED0980C8D7E024_AdjustorThunk (void);
extern void FaceSubsystemParams_get_supportsFacePose_m68CEF716DBA9D0D75EDA10A87A64A5827C78CA59_AdjustorThunk (void);
extern void FaceSubsystemParams_set_supportsFacePose_mCBD1B88CB04F00F212C4B5B8A11821DDCC5C8353_AdjustorThunk (void);
extern void FaceSubsystemParams_get_supportsFaceMeshVerticesAndIndices_mB1D5078180B153FE76FCA509905D0A79D575689B_AdjustorThunk (void);
extern void FaceSubsystemParams_set_supportsFaceMeshVerticesAndIndices_m8783686211AB86E509EE6538E3CFC9A6CF82BF9D_AdjustorThunk (void);
extern void FaceSubsystemParams_get_supportsFaceMeshUVs_m3734F66B4ADBD0A39FBCAEB927A4409023D6164A_AdjustorThunk (void);
extern void FaceSubsystemParams_set_supportsFaceMeshUVs_m151C1B6BE0BA832416557E90493F04DCF97E6809_AdjustorThunk (void);
extern void FaceSubsystemParams_get_supportsFaceMeshNormals_m194410916CDF0F71EBF2AD9C419155D68F9969B0_AdjustorThunk (void);
extern void FaceSubsystemParams_set_supportsFaceMeshNormals_m0664A1AD48A3B41681B9680F7773D1849FA1450A_AdjustorThunk (void);
extern void FaceSubsystemParams_get_supportsEyeTracking_mC254E7AD7AD126742CC7CFDFDB944CD2AC0C0FBD_AdjustorThunk (void);
extern void FaceSubsystemParams_set_supportsEyeTracking_mFEB7DACFE5B6DA3EB37AED2C3428A1CEE0E0DBDE_AdjustorThunk (void);
extern void FaceSubsystemParams_Equals_mD22C227C324E8205B6ACA8F6C625C62F58224D3A_AdjustorThunk (void);
extern void FaceSubsystemParams_Equals_m4AA3EAA9779EDA380AD89EA9BA7E1865B0BE6017_AdjustorThunk (void);
extern void FaceSubsystemParams_GetHashCode_m4C9D85CB7B820FC7E57A5B214D85EDDBD6D5499D_AdjustorThunk (void);
extern void XRHumanBody_get_trackableId_m7CC5B8BB5179303ED1424ACDC46FBFA16C30B2FD_AdjustorThunk (void);
extern void XRHumanBody_set_trackableId_mCE04EA8307BC1B6670AE915575E5297103620E87_AdjustorThunk (void);
extern void XRHumanBody_get_pose_mE154F73E48997BDB6828FE12D7116E93E4D4BBCF_AdjustorThunk (void);
extern void XRHumanBody_set_pose_m036F9C1AB8DA4836D85CF15256C0FF6C83E8B712_AdjustorThunk (void);
extern void XRHumanBody_get_estimatedHeightScaleFactor_m455E9FD1B289BA71C5FEE2A67D72EEE10727246B_AdjustorThunk (void);
extern void XRHumanBody_set_estimatedHeightScaleFactor_m857381C931D5F597AE28A4BD96E8225DE2250693_AdjustorThunk (void);
extern void XRHumanBody_get_trackingState_mE53C1B287B5BD8E021FCAC0E4550C0D551C0F79A_AdjustorThunk (void);
extern void XRHumanBody_set_trackingState_m470B4AD877F377DB7B85F736D80EC5FB2AD39187_AdjustorThunk (void);
extern void XRHumanBody_get_nativePtr_mBD7FABEADEC1EA20A472626430176AC6681C50E2_AdjustorThunk (void);
extern void XRHumanBody_set_nativePtr_m140ED78793BB10C9126C5539804291CE69F00381_AdjustorThunk (void);
extern void XRHumanBody_Equals_mED06668B3B016A173D38A33D8D4CC24691A90CF1_AdjustorThunk (void);
extern void XRHumanBody_Equals_mC6FA42C2E907195A60B2CB8A6230462762C6B003_AdjustorThunk (void);
extern void XRHumanBody_GetHashCode_m44E8812541CCF52BB596A789A350C77CF32B8B06_AdjustorThunk (void);
extern void XRHumanBodyJoint_get_index_m3AD361AAD68A37A0EC5490A716FA0F0D5AC6D386_AdjustorThunk (void);
extern void XRHumanBodyJoint_get_parentIndex_m4DA1B768A618B7AE553D67CC82F6B2545B8F2FBA_AdjustorThunk (void);
extern void XRHumanBodyJoint_get_localScale_m9A7DDC16FAEB5CFF269393403A92C375CE8387B6_AdjustorThunk (void);
extern void XRHumanBodyJoint_get_localPose_m5330B565E89F7276A497ED8E94DAA288A352FDD2_AdjustorThunk (void);
extern void XRHumanBodyJoint_get_anchorScale_m01EC3D9B0020D0BFBCDF9ADD26149F6D9E6D87C0_AdjustorThunk (void);
extern void XRHumanBodyJoint_get_anchorPose_mC409FE9C6F4CFD14C156977B59096FA4340EE61E_AdjustorThunk (void);
extern void XRHumanBodyJoint_get_tracked_mC8DA59028CFA50982FD6E319736F0C93EA097899_AdjustorThunk (void);
extern void XRHumanBodyJoint__ctor_mF74F3B39077D10EED35A34F3DBF7C217CA1D8753_AdjustorThunk (void);
extern void XRHumanBodyJoint_Equals_m7DFBAA24024C04E8A38A962862BA744F9A515AE5_AdjustorThunk (void);
extern void XRHumanBodyJoint_Equals_m59EDC2A704F17057288266550340CCB7FE041680_AdjustorThunk (void);
extern void XRHumanBodyJoint_GetHashCode_mC37463DF2B57FF4BA22AD008F91AF061E30575EF_AdjustorThunk (void);
extern void XRHumanBodyJoint_ToString_mE909C8943965A053938EFE3B7DC365673632F899_AdjustorThunk (void);
extern void XRHumanBodyJoint_ToString_m814AEF251F6D72B22EE7DE358A422C638FF6D089_AdjustorThunk (void);
extern void XRHumanBodyPose2DJoint_get_index_m63E7D2C639973B20B8721BD412441AB87F32C626_AdjustorThunk (void);
extern void XRHumanBodyPose2DJoint_get_parentIndex_m74403971AC59748A0FC11187E701F8EF835A97F5_AdjustorThunk (void);
extern void XRHumanBodyPose2DJoint_get_position_m4A5CE8370D7E1DEB0B2CC27487182A0776AAC8E8_AdjustorThunk (void);
extern void XRHumanBodyPose2DJoint_get_tracked_mCC6E1D56159DA4501534E47AB27D4EA05AA3FCF8_AdjustorThunk (void);
extern void XRHumanBodyPose2DJoint__ctor_m2BE9BC97DB9FA4C84623810D624196524B9F488A_AdjustorThunk (void);
extern void XRHumanBodyPose2DJoint_Equals_m7023E676891F764891104A57CD41D77BE31360F4_AdjustorThunk (void);
extern void XRHumanBodyPose2DJoint_Equals_mE8F361B51A58F789BC559B550AA5CA08691A88E1_AdjustorThunk (void);
extern void XRHumanBodyPose2DJoint_GetHashCode_m9CA16CABF11BB7137978E5D8EE83FACBF7D1622F_AdjustorThunk (void);
extern void XRHumanBodyPose2DJoint_ToString_m487FA2ED54B8FC493572D75FAFC68BA40B4FEFC4_AdjustorThunk (void);
extern void XRHumanBodyPose2DJoint_ToString_m7BBC96E86E94C35680E22AB7CED6215407E48CBB_AdjustorThunk (void);
extern void XRHumanBodySubsystemCinfo_get_id_m2B4E8095B5AE6AF6E64FB7B56DBD795989A862F8_AdjustorThunk (void);
extern void XRHumanBodySubsystemCinfo_set_id_m58CBD535224B7049AC8AC9D82305E1D8BBF90084_AdjustorThunk (void);
extern void XRHumanBodySubsystemCinfo_get_providerType_m71F869C8A2B8C6AC2BB7D0C3842549886608BD19_AdjustorThunk (void);
extern void XRHumanBodySubsystemCinfo_set_providerType_m228A78E8403BF03833D556B3EE0637291DC46615_AdjustorThunk (void);
extern void XRHumanBodySubsystemCinfo_get_subsystemTypeOverride_m1AD3DA828683BFEC871F5E62C5B552E27F47A009_AdjustorThunk (void);
extern void XRHumanBodySubsystemCinfo_set_subsystemTypeOverride_m847719A54A861DE6FE06E9DAFA1485E6184D2909_AdjustorThunk (void);
extern void XRHumanBodySubsystemCinfo_get_implementationType_m3CD85264DFA467891430738097EBB79174BA518C_AdjustorThunk (void);
extern void XRHumanBodySubsystemCinfo_set_implementationType_m8001903267263EED38F960C2E1BD5A53CE64C798_AdjustorThunk (void);
extern void XRHumanBodySubsystemCinfo_get_supportsHumanBody2D_m93255214CEECB1C656D88ACFFF73D736D81FD8BD_AdjustorThunk (void);
extern void XRHumanBodySubsystemCinfo_set_supportsHumanBody2D_m7E4750E667A695B158E006DDC6F74C56542CBE0E_AdjustorThunk (void);
extern void XRHumanBodySubsystemCinfo_get_supportsHumanBody3D_m133406910A68DE814A0A335A59285098FEE70C19_AdjustorThunk (void);
extern void XRHumanBodySubsystemCinfo_set_supportsHumanBody3D_m02D0D700619D9CB7D96D027EFE3EC41D47F81F8A_AdjustorThunk (void);
extern void XRHumanBodySubsystemCinfo_get_supportsHumanBody3DScaleEstimation_m81A73CFB3B8232EF098B2CDF96F25687AC19845C_AdjustorThunk (void);
extern void XRHumanBodySubsystemCinfo_set_supportsHumanBody3DScaleEstimation_m5F982C883E1918068DA02BA87C0069BF379E147F_AdjustorThunk (void);
extern void XRHumanBodySubsystemCinfo_Equals_m99F61D9E0CFA86A7171F27A236AD8CD2FD7BF08A_AdjustorThunk (void);
extern void XRHumanBodySubsystemCinfo_Equals_mE22CDDCA260523EF651BD2B23C1F66F11BB95C0A_AdjustorThunk (void);
extern void XRHumanBodySubsystemCinfo_GetHashCode_mF9F242A2F525ED4246171238CE9B5A7FD24C63FE_AdjustorThunk (void);
extern void AddReferenceImageJobState__ctor_mC0CCEC53FEB86CE2B9560D06DE28919ADB2440E2_AdjustorThunk (void);
extern void AddReferenceImageJobState_get_jobHandle_m02E9565D08C8156E799D1B852C14707856E6B12E_AdjustorThunk (void);
extern void AddReferenceImageJobState_AsIntPtr_m8C97E68E09387D512B5A2D921841B3E0FCF44CC0_AdjustorThunk (void);
extern void AddReferenceImageJobState_get_status_mDF8FE0C1BC9407AD9EAA821DE78B76599455A25F_AdjustorThunk (void);
extern void AddReferenceImageJobState_ToString_m89383245617B4E89FF1CA2FF897917062CD663A7_AdjustorThunk (void);
extern void AddReferenceImageJobState_GetHashCode_m6EABAC53399090ADFD2932E561BA0FA12EA63DC0_AdjustorThunk (void);
extern void AddReferenceImageJobState_Equals_mCFA105DAC305C1B3B34F0C7D0D856F3671356D37_AdjustorThunk (void);
extern void AddReferenceImageJobState_Equals_mD0EE6BB78CB7601C9E1AC6C297417B6E4AE70502_AdjustorThunk (void);
extern void Enumerator__ctor_m25C351F3CA22AFB104CE79D00CFF851C7E247ECE_AdjustorThunk (void);
extern void Enumerator_MoveNext_mF3DBBFA17313E104979A8A4F7CD5F111C352AF67_AdjustorThunk (void);
extern void Enumerator_get_Current_mCAFA85FE5DFA6D3AF14AE3E0BD39A478B00D5F03_AdjustorThunk (void);
extern void Enumerator_Dispose_m4CBA500DF0A5E65FA1BECA0D61521C960884E06B_AdjustorThunk (void);
extern void Enumerator_GetHashCode_m687007D953BFC4902A3A0115F0E55CA3EBEB1DFE_AdjustorThunk (void);
extern void Enumerator_Equals_m6831767F67C100E80B4C5BCDC1980E8453298DF9_AdjustorThunk (void);
extern void Enumerator_Equals_m9D822B4C42050C4A121A8CDC1174F2A5824015BB_AdjustorThunk (void);
extern void Cinfo_get_id_m575543324B2C5A08D4C786C63370AE71BED07969_AdjustorThunk (void);
extern void Cinfo_set_id_m26F70E551A2F6B517FB5F5C3E5EE4C129FA7BF42_AdjustorThunk (void);
extern void Cinfo_get_providerType_mBE30E1C47EDAB073ED2660418CA3E29DA5F0CC9B_AdjustorThunk (void);
extern void Cinfo_set_providerType_m4EEFA53693D860609EE4E84FBBD7411BCF38C7D8_AdjustorThunk (void);
extern void Cinfo_get_subsystemTypeOverride_m1ECE580A62D07D72403739C3B0A3402066CA2939_AdjustorThunk (void);
extern void Cinfo_set_subsystemTypeOverride_m92548450D405AAF6961BFAD7DFBA20AC0F258A16_AdjustorThunk (void);
extern void Cinfo_get_subsystemImplementationType_m6D5DFEBCB48B3C0A9574AF76063A4E4C0BEE4D9C_AdjustorThunk (void);
extern void Cinfo_set_subsystemImplementationType_m38DB94BDE396C4375A2384D92893106166598BD3_AdjustorThunk (void);
extern void Cinfo_get_supportsMovingImages_mF03413F05E6DA7C176CC49907024D17F3CA8CDD4_AdjustorThunk (void);
extern void Cinfo_set_supportsMovingImages_m8A9EC55903324606F3A00A1B9E0BA4A7F9FBA636_AdjustorThunk (void);
extern void Cinfo_get_requiresPhysicalImageDimensions_m75BC1904E82964AAB2D4878CDC01A5DA91BCEF8B_AdjustorThunk (void);
extern void Cinfo_set_requiresPhysicalImageDimensions_mEDA76B05F4AADE1843195F0C011BFAD5A10179CD_AdjustorThunk (void);
extern void Cinfo_get_supportsMutableLibrary_mEED5E973B93B77E2EB889635CF0BFBED0F3AAD65_AdjustorThunk (void);
extern void Cinfo_set_supportsMutableLibrary_mE18B618EF5F3EC11E1AB460302D88A92091DE9E1_AdjustorThunk (void);
extern void Cinfo_get_supportsImageValidation_m4A509134A5D26AAEFA77EC28D48802E48D98AF35_AdjustorThunk (void);
extern void Cinfo_set_supportsImageValidation_mCD49CB40A3C9FDC8340016FC0C6D4CA3AC71A98B_AdjustorThunk (void);
extern void Cinfo_GetHashCode_m17D577D55135627B8C8B252E8694CE37D2DCDC8A_AdjustorThunk (void);
extern void Cinfo_Equals_m1B1870077B045D565BC51101AB7C0F66734249FC_AdjustorThunk (void);
extern void Cinfo_Equals_m6FB5DA5B648EE40F20736E915C5E64C13A4D312D_AdjustorThunk (void);
extern void XRReferenceImage__ctor_mCD536BB9053D7775175E0A8AE51BBF026AB06765_AdjustorThunk (void);
extern void XRReferenceImage_get_guid_m6BEA9888191B7528B60F98EE03C9DBB2B9B8ADEE_AdjustorThunk (void);
extern void XRReferenceImage_get_textureGuid_m70BB73989E26562E2B37F8C272F14F2D06659615_AdjustorThunk (void);
extern void XRReferenceImage_get_specifySize_m571D71A02EF695A72121AAA086F8B52323E4E4A5_AdjustorThunk (void);
extern void XRReferenceImage_get_size_mF44BF21ADEFBB155BFD8043E1067057219EC59F2_AdjustorThunk (void);
extern void XRReferenceImage_get_width_mB6465C498B58CD9093F2FF5EA55DAC8F0E7580A9_AdjustorThunk (void);
extern void XRReferenceImage_get_height_m422FF3F85DE70E492B7FBABC02277DF6BD76DCD8_AdjustorThunk (void);
extern void XRReferenceImage_get_name_mF1BE1E54AD911D48445B7DDEF2E27EA01E1E73BB_AdjustorThunk (void);
extern void XRReferenceImage_get_texture_mEC132411644C747C782F41A32A97C95B306D0891_AdjustorThunk (void);
extern void XRReferenceImage_ToString_mA4374950A18DB316C790DD07F2485A385CE7F3D3_AdjustorThunk (void);
extern void XRReferenceImage_GetHashCode_m4A2F5EA86EF5B9CDF39516FABD5E378D779B1BA0_AdjustorThunk (void);
extern void XRReferenceImage_Equals_m1FACD89998C2C9ED6E65DDEE6C1466AE7CC4537E_AdjustorThunk (void);
extern void XRReferenceImage_Equals_m6EA6760F9A443A324475B1E442AFA83C84F06D08_AdjustorThunk (void);
extern void XRTrackedImage__ctor_mC69E3D11AAD1CF838BC8575A0E476C296B4B1F3F_AdjustorThunk (void);
extern void XRTrackedImage_get_trackableId_m9EA6E15BEF6777E27B50A4903E0069AC04ED6405_AdjustorThunk (void);
extern void XRTrackedImage_get_sourceImageId_mAAAA675839747EA6AF8A903E461F0D198CFAFDBF_AdjustorThunk (void);
extern void XRTrackedImage_get_pose_m24132085AC8CCE5762C01ECCC1C264A36E77FD69_AdjustorThunk (void);
extern void XRTrackedImage_get_size_m57847CD4307A9A560D358981700B8722D8A02438_AdjustorThunk (void);
extern void XRTrackedImage_get_trackingState_m059B99A670B142384AE772376780095877CA72F9_AdjustorThunk (void);
extern void XRTrackedImage_get_nativePtr_m2ECEAC93477008FB415D4A388ACAA4A9DB6E1892_AdjustorThunk (void);
extern void XRTrackedImage_GetHashCode_m3900E50D96F2687C63C8F78C9BEA6E469FAE5E2C_AdjustorThunk (void);
extern void XRTrackedImage_Equals_m95C7E1338C9CD5F37EE9D6452AE5820D2BC87FB7_AdjustorThunk (void);
extern void XRTrackedImage_Equals_m54F9B4F5CC42200E927B5025274C6E765264C9BF_AdjustorThunk (void);
extern void Capabilities_Equals_m60B932F4020B4C1D938F76F8B143AAD76901C48C_AdjustorThunk (void);
extern void Capabilities_Equals_mC78F86790EF9479F76FC84B4E7F74E2E2C07D249_AdjustorThunk (void);
extern void Capabilities_GetHashCode_mE4E2BB398DA4790DD8E2D0FCA8477062537CF432_AdjustorThunk (void);
extern void XRReferenceObject__ctor_mBAE1DECE98351EEFC3B058C143FECED732A58B71_AdjustorThunk (void);
extern void XRReferenceObject_AddEntry_m83F55C34FB2B5294EFA94E936676E3A3401E07BA_AdjustorThunk (void);
extern void XRReferenceObject_get_name_m30CA572092D7E0DD1D7028A84BB0F5999A92D8FD_AdjustorThunk (void);
extern void XRReferenceObject_get_guid_m96423410888B4CB9712D1A064CF874B5191A49D1_AdjustorThunk (void);
extern void XRReferenceObject_FindEntry_mFF9C29CCCDF7BA08B307B787DF83EF8F27B6017C_AdjustorThunk (void);
extern void XRReferenceObject_OnAddToLibrary_mF06627EC55B52C25A701E1E00D4F941C70432E42_AdjustorThunk (void);
extern void XRReferenceObject_Equals_m464CFD79821FAA617E311B82ADA20968A248F511_AdjustorThunk (void);
extern void XRReferenceObject_GetHashCode_m8ACBD77E6AAF860411D1A1135C4838667F1FB77D_AdjustorThunk (void);
extern void XRReferenceObject_Equals_m3606543B9EF155334F0E71FFE4A550EF5DA0957C_AdjustorThunk (void);
extern void XRTrackedObject_get_trackableId_mE8CA173C4D77E4910C47CD5A3DBEA7570CCA69F8_AdjustorThunk (void);
extern void XRTrackedObject_get_pose_mC8BBDFCC19D9FAF22FA0484E58C5BB1114C929C7_AdjustorThunk (void);
extern void XRTrackedObject_get_trackingState_m7D1D9DD436ECB10D02D1413AB660ACBC23D60E89_AdjustorThunk (void);
extern void XRTrackedObject_get_nativePtr_m804A89CA593F513109FCBC04A4FB2C16505F388C_AdjustorThunk (void);
extern void XRTrackedObject_get_referenceObjectGuid_m5E5FEE2B90403C2F75252A6DF4BD9436A7927FD2_AdjustorThunk (void);
extern void XRTrackedObject__ctor_m9916083096F5B1A8034C7450D07AA9192CDE7BDC_AdjustorThunk (void);
extern void XRTrackedObject_Equals_m4D9D7A47BBD40F713CA964F4109DFD1AFB225D7A_AdjustorThunk (void);
extern void XRTrackedObject_GetHashCode_m9E4ECD0C61FC65EF59F51AD6930540BDF65D2CE3_AdjustorThunk (void);
extern void XRTrackedObject_Equals_mEA3B08014486E3DF12E17FDCA8D1FAE067FF8BEA_AdjustorThunk (void);
extern void XROcclusionSubsystemCinfo_get_id_mF11E38C57E4AB8E81F9E7875A0A41D04A19C4039_AdjustorThunk (void);
extern void XROcclusionSubsystemCinfo_set_id_mF8B41D7F5FACF940467D57208BC03DDD89D9B7A8_AdjustorThunk (void);
extern void XROcclusionSubsystemCinfo_get_providerType_m98D7D72FF4C0B36F28D6E39BC498E43691AE718B_AdjustorThunk (void);
extern void XROcclusionSubsystemCinfo_set_providerType_m5D9D3B330216EAB023F4B17F4853D5A612B07380_AdjustorThunk (void);
extern void XROcclusionSubsystemCinfo_get_subsystemTypeOverride_mAE0D5036913033AB1D45B54C10047F180648BD3E_AdjustorThunk (void);
extern void XROcclusionSubsystemCinfo_set_subsystemTypeOverride_mA0E976FA9B7955BD224DF93F7AB9AEA883779563_AdjustorThunk (void);
extern void XROcclusionSubsystemCinfo_get_implementationType_mC1121AB1278E4F86B951FBD0B2EAD85D81A2AB45_AdjustorThunk (void);
extern void XROcclusionSubsystemCinfo_set_implementationType_m0D5D7F0B926679A2195C01F183280029ADC525BB_AdjustorThunk (void);
extern void XROcclusionSubsystemCinfo_get_supportsHumanSegmentationStencilImage_mB151BF0F40B3C9D9D2DE26318FD219FEF0C2AB9D_AdjustorThunk (void);
extern void XROcclusionSubsystemCinfo_set_supportsHumanSegmentationStencilImage_mE4E442080331134DC255F46E5342D25FF48CB666_AdjustorThunk (void);
extern void XROcclusionSubsystemCinfo_get_humanSegmentationStencilImageSupportedDelegate_m77677BDADCDA75FD77F97A942FA6B29706500292_AdjustorThunk (void);
extern void XROcclusionSubsystemCinfo_set_humanSegmentationStencilImageSupportedDelegate_mB0F746AC0CBE2CC986B43CA50873FF91D3D9860F_AdjustorThunk (void);
extern void XROcclusionSubsystemCinfo_get_supportsHumanSegmentationDepthImage_mB73625A00528D80575D712BE5621FF9219E3B6E9_AdjustorThunk (void);
extern void XROcclusionSubsystemCinfo_set_supportsHumanSegmentationDepthImage_m3D99453F58EEFC98739B052C19B05A4FD5341926_AdjustorThunk (void);
extern void XROcclusionSubsystemCinfo_get_humanSegmentationDepthImageSupportedDelegate_m67F62406957D42F2EF689DE57FD0074C3DAF2BD5_AdjustorThunk (void);
extern void XROcclusionSubsystemCinfo_set_humanSegmentationDepthImageSupportedDelegate_m4AB47FCB92617E34DC2C66699B26346112E5C145_AdjustorThunk (void);
extern void XROcclusionSubsystemCinfo_get_environmentDepthTemporalSmoothingSupportedDelegate_mD84F3F4F9DFCFC8C4CE86F9276EEA5ACA2392D1D_AdjustorThunk (void);
extern void XROcclusionSubsystemCinfo_set_environmentDepthTemporalSmoothingSupportedDelegate_m6B67C82AEA73E79B7CD03F9912746C6B52C27949_AdjustorThunk (void);
extern void XROcclusionSubsystemCinfo_get_queryForSupportsEnvironmentDepthImage_mA29F2AB5C4AA073556B074CFD5A88D3345D596B3_AdjustorThunk (void);
extern void XROcclusionSubsystemCinfo_set_queryForSupportsEnvironmentDepthImage_m4BFFF1A117B8452E731F6EC8A4B3CBBFAA0F0B9D_AdjustorThunk (void);
extern void XROcclusionSubsystemCinfo_get_environmentDepthImageSupportedDelegate_m540B191F5215CDD7A1DEC6E370065AE8C9ADE75C_AdjustorThunk (void);
extern void XROcclusionSubsystemCinfo_set_environmentDepthImageSupportedDelegate_m18E4C878D993208EB9EEA9D667CA5C88E4E1D4BE_AdjustorThunk (void);
extern void XROcclusionSubsystemCinfo_get_queryForSupportsEnvironmentDepthConfidenceImage_mA3552C9DF07B609155051A3A80FA30AFDE2D3D1F_AdjustorThunk (void);
extern void XROcclusionSubsystemCinfo_set_queryForSupportsEnvironmentDepthConfidenceImage_m4717AAF7FA480A374DF1357BFDD2D9A256CF5117_AdjustorThunk (void);
extern void XROcclusionSubsystemCinfo_get_environmentDepthConfidenceImageSupportedDelegate_m562A6E788BE3D0CCD49F0DC2DC698CFAB87DDA6C_AdjustorThunk (void);
extern void XROcclusionSubsystemCinfo_set_environmentDepthConfidenceImageSupportedDelegate_m0F91D5C5B63DEFD91FE9A00EF95B45ED9711461A_AdjustorThunk (void);
extern void XROcclusionSubsystemCinfo_Equals_mEFD9C210D7814A35FFE675EBD2EE91E6A3856623_AdjustorThunk (void);
extern void XROcclusionSubsystemCinfo_Equals_m232468ACCEB9CD8E20E096E270EEC1B262013812_AdjustorThunk (void);
extern void XROcclusionSubsystemCinfo_GetHashCode_m672CF4A97241C59DE78E46E2D41245EACC13B09E_AdjustorThunk (void);
extern void XRParticipant__ctor_mB90C6BDD46B876A4500C55B8CC4C5397AF98B4B6_AdjustorThunk (void);
extern void XRParticipant_get_trackableId_mA33D6F01E0C98B53E73D3AE91E9D27637D3EDCF1_AdjustorThunk (void);
extern void XRParticipant_get_pose_m68A36D0B3837325D073CCB92A93F600A2D535B7B_AdjustorThunk (void);
extern void XRParticipant_get_trackingState_m65687E606627A087623C9937E49EDE133C5EDD89_AdjustorThunk (void);
extern void XRParticipant_get_nativePtr_m55B683C801F0EDB9EBBCE21A31B507469838D09A_AdjustorThunk (void);
extern void XRParticipant_get_sessionId_m266D8DE3F178F3EFF1ACBC50578F67E2152006AE_AdjustorThunk (void);
extern void XRParticipant_GetHashCode_mBDFD1ECFEF61A85D9A8BF3857AA0B6220B4A25F1_AdjustorThunk (void);
extern void XRParticipant_Equals_mED9407D8D4A118CC1E4B2591A3C3884E3FD74708_AdjustorThunk (void);
extern void XRParticipant_Equals_mA78A4E55D2F314EA48933FB16F3AE557819DCC9E_AdjustorThunk (void);
extern void BoundedPlane__ctor_m95C41A6B0DB95A2636683BE716E9F92A0465EF87_AdjustorThunk (void);
extern void BoundedPlane_get_trackableId_m7AA7FD63EA8F8A903300EFDF15616315ACFDA8AA_AdjustorThunk (void);
extern void BoundedPlane_get_subsumedById_m27EFD2CAFFDCF6560D445115D596F23094F612B9_AdjustorThunk (void);
extern void BoundedPlane_get_pose_mE6F416B0C7519EDA0D1AE8D8BD4D627E4CEA96CC_AdjustorThunk (void);
extern void BoundedPlane_get_center_m3BB7635D2137C7C414FC682EBE0CB5E1F8D3F7D3_AdjustorThunk (void);
extern void BoundedPlane_get_extents_m60341CDB176C9039D5B88B2F52534D356E11F400_AdjustorThunk (void);
extern void BoundedPlane_get_size_m2645C0FF517DF15F381B268DF6366F4D14381DC8_AdjustorThunk (void);
extern void BoundedPlane_get_alignment_m4E43582A7059AE23DD506878BCF164C61422DBEF_AdjustorThunk (void);
extern void BoundedPlane_get_trackingState_mC294F13F8F79D53F8F04D8FB4E160B092BA6A577_AdjustorThunk (void);
extern void BoundedPlane_get_nativePtr_mE8E2608856FE4327913A38005F4A8590D65A43E7_AdjustorThunk (void);
extern void BoundedPlane_get_classification_m4EA9556C440097648A87D3AB7EC433776468A725_AdjustorThunk (void);
extern void BoundedPlane_get_width_m634AD1BAD468FF96CBFC5786A4CB8A9747737E96_AdjustorThunk (void);
extern void BoundedPlane_get_height_mB72E46326D1B3DAA5EF67D7FC65D58ECF02FEB5E_AdjustorThunk (void);
extern void BoundedPlane_get_normal_m219E5CB840E4DEE8ECC168F2E5BC3FA6AD5E3DCA_AdjustorThunk (void);
extern void BoundedPlane_get_plane_mC4E55F965A895DDD4EB960BEB612185DE21FD9AC_AdjustorThunk (void);
extern void BoundedPlane_GetCorners_mA9013A95E8FF0830A83791567377903D4D4ED8A8_AdjustorThunk (void);
extern void BoundedPlane_ToString_mA4FBDD41FC676DB2C2EEB22DA2E624099EF06ADA_AdjustorThunk (void);
extern void BoundedPlane_Equals_mE1A074D048C20E980CB7016FAFA7EDFDA52DB15D_AdjustorThunk (void);
extern void BoundedPlane_GetHashCode_m8C684989A748253B2A3772BCAA87D8758FB98941_AdjustorThunk (void);
extern void BoundedPlane_Equals_m1F738CE040A5D498E41B35521109A3FFBEB7196D_AdjustorThunk (void);
extern void Cinfo_get_id_m3C9491FE6D19662F5CDE221328F86862621B6DE2_AdjustorThunk (void);
extern void Cinfo_set_id_mCADBFEB62A645F33A8FE7684CE21D11A837FB6F9_AdjustorThunk (void);
extern void Cinfo_get_providerType_m43F95297A90490AFA397FC61B990A33F74259EF5_AdjustorThunk (void);
extern void Cinfo_set_providerType_m75AA9217739457DA075AB764BF440217E87A1126_AdjustorThunk (void);
extern void Cinfo_get_subsystemTypeOverride_m30B55A83F149F3B304AAAD85FE3E80BFCD75552D_AdjustorThunk (void);
extern void Cinfo_set_subsystemTypeOverride_m8880B201EBF541B726CA629EF2DBA762E5ACB010_AdjustorThunk (void);
extern void Cinfo_get_subsystemImplementationType_m87A964B3827007A58DEAF8966B51575BA7687D6D_AdjustorThunk (void);
extern void Cinfo_set_subsystemImplementationType_m63ACA332E759D120AB82AA3FE630512846B04E4E_AdjustorThunk (void);
extern void Cinfo_get_supportsHorizontalPlaneDetection_m25246A60EFD930C7AC4C50A950E910E7716C9315_AdjustorThunk (void);
extern void Cinfo_set_supportsHorizontalPlaneDetection_m73E4DE44A091E9B268214E732EFA29174703EEF1_AdjustorThunk (void);
extern void Cinfo_get_supportsVerticalPlaneDetection_mEA496CC68069CCAD03DBC1B57F53DDA57D56A8BF_AdjustorThunk (void);
extern void Cinfo_set_supportsVerticalPlaneDetection_m989F6D7C88D39981D1DD342DEC887E9DB3E44AF3_AdjustorThunk (void);
extern void Cinfo_get_supportsArbitraryPlaneDetection_m7E19D041E3828651646769D594B9647149B5A0F4_AdjustorThunk (void);
extern void Cinfo_set_supportsArbitraryPlaneDetection_m423FEFB76FB81C496A35BB358EF04592C8EE9C10_AdjustorThunk (void);
extern void Cinfo_get_supportsBoundaryVertices_m75615CA66C3E0020B75915F8426FE6B2B475BEDD_AdjustorThunk (void);
extern void Cinfo_set_supportsBoundaryVertices_m96752ABD368822EE7EE393F7AE3AAE631A4C3657_AdjustorThunk (void);
extern void Cinfo_get_supportsClassification_m2596CCB90308DA90A90177C91854DDFBF18F464A_AdjustorThunk (void);
extern void Cinfo_set_supportsClassification_mB1E8AAC1F2A7D511C960C6606364C87EEA1A221D_AdjustorThunk (void);
extern void Cinfo_Equals_m8A992E8675D4C2A5FCF7FCD7714CD1DBD734FEC0_AdjustorThunk (void);
extern void Cinfo_Equals_m2B155451B272C1E8954EDA6D6DFD1C151408D393_AdjustorThunk (void);
extern void Cinfo_GetHashCode_mC8813973E6CB5AB8D267B6D76693B6F96C006BF9_AdjustorThunk (void);
extern void Cinfo_get_providerType_m9A765D29AAB5D3BB7C85A54C8B5BD5ACF0A34834_AdjustorThunk (void);
extern void Cinfo_set_providerType_mF68F31F518ED15570B01758D651590F2A79ADA50_AdjustorThunk (void);
extern void Cinfo_get_subsystemTypeOverride_m9E130C961C57B8F9DEE5CB775E5BAC1E82FE9ACC_AdjustorThunk (void);
extern void Cinfo_set_subsystemTypeOverride_mF477DEE9A66BA9E979AE210507B158E20DB7F897_AdjustorThunk (void);
extern void Cinfo_get_supportsFeaturePoints_mDA5E397852EFECCFB9D72BEF50D8149389AF32A0_AdjustorThunk (void);
extern void Cinfo_set_supportsFeaturePoints_m9AB5B37930ED13405E788D882B2ED496A8A003A4_AdjustorThunk (void);
extern void Cinfo_get_supportsConfidence_mD64AB8201EF5FED46B5961D3E20A501074F442BA_AdjustorThunk (void);
extern void Cinfo_set_supportsConfidence_m8455406D7A9D0EA2B0600FA6D952CF4E7B169AA2_AdjustorThunk (void);
extern void Cinfo_get_supportsUniqueIds_m0CD91E9193EA5454EBE851570155F301B1B4E499_AdjustorThunk (void);
extern void Cinfo_set_supportsUniqueIds_m0C526CF6938A8220903C47BD11AC9F6A1B1E86F6_AdjustorThunk (void);
extern void Cinfo_get_capabilities_m75D2555477E50E9EE792D385FB7E178EF121362C_AdjustorThunk (void);
extern void Cinfo_set_capabilities_m057D002DC3F65F4A194653D7724131E7DE20D852_AdjustorThunk (void);
extern void Cinfo_Equals_m45095412211AFF025FFA31098667F7CF82BF2999_AdjustorThunk (void);
extern void Cinfo_Equals_mDA07D4DDCD91815E6748EC1C73EAB4CA9C911921_AdjustorThunk (void);
extern void Cinfo_GetHashCode_mD5232DFA01B322FD842CE7B2440D51800770C3A9_AdjustorThunk (void);
extern void XRPointCloud__ctor_m7689BEC3D1FFA90CE83BD510C993768CCA777688_AdjustorThunk (void);
extern void XRPointCloud_get_trackableId_m3AFB6026E205E26C8B7A3209696F566FB686144D_AdjustorThunk (void);
extern void XRPointCloud_get_pose_m0A8AC4386A388238F4910916CDD3D1B936DB8A51_AdjustorThunk (void);
extern void XRPointCloud_get_trackingState_m2CD370D0D6A2A920AC1637D94BDD3BCC5DB8945D_AdjustorThunk (void);
extern void XRPointCloud_get_nativePtr_m606D5A1327EAF4D7A23811FE9D90BB8A027D5B11_AdjustorThunk (void);
extern void XRPointCloud_GetHashCode_m96702297D45AEC469D7D3106FBE47F36B50F9688_AdjustorThunk (void);
extern void XRPointCloud_Equals_m401E9050FB531805238DF8BCBB06491E23A763B0_AdjustorThunk (void);
extern void XRPointCloud_Equals_m1A2A56D489C6CFB2DA66238FA843A972E5B2341D_AdjustorThunk (void);
extern void XRPointCloudData_get_positions_m6B1843590E0A5A94DBA711BF1FBA3A64E39A00A5_AdjustorThunk (void);
extern void XRPointCloudData_set_positions_mDE6F539B73AEA3C49189F4210F9D01094A02F14D_AdjustorThunk (void);
extern void XRPointCloudData_get_confidenceValues_m4553186D87BC21D13B0B5AC3542BDFE6CFDA15FA_AdjustorThunk (void);
extern void XRPointCloudData_set_confidenceValues_m2541483932A4753B91B3038EF869340A1B949355_AdjustorThunk (void);
extern void XRPointCloudData_get_identifiers_m86966DF55A38D54A4284AE08D1EBE95F95F80203_AdjustorThunk (void);
extern void XRPointCloudData_set_identifiers_mA6E3D5E038C89FE4187BE6F57CA92822F0A0CB9B_AdjustorThunk (void);
extern void XRPointCloudData_Dispose_m761F04E465F85CB79EC6BDFFBAB3A348CCB02F75_AdjustorThunk (void);
extern void XRPointCloudData_GetHashCode_m1787DE4A09656A3F814952590544879379129FB2_AdjustorThunk (void);
extern void XRPointCloudData_Equals_mA86AEB4AE413BE16DB87461174F31B9ECE87EDED_AdjustorThunk (void);
extern void XRPointCloudData_ToString_mE72394A861C3A87F2DA161BAE348FF5E5A0E8052_AdjustorThunk (void);
extern void XRPointCloudData_Equals_mE40F5EEEE84C1953A50F3E08BE1C17D3A965B970_AdjustorThunk (void);
extern void Cinfo_get_providerType_mDDD1F34666705A5BB5B8ED6BD6A76D3449F35323_AdjustorThunk (void);
extern void Cinfo_set_providerType_mC05B07E51AB9C1D9876C0B38F185CE8A8CAB3ACF_AdjustorThunk (void);
extern void Cinfo_get_subsystemTypeOverride_m67FF1505BC8E11A5A8B8AD478D581703ACC11AFB_AdjustorThunk (void);
extern void Cinfo_set_subsystemTypeOverride_m242BF043654209565488CEE2A0ACB93CE23E9C72_AdjustorThunk (void);
extern void Cinfo_get_supportsFeaturePoints_m21644904ECC661B186FACE0FD45BA53FCBE67C29_AdjustorThunk (void);
extern void Cinfo_set_supportsFeaturePoints_m9B0684F80A0FFF829E28C477E0E49AF60C1DDC15_AdjustorThunk (void);
extern void Cinfo_get_supportsConfidence_m24690BFED822D8034981F9ABF2C02361C071798D_AdjustorThunk (void);
extern void Cinfo_set_supportsConfidence_m7EE730D229E3187CE6DC3E9FB2E31FA32D1A29D8_AdjustorThunk (void);
extern void Cinfo_get_supportsUniqueIds_m955E4A28D276A9459A9496C92C242F6BD429E236_AdjustorThunk (void);
extern void Cinfo_set_supportsUniqueIds_mA096C2070124D20D28C7009BA35B23918DC974A0_AdjustorThunk (void);
extern void Cinfo_get_capabilities_mE55D2E7B6B53FB7350832A08A1D626E2E3855090_AdjustorThunk (void);
extern void Cinfo_set_capabilities_m975B6EDB8EB063F712A26448245859759A35D7CE_AdjustorThunk (void);
extern void Cinfo_Equals_mB548ABCC0365E9FF78A36324ED2F1128B3836FF7_AdjustorThunk (void);
extern void Cinfo_Equals_m0305229B15DB2B5DF7EEEB4C8D28883DC35A9588_AdjustorThunk (void);
extern void Cinfo_GetHashCode_m4D75518C4E9BED4C7BF4508CAD92A03D5D1A77A7_AdjustorThunk (void);
extern void XRRaycast_get_trackableId_mA844E950A9862ABA13C47395893C18A55C9117AB_AdjustorThunk (void);
extern void XRRaycast_get_pose_mADE80A4AABEFCCCB8297186D7E836EAE5B730F73_AdjustorThunk (void);
extern void XRRaycast_get_trackingState_mDBA1DEB482B9346E44263E8B2201C1D8AF919B09_AdjustorThunk (void);
extern void XRRaycast_get_nativePtr_m79D980249D35343744B394D7238F8A8FB943D484_AdjustorThunk (void);
extern void XRRaycast_get_distance_m0B11F8743558DCA40C4E724ECAB8E4DD5ECFFD2B_AdjustorThunk (void);
extern void XRRaycast_get_hitTrackableId_m54245AC20302081DF8658019AA0261DEE1E7D6FE_AdjustorThunk (void);
extern void XRRaycast__ctor_m0ACF53702D817AC34FD8C21F2C01EF7A8F592F9D_AdjustorThunk (void);
extern void XRRaycast_GetHashCode_m94E4A6BDC4CD5E875F40777E273D1E9CD37D54A6_AdjustorThunk (void);
extern void XRRaycast_Equals_m7F141CB415FF28341035CBD2B32037DC80469575_AdjustorThunk (void);
extern void XRRaycast_Equals_m2A00EBA5AD411F5BFF724BB7D60175FAE69F8D74_AdjustorThunk (void);
extern void XRRaycastHit_get_trackableId_m8B92C0F8977D274743D9388DEB7DCEBCC88E7325_AdjustorThunk (void);
extern void XRRaycastHit_set_trackableId_mA41CAE66DB4E6054512F496DABE4C15B6217FA30_AdjustorThunk (void);
extern void XRRaycastHit_get_pose_m3B8D69B763A39178CB583948B4E08255FE9A633E_AdjustorThunk (void);
extern void XRRaycastHit_set_pose_m26D8C795FDFF7DEE86AB77BC5F0A0B6405150AD4_AdjustorThunk (void);
extern void XRRaycastHit_get_distance_m7098B7C90D22697CA37FBBDF50A4109AD055CA80_AdjustorThunk (void);
extern void XRRaycastHit_set_distance_m93182B0265D3D34E9D1730860A5B39F515EA729D_AdjustorThunk (void);
extern void XRRaycastHit_get_hitType_m30A8013E847E6B2B70A9511B522099C03102E933_AdjustorThunk (void);
extern void XRRaycastHit_set_hitType_m89FAB9AF35A52F7CA3F997AE1494EB92B60CA997_AdjustorThunk (void);
extern void XRRaycastHit__ctor_mEFB9D7632D78C282C02A913F1E4A2F7866C6B641_AdjustorThunk (void);
extern void XRRaycastHit_GetHashCode_m7C9DBAE43B929D3D4BBFF37E15E4E01143BC4A6B_AdjustorThunk (void);
extern void XRRaycastHit_Equals_m319801A0EFB8A841B3B7E6197BB612780698759A_AdjustorThunk (void);
extern void XRRaycastHit_Equals_mE45E36906807C4F3C5E28C1F54228142D444DA0A_AdjustorThunk (void);
extern void Cinfo_get_id_m14E2737CF1E90C961F8D5B282C17E125018668E0_AdjustorThunk (void);
extern void Cinfo_set_id_m962E07A26F49D8C32DAEFEC4F9E0F79EBC128533_AdjustorThunk (void);
extern void Cinfo_get_providerType_mB4A72CE35BDA5CCC57B2CCC3E1D88672D9E59021_AdjustorThunk (void);
extern void Cinfo_set_providerType_m5D183591B4A3ECA1EC2AA9015C02DCD1EB1F076F_AdjustorThunk (void);
extern void Cinfo_get_subsystemTypeOverride_m473983CC8B06F42BC47E92AA261EC2CCBEBFC292_AdjustorThunk (void);
extern void Cinfo_set_subsystemTypeOverride_m20C9DAD8677611ACD22689DA249911EEDE46EBF7_AdjustorThunk (void);
extern void Cinfo_get_subsystemImplementationType_mF1AF41E2F764A5BF0AF6A35C54C27DE25809AED9_AdjustorThunk (void);
extern void Cinfo_set_subsystemImplementationType_m4A20D7E4790C43392F4F82D6B9FAC1D2759F744C_AdjustorThunk (void);
extern void Cinfo_get_supportsViewportBasedRaycast_mBAE62868186C81B758E7B9B87F3C67F6C1586700_AdjustorThunk (void);
extern void Cinfo_set_supportsViewportBasedRaycast_mC7DB604D2E288A145177EC66F8C1DAA50F38693F_AdjustorThunk (void);
extern void Cinfo_get_supportsWorldBasedRaycast_m6D9743F420975015239E33D6641CA95A092D501D_AdjustorThunk (void);
extern void Cinfo_set_supportsWorldBasedRaycast_m05D5BC11896AED796A6E237BBB98FA4EF3CF17A4_AdjustorThunk (void);
extern void Cinfo_get_supportedTrackableTypes_mA59CFA06B5968E2DF45075C6C40E0A776CC19488_AdjustorThunk (void);
extern void Cinfo_set_supportedTrackableTypes_m319BC9C9EB554C35CD40B791917A603D63BEEE0A_AdjustorThunk (void);
extern void Cinfo_get_supportsTrackedRaycasts_mF9B448767ADE954E8357D686935ACCF3208DB1B8_AdjustorThunk (void);
extern void Cinfo_set_supportsTrackedRaycasts_m7E65DAEB9ED0CB4C4C83DB751C1547145949AA41_AdjustorThunk (void);
extern void Cinfo_GetHashCode_m6CC66C9C4BA7904DF1E9E9D7A3C74DC8D6A6C1BA_AdjustorThunk (void);
extern void Cinfo_Equals_m4D9FFB9CFA4DF03E9AF2763D3E91926896FCD64D_AdjustorThunk (void);
extern void Cinfo_ToString_m3022468C4555B097321DCDB08B4079DA430EBB3A_AdjustorThunk (void);
extern void Cinfo_Equals_mD2E403B4E791DCE658297F8CA484149EB2D5F5B8_AdjustorThunk (void);
extern void ScopedProfiler__ctor_m652B5689DE1A3C3EF7D12801DA27FA3B40E4412F_AdjustorThunk (void);
extern void ScopedProfiler__ctor_m1F4C2F43E028839CDD9B09EB51402C6F706431B5_AdjustorThunk (void);
extern void ScopedProfiler_Dispose_m7B646405B4E52CC4677329D3B860BE9C17A9DAC4_AdjustorThunk (void);
extern void SerializableGuid__ctor_m0F2435157FEC8427E91A7D0DD39960BADE7209F0_AdjustorThunk (void);
extern void SerializableGuid_get_guid_mC9C573E5730B2B18F6DFA80F0BCFD1A097C362B3_AdjustorThunk (void);
extern void SerializableGuid_GetHashCode_mC33B7B6D908B3A62767C19B331620784F1998D07_AdjustorThunk (void);
extern void SerializableGuid_Equals_mEB4A1B39DD600CB499AC43BF60A3BD206A1EFD71_AdjustorThunk (void);
extern void SerializableGuid_ToString_m4FB29C69FF91DC2020A96C3C83FE1B60F9C73047_AdjustorThunk (void);
extern void SerializableGuid_ToString_m66A8E16F22314214DECE08D94A189101A421603E_AdjustorThunk (void);
extern void SerializableGuid_ToString_m514BCF03CE14CE663D9ECC9616DD28453334BE96_AdjustorThunk (void);
extern void SerializableGuid_Equals_m7096244EB28310B3CB17CD79EE7068768C6AB4F7_AdjustorThunk (void);
extern void Cinfo_get_supportsInstall_mEEEDF86E5DE1B7515989BAECAF2F1714A327B720_AdjustorThunk (void);
extern void Cinfo_set_supportsInstall_mA4CF39BDB54C42BC8CBD401F6A77353B59EB074F_AdjustorThunk (void);
extern void Cinfo_get_supportsMatchFrameRate_mD86213A3EA6096133BF355DDDC55823027B48B7C_AdjustorThunk (void);
extern void Cinfo_set_supportsMatchFrameRate_mC501E193696EC21EF655C7B789CABFE2D7D2B3E4_AdjustorThunk (void);
extern void Cinfo_get_id_m20097DC1BBE19C329FD6F99312B6B5CD14678921_AdjustorThunk (void);
extern void Cinfo_set_id_m7ABF4746E3D8D5C9F5E845638AB861FFBF665594_AdjustorThunk (void);
extern void Cinfo_get_providerType_m89ED3FB1640690CFD1735E14FB7834A2BF9203EC_AdjustorThunk (void);
extern void Cinfo_set_providerType_m2B27139CECCAF21A444D7891124893D11B7B6484_AdjustorThunk (void);
extern void Cinfo_get_subsystemTypeOverride_m5A1888DCB068C90C7C5197246602893767C5A2B7_AdjustorThunk (void);
extern void Cinfo_set_subsystemTypeOverride_m5DD40EFEE5ADFC4A6624E1EF7345F63D66027423_AdjustorThunk (void);
extern void Cinfo_get_subsystemImplementationType_m1E7C284EE29C67952C8742FD257B3BA09F84D8B3_AdjustorThunk (void);
extern void Cinfo_set_subsystemImplementationType_m116B6D5857F561E8119812F01089CDDC08AFF3CA_AdjustorThunk (void);
extern void Cinfo_GetHashCode_m2D7B3F90910D284E6D8696F3E187E61436768107_AdjustorThunk (void);
extern void Cinfo_Equals_m0C051CCCCA3A44F0C851768FE1CDB49DAC6D82B6_AdjustorThunk (void);
extern void Cinfo_Equals_m053CC1A9E8D4DBF1FAFACF66083B615EF36CA572_AdjustorThunk (void);
extern void XRSessionUpdateParams_get_screenOrientation_m5BD0BD187D579592C71665C78BB09685F08BB23C_AdjustorThunk (void);
extern void XRSessionUpdateParams_set_screenOrientation_m95E8C1C9AEEFCD0577AE4605645FA8CD8F1D6B9B_AdjustorThunk (void);
extern void XRSessionUpdateParams_get_screenDimensions_mED2BC29E3B820C5CF96ED275DFA172B23EA52119_AdjustorThunk (void);
extern void XRSessionUpdateParams_set_screenDimensions_mC99924339E008CEFCF202EC394463F00DF0DA4B3_AdjustorThunk (void);
extern void XRSessionUpdateParams_GetHashCode_m735A861B2C2718DBF5588467EC76FC6EC77EFE8D_AdjustorThunk (void);
extern void XRSessionUpdateParams_Equals_mE2FA6A03BEBC662F543FA73D25561369FAEE7EAF_AdjustorThunk (void);
extern void XRSessionUpdateParams_ToString_m82D102D9405D9B5FCA96E55074C982F225287D80_AdjustorThunk (void);
extern void XRSessionUpdateParams_Equals_mEC4D21B1DFB2DB2327FCE21B43C144DD2003828C_AdjustorThunk (void);
extern void TrackableId_get_subId1_m1F4296FEADE76DF0379F20BCBD94E807E2EF021F_AdjustorThunk (void);
extern void TrackableId_set_subId1_mFA12049C24961BC2FE7D41A2D0FE30DF4B3F39D2_AdjustorThunk (void);
extern void TrackableId_get_subId2_m53BAB4D373B736E473381B24CB608EEF666BA24E_AdjustorThunk (void);
extern void TrackableId_set_subId2_mB7AA91412C0731CF59A8CC24CF75012D3C77C76D_AdjustorThunk (void);
extern void TrackableId__ctor_mB12C56ADDEFA44578A429DDA57A6C78B833B41F5_AdjustorThunk (void);
extern void TrackableId__ctor_m75F2739A83A25E2B7C34DE87E85187F79A4C86AF_AdjustorThunk (void);
extern void TrackableId_ToString_m4BE1AD91726751D994E6FB864B231BE5D7D3F85F_AdjustorThunk (void);
extern void TrackableId_GetHashCode_m6150BF091C3C17A84021CC18B443D5C297F89537_AdjustorThunk (void);
extern void TrackableId_Equals_m67C98169A04DB96CCEBC08A05B3FF9544B52C3E5_AdjustorThunk (void);
extern void TrackableId_Equals_m7263BB158392C4F1B57BEE16D4F1FBBCF01E4A6E_AdjustorThunk (void);
extern void XRTextureDescriptor_get_nativeTexture_m1E27C0E1DC11DDC6139178509EE91B8DF54DBAD4_AdjustorThunk (void);
extern void XRTextureDescriptor_set_nativeTexture_mE5EF6CBBBE13191EF65501EC9A45C2F64964B27D_AdjustorThunk (void);
extern void XRTextureDescriptor_get_width_m570472F03994BC63F21751414105A2E0C112DBF2_AdjustorThunk (void);
extern void XRTextureDescriptor_set_width_mA5D674B5378CB5B8AADD7A93E027CBF4BD27A37C_AdjustorThunk (void);
extern void XRTextureDescriptor_get_height_mC0B37241C24FA883E2594B9411080EDF654E3E01_AdjustorThunk (void);
extern void XRTextureDescriptor_set_height_mCD63667233B39883DF1E431446ED926AC3AF3992_AdjustorThunk (void);
extern void XRTextureDescriptor_get_mipmapCount_m4B2ED0D6EBE06AD86E356203B4AB5DE3807C1D31_AdjustorThunk (void);
extern void XRTextureDescriptor_set_mipmapCount_m7B8AAB937C5157B15A280672BC5C105FAF30D7E8_AdjustorThunk (void);
extern void XRTextureDescriptor_get_format_mA745AA87046D4FE4846C11B8285B980FF6DDDD1A_AdjustorThunk (void);
extern void XRTextureDescriptor_set_format_mAB9FB1797A83CC68AC222A861C185FE2F8035058_AdjustorThunk (void);
extern void XRTextureDescriptor_get_propertyNameId_mF5A620F0DAEE746BDD293DB7F02909FB5404DCC1_AdjustorThunk (void);
extern void XRTextureDescriptor_set_propertyNameId_m4D99BAF8AF884D653834D29D124F106A4AD7189D_AdjustorThunk (void);
extern void XRTextureDescriptor_get_valid_mBEE2CC268CC8773618BAB7794118746E235A6761_AdjustorThunk (void);
extern void XRTextureDescriptor_get_depth_m5885EBF7D767C918B1483D63D1B11EE60D939E7D_AdjustorThunk (void);
extern void XRTextureDescriptor_set_depth_mD62E28995B11B8631C2DF7B02416A2D310F35C49_AdjustorThunk (void);
extern void XRTextureDescriptor_get_dimension_mAEB2447102404A845F9B20317A2AB82B956E4A12_AdjustorThunk (void);
extern void XRTextureDescriptor_set_dimension_m75DC4703441BF9E812D18C0DFBF0A9839A52554B_AdjustorThunk (void);
extern void XRTextureDescriptor__ctor_m32EAA2098F51625289A1BFEFFAC002BA9F274ACF_AdjustorThunk (void);
extern void XRTextureDescriptor_hasIdenticalTextureMetadata_mB4DA1A4CFF4ABB66F8FF3AF1F310E60BA1B3F872_AdjustorThunk (void);
extern void XRTextureDescriptor_Reset_m1BE8024830BA7AFB94AAD01731FDB449DD12A01F_AdjustorThunk (void);
extern void XRTextureDescriptor_Equals_m4931F85C225CAC63EC71FBCE246204E244B6CA2B_AdjustorThunk (void);
extern void XRTextureDescriptor_Equals_m42127F01DF3CDEA1F38CF07E6057E8AD9E6F4570_AdjustorThunk (void);
extern void XRTextureDescriptor_GetHashCode_mFEB456F0A0985232D0E342B8F10669149F190012_AdjustorThunk (void);
extern void XRTextureDescriptor_ToString_m452F36D253986001921C5F627E67E2452D685493_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[726] = 
{
	{ 0x06000004, XRAnchor__ctor_mDD8A7F48E03A25972AA93D2C89C1D773635CA15B_AdjustorThunk },
	{ 0x06000005, XRAnchor__ctor_m3C8D3F14E6CD1FC66CB5E27096253B81BAA4C75C_AdjustorThunk },
	{ 0x06000006, XRAnchor_get_trackableId_m0F50E81D0152D0BA4152EF9B66F648EF9FC664AE_AdjustorThunk },
	{ 0x06000007, XRAnchor_get_pose_m2347783C1262EEFBC0B817EF0357FA4BB4BF053F_AdjustorThunk },
	{ 0x06000008, XRAnchor_get_trackingState_m6124A26C36CA93C25C57548576CB00C1F496ED83_AdjustorThunk },
	{ 0x06000009, XRAnchor_get_nativePtr_mC0551FA7E8DB8A0DA1EAE02D9B0BFD9D47389C26_AdjustorThunk },
	{ 0x0600000A, XRAnchor_get_sessionId_m719628E8A58027C75FF2CEA3345DC41200FB5F76_AdjustorThunk },
	{ 0x0600000B, XRAnchor_GetHashCode_mEFA5E37600C1A0B56F911227326704C17C3B5400_AdjustorThunk },
	{ 0x0600000C, XRAnchor_Equals_m8F3408527C2CF86CF0A09AE74BF790F8E60ED6F1_AdjustorThunk },
	{ 0x0600000D, XRAnchor_Equals_mFD12F373615A9015CB110787F6FF06CDAAC1433F_AdjustorThunk },
	{ 0x0600001E, Cinfo_get_id_mFF66DF9642403D18476451C30BD5F2FC89CAF8B1_AdjustorThunk },
	{ 0x0600001F, Cinfo_set_id_m6344F3700C1D743D82AB9E74925F7687925734A6_AdjustorThunk },
	{ 0x06000020, Cinfo_get_providerType_m2D11E14B5E4C48474C00C579D4C2F5F45970D70B_AdjustorThunk },
	{ 0x06000021, Cinfo_set_providerType_m0E291C22B0B2CF634024F32ECA2E424F18157ACF_AdjustorThunk },
	{ 0x06000022, Cinfo_get_subsystemTypeOverride_mD1DEE5FBF656FDF4BF5E28B60C04F53B6D146C3B_AdjustorThunk },
	{ 0x06000023, Cinfo_set_subsystemTypeOverride_mD5C5CAAC167444FE1BD48C1A16AA904E8DFF7052_AdjustorThunk },
	{ 0x06000024, Cinfo_get_subsystemImplementationType_m9F7B46950CE7957D33333EBBBAA65B27407831FA_AdjustorThunk },
	{ 0x06000025, Cinfo_set_subsystemImplementationType_m97E16A222C068284404DA8CC6C828D34E2F15265_AdjustorThunk },
	{ 0x06000026, Cinfo_get_supportsTrackableAttachments_m2BA4A0E85635C4D85059CDF713EE7FC21F80DBF4_AdjustorThunk },
	{ 0x06000027, Cinfo_set_supportsTrackableAttachments_m58F12783B1EF42ED420CC99E0585FA6ED046C4AD_AdjustorThunk },
	{ 0x06000028, Cinfo_GetHashCode_mE063F9FDF568ECC85F4D30998CB9A071A82C3859_AdjustorThunk },
	{ 0x06000029, Cinfo_Equals_m11F554099FC7163A8405E15A238CD1084BCCB65E_AdjustorThunk },
	{ 0x0600002A, Cinfo_Equals_mD8F45C05DFDE73ABDD26DC002B6F0B1506149F6D_AdjustorThunk },
	{ 0x0600002E, XRCameraConfiguration_get_width_mCEA441DFABEDE3E552A2D4452508BCE923B6C3C6_AdjustorThunk },
	{ 0x0600002F, XRCameraConfiguration_get_height_m9130BF72BE684B67C2100DD1624AF851E42B81A8_AdjustorThunk },
	{ 0x06000030, XRCameraConfiguration_get_resolution_m8EB20C15322147BCA971867F394BC0E0EDCB5A0D_AdjustorThunk },
	{ 0x06000031, XRCameraConfiguration_get_framerate_m3BFA6E6FB947828EDC20AC9CED31391634F5EB6F_AdjustorThunk },
	{ 0x06000032, XRCameraConfiguration_get_depthSensorSupported_m05B25DB3D4E83E385BE419109288B77684532A53_AdjustorThunk },
	{ 0x06000033, XRCameraConfiguration_get_nativeConfigurationHandle_mD9C92AE35660E0441A296301336578BA11313155_AdjustorThunk },
	{ 0x06000034, XRCameraConfiguration__ctor_m6A41DA8E8540120FABB6436C2DCB07B6BC520281_AdjustorThunk },
	{ 0x06000035, XRCameraConfiguration__ctor_m2943E6AD678C2106CF20EA8CA967F2EAB15303FB_AdjustorThunk },
	{ 0x06000036, XRCameraConfiguration__ctor_m0C8EC1223917D880B58850B06E1DC4902F269C4C_AdjustorThunk },
	{ 0x06000037, XRCameraConfiguration_ToString_mD69E2A39496C4B88A7089AF64A13057585A6F159_AdjustorThunk },
	{ 0x06000038, XRCameraConfiguration_GetHashCode_mE50DD8C034ED9415443191DF89F044B05510CEB7_AdjustorThunk },
	{ 0x06000039, XRCameraConfiguration_Equals_m26024336DA6F68CDCBF7916F6B6BF690DF152FA5_AdjustorThunk },
	{ 0x0600003A, XRCameraConfiguration_Equals_mCFE381E6FB6B3650DCAB79FB6894DA8BB511A708_AdjustorThunk },
	{ 0x0600003D, XRCameraFrame_get_timestampNs_m93571F53415C7DC6195854F3297E95D2E55A4DFB_AdjustorThunk },
	{ 0x0600003E, XRCameraFrame_get_averageBrightness_mD1106801D777BFB9EE60FDE5DE194EBACEFB6071_AdjustorThunk },
	{ 0x0600003F, XRCameraFrame_get_averageColorTemperature_m29B8FBE0061F8895678D3C4DA5BAB7BDBE154D4E_AdjustorThunk },
	{ 0x06000040, XRCameraFrame_get_colorCorrection_m00236A30115F91E2696EAAAF6F1CDF9CA0F83354_AdjustorThunk },
	{ 0x06000041, XRCameraFrame_get_projectionMatrix_mDE497D5208A1D08226B6B6C7521F53125E6EB9BD_AdjustorThunk },
	{ 0x06000042, XRCameraFrame_get_displayMatrix_m221E85929B55C0B8F6AB494FF27CC3200A80F287_AdjustorThunk },
	{ 0x06000043, XRCameraFrame_get_trackingState_mA6E95E5F574FC6506C0F602E430C42763797779A_AdjustorThunk },
	{ 0x06000044, XRCameraFrame_get_nativePtr_m4DED9CE0A7333F6A1E5C4932A6C98A8A0DD9E62D_AdjustorThunk },
	{ 0x06000045, XRCameraFrame_get_properties_m0C853765A7C76148A439A2C275E3687659DD8DFB_AdjustorThunk },
	{ 0x06000046, XRCameraFrame_get_averageIntensityInLumens_m639F0315B64DA5EC8321609C8673EA14A7263115_AdjustorThunk },
	{ 0x06000047, XRCameraFrame_get_exposureDuration_m4D8412C33F590A282E1671AFD89CC543837BD007_AdjustorThunk },
	{ 0x06000048, XRCameraFrame_get_exposureOffset_m9683C51CB26F830F5FC5720AD0FD541EC053202E_AdjustorThunk },
	{ 0x06000049, XRCameraFrame_get_mainLightIntensityLumens_m55B353C41D7A9E00F596D4BAD4813793C9754BE8_AdjustorThunk },
	{ 0x0600004A, XRCameraFrame_get_mainLightColor_mE96BCE9B4E4E241AF9F6BC758942EB8D4584138C_AdjustorThunk },
	{ 0x0600004B, XRCameraFrame_get_mainLightDirection_mF33683D8BF23ADFB8EBC2D2875C2EDF5922F21B8_AdjustorThunk },
	{ 0x0600004C, XRCameraFrame_get_ambientSphericalHarmonics_mB62D6BDCC0A0DAAB5C057225A84289502076EFCE_AdjustorThunk },
	{ 0x0600004D, XRCameraFrame_get_cameraGrain_m7FF742DB5555C9D84DCD7937828C612FAACFEEFE_AdjustorThunk },
	{ 0x0600004E, XRCameraFrame_get_noiseIntensity_mA1D17EA6D00D7FF958FFF6A62B99B34B052F2FEC_AdjustorThunk },
	{ 0x0600004F, XRCameraFrame_get_hasTimestamp_mD6AD6768B71946B0643836ACD28BF32876A5E0FF_AdjustorThunk },
	{ 0x06000050, XRCameraFrame_get_hasAverageBrightness_m8CC4709AA168C8762763837B384B7332FC2B73B0_AdjustorThunk },
	{ 0x06000051, XRCameraFrame_get_hasAverageColorTemperature_m163AF5FAD20B5779A28550ED502F5037C4BDB93A_AdjustorThunk },
	{ 0x06000052, XRCameraFrame_get_hasColorCorrection_mCEB8BC23DF1997AB5DFCD013F56111FB8A8D118E_AdjustorThunk },
	{ 0x06000053, XRCameraFrame_get_hasProjectionMatrix_m850BCDBFBBD894BF56EEED3A82349A4E4811CC1F_AdjustorThunk },
	{ 0x06000054, XRCameraFrame_get_hasDisplayMatrix_m7D5DA2AA4F3C83B25714C0FED9EEAE1E51B95959_AdjustorThunk },
	{ 0x06000055, XRCameraFrame_get_hasAverageIntensityInLumens_m7E14C289B8D931F55B7A98D5075263E96CE3B4DE_AdjustorThunk },
	{ 0x06000056, XRCameraFrame_get_hasExposureDuration_m02C1ACB25E72D090C9A56FC158E8D4B0D3C04D50_AdjustorThunk },
	{ 0x06000057, XRCameraFrame_get_hasExposureOffset_m6A4048142BD1E59E403F858144092C5F7846CA53_AdjustorThunk },
	{ 0x06000058, XRCameraFrame_get_hasMainLightIntensityLumens_mA423D7DEF78D1888AFED8BF17B3E1037C24E469B_AdjustorThunk },
	{ 0x06000059, XRCameraFrame_get_hasMainLightColor_m07A53E75212D8BA3582613228AC0DACBDDF983FF_AdjustorThunk },
	{ 0x0600005A, XRCameraFrame_get_hasMainLightDirection_m67DFB7C0DAD130D98290130131EDC4BA62818B5E_AdjustorThunk },
	{ 0x0600005B, XRCameraFrame_get_hasAmbientSphericalHarmonics_m45F02EFE6E47FC9B9AEE4F1B6AEC4F9E7FF1F92A_AdjustorThunk },
	{ 0x0600005C, XRCameraFrame_get_hasCameraGrain_mC37056CCCDBEFD620038107A078B6A39F61D99AE_AdjustorThunk },
	{ 0x0600005D, XRCameraFrame_get_hasNoiseIntensity_m076641BB06432F1F27EFD353B6E7116B098BE4B7_AdjustorThunk },
	{ 0x0600005E, XRCameraFrame_get_hasExifData_mB7025B1AA39F37A2DAB00F455D9803096B12DF24_AdjustorThunk },
	{ 0x0600005F, XRCameraFrame__ctor_m7A19EA8CCC1391BE7463FF9B492863BDA975E90B_AdjustorThunk },
	{ 0x06000060, XRCameraFrame__ctor_m991480EB7E8C7C3A8C63974AFE3FD8900A2EBC03_AdjustorThunk },
	{ 0x06000061, XRCameraFrame_TryGetTimestamp_m60FE1777D7379C288482A23E5A7C5B297F1DDD94_AdjustorThunk },
	{ 0x06000062, XRCameraFrame_TryGetAverageBrightness_m1FDC9DCF0A34227DC5ECB78A5E80E614F0063C98_AdjustorThunk },
	{ 0x06000063, XRCameraFrame_TryGetAverageColorTemperature_m2737123C8E46EF119C04162FAD8EAA312FA2EF3B_AdjustorThunk },
	{ 0x06000064, XRCameraFrame_TryGetProjectionMatrix_mC25F35BF17829DBEDA748D61362289E4842B5098_AdjustorThunk },
	{ 0x06000065, XRCameraFrame_TryGetDisplayMatrix_mA034AD550B172CACDDED708A12FFBA548E750312_AdjustorThunk },
	{ 0x06000066, XRCameraFrame_TryGetAverageIntensityInLumens_mE5FC24C1E3D49FB679137698834F22BF258DD674_AdjustorThunk },
	{ 0x06000067, XRCameraFrame_TryGetExifData_m99AAC21B7048242487A8D48DE8D92AC9F4C53AF4_AdjustorThunk },
	{ 0x06000068, XRCameraFrame_Equals_mA1542DDF01588CB1AEDFEB763F63684C18B717C8_AdjustorThunk },
	{ 0x06000069, XRCameraFrame_Equals_m53FA29D21C4E68E89D59E8904EF7222571C04A50_AdjustorThunk },
	{ 0x0600006C, XRCameraFrame_GetHashCode_m9392ED676A0D41F404CF03A0C570068AC2832352_AdjustorThunk },
	{ 0x0600006D, XRCameraFrame_ToString_m4825A21E18219B4B626C1A2CB3EDD006DFCA44AA_AdjustorThunk },
	{ 0x0600006E, XRCameraFrameExifData_get_hasApertureValue_m2C361DC00694AE380ABE97C9A99F6B645CC8F53F_AdjustorThunk },
	{ 0x0600006F, XRCameraFrameExifData_get_hasBrightnessValue_m39C2542ED3A9A2F1BA136FE4EE381DA0C21ED23D_AdjustorThunk },
	{ 0x06000070, XRCameraFrameExifData_get_hasExposureTime_mF0CE4E1C0E88FF4D3B727E72DA507117A252CC0C_AdjustorThunk },
	{ 0x06000071, XRCameraFrameExifData_get_hasShutterSpeedValue_m3AC9A6CD82AAC78E5E74D1AA654CBCC0D7F7B2C4_AdjustorThunk },
	{ 0x06000072, XRCameraFrameExifData_get_hasExposureBiasValue_m2A069367FABB0671F888CDEDEC473BE1841211E2_AdjustorThunk },
	{ 0x06000073, XRCameraFrameExifData_get_hasFNumber_mFCA781B3A5BE10C965D76A608B49CDE619ED46F4_AdjustorThunk },
	{ 0x06000074, XRCameraFrameExifData_get_hasFocalLength_m0107A9D19FE993FB4836FB1EA5BF17656F2FDEF0_AdjustorThunk },
	{ 0x06000075, XRCameraFrameExifData_get_hasFlash_m6CFD79284C340A886C21336708FE674F6EF80254_AdjustorThunk },
	{ 0x06000076, XRCameraFrameExifData_get_hasColorSpace_m8B09CAAB1A79D588F04F3D0E26F42261B87EFE7F_AdjustorThunk },
	{ 0x06000077, XRCameraFrameExifData_get_hasPhotographicSensitivity_m8F78AC08D6DFE157D1B1D40D1D06517CAF6FFB1A_AdjustorThunk },
	{ 0x06000078, XRCameraFrameExifData_get_hasMeteringMode_m62016BF52989FE5849139AFB087F6C0244517D7C_AdjustorThunk },
	{ 0x06000079, XRCameraFrameExifData_get_nativePtr_mCB1987D3B4CABB2C972BE7D4E92365763550783E_AdjustorThunk },
	{ 0x0600007A, XRCameraFrameExifData__ctor_mC632ECB5B132C6297A8641A5061024B71F850242_AdjustorThunk },
	{ 0x0600007B, XRCameraFrameExifData_TryGetApertureValue_m4F59F7DEF4992F533DE48D18DBBB0A01EB0E684C_AdjustorThunk },
	{ 0x0600007C, XRCameraFrameExifData_TryGetBrightnessValue_m3C19A37A3534F7E451759E89578260FADBC9B14F_AdjustorThunk },
	{ 0x0600007D, XRCameraFrameExifData_TryGetExposureTime_m46BBCA6CA64E217ED60F3D878983314A59C6D5FA_AdjustorThunk },
	{ 0x0600007E, XRCameraFrameExifData_TryGetShutterSpeedValue_mC955FAA772EC0893B3D06E4ECEA8EE80023FE429_AdjustorThunk },
	{ 0x0600007F, XRCameraFrameExifData_TryGetExposureBiasValue_m52A2F65945A75DAC6F0BD013EB94DF65D5A6BF0C_AdjustorThunk },
	{ 0x06000080, XRCameraFrameExifData_TryGetFNumber_m7512F213AD901E6FCDCDF7FD9A582AB322F6063D_AdjustorThunk },
	{ 0x06000081, XRCameraFrameExifData_TryGetFocalLength_m0109D48BD14F7E853514F66ACD493DD1657EC138_AdjustorThunk },
	{ 0x06000082, XRCameraFrameExifData_TryGetFlash_m8BE2090E79E851BA4961115DC320CEB3B6A2A4AB_AdjustorThunk },
	{ 0x06000083, XRCameraFrameExifData_TryGetColorSpace_mDD98A093493C3E1659125B797F0E8C49E2C25E76_AdjustorThunk },
	{ 0x06000084, XRCameraFrameExifData_TryGetPhotographicSensitivity_m215A7867030DC8738CFDF104E0CE9DA2569A6307_AdjustorThunk },
	{ 0x06000085, XRCameraFrameExifData_TryGetMeteringMode_m3DE1E2935F15F9F91899A58E0585C3CB1E7BFE34_AdjustorThunk },
	{ 0x06000086, XRCameraFrameExifData_get_hasAnyProperties_m608D0509C48BB26408E3B58ADE152ED372101624_AdjustorThunk },
	{ 0x06000087, XRCameraFrameExifData_Equals_mE16880FA32EC214A99E76AA88BC65988E6A61919_AdjustorThunk },
	{ 0x06000088, XRCameraFrameExifData_Equals_m55925A51CE7F4B028CF9B77393BD8FCCB56F7302_AdjustorThunk },
	{ 0x0600008B, XRCameraFrameExifData_GetHashCode_m3FB9ECCBAE6452B72A6B167F589098899819AFAA_AdjustorThunk },
	{ 0x0600008C, XRCameraFrameExifData_ToString_m9200B453787D807084089B68F941C9B768A8D374_AdjustorThunk },
	{ 0x0600008D, XRCameraIntrinsics_get_focalLength_m9B19B7C0AF4CDAF1C8BA121C20BE8A80A7DF778D_AdjustorThunk },
	{ 0x0600008E, XRCameraIntrinsics_get_principalPoint_m677A9880F319E54576353AD01EF0936317E1D83D_AdjustorThunk },
	{ 0x0600008F, XRCameraIntrinsics_get_resolution_mDC07EA111909E8903F1B89577FA2A6BF8FB14D52_AdjustorThunk },
	{ 0x06000090, XRCameraIntrinsics__ctor_mA7F2F4A7709FC7DA6E9560367A08C28374365020_AdjustorThunk },
	{ 0x06000091, XRCameraIntrinsics_Equals_m7C6C306C554F5F2A69E5CB831FB2C38F7A252866_AdjustorThunk },
	{ 0x06000092, XRCameraIntrinsics_Equals_m81F681CB1C13344784F9B2DD6ACE032F2C9A06AE_AdjustorThunk },
	{ 0x06000095, XRCameraIntrinsics_GetHashCode_mEC06B793ED903AC34149EAA935C66284947CDF63_AdjustorThunk },
	{ 0x06000096, XRCameraIntrinsics_ToString_m3C7131BDF5882D7F01DA3EC7A7544A16E7F6A783_AdjustorThunk },
	{ 0x06000097, XRCameraParams_get_zNear_mECA80F2D2C74318641F94031BB7964DD06ABEA75_AdjustorThunk },
	{ 0x06000098, XRCameraParams_set_zNear_m13DFECBAE558037DEBE998F3EFF2E1C6372EE6E0_AdjustorThunk },
	{ 0x06000099, XRCameraParams_get_zFar_mDF023917C3AD6AA2C909A2295219F34B085638DA_AdjustorThunk },
	{ 0x0600009A, XRCameraParams_set_zFar_mA528373BCB66A9DE2A393512B883B932AB02D600_AdjustorThunk },
	{ 0x0600009B, XRCameraParams_get_screenWidth_m791F2E175953698508D73BF5B38087BA66875FDA_AdjustorThunk },
	{ 0x0600009C, XRCameraParams_set_screenWidth_mA367A9BD005F2F052549E9B509F4D41F276CA021_AdjustorThunk },
	{ 0x0600009D, XRCameraParams_get_screenHeight_m560E3D0692A29242E6E137CA8895C3754E8A7745_AdjustorThunk },
	{ 0x0600009E, XRCameraParams_set_screenHeight_m7F6C7A3B7F3D7AEBE074A19FF20EF3DDACB79DE9_AdjustorThunk },
	{ 0x0600009F, XRCameraParams_get_screenOrientation_m0EC129A67B19D30348027E60C9A6C982DBC89D3A_AdjustorThunk },
	{ 0x060000A0, XRCameraParams_set_screenOrientation_m9AA6D552ED0B67E9560CDF2C775FC27AA7A83A07_AdjustorThunk },
	{ 0x060000A1, XRCameraParams_Equals_m5C32A8D9FE83014E8A424C2D09688635E88A86B0_AdjustorThunk },
	{ 0x060000A2, XRCameraParams_Equals_mD063C934A21CE21B40F834E0C90AFF645A236CD6_AdjustorThunk },
	{ 0x060000A5, XRCameraParams_GetHashCode_m1F7C2E3CC02169BDEC645B0CE0E540BB1FE1FCD5_AdjustorThunk },
	{ 0x060000A6, XRCameraParams_ToString_mFD8C6218C724EAAF2F9A953CBB08AA3BBF67DB64_AdjustorThunk },
	{ 0x060000E2, XRCameraSubsystemCinfo_get_id_mDCD0C107058AEA702A80B8E305F262CAB8E07FD0_AdjustorThunk },
	{ 0x060000E3, XRCameraSubsystemCinfo_set_id_m24A04B94756616FBA387977AF0F6A894D4DC5BCE_AdjustorThunk },
	{ 0x060000E4, XRCameraSubsystemCinfo_get_providerType_mFBE1614FF701AC94FE53078962B455A355EA45A7_AdjustorThunk },
	{ 0x060000E5, XRCameraSubsystemCinfo_set_providerType_mFE0D3D8FEAF0FFEEA66D0E2C7CABB36944EAD484_AdjustorThunk },
	{ 0x060000E6, XRCameraSubsystemCinfo_get_subsystemTypeOverride_m5CD01638A223E6C620ADDDDBA05F7539ED5229C4_AdjustorThunk },
	{ 0x060000E7, XRCameraSubsystemCinfo_set_subsystemTypeOverride_m82AD4886D0CE8C4D762AC459630CBE13D024FCD2_AdjustorThunk },
	{ 0x060000E8, XRCameraSubsystemCinfo_get_implementationType_mCD8363878940A476D3A3D5CD999A01ED736529F7_AdjustorThunk },
	{ 0x060000E9, XRCameraSubsystemCinfo_set_implementationType_m8A37CCAA77FD7B184244763108178DC60BA69D0E_AdjustorThunk },
	{ 0x060000EA, XRCameraSubsystemCinfo_get_supportsAverageBrightness_m694C685E738909EABF44140E672CB8176649D7E6_AdjustorThunk },
	{ 0x060000EB, XRCameraSubsystemCinfo_set_supportsAverageBrightness_mD5F49B41F00DBC4C531FB5593E572E6A67B1EE77_AdjustorThunk },
	{ 0x060000EC, XRCameraSubsystemCinfo_get_supportsAverageColorTemperature_mDCCD0A414E292EAD5FB817C6AC16DE8AF8C7D076_AdjustorThunk },
	{ 0x060000ED, XRCameraSubsystemCinfo_set_supportsAverageColorTemperature_mBF21A51F1C4ED4A72AF54588CF9E08DDC92A213B_AdjustorThunk },
	{ 0x060000EE, XRCameraSubsystemCinfo_get_supportsAverageIntensityInLumens_mD46BA194C3AC26510694D649673015A450D0F019_AdjustorThunk },
	{ 0x060000EF, XRCameraSubsystemCinfo_set_supportsAverageIntensityInLumens_m7FEF058FFE0C0B54E91A8FC8500F88C17B294743_AdjustorThunk },
	{ 0x060000F0, XRCameraSubsystemCinfo_get_supportsCameraGrain_m9D63DE92F3FD22536481F75DC9092F3526EB9FF2_AdjustorThunk },
	{ 0x060000F1, XRCameraSubsystemCinfo_set_supportsCameraGrain_m3E5D929246B89F7B2EFE1F7E75B9A20C67A556C9_AdjustorThunk },
	{ 0x060000F2, XRCameraSubsystemCinfo_get_supportsColorCorrection_m8BEA88F615A8CD5FC068D9F7BE8039C68A110A15_AdjustorThunk },
	{ 0x060000F3, XRCameraSubsystemCinfo_set_supportsColorCorrection_m5E9520636C4268DFAFB9E84E5901FD65E14BBB7F_AdjustorThunk },
	{ 0x060000F4, XRCameraSubsystemCinfo_get_supportsDisplayMatrix_m33AECE0011BFF7206E0F6A99C3C32ACD28DF09B0_AdjustorThunk },
	{ 0x060000F5, XRCameraSubsystemCinfo_set_supportsDisplayMatrix_m0AF4D70253EF5AC5124C747D6F1AF39B6E199B53_AdjustorThunk },
	{ 0x060000F6, XRCameraSubsystemCinfo_get_supportsProjectionMatrix_mD62E482D66D23D02FBF1591EFE8CBF6AB2B7AA60_AdjustorThunk },
	{ 0x060000F7, XRCameraSubsystemCinfo_set_supportsProjectionMatrix_m5A4C42AD394D5FEA20DB5FE1C045634929839B7A_AdjustorThunk },
	{ 0x060000F8, XRCameraSubsystemCinfo_get_supportsTimestamp_m53905529FC20BB4B064986AC5E6586DAF996148F_AdjustorThunk },
	{ 0x060000F9, XRCameraSubsystemCinfo_set_supportsTimestamp_m46561CEC2016CA165B4E725395C0E8836C0B46F3_AdjustorThunk },
	{ 0x060000FA, XRCameraSubsystemCinfo_get_supportsCameraConfigurations_m107FD5D148D109EDDE9345754995ACF01D7A3F67_AdjustorThunk },
	{ 0x060000FB, XRCameraSubsystemCinfo_set_supportsCameraConfigurations_m5425AF5D348E1918644909C45ABA7220D2A8B92F_AdjustorThunk },
	{ 0x060000FC, XRCameraSubsystemCinfo_get_supportsCameraImage_m3C11104CBEE0AF690F3A4F9729F78F48E1065970_AdjustorThunk },
	{ 0x060000FD, XRCameraSubsystemCinfo_set_supportsCameraImage_mC9312D97D6F2508F8692EF4C40909CBA55F6D769_AdjustorThunk },
	{ 0x060000FE, XRCameraSubsystemCinfo_get_supportsFocusModes_m0B2A62A1F5A5603B2BE190A5B5AB0703842E6B51_AdjustorThunk },
	{ 0x060000FF, XRCameraSubsystemCinfo_set_supportsFocusModes_m7A495A132ED5160BF69E69CAAA132F83319A3191_AdjustorThunk },
	{ 0x06000100, XRCameraSubsystemCinfo_get_supportsFaceTrackingAmbientIntensityLightEstimation_mA6C68E53BB8242F222104A33EA99EF7AFF754A9B_AdjustorThunk },
	{ 0x06000101, XRCameraSubsystemCinfo_set_supportsFaceTrackingAmbientIntensityLightEstimation_m17EC791181F76254B286308B213F19E085DE36D9_AdjustorThunk },
	{ 0x06000102, XRCameraSubsystemCinfo_get_supportsFaceTrackingHDRLightEstimation_m28390AEBE6EBE78D410C0CB9D8D7DBE237D8DEFF_AdjustorThunk },
	{ 0x06000103, XRCameraSubsystemCinfo_set_supportsFaceTrackingHDRLightEstimation_m022FA258FE1F3E9D1D94D02985C3459298093D3A_AdjustorThunk },
	{ 0x06000104, XRCameraSubsystemCinfo_get_supportsWorldTrackingAmbientIntensityLightEstimation_m85AD9C10C625555A50D39FE3FE5B75E4DFEAC0A4_AdjustorThunk },
	{ 0x06000105, XRCameraSubsystemCinfo_set_supportsWorldTrackingAmbientIntensityLightEstimation_m5F43C3317A17DAFA6ACF9550069FB10F8EB50300_AdjustorThunk },
	{ 0x06000106, XRCameraSubsystemCinfo_get_supportsWorldTrackingHDRLightEstimation_mACCAED13DF9023738352CB5C89D5CBCF01B46899_AdjustorThunk },
	{ 0x06000107, XRCameraSubsystemCinfo_set_supportsWorldTrackingHDRLightEstimation_mF04D46841BB7C8BEF8A17300F6F12491A8AC895C_AdjustorThunk },
	{ 0x06000108, XRCameraSubsystemCinfo_get_supportsExifData_m594585E6727EED9B24E835F2E14109565327F680_AdjustorThunk },
	{ 0x06000109, XRCameraSubsystemCinfo_set_supportsExifData_mBAA63CA8656661FF0D35EB93605744BF7ADCA0B7_AdjustorThunk },
	{ 0x0600010A, XRCameraSubsystemCinfo_Equals_mED29F3CB627AF187AC3CB817FC3DC7905B0228A0_AdjustorThunk },
	{ 0x0600010B, XRCameraSubsystemCinfo_Equals_m6D8C4679C25B3C93C0BAC84C2E8214DC861285F3_AdjustorThunk },
	{ 0x0600010E, XRCameraSubsystemCinfo_GetHashCode_m9AD75EE7E43274694277A4703099621467E69114_AdjustorThunk },
	{ 0x06000131, Configuration_get_descriptor_m3C4973351367EA0BD9E48DA1E2201D8803BA8D1E_AdjustorThunk },
	{ 0x06000132, Configuration_set_descriptor_mBB8354A895DDAD560293EEF81BFFDB4CB30070F0_AdjustorThunk },
	{ 0x06000133, Configuration_get_features_m704F372E940AF1DB435C1EBFF8E48EAD4E8B3776_AdjustorThunk },
	{ 0x06000134, Configuration_set_features_m9F397F777C9593646918ECB4AF778336900ED3EC_AdjustorThunk },
	{ 0x06000135, Configuration__ctor_m4D712D942AEBEF0DA6B5687C1D9CD4E24F0ED4AE_AdjustorThunk },
	{ 0x06000136, Configuration_GetHashCode_m19DCAAF7939DB5DAAF29A2A4E994D41F66FB73D2_AdjustorThunk },
	{ 0x06000137, Configuration_Equals_mFC36BD166DE654A704096918BDA1FE9E34A7B7E6_AdjustorThunk },
	{ 0x06000138, Configuration_Equals_m8D6DE5FC0FAD2DD34D2F3CEF1738FC3A2F131A91_AdjustorThunk },
	{ 0x0600013D, ConfigurationDescriptor_get_identifier_m858F4B730002C1823D283460115DA65C6A46BCB6_AdjustorThunk },
	{ 0x0600013E, ConfigurationDescriptor_get_capabilities_m6A4EF4C0E0FE3671E8564EF13BA2A5B4264CF938_AdjustorThunk },
	{ 0x0600013F, ConfigurationDescriptor_get_rank_mEDFBF5E2173FA84A0695BB01A6A40860794F6FA8_AdjustorThunk },
	{ 0x06000140, ConfigurationDescriptor__ctor_m79BD6295C5A725B6B65CA3A4281EC801C12B2C41_AdjustorThunk },
	{ 0x06000141, ConfigurationDescriptor_HexString_mA5D97CE4BCD0DD66455BB9BE281302136382BCD5_AdjustorThunk },
	{ 0x06000142, ConfigurationDescriptor_ToString_m20EA191A42A1855B5E97CD8949F6AE5B9ACBDF65_AdjustorThunk },
	{ 0x06000143, ConfigurationDescriptor_GetHashCode_mAD2765B79FFD1806DEA8D927D928C496AAADB411_AdjustorThunk },
	{ 0x06000144, ConfigurationDescriptor_Equals_mC5F92BBF22292A48CAD47A31EF13F3D5A0DC4091_AdjustorThunk },
	{ 0x06000145, ConfigurationDescriptor_Equals_m4FAAC4A13BF03211A9C3EB66F65FB48BE334A611_AdjustorThunk },
	{ 0x06000159, XRCpuImage_get_dimensions_m49AF06CB1BDF89E7C9EC343D3260BD73ECABF414_AdjustorThunk },
	{ 0x0600015A, XRCpuImage_set_dimensions_m67B3C05A3CA2F0CED5B4E1808967FF2BF77AED86_AdjustorThunk },
	{ 0x0600015B, XRCpuImage_get_width_m176240EBEBBD41DC5AEF33F945C88E9492370AFA_AdjustorThunk },
	{ 0x0600015C, XRCpuImage_get_height_m139489AD26B264FA46EE5659258BBF9C6584E5E9_AdjustorThunk },
	{ 0x0600015D, XRCpuImage_get_planeCount_mEDCBE71D55CCC9FDA1B3ED951306875283E37B6B_AdjustorThunk },
	{ 0x0600015E, XRCpuImage_set_planeCount_m51DC647BC967DE5E565AA4ACF66B5B86FE380B13_AdjustorThunk },
	{ 0x0600015F, XRCpuImage_get_format_mB777BBC485ED5A88CD78536F78F43E9795DEEE20_AdjustorThunk },
	{ 0x06000160, XRCpuImage_set_format_mBC167A4F6985102169436A10C58AF5EBC17B4C1F_AdjustorThunk },
	{ 0x06000161, XRCpuImage_get_timestamp_mA80E146875C26B8F319B283C20A6BD499AD55B90_AdjustorThunk },
	{ 0x06000162, XRCpuImage_set_timestamp_m7FF97B03D5A4506993F8119BCB4BC47B185AA8D1_AdjustorThunk },
	{ 0x06000163, XRCpuImage_get_valid_mFF799BC0D09BF35BD3AEC063FF5558EE2EB6766F_AdjustorThunk },
	{ 0x06000164, XRCpuImage__ctor_m06AE81550FF74789CD8D66ABBA9B2F3D9D060612_AdjustorThunk },
	{ 0x06000165, XRCpuImage_FormatSupported_m15D60F33E5EB00039BA41B9C61AE114C1AA6B40A_AdjustorThunk },
	{ 0x06000166, XRCpuImage_GetPlane_m0C2A7BE6FE964FCF5A82273AA9DA6C135648721B_AdjustorThunk },
	{ 0x06000167, XRCpuImage_GetConvertedDataSize_mC7AF0A096D1FF758D3E086D3D43F778E9257D4BE_AdjustorThunk },
	{ 0x06000168, XRCpuImage_GetConvertedDataSize_m1A292AE01390513BEA935CC4C19A0F8FD52341DE_AdjustorThunk },
	{ 0x06000169, XRCpuImage_Convert_m04EB3992B85AEB87D03C5626EFD0A9C0158AC9CB_AdjustorThunk },
	{ 0x0600016A, XRCpuImage_Convert_mFE71425F0E4FD4ADB839551590FE9728BA037EE1_AdjustorThunk },
	{ 0x0600016B, XRCpuImage_ConvertAsync_mECB96371D5F7C49A4E995B285F9FC02FE4109814_AdjustorThunk },
	{ 0x0600016C, XRCpuImage_ConvertAsync_m881B8A7A185F454AB34151D858CA0AE8A54F2949_AdjustorThunk },
	{ 0x0600016E, XRCpuImage_ValidateNativeHandleAndThrow_mA1A3B64DF91A003BDB013FC04111945F03853395_AdjustorThunk },
	{ 0x0600016F, XRCpuImage_ValidateConversionParamsAndThrow_m39B351E15FD65E6969933F0EBE42CA63DD090E72_AdjustorThunk },
	{ 0x06000170, XRCpuImage_Dispose_m80B8CA56700DD5EB8A5613AA42F6F389D86A746B_AdjustorThunk },
	{ 0x06000171, XRCpuImage_GetHashCode_m4C976024EE9CFFDBE53682B8307FD0819F42E31C_AdjustorThunk },
	{ 0x06000172, XRCpuImage_Equals_mE00DCB100FC7743E62959883CBAF479ADEDCBAC3_AdjustorThunk },
	{ 0x06000173, XRCpuImage_Equals_m0BD02471E5A85EFF5F078CE0ACEDD4F969B66AB2_AdjustorThunk },
	{ 0x06000176, XRCpuImage_ToString_mA4BF5B6A1D341098584B0EC3E51D324092AE98ED_AdjustorThunk },
	{ 0x06000188, AsyncConversion_get_conversionParams_m467C52D7FD3E87614FA34FCB630EDD89289F12B0_AdjustorThunk },
	{ 0x06000189, AsyncConversion_set_conversionParams_m5144DBB33F2D0003BF4E2ED884B9D8AA4EE89071_AdjustorThunk },
	{ 0x0600018A, AsyncConversion_get_status_mB3A873407AF4E202A39758599DCEE52BEC196E2A_AdjustorThunk },
	{ 0x0600018B, AsyncConversion__ctor_mEEE052FCAD2BF2E9FBF78C829C0A11A6D5CD5ABD_AdjustorThunk },
	{ 0x0600018D, AsyncConversion_Dispose_m81B54378570A2C9C1009618A0380E5C204DD2AC6_AdjustorThunk },
	{ 0x0600018E, AsyncConversion_GetHashCode_mF14F2C21ADCA19EB7300B2F07B59B2758CE0195D_AdjustorThunk },
	{ 0x0600018F, AsyncConversion_Equals_m0D926BE22C3B6333F413DFB6E051021428C6D3D5_AdjustorThunk },
	{ 0x06000190, AsyncConversion_Equals_m84273ECB152DB76244DFD3BF9BC2AB648682F4A0_AdjustorThunk },
	{ 0x06000193, AsyncConversion_ToString_m59F4615A570F43A086B43067B37CCF382BEE5225_AdjustorThunk },
	{ 0x06000194, ConversionParams_get_inputRect_m59986429062905012283B892A6EE2DAD88A810FC_AdjustorThunk },
	{ 0x06000195, ConversionParams_set_inputRect_m7965864AED4C5176D58F3766D6BBB35DFF7BC903_AdjustorThunk },
	{ 0x06000196, ConversionParams_get_outputDimensions_m6295F96DCE9B406AB6D79E8CD86A6FF388CF5035_AdjustorThunk },
	{ 0x06000197, ConversionParams_set_outputDimensions_m97EC09EE536EA456A18894311BF75AC9D5A90A3B_AdjustorThunk },
	{ 0x06000198, ConversionParams_get_outputFormat_m8CD52ADADE8FFE505A90E02D9BD6C7D9EE1C8715_AdjustorThunk },
	{ 0x06000199, ConversionParams_set_outputFormat_mA82EA0ECC19D14AECBA318B9B485D08CFB46A1F4_AdjustorThunk },
	{ 0x0600019A, ConversionParams_get_transformation_m46ADA14AEDC98630828D5DCE19F1905233627CE7_AdjustorThunk },
	{ 0x0600019B, ConversionParams_set_transformation_mBCE73B14CCE8A31A258C6B8F6104446A2D495A0F_AdjustorThunk },
	{ 0x0600019C, ConversionParams__ctor_m2EA9FC7BD411FA61269B314ACD03174F5BB96273_AdjustorThunk },
	{ 0x0600019D, ConversionParams_GetHashCode_m15B2EFDD22B43B4201646E49BCB9155F67D5A12A_AdjustorThunk },
	{ 0x0600019E, ConversionParams_Equals_mD0A055A44755C75EFF2B09B2FD7C9C50D057020B_AdjustorThunk },
	{ 0x0600019F, ConversionParams_Equals_mDC60F1518FE83109D22DB3A1606C82930B16356D_AdjustorThunk },
	{ 0x060001A2, ConversionParams_ToString_mB11C93DA6E5D57F694BC5D64E5567958A647557C_AdjustorThunk },
	{ 0x060001A3, Plane_get_rowStride_m5461CF97009BA5CB09931F85D9C4E11BB298E01F_AdjustorThunk },
	{ 0x060001A4, Plane_set_rowStride_m03DE76183744D782EAB661F50D8191DA9CB34A31_AdjustorThunk },
	{ 0x060001A5, Plane_get_pixelStride_m78990A3DB8530B302D4B138E92BFEFF6F6F8D5E0_AdjustorThunk },
	{ 0x060001A6, Plane_set_pixelStride_m26005BF1B5C79A0B816D37C53CF5A34BA3499CFC_AdjustorThunk },
	{ 0x060001A7, Plane_get_data_m8A88D9DDDAB3081E788B3DCF7DE314D2E672B15D_AdjustorThunk },
	{ 0x060001A8, Plane_set_data_mFDEC268CEEE5FEBFC54FE823BEC6B3DCC4DB182E_AdjustorThunk },
	{ 0x060001A9, Plane__ctor_m9563F685C69A49502C62A7EA4F1F64FCC392A485_AdjustorThunk },
	{ 0x060001AA, Plane_GetHashCode_m81E44303AC89B3792D4238BEFF767D459D72FDD1_AdjustorThunk },
	{ 0x060001AB, Plane_Equals_m05599C5BA1316FF0667B8D3752DC2464E559A24E_AdjustorThunk },
	{ 0x060001AC, Plane_Equals_mD97496D640121AA88AF730F5DDE9F1ED6582842A_AdjustorThunk },
	{ 0x060001AF, Plane_ToString_m0844EFBBF3A11852B21C58FCBC543A554E838EE2_AdjustorThunk },
	{ 0x060001B0, Cinfo_get_dataPtr_m0865701DF77079918906809E61CCF8C080120AB1_AdjustorThunk },
	{ 0x060001B1, Cinfo_get_dataLength_mF704FE891CD1628CF48C8434DF1CD5C461A7EE86_AdjustorThunk },
	{ 0x060001B2, Cinfo_get_rowStride_m3CB25349C2380F5FC9022EB25A1FCF95C1498513_AdjustorThunk },
	{ 0x060001B3, Cinfo_get_pixelStride_m5A3C2E9C12F194F7237EF96FC12E319928E14A6E_AdjustorThunk },
	{ 0x060001B4, Cinfo__ctor_mF1859C21D692CA1783BF64CDD8C45BB1984C427F_AdjustorThunk },
	{ 0x060001B5, Cinfo_Equals_m8F4B3A7591D02605076B062B37312733D533D3AA_AdjustorThunk },
	{ 0x060001B6, Cinfo_Equals_mA464FE0F8B24D6AACEE40E5A290572D4ABBE1333_AdjustorThunk },
	{ 0x060001B9, Cinfo_GetHashCode_mDD348F8626D5B49ED6EC593263AFD20A21329F08_AdjustorThunk },
	{ 0x060001BA, Cinfo_ToString_m6CC828632F333B765128A31434D3DFF040F90754_AdjustorThunk },
	{ 0x060001BB, Cinfo_get_nativeHandle_m63F2835811F1DCFF2EE1AEBF8A8F7A1ADA1FD7E0_AdjustorThunk },
	{ 0x060001BC, Cinfo_get_dimensions_m7B777F060E825839302EA722B35E1BBB4E402D2A_AdjustorThunk },
	{ 0x060001BD, Cinfo_get_planeCount_m5D077F0399217E11C6A11378F5D08D86CC5CEA7F_AdjustorThunk },
	{ 0x060001BE, Cinfo_get_timestamp_mFFA3FA7E91717B748F6159B7E78FBFE2290F4E97_AdjustorThunk },
	{ 0x060001BF, Cinfo_get_format_m0F7BD9189DB7D30D44882FF3F53EF2FFBD05C7DE_AdjustorThunk },
	{ 0x060001C0, Cinfo__ctor_mB5890536579096BE14554ED96E868003C9A2CC5C_AdjustorThunk },
	{ 0x060001C1, Cinfo_Equals_mEEFFAFF7E0FE0F0445AF0F96F66D5D68DF1BC3E1_AdjustorThunk },
	{ 0x060001C2, Cinfo_Equals_mBA9B7A52398AB07042615E27D6AC30542F9EF124_AdjustorThunk },
	{ 0x060001C5, Cinfo_GetHashCode_m26AA585D94FCF87B154728D81E40295716B37B03_AdjustorThunk },
	{ 0x060001C6, Cinfo_ToString_m47CB9E0B83E1E5C5CA1517D982A5E6FF4A9F05A3_AdjustorThunk },
	{ 0x060001CA, XREnvironmentProbe__ctor_m756BDCC73762A50BDAF24FD4F430D8F8EA18869D_AdjustorThunk },
	{ 0x060001CB, XREnvironmentProbe_get_trackableId_m7B20AFD8D153397E7270F72C81B32043DA83C57F_AdjustorThunk },
	{ 0x060001CC, XREnvironmentProbe_set_trackableId_mCAD11E54A600B26FDC6D546A15F5E13030605EE4_AdjustorThunk },
	{ 0x060001CD, XREnvironmentProbe_get_scale_m7C53FA5C36BD5616CCF2EDC543C260FD381BCB64_AdjustorThunk },
	{ 0x060001CE, XREnvironmentProbe_set_scale_m230AA2EF5AE396A8E5A43FE809BEEF811CE68302_AdjustorThunk },
	{ 0x060001CF, XREnvironmentProbe_get_pose_m56C2FCB790DC220FAE0339EFC82055360984CAF0_AdjustorThunk },
	{ 0x060001D0, XREnvironmentProbe_set_pose_m1A1776C7D4A99F29708883F081A54936BC46A4B0_AdjustorThunk },
	{ 0x060001D1, XREnvironmentProbe_get_size_m92A310E405DC33AFF0968D0B7C17BDB8D039A1B0_AdjustorThunk },
	{ 0x060001D2, XREnvironmentProbe_set_size_m191D7253226516A2BBC83D0DD28A154FAD2F3C33_AdjustorThunk },
	{ 0x060001D3, XREnvironmentProbe_get_textureDescriptor_mD514443491B53FCBC49AD477CC5C7C6084543FAD_AdjustorThunk },
	{ 0x060001D4, XREnvironmentProbe_set_textureDescriptor_mAE07E8E52FD3D564E1366EC75E0B1EB80A1A43B0_AdjustorThunk },
	{ 0x060001D5, XREnvironmentProbe_get_trackingState_m4051D90D37D33EC33534368B64E5C85EA1888C83_AdjustorThunk },
	{ 0x060001D6, XREnvironmentProbe_set_trackingState_m8AC9F0BB01B0B26935D09B5A723680E658A3A196_AdjustorThunk },
	{ 0x060001D7, XREnvironmentProbe_get_nativePtr_m0C6C620B2D3C20FBE8AEE478EBEA0006E8E7FB40_AdjustorThunk },
	{ 0x060001D8, XREnvironmentProbe_set_nativePtr_m75CED6350AB93167C23B4E1A837879BCEB6A7AAC_AdjustorThunk },
	{ 0x060001D9, XREnvironmentProbe_Equals_m891BD688A67E6AF40E4DE164936AFC6D59762AF0_AdjustorThunk },
	{ 0x060001DA, XREnvironmentProbe_Equals_m76FC4B88F469A7E33C17E4F9A59DBEBDF1A66745_AdjustorThunk },
	{ 0x060001DD, XREnvironmentProbe_GetHashCode_mC8C8046B5523D71CADA65C3D38232925243CEA86_AdjustorThunk },
	{ 0x060001DE, XREnvironmentProbe_ToString_mBD160B7DBD096BB94201C93B1821FF73728C3E4F_AdjustorThunk },
	{ 0x060001DF, XREnvironmentProbe_ToString_mFB69B6A7B36CD0B02B4283AEFF6CEFAA72EE8DB2_AdjustorThunk },
	{ 0x060001F6, XREnvironmentProbeSubsystemCinfo_get_id_m590E5DEBA8C344FFEA51F351F99DEFFD703DC57F_AdjustorThunk },
	{ 0x060001F7, XREnvironmentProbeSubsystemCinfo_set_id_m0157B51BEBA22C10D835283A2E342811FB3904E0_AdjustorThunk },
	{ 0x060001F8, XREnvironmentProbeSubsystemCinfo_get_providerType_mBC6F352EF89BED518336375D44A5D9CADDE464C1_AdjustorThunk },
	{ 0x060001F9, XREnvironmentProbeSubsystemCinfo_set_providerType_m053C0551B3059F11B606708ED8C6B3DA9C4CF2F2_AdjustorThunk },
	{ 0x060001FA, XREnvironmentProbeSubsystemCinfo_get_subsystemTypeOverride_m5A1A5D3ADB4E4304029B3F1688CEBFAB4E4C8F7E_AdjustorThunk },
	{ 0x060001FB, XREnvironmentProbeSubsystemCinfo_set_subsystemTypeOverride_m89507B15871CD2E8F093EF084176BC98CAD3D1E4_AdjustorThunk },
	{ 0x060001FC, XREnvironmentProbeSubsystemCinfo_get_implementationType_m6C1B50EAB87465FE798443F1C2803F1A437EEC3E_AdjustorThunk },
	{ 0x060001FD, XREnvironmentProbeSubsystemCinfo_set_implementationType_m09326FDF5E9024E0AA37F89388F8BDD4AB40E600_AdjustorThunk },
	{ 0x060001FE, XREnvironmentProbeSubsystemCinfo_get_supportsManualPlacement_mFA2560E13D32A8228105D6F205E109DEDE0BFB37_AdjustorThunk },
	{ 0x060001FF, XREnvironmentProbeSubsystemCinfo_set_supportsManualPlacement_mE948D3D31063A4015F18BABCD4475E2CEDD00E2C_AdjustorThunk },
	{ 0x06000200, XREnvironmentProbeSubsystemCinfo_get_supportsRemovalOfManual_mE1E3D58FB734841B3B1E885592C84A2748953847_AdjustorThunk },
	{ 0x06000201, XREnvironmentProbeSubsystemCinfo_set_supportsRemovalOfManual_m9784D47E8903EC1BEFA722994586FF633BB09327_AdjustorThunk },
	{ 0x06000202, XREnvironmentProbeSubsystemCinfo_get_supportsAutomaticPlacement_m01E383AF1BDC43E0755B0C99BF838698B2FAD67C_AdjustorThunk },
	{ 0x06000203, XREnvironmentProbeSubsystemCinfo_set_supportsAutomaticPlacement_mD728F648E5A75DC074D9718432F07D0D460A289D_AdjustorThunk },
	{ 0x06000204, XREnvironmentProbeSubsystemCinfo_get_supportsRemovalOfAutomatic_m5C05042091803872B2B5FE45C791A728F92B5A1A_AdjustorThunk },
	{ 0x06000205, XREnvironmentProbeSubsystemCinfo_set_supportsRemovalOfAutomatic_mBD5F44750877ADEFD8C6207F0B30D0FE15B70526_AdjustorThunk },
	{ 0x06000206, XREnvironmentProbeSubsystemCinfo_get_supportsEnvironmentTexture_m7B872B5FE1B9517D9626E269E09B7C4C16E06A06_AdjustorThunk },
	{ 0x06000207, XREnvironmentProbeSubsystemCinfo_set_supportsEnvironmentTexture_m724298B12B4DDEFC4C1274A551E04299A040374D_AdjustorThunk },
	{ 0x06000208, XREnvironmentProbeSubsystemCinfo_get_supportsEnvironmentTextureHDR_m7AC20590F176681AD486ACC8C9A0189209B1C35E_AdjustorThunk },
	{ 0x06000209, XREnvironmentProbeSubsystemCinfo_set_supportsEnvironmentTextureHDR_mEED10741684E8045104833D1A58A1A02612FBE4B_AdjustorThunk },
	{ 0x0600020A, XREnvironmentProbeSubsystemCinfo_Equals_m4297DA57D68A4D83010A2D370092CB925B89CEA0_AdjustorThunk },
	{ 0x0600020B, XREnvironmentProbeSubsystemCinfo_Equals_m8AE52E39A349B7A5E21C624AE22C4A1078E8C327_AdjustorThunk },
	{ 0x0600020E, XREnvironmentProbeSubsystemCinfo_GetHashCode_m66A5517BE16EE35C50CBD336B9B2928760E07121_AdjustorThunk },
	{ 0x0600021E, XRFace_get_trackableId_m9FC29FB643FFBAB989AB8179F57CDB52D14737B3_AdjustorThunk },
	{ 0x0600021F, XRFace_get_pose_m1625DED173751F25873C4BB6650238A195CD04EE_AdjustorThunk },
	{ 0x06000220, XRFace_get_trackingState_m7D5C3002DCB9FC01F7C1BE66D3F1092281E847FB_AdjustorThunk },
	{ 0x06000221, XRFace_get_nativePtr_mCE3681420B25EA0AE4B5FA1687310DF7D49C0899_AdjustorThunk },
	{ 0x06000222, XRFace_get_leftEyePose_m5406913BE94DA663C80EA8C555EEC1439C0ADAE3_AdjustorThunk },
	{ 0x06000223, XRFace_get_rightEyePose_m276AD0EBDCD8B62AAAAA2A33920E2FF1415E769D_AdjustorThunk },
	{ 0x06000224, XRFace_get_fixationPoint_m2628733EA6C1FEEAC047347DBA08A602B7C88429_AdjustorThunk },
	{ 0x06000225, XRFace_Equals_m6E2D8C6F4F57BB604AA31EEEAEB06BB64EBFC299_AdjustorThunk },
	{ 0x06000226, XRFace_GetHashCode_mC17A1126F3ADDDB95C12C3E908353704DCCB14D0_AdjustorThunk },
	{ 0x06000229, XRFace_Equals_mC82B627F3AA8A164D6AE1A999A5BCB55DD2E2C51_AdjustorThunk },
	{ 0x0600022B, XRFaceMesh_Resize_mD9373FB138642F70F4068345A64B49B64B1A3830_AdjustorThunk },
	{ 0x0600022C, XRFaceMesh_get_vertices_m8B133063FC373FD34B8ECBEE696B3462DC65277E_AdjustorThunk },
	{ 0x0600022D, XRFaceMesh_get_normals_m37A411662D1051785AFC6807E3BBEC0E2B3BB61B_AdjustorThunk },
	{ 0x0600022E, XRFaceMesh_get_indices_m2658965B1B99DF1CF00154D791B580AE71CB136D_AdjustorThunk },
	{ 0x0600022F, XRFaceMesh_get_uvs_m71BF16345717D8B5D8F41C571A8D3152337E0A28_AdjustorThunk },
	{ 0x06000230, XRFaceMesh_Dispose_m02478E536865BA52126039CCAE5B62E5DE58AECF_AdjustorThunk },
	{ 0x06000231, XRFaceMesh_GetHashCode_mE6F88C5914358332601C00E22FE0A34A137EC982_AdjustorThunk },
	{ 0x06000232, XRFaceMesh_Equals_mDE9CF3DB2761831C9E9A72B6C2C3EB1D6D155D6F_AdjustorThunk },
	{ 0x06000233, XRFaceMesh_ToString_mEA1FF45022C6E287675E27526448295468B2884B_AdjustorThunk },
	{ 0x06000234, XRFaceMesh_Equals_m56870D4CC9E4BC2D1839D5DEFA77A062C29C97A4_AdjustorThunk },
	{ 0x06000246, FaceSubsystemParams_get_id_m31A6CBE37287374259BEF3B328FB00B1ED871D6A_AdjustorThunk },
	{ 0x06000247, FaceSubsystemParams_set_id_m963078C97310E91B43D52A4A682D6A4DC0D9A40A_AdjustorThunk },
	{ 0x06000248, FaceSubsystemParams_get_providerType_mAC8A9B7FF24C751EF95C8B06D71746709FBAED0F_AdjustorThunk },
	{ 0x06000249, FaceSubsystemParams_set_providerType_m96D22AB9770398D6C371502A95F86A6F45E17C5D_AdjustorThunk },
	{ 0x0600024A, FaceSubsystemParams_get_subsystemTypeOverride_m15AFA4BED668BB995597E91557337E9B30994467_AdjustorThunk },
	{ 0x0600024B, FaceSubsystemParams_set_subsystemTypeOverride_m113E1A7A0F27FE1A56FF92504E6BDC2FD287846E_AdjustorThunk },
	{ 0x0600024C, FaceSubsystemParams_get_subsystemImplementationType_mB85CD541326A5FBDE4378093FA3735066DBDEFED_AdjustorThunk },
	{ 0x0600024D, FaceSubsystemParams_set_subsystemImplementationType_m3A131EEBF975AA6ADF3A39ABC3ED0980C8D7E024_AdjustorThunk },
	{ 0x0600024E, FaceSubsystemParams_get_supportsFacePose_m68CEF716DBA9D0D75EDA10A87A64A5827C78CA59_AdjustorThunk },
	{ 0x0600024F, FaceSubsystemParams_set_supportsFacePose_mCBD1B88CB04F00F212C4B5B8A11821DDCC5C8353_AdjustorThunk },
	{ 0x06000250, FaceSubsystemParams_get_supportsFaceMeshVerticesAndIndices_mB1D5078180B153FE76FCA509905D0A79D575689B_AdjustorThunk },
	{ 0x06000251, FaceSubsystemParams_set_supportsFaceMeshVerticesAndIndices_m8783686211AB86E509EE6538E3CFC9A6CF82BF9D_AdjustorThunk },
	{ 0x06000252, FaceSubsystemParams_get_supportsFaceMeshUVs_m3734F66B4ADBD0A39FBCAEB927A4409023D6164A_AdjustorThunk },
	{ 0x06000253, FaceSubsystemParams_set_supportsFaceMeshUVs_m151C1B6BE0BA832416557E90493F04DCF97E6809_AdjustorThunk },
	{ 0x06000254, FaceSubsystemParams_get_supportsFaceMeshNormals_m194410916CDF0F71EBF2AD9C419155D68F9969B0_AdjustorThunk },
	{ 0x06000255, FaceSubsystemParams_set_supportsFaceMeshNormals_m0664A1AD48A3B41681B9680F7773D1849FA1450A_AdjustorThunk },
	{ 0x06000256, FaceSubsystemParams_get_supportsEyeTracking_mC254E7AD7AD126742CC7CFDFDB944CD2AC0C0FBD_AdjustorThunk },
	{ 0x06000257, FaceSubsystemParams_set_supportsEyeTracking_mFEB7DACFE5B6DA3EB37AED2C3428A1CEE0E0DBDE_AdjustorThunk },
	{ 0x06000258, FaceSubsystemParams_Equals_mD22C227C324E8205B6ACA8F6C625C62F58224D3A_AdjustorThunk },
	{ 0x06000259, FaceSubsystemParams_Equals_m4AA3EAA9779EDA380AD89EA9BA7E1865B0BE6017_AdjustorThunk },
	{ 0x0600025A, FaceSubsystemParams_GetHashCode_m4C9D85CB7B820FC7E57A5B214D85EDDBD6D5499D_AdjustorThunk },
	{ 0x06000272, XRHumanBody_get_trackableId_m7CC5B8BB5179303ED1424ACDC46FBFA16C30B2FD_AdjustorThunk },
	{ 0x06000273, XRHumanBody_set_trackableId_mCE04EA8307BC1B6670AE915575E5297103620E87_AdjustorThunk },
	{ 0x06000274, XRHumanBody_get_pose_mE154F73E48997BDB6828FE12D7116E93E4D4BBCF_AdjustorThunk },
	{ 0x06000275, XRHumanBody_set_pose_m036F9C1AB8DA4836D85CF15256C0FF6C83E8B712_AdjustorThunk },
	{ 0x06000276, XRHumanBody_get_estimatedHeightScaleFactor_m455E9FD1B289BA71C5FEE2A67D72EEE10727246B_AdjustorThunk },
	{ 0x06000277, XRHumanBody_set_estimatedHeightScaleFactor_m857381C931D5F597AE28A4BD96E8225DE2250693_AdjustorThunk },
	{ 0x06000278, XRHumanBody_get_trackingState_mE53C1B287B5BD8E021FCAC0E4550C0D551C0F79A_AdjustorThunk },
	{ 0x06000279, XRHumanBody_set_trackingState_m470B4AD877F377DB7B85F736D80EC5FB2AD39187_AdjustorThunk },
	{ 0x0600027A, XRHumanBody_get_nativePtr_mBD7FABEADEC1EA20A472626430176AC6681C50E2_AdjustorThunk },
	{ 0x0600027B, XRHumanBody_set_nativePtr_m140ED78793BB10C9126C5539804291CE69F00381_AdjustorThunk },
	{ 0x0600027D, XRHumanBody_Equals_mED06668B3B016A173D38A33D8D4CC24691A90CF1_AdjustorThunk },
	{ 0x0600027E, XRHumanBody_Equals_mC6FA42C2E907195A60B2CB8A6230462762C6B003_AdjustorThunk },
	{ 0x06000281, XRHumanBody_GetHashCode_m44E8812541CCF52BB596A789A350C77CF32B8B06_AdjustorThunk },
	{ 0x06000283, XRHumanBodyJoint_get_index_m3AD361AAD68A37A0EC5490A716FA0F0D5AC6D386_AdjustorThunk },
	{ 0x06000284, XRHumanBodyJoint_get_parentIndex_m4DA1B768A618B7AE553D67CC82F6B2545B8F2FBA_AdjustorThunk },
	{ 0x06000285, XRHumanBodyJoint_get_localScale_m9A7DDC16FAEB5CFF269393403A92C375CE8387B6_AdjustorThunk },
	{ 0x06000286, XRHumanBodyJoint_get_localPose_m5330B565E89F7276A497ED8E94DAA288A352FDD2_AdjustorThunk },
	{ 0x06000287, XRHumanBodyJoint_get_anchorScale_m01EC3D9B0020D0BFBCDF9ADD26149F6D9E6D87C0_AdjustorThunk },
	{ 0x06000288, XRHumanBodyJoint_get_anchorPose_mC409FE9C6F4CFD14C156977B59096FA4340EE61E_AdjustorThunk },
	{ 0x06000289, XRHumanBodyJoint_get_tracked_mC8DA59028CFA50982FD6E319736F0C93EA097899_AdjustorThunk },
	{ 0x0600028A, XRHumanBodyJoint__ctor_mF74F3B39077D10EED35A34F3DBF7C217CA1D8753_AdjustorThunk },
	{ 0x0600028B, XRHumanBodyJoint_Equals_m7DFBAA24024C04E8A38A962862BA744F9A515AE5_AdjustorThunk },
	{ 0x0600028C, XRHumanBodyJoint_Equals_m59EDC2A704F17057288266550340CCB7FE041680_AdjustorThunk },
	{ 0x0600028F, XRHumanBodyJoint_GetHashCode_mC37463DF2B57FF4BA22AD008F91AF061E30575EF_AdjustorThunk },
	{ 0x06000290, XRHumanBodyJoint_ToString_mE909C8943965A053938EFE3B7DC365673632F899_AdjustorThunk },
	{ 0x06000291, XRHumanBodyJoint_ToString_m814AEF251F6D72B22EE7DE358A422C638FF6D089_AdjustorThunk },
	{ 0x06000292, XRHumanBodyPose2DJoint_get_index_m63E7D2C639973B20B8721BD412441AB87F32C626_AdjustorThunk },
	{ 0x06000293, XRHumanBodyPose2DJoint_get_parentIndex_m74403971AC59748A0FC11187E701F8EF835A97F5_AdjustorThunk },
	{ 0x06000294, XRHumanBodyPose2DJoint_get_position_m4A5CE8370D7E1DEB0B2CC27487182A0776AAC8E8_AdjustorThunk },
	{ 0x06000295, XRHumanBodyPose2DJoint_get_tracked_mCC6E1D56159DA4501534E47AB27D4EA05AA3FCF8_AdjustorThunk },
	{ 0x06000296, XRHumanBodyPose2DJoint__ctor_m2BE9BC97DB9FA4C84623810D624196524B9F488A_AdjustorThunk },
	{ 0x06000297, XRHumanBodyPose2DJoint_Equals_m7023E676891F764891104A57CD41D77BE31360F4_AdjustorThunk },
	{ 0x06000298, XRHumanBodyPose2DJoint_Equals_mE8F361B51A58F789BC559B550AA5CA08691A88E1_AdjustorThunk },
	{ 0x0600029B, XRHumanBodyPose2DJoint_GetHashCode_m9CA16CABF11BB7137978E5D8EE83FACBF7D1622F_AdjustorThunk },
	{ 0x0600029C, XRHumanBodyPose2DJoint_ToString_m487FA2ED54B8FC493572D75FAFC68BA40B4FEFC4_AdjustorThunk },
	{ 0x0600029D, XRHumanBodyPose2DJoint_ToString_m7BBC96E86E94C35680E22AB7CED6215407E48CBB_AdjustorThunk },
	{ 0x060002B9, XRHumanBodySubsystemCinfo_get_id_m2B4E8095B5AE6AF6E64FB7B56DBD795989A862F8_AdjustorThunk },
	{ 0x060002BA, XRHumanBodySubsystemCinfo_set_id_m58CBD535224B7049AC8AC9D82305E1D8BBF90084_AdjustorThunk },
	{ 0x060002BB, XRHumanBodySubsystemCinfo_get_providerType_m71F869C8A2B8C6AC2BB7D0C3842549886608BD19_AdjustorThunk },
	{ 0x060002BC, XRHumanBodySubsystemCinfo_set_providerType_m228A78E8403BF03833D556B3EE0637291DC46615_AdjustorThunk },
	{ 0x060002BD, XRHumanBodySubsystemCinfo_get_subsystemTypeOverride_m1AD3DA828683BFEC871F5E62C5B552E27F47A009_AdjustorThunk },
	{ 0x060002BE, XRHumanBodySubsystemCinfo_set_subsystemTypeOverride_m847719A54A861DE6FE06E9DAFA1485E6184D2909_AdjustorThunk },
	{ 0x060002BF, XRHumanBodySubsystemCinfo_get_implementationType_m3CD85264DFA467891430738097EBB79174BA518C_AdjustorThunk },
	{ 0x060002C0, XRHumanBodySubsystemCinfo_set_implementationType_m8001903267263EED38F960C2E1BD5A53CE64C798_AdjustorThunk },
	{ 0x060002C1, XRHumanBodySubsystemCinfo_get_supportsHumanBody2D_m93255214CEECB1C656D88ACFFF73D736D81FD8BD_AdjustorThunk },
	{ 0x060002C2, XRHumanBodySubsystemCinfo_set_supportsHumanBody2D_m7E4750E667A695B158E006DDC6F74C56542CBE0E_AdjustorThunk },
	{ 0x060002C3, XRHumanBodySubsystemCinfo_get_supportsHumanBody3D_m133406910A68DE814A0A335A59285098FEE70C19_AdjustorThunk },
	{ 0x060002C4, XRHumanBodySubsystemCinfo_set_supportsHumanBody3D_m02D0D700619D9CB7D96D027EFE3EC41D47F81F8A_AdjustorThunk },
	{ 0x060002C5, XRHumanBodySubsystemCinfo_get_supportsHumanBody3DScaleEstimation_m81A73CFB3B8232EF098B2CDF96F25687AC19845C_AdjustorThunk },
	{ 0x060002C6, XRHumanBodySubsystemCinfo_set_supportsHumanBody3DScaleEstimation_m5F982C883E1918068DA02BA87C0069BF379E147F_AdjustorThunk },
	{ 0x060002C7, XRHumanBodySubsystemCinfo_Equals_m99F61D9E0CFA86A7171F27A236AD8CD2FD7BF08A_AdjustorThunk },
	{ 0x060002C8, XRHumanBodySubsystemCinfo_Equals_mE22CDDCA260523EF651BD2B23C1F66F11BB95C0A_AdjustorThunk },
	{ 0x060002CB, XRHumanBodySubsystemCinfo_GetHashCode_mF9F242A2F525ED4246171238CE9B5A7FD24C63FE_AdjustorThunk },
	{ 0x060002D4, AddReferenceImageJobState__ctor_mC0CCEC53FEB86CE2B9560D06DE28919ADB2440E2_AdjustorThunk },
	{ 0x060002D5, AddReferenceImageJobState_get_jobHandle_m02E9565D08C8156E799D1B852C14707856E6B12E_AdjustorThunk },
	{ 0x060002D6, AddReferenceImageJobState_AsIntPtr_m8C97E68E09387D512B5A2D921841B3E0FCF44CC0_AdjustorThunk },
	{ 0x060002D8, AddReferenceImageJobState_get_status_mDF8FE0C1BC9407AD9EAA821DE78B76599455A25F_AdjustorThunk },
	{ 0x060002D9, AddReferenceImageJobState_ToString_m89383245617B4E89FF1CA2FF897917062CD663A7_AdjustorThunk },
	{ 0x060002DA, AddReferenceImageJobState_GetHashCode_m6EABAC53399090ADFD2932E561BA0FA12EA63DC0_AdjustorThunk },
	{ 0x060002DB, AddReferenceImageJobState_Equals_mCFA105DAC305C1B3B34F0C7D0D856F3671356D37_AdjustorThunk },
	{ 0x060002DC, AddReferenceImageJobState_Equals_mD0EE6BB78CB7601C9E1AC6C297417B6E4AE70502_AdjustorThunk },
	{ 0x060002F4, Enumerator__ctor_m25C351F3CA22AFB104CE79D00CFF851C7E247ECE_AdjustorThunk },
	{ 0x060002F5, Enumerator_MoveNext_mF3DBBFA17313E104979A8A4F7CD5F111C352AF67_AdjustorThunk },
	{ 0x060002F6, Enumerator_get_Current_mCAFA85FE5DFA6D3AF14AE3E0BD39A478B00D5F03_AdjustorThunk },
	{ 0x060002F7, Enumerator_Dispose_m4CBA500DF0A5E65FA1BECA0D61521C960884E06B_AdjustorThunk },
	{ 0x060002F8, Enumerator_GetHashCode_m687007D953BFC4902A3A0115F0E55CA3EBEB1DFE_AdjustorThunk },
	{ 0x060002F9, Enumerator_Equals_m6831767F67C100E80B4C5BCDC1980E8453298DF9_AdjustorThunk },
	{ 0x060002FA, Enumerator_Equals_m9D822B4C42050C4A121A8CDC1174F2A5824015BB_AdjustorThunk },
	{ 0x06000318, Cinfo_get_id_m575543324B2C5A08D4C786C63370AE71BED07969_AdjustorThunk },
	{ 0x06000319, Cinfo_set_id_m26F70E551A2F6B517FB5F5C3E5EE4C129FA7BF42_AdjustorThunk },
	{ 0x0600031A, Cinfo_get_providerType_mBE30E1C47EDAB073ED2660418CA3E29DA5F0CC9B_AdjustorThunk },
	{ 0x0600031B, Cinfo_set_providerType_m4EEFA53693D860609EE4E84FBBD7411BCF38C7D8_AdjustorThunk },
	{ 0x0600031C, Cinfo_get_subsystemTypeOverride_m1ECE580A62D07D72403739C3B0A3402066CA2939_AdjustorThunk },
	{ 0x0600031D, Cinfo_set_subsystemTypeOverride_m92548450D405AAF6961BFAD7DFBA20AC0F258A16_AdjustorThunk },
	{ 0x0600031E, Cinfo_get_subsystemImplementationType_m6D5DFEBCB48B3C0A9574AF76063A4E4C0BEE4D9C_AdjustorThunk },
	{ 0x0600031F, Cinfo_set_subsystemImplementationType_m38DB94BDE396C4375A2384D92893106166598BD3_AdjustorThunk },
	{ 0x06000320, Cinfo_get_supportsMovingImages_mF03413F05E6DA7C176CC49907024D17F3CA8CDD4_AdjustorThunk },
	{ 0x06000321, Cinfo_set_supportsMovingImages_m8A9EC55903324606F3A00A1B9E0BA4A7F9FBA636_AdjustorThunk },
	{ 0x06000322, Cinfo_get_requiresPhysicalImageDimensions_m75BC1904E82964AAB2D4878CDC01A5DA91BCEF8B_AdjustorThunk },
	{ 0x06000323, Cinfo_set_requiresPhysicalImageDimensions_mEDA76B05F4AADE1843195F0C011BFAD5A10179CD_AdjustorThunk },
	{ 0x06000324, Cinfo_get_supportsMutableLibrary_mEED5E973B93B77E2EB889635CF0BFBED0F3AAD65_AdjustorThunk },
	{ 0x06000325, Cinfo_set_supportsMutableLibrary_mE18B618EF5F3EC11E1AB460302D88A92091DE9E1_AdjustorThunk },
	{ 0x06000326, Cinfo_get_supportsImageValidation_m4A509134A5D26AAEFA77EC28D48802E48D98AF35_AdjustorThunk },
	{ 0x06000327, Cinfo_set_supportsImageValidation_mCD49CB40A3C9FDC8340016FC0C6D4CA3AC71A98B_AdjustorThunk },
	{ 0x06000328, Cinfo_GetHashCode_m17D577D55135627B8C8B252E8694CE37D2DCDC8A_AdjustorThunk },
	{ 0x06000329, Cinfo_Equals_m1B1870077B045D565BC51101AB7C0F66734249FC_AdjustorThunk },
	{ 0x0600032A, Cinfo_Equals_m6FB5DA5B648EE40F20736E915C5E64C13A4D312D_AdjustorThunk },
	{ 0x0600032D, XRReferenceImage__ctor_mCD536BB9053D7775175E0A8AE51BBF026AB06765_AdjustorThunk },
	{ 0x0600032E, XRReferenceImage_get_guid_m6BEA9888191B7528B60F98EE03C9DBB2B9B8ADEE_AdjustorThunk },
	{ 0x0600032F, XRReferenceImage_get_textureGuid_m70BB73989E26562E2B37F8C272F14F2D06659615_AdjustorThunk },
	{ 0x06000330, XRReferenceImage_get_specifySize_m571D71A02EF695A72121AAA086F8B52323E4E4A5_AdjustorThunk },
	{ 0x06000331, XRReferenceImage_get_size_mF44BF21ADEFBB155BFD8043E1067057219EC59F2_AdjustorThunk },
	{ 0x06000332, XRReferenceImage_get_width_mB6465C498B58CD9093F2FF5EA55DAC8F0E7580A9_AdjustorThunk },
	{ 0x06000333, XRReferenceImage_get_height_m422FF3F85DE70E492B7FBABC02277DF6BD76DCD8_AdjustorThunk },
	{ 0x06000334, XRReferenceImage_get_name_mF1BE1E54AD911D48445B7DDEF2E27EA01E1E73BB_AdjustorThunk },
	{ 0x06000335, XRReferenceImage_get_texture_mEC132411644C747C782F41A32A97C95B306D0891_AdjustorThunk },
	{ 0x06000336, XRReferenceImage_ToString_mA4374950A18DB316C790DD07F2485A385CE7F3D3_AdjustorThunk },
	{ 0x06000337, XRReferenceImage_GetHashCode_m4A2F5EA86EF5B9CDF39516FABD5E378D779B1BA0_AdjustorThunk },
	{ 0x06000338, XRReferenceImage_Equals_m1FACD89998C2C9ED6E65DDEE6C1466AE7CC4537E_AdjustorThunk },
	{ 0x06000339, XRReferenceImage_Equals_m6EA6760F9A443A324475B1E442AFA83C84F06D08_AdjustorThunk },
	{ 0x06000347, XRTrackedImage__ctor_mC69E3D11AAD1CF838BC8575A0E476C296B4B1F3F_AdjustorThunk },
	{ 0x06000349, XRTrackedImage_get_trackableId_m9EA6E15BEF6777E27B50A4903E0069AC04ED6405_AdjustorThunk },
	{ 0x0600034A, XRTrackedImage_get_sourceImageId_mAAAA675839747EA6AF8A903E461F0D198CFAFDBF_AdjustorThunk },
	{ 0x0600034B, XRTrackedImage_get_pose_m24132085AC8CCE5762C01ECCC1C264A36E77FD69_AdjustorThunk },
	{ 0x0600034C, XRTrackedImage_get_size_m57847CD4307A9A560D358981700B8722D8A02438_AdjustorThunk },
	{ 0x0600034D, XRTrackedImage_get_trackingState_m059B99A670B142384AE772376780095877CA72F9_AdjustorThunk },
	{ 0x0600034E, XRTrackedImage_get_nativePtr_m2ECEAC93477008FB415D4A388ACAA4A9DB6E1892_AdjustorThunk },
	{ 0x0600034F, XRTrackedImage_GetHashCode_m3900E50D96F2687C63C8F78C9BEA6E469FAE5E2C_AdjustorThunk },
	{ 0x06000350, XRTrackedImage_Equals_m95C7E1338C9CD5F37EE9D6452AE5820D2BC87FB7_AdjustorThunk },
	{ 0x06000351, XRTrackedImage_Equals_m54F9B4F5CC42200E927B5025274C6E765264C9BF_AdjustorThunk },
	{ 0x0600036E, Capabilities_Equals_m60B932F4020B4C1D938F76F8B143AAD76901C48C_AdjustorThunk },
	{ 0x0600036F, Capabilities_Equals_mC78F86790EF9479F76FC84B4E7F74E2E2C07D249_AdjustorThunk },
	{ 0x06000370, Capabilities_GetHashCode_mE4E2BB398DA4790DD8E2D0FCA8477062537CF432_AdjustorThunk },
	{ 0x06000373, XRReferenceObject__ctor_mBAE1DECE98351EEFC3B058C143FECED732A58B71_AdjustorThunk },
	{ 0x06000374, XRReferenceObject_AddEntry_m83F55C34FB2B5294EFA94E936676E3A3401E07BA_AdjustorThunk },
	{ 0x06000375, XRReferenceObject_get_name_m30CA572092D7E0DD1D7028A84BB0F5999A92D8FD_AdjustorThunk },
	{ 0x06000376, XRReferenceObject_get_guid_m96423410888B4CB9712D1A064CF874B5191A49D1_AdjustorThunk },
	{ 0x06000378, XRReferenceObject_FindEntry_mFF9C29CCCDF7BA08B307B787DF83EF8F27B6017C_AdjustorThunk },
	{ 0x06000379, XRReferenceObject_OnAddToLibrary_mF06627EC55B52C25A701E1E00D4F941C70432E42_AdjustorThunk },
	{ 0x0600037A, XRReferenceObject_Equals_m464CFD79821FAA617E311B82ADA20968A248F511_AdjustorThunk },
	{ 0x0600037B, XRReferenceObject_GetHashCode_m8ACBD77E6AAF860411D1A1135C4838667F1FB77D_AdjustorThunk },
	{ 0x0600037C, XRReferenceObject_Equals_m3606543B9EF155334F0E71FFE4A550EF5DA0957C_AdjustorThunk },
	{ 0x0600038C, XRTrackedObject_get_trackableId_mE8CA173C4D77E4910C47CD5A3DBEA7570CCA69F8_AdjustorThunk },
	{ 0x0600038D, XRTrackedObject_get_pose_mC8BBDFCC19D9FAF22FA0484E58C5BB1114C929C7_AdjustorThunk },
	{ 0x0600038E, XRTrackedObject_get_trackingState_m7D1D9DD436ECB10D02D1413AB660ACBC23D60E89_AdjustorThunk },
	{ 0x0600038F, XRTrackedObject_get_nativePtr_m804A89CA593F513109FCBC04A4FB2C16505F388C_AdjustorThunk },
	{ 0x06000390, XRTrackedObject_get_referenceObjectGuid_m5E5FEE2B90403C2F75252A6DF4BD9436A7927FD2_AdjustorThunk },
	{ 0x06000391, XRTrackedObject__ctor_m9916083096F5B1A8034C7450D07AA9192CDE7BDC_AdjustorThunk },
	{ 0x06000392, XRTrackedObject_Equals_m4D9D7A47BBD40F713CA964F4109DFD1AFB225D7A_AdjustorThunk },
	{ 0x06000393, XRTrackedObject_GetHashCode_m9E4ECD0C61FC65EF59F51AD6930540BDF65D2CE3_AdjustorThunk },
	{ 0x06000396, XRTrackedObject_Equals_mEA3B08014486E3DF12E17FDCA8D1FAE067FF8BEA_AdjustorThunk },
	{ 0x060003D8, XROcclusionSubsystemCinfo_get_id_mF11E38C57E4AB8E81F9E7875A0A41D04A19C4039_AdjustorThunk },
	{ 0x060003D9, XROcclusionSubsystemCinfo_set_id_mF8B41D7F5FACF940467D57208BC03DDD89D9B7A8_AdjustorThunk },
	{ 0x060003DA, XROcclusionSubsystemCinfo_get_providerType_m98D7D72FF4C0B36F28D6E39BC498E43691AE718B_AdjustorThunk },
	{ 0x060003DB, XROcclusionSubsystemCinfo_set_providerType_m5D9D3B330216EAB023F4B17F4853D5A612B07380_AdjustorThunk },
	{ 0x060003DC, XROcclusionSubsystemCinfo_get_subsystemTypeOverride_mAE0D5036913033AB1D45B54C10047F180648BD3E_AdjustorThunk },
	{ 0x060003DD, XROcclusionSubsystemCinfo_set_subsystemTypeOverride_mA0E976FA9B7955BD224DF93F7AB9AEA883779563_AdjustorThunk },
	{ 0x060003DE, XROcclusionSubsystemCinfo_get_implementationType_mC1121AB1278E4F86B951FBD0B2EAD85D81A2AB45_AdjustorThunk },
	{ 0x060003DF, XROcclusionSubsystemCinfo_set_implementationType_m0D5D7F0B926679A2195C01F183280029ADC525BB_AdjustorThunk },
	{ 0x060003E0, XROcclusionSubsystemCinfo_get_supportsHumanSegmentationStencilImage_mB151BF0F40B3C9D9D2DE26318FD219FEF0C2AB9D_AdjustorThunk },
	{ 0x060003E1, XROcclusionSubsystemCinfo_set_supportsHumanSegmentationStencilImage_mE4E442080331134DC255F46E5342D25FF48CB666_AdjustorThunk },
	{ 0x060003E2, XROcclusionSubsystemCinfo_get_humanSegmentationStencilImageSupportedDelegate_m77677BDADCDA75FD77F97A942FA6B29706500292_AdjustorThunk },
	{ 0x060003E3, XROcclusionSubsystemCinfo_set_humanSegmentationStencilImageSupportedDelegate_mB0F746AC0CBE2CC986B43CA50873FF91D3D9860F_AdjustorThunk },
	{ 0x060003E4, XROcclusionSubsystemCinfo_get_supportsHumanSegmentationDepthImage_mB73625A00528D80575D712BE5621FF9219E3B6E9_AdjustorThunk },
	{ 0x060003E5, XROcclusionSubsystemCinfo_set_supportsHumanSegmentationDepthImage_m3D99453F58EEFC98739B052C19B05A4FD5341926_AdjustorThunk },
	{ 0x060003E6, XROcclusionSubsystemCinfo_get_humanSegmentationDepthImageSupportedDelegate_m67F62406957D42F2EF689DE57FD0074C3DAF2BD5_AdjustorThunk },
	{ 0x060003E7, XROcclusionSubsystemCinfo_set_humanSegmentationDepthImageSupportedDelegate_m4AB47FCB92617E34DC2C66699B26346112E5C145_AdjustorThunk },
	{ 0x060003E8, XROcclusionSubsystemCinfo_get_environmentDepthTemporalSmoothingSupportedDelegate_mD84F3F4F9DFCFC8C4CE86F9276EEA5ACA2392D1D_AdjustorThunk },
	{ 0x060003E9, XROcclusionSubsystemCinfo_set_environmentDepthTemporalSmoothingSupportedDelegate_m6B67C82AEA73E79B7CD03F9912746C6B52C27949_AdjustorThunk },
	{ 0x060003EA, XROcclusionSubsystemCinfo_get_queryForSupportsEnvironmentDepthImage_mA29F2AB5C4AA073556B074CFD5A88D3345D596B3_AdjustorThunk },
	{ 0x060003EB, XROcclusionSubsystemCinfo_set_queryForSupportsEnvironmentDepthImage_m4BFFF1A117B8452E731F6EC8A4B3CBBFAA0F0B9D_AdjustorThunk },
	{ 0x060003EC, XROcclusionSubsystemCinfo_get_environmentDepthImageSupportedDelegate_m540B191F5215CDD7A1DEC6E370065AE8C9ADE75C_AdjustorThunk },
	{ 0x060003ED, XROcclusionSubsystemCinfo_set_environmentDepthImageSupportedDelegate_m18E4C878D993208EB9EEA9D667CA5C88E4E1D4BE_AdjustorThunk },
	{ 0x060003EE, XROcclusionSubsystemCinfo_get_queryForSupportsEnvironmentDepthConfidenceImage_mA3552C9DF07B609155051A3A80FA30AFDE2D3D1F_AdjustorThunk },
	{ 0x060003EF, XROcclusionSubsystemCinfo_set_queryForSupportsEnvironmentDepthConfidenceImage_m4717AAF7FA480A374DF1357BFDD2D9A256CF5117_AdjustorThunk },
	{ 0x060003F0, XROcclusionSubsystemCinfo_get_environmentDepthConfidenceImageSupportedDelegate_m562A6E788BE3D0CCD49F0DC2DC698CFAB87DDA6C_AdjustorThunk },
	{ 0x060003F1, XROcclusionSubsystemCinfo_set_environmentDepthConfidenceImageSupportedDelegate_m0F91D5C5B63DEFD91FE9A00EF95B45ED9711461A_AdjustorThunk },
	{ 0x060003F2, XROcclusionSubsystemCinfo_Equals_mEFD9C210D7814A35FFE675EBD2EE91E6A3856623_AdjustorThunk },
	{ 0x060003F3, XROcclusionSubsystemCinfo_Equals_m232468ACCEB9CD8E20E096E270EEC1B262013812_AdjustorThunk },
	{ 0x060003F6, XROcclusionSubsystemCinfo_GetHashCode_m672CF4A97241C59DE78E46E2D41245EACC13B09E_AdjustorThunk },
	{ 0x06000402, XRParticipant__ctor_mB90C6BDD46B876A4500C55B8CC4C5397AF98B4B6_AdjustorThunk },
	{ 0x06000404, XRParticipant_get_trackableId_mA33D6F01E0C98B53E73D3AE91E9D27637D3EDCF1_AdjustorThunk },
	{ 0x06000405, XRParticipant_get_pose_m68A36D0B3837325D073CCB92A93F600A2D535B7B_AdjustorThunk },
	{ 0x06000406, XRParticipant_get_trackingState_m65687E606627A087623C9937E49EDE133C5EDD89_AdjustorThunk },
	{ 0x06000407, XRParticipant_get_nativePtr_m55B683C801F0EDB9EBBCE21A31B507469838D09A_AdjustorThunk },
	{ 0x06000408, XRParticipant_get_sessionId_m266D8DE3F178F3EFF1ACBC50578F67E2152006AE_AdjustorThunk },
	{ 0x06000409, XRParticipant_GetHashCode_mBDFD1ECFEF61A85D9A8BF3857AA0B6220B4A25F1_AdjustorThunk },
	{ 0x0600040A, XRParticipant_Equals_mED9407D8D4A118CC1E4B2591A3C3884E3FD74708_AdjustorThunk },
	{ 0x0600040B, XRParticipant_Equals_mA78A4E55D2F314EA48933FB16F3AE557819DCC9E_AdjustorThunk },
	{ 0x06000419, BoundedPlane__ctor_m95C41A6B0DB95A2636683BE716E9F92A0465EF87_AdjustorThunk },
	{ 0x0600041A, BoundedPlane_get_trackableId_m7AA7FD63EA8F8A903300EFDF15616315ACFDA8AA_AdjustorThunk },
	{ 0x0600041B, BoundedPlane_get_subsumedById_m27EFD2CAFFDCF6560D445115D596F23094F612B9_AdjustorThunk },
	{ 0x0600041C, BoundedPlane_get_pose_mE6F416B0C7519EDA0D1AE8D8BD4D627E4CEA96CC_AdjustorThunk },
	{ 0x0600041D, BoundedPlane_get_center_m3BB7635D2137C7C414FC682EBE0CB5E1F8D3F7D3_AdjustorThunk },
	{ 0x0600041E, BoundedPlane_get_extents_m60341CDB176C9039D5B88B2F52534D356E11F400_AdjustorThunk },
	{ 0x0600041F, BoundedPlane_get_size_m2645C0FF517DF15F381B268DF6366F4D14381DC8_AdjustorThunk },
	{ 0x06000420, BoundedPlane_get_alignment_m4E43582A7059AE23DD506878BCF164C61422DBEF_AdjustorThunk },
	{ 0x06000421, BoundedPlane_get_trackingState_mC294F13F8F79D53F8F04D8FB4E160B092BA6A577_AdjustorThunk },
	{ 0x06000422, BoundedPlane_get_nativePtr_mE8E2608856FE4327913A38005F4A8590D65A43E7_AdjustorThunk },
	{ 0x06000423, BoundedPlane_get_classification_m4EA9556C440097648A87D3AB7EC433776468A725_AdjustorThunk },
	{ 0x06000424, BoundedPlane_get_width_m634AD1BAD468FF96CBFC5786A4CB8A9747737E96_AdjustorThunk },
	{ 0x06000425, BoundedPlane_get_height_mB72E46326D1B3DAA5EF67D7FC65D58ECF02FEB5E_AdjustorThunk },
	{ 0x06000426, BoundedPlane_get_normal_m219E5CB840E4DEE8ECC168F2E5BC3FA6AD5E3DCA_AdjustorThunk },
	{ 0x06000427, BoundedPlane_get_plane_mC4E55F965A895DDD4EB960BEB612185DE21FD9AC_AdjustorThunk },
	{ 0x06000428, BoundedPlane_GetCorners_mA9013A95E8FF0830A83791567377903D4D4ED8A8_AdjustorThunk },
	{ 0x06000429, BoundedPlane_ToString_mA4FBDD41FC676DB2C2EEB22DA2E624099EF06ADA_AdjustorThunk },
	{ 0x0600042A, BoundedPlane_Equals_mE1A074D048C20E980CB7016FAFA7EDFDA52DB15D_AdjustorThunk },
	{ 0x0600042B, BoundedPlane_GetHashCode_m8C684989A748253B2A3772BCAA87D8758FB98941_AdjustorThunk },
	{ 0x0600042E, BoundedPlane_Equals_m1F738CE040A5D498E41B35521109A3FFBEB7196D_AdjustorThunk },
	{ 0x06000447, Cinfo_get_id_m3C9491FE6D19662F5CDE221328F86862621B6DE2_AdjustorThunk },
	{ 0x06000448, Cinfo_set_id_mCADBFEB62A645F33A8FE7684CE21D11A837FB6F9_AdjustorThunk },
	{ 0x06000449, Cinfo_get_providerType_m43F95297A90490AFA397FC61B990A33F74259EF5_AdjustorThunk },
	{ 0x0600044A, Cinfo_set_providerType_m75AA9217739457DA075AB764BF440217E87A1126_AdjustorThunk },
	{ 0x0600044B, Cinfo_get_subsystemTypeOverride_m30B55A83F149F3B304AAAD85FE3E80BFCD75552D_AdjustorThunk },
	{ 0x0600044C, Cinfo_set_subsystemTypeOverride_m8880B201EBF541B726CA629EF2DBA762E5ACB010_AdjustorThunk },
	{ 0x0600044D, Cinfo_get_subsystemImplementationType_m87A964B3827007A58DEAF8966B51575BA7687D6D_AdjustorThunk },
	{ 0x0600044E, Cinfo_set_subsystemImplementationType_m63ACA332E759D120AB82AA3FE630512846B04E4E_AdjustorThunk },
	{ 0x0600044F, Cinfo_get_supportsHorizontalPlaneDetection_m25246A60EFD930C7AC4C50A950E910E7716C9315_AdjustorThunk },
	{ 0x06000450, Cinfo_set_supportsHorizontalPlaneDetection_m73E4DE44A091E9B268214E732EFA29174703EEF1_AdjustorThunk },
	{ 0x06000451, Cinfo_get_supportsVerticalPlaneDetection_mEA496CC68069CCAD03DBC1B57F53DDA57D56A8BF_AdjustorThunk },
	{ 0x06000452, Cinfo_set_supportsVerticalPlaneDetection_m989F6D7C88D39981D1DD342DEC887E9DB3E44AF3_AdjustorThunk },
	{ 0x06000453, Cinfo_get_supportsArbitraryPlaneDetection_m7E19D041E3828651646769D594B9647149B5A0F4_AdjustorThunk },
	{ 0x06000454, Cinfo_set_supportsArbitraryPlaneDetection_m423FEFB76FB81C496A35BB358EF04592C8EE9C10_AdjustorThunk },
	{ 0x06000455, Cinfo_get_supportsBoundaryVertices_m75615CA66C3E0020B75915F8426FE6B2B475BEDD_AdjustorThunk },
	{ 0x06000456, Cinfo_set_supportsBoundaryVertices_m96752ABD368822EE7EE393F7AE3AAE631A4C3657_AdjustorThunk },
	{ 0x06000457, Cinfo_get_supportsClassification_m2596CCB90308DA90A90177C91854DDFBF18F464A_AdjustorThunk },
	{ 0x06000458, Cinfo_set_supportsClassification_mB1E8AAC1F2A7D511C960C6606364C87EEA1A221D_AdjustorThunk },
	{ 0x06000459, Cinfo_Equals_m8A992E8675D4C2A5FCF7FCD7714CD1DBD734FEC0_AdjustorThunk },
	{ 0x0600045A, Cinfo_Equals_m2B155451B272C1E8954EDA6D6DFD1C151408D393_AdjustorThunk },
	{ 0x0600045B, Cinfo_GetHashCode_mC8813973E6CB5AB8D267B6D76693B6F96C006BF9_AdjustorThunk },
	{ 0x0600046F, Cinfo_get_providerType_m9A765D29AAB5D3BB7C85A54C8B5BD5ACF0A34834_AdjustorThunk },
	{ 0x06000470, Cinfo_set_providerType_mF68F31F518ED15570B01758D651590F2A79ADA50_AdjustorThunk },
	{ 0x06000471, Cinfo_get_subsystemTypeOverride_m9E130C961C57B8F9DEE5CB775E5BAC1E82FE9ACC_AdjustorThunk },
	{ 0x06000472, Cinfo_set_subsystemTypeOverride_mF477DEE9A66BA9E979AE210507B158E20DB7F897_AdjustorThunk },
	{ 0x06000473, Cinfo_get_supportsFeaturePoints_mDA5E397852EFECCFB9D72BEF50D8149389AF32A0_AdjustorThunk },
	{ 0x06000474, Cinfo_set_supportsFeaturePoints_m9AB5B37930ED13405E788D882B2ED496A8A003A4_AdjustorThunk },
	{ 0x06000475, Cinfo_get_supportsConfidence_mD64AB8201EF5FED46B5961D3E20A501074F442BA_AdjustorThunk },
	{ 0x06000476, Cinfo_set_supportsConfidence_m8455406D7A9D0EA2B0600FA6D952CF4E7B169AA2_AdjustorThunk },
	{ 0x06000477, Cinfo_get_supportsUniqueIds_m0CD91E9193EA5454EBE851570155F301B1B4E499_AdjustorThunk },
	{ 0x06000478, Cinfo_set_supportsUniqueIds_m0C526CF6938A8220903C47BD11AC9F6A1B1E86F6_AdjustorThunk },
	{ 0x06000479, Cinfo_get_capabilities_m75D2555477E50E9EE792D385FB7E178EF121362C_AdjustorThunk },
	{ 0x0600047A, Cinfo_set_capabilities_m057D002DC3F65F4A194653D7724131E7DE20D852_AdjustorThunk },
	{ 0x0600047B, Cinfo_Equals_m45095412211AFF025FFA31098667F7CF82BF2999_AdjustorThunk },
	{ 0x0600047C, Cinfo_Equals_mDA07D4DDCD91815E6748EC1C73EAB4CA9C911921_AdjustorThunk },
	{ 0x0600047D, Cinfo_GetHashCode_mD5232DFA01B322FD842CE7B2440D51800770C3A9_AdjustorThunk },
	{ 0x06000481, XRPointCloud__ctor_m7689BEC3D1FFA90CE83BD510C993768CCA777688_AdjustorThunk },
	{ 0x06000482, XRPointCloud_get_trackableId_m3AFB6026E205E26C8B7A3209696F566FB686144D_AdjustorThunk },
	{ 0x06000483, XRPointCloud_get_pose_m0A8AC4386A388238F4910916CDD3D1B936DB8A51_AdjustorThunk },
	{ 0x06000484, XRPointCloud_get_trackingState_m2CD370D0D6A2A920AC1637D94BDD3BCC5DB8945D_AdjustorThunk },
	{ 0x06000485, XRPointCloud_get_nativePtr_m606D5A1327EAF4D7A23811FE9D90BB8A027D5B11_AdjustorThunk },
	{ 0x06000486, XRPointCloud_GetHashCode_m96702297D45AEC469D7D3106FBE47F36B50F9688_AdjustorThunk },
	{ 0x06000487, XRPointCloud_Equals_m401E9050FB531805238DF8BCBB06491E23A763B0_AdjustorThunk },
	{ 0x06000488, XRPointCloud_Equals_m1A2A56D489C6CFB2DA66238FA843A972E5B2341D_AdjustorThunk },
	{ 0x0600048C, XRPointCloudData_get_positions_m6B1843590E0A5A94DBA711BF1FBA3A64E39A00A5_AdjustorThunk },
	{ 0x0600048D, XRPointCloudData_set_positions_mDE6F539B73AEA3C49189F4210F9D01094A02F14D_AdjustorThunk },
	{ 0x0600048E, XRPointCloudData_get_confidenceValues_m4553186D87BC21D13B0B5AC3542BDFE6CFDA15FA_AdjustorThunk },
	{ 0x0600048F, XRPointCloudData_set_confidenceValues_m2541483932A4753B91B3038EF869340A1B949355_AdjustorThunk },
	{ 0x06000490, XRPointCloudData_get_identifiers_m86966DF55A38D54A4284AE08D1EBE95F95F80203_AdjustorThunk },
	{ 0x06000491, XRPointCloudData_set_identifiers_mA6E3D5E038C89FE4187BE6F57CA92822F0A0CB9B_AdjustorThunk },
	{ 0x06000492, XRPointCloudData_Dispose_m761F04E465F85CB79EC6BDFFBAB3A348CCB02F75_AdjustorThunk },
	{ 0x06000493, XRPointCloudData_GetHashCode_m1787DE4A09656A3F814952590544879379129FB2_AdjustorThunk },
	{ 0x06000494, XRPointCloudData_Equals_mA86AEB4AE413BE16DB87461174F31B9ECE87EDED_AdjustorThunk },
	{ 0x06000495, XRPointCloudData_ToString_mE72394A861C3A87F2DA161BAE348FF5E5A0E8052_AdjustorThunk },
	{ 0x06000496, XRPointCloudData_Equals_mE40F5EEEE84C1953A50F3E08BE1C17D3A965B970_AdjustorThunk },
	{ 0x060004AA, Cinfo_get_providerType_mDDD1F34666705A5BB5B8ED6BD6A76D3449F35323_AdjustorThunk },
	{ 0x060004AB, Cinfo_set_providerType_mC05B07E51AB9C1D9876C0B38F185CE8A8CAB3ACF_AdjustorThunk },
	{ 0x060004AC, Cinfo_get_subsystemTypeOverride_m67FF1505BC8E11A5A8B8AD478D581703ACC11AFB_AdjustorThunk },
	{ 0x060004AD, Cinfo_set_subsystemTypeOverride_m242BF043654209565488CEE2A0ACB93CE23E9C72_AdjustorThunk },
	{ 0x060004AE, Cinfo_get_supportsFeaturePoints_m21644904ECC661B186FACE0FD45BA53FCBE67C29_AdjustorThunk },
	{ 0x060004AF, Cinfo_set_supportsFeaturePoints_m9B0684F80A0FFF829E28C477E0E49AF60C1DDC15_AdjustorThunk },
	{ 0x060004B0, Cinfo_get_supportsConfidence_m24690BFED822D8034981F9ABF2C02361C071798D_AdjustorThunk },
	{ 0x060004B1, Cinfo_set_supportsConfidence_m7EE730D229E3187CE6DC3E9FB2E31FA32D1A29D8_AdjustorThunk },
	{ 0x060004B2, Cinfo_get_supportsUniqueIds_m955E4A28D276A9459A9496C92C242F6BD429E236_AdjustorThunk },
	{ 0x060004B3, Cinfo_set_supportsUniqueIds_mA096C2070124D20D28C7009BA35B23918DC974A0_AdjustorThunk },
	{ 0x060004B4, Cinfo_get_capabilities_mE55D2E7B6B53FB7350832A08A1D626E2E3855090_AdjustorThunk },
	{ 0x060004B5, Cinfo_set_capabilities_m975B6EDB8EB063F712A26448245859759A35D7CE_AdjustorThunk },
	{ 0x060004B6, Cinfo_Equals_mB548ABCC0365E9FF78A36324ED2F1128B3836FF7_AdjustorThunk },
	{ 0x060004B7, Cinfo_Equals_m0305229B15DB2B5DF7EEEB4C8D28883DC35A9588_AdjustorThunk },
	{ 0x060004B8, Cinfo_GetHashCode_m4D75518C4E9BED4C7BF4508CAD92A03D5D1A77A7_AdjustorThunk },
	{ 0x060004C5, XRRaycast_get_trackableId_mA844E950A9862ABA13C47395893C18A55C9117AB_AdjustorThunk },
	{ 0x060004C6, XRRaycast_get_pose_mADE80A4AABEFCCCB8297186D7E836EAE5B730F73_AdjustorThunk },
	{ 0x060004C7, XRRaycast_get_trackingState_mDBA1DEB482B9346E44263E8B2201C1D8AF919B09_AdjustorThunk },
	{ 0x060004C8, XRRaycast_get_nativePtr_m79D980249D35343744B394D7238F8A8FB943D484_AdjustorThunk },
	{ 0x060004C9, XRRaycast_get_distance_m0B11F8743558DCA40C4E724ECAB8E4DD5ECFFD2B_AdjustorThunk },
	{ 0x060004CA, XRRaycast_get_hitTrackableId_m54245AC20302081DF8658019AA0261DEE1E7D6FE_AdjustorThunk },
	{ 0x060004CB, XRRaycast__ctor_m0ACF53702D817AC34FD8C21F2C01EF7A8F592F9D_AdjustorThunk },
	{ 0x060004CC, XRRaycast_GetHashCode_m94E4A6BDC4CD5E875F40777E273D1E9CD37D54A6_AdjustorThunk },
	{ 0x060004CD, XRRaycast_Equals_m7F141CB415FF28341035CBD2B32037DC80469575_AdjustorThunk },
	{ 0x060004CE, XRRaycast_Equals_m2A00EBA5AD411F5BFF724BB7D60175FAE69F8D74_AdjustorThunk },
	{ 0x060004D3, XRRaycastHit_get_trackableId_m8B92C0F8977D274743D9388DEB7DCEBCC88E7325_AdjustorThunk },
	{ 0x060004D4, XRRaycastHit_set_trackableId_mA41CAE66DB4E6054512F496DABE4C15B6217FA30_AdjustorThunk },
	{ 0x060004D5, XRRaycastHit_get_pose_m3B8D69B763A39178CB583948B4E08255FE9A633E_AdjustorThunk },
	{ 0x060004D6, XRRaycastHit_set_pose_m26D8C795FDFF7DEE86AB77BC5F0A0B6405150AD4_AdjustorThunk },
	{ 0x060004D7, XRRaycastHit_get_distance_m7098B7C90D22697CA37FBBDF50A4109AD055CA80_AdjustorThunk },
	{ 0x060004D8, XRRaycastHit_set_distance_m93182B0265D3D34E9D1730860A5B39F515EA729D_AdjustorThunk },
	{ 0x060004D9, XRRaycastHit_get_hitType_m30A8013E847E6B2B70A9511B522099C03102E933_AdjustorThunk },
	{ 0x060004DA, XRRaycastHit_set_hitType_m89FAB9AF35A52F7CA3F997AE1494EB92B60CA997_AdjustorThunk },
	{ 0x060004DB, XRRaycastHit__ctor_mEFB9D7632D78C282C02A913F1E4A2F7866C6B641_AdjustorThunk },
	{ 0x060004DC, XRRaycastHit_GetHashCode_m7C9DBAE43B929D3D4BBFF37E15E4E01143BC4A6B_AdjustorThunk },
	{ 0x060004DD, XRRaycastHit_Equals_m319801A0EFB8A841B3B7E6197BB612780698759A_AdjustorThunk },
	{ 0x060004DE, XRRaycastHit_Equals_mE45E36906807C4F3C5E28C1F54228142D444DA0A_AdjustorThunk },
	{ 0x060004FD, Cinfo_get_id_m14E2737CF1E90C961F8D5B282C17E125018668E0_AdjustorThunk },
	{ 0x060004FE, Cinfo_set_id_m962E07A26F49D8C32DAEFEC4F9E0F79EBC128533_AdjustorThunk },
	{ 0x060004FF, Cinfo_get_providerType_mB4A72CE35BDA5CCC57B2CCC3E1D88672D9E59021_AdjustorThunk },
	{ 0x06000500, Cinfo_set_providerType_m5D183591B4A3ECA1EC2AA9015C02DCD1EB1F076F_AdjustorThunk },
	{ 0x06000501, Cinfo_get_subsystemTypeOverride_m473983CC8B06F42BC47E92AA261EC2CCBEBFC292_AdjustorThunk },
	{ 0x06000502, Cinfo_set_subsystemTypeOverride_m20C9DAD8677611ACD22689DA249911EEDE46EBF7_AdjustorThunk },
	{ 0x06000503, Cinfo_get_subsystemImplementationType_mF1AF41E2F764A5BF0AF6A35C54C27DE25809AED9_AdjustorThunk },
	{ 0x06000504, Cinfo_set_subsystemImplementationType_m4A20D7E4790C43392F4F82D6B9FAC1D2759F744C_AdjustorThunk },
	{ 0x06000505, Cinfo_get_supportsViewportBasedRaycast_mBAE62868186C81B758E7B9B87F3C67F6C1586700_AdjustorThunk },
	{ 0x06000506, Cinfo_set_supportsViewportBasedRaycast_mC7DB604D2E288A145177EC66F8C1DAA50F38693F_AdjustorThunk },
	{ 0x06000507, Cinfo_get_supportsWorldBasedRaycast_m6D9743F420975015239E33D6641CA95A092D501D_AdjustorThunk },
	{ 0x06000508, Cinfo_set_supportsWorldBasedRaycast_m05D5BC11896AED796A6E237BBB98FA4EF3CF17A4_AdjustorThunk },
	{ 0x06000509, Cinfo_get_supportedTrackableTypes_mA59CFA06B5968E2DF45075C6C40E0A776CC19488_AdjustorThunk },
	{ 0x0600050A, Cinfo_set_supportedTrackableTypes_m319BC9C9EB554C35CD40B791917A603D63BEEE0A_AdjustorThunk },
	{ 0x0600050B, Cinfo_get_supportsTrackedRaycasts_mF9B448767ADE954E8357D686935ACCF3208DB1B8_AdjustorThunk },
	{ 0x0600050C, Cinfo_set_supportsTrackedRaycasts_m7E65DAEB9ED0CB4C4C83DB751C1547145949AA41_AdjustorThunk },
	{ 0x0600050D, Cinfo_GetHashCode_m6CC66C9C4BA7904DF1E9E9D7A3C74DC8D6A6C1BA_AdjustorThunk },
	{ 0x0600050E, Cinfo_Equals_m4D9FFB9CFA4DF03E9AF2763D3E91926896FCD64D_AdjustorThunk },
	{ 0x0600050F, Cinfo_ToString_m3022468C4555B097321DCDB08B4079DA430EBB3A_AdjustorThunk },
	{ 0x06000510, Cinfo_Equals_mD2E403B4E791DCE658297F8CA484149EB2D5F5B8_AdjustorThunk },
	{ 0x06000513, ScopedProfiler__ctor_m652B5689DE1A3C3EF7D12801DA27FA3B40E4412F_AdjustorThunk },
	{ 0x06000514, ScopedProfiler__ctor_m1F4C2F43E028839CDD9B09EB51402C6F706431B5_AdjustorThunk },
	{ 0x06000515, ScopedProfiler_Dispose_m7B646405B4E52CC4677329D3B860BE9C17A9DAC4_AdjustorThunk },
	{ 0x0600051A, SerializableGuid__ctor_m0F2435157FEC8427E91A7D0DD39960BADE7209F0_AdjustorThunk },
	{ 0x0600051C, SerializableGuid_get_guid_mC9C573E5730B2B18F6DFA80F0BCFD1A097C362B3_AdjustorThunk },
	{ 0x0600051D, SerializableGuid_GetHashCode_mC33B7B6D908B3A62767C19B331620784F1998D07_AdjustorThunk },
	{ 0x0600051E, SerializableGuid_Equals_mEB4A1B39DD600CB499AC43BF60A3BD206A1EFD71_AdjustorThunk },
	{ 0x0600051F, SerializableGuid_ToString_m4FB29C69FF91DC2020A96C3C83FE1B60F9C73047_AdjustorThunk },
	{ 0x06000520, SerializableGuid_ToString_m66A8E16F22314214DECE08D94A189101A421603E_AdjustorThunk },
	{ 0x06000521, SerializableGuid_ToString_m514BCF03CE14CE663D9ECC9616DD28453334BE96_AdjustorThunk },
	{ 0x06000522, SerializableGuid_Equals_m7096244EB28310B3CB17CD79EE7068768C6AB4F7_AdjustorThunk },
	{ 0x06000560, Cinfo_get_supportsInstall_mEEEDF86E5DE1B7515989BAECAF2F1714A327B720_AdjustorThunk },
	{ 0x06000561, Cinfo_set_supportsInstall_mA4CF39BDB54C42BC8CBD401F6A77353B59EB074F_AdjustorThunk },
	{ 0x06000562, Cinfo_get_supportsMatchFrameRate_mD86213A3EA6096133BF355DDDC55823027B48B7C_AdjustorThunk },
	{ 0x06000563, Cinfo_set_supportsMatchFrameRate_mC501E193696EC21EF655C7B789CABFE2D7D2B3E4_AdjustorThunk },
	{ 0x06000564, Cinfo_get_id_m20097DC1BBE19C329FD6F99312B6B5CD14678921_AdjustorThunk },
	{ 0x06000565, Cinfo_set_id_m7ABF4746E3D8D5C9F5E845638AB861FFBF665594_AdjustorThunk },
	{ 0x06000566, Cinfo_get_providerType_m89ED3FB1640690CFD1735E14FB7834A2BF9203EC_AdjustorThunk },
	{ 0x06000567, Cinfo_set_providerType_m2B27139CECCAF21A444D7891124893D11B7B6484_AdjustorThunk },
	{ 0x06000568, Cinfo_get_subsystemTypeOverride_m5A1888DCB068C90C7C5197246602893767C5A2B7_AdjustorThunk },
	{ 0x06000569, Cinfo_set_subsystemTypeOverride_m5DD40EFEE5ADFC4A6624E1EF7345F63D66027423_AdjustorThunk },
	{ 0x0600056A, Cinfo_get_subsystemImplementationType_m1E7C284EE29C67952C8742FD257B3BA09F84D8B3_AdjustorThunk },
	{ 0x0600056B, Cinfo_set_subsystemImplementationType_m116B6D5857F561E8119812F01089CDDC08AFF3CA_AdjustorThunk },
	{ 0x0600056C, Cinfo_GetHashCode_m2D7B3F90910D284E6D8696F3E187E61436768107_AdjustorThunk },
	{ 0x0600056D, Cinfo_Equals_m0C051CCCCA3A44F0C851768FE1CDB49DAC6D82B6_AdjustorThunk },
	{ 0x0600056E, Cinfo_Equals_m053CC1A9E8D4DBF1FAFACF66083B615EF36CA572_AdjustorThunk },
	{ 0x06000571, XRSessionUpdateParams_get_screenOrientation_m5BD0BD187D579592C71665C78BB09685F08BB23C_AdjustorThunk },
	{ 0x06000572, XRSessionUpdateParams_set_screenOrientation_m95E8C1C9AEEFCD0577AE4605645FA8CD8F1D6B9B_AdjustorThunk },
	{ 0x06000573, XRSessionUpdateParams_get_screenDimensions_mED2BC29E3B820C5CF96ED275DFA172B23EA52119_AdjustorThunk },
	{ 0x06000574, XRSessionUpdateParams_set_screenDimensions_mC99924339E008CEFCF202EC394463F00DF0DA4B3_AdjustorThunk },
	{ 0x06000575, XRSessionUpdateParams_GetHashCode_m735A861B2C2718DBF5588467EC76FC6EC77EFE8D_AdjustorThunk },
	{ 0x06000576, XRSessionUpdateParams_Equals_mE2FA6A03BEBC662F543FA73D25561369FAEE7EAF_AdjustorThunk },
	{ 0x06000577, XRSessionUpdateParams_ToString_m82D102D9405D9B5FCA96E55074C982F225287D80_AdjustorThunk },
	{ 0x06000578, XRSessionUpdateParams_Equals_mEC4D21B1DFB2DB2327FCE21B43C144DD2003828C_AdjustorThunk },
	{ 0x0600057C, TrackableId_get_subId1_m1F4296FEADE76DF0379F20BCBD94E807E2EF021F_AdjustorThunk },
	{ 0x0600057D, TrackableId_set_subId1_mFA12049C24961BC2FE7D41A2D0FE30DF4B3F39D2_AdjustorThunk },
	{ 0x0600057E, TrackableId_get_subId2_m53BAB4D373B736E473381B24CB608EEF666BA24E_AdjustorThunk },
	{ 0x0600057F, TrackableId_set_subId2_mB7AA91412C0731CF59A8CC24CF75012D3C77C76D_AdjustorThunk },
	{ 0x06000580, TrackableId__ctor_mB12C56ADDEFA44578A429DDA57A6C78B833B41F5_AdjustorThunk },
	{ 0x06000581, TrackableId__ctor_m75F2739A83A25E2B7C34DE87E85187F79A4C86AF_AdjustorThunk },
	{ 0x06000582, TrackableId_ToString_m4BE1AD91726751D994E6FB864B231BE5D7D3F85F_AdjustorThunk },
	{ 0x06000583, TrackableId_GetHashCode_m6150BF091C3C17A84021CC18B443D5C297F89537_AdjustorThunk },
	{ 0x06000584, TrackableId_Equals_m67C98169A04DB96CCEBC08A05B3FF9544B52C3E5_AdjustorThunk },
	{ 0x06000585, TrackableId_Equals_m7263BB158392C4F1B57BEE16D4F1FBBCF01E4A6E_AdjustorThunk },
	{ 0x060005A9, XRTextureDescriptor_get_nativeTexture_m1E27C0E1DC11DDC6139178509EE91B8DF54DBAD4_AdjustorThunk },
	{ 0x060005AA, XRTextureDescriptor_set_nativeTexture_mE5EF6CBBBE13191EF65501EC9A45C2F64964B27D_AdjustorThunk },
	{ 0x060005AB, XRTextureDescriptor_get_width_m570472F03994BC63F21751414105A2E0C112DBF2_AdjustorThunk },
	{ 0x060005AC, XRTextureDescriptor_set_width_mA5D674B5378CB5B8AADD7A93E027CBF4BD27A37C_AdjustorThunk },
	{ 0x060005AD, XRTextureDescriptor_get_height_mC0B37241C24FA883E2594B9411080EDF654E3E01_AdjustorThunk },
	{ 0x060005AE, XRTextureDescriptor_set_height_mCD63667233B39883DF1E431446ED926AC3AF3992_AdjustorThunk },
	{ 0x060005AF, XRTextureDescriptor_get_mipmapCount_m4B2ED0D6EBE06AD86E356203B4AB5DE3807C1D31_AdjustorThunk },
	{ 0x060005B0, XRTextureDescriptor_set_mipmapCount_m7B8AAB937C5157B15A280672BC5C105FAF30D7E8_AdjustorThunk },
	{ 0x060005B1, XRTextureDescriptor_get_format_mA745AA87046D4FE4846C11B8285B980FF6DDDD1A_AdjustorThunk },
	{ 0x060005B2, XRTextureDescriptor_set_format_mAB9FB1797A83CC68AC222A861C185FE2F8035058_AdjustorThunk },
	{ 0x060005B3, XRTextureDescriptor_get_propertyNameId_mF5A620F0DAEE746BDD293DB7F02909FB5404DCC1_AdjustorThunk },
	{ 0x060005B4, XRTextureDescriptor_set_propertyNameId_m4D99BAF8AF884D653834D29D124F106A4AD7189D_AdjustorThunk },
	{ 0x060005B5, XRTextureDescriptor_get_valid_mBEE2CC268CC8773618BAB7794118746E235A6761_AdjustorThunk },
	{ 0x060005B6, XRTextureDescriptor_get_depth_m5885EBF7D767C918B1483D63D1B11EE60D939E7D_AdjustorThunk },
	{ 0x060005B7, XRTextureDescriptor_set_depth_mD62E28995B11B8631C2DF7B02416A2D310F35C49_AdjustorThunk },
	{ 0x060005B8, XRTextureDescriptor_get_dimension_mAEB2447102404A845F9B20317A2AB82B956E4A12_AdjustorThunk },
	{ 0x060005B9, XRTextureDescriptor_set_dimension_m75DC4703441BF9E812D18C0DFBF0A9839A52554B_AdjustorThunk },
	{ 0x060005BA, XRTextureDescriptor__ctor_m32EAA2098F51625289A1BFEFFAC002BA9F274ACF_AdjustorThunk },
	{ 0x060005BB, XRTextureDescriptor_hasIdenticalTextureMetadata_mB4DA1A4CFF4ABB66F8FF3AF1F310E60BA1B3F872_AdjustorThunk },
	{ 0x060005BC, XRTextureDescriptor_Reset_m1BE8024830BA7AFB94AAD01731FDB449DD12A01F_AdjustorThunk },
	{ 0x060005BD, XRTextureDescriptor_Equals_m4931F85C225CAC63EC71FBCE246204E244B6CA2B_AdjustorThunk },
	{ 0x060005BE, XRTextureDescriptor_Equals_m42127F01DF3CDEA1F38CF07E6057E8AD9E6F4570_AdjustorThunk },
	{ 0x060005C1, XRTextureDescriptor_GetHashCode_mFEB456F0A0985232D0E342B8F10669149F190012_AdjustorThunk },
	{ 0x060005C2, XRTextureDescriptor_ToString_m452F36D253986001921C5F627E67E2452D685493_AdjustorThunk },
};
static const int32_t s_InvokerIndices[1474] = 
{
	11835,
	7776,
	11803,
	1195,
	585,
	7749,
	7671,
	7618,
	7620,
	7595,
	7618,
	4573,
	4450,
	10231,
	10231,
	11802,
	7776,
	3882,
	2041,
	1316,
	4541,
	0,
	2041,
	1316,
	4541,
	7776,
	7540,
	11594,
	6449,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7540,
	6094,
	7618,
	4450,
	4700,
	10271,
	10271,
	11724,
	7618,
	7618,
	7766,
	7350,
	7618,
	7620,
	1079,
	1681,
	3383,
	7656,
	7618,
	4450,
	4574,
	10232,
	10232,
	7619,
	7707,
	7707,
	7542,
	7645,
	7645,
	7618,
	7620,
	7618,
	7707,
	7566,
	7707,
	7707,
	7542,
	7767,
	7709,
	7798,
	7707,
	7540,
	7540,
	7540,
	7540,
	7540,
	7540,
	7540,
	7540,
	7540,
	7540,
	7540,
	7540,
	7540,
	7540,
	7540,
	7540,
	5,
	4,
	4283,
	4283,
	4283,
	4283,
	4283,
	4283,
	4283,
	4575,
	4450,
	10233,
	10233,
	7618,
	7656,
	7540,
	7540,
	7540,
	7540,
	7540,
	7540,
	7540,
	7540,
	7540,
	7540,
	7540,
	7620,
	14,
	4283,
	4283,
	4283,
	4283,
	4283,
	4283,
	4283,
	4283,
	4283,
	4283,
	4283,
	7540,
	4576,
	4450,
	10234,
	10234,
	7618,
	7656,
	7765,
	7765,
	7766,
	1783,
	4577,
	4450,
	10235,
	10235,
	7618,
	7656,
	7707,
	6253,
	7707,
	6253,
	7707,
	6253,
	7707,
	6253,
	7618,
	6176,
	4578,
	4450,
	10237,
	10237,
	7618,
	7656,
	7763,
	7763,
	6305,
	7540,
	7540,
	6094,
	7540,
	7540,
	7540,
	6094,
	7763,
	7763,
	6305,
	7656,
	7540,
	7540,
	7358,
	5954,
	7776,
	3777,
	6176,
	4283,
	3773,
	2109,
	2802,
	4283,
	4283,
	11111,
	7656,
	7656,
	7540,
	7540,
	7763,
	7763,
	6305,
	7540,
	7540,
	6094,
	7763,
	7763,
	6305,
	7358,
	5954,
	7540,
	7540,
	6094,
	7540,
	7776,
	7776,
	7776,
	2109,
	4283,
	1829,
	1830,
	2802,
	4283,
	5461,
	6176,
	7776,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	4579,
	4450,
	10238,
	10238,
	7618,
	6325,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	11365,
	7549,
	6105,
	7763,
	6305,
	2832,
	7618,
	4350,
	4450,
	10107,
	10107,
	0,
	7776,
	7620,
	7763,
	7618,
	1680,
	5457,
	7656,
	7618,
	4351,
	4450,
	10108,
	10108,
	2203,
	7776,
	10222,
	10222,
	10222,
	10613,
	10613,
	10613,
	10613,
	9640,
	11534,
	11534,
	11534,
	11534,
	11534,
	10510,
	11219,
	7766,
	6308,
	7618,
	7618,
	7618,
	6176,
	7618,
	6176,
	7566,
	6125,
	7540,
	3470,
	4412,
	6664,
	2393,
	5355,
	1805,
	3558,
	6663,
	3559,
	8584,
	7776,
	6450,
	7776,
	7618,
	4450,
	4580,
	10239,
	10239,
	7656,
	11802,
	1258,
	610,
	611,
	2298,
	4412,
	1254,
	1073,
	6176,
	6176,
	5130,
	2110,
	7776,
	3432,
	539,
	171,
	6213,
	7926,
	6450,
	7618,
	1719,
	0,
	7776,
	7618,
	4450,
	4701,
	10272,
	10272,
	7656,
	7683,
	6235,
	7766,
	6308,
	7618,
	6176,
	7618,
	6176,
	1797,
	7618,
	4703,
	4450,
	10274,
	10274,
	7656,
	7618,
	6176,
	7618,
	6176,
	7304,
	5876,
	1648,
	7618,
	4450,
	4704,
	10275,
	10275,
	7656,
	7620,
	7618,
	7618,
	7618,
	1078,
	4722,
	4450,
	10283,
	10283,
	7618,
	7656,
	7618,
	7766,
	7618,
	7566,
	7618,
	537,
	4702,
	4450,
	10273,
	10273,
	7618,
	7656,
	11209,
	11209,
	11804,
	208,
	7749,
	6292,
	7767,
	6309,
	7671,
	6226,
	7767,
	6309,
	7798,
	6343,
	7618,
	6176,
	7620,
	6178,
	4581,
	4450,
	10240,
	10240,
	7618,
	7656,
	5461,
	11802,
	7776,
	7540,
	6094,
	7540,
	7540,
	6094,
	7540,
	3883,
	636,
	4541,
	11112,
	7540,
	6094,
	7540,
	7540,
	6094,
	7540,
	636,
	4541,
	0,
	7776,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	4582,
	4450,
	10241,
	10241,
	7618,
	6327,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	11366,
	11805,
	7749,
	7671,
	7618,
	7620,
	7671,
	7671,
	7767,
	4450,
	7618,
	10242,
	10242,
	4583,
	11802,
	1062,
	7315,
	7315,
	7309,
	7314,
	7776,
	7618,
	4450,
	7656,
	4584,
	10243,
	10243,
	0,
	7776,
	7618,
	6176,
	7618,
	7618,
	3884,
	1771,
	1771,
	0,
	7618,
	7618,
	6176,
	7618,
	7776,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	4378,
	4450,
	7618,
	10119,
	10119,
	6135,
	7540,
	7540,
	7540,
	7540,
	7540,
	11569,
	10329,
	11000,
	10368,
	11213,
	9417,
	8751,
	8250,
	8094,
	8021,
	7983,
	7960,
	7951,
	6213,
	6213,
	7749,
	6292,
	7671,
	6226,
	7707,
	6253,
	7618,
	6176,
	7620,
	6178,
	11806,
	4586,
	4450,
	10244,
	10244,
	7618,
	11802,
	7618,
	7618,
	7767,
	7671,
	7767,
	7671,
	7540,
	187,
	4587,
	4450,
	10245,
	10245,
	7618,
	7656,
	5461,
	7618,
	7618,
	7765,
	7540,
	1067,
	4588,
	4450,
	10246,
	10246,
	7618,
	7656,
	5461,
	7540,
	6094,
	7540,
	7540,
	6094,
	7540,
	7540,
	6094,
	7540,
	7776,
	3885,
	1771,
	3774,
	11113,
	7540,
	6094,
	7540,
	7540,
	6094,
	7540,
	7540,
	6094,
	7540,
	0,
	1771,
	324,
	7776,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	4589,
	4450,
	10247,
	10247,
	7618,
	6333,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	11367,
	1679,
	7627,
	7620,
	11243,
	7618,
	7656,
	7618,
	4450,
	4308,
	10089,
	10089,
	11093,
	11093,
	11093,
	11093,
	0,
	0,
	0,
	1855,
	5069,
	7540,
	325,
	325,
	461,
	1012,
	0,
	5130,
	0,
	4412,
	7875,
	11785,
	7776,
	6213,
	7540,
	7796,
	7776,
	7618,
	4450,
	4658,
	10265,
	10265,
	6490,
	0,
	0,
	7776,
	7776,
	7776,
	7776,
	7656,
	6213,
	5461,
	3889,
	7618,
	6176,
	7618,
	0,
	0,
	0,
	7618,
	6176,
	7618,
	7776,
	7540,
	7540,
	7540,
	7540,
	11596,
	6452,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7618,
	4706,
	4450,
	10277,
	10277,
	581,
	7595,
	7595,
	7540,
	7765,
	7707,
	7707,
	7656,
	7656,
	7656,
	7618,
	4450,
	4597,
	10254,
	10254,
	7618,
	7656,
	6972,
	7656,
	7656,
	6490,
	5260,
	7595,
	7776,
	7776,
	7776,
	315,
	11811,
	7749,
	7595,
	7671,
	7765,
	7618,
	7620,
	7618,
	4601,
	4450,
	10258,
	10258,
	11802,
	7656,
	6213,
	7656,
	6213,
	7776,
	7776,
	0,
	0,
	0,
	0,
	0,
	7776,
	7776,
	7656,
	6213,
	7776,
	3890,
	0,
	0,
	0,
	6213,
	7776,
	7927,
	6453,
	1163,
	4707,
	4450,
	7618,
	10278,
	10278,
	6213,
	6213,
	7656,
	7595,
	0,
	5461,
	6213,
	4598,
	7618,
	4450,
	10255,
	10255,
	3458,
	7776,
	7618,
	6973,
	7656,
	7656,
	6493,
	7595,
	5261,
	5261,
	6341,
	7776,
	11812,
	7749,
	7671,
	7618,
	7620,
	7595,
	585,
	4450,
	7618,
	10259,
	10259,
	4602,
	11802,
	11093,
	11093,
	11093,
	7618,
	6176,
	7618,
	7618,
	6176,
	7618,
	7618,
	6176,
	7618,
	7540,
	6094,
	7540,
	7618,
	6176,
	7618,
	7776,
	4283,
	4283,
	4283,
	4283,
	4283,
	4283,
	4283,
	4283,
	4283,
	4283,
	3777,
	2802,
	11114,
	7618,
	6176,
	7618,
	7618,
	6176,
	7618,
	7618,
	6176,
	7618,
	7540,
	6094,
	7540,
	7618,
	6176,
	7618,
	4283,
	4283,
	7656,
	4283,
	4283,
	7656,
	4283,
	4283,
	4283,
	4283,
	7656,
	4283,
	4283,
	7656,
	1830,
	2802,
	7776,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7540,
	6094,
	7656,
	6213,
	7540,
	6094,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	4591,
	4450,
	10248,
	10248,
	7618,
	6335,
	7540,
	7618,
	7540,
	7618,
	7540,
	7618,
	7618,
	7540,
	7618,
	11368,
	585,
	11807,
	7749,
	7671,
	7618,
	7620,
	7595,
	7618,
	4592,
	4450,
	10249,
	10249,
	11802,
	7776,
	3886,
	0,
	7776,
	7618,
	6176,
	0,
	0,
	1160,
	11721,
	80,
	7749,
	7749,
	7671,
	7765,
	7765,
	7765,
	7618,
	7618,
	7620,
	7618,
	7707,
	7707,
	7767,
	7662,
	1016,
	7656,
	4450,
	7618,
	10101,
	10101,
	4337,
	11802,
	11093,
	11093,
	7776,
	7618,
	6176,
	7618,
	3881,
	1771,
	0,
	0,
	1771,
	0,
	7618,
	6176,
	7618,
	7776,
	7540,
	7540,
	7540,
	7540,
	7540,
	11597,
	6454,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	4708,
	4450,
	7618,
	10279,
	10279,
	3887,
	3561,
	7776,
	7776,
	7776,
	7776,
	0,
	0,
	7776,
	6451,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	11595,
	7656,
	6213,
	7656,
	6213,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7618,
	6176,
	4705,
	4450,
	7618,
	10276,
	10276,
	11808,
	1195,
	7749,
	7671,
	7618,
	7620,
	7618,
	4593,
	4450,
	10250,
	10250,
	11802,
	7315,
	5903,
	7311,
	5897,
	7313,
	5901,
	7776,
	7618,
	4450,
	7656,
	4594,
	10251,
	10251,
	3887,
	3561,
	7776,
	7776,
	7776,
	7776,
	0,
	0,
	7776,
	6455,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	11598,
	7656,
	6213,
	7656,
	6213,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7618,
	6176,
	4709,
	4450,
	7618,
	10280,
	10280,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	11809,
	7749,
	7671,
	7618,
	7620,
	7707,
	7749,
	316,
	7618,
	4450,
	4595,
	10252,
	10252,
	11802,
	11810,
	7749,
	6292,
	7671,
	6226,
	7707,
	6253,
	7618,
	6176,
	1196,
	7618,
	4450,
	4596,
	10253,
	10253,
	11802,
	7776,
	3888,
	1329,
	1312,
	6292,
	1230,
	1231,
	7776,
	7776,
	7776,
	1329,
	1312,
	6292,
	1853,
	597,
	598,
	7776,
	7540,
	6094,
	7540,
	6094,
	7618,
	6176,
	7540,
	6094,
	11599,
	6456,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7540,
	6094,
	7540,
	6094,
	7618,
	6176,
	7540,
	6094,
	7618,
	4450,
	7656,
	4710,
	10281,
	10281,
	6213,
	3435,
	7776,
	0,
	0,
	0,
	0,
	3523,
	11785,
	7595,
	7618,
	4450,
	7656,
	5461,
	2592,
	4492,
	10185,
	10185,
	11802,
	11093,
	11093,
	7620,
	7595,
	7656,
	7656,
	7776,
	7776,
	3789,
	6342,
	7344,
	5942,
	7763,
	3764,
	7776,
	7776,
	7618,
	7763,
	6305,
	7763,
	7656,
	6213,
	7618,
	7540,
	7540,
	6094,
	7618,
	11802,
	7776,
	7776,
	6342,
	3536,
	7763,
	7763,
	6305,
	7763,
	3764,
	7776,
	7776,
	7776,
	7776,
	7620,
	7656,
	7656,
	7618,
	7618,
	7595,
	7540,
	7540,
	6094,
	7618,
	7776,
	7540,
	6094,
	7540,
	6094,
	11600,
	6457,
	7540,
	6094,
	7540,
	6094,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7618,
	4711,
	4450,
	10282,
	10282,
	7618,
	6176,
	7766,
	6308,
	7618,
	4450,
	7656,
	4599,
	10256,
	10256,
	11791,
	7763,
	6305,
	7763,
	6305,
	3523,
	6213,
	7656,
	7618,
	4450,
	4541,
	10212,
	10212,
	11802,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	11093,
	11093,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	7620,
	6178,
	7618,
	6176,
	7618,
	6176,
	7618,
	6176,
	7618,
	6176,
	7618,
	6176,
	7540,
	7618,
	6176,
	7618,
	6176,
	132,
	4600,
	7776,
	4600,
	4450,
	10257,
	10257,
	7618,
	7656,
};
static const Il2CppTokenIndexMethodTuple s_reversePInvokeIndices[1] = 
{
	{ 0x0600016D, 6,  (void**)&XRCpuImage_OnAsyncConversionComplete_mDC3A0C88A34909C9D08E4BE7E94C8E27E2BB3D3C_RuntimeMethod_var, 0 },
};
static const Il2CppTokenRangePair s_rgctxIndices[21] = 
{
	{ 0x02000083, { 69, 6 } },
	{ 0x02000084, { 75, 4 } },
	{ 0x0200008D, { 79, 22 } },
	{ 0x0200009C, { 101, 14 } },
	{ 0x0200009D, { 115, 2 } },
	{ 0x0200009E, { 117, 20 } },
	{ 0x020000A0, { 137, 6 } },
	{ 0x0600018C, { 0, 3 } },
	{ 0x06000237, { 3, 7 } },
	{ 0x0600035B, { 10, 5 } },
	{ 0x0600035C, { 15, 8 } },
	{ 0x0600035D, { 23, 4 } },
	{ 0x0600035E, { 27, 9 } },
	{ 0x0600035F, { 36, 11 } },
	{ 0x06000366, { 47, 1 } },
	{ 0x06000367, { 48, 2 } },
	{ 0x06000377, { 50, 2 } },
	{ 0x06000415, { 52, 1 } },
	{ 0x06000416, { 53, 2 } },
	{ 0x06000438, { 55, 7 } },
	{ 0x06000439, { 62, 7 } },
};
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_t8CEE0CD1B9B02FE98150FDD9CD4BAF570ABE6AC7_m9544C4B83A26A6D3CE8072AC14CA61CE953BFD6C;
extern const uint32_t g_rgctx_NativeArrayUnsafeUtility_ConvertExistingDataToNativeArray_TisT_t8CEE0CD1B9B02FE98150FDD9CD4BAF570ABE6AC7_m8A3CADFD90C6A321EE9469C3EF9EA6CFAF8E6CFF;
extern const uint32_t g_rgctx_NativeArray_1_tE60FB9FF3DE41F71757C647DB0D383A40C3E2C92;
extern const uint32_t g_rgctx_NativeArray_1U26_t8D3315C2A4D77133030E94CCEBDDAF955FB48B0D;
extern const uint32_t g_rgctx_NativeArray_1_get_IsCreated_mE6BF167469B67D3CF8E7BF4AC3B0F35729AAFD51;
extern const uint32_t g_rgctx_NativeArray_1_t2F2012F8E6F0C9EA8AAD80C0A96AEBCC1CD46724;
extern const uint32_t g_rgctx_NativeArray_1_get_Length_mA2C9D97218143C7BD11DCFDBF15C06B0197FDD1B;
extern const uint32_t g_rgctx_NativeArray_1_Dispose_m377A12F2775E802CB9610C74CC4D932E415860FA;
extern const uint32_t g_rgctx_NativeArray_1_t2F2012F8E6F0C9EA8AAD80C0A96AEBCC1CD46724;
extern const uint32_t g_rgctx_NativeArray_1__ctor_mB9E557421B9838DBAF3CF5F9F77B6C65AB92BB62;
extern const uint32_t g_rgctx_T_t46F0637140E4FF25028DE7404F0247222D7797BE;
extern const uint32_t g_rgctx_NativeCopyUtility_CreateArrayFilledWithValue_TisT_t46F0637140E4FF25028DE7404F0247222D7797BE_mD1585AC9C86748B292EEC47448DC0C741A8909A8;
extern const uint32_t g_rgctx_NativeArray_1_tAED91BCD750B3EB0E02D5E64593290DF944F98B9;
extern const uint32_t g_rgctx_NativeArrayUnsafeUtility_GetUnsafePtr_TisT_t46F0637140E4FF25028DE7404F0247222D7797BE_m9E5CE904967575F9F4A704ACECB92B458D185431;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_t46F0637140E4FF25028DE7404F0247222D7797BE_mCF5E37A6A72157F01663AB853E117192FB833B2C;
extern const uint32_t g_rgctx_NativeArray_1_t9D982576BF235A006BB2783374B8D65BBD8AEADB;
extern const uint32_t g_rgctx_NativeArray_1_get_Length_m4BD7EC804375A02C6E99D67ACC20E4A09D46B459;
extern const uint32_t g_rgctx_NativeArray_1_t9D982576BF235A006BB2783374B8D65BBD8AEADB;
extern const uint32_t g_rgctx_NativeArrayUnsafeUtility_GetUnsafePtr_TisT_t46D723EBCAFD9FCDBA5C1EED2947CF9633778E65_m2F5D87E043ACC5074B34DAAA50E2CFD808472338;
extern const uint32_t g_rgctx_T_t46D723EBCAFD9FCDBA5C1EED2947CF9633778E65;
extern const uint32_t g_rgctx_UnsafeUtility_AddressOf_TisT_t46D723EBCAFD9FCDBA5C1EED2947CF9633778E65_m61A84F02D7286F06EE0E5D7486BC4F2A067DC17B;
extern const uint32_t g_rgctx_TU26_t2230670192D1109FBD18915B9130A3BAFB945AE7;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_t46D723EBCAFD9FCDBA5C1EED2947CF9633778E65_mD929F8CB76205014C143EE33DB9BF8827B1B75DE;
extern const uint32_t g_rgctx_NativeArray_1_t7E4D78F08124A021427A81B79A80593A0A1A2AB0;
extern const uint32_t g_rgctx_NativeArray_1__ctor_m3AC7456BE623BFBA31BDD90AAD220EEE3EDB41FC;
extern const uint32_t g_rgctx_T_tE00E2EDF5114EDED253006285FA92C0F10576ABE;
extern const uint32_t g_rgctx_NativeCopyUtility_FillArrayWithValue_TisT_tE00E2EDF5114EDED253006285FA92C0F10576ABE_m7614963F66C245D1D6D1D5FD9894485E1BB23171;
extern const uint32_t g_rgctx_IReadOnlyList_1_tBC7A39B064452C20BA8EE81A515A9C245EE0A8C4;
extern const uint32_t g_rgctx_IReadOnlyCollection_1_t6927420BAAE06F08CCD8DDB0F8E84A05518EF334;
extern const uint32_t g_rgctx_IReadOnlyCollection_1_get_Count_m96CB02C2FB8E357950B81B6D4C6DA55FD8BE2615;
extern const uint32_t g_rgctx_NativeArray_1_tAAAAFD90F19BCB55F92FB66CF9142F8F1BE53990;
extern const uint32_t g_rgctx_NativeArray_1_get_Length_mE88B5B8C31DC915C8D8C647738714E4FC5A618EA;
extern const uint32_t g_rgctx_NativeArray_1_tAAAAFD90F19BCB55F92FB66CF9142F8F1BE53990;
extern const uint32_t g_rgctx_IReadOnlyList_1_get_Item_m75FC1CFD5484A398F7AC7679835775D72E6339F1;
extern const uint32_t g_rgctx_T_tCED23063971432D2553EFD24D045EB088076A383;
extern const uint32_t g_rgctx_NativeArray_1_set_Item_m9D779C0589E3D8DF61F6E7EC5435E26367891DF5;
extern const uint32_t g_rgctx_IReadOnlyCollection_1_tBCC0AD0EFAED46896493856525DB2269B2A487C9;
extern const uint32_t g_rgctx_IReadOnlyCollection_1_get_Count_mD9257876966C88CDBBA94E1478DC0ACE61F19AD6;
extern const uint32_t g_rgctx_NativeArray_1_t31ABE3C659D930F34CD7782C360A8E18354ECB00;
extern const uint32_t g_rgctx_NativeArray_1_get_Length_m6EC7C0F1D486F12469B1A81D977AD7E7CE1247EE;
extern const uint32_t g_rgctx_NativeArray_1_t31ABE3C659D930F34CD7782C360A8E18354ECB00;
extern const uint32_t g_rgctx_IEnumerable_1_tB11DCC4533B0D79CEE16A7B65A0CC044600C52DD;
extern const uint32_t g_rgctx_IEnumerable_1_GetEnumerator_mE413ADEC7A22B714DA27EA57572D7682B644CF53;
extern const uint32_t g_rgctx_IEnumerator_1_tF149B80C37B727A282D71C1712D6A64B07A8197A;
extern const uint32_t g_rgctx_IEnumerator_1_get_Current_mB2006D5F92F5CF00379A078F8DE3F7FA4FBF31D6;
extern const uint32_t g_rgctx_T_t45647A49FE978B1D0B0E997FC1BEA3661E2BF799;
extern const uint32_t g_rgctx_NativeArray_1_set_Item_mDBC781F75F00BFA4DD54DB1D073C35BDA0E8AA5C;
extern const uint32_t g_rgctx_T_t87DD8B59E813AB4F081B63405E60A90B7B08FF71;
extern const uint32_t g_rgctx_TProvider_t8E51106FBEB05373E37A026A5A59BD8D59185CA4;
extern const uint32_t g_rgctx_TSubsystemOverride_t613186FECC10DF96812888E7BB71A034FD38A0B8;
extern const uint32_t g_rgctx_T_tFF908F73D211A2D8E3E3A00C95FC122E764F8FF8;
extern const uint32_t g_rgctx_T_tFF908F73D211A2D8E3E3A00C95FC122E764F8FF8;
extern const uint32_t g_rgctx_T_tEDF74920531CBF513CE8BFE13B89C2CABE1D9611;
extern const uint32_t g_rgctx_TProvider_tC0C9AEDC62BC6FE5F0A0E187A6D5971905CCC0F5;
extern const uint32_t g_rgctx_TSubsystemOverride_tE37891F1FE6079154BBEBC4423A0C1DF181A60A2;
extern const uint32_t g_rgctx_NativeArray_1U26_tD00A9429EA654F85BAC7728BAF349ACF60EF925E;
extern const uint32_t g_rgctx_NativeArray_1_get_IsCreated_m264624AF7CA54FB7659CD77F51B234E5A8511586;
extern const uint32_t g_rgctx_NativeArray_1_t1DCC210C57A703DF6F293CBDDA240719C71C8B60;
extern const uint32_t g_rgctx_NativeArray_1_get_Length_mB1A8074527B615D0AB2B3660707AA2380157EACE;
extern const uint32_t g_rgctx_NativeArray_1_Dispose_m2E96ABE1BABD1970A64B2F856BF35D1AA6008CCC;
extern const uint32_t g_rgctx_NativeArray_1_t1DCC210C57A703DF6F293CBDDA240719C71C8B60;
extern const uint32_t g_rgctx_NativeArray_1__ctor_m406B72E76589EDB1676506C086E8C77F7D27A636;
extern const uint32_t g_rgctx_NativeArray_1U26_t06EB9038716BB318BAA17BC3C82F32EA0CF4811C;
extern const uint32_t g_rgctx_NativeArray_1_get_IsCreated_m35A13DD17423ADDD0D3F2D0219446431297D42F2;
extern const uint32_t g_rgctx_NativeArray_1_t298BCF88035223587801CB0C703C78D3913EF0D3;
extern const uint32_t g_rgctx_NativeArray_1_get_Length_m27918463CD8696C71C72A0F8544DB09426669A23;
extern const uint32_t g_rgctx_NativeArray_1_Dispose_m04C4499D0AED9E7AE2BD56EA5AF6ABC7DBA29C20;
extern const uint32_t g_rgctx_NativeArray_1_t298BCF88035223587801CB0C703C78D3913EF0D3;
extern const uint32_t g_rgctx_NativeArray_1__ctor_m4163181E70C48BDF3C451664C7860BA00C5ECB24;
extern const uint32_t g_rgctx_Promise_1_t0CAB1D97415B8BD7BE81702FD980AE8E0FDAB6DF;
extern const uint32_t g_rgctx_Promise_1_OnKeepWaiting_mBAAC90230333F68982E97CC008099215EB16A62D;
extern const uint32_t g_rgctx_T_tA483635EF1C01EFC033CDAEB9180FEAD7CB26ED7;
extern const uint32_t g_rgctx_ImmediatePromise_t11844090DF8810696B57F81F4D09AFB4CDABAB42;
extern const uint32_t g_rgctx_ImmediatePromise__ctor_m4E090331677D6C21BEB668A07AF721700FC695F3;
extern const uint32_t g_rgctx_Promise_1_set_result_m1B9C9EB65EF0E424BED23AF9823D4867A8B61734;
extern const uint32_t g_rgctx_Promise_1__ctor_mCC6AA499EF6F09B0219FCB874EBDC4D936BFCC6A;
extern const uint32_t g_rgctx_Promise_1_t1AEED25D14429797D0ED3CC125ED1AA53513F174;
extern const uint32_t g_rgctx_T_tAAE31BB78C900E79091FD49490FDED36A9FABD4A;
extern const uint32_t g_rgctx_Promise_1_Resolve_mC504ED1C3051344A1FAEF6BCC4526AF92D539536;
extern const uint32_t g_rgctx_SerializableDictionary_2_tAF6E81F87930FEE664A25CE62370D6103276E7BC;
extern const uint32_t g_rgctx_Dictionary_2_t46469724FC3429657A1535432DF45E61368A845A;
extern const uint32_t g_rgctx_SerializableDictionary_2_get_dictionary_m105E9CAC7EF9E542207F7FECCDF5FABE955AD325;
extern const uint32_t g_rgctx_Dictionary_2_get_Count_m5B54E0C634BC9E38FA50194BDA4340FDA5B3B3AE;
extern const uint32_t g_rgctx_KeyValuePairU5BU5D_t86595024C041DB8623BADA1640E11606ED5AF3B7;
extern const uint32_t g_rgctx_KeyValuePairU5BU5D_t86595024C041DB8623BADA1640E11606ED5AF3B7;
extern const uint32_t g_rgctx_Dictionary_2_GetEnumerator_m0E22A1778DBA563E5DB9EBEB5F0989B26AF49461;
extern const uint32_t g_rgctx_Enumerator_t830646EFE3C118AB1C95EA23A0559718743CE1B1;
extern const uint32_t g_rgctx_Enumerator_get_Current_m16C410B950F9EC9401EB5BD3B0486801464F57CC;
extern const uint32_t g_rgctx_Enumerator_t830646EFE3C118AB1C95EA23A0559718743CE1B1;
extern const uint32_t g_rgctx_KeyValuePair_2_t32441F20B8BFB9776199A3EA374BAEA4081E15F4;
extern const uint32_t g_rgctx_KeyValuePair_t2B7EAE6840DB2924E512FA67E009CBDADBE5AFC4;
extern const uint32_t g_rgctx_KeyValuePair_2_get_Key_mD9EA4F93592369501A9FAA3B59B3D550103DA2DF;
extern const uint32_t g_rgctx_KeyValuePair_2_t32441F20B8BFB9776199A3EA374BAEA4081E15F4;
extern const uint32_t g_rgctx_TKey_t2B31B8647CA98F2E10EFB3BE12F7625CEB350649;
extern const uint32_t g_rgctx_KeyValuePair_2_get_Value_m756CBFED9B24A8F6C39A554F1A12A3514DCF9028;
extern const uint32_t g_rgctx_TValue_t6CCAE4A07745C46EDF1B499AF922687C1FDE901A;
extern const uint32_t g_rgctx_Enumerator_MoveNext_m686C012F6FDE0C4B812E2D41FDA187DADB884C01;
extern const Il2CppRGCTXConstrainedData g_rgctx_Enumerator_t830646EFE3C118AB1C95EA23A0559718743CE1B1_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7;
extern const uint32_t g_rgctx_Dictionary_2_Clear_m447D6FB60BDFEB187401990D2E7C23EC7376132D;
extern const uint32_t g_rgctx_Dictionary_2_Add_m11F68D8B6ADA0C4DB940A648967A8BC846743D2B;
extern const uint32_t g_rgctx_Dictionary_2__ctor_m3DB2A4D4728E59376E1525BC6FABF3E648E0A53C;
extern const uint32_t g_rgctx_TrackableChanges_1_t5C081C6B9DBE043479246BE8E81EFFF32B96BA44;
extern const uint32_t g_rgctx_NativeArray_1_tC85A8F25BE7A76576E3D8A4A8D52ED8FFB979765;
extern const uint32_t g_rgctx_NativeArray_1__ctor_mEEEE9171129A3C1275195FD4AD65DAC36115C619;
extern const uint32_t g_rgctx_TrackableChanges_1_set_isCreated_m9A15621EDC9FF64A40D8DF53144E376708C06857;
extern const uint32_t g_rgctx_TrackableChanges_1_t5C081C6B9DBE043479246BE8E81EFFF32B96BA44;
extern const uint32_t g_rgctx_T_tCAB6448429CF0BAB39FFC61B2580DB460F8E87B9;
extern const uint32_t g_rgctx_NativeCopyUtility_CreateArrayFilledWithValue_TisT_tCAB6448429CF0BAB39FFC61B2580DB460F8E87B9_m29AD4E086A175D6F8C1F808E20DA2D324239C85D;
extern const uint32_t g_rgctx_NativeCopyUtility_PtrToNativeArrayWithDefault_TisT_tCAB6448429CF0BAB39FFC61B2580DB460F8E87B9_m2A676CF290B1B07082D8A7ED0FAE9891B617F520;
extern const uint32_t g_rgctx_NativeArray_1_get_Length_m0A9FF440DDDAD682A81FD8EA54F42A1F38342239;
extern const uint32_t g_rgctx_NativeArray_1_tC85A8F25BE7A76576E3D8A4A8D52ED8FFB979765;
extern const uint32_t g_rgctx_NativeArray_1_CopyFrom_m56585E67CCDEBD89C5A62D74B6A0788A47A9325F;
extern const uint32_t g_rgctx_TrackableChanges_1__ctor_mA225CE3D2A7CB311847427D7AC63D8B87780A64F;
extern const uint32_t g_rgctx_TrackableChanges_1_get_isCreated_mD058CE8ACD0506767BE2E6F309722B98F6727134;
extern const uint32_t g_rgctx_NativeArray_1_Dispose_m7B821AE7B2C8B72348DC493B66344C0AFB74B74D;
extern const uint32_t g_rgctx_SubsystemWithProvider_3__ctor_m296CFE492E776CDFACE84072212984D479903121;
extern const uint32_t g_rgctx_SubsystemWithProvider_3_t917622DEF7441E0CF4969E7958A4A855C808A163;
extern const uint32_t g_rgctx_ValidationUtility_1_t9FE82EB474B35DDC09AFFEF97991D887DF9EFC2C;
extern const uint32_t g_rgctx_ValidationUtility_1_t9FE82EB474B35DDC09AFFEF97991D887DF9EFC2C;
extern const uint32_t g_rgctx_TrackableChanges_1_tD04A53725A94FCDC6C1451CA42F414006596B96E;
extern const uint32_t g_rgctx_TrackableChanges_1_get_added_m5730CC78FF0AB0F9237D32BBE517B9FB48006870;
extern const uint32_t g_rgctx_TrackableChanges_1_tD04A53725A94FCDC6C1451CA42F414006596B96E;
extern const uint32_t g_rgctx_NativeArray_1_tE425D0061965BEAD252F4AD4929205E56C736D55;
extern const uint32_t g_rgctx_NativeArray_1_GetEnumerator_m5EE50000A53B45D519978B40EF1089DC9778A379;
extern const uint32_t g_rgctx_NativeArray_1_tE425D0061965BEAD252F4AD4929205E56C736D55;
extern const uint32_t g_rgctx_Enumerator_tC2736CC8B25AD4EB46B9BD4ADC98BF26C78AE22D;
extern const uint32_t g_rgctx_Enumerator_get_Current_m44563E4727F87D239347259A856F424CD196BC39;
extern const uint32_t g_rgctx_Enumerator_tC2736CC8B25AD4EB46B9BD4ADC98BF26C78AE22D;
extern const uint32_t g_rgctx_T_t0E25A4B26FF79251CAB9D7C9C4283C339BC1F05F;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t0E25A4B26FF79251CAB9D7C9C4283C339BC1F05F_ITrackable_get_trackableId_m7242DF9928E5787565A8DDE0374D160431569206;
extern const uint32_t g_rgctx_ValidationUtility_1_AddToSetAndThrowIfDuplicate_mA88F08B984E30ACBC0F19E4DDB191C35030FDE4F;
extern const uint32_t g_rgctx_Enumerator_MoveNext_mE57804E5B928D0F19982DD88591477540AD6BB2F;
extern const Il2CppRGCTXConstrainedData g_rgctx_Enumerator_tC2736CC8B25AD4EB46B9BD4ADC98BF26C78AE22D_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7;
extern const uint32_t g_rgctx_TrackableChanges_1_get_updated_mE5CAB2DB386973E2CA045267354EF6DAB0F8022F;
extern const uint32_t g_rgctx_TrackableChanges_1_get_removed_mE544933CC6E98991F37B0A6129FAAFE1AFE77FC7;
extern const uint32_t g_rgctx_ValidationUtility_1_ValidateAndThrow_mE3BAE011CA1B620D0EB696FB44F6B0ECC8F926AD;
extern const uint32_t g_rgctx_TrackableChanges_1_Dispose_mCF872D88283DE20661760DE3E1925053F7993A94;
extern const uint32_t g_rgctx_XRSubsystem_1_t3A3C5F9AAB5BACBFA3B7E29BC6C0981F60BB0FD3;
extern const uint32_t g_rgctx_XRSubsystem_1_OnDestroyed_m1197DB1C252FE0CEC1E1312AA7B2D93487540467;
extern const uint32_t g_rgctx_XRSubsystem_1_OnStart_m946EED52AFDCB529B3E037B350653CA79A67BDCF;
extern const uint32_t g_rgctx_XRSubsystem_1_OnStop_mCC54FB0F8161B29D486393408C35C213B1AF11FB;
extern const uint32_t g_rgctx_Subsystem_1__ctor_m63C695A54B13A98ACB3620B1804E340F3C929EC1;
extern const uint32_t g_rgctx_Subsystem_1_t60DB20C2FBF9D6BC149DB549AE1C5813A17AAF38;
static const Il2CppRGCTXDefinition s_rgctxValues[143] = 
{
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_t8CEE0CD1B9B02FE98150FDD9CD4BAF570ABE6AC7_m9544C4B83A26A6D3CE8072AC14CA61CE953BFD6C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArrayUnsafeUtility_ConvertExistingDataToNativeArray_TisT_t8CEE0CD1B9B02FE98150FDD9CD4BAF570ABE6AC7_m8A3CADFD90C6A321EE9469C3EF9EA6CFAF8E6CFF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tE60FB9FF3DE41F71757C647DB0D383A40C3E2C92 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1U26_t8D3315C2A4D77133030E94CCEBDDAF955FB48B0D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_IsCreated_mE6BF167469B67D3CF8E7BF4AC3B0F35729AAFD51 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t2F2012F8E6F0C9EA8AAD80C0A96AEBCC1CD46724 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Length_mA2C9D97218143C7BD11DCFDBF15C06B0197FDD1B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_Dispose_m377A12F2775E802CB9610C74CC4D932E415860FA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t2F2012F8E6F0C9EA8AAD80C0A96AEBCC1CD46724 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1__ctor_mB9E557421B9838DBAF3CF5F9F77B6C65AB92BB62 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t46F0637140E4FF25028DE7404F0247222D7797BE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeCopyUtility_CreateArrayFilledWithValue_TisT_t46F0637140E4FF25028DE7404F0247222D7797BE_mD1585AC9C86748B292EEC47448DC0C741A8909A8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tAED91BCD750B3EB0E02D5E64593290DF944F98B9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArrayUnsafeUtility_GetUnsafePtr_TisT_t46F0637140E4FF25028DE7404F0247222D7797BE_m9E5CE904967575F9F4A704ACECB92B458D185431 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_t46F0637140E4FF25028DE7404F0247222D7797BE_mCF5E37A6A72157F01663AB853E117192FB833B2C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t9D982576BF235A006BB2783374B8D65BBD8AEADB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Length_m4BD7EC804375A02C6E99D67ACC20E4A09D46B459 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t9D982576BF235A006BB2783374B8D65BBD8AEADB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArrayUnsafeUtility_GetUnsafePtr_TisT_t46D723EBCAFD9FCDBA5C1EED2947CF9633778E65_m2F5D87E043ACC5074B34DAAA50E2CFD808472338 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t46D723EBCAFD9FCDBA5C1EED2947CF9633778E65 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_AddressOf_TisT_t46D723EBCAFD9FCDBA5C1EED2947CF9633778E65_m61A84F02D7286F06EE0E5D7486BC4F2A067DC17B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t2230670192D1109FBD18915B9130A3BAFB945AE7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_t46D723EBCAFD9FCDBA5C1EED2947CF9633778E65_mD929F8CB76205014C143EE33DB9BF8827B1B75DE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t7E4D78F08124A021427A81B79A80593A0A1A2AB0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1__ctor_m3AC7456BE623BFBA31BDD90AAD220EEE3EDB41FC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tE00E2EDF5114EDED253006285FA92C0F10576ABE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeCopyUtility_FillArrayWithValue_TisT_tE00E2EDF5114EDED253006285FA92C0F10576ABE_m7614963F66C245D1D6D1D5FD9894485E1BB23171 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IReadOnlyList_1_tBC7A39B064452C20BA8EE81A515A9C245EE0A8C4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IReadOnlyCollection_1_t6927420BAAE06F08CCD8DDB0F8E84A05518EF334 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IReadOnlyCollection_1_get_Count_m96CB02C2FB8E357950B81B6D4C6DA55FD8BE2615 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tAAAAFD90F19BCB55F92FB66CF9142F8F1BE53990 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Length_mE88B5B8C31DC915C8D8C647738714E4FC5A618EA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tAAAAFD90F19BCB55F92FB66CF9142F8F1BE53990 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IReadOnlyList_1_get_Item_m75FC1CFD5484A398F7AC7679835775D72E6339F1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tCED23063971432D2553EFD24D045EB088076A383 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_set_Item_m9D779C0589E3D8DF61F6E7EC5435E26367891DF5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IReadOnlyCollection_1_tBCC0AD0EFAED46896493856525DB2269B2A487C9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IReadOnlyCollection_1_get_Count_mD9257876966C88CDBBA94E1478DC0ACE61F19AD6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t31ABE3C659D930F34CD7782C360A8E18354ECB00 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Length_m6EC7C0F1D486F12469B1A81D977AD7E7CE1247EE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t31ABE3C659D930F34CD7782C360A8E18354ECB00 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_tB11DCC4533B0D79CEE16A7B65A0CC044600C52DD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerable_1_GetEnumerator_mE413ADEC7A22B714DA27EA57572D7682B644CF53 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_tF149B80C37B727A282D71C1712D6A64B07A8197A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerator_1_get_Current_mB2006D5F92F5CF00379A078F8DE3F7FA4FBF31D6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t45647A49FE978B1D0B0E997FC1BEA3661E2BF799 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_set_Item_mDBC781F75F00BFA4DD54DB1D073C35BDA0E8AA5C },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t87DD8B59E813AB4F081B63405E60A90B7B08FF71 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TProvider_t8E51106FBEB05373E37A026A5A59BD8D59185CA4 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TSubsystemOverride_t613186FECC10DF96812888E7BB71A034FD38A0B8 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tFF908F73D211A2D8E3E3A00C95FC122E764F8FF8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tFF908F73D211A2D8E3E3A00C95FC122E764F8FF8 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tEDF74920531CBF513CE8BFE13B89C2CABE1D9611 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TProvider_tC0C9AEDC62BC6FE5F0A0E187A6D5971905CCC0F5 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TSubsystemOverride_tE37891F1FE6079154BBEBC4423A0C1DF181A60A2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1U26_tD00A9429EA654F85BAC7728BAF349ACF60EF925E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_IsCreated_m264624AF7CA54FB7659CD77F51B234E5A8511586 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t1DCC210C57A703DF6F293CBDDA240719C71C8B60 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Length_mB1A8074527B615D0AB2B3660707AA2380157EACE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_Dispose_m2E96ABE1BABD1970A64B2F856BF35D1AA6008CCC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t1DCC210C57A703DF6F293CBDDA240719C71C8B60 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1__ctor_m406B72E76589EDB1676506C086E8C77F7D27A636 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1U26_t06EB9038716BB318BAA17BC3C82F32EA0CF4811C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_IsCreated_m35A13DD17423ADDD0D3F2D0219446431297D42F2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t298BCF88035223587801CB0C703C78D3913EF0D3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Length_m27918463CD8696C71C72A0F8544DB09426669A23 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_Dispose_m04C4499D0AED9E7AE2BD56EA5AF6ABC7DBA29C20 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t298BCF88035223587801CB0C703C78D3913EF0D3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1__ctor_m4163181E70C48BDF3C451664C7860BA00C5ECB24 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Promise_1_t0CAB1D97415B8BD7BE81702FD980AE8E0FDAB6DF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Promise_1_OnKeepWaiting_mBAAC90230333F68982E97CC008099215EB16A62D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tA483635EF1C01EFC033CDAEB9180FEAD7CB26ED7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ImmediatePromise_t11844090DF8810696B57F81F4D09AFB4CDABAB42 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ImmediatePromise__ctor_m4E090331677D6C21BEB668A07AF721700FC695F3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Promise_1_set_result_m1B9C9EB65EF0E424BED23AF9823D4867A8B61734 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Promise_1__ctor_mCC6AA499EF6F09B0219FCB874EBDC4D936BFCC6A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Promise_1_t1AEED25D14429797D0ED3CC125ED1AA53513F174 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tAAE31BB78C900E79091FD49490FDED36A9FABD4A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Promise_1_Resolve_mC504ED1C3051344A1FAEF6BCC4526AF92D539536 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_SerializableDictionary_2_tAF6E81F87930FEE664A25CE62370D6103276E7BC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Dictionary_2_t46469724FC3429657A1535432DF45E61368A845A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SerializableDictionary_2_get_dictionary_m105E9CAC7EF9E542207F7FECCDF5FABE955AD325 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_get_Count_m5B54E0C634BC9E38FA50194BDA4340FDA5B3B3AE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_KeyValuePairU5BU5D_t86595024C041DB8623BADA1640E11606ED5AF3B7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_KeyValuePairU5BU5D_t86595024C041DB8623BADA1640E11606ED5AF3B7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_GetEnumerator_m0E22A1778DBA563E5DB9EBEB5F0989B26AF49461 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t830646EFE3C118AB1C95EA23A0559718743CE1B1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_get_Current_m16C410B950F9EC9401EB5BD3B0486801464F57CC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t830646EFE3C118AB1C95EA23A0559718743CE1B1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_KeyValuePair_2_t32441F20B8BFB9776199A3EA374BAEA4081E15F4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_KeyValuePair_t2B7EAE6840DB2924E512FA67E009CBDADBE5AFC4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_KeyValuePair_2_get_Key_mD9EA4F93592369501A9FAA3B59B3D550103DA2DF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_KeyValuePair_2_t32441F20B8BFB9776199A3EA374BAEA4081E15F4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TKey_t2B31B8647CA98F2E10EFB3BE12F7625CEB350649 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_KeyValuePair_2_get_Value_m756CBFED9B24A8F6C39A554F1A12A3514DCF9028 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValue_t6CCAE4A07745C46EDF1B499AF922687C1FDE901A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_MoveNext_m686C012F6FDE0C4B812E2D41FDA187DADB884C01 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_Enumerator_t830646EFE3C118AB1C95EA23A0559718743CE1B1_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_Clear_m447D6FB60BDFEB187401990D2E7C23EC7376132D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_Add_m11F68D8B6ADA0C4DB940A648967A8BC846743D2B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2__ctor_m3DB2A4D4728E59376E1525BC6FABF3E648E0A53C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TrackableChanges_1_t5C081C6B9DBE043479246BE8E81EFFF32B96BA44 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tC85A8F25BE7A76576E3D8A4A8D52ED8FFB979765 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1__ctor_mEEEE9171129A3C1275195FD4AD65DAC36115C619 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TrackableChanges_1_set_isCreated_m9A15621EDC9FF64A40D8DF53144E376708C06857 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TrackableChanges_1_t5C081C6B9DBE043479246BE8E81EFFF32B96BA44 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tCAB6448429CF0BAB39FFC61B2580DB460F8E87B9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeCopyUtility_CreateArrayFilledWithValue_TisT_tCAB6448429CF0BAB39FFC61B2580DB460F8E87B9_m29AD4E086A175D6F8C1F808E20DA2D324239C85D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeCopyUtility_PtrToNativeArrayWithDefault_TisT_tCAB6448429CF0BAB39FFC61B2580DB460F8E87B9_m2A676CF290B1B07082D8A7ED0FAE9891B617F520 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Length_m0A9FF440DDDAD682A81FD8EA54F42A1F38342239 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tC85A8F25BE7A76576E3D8A4A8D52ED8FFB979765 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_CopyFrom_m56585E67CCDEBD89C5A62D74B6A0788A47A9325F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TrackableChanges_1__ctor_mA225CE3D2A7CB311847427D7AC63D8B87780A64F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TrackableChanges_1_get_isCreated_mD058CE8ACD0506767BE2E6F309722B98F6727134 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_Dispose_m7B821AE7B2C8B72348DC493B66344C0AFB74B74D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SubsystemWithProvider_3__ctor_m296CFE492E776CDFACE84072212984D479903121 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_SubsystemWithProvider_3_t917622DEF7441E0CF4969E7958A4A855C808A163 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ValidationUtility_1_t9FE82EB474B35DDC09AFFEF97991D887DF9EFC2C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ValidationUtility_1_t9FE82EB474B35DDC09AFFEF97991D887DF9EFC2C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TrackableChanges_1_tD04A53725A94FCDC6C1451CA42F414006596B96E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TrackableChanges_1_get_added_m5730CC78FF0AB0F9237D32BBE517B9FB48006870 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TrackableChanges_1_tD04A53725A94FCDC6C1451CA42F414006596B96E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tE425D0061965BEAD252F4AD4929205E56C736D55 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_GetEnumerator_m5EE50000A53B45D519978B40EF1089DC9778A379 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tE425D0061965BEAD252F4AD4929205E56C736D55 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_tC2736CC8B25AD4EB46B9BD4ADC98BF26C78AE22D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_get_Current_m44563E4727F87D239347259A856F424CD196BC39 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_tC2736CC8B25AD4EB46B9BD4ADC98BF26C78AE22D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t0E25A4B26FF79251CAB9D7C9C4283C339BC1F05F },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t0E25A4B26FF79251CAB9D7C9C4283C339BC1F05F_ITrackable_get_trackableId_m7242DF9928E5787565A8DDE0374D160431569206 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ValidationUtility_1_AddToSetAndThrowIfDuplicate_mA88F08B984E30ACBC0F19E4DDB191C35030FDE4F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_MoveNext_mE57804E5B928D0F19982DD88591477540AD6BB2F },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_Enumerator_tC2736CC8B25AD4EB46B9BD4ADC98BF26C78AE22D_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TrackableChanges_1_get_updated_mE5CAB2DB386973E2CA045267354EF6DAB0F8022F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TrackableChanges_1_get_removed_mE544933CC6E98991F37B0A6129FAAFE1AFE77FC7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ValidationUtility_1_ValidateAndThrow_mE3BAE011CA1B620D0EB696FB44F6B0ECC8F926AD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TrackableChanges_1_Dispose_mCF872D88283DE20661760DE3E1925053F7993A94 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_XRSubsystem_1_t3A3C5F9AAB5BACBFA3B7E29BC6C0981F60BB0FD3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_XRSubsystem_1_OnDestroyed_m1197DB1C252FE0CEC1E1312AA7B2D93487540467 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_XRSubsystem_1_OnStart_m946EED52AFDCB529B3E037B350653CA79A67BDCF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_XRSubsystem_1_OnStop_mCC54FB0F8161B29D486393408C35C213B1AF11FB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Subsystem_1__ctor_m63C695A54B13A98ACB3620B1804E340F3C929EC1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Subsystem_1_t60DB20C2FBF9D6BC149DB549AE1C5813A17AAF38 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_XR_ARSubsystems_CodeGenModule;
const Il2CppCodeGenModule g_Unity_XR_ARSubsystems_CodeGenModule = 
{
	"Unity.XR.ARSubsystems.dll",
	1474,
	s_methodPointers,
	726,
	s_adjustorThunks,
	s_InvokerIndices,
	1,
	s_reversePInvokeIndices,
	21,
	s_rgctxIndices,
	143,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
