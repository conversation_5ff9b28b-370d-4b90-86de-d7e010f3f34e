﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mAE2DDF7FCE8FE1F3660DEB64FBC6BF057F09E861 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mD1FBF1E8C89F62B711476EB16EA754FB281D936B (void);
extern void FindObjectsUtility_FindAnyObjectByType_m0DB6DB459BF52A74B398EB069D81158DEFD84A89 (void);
extern void FindObjectsUtility_FindObjectsByType_m3319ABCA3B46008448BE671C3E04A4E73BA7BED0 (void);
extern void PoseUtils_CalculateOffset_m6879E7AFD770A7788A8229393C573207C757FABF (void);
extern void PoseUtils_WithOffset_mB70A61FE6A3A0379F96848733DF4AFCBA6D6B910 (void);
extern void SerializableGuidUtility_AsSerializedGuid_m357A4BB3D52EC18EB467A58D9C8357C13767171B (void);
static Il2CppMethodPointer s_methodPointers[11] = 
{
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mAE2DDF7FCE8FE1F3660DEB64FBC6BF057F09E861,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mD1FBF1E8C89F62B711476EB16EA754FB281D936B,
	NULL,
	FindObjectsUtility_FindAnyObjectByType_m0DB6DB459BF52A74B398EB069D81158DEFD84A89,
	NULL,
	FindObjectsUtility_FindObjectsByType_m3319ABCA3B46008448BE671C3E04A4E73BA7BED0,
	PoseUtils_CalculateOffset_m6879E7AFD770A7788A8229393C573207C757FABF,
	PoseUtils_WithOffset_mB70A61FE6A3A0379F96848733DF4AFCBA6D6B910,
	SerializableGuidUtility_AsSerializedGuid_m357A4BB3D52EC18EB467A58D9C8357C13767171B,
	NULL,
	NULL,
};
static const int32_t s_InvokerIndices[11] = 
{
	11833,
	7776,
	0,
	11351,
	0,
	11351,
	10535,
	10535,
	11444,
	0,
	0,
};
static const Il2CppTokenRangePair s_rgctxIndices[4] = 
{
	{ 0x06000003, { 0, 2 } },
	{ 0x06000005, { 2, 2 } },
	{ 0x0600000A, { 4, 4 } },
	{ 0x0600000B, { 8, 3 } },
};
extern const uint32_t g_rgctx_Object_FindObjectOfType_TisT_tEFC74842AD79F696D304635BC0BCF8AEA32E49AD_mDD7E494BBC0E054323353CDDC64A5CC6D194A7EC;
extern const uint32_t g_rgctx_T_tEFC74842AD79F696D304635BC0BCF8AEA32E49AD;
extern const uint32_t g_rgctx_Object_FindObjectsOfType_TisT_tABA244D670AAB6CE1BA83DE404F13DE3F8BE2F7E_m781FF4481CB052B026C33AD72AA3B4CBE8566717;
extern const uint32_t g_rgctx_TU5BU5D_t10446CA40F85BC2ED3D7F43B61320F81A9964FD9;
extern const uint32_t g_rgctx_TSubsystemConcreteU26_t47345D2BF1D6F633DCC9DAB3171F58344BA7CC37;
extern const uint32_t g_rgctx_TSubsystemConcrete_t3B4DDB472672601F08465F39271AAB39834CE9AA;
extern const uint32_t g_rgctx_TSubsystemBase_tC4E2E233093789D3B4FB20D6AD6EBCB4D924E3A4;
extern const uint32_t g_rgctx_XRLoader_GetLoadedSubsystem_TisTSubsystemBase_tC4E2E233093789D3B4FB20D6AD6EBCB4D924E3A4_mCFC9C83D0BFD50EC888291AA7F1457D0433E1FAC;
extern const uint32_t g_rgctx_Enumerable_OfType_TisTLoader_t26003C476ADF8CC1F57374FEB8D0232AC674F370_m3B40B29D492D50ADA99283ED19EAFA3EFBD32557;
extern const uint32_t g_rgctx_IEnumerable_1_t5DBAD665E2F5E5FD9B0FA190A4617BAD6DF37305;
extern const uint32_t g_rgctx_Enumerable_Any_TisTLoader_t26003C476ADF8CC1F57374FEB8D0232AC674F370_m2D9269350ED592D8B5D1F21272BB5CB029FD4B8D;
static const Il2CppRGCTXDefinition s_rgctxValues[11] = 
{
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Object_FindObjectOfType_TisT_tEFC74842AD79F696D304635BC0BCF8AEA32E49AD_mDD7E494BBC0E054323353CDDC64A5CC6D194A7EC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tEFC74842AD79F696D304635BC0BCF8AEA32E49AD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Object_FindObjectsOfType_TisT_tABA244D670AAB6CE1BA83DE404F13DE3F8BE2F7E_m781FF4481CB052B026C33AD72AA3B4CBE8566717 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t10446CA40F85BC2ED3D7F43B61320F81A9964FD9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TSubsystemConcreteU26_t47345D2BF1D6F633DCC9DAB3171F58344BA7CC37 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TSubsystemConcrete_t3B4DDB472672601F08465F39271AAB39834CE9AA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TSubsystemBase_tC4E2E233093789D3B4FB20D6AD6EBCB4D924E3A4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_XRLoader_GetLoadedSubsystem_TisTSubsystemBase_tC4E2E233093789D3B4FB20D6AD6EBCB4D924E3A4_mCFC9C83D0BFD50EC888291AA7F1457D0433E1FAC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerable_OfType_TisTLoader_t26003C476ADF8CC1F57374FEB8D0232AC674F370_m3B40B29D492D50ADA99283ED19EAFA3EFBD32557 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t5DBAD665E2F5E5FD9B0FA190A4617BAD6DF37305 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerable_Any_TisTLoader_t26003C476ADF8CC1F57374FEB8D0232AC674F370_m2D9269350ED592D8B5D1F21272BB5CB029FD4B8D },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_XR_ARFoundation_InternalUtils_CodeGenModule;
const Il2CppCodeGenModule g_Unity_XR_ARFoundation_InternalUtils_CodeGenModule = 
{
	"Unity.XR.ARFoundation.InternalUtils.dll",
	11,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	4,
	s_rgctxIndices,
	11,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
