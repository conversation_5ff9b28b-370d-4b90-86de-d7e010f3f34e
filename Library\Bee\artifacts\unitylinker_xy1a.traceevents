{ "pid": 6688, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "<PERSON><PERSON>ink<PERSON>" } },
{ "pid": 6688, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "0" } },
{ "pid": 6688, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen2" } },
{ "pid": 6688, "tid": 12884901888, "ts": 1751453318729106, "dur": 2320, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "LOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 12884901888, "ts": 1751453318763499, "dur": 2658, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "LOH allocation", "Type": "Background GC"} },
{ "pid": 6688, "tid": 12884901888, "ts": 1751453319004954, "dur": 8659, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "SOH allocation", "Type": "Background GC"} },
{ "pid": 6688, "tid": 12884901888, "ts": 1751453319109084, "dur": 34761, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "SOH allocation", "Type": "Background GC"} },
{ "pid": 6688, "tid": 12884901888, "ts": 1751453319277524, "dur": 84420, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "SOH allocation", "Type": "Background GC"} },
{ "pid": 6688, "tid": 12884901888, "ts": 1751453320850698, "dur": 101056, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "SOH allocation", "Type": "Background GC"} },
{ "pid": 6688, "tid": 12884901888, "ts": 1751453323266494, "dur": 110363, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "SOH allocation", "Type": "Background GC"} },
{ "pid": 6688, "tid": 1, "ts": 1751453323384144, "dur": 2285, "ph": "X", "name": "GC - Gen2", "args": {} },
{ "pid": 6688, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen1" } },
{ "pid": 6688, "tid": 8589934592, "ts": 1751453318892841, "dur": 4092, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 8589934592, "ts": 1751453319031124, "dur": 9028, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 8589934592, "ts": 1751453319109086, "dur": 17412, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 8589934592, "ts": 1751453319177680, "dur": 16782, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 8589934592, "ts": 1751453319227196, "dur": 19759, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 8589934592, "ts": 1751453319277525, "dur": 19185, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 8589934592, "ts": 1751453319424558, "dur": 49526, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 8589934592, "ts": 1751453319573500, "dur": 27039, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 8589934592, "ts": 1751453319703387, "dur": 49456, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 8589934592, "ts": 1751453319813992, "dur": 13731, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 8589934592, "ts": 1751453319863253, "dur": 16090, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 8589934592, "ts": 1751453319929372, "dur": 6581, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 8589934592, "ts": 1751453319978401, "dur": 10723, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 8589934592, "ts": 1751453320250279, "dur": 4820, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 8589934592, "ts": 1751453320295101, "dur": 1007, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 8589934592, "ts": 1751453320349032, "dur": 5740, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 8589934592, "ts": 1751453320396647, "dur": 4812, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 8589934592, "ts": 1751453320635211, "dur": 7486, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 8589934592, "ts": 1751453320746558, "dur": 11538, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 8589934592, "ts": 1751453320850700, "dur": 12160, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 8589934592, "ts": 1751453321071079, "dur": 16195, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 8589934592, "ts": 1751453321243416, "dur": 38247, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 8589934592, "ts": 1751453321364187, "dur": 18385, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 8589934592, "ts": 1751453321422908, "dur": 14432, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 8589934592, "ts": 1751453321511056, "dur": 16433, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 8589934592, "ts": 1751453321692178, "dur": 30360, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 8589934592, "ts": 1751453321879413, "dur": 16276, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 8589934592, "ts": 1751453322054911, "dur": 43061, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 8589934592, "ts": 1751453322260102, "dur": 16353, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 8589934592, "ts": 1751453322440137, "dur": 18292, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 8589934592, "ts": 1751453322737691, "dur": 31716, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 8589934592, "ts": 1751453322914950, "dur": 18434, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 8589934592, "ts": 1751453323091038, "dur": 18003, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 1, "ts": 1751453323386437, "dur": 112, "ph": "X", "name": "GC - Gen1", "args": {} },
{ "pid": 6688, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen0" } },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453318772747, "dur": 1744, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453318941633, "dur": 4138, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453318968229, "dur": 1316, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453319004955, "dur": 1894, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453319089424, "dur": 6903, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453319155444, "dur": 6983, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453319207241, "dur": 7495, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453319259135, "dur": 6330, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453319382295, "dur": 6885, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453319493745, "dur": 22429, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453319527564, "dur": 33901, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453319651252, "dur": 25690, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453319772070, "dur": 12321, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453319843150, "dur": 8998, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453319905514, "dur": 2278, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453319919848, "dur": 815, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453319943839, "dur": 11682, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453319963832, "dur": 6398, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453320000334, "dur": 101, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453320046527, "dur": 955, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453320220668, "dur": 4742, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453320237277, "dur": 2081, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453320269522, "dur": 595, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453320281681, "dur": 415, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453320308576, "dur": 1663, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453320324611, "dur": 1038, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453320367117, "dur": 910, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453320421136, "dur": 2650, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453320566672, "dur": 7391, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453320697146, "dur": 11807, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453320798346, "dur": 12282, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453320983510, "dur": 6014, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453321024448, "dur": 10827, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453321134555, "dur": 11748, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453321190026, "dur": 4865, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453321320557, "dur": 9153, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453321487497, "dur": 10024, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453321591204, "dur": 10295, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453321645663, "dur": 6138, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453321768030, "dur": 5377, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453321819050, "dur": 14749, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453321952253, "dur": 13975, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453322002205, "dur": 6392, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453322139544, "dur": 4880, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453322188616, "dur": 12160, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453322362781, "dur": 10078, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453322635643, "dur": 19229, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453322802832, "dur": 12678, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453322867162, "dur": 11555, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453322987291, "dur": 8378, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453323053758, "dur": 7717, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453323230093, "dur": 6293, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 4294967296, "ts": 1751453323266508, "dur": 14061, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 6688, "tid": 1, "ts": 1751453323386550, "dur": 81, "ph": "X", "name": "GC - Gen0", "args": {} },
{ "pid": 6688, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 6688, "tid": 1, "ts": 1751453318542919, "dur": 4819249, "ph": "X", "name": "UnityLinker.exe", "args": {"analytics": "1"} },
{ "pid": 6688, "tid": 1, "ts": 1751453318544616, "dur": 51752, "ph": "X", "name": "InitAndSetup", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453318549305, "dur": 25201, "ph": "X", "name": "PrepareInstances", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453318596370, "dur": 11559, "ph": "X", "name": "RegisterRuntimeEventListeners", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453318631359, "dur": 27069, "ph": "X", "name": "ParseArguments", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453318660666, "dur": 95934, "ph": "X", "name": "SetupAndRegisterUnityEngineSteps", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453318756629, "dur": 36782, "ph": "X", "name": "ResolveUnityEngine", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453318793422, "dur": 30081, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453318823512, "dur": 127712, "ph": "X", "name": "InitializeEngineStrippingStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453318951232, "dur": 3863, "ph": "X", "name": "ResolveForEngineModuleStrippingEnabledStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453318955101, "dur": 14066, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453318969174, "dur": 866, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453318970042, "dur": 2772, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453318972816, "dur": 187, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453318973006, "dur": 11827, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453318984837, "dur": 451, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453318985290, "dur": 425, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453318985717, "dur": 427, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453318986146, "dur": 565, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453318986713, "dur": 466, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453318987181, "dur": 471, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453318987659, "dur": 7503, "ph": "X", "name": "SetupAndRegisterUnityRootsSteps", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453318995173, "dur": 7557, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453319002732, "dur": 330, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453319003063, "dur": 272, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453319003337, "dur": 2773, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453319006115, "dur": 6301, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453319012421, "dur": 983, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453319013405, "dur": 645, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453319014052, "dur": 917, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453319014970, "dur": 460, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453319015431, "dur": 3724, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453319019157, "dur": 1116, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453319020280, "dur": 3504, "ph": "X", "name": "UnityLoadReferencesStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453319023792, "dur": 1666, "ph": "X", "name": "SetupAndRegisterUnityRootsSteps2", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453319025463, "dur": 818, "ph": "X", "name": "SetupI18N", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453319026285, "dur": 13118, "ph": "X", "name": "ResolveFromDescriptorsStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453319039414, "dur": 139, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453319039553, "dur": 147, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453319039701, "dur": 3346, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453319043049, "dur": 1217, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453319044268, "dur": 633, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453319044906, "dur": 17595, "ph": "X", "name": "UnityBlacklistStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453319062506, "dur": 7096, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453319069611, "dur": 322936, "ph": "X", "name": "DynamicDependencyLookupStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453319392558, "dur": 385, "ph": "X", "name": "EarlyReportGenerationStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453319392953, "dur": 527, "ph": "X", "name": "ResolveTestsStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453319393486, "dur": 270, "ph": "X", "name": "LoadI18nAssemblies", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453319393761, "dur": 887, "ph": "X", "name": "ResolveMonoBehaviourItselfStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453319394654, "dur": 644, "ph": "X", "name": "ResolveFromAllUserMonoBehaviours", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453319395304, "dur": 4149, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453319399455, "dur": 189, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453319399645, "dur": 241, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453319399887, "dur": 17, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453319399904, "dur": 139, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453319400044, "dur": 85, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453319400133, "dur": 200656, "ph": "X", "name": "ResolveFromPreserveAttribute", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453319600812, "dur": 12613, "ph": "X", "name": "EngineStrippingAnnotationStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453319613435, "dur": 274853, "ph": "X", "name": "UnityTypeMapStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453319888299, "dur": 211, "ph": "X", "name": "BeforeMarkReportGenerationStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453319888516, "dur": 120213, "ph": "X", "name": "BeforeMarkAnalyticsStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453320008741, "dur": 21680, "ph": "X", "name": "RemoveSecurityStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453320030432, "dur": 6588, "ph": "X", "name": "RemoveSecurityFromCopyAssemblies", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453320037028, "dur": 17370, "ph": "X", "name": "RemoveFeaturesStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453320054408, "dur": 16787, "ph": "X", "name": "RemoveUnreachableBlocksStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453320071205, "dur": 2256702, "ph": "X", "name": "UnityMarkStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453322327916, "dur": 716, "ph": "X", "name": "ValidateVirtualMethodAnnotationsStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453322328637, "dur": 5603, "ph": "X", "name": "ProcessWarningsStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453322334248, "dur": 69107, "ph": "X", "name": "UnitySweepStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453322403365, "dur": 4070, "ph": "X", "name": "UnityCodeRewriterStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453322407441, "dur": 4127, "ph": "X", "name": "CleanStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453322411574, "dur": 2540, "ph": "X", "name": "StubifyStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453322414119, "dur": 2611, "ph": "X", "name": "AddUnresolvedStubsStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453322416734, "dur": 3574, "ph": "X", "name": "BeforeOutputAnalyticsStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453322420315, "dur": 163, "ph": "X", "name": "BeforeOutputReportGenerationStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453322420484, "dur": 381, "ph": "X", "name": "ProcessCopiedAssembliesRequiringSecurityAttributesRemoved", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453322420868, "dur": 428, "ph": "X", "name": "SealerStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453322421300, "dur": 904875, "ph": "X", "name": "UnityOutputStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453323326188, "dur": 20198, "ph": "X", "name": "LinkerToEditorDataGenerationStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453323346402, "dur": 262, "ph": "X", "name": "ReportGenerationStep", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453323348973, "dur": 13066, "ph": "X", "name": "Analytics", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453323362170, "dur": 14689, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453323386632, "dur": 34, "ph": "X", "name": "", "args": {} },
{ "pid": 6688, "tid": 1, "ts": 1751453323383664, "dur": 3283, "ph": "X", "name": "Write chrome-trace events", "args": {} },
