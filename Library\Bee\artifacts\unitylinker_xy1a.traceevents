{ "pid": 19488, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "<PERSON><PERSON><PERSON><PERSON>" } },
{ "pid": 19488, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "0" } },
{ "pid": 19488, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen2" } },
{ "pid": 19488, "tid": 12884901888, "ts": 1751450383014298, "dur": 2269, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "LOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 12884901888, "ts": 1751450383048193, "dur": 2737, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "LOH allocation", "Type": "Background GC"} },
{ "pid": 19488, "tid": 12884901888, "ts": 1751450383354480, "dur": 8882, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "SOH allocation", "Type": "Background GC"} },
{ "pid": 19488, "tid": 12884901888, "ts": 1751450383467920, "dur": 33920, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "SOH allocation", "Type": "Background GC"} },
{ "pid": 19488, "tid": 12884901888, "ts": 1751450383649455, "dur": 85186, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "SOH allocation", "Type": "Background GC"} },
{ "pid": 19488, "tid": 12884901888, "ts": 1751450385205676, "dur": 97902, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "SOH allocation", "Type": "Background GC"} },
{ "pid": 19488, "tid": 12884901888, "ts": 1751450387530964, "dur": 137681, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "SOH allocation", "Type": "Background GC"} },
{ "pid": 19488, "tid": 1, "ts": 1751450387675602, "dur": 2174, "ph": "X", "name": "GC - Gen2", "args": {} },
{ "pid": 19488, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen1" } },
{ "pid": 19488, "tid": 8589934592, "ts": 1751450383214677, "dur": 4014, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 8589934592, "ts": 1751450383391390, "dur": 8991, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 8589934592, "ts": 1751450383467921, "dur": 15261, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 8589934592, "ts": 1751450383548805, "dur": 16827, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 8589934592, "ts": 1751450383600213, "dur": 18773, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 8589934592, "ts": 1751450383649457, "dur": 18504, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 8589934592, "ts": 1751450383794133, "dur": 51951, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 8589934592, "ts": 1751450383943174, "dur": 27029, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 8589934592, "ts": 1751450384071930, "dur": 44976, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 8589934592, "ts": 1751450384175569, "dur": 12338, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 8589934592, "ts": 1751450384224420, "dur": 14777, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 8589934592, "ts": 1751450384290294, "dur": 6650, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 8589934592, "ts": 1751450384338688, "dur": 10942, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 8589934592, "ts": 1751450384611848, "dur": 4964, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 8589934592, "ts": 1751450384656968, "dur": 1016, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 8589934592, "ts": 1751450384712572, "dur": 5987, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 8589934592, "ts": 1751450384760527, "dur": 4658, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 8589934592, "ts": 1751450384997627, "dur": 7173, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 8589934592, "ts": 1751450385104406, "dur": 11488, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 8589934592, "ts": 1751450385205678, "dur": 12314, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 8589934592, "ts": 1751450385425558, "dur": 15854, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 8589934592, "ts": 1751450385594711, "dur": 37292, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 8589934592, "ts": 1751450385712381, "dur": 18092, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 8589934592, "ts": 1751450385770511, "dur": 16221, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 8589934592, "ts": 1751450385863839, "dur": 16168, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 8589934592, "ts": 1751450386041421, "dur": 27940, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 8589934592, "ts": 1751450386220517, "dur": 15596, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 8589934592, "ts": 1751450386388791, "dur": 44605, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 8589934592, "ts": 1751450386587117, "dur": 15481, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 8589934592, "ts": 1751450386773618, "dur": 16437, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 8589934592, "ts": 1751450387040722, "dur": 30140, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 8589934592, "ts": 1751450387210352, "dur": 16819, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 8589934592, "ts": 1751450387378796, "dur": 17016, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 1, "ts": 1751450387677782, "dur": 102, "ph": "X", "name": "GC - Gen1", "args": {} },
{ "pid": 19488, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen0" } },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450383057445, "dur": 1636, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450383264466, "dur": 4192, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450383300822, "dur": 1515, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450383354482, "dur": 1905, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450383449313, "dur": 5996, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450383526677, "dur": 6553, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450383579231, "dur": 7587, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450383630829, "dur": 6492, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450383752755, "dur": 7853, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450383864961, "dur": 22963, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450383898729, "dur": 32495, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450384020802, "dur": 24394, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450384135376, "dur": 10695, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450384203837, "dur": 9288, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450384265861, "dur": 2294, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450384280378, "dur": 901, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450384305010, "dur": 11798, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450384324309, "dur": 6471, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450384360375, "dur": 89, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450384406682, "dur": 1186, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450384582172, "dur": 3598, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450384598246, "dur": 2269, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450384631131, "dur": 576, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450384643400, "dur": 398, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450384670555, "dur": 2489, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450384687363, "dur": 1034, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450384730732, "dur": 918, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450384784662, "dur": 2374, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450384930990, "dur": 7117, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450385055997, "dur": 11934, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450385155289, "dur": 12088, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450385338684, "dur": 6123, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450385379113, "dur": 11142, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450385488990, "dur": 11769, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450385541633, "dur": 4655, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450385669845, "dur": 9682, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450385840673, "dur": 9612, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450385943690, "dur": 8962, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450385995578, "dur": 5964, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450386115256, "dur": 5200, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450386165657, "dur": 12050, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450386289645, "dur": 13101, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450386337656, "dur": 6505, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450386477695, "dur": 4868, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450386520169, "dur": 11776, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450386685694, "dur": 8051, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450386949063, "dur": 19706, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450387102932, "dur": 11906, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450387164407, "dur": 10412, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450387279116, "dur": 8076, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450387342501, "dur": 7476, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450387493252, "dur": 6594, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 4294967296, "ts": 1751450387530976, "dur": 12947, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 19488, "tid": 1, "ts": 1751450387677885, "dur": 56, "ph": "X", "name": "GC - Gen0", "args": {} },
{ "pid": 19488, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 19488, "tid": 1, "ts": 1751450382832827, "dur": 4811243, "ph": "X", "name": "UnityLinker.exe", "args": {"analytics": "1"} },
{ "pid": 19488, "tid": 1, "ts": 1751450382834496, "dur": 49069, "ph": "X", "name": "InitAndSetup", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450382839033, "dur": 23982, "ph": "X", "name": "PrepareInstances", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450382883567, "dur": 11585, "ph": "X", "name": "RegisterRuntimeEventListeners", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450382918467, "dur": 27135, "ph": "X", "name": "ParseArguments", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450382947819, "dur": 94051, "ph": "X", "name": "SetupAndRegisterUnityEngineSteps", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383041898, "dur": 35197, "ph": "X", "name": "ResolveUnityEngine", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383077106, "dur": 68661, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383145776, "dur": 129070, "ph": "X", "name": "InitializeEngineStrippingStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383274856, "dur": 4000, "ph": "X", "name": "ResolveForEngineModuleStrippingEnabledStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383278861, "dur": 23763, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383302632, "dur": 959, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383303594, "dur": 3013, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383306610, "dur": 197, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383306812, "dur": 22094, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383328910, "dur": 448, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383329361, "dur": 404, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383329766, "dur": 414, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383330182, "dur": 532, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383330716, "dur": 507, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383331224, "dur": 449, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383331682, "dur": 7998, "ph": "X", "name": "SetupAndRegisterUnityRootsSteps", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383339688, "dur": 13051, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383352743, "dur": 446, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383353191, "dur": 286, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383353478, "dur": 2804, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383356288, "dur": 6494, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383362786, "dur": 1102, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383363890, "dur": 750, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383364641, "dur": 968, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383365611, "dur": 481, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383366093, "dur": 3838, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383369932, "dur": 762, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383370699, "dur": 13748, "ph": "X", "name": "UnityLoadReferencesStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383384456, "dur": 1758, "ph": "X", "name": "SetupAndRegisterUnityRootsSteps2", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383386219, "dur": 835, "ph": "X", "name": "SetupI18N", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383387058, "dur": 13137, "ph": "X", "name": "ResolveFromDescriptorsStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383400203, "dur": 126, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383400331, "dur": 151, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383400483, "dur": 3325, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383403809, "dur": 1213, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383405024, "dur": 623, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383405651, "dur": 17391, "ph": "X", "name": "UnityBlacklistStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383423045, "dur": 6661, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383429712, "dur": 334133, "ph": "X", "name": "DynamicDependencyLookupStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383763857, "dur": 389, "ph": "X", "name": "EarlyReportGenerationStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383764255, "dur": 536, "ph": "X", "name": "ResolveTestsStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383764797, "dur": 251, "ph": "X", "name": "LoadI18nAssemblies", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383765053, "dur": 895, "ph": "X", "name": "ResolveMonoBehaviourItselfStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383765959, "dur": 641, "ph": "X", "name": "ResolveFromAllUserMonoBehaviours", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383766605, "dur": 4183, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383770791, "dur": 182, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383770975, "dur": 226, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383771202, "dur": 15, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383771217, "dur": 137, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383771355, "dur": 83, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383771442, "dur": 199761, "ph": "X", "name": "ResolveFromPreserveAttribute", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383971220, "dur": 12826, "ph": "X", "name": "EngineStrippingAnnotationStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450383984058, "dur": 264998, "ph": "X", "name": "UnityTypeMapStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450384249069, "dur": 201, "ph": "X", "name": "BeforeMarkReportGenerationStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450384249274, "dur": 120091, "ph": "X", "name": "BeforeMarkAnalyticsStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450384369377, "dur": 21937, "ph": "X", "name": "RemoveSecurityStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450384391326, "dur": 6665, "ph": "X", "name": "RemoveSecurityFromCopyAssemblies", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450384397998, "dur": 18428, "ph": "X", "name": "RemoveFeaturesStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450384416439, "dur": 16672, "ph": "X", "name": "RemoveUnreachableBlocksStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450384433120, "dur": 2219527, "ph": "X", "name": "UnityMarkStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450386652659, "dur": 719, "ph": "X", "name": "ValidateVirtualMethodAnnotationsStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450386653383, "dur": 5187, "ph": "X", "name": "ProcessWarningsStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450386658574, "dur": 65546, "ph": "X", "name": "UnitySweepStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450386724130, "dur": 3611, "ph": "X", "name": "UnityCodeRewriterStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450386727746, "dur": 3936, "ph": "X", "name": "CleanStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450386731687, "dur": 2447, "ph": "X", "name": "StubifyStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450386734138, "dur": 2515, "ph": "X", "name": "AddUnresolvedStubsStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450386736658, "dur": 3361, "ph": "X", "name": "BeforeOutputAnalyticsStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450386740024, "dur": 165, "ph": "X", "name": "BeforeOutputReportGenerationStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450386740195, "dur": 369, "ph": "X", "name": "ProcessCopiedAssembliesRequiringSecurityAttributesRemoved", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450386740569, "dur": 407, "ph": "X", "name": "SealerStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450386740980, "dur": 858684, "ph": "X", "name": "UnityOutputStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450387599677, "dur": 14266, "ph": "X", "name": "LinkerToEditorDataGenerationStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450387613951, "dur": 200, "ph": "X", "name": "ReportGenerationStep", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450387616353, "dur": 27592, "ph": "X", "name": "Analytics", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450387644072, "dur": 24574, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450387677942, "dur": 68, "ph": "X", "name": "", "args": {} },
{ "pid": 19488, "tid": 1, "ts": 1751450387675168, "dur": 3109, "ph": "X", "name": "Write chrome-trace events", "args": {} },
