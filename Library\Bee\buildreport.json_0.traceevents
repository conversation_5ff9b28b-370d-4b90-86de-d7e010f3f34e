{ "pid": 20040, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 20040, "tid": 1, "ts": 1751453314877911, "dur": 12923, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 20040, "tid": 1, "ts": 1751453314890839, "dur": 63662, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 20040, "tid": 1, "ts": 1751453314954509, "dur": 3050, "ph": "X", "name": "Write<PERSON><PERSON>", "args": {} },
{ "pid": 20040, "tid": 288658, "ts": 1751453315588297, "dur": 1393, "ph": "X", "name": "", "args": {} },
{ "pid": 20040, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 20040, "tid": 12884901888, "ts": 1751453314875793, "dur": 8267, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 20040, "tid": 12884901888, "ts": 1751453314884063, "dur": 694665, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 20040, "tid": 12884901888, "ts": 1751453314890061, "dur": 2613, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 20040, "tid": 12884901888, "ts": 1751453314892680, "dur": 2254, "ph": "X", "name": "ProcessMessages 567", "args": {} },
{ "pid": 20040, "tid": 12884901888, "ts": 1751453314894939, "dur": 663866, "ph": "X", "name": "ReadAsync 567", "args": {} },
{ "pid": 20040, "tid": 12884901888, "ts": 1751453315558814, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 20040, "tid": 12884901888, "ts": 1751453315558816, "dur": 336, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 12884901888, "ts": 1751453315559155, "dur": 2174, "ph": "X", "name": "ProcessMessages 12745", "args": {} },
{ "pid": 20040, "tid": 12884901888, "ts": 1751453315561334, "dur": 37, "ph": "X", "name": "ReadAsync 12745", "args": {} },
{ "pid": 20040, "tid": 12884901888, "ts": 1751453315561374, "dur": 200, "ph": "X", "name": "ProcessMessages 41", "args": {} },
{ "pid": 20040, "tid": 12884901888, "ts": 1751453315561577, "dur": 17091, "ph": "X", "name": "ReadAsync 41", "args": {} },
{ "pid": 20040, "tid": 288658, "ts": 1751453315589694, "dur": 19, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 20040, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 20040, "tid": 8589934592, "ts": 1751453314872437, "dur": 85188, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 20040, "tid": 8589934592, "ts": 1751453314957627, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 20040, "tid": 8589934592, "ts": 1751453314957631, "dur": 1081, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 20040, "tid": 288658, "ts": 1751453315589714, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 20040, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 20040, "tid": 4294967296, "ts": 1751453314856768, "dur": 722957, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 20040, "tid": 4294967296, "ts": 1751453314861177, "dur": 4988, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 20040, "tid": 4294967296, "ts": 1751453315579741, "dur": 3898, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 20040, "tid": 4294967296, "ts": 1751453315582074, "dur": 83, "ph": "X", "name": "await ScriptUpdaters", "args": {} },
{ "pid": 20040, "tid": 4294967296, "ts": 1751453315583733, "dur": 17, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 20040, "tid": 288658, "ts": 1751453315589720, "dur": 7, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1751453314881372, "dur":1757, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751453314883144, "dur":395, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751453314883558, "dur":460, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751453314884070, "dur":675476, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751453315559547, "dur":182, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751453315559729, "dur":179, "ph":"X", "name": "ThreadStateDestroy",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751453315560073, "dur":14544, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1751453314884090, "dur":74111, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751453314958203, "dur":1948, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751453314960151, "dur":599400, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751453314884108, "dur":675437, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751453314884089, "dur":61, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe" }}
,{ "pid":12345, "tid":3, "ts":1751453314884224, "dur":163, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":3, "ts":1751453314884080, "dur":571, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":3, "ts":1751453314884998, "dur":674177, "ph":"X", "name": "BuildPlayerDataGenerator",  "args": { "detail":"Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":4, "ts":1751453314884099, "dur":76056, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751453314960155, "dur":599392, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751453314884133, "dur":675416, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751453314884164, "dur":675379, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751453314884190, "dur":675533, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751453314884213, "dur":675503, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751453314884237, "dur":675362, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751453314884265, "dur":675447, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751453314884291, "dur":675309, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751453314884316, "dur":675372, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751453314884334, "dur":675371, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751453314884359, "dur":675339, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751453314884396, "dur":675167, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751453314884416, "dur":675266, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751453314884451, "dur":675152, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751453314884471, "dur":675086, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751453314884501, "dur":675097, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751453314884523, "dur":675078, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751453315577499, "dur":262, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 20040, "tid": 288658, "ts": 1751453315590118, "dur": 38155, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 20040, "tid": 288658, "ts": 1751453315628316, "dur": 1647, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 20040, "tid": 288658, "ts": 1751453315587625, "dur": 43141, "ph": "X", "name": "Write chrome-trace events", "args": {} },
