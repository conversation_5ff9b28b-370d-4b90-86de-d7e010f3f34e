{ "pid": 20040, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 20040, "tid": 1, "ts": 1751450377975342, "dur": 9438, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 20040, "tid": 1, "ts": 1751450377984783, "dur": 67348, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 20040, "tid": 1, "ts": 1751450378052142, "dur": 2881, "ph": "X", "name": "Write<PERSON><PERSON>", "args": {} },
{ "pid": 20040, "tid": 19557, "ts": 1751450378679713, "dur": 1264, "ph": "X", "name": "", "args": {} },
{ "pid": 20040, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 20040, "tid": 12884901888, "ts": 1751450377973716, "dur": 9484, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 20040, "tid": 12884901888, "ts": 1751450377983202, "dur": 687517, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 20040, "tid": 12884901888, "ts": 1751450377984051, "dur": 2747, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 20040, "tid": 12884901888, "ts": 1751450377986803, "dur": 2280, "ph": "X", "name": "ProcessMessages 567", "args": {} },
{ "pid": 20040, "tid": 12884901888, "ts": 1751450377989086, "dur": 661736, "ph": "X", "name": "ReadAsync 567", "args": {} },
{ "pid": 20040, "tid": 12884901888, "ts": 1751450378650830, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 20040, "tid": 12884901888, "ts": 1751450378650833, "dur": 95, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 12884901888, "ts": 1751450378650932, "dur": 2180, "ph": "X", "name": "ProcessMessages 12745", "args": {} },
{ "pid": 20040, "tid": 12884901888, "ts": 1751450378653115, "dur": 67, "ph": "X", "name": "ReadAsync 12745", "args": {} },
{ "pid": 20040, "tid": 12884901888, "ts": 1751450378653186, "dur": 342, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 20040, "tid": 12884901888, "ts": 1751450378653533, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 12884901888, "ts": 1751450378653549, "dur": 210, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 20040, "tid": 12884901888, "ts": 1751450378653762, "dur": 16913, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 20040, "tid": 19557, "ts": 1751450378680981, "dur": 21, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 20040, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 20040, "tid": 8589934592, "ts": 1751450377971264, "dur": 83796, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 20040, "tid": 8589934592, "ts": 1751450378055062, "dur": 3, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 20040, "tid": 8589934592, "ts": 1751450378055066, "dur": 1099, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 20040, "tid": 19557, "ts": 1751450378681004, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 20040, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 20040, "tid": 4294967296, "ts": 1751450377954111, "dur": 717473, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 20040, "tid": 4294967296, "ts": 1751450377961068, "dur": 4716, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 20040, "tid": 4294967296, "ts": 1751450378671597, "dur": 3820, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 20040, "tid": 4294967296, "ts": 1751450378674015, "dur": 80, "ph": "X", "name": "await ScriptUpdaters", "args": {} },
{ "pid": 20040, "tid": 4294967296, "ts": 1751450378675495, "dur": 11, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 20040, "tid": 19557, "ts": 1751450378681010, "dur": 6, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1751450377982031, "dur":1720, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751450377983760, "dur":403, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751450377984183, "dur":440, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751450377984673, "dur":668704, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751450378653378, "dur":610, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751450378653995, "dur":119, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751450378654129, "dur":83, "ph":"X", "name": "ThreadStateDestroy",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751450378654338, "dur":14216, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1751450377984640, "dur":70, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751450377984720, "dur":72351, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751450378057072, "dur":1516, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751450378058589, "dur":594917, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751450377984745, "dur":668633, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751450377985607, "dur":667844, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751450377984738, "dur":73886, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751450378058625, "dur":594756, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751450377984797, "dur":668583, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751450377984928, "dur":668522, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751450377984864, "dur":668511, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751450377985092, "dur":668393, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751450377984950, "dur":668502, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751450377985122, "dur":668370, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751450377985220, "dur":668297, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751450377985150, "dur":668349, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751450377985276, "dur":668201, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751450377985410, "dur":667974, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751450377985305, "dur":668081, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751450377985328, "dur":668054, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751450377985383, "dur":668064, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751450377985521, "dur":667989, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751450377985582, "dur":667867, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751450377984687, "dur":81, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe" }}
,{ "pid":12345, "tid":20, "ts":1751450377984769, "dur":58, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\BuildPlayerDataGenerator\\BuildPlayerDataGenerator.exe" }}
,{ "pid":12345, "tid":20, "ts":1751450377984853, "dur":145, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":20, "ts":1751450377984679, "dur":585, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":20, "ts":1751450377985571, "dur":667454, "ph":"X", "name": "BuildPlayerDataGenerator",  "args": { "detail":"Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":0, "ts":1751450378671062, "dur":258, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 20040, "tid": 19557, "ts": 1751450378681388, "dur": 1528, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 20040, "tid": 19557, "ts": 1751450378682953, "dur": 1393, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 20040, "tid": 19557, "ts": 1751450378678955, "dur": 6133, "ph": "X", "name": "Write chrome-trace events", "args": {} },
