﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"


extern const RuntimeMethod* ARCoreProvider_CameraPermissionRequestProvider_m19E1C66D509D30992AD54875823B7257595D9B0C_RuntimeMethod_var;
extern const RuntimeMethod* ARCoreProvider_GenerateGuid_m7C5047E30C06237E3F8A86CE2F29CF71DE056DA0_RuntimeMethod_var;
extern const RuntimeMethod* ARCoreProvider_GenerateGuid_mEFBF2DA86959890B2BFF0479F6D989E0A03E64D3_RuntimeMethod_var;
extern const RuntimeMethod* ARCoreProvider_OnApkInstallation_mCF471D5BC5E1FE4740696294320C033495CF3274_RuntimeMethod_var;
extern const RuntimeMethod* ARCoreProvider_OnBeforeGetCameraConfiguration_mA33A6474A489CA38E45F609013293D679AF4D06A_RuntimeMethod_var;
extern const RuntimeMethod* ARCoreProvider_OnCheckApkAvailability_mBEC148833875195FFBB34791C412F2DBA1BC2009_RuntimeMethod_var;
extern const RuntimeMethod* ARCoreProvider_SetConfigurationCallback_mF0CB81541E8AED4D6D8C13EEFC28F02C044632BA_RuntimeMethod_var;



extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m6F15F3BD33B7D2DEBB889CBB6198147913047B69 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m19B5A5F70EE46E88531B4A04E6B903E9F976ABB8 (void);
extern void ArCameraConfig__ctor_m03B7CCBE0A6A9F8C9CF14EBBCB1517566DA47015 (void);
extern void ArCameraConfig_FromIntPtr_mBCBEFFFEAEBB82AD1AFDDDA8B450F161938A83F8 (void);
extern void ArCameraConfig_get_Null_m24A947EA533C02BC3AE925CDE05BDF0748B053D2 (void);
extern void ArCameraConfig_get_IsNull_m0E09F4D2B8C7F7159FBF65FCA7486A8C79741ED6 (void);
extern void ArCameraConfig_AsIntPtr_m319C668BCFE412C0D53FF26D97EDB93249B39444 (void);
extern void ArCameraConfig__ctor_mE14FEFC3215FE2A052D06E5A43C619D320B9AA19 (void);
extern void ArCameraConfig_Dispose_m165F78DBFE34B76060059B7BFC7EB367E0D626CC (void);
extern void ArCameraConfig_GetCameraId_mE547D5D96CFA540BE2631831CB839D3D907DAC90 (void);
extern void ArCameraConfig_GetDepthSensorUsage_m5F136C8F9C5B3044C503580EB45DBC2671EC7C4F (void);
extern void ArCameraConfig_GetFacingDirection_m3C022FA6AC02ED9BBBE8B4923E9CA397E72FB7A4 (void);
extern void ArCameraConfig_GetFpsRange_mF9215E84CF0CF2EB05D2ACD0CFDE02899C9F2533 (void);
extern void ArCameraConfig_GetImageDimensions_m3746D40ABD8902AEA59C72E1F6B23635A1A2C2C6 (void);
extern void ArCameraConfig_GetTextureDimensions_mA0E9C970FDEE0847B4FC7AABC35D82B0259648AE (void);
extern void ArCameraConfig_op_Explicit_m94CDC0BE7224BA7B163E8E27129C92963FFB7F87 (void);
extern void ArCameraConfig_Equals_m970EB204E68B633C9E34D1ED6A06EDCCD7E461E9 (void);
extern void ArCameraConfig_Equals_m475848DB0C28F764CAFC0F44E27B06BEB7564041 (void);
extern void ArCameraConfig_GetHashCode_m12BD55CF30C1466B45155A17224C4AA9AB16C4E0 (void);
extern void ArCameraConfig_op_Equality_m84667F11E490E27DE28189D00CA36C0575DEBDCD (void);
extern void ArCameraConfig_op_Inequality_mF85ED6F7896064432D80B39BF4A42F794108230E (void);
extern void ArCameraConfig_op_Equality_mCE55DA201D1AB39471354684350E00BB06DBFA5C (void);
extern void ArCameraConfig_op_Inequality_m0122056A9A2B35072F4B6E815FF762268D32DE32 (void);
extern void ArCameraConfig_Create_m886224169A78B3D22AB043E40F1524CF667F28FC (void);
extern void ArCameraConfig_Destroy_mABA8B48536BC128B2F42CA1F8C7381412C11046F (void);
extern void ArCameraConfig_GetCameraId_mEDFA5672A4B0455D422FB0C4124606BF5792FE55 (void);
extern void ArCameraConfig_GetDepthSensorUsage_m1E6D173C6463759F5E3C80848B26598DC419C0DE (void);
extern void ArCameraConfig_GetFacingDirection_mF3A1BE5B912348463F8FECFE1EFD6C4660912114 (void);
extern void ArCameraConfig_GetFpsRange_m288AE232C4A22D6FC87AE2EFA38B4611CE222087 (void);
extern void ArCameraConfig_GetImageDimensions_m00F158B234F449E732FA04FE3F2962A6B6D3BB7C (void);
extern void ArCameraConfig_GetTextureDimensions_mC2ED0976D9D803C8067E13BE4C5311C3300FAAB7 (void);
extern void XRCameraConfigurationExtensions_AsArCameraConfig_m9D94625F0D83F9421073FEEBC143F5AB4F80E6D5 (void);
extern void ArCameraConfigFilter__ctor_mAFDDE747B063B59B3DA2DD79FC20A16D497C07FA (void);
extern void ArCameraConfigFilter_FromIntPtr_m3216CCE246F8D70DDA692B47E294EF62C43559A7 (void);
extern void ArCameraConfigFilter_get_Null_m14F7F5F8AAE885AA0E25261991DB627EF6ADA4E0 (void);
extern void ArCameraConfigFilter__ctor_mF486A6AFC61683CFC3E52B7E5494E983D4DE83E7 (void);
extern void ArCameraConfigFilter_AsIntPtr_mC26AD4E33C4D07ED7C4970BC7C75456AC84D62BF (void);
extern void ArCameraConfigFilter_get_IsNull_m0C4936F2612BC0BB7B4BAF08F9F0947D69EDC383 (void);
extern void ArCameraConfigFilter_GetDepthSensorUsage_mAD042C592CA038A62E72173B8B0E9224067C7C12 (void);
extern void ArCameraConfigFilter_SetDepthSensorUsage_m92C45E574F2383B585B96C7FEC9FF2CCEABC68E3 (void);
extern void ArCameraConfigFilter_GetTargetFps_m8A00967A664541D6702D5256F048DC56EBB74983 (void);
extern void ArCameraConfigFilter_SetTargetFps_mD09E6CDC671DB61E9FC443F99A26EF4C716C9766 (void);
extern void ArCameraConfigFilter_Dispose_m71514EB30557871157FCA62921F794E10EC3BD59 (void);
extern void ArCameraConfigFilter_op_Explicit_m89BE76307F2F39AB124E955E03228A45AD6F50A7 (void);
extern void ArCameraConfigFilter_Equals_m7A03616AACBC45070F8F65ACE805CF16ADF65494 (void);
extern void ArCameraConfigFilter_Equals_mF6B858B3A8FC33E7912163F338B1CC71C59744ED (void);
extern void ArCameraConfigFilter_GetHashCode_mAB06AE4573E43FB7802536BD3809666832F1348A (void);
extern void ArCameraConfigFilter_op_Equality_mA7C1718FBD058D7E2DB53C134BCE698FDF0E9136 (void);
extern void ArCameraConfigFilter_op_Inequality_m334D48DAF3A4F5F757C12A61B95913F3E560B362 (void);
extern void ArCameraConfigFilter_op_Equality_mEC832B8112587B3269F1EE3E5F42320B0CE170B9 (void);
extern void ArCameraConfigFilter_op_Inequality_m077FD4EF3B102E15565FE92111F8D6858BD2CEC6 (void);
extern void ArCameraConfigFilter_Create_m142C7ADE353D6E38316A3A6EE50CB56FC883AC07 (void);
extern void ArCameraConfigFilter_Destroy_m9EAD8BB21D88A11EE1ED2E885E92B06FE96BEA6F (void);
extern void ArCameraConfigFilter_GetDepthSensorUsage_mD6B774F26D0C2DE2202A91A98F6DBACF90D98E61 (void);
extern void ArCameraConfigFilter_SetDepthSensorUsage_m5B7D3EC70DBB599EE5F6F6A682D6D55011D30555 (void);
extern void ArCameraConfigFilter_GetTargetFps_m9973098C5F69BF6DBEB4694352ADF354C213BC02 (void);
extern void ArCameraConfigFilter_SetTargetFps_mABECEE8F72881F7B6F0CD21C0916DCB68B949FCF (void);
extern void ArConfig__ctor_m0E326EEBB3B5154A7BA864AF3B31CA153C4AC072 (void);
extern void ArConfig__ctor_m807FC812AF15E78B8FD5025A164FF214A030F793 (void);
extern void ArConfig_FromIntPtr_mE9B04C63D4EE5D8223D76D67FE5DCF2C7071D2AF (void);
extern void ArConfig_get_Null_mCAF6C746D44A04E3AE94752CD94D2A4564CB48CE (void);
extern void ArConfig_get_IsNull_mC16E8E4A5FD7562D365B3CED6F6195F6D3A4BA98 (void);
extern void ArConfig_AsIntPtr_m9CFB8A76A4BB209848A58B8CB8CC7481BF763A42 (void);
extern void ArConfig_op_Explicit_mCCBCE6CB895992DB341D46AE4141049A24D16639 (void);
extern void ArConfig_Equals_mCE8CA68F242CF012255732C5387E953EFB7C7572 (void);
extern void ArConfig_Equals_mAF04B9E9552EAF1D2DE31F107EF45BBC9CF8BBA4 (void);
extern void ArConfig_GetHashCode_m2AA440E48A34FDA2186E99FDF0630578AB433DC4 (void);
extern void ArConfig_op_Equality_mE6B7E1ACCC7A3F8835FDB29C2652183F5ACAB162 (void);
extern void ArConfig_op_Inequality_mF17E9CAD6C39547693DB6105CBAB4AD9C1CC511B (void);
extern void ArConfig_Dispose_m7AFD805848963702EA520A205BF9E3AC79BFA37B (void);
extern void ArConfig_op_Equality_m5078A53EB4B64DB5D3415C642CE14B353E370B7C (void);
extern void ArConfig_op_Inequality_m9F0E103ACB9A3EBC2D02B926BE22986213116EB9 (void);
extern void ArConfig_Create_m165DEBEB687311E29D05FBB31CB1C31916DFDBAA (void);
extern void ArConfig_Destroy_m28D7B99179AA968D94454EFF9FD67F44AE354DE3 (void);
extern void ARCoreAnchorSubsystem_RegisterDescriptor_m0B16D64374E148EB807D101D46503CB91525E57F (void);
extern void ARCoreAnchorSubsystem__ctor_mC98DC11B6F123B0FC0EA5ADFF88B8FA0E8A8D08B (void);
extern void ARCoreProvider_Start_mD07637BF200496D0C3615BB8CDB276EAF57A9901 (void);
extern void ARCoreProvider_Stop_mEA426F258151A7C2A8D1E638C02C6208A3583DB1 (void);
extern void ARCoreProvider_Destroy_m2C54D63343AAAA27AE6CA3A9AD64A45E0DFA5148 (void);
extern void ARCoreProvider_GetChanges_mDF33BE46E05FE3C966FBA1250EB5025782AD01DA (void);
extern void ARCoreProvider_TryAddAnchor_m943778D8A0B7D1A12C866ED6C4C2E8C20749D3EC (void);
extern void ARCoreProvider_TryAttachAnchor_m0199986FA5941EE3C5265B5BE24B60EAC0F3526C (void);
extern void ARCoreProvider_TryRemoveAnchor_mB6B7A1248155B29CACE8626014BCDD37FDD4FB05 (void);
extern void ARCoreProvider_UnityARCore_refPoints_start_m3BF0EDD507607790FB6A73B908381CAA98C2B980 (void);
extern void ARCoreProvider_UnityARCore_refPoints_stop_mC98354069993B42D46D731556296E48E198BCCC2 (void);
extern void ARCoreProvider_UnityARCore_refPoints_onDestroy_mD8491220209464421E7E61A4A8D2AC7E61093B35 (void);
extern void ARCoreProvider_UnityARCore_refPoints_acquireChanges_m10A2AA33C27724FF1E27F831A0CFE49A6842F100 (void);
extern void ARCoreProvider_UnityARCore_refPoints_releaseChanges_m080766056910991717D5B6803BF977E7C28BF6C1 (void);
extern void ARCoreProvider_UnityARCore_refPoints_tryAdd_m0C7D4AB47E2EC13723A1E950F618E250AC7746D8 (void);
extern void ARCoreProvider_UnityARCore_refPoints_tryAttach_m646E2CC0804C2F2FEA5A50331CD656295F07F439 (void);
extern void ARCoreProvider_UnityARCore_refPoints_tryRemove_m50646A941477F2488B14C2435DDC8B1024D70641 (void);
extern void ARCoreProvider__ctor_m77B4C1ACBC5FE569D98454C3530E26CF11089C6A (void);
extern void Api_SetFeatureRequested_m4F773D423194515D322A72241249268319B69472 (void);
extern void Api_GetRequestedFeatures_mB3FB6A19484851715A59DE76B42F44AB56EA2B26 (void);
extern void Api_get_platformAndroid_m6AD0B05ABD6C0211049325687C9FFD80128A5479 (void);
extern void Api_FindLoader_mB06884CB5D22BA3696C18BB324C3012A98A17CAD (void);
extern void Api_get_loaderPresent_mA5AF7546F25C9A370DB1E9CF82C5E5BC2B58C301 (void);
extern void Api__cctor_mD2F47985A010C03E7CD96F715CA0DE566ECCFE0A (void);
extern void ARCoreBeforeGetCameraConfigurationEventArgs_get_session_mCC2430CFD657AA39475EC7F94D12E94602318773 (void);
extern void ARCoreBeforeGetCameraConfigurationEventArgs_set_session_m0E5A8E5B0E3D93A3CAD6F54F7E44C732FCE8686A (void);
extern void ARCoreBeforeGetCameraConfigurationEventArgs_get_filter_mF91BB4DDE06DAD980576BE33973865F4CAD2C702 (void);
extern void ARCoreBeforeGetCameraConfigurationEventArgs_set_filter_m136097E18565BAB7B14635A951EF75F91208BB08 (void);
extern void ARCoreBeforeGetCameraConfigurationEventArgs_Equals_m967BC71D3DF0590295A53C8BFA897626CC513EFC (void);
extern void ARCoreBeforeGetCameraConfigurationEventArgs_Equals_m08904E4BE8373C7A8930D51AF33F2EF8F60BA528 (void);
extern void ARCoreBeforeGetCameraConfigurationEventArgs_GetHashCode_mBF1B9E848558FCF41FFA006B2A67018D2F405899 (void);
extern void ARCoreBeforeGetCameraConfigurationEventArgs_op_Equality_mFCBA680669F086A7FF218857E32FB6AE61BCDF1D (void);
extern void ARCoreBeforeGetCameraConfigurationEventArgs_op_Inequality_m5B0607B9CAC0D53A85D1F56433AD62FEE4601EED (void);
extern void ARCoreBeforeSetConfigurationEventArgs_get_session_mFB401F61257971B12D20BA2C2A13427ADF110405 (void);
extern void ARCoreBeforeSetConfigurationEventArgs_get_arSession_mFD007DB2BB188809848E2E6F699A87C4041137B9 (void);
extern void ARCoreBeforeSetConfigurationEventArgs_get_config_mABF124F70ED16AECD48A7AD0758798446A53DF31 (void);
extern void ARCoreBeforeSetConfigurationEventArgs_get_arConfig_m1EE2C53DDF02FA08DF8EEC1C016B7C4F5BBFC598 (void);
extern void ARCoreBeforeSetConfigurationEventArgs__ctor_mDEADF5D3E5BD90B0D3715FE63350F8B23475B297 (void);
extern void ARCoreBeforeSetConfigurationEventArgs__ctor_mE6E334059D83C27B306C1F72CD68C86E6DA4FED2 (void);
extern void ARCoreBeforeSetConfigurationEventArgs_Equals_m4B4E1CC09BDDC6E0259BAC9D254FE94F81E336BB (void);
extern void ARCoreBeforeSetConfigurationEventArgs_GetHashCode_m6E3BC2573D8D619B0E86CCF48EB8F27283F41349 (void);
extern void ARCoreBeforeSetConfigurationEventArgs_Equals_m8969624DBC58D203BE709765D5623E86DAAAC90B (void);
extern void ARCoreBeforeSetConfigurationEventArgs_op_Equality_mEC9AF5E7AEF64485667A015E7C13AA09F0716F44 (void);
extern void ARCoreBeforeSetConfigurationEventArgs_op_Inequality_m99E23DE462306E6740509568AB1B9286F2ED3E9B (void);
extern void ARCoreCameraSubsystem_get_backgroundShaderName_m1AED04C9CE5C791D05FA636FE228E6DD3E29811E (void);
extern void ARCoreCameraSubsystem_Register_mDB9444ABD74F698F8DF2C26753B86A8746228DEC (void);
extern void ARCoreCameraSubsystem_add_beforeGetCameraConfiguration_mEF3CC67CF983EDDA871ACE63A8F93B6643526F53 (void);
extern void ARCoreCameraSubsystem_remove_beforeGetCameraConfiguration_mBAD3DB452B748425BECB242DAAE2CDA672B95C06 (void);
extern void ARCoreCameraSubsystem_TryGetCurrentConfiguration_m334348283EABC372AD15A49B02C504B543B53F78 (void);
extern void ARCoreCameraSubsystem__ctor_mBD839198513F7B370BA4BC7523726EBF762BC83E (void);
extern void ARCoreCameraSubsystem__cctor_mF76DBAF369FE461D54F27CA1C6A6D18BD277DC61 (void);
extern void ARCoreProvider_get_cameraMaterial_m43BB6CB876819A670E99E2AF819F21F093E825AF (void);
extern void ARCoreProvider_get_permissionGranted_m3A14A077F17265C42FE4F0C056EC08AA5BECE474 (void);
extern void ARCoreProvider_get_invertCulling_m12A9EF15FB1037990513A6E8729B266F38760405 (void);
extern void ARCoreProvider_get_cpuImageApi_m24D27620E1001943EF0A520D1D3DD21D08BDBFF1 (void);
extern void ARCoreProvider_get_currentBackgroundRenderingMode_mCEFE851C8BBAA64426C05EA4F80B09D6330E6BE2 (void);
extern void ARCoreProvider_get_requestedBackgroundRenderingMode_m49F511F80CDD4410F31991788042B8F85668E388 (void);
extern void ARCoreProvider_set_requestedBackgroundRenderingMode_m1B730EA388B83551B6927485B042938BB6C1CF82 (void);
extern void ARCoreProvider_get_supportedBackgroundRenderingMode_m7EF47D3103060BCF105F8191E7905F81C2577176 (void);
extern void ARCoreProvider__ctor_mB3641D95DB0A7353E5F3C987660BA2282151A3A2 (void);
extern void ARCoreProvider_get_currentCamera_mDFD20E6CA1F40F899A04DF4EC99FE4ADED02BF21 (void);
extern void ARCoreProvider_get_requestedCamera_m87CE71EFDF07B0D4A00F4ED55E2F58BBE6FA7B4A (void);
extern void ARCoreProvider_set_requestedCamera_m20BBB626E437F07057452F1F89441268CA217CA8 (void);
extern void ARCoreProvider_Start_m7622EA81D53C25844D9EC8990A0927D3E144C8EB (void);
extern void ARCoreProvider_Stop_m28914ABC564DB03341B5FA1B384BBBC2FA5B2689 (void);
extern void ARCoreProvider_Destroy_m97DEF45E2DD070C2DA43C7D0C42B6B119973A484 (void);
extern void ARCoreProvider_TryGetFrame_m5DBFF8050E393426C025DC027812000FDECDCFD5 (void);
extern void ARCoreProvider_get_autoFocusRequested_m1DC365A076DD9E57832CAB0D9C8E3AC31807D317 (void);
extern void ARCoreProvider_set_autoFocusRequested_mC8A88C6393A2738B39DB3DCDB70674BCA83DDC61 (void);
extern void ARCoreProvider_get_autoFocusEnabled_m048D6D0D465785EB8DEFD70BFC57DCFCFF010EDA (void);
extern void ARCoreProvider_OnBeforeBackgroundRender_m93F93BFCE712FC6CF12F3407DA175A0D1A1BB204 (void);
extern void ARCoreProvider_get_requestedLightEstimation_mA5865DCE9DDC79B34007981126D8C0CC6C0969DF (void);
extern void ARCoreProvider_set_requestedLightEstimation_m8D927B6282C8DDA7BD4365148AF597EAC02E9E46 (void);
extern void ARCoreProvider_get_currentLightEstimation_mA0829B77045555BF2B50BC73BD536AFF48E789B9 (void);
extern void ARCoreProvider_TryGetIntrinsics_m13E64DDA943B4179334A01D9821C7EB53455EB7D (void);
extern void ARCoreProvider_GetConfigurations_m9D7C215C704B94C838518874CA90A3B62D7CCCD3 (void);
extern void ARCoreProvider_get_currentConfiguration_mD5373EEAA8C48033E3B2F3D892B0C384E2B9D67C (void);
extern void ARCoreProvider_set_currentConfiguration_m687F79EC299F69E1A227CE4FE4AE4CE7DC07F305 (void);
extern void ARCoreProvider_GetTextureDescriptors_m3CDF78C26FAD8C70D183EC23001E4BFA872AC4C5 (void);
extern void ARCoreProvider_TryAcquireLatestCpuImage_m092583292CFCF7D6AB32DACA59817991B5644839 (void);
extern void ARCoreProvider_add_m_BeforeGetCameraConfiguration_mB56992655281D83A97D444CDD8CFBCBE843C38AA (void);
extern void ARCoreProvider_remove_m_BeforeGetCameraConfiguration_mCC65AEBA7C580EF96F2DBE05418BC9674A5EF417 (void);
extern void ARCoreProvider_add_beforeGetCameraConfiguration_mD986481F241B18D60730DE4979BDF5214F614453 (void);
extern void ARCoreProvider_remove_beforeGetCameraConfiguration_m5C92FF8B21BD98B983C858D6707B27E657CF45AC (void);
extern void ARCoreProvider_OnBeforeGetCameraConfiguration_mA33A6474A489CA38E45F609013293D679AF4D06A (void);
extern void ARCoreProvider__cctor_m421CDDCEC07E8682FEDA13984923CCF415E75D4F (void);
extern void NativeApi_SetOnBeforeGetCameraConfigurationCallback_m404BBA78562A936BEB5EB42FE09EB8453F91268D (void);
extern void NativeApi_UnityARCore_Camera_Construct_m09C6A5328195C5F09495FC5D0A61F97AB854FDF6 (void);
extern void NativeApi_UnityARCore_Camera_Destruct_mD0587A807E9B6043FCA14A573F56B6B413E8A4E6 (void);
extern void NativeApi_UnityARCore_Camera_Start_m18F79AD4568ED1311E5930E06D25B54EC87C1B23 (void);
extern void NativeApi_UnityARCore_Camera_Stop_m9B882F8A26DF9BE25D69AB2578178834E38F4153 (void);
extern void NativeApi_UnityARCore_Camera_TryGetFrame_mA83D08077103882819A8A26D9470699A90EDF56B (void);
extern void NativeApi_GetAutoFocusEnabled_m2E73F4CBF605540EB4FB0268BE89A38183065453 (void);
extern void NativeApi_GetCurrentLightEstimation_mD098CD7D0A0582093717DCDA7563EE67B23936C9 (void);
extern void NativeApi_UnityARCore_Camera_TryGetIntrinsics_m22787C20AE622991207EF3F62214097B3300F3D7 (void);
extern void NativeApi_UnityARCore_Camera_AcquireConfigurations_m3DFFB1427453B8FF4662B2136C260B62A9742C8F (void);
extern void NativeApi_UnityARCore_Camera_ReleaseConfigurations_m23C062833C5DAAC916BBA482B5BB4D4723B31C90 (void);
extern void NativeApi_UnityARCore_Camera_TryGetCurrentConfiguration_m98FA207339AC6B64F487D3B002FDACB16C5CC1F9 (void);
extern void NativeApi_UnityARCore_Camera_TrySetCurrentConfiguration_m2145D1E21F1C3C88D921ABEF2C0A17E8CB006D00 (void);
extern void NativeApi_UnityARCore_Camera_AcquireTextureDescriptors_mF8DCCA2D9A1D47441F10200174B0F244EF67A727 (void);
extern void NativeApi_UnityARCore_Camera_ReleaseTextureDescriptors_m8F3572CC377BD7DADFECCE83BB39A701AF5011A9 (void);
extern void NativeApi_UnityARCore_Camera_ShouldInvertCulling_m81401BC5C2C778FDFDAFDBF363E8B33088C0C686 (void);
extern void NativeApi_GetCurrentFacingDirection_m0C3820870A31AACCDD681A4FE4AE8F13DA409A02 (void);
extern void NativeApi_UnityARCore_Camera_GetFenceWaitHandler_mB93CED3C6C7F04955BF74F206B941D92AD9A915F (void);
extern void ARCoreCpuImageApi_get_instance_mE85E10D548E0FFF2FDB0532444C6D2E33387BEAD (void);
extern void ARCoreCpuImageApi_TryAcquireLatestImage_m5C02A41B0E866D8AC99E4F32B25F2D5890ECA073 (void);
extern void ARCoreCpuImageApi_GetAsyncRequestStatus_m3353734BAFE73783A6400AE409D99D4BD6F95261 (void);
extern void ARCoreCpuImageApi_DisposeImage_mB7DFA6357901007B40AB836685665DF868A50A48 (void);
extern void ARCoreCpuImageApi_DisposeAsyncRequest_m1C836B0DFC1351EC84ED75B00C72B36B878E7208 (void);
extern void ARCoreCpuImageApi_TryGetPlane_m9336AE63158D11FCB0454F8D984B0AA7EF1F07E2 (void);
extern void ARCoreCpuImageApi_NativeHandleValid_m0A091EF7277486E0AA8CD4AE07DA678D166F73E9 (void);
extern void ARCoreCpuImageApi_TryGetConvertedDataSize_m2CBCAFAABF0F11B1F5E822CC68B76DF46419D95A (void);
extern void ARCoreCpuImageApi_TryConvert_m73099E871C6073CF094D502ACEA729F7608F791E (void);
extern void ARCoreCpuImageApi_ConvertAsync_m47508107848E58A4F8D3E2A7269E13253D357BED (void);
extern void ARCoreCpuImageApi_TryGetAsyncRequestData_m3518605946425B8BBF80F7B97384F561DB95E9B5 (void);
extern void ARCoreCpuImageApi_ConvertAsync_mCB3F9F6299DB7CF6812DC1E1FDC38E5A891563D0 (void);
extern void ARCoreCpuImageApi_FormatSupported_m668336D4CFC1D2A98637ED8524387E67267967E1 (void);
extern void ARCoreCpuImageApi__ctor_m16F0D8727C47B3BCAA39133E46AF6199C646F427 (void);
extern void ARCoreCpuImageApi__cctor_mA328729FD7C06E9389F62404F3C18D1797B6E13D (void);
extern void NativeApi_UnityARCore_CpuImage_TryAcquireLatestImage_m961556B4A05E02E1E0913A05293B6B38ACDADDD8 (void);
extern void NativeApi_UnityARCore_CpuImage_GetAsyncRequestStatus_mAA543CADE1A13A202E8073F025888D0285EE3B06 (void);
extern void NativeApi_UnityARCore_CpuImage_DisposeImage_mE3226ADD096206D705A18DB3856ED66B0B2E689E (void);
extern void NativeApi_UnityARCore_CpuImage_DisposeAsyncRequest_m84763E96A20BF94A2EE96A5757ADAE3529A89421 (void);
extern void NativeApi_UnityARCore_CpuImage_TryGetPlane_mE677AC286DB8A37FD3E116B917C6B2043A0023AD (void);
extern void NativeApi_UnityARCore_CpuImage_HandleValid_mE49A1DD268714610CD220AED14D40B387F274BA8 (void);
extern void NativeApi_UnityARCore_CpuImage_TryGetConvertedDataSize_mE2F9C873F3FAAB9CA0A600E51A9281B5E00BB018 (void);
extern void NativeApi_UnityARCore_CpuImage_TryConvert_m056AFCB611BC71AC56AE69DCABEC98440387C6A5 (void);
extern void NativeApi_UnityARCore_CpuImage_CreateAsyncConversionRequest_mD0B2E29B3DB70008E22AD099E3F744BE6607019E (void);
extern void NativeApi_UnityARCore_CpuImage_TryGetAsyncRequestData_mC79F97E6877D4D1BEA3D9E8E05F11ED9C24EB205 (void);
extern void NativeApi_UnityARCore_CpuImage_CreateAsyncConversionRequestWithCallback_mC60CA405C610659CDBCCE983EBBB93AD0B70E027 (void);
extern void ARCoreEnvironmentProbeSubsystem_Register_m41908310BC181A097546334CA89E74F7E4CDFC11 (void);
extern void ARCoreEnvironmentProbeSubsystem__ctor_m89DECFC44829DEFA9B37B205F2DCD73080ED78B0 (void);
extern void ARCoreProvider__ctor_mA174658BF6BFB950970E9076BB5F166FE696C264 (void);
extern void ARCoreProvider_Start_mD152BD0A62302F3C7AEFB38AB10BDE64D25CDF73 (void);
extern void ARCoreProvider_Stop_mF4D5343658FFDA68F481D8E998C8859B05E5A353 (void);
extern void ARCoreProvider_Destroy_m10827FBF253B47F6B6134DC64DBB0A0D6E43F8AD (void);
extern void ARCoreProvider_get_automaticPlacementRequested_m8DB8F64671B4DD9C57A7FE7B42F0595E681AC456 (void);
extern void ARCoreProvider_set_automaticPlacementRequested_m18F7D90E52BBE1656F004B3CAA4F6CBF9B5000D4 (void);
extern void ARCoreProvider_get_automaticPlacementEnabled_m60D0A0EA74B9C86D7F0EFBF02BF0CA385A2CBC1F (void);
extern void ARCoreProvider_get_environmentTextureHDRRequested_mC7E1DEED9E31F26A4089FDCC7BF7DB10352A5732 (void);
extern void ARCoreProvider_set_environmentTextureHDRRequested_mB9DFB8284C97AC4D77505C872D3E471C436C77E8 (void);
extern void ARCoreProvider_get_environmentTextureHDREnabled_m7F547DA0179CEBE47CA9A123E2D01007F8429F3F (void);
extern void ARCoreProvider_GetChanges_mAC7FEC7A1AD00B7207342CDD2654505031F48728 (void);
extern void NativeApi_UnityARCore_EnvironmentProbeProvider_Construct_m01ADDDD26A6B93C63294CB7B612181E92154BC9D (void);
extern void NativeApi_UnityARCore_EnvironmentProbeProvider_Start_m7B61D1337949AF31C753FC702F4D31A74C0AC06D (void);
extern void NativeApi_UnityARCore_EnvironmentProbeProvider_Stop_m3F992A4699AE1C21976F5D7A354671059A169AD4 (void);
extern void NativeApi_UnityARCore_EnvironmentProbeProvider_Destroy_m80C3C96A2F4E056BD89ACDC7B0C317046DF59756 (void);
extern void NativeApi_UnityARCore_EnvironmentProbeProvider_SetAutomaticPlacementEnabled_mF051761CA9DF85672D998961422674F675F50BEC (void);
extern void NativeApi_UnityARCore_EnvironmentProbeProvider_TrySetEnvironmentTextureHDREnabled_m00678897FC7ECB1C75D6630B3687FB8100147856 (void);
extern void NativeApi_UnityARCore_EnvironmentProbeProvider_GetChanges_m735B2332E72E233E69CF6569BED3FB8B1D66AC89 (void);
extern void ARCoreFaceRegionData_get_region_m7CD0AB1C423FD7A964F41C4CCCD14857C08177FE (void);
extern void ARCoreFaceRegionData_get_pose_m8B0A57A837D75EC314AE73F2931075A0F10A3775 (void);
extern void ARCoreFaceRegionData__ctor_m589405112E30EB33BE5D5E10C0F5CC67D24FBB49 (void);
extern void ARCoreFaceRegionData_Equals_m2056862DD1AD264FBDD1C54E254871098B6995DC (void);
extern void ARCoreFaceRegionData_GetHashCode_mC1E01B54503EADB48B6927CF2074C7FA4D426ECE (void);
extern void ARCoreFaceRegionData_Equals_m17225C794C37005208BCDAAEB9DDE4F9B664D126 (void);
extern void ARCoreFaceRegionData_ToString_m01A02559D50A9AB69A98601C0DC8E707ECDDB02F (void);
extern void ARCoreFaceRegionData_op_Equality_m0AD73FE00B48C54DE058A86A425B430F4F6E7252 (void);
extern void ARCoreFaceRegionData_op_Inequality_mC9B7928874917F1AFAF5FBAAD223164B0536E58C (void);
extern void ARCoreFaceSubsystem_GetRegionPoses_m86783D41F79D1D4E8811CABE414E1F12437F21C5 (void);
extern void ARCoreFaceSubsystem_UnityARCore_faceTracking_Start_mE2B22D6EBED2B0A2726BAA6E88691BBA7DD315BD (void);
extern void ARCoreFaceSubsystem_UnityARCore_faceTracking_Stop_m65991C5FFE2FEA7DF1FA0515EAB390C00B6F6F64 (void);
extern void ARCoreFaceSubsystem_UnityARCore_faceTracking_Destroy_m3D9A9C059215ED4839D516843174A9E2D65F56F9 (void);
extern void ARCoreFaceSubsystem_UnityARCore_faceTracking_TryGetFaceData_mEF3EEAE21430BBB9A1707E1695631CD13B67B9FC (void);
extern void ARCoreFaceSubsystem_UnityARCore_faceTracking_AcquireChanges_mCB0CEE9BF0F8E316F02641C04B20B0E1EBB1DBAE (void);
extern void ARCoreFaceSubsystem_UnityARCore_faceTracking_ReleaseChanges_mFCEC09D4F7BA73E3C3968BF49042EE48BAD9C4BF (void);
extern void ARCoreFaceSubsystem_UnityARCore_faceTracking_acquireRegions_m0886B63972051E8B9B53723699FC4466070BF452 (void);
extern void ARCoreFaceSubsystem_UnityARCore_faceTracking_deallocateTemp_mD03CDFA5D36E7AF8633A55677CC3C98562EABD68 (void);
extern void ARCoreFaceSubsystem_RegisterDescriptor_mBAAA288ECADA5B4680BD731E33638DA216DB130F (void);
extern void ARCoreFaceSubsystem__ctor_mE994B52DC28CB49FDBC336E54C3510FEDF663138 (void);
extern void ARCorePose__ctor_m1328D26F798D034CBD7F0402528B873D5994D5AF (void);
extern void FaceRegionWithARCorePose__ctor_m1021176D2016468413133E87B6F50884A240E76D (void);
extern void TransformPoseJob_Execute_m7EBE008B308EEF99D66204836A8954E1C6F6F1D3 (void);
extern void ARCoreProvider_Start_m674E115A338FF9DABC486AEF8E2B2B469D68452C (void);
extern void ARCoreProvider_Stop_m67BF08E9D1BA2C4C31060C97F2B6324AF74FA4DF (void);
extern void ARCoreProvider_Destroy_m3B9CEC2B8C99C1AB440C8434965D002153639BE5 (void);
extern void ARCoreProvider_GetFaceMesh_mE8C775C1B20E2648524995615154E24D8312559D (void);
extern void ARCoreProvider_GetChanges_m3F0274A1E1E2BCFACB2CB91032CD40ACE21E2710 (void);
extern void ARCoreProvider__ctor_m7212DC6DFC295E14FECF45D31586ED62BE00E52D (void);
extern void TransformVerticesJob_Execute_mB25F13AA4AB3152365B51B0C2ACA31FFC756D74F (void);
extern void TransformUVsJob_Execute_m3A9C954126E9929C947C66111DC50B1A4EB8478A (void);
extern void TransformIndicesJob_Execute_m20C5A48E77A83348D5BD86AD436EBC581C5F0AAB (void);
extern void ARCoreImageDatabase_GetLibraryData_m9F28026F0212BA3299F23C27645BD6C66BF478C2 (void);
extern void ARCoreImageDatabase__ctor_m088924D76C9D933AAD4ACAA904D5077384B9FBE5 (void);
extern void ARCoreImageDatabase_op_Explicit_m7A53CCF8374A72B15D6B73ACADC5909886E0C92E (void);
extern void ARCoreImageDatabase_Finalize_m1A668727FF90C396A2501036A38C4BA412212F8F (void);
extern void ARCoreImageDatabase_get_supportsValidation_mCD44728F3EBAD0F56E9B016C009DAADFDB6E3379 (void);
extern void ARCoreImageDatabase_get_supportedTextureFormatCount_mAED9D8669DE36B2AA73C7143C24BD620548ABC52 (void);
extern void ARCoreImageDatabase_GetSupportedTextureFormatAtImpl_m1D2F965E4E7F58B0FE7BA6262ECBC994F73BE9C2 (void);
extern void ARCoreImageDatabase_GetAddReferenceImageJobStatus_m1BBD7DD51F11B3FD80A4E0CD21D085399471BEB7 (void);
extern void ARCoreImageDatabase_GetUTF8Bytes_mDF50981E4D87ABAC80566B234E197207BCD4074A (void);
extern void ARCoreImageDatabase_ScheduleAddImageWithValidationJobImpl_m8458FC816306C31C531AEDF9342441B13FE85577 (void);
extern void ARCoreImageDatabase_ScheduleAddImageJobImpl_m443DE86ADBC43475425A2C26ABE5EC6B3D78FA20 (void);
extern void ARCoreImageDatabase_GetReferenceImageAt_m9319449DB183EAA9CB1F16CDF886E7601B798E56 (void);
extern void ARCoreImageDatabase_get_count_m97A7B403F0B4544C7CD6DE8B860DBEA9AB1E4F99 (void);
extern void ARCoreImageDatabase_GetHashCode_mA4D7878527E9FC94B5F3AFD8E946C7CBBDDA6DC2 (void);
extern void ARCoreImageDatabase_Equals_m49780C2062A4B7D7A72D24F93B242DBC1CC8336C (void);
extern void ARCoreImageDatabase_Equals_m00A589ECD8847528CCA00E2AC42863D9437C492E (void);
extern void ARCoreImageDatabase_op_Equality_m2823B083318E8120CF73F4B4B27BCF2493BC6969 (void);
extern void ARCoreImageDatabase_op_Inequality_mD705CA7598C95660A54703EB72A0A81CAE6A573C (void);
extern void ARCoreImageDatabase_GetReferenceImage_m153C4089F52AEF938CABD2975A16DD9C0AC2E969 (void);
extern void ARCoreImageDatabase_GetReferenceImageCount_m28772B1CADB95B265D85FDF95EA30D9FC9E0609F (void);
extern void ARCoreImageDatabase_Deserialize_mCCEEFCA3F11676EDADB06955019777B6EE1627C8 (void);
extern void ARCoreImageDatabase_CreateValidator_mE8BC250B5B5421C3C58E64B3140CF6DB1E4D85CD (void);
extern void ARCoreImageDatabase_GetStatus_mE6201BFBCDE4FFD9F40F842E0DE11BEE2D66D5EC (void);
extern void ARCoreImageDatabase__cctor_mFAD60574CD25D3C475E9CA655B28BDB28D391D7C (void);
extern void AddImageJob_Execute_mFF2FF44D70D3647AF25843519E6B767A99BAAF36 (void);
extern void AddImageJob_AddImage_m04AFBEC870C62812FD6EF08E1A4B6FB85E8AA3EA (void);
extern void ARCoreImageTrackingSubsystem_GetPathForLibrary_m42D960137532DA8802302BBC3202D1D05CB63A7A (void);
extern void ARCoreImageTrackingSubsystem_RegisterDescriptor_m5B575A92EA6372775F273462FB572D5B236364C6 (void);
extern void ARCoreImageTrackingSubsystem__ctor_m8B8F027237262EB2944D5678C3B5FEF4389A20C5 (void);
extern void ARCoreImageTrackingSubsystem__cctor_m655EE8417F9BAE6B9577466A43F074A3ACFB4D4B (void);
extern void ARCoreProvider_Start_m95DD106DD54DAAEE54CA17B159AFF4331272FCCC (void);
extern void ARCoreProvider_Stop_mEDF427A8C78A9AE6C0F91AD82FC0BC92E127C728 (void);
extern void ARCoreProvider_set_imageLibrary_m5E48BF05853F210F48F84F820AE48C4B02A63633 (void);
extern void ARCoreProvider_CreateRuntimeLibrary_m578F5264A70564E1A0B21D060F40CEA4C15657B7 (void);
extern void ARCoreProvider_GetChanges_mBD6B8435337693F22D58AC045B036DB668985F37 (void);
extern void ARCoreProvider_Destroy_m3CAE3DD73B251AADAAA18E255A50D979BDD4574E (void);
extern void ARCoreProvider_get_requestedMaxNumberOfMovingImages_m9136442725D06CDB5469F07BAF880A163B70266B (void);
extern void ARCoreProvider_set_requestedMaxNumberOfMovingImages_m75E4CAEB39E571F7F50F0D77AAF134DC8466912C (void);
extern void ARCoreProvider_get_currentMaxNumberOfMovingImages_m40F817E6DB85D560F08C853FA45E9B15AE4D8E90 (void);
extern void ARCoreProvider_UnityARCore_imageTracking_setDatabase_mB0F8D74CFA251FC79150EA3B5CAAAD0A65600251 (void);
extern void ARCoreProvider_UnityARCore_imageTracking_destroy_m4EADD7AB13CC0F86B4BED490FEB521BE7534AC20 (void);
extern void ARCoreProvider_UnityARCore_imageTracking_acquireChanges_m4BF017C6A4F52A546B214540DF7D2F263D1885F6 (void);
extern void ARCoreProvider_UnityARCore_imageTracking_releaseChanges_m4BD377E7CB1BA6FFA63B744B00DD92144E94AEDF (void);
extern void ARCoreProvider_GetNumberOfTrackedImages_mE359250D65D55B04C3089C5AD8D720407D7F37C3 (void);
extern void ARCoreProvider__ctor_m6D2C8A015DB244077C5C82EA873B7AEE23DC926C (void);
extern void ARCoreLoader_get_sessionSubsystem_mB719854EF11ED7529B060881E078347967098818 (void);
extern void ARCoreLoader_get_cameraSubsystem_mA7423C7021CC4D3D47589BB1374C8C3D2FFAFC5F (void);
extern void ARCoreLoader_get_depthSubsystem_m3ED669F0F0517001E746FC4E4CC5E1B5816DD9CD (void);
extern void ARCoreLoader_get_pointCloudSubsystem_mEE843FA3D3213B8DA65E81532BE10C19217EDFAB (void);
extern void ARCoreLoader_get_planeSubsystem_m795EB938590D1B0D3ACF396C6EF51CCE279D311A (void);
extern void ARCoreLoader_get_anchorSubsystem_mC42CCAA64AA29DB7E7AC0D76B6D3979935807A31 (void);
extern void ARCoreLoader_get_raycastSubsystem_m908C2D2156F387879196FA0ADF530EB02366F6BF (void);
extern void ARCoreLoader_get_imageTrackingSubsystem_m3DBC8FB6CAEF6D6BEFC56115C1611BB515647190 (void);
extern void ARCoreLoader_get_inputSubsystem_mC96B3C65EF908BE1E4B5CC5F3F9C27631C314242 (void);
extern void ARCoreLoader_get_faceSubsystem_mBE92A8D0FA340647A4BBED7D37EE3E753A4022D5 (void);
extern void ARCoreLoader_get_environmentProbeSubsystem_m3BF82CA0654C4963BAB3C41DC8CD3CE488C7135E (void);
extern void ARCoreLoader_get_occlusionSubsystem_m20523A3C486A422505823C97EABC56AF165358F0 (void);
extern void ARCoreLoader_Initialize_m7958140B0AD77F780834F1CB4CE76468A1A4EB1F (void);
extern void ARCoreLoader_Start_mC2E2B5ED50FD4747F02545B18115DDC30DA4F924 (void);
extern void ARCoreLoader_Stop_mF234BEB25E607B591CADD9C0AAF511F5F162C9DD (void);
extern void ARCoreLoader_Deinitialize_m34E45DD93616D53D953992DB28C1C379CE69DEAF (void);
extern void ARCoreLoader__ctor_mA1E8D67717DEB72A3CEEEF54E8F360B2EAAAF1AF (void);
extern void ARCoreLoader__cctor_m0D2C1C4AD74B7264423250519676B7F9D7D0C86E (void);
extern void ARCoreLoaderSettings__ctor_mBBD3002FC45E720F566E60290D60C3C332739AC9 (void);
extern void ARCoreOcclusionSubsystem_Register_m7F9EB8509E889D4523D85A5A98301E7EC83F7E85 (void);
extern void ARCoreOcclusionSubsystem__ctor_m16B5A395AC5676532E8AEBF4360F919348883755 (void);
extern void ARCoreProvider__ctor_m740EFF84FFF6B9B2C7C4FD5F28A95A78568D2AE7 (void);
extern void ARCoreProvider_Start_m75EEF8FA8584F7501A752662804FB39A888552D7 (void);
extern void ARCoreProvider_Stop_m748C215A4A853B42DFC52A223F826A9DD7E638F2 (void);
extern void ARCoreProvider_Destroy_m1FB0B254074AF688C79F6CBC4F313AF374003F47 (void);
extern void ARCoreProvider_get_requestedEnvironmentDepthMode_m7285AE1918DD6EF69250D54747E7620CA6FA9166 (void);
extern void ARCoreProvider_set_requestedEnvironmentDepthMode_m4284137A5F92528C355BB811D43624F722EE3CD3 (void);
extern void ARCoreProvider_get_currentEnvironmentDepthMode_m13ECB4DF298101AD41CCF0A5EF8F465B029F8B61 (void);
extern void ARCoreProvider_get_environmentDepthTemporalSmoothingEnabled_m0AAA4E5B48A1217AEC938D2E6F51982382737322 (void);
extern void ARCoreProvider_get_environmentDepthTemporalSmoothingRequested_m7BE8092F27B40CE259C447BDFA11D3B031EA0645 (void);
extern void ARCoreProvider_set_environmentDepthTemporalSmoothingRequested_m35D777A806EBC1F3380775E872707026BF49016F (void);
extern void ARCoreProvider_get_requestedOcclusionPreferenceMode_m491F11E20BABA570FC52CE167FFFCEA56FDAFE15 (void);
extern void ARCoreProvider_set_requestedOcclusionPreferenceMode_m4948A8F68DC62CCAC078D15F4D9CE55E4F8007BB (void);
extern void ARCoreProvider_get_currentOcclusionPreferenceMode_m124D76BDB3DBD823AC8C8676D741AC742DD47A2B (void);
extern void ARCoreProvider_TryGetEnvironmentDepth_m78238FE8CD7D4AAE21D15E288563FFFA94E50890 (void);
extern void ARCoreProvider_TryAcquireEnvironmentDepthCpuImage_m425D9F8AE24B409C5BDF7E959B30245A9336AE23 (void);
extern void ARCoreProvider_TryAcquireRawEnvironmentDepthCpuImage_m438FEF338186F8B8D9CF5587B30318168D3C7407 (void);
extern void ARCoreProvider_TryAcquireSmoothedEnvironmentDepthCpuImage_m90DF281C8FBC87903E3BA776A46F3B6F0F11C22F (void);
extern void ARCoreProvider_get_environmentDepthCpuImageApi_m61FB1F4AE16ABCA3AA8A07DCCBB149603E7137B7 (void);
extern void ARCoreProvider_TryGetEnvironmentDepthConfidence_m944FA850E27746FE2B692979C35BC4EB1C3B21BC (void);
extern void ARCoreProvider_TryAcquireEnvironmentDepthConfidenceCpuImage_mB3A215787E01AF6E90DE3E35D23A3E5EFA627F83 (void);
extern void ARCoreProvider_get_environmentDepthConfidenceCpuImageApi_mA3E737368FBC38050E3620505E826949899B1F36 (void);
extern void ARCoreProvider_GetTextureDescriptors_m93940B7502A6A2E218259EF3B26C2ABEA5114070 (void);
extern void ARCoreProvider_GetMaterialKeywords_m830087C8188384A42E71AAA5B2B09C97BCA5D73A (void);
extern void ARCoreProvider__cctor_m5BACEE5E109F2DD4DC64190D2420BDD5AEBD5036 (void);
extern void NativeApi_UnityARCore_OcclusionProvider_DoesSupportEnvironmentDepth_m9FE0E9226362ADC8A84B665DDB63F6D866412EDD (void);
extern void NativeApi_UnityARCore_OcclusionProvider_Construct_m4569505915E0A123538C6C77D1CF2255DD2F781A (void);
extern void NativeApi_UnityARCore_OcclusionProvider_Start_mECE64FEE6F26E064D5458675944396A8D6B0DB04 (void);
extern void NativeApi_UnityARCore_OcclusionProvider_Stop_mC3662D7828F0D4C8D334A79EF7F30D83AD3B3D7D (void);
extern void NativeApi_UnityARCore_OcclusionProvider_Destruct_mEA7D53D06C212F7BD6FA5B5AF3DA3E9ABA91519A (void);
extern void NativeApi_UnityARCore_OcclusionProvider_GetRequestedEnvironmentDepthMode_m3BEFFE79231EADEFDB14CB7ACACBEB4C89C8BA01 (void);
extern void NativeApi_UnityARCore_OcclusionProvider_SetRequestedEnvironmentDepthMode_m61C9D8DBC93CAB41F9DCCD8C4076C4FB0174C4D4 (void);
extern void NativeApi_UnityARCore_OcclusionProvider_GetCurrentEnvironmentDepthMode_mC39A09403F460011D6DD5E816F0D6C2496DFE38E (void);
extern void NativeApi_UnityARCore_OcclusionProvider_TryGetEnvironmentDepth_m3F29D3D4DD60A77D32BA5405C41E45F25991C874 (void);
extern void NativeApi_UnityARCore_OcclusionProvider_AcquireTextureDescriptors_m817E98B17DE94B409FE6D826283A31D748FA2C1E (void);
extern void NativeApi_UnityARCore_OcclusionProvider_ReleaseTextureDescriptors_mD4DD06BF3DC2D03D31F450BE3DFFB06C371986F3 (void);
extern void NativeApi_UnityARCore_OcclusionProvider_IsEnvironmentDepthEnabled_m1ED084E2A34A0C10C2432A94970408B5BBC5FABA (void);
extern void NativeApi_UnityARCore_OcclusionProvider_GetEnvironmentDepthTemporalSmoothingEnabled_mEC9572769528B85914D99CFBA9241E4B5FBCDECB (void);
extern void NativeApi_UnityARCore_OcclusionProvider_TryGetEnvironmentDepthConfidence_m11BB197D49D6ECBA23EBDAC772C15AC31FBCDC3E (void);
extern void ARCorePermissionManager_IsPermissionGranted_mF4555A12A77D31E4D3E29E481DF3F53F05F2B836 (void);
extern void ARCorePermissionManager_RequestPermission_m85D7447D4D95D74B4ECC898D56295392264B1D8C (void);
extern void ARCorePermissionManager_CancelPermissionRequest_m6028D958E440B6FF21DE814A37F0EEC9E908539E (void);
extern void ARCorePermissionManager_OnPermissionGranted_m6F977C06DFFDA17DB3D82BDD28EBA57ADAE68FD5 (void);
extern void ARCorePermissionManager_OnPermissionDenied_m84D8B52092834DE931A3D6F8C0AE7F56FAA75091 (void);
extern void ARCorePermissionManager_OnActivityResult_m7FBB087DEC75D2315A9D06FA5D4A5C238096BB2B (void);
extern void ARCorePermissionManager__ctor_m995B52F67EB43FB3BBDC858DFDA0C6FD5D1293AC (void);
extern void ARCorePermissionManager_get_instance_m344C4B4A237CDB080CBE74E69C78881244B999EC (void);
extern void ARCorePermissionManager_get_activity_mD8500EAC35737E2F9630F84D03C4916BBEB9541F (void);
extern void ARCorePermissionManager_get_permissionsService_m255C1DBB66AD92E34654754B7CE146C33037F165 (void);
extern void ARCorePlaneSubsystem_RegisterDescriptor_m1D8C1EC22E5157CA6C5537E1453C575D4F56E232 (void);
extern void ARCorePlaneSubsystem__ctor_m1F0CD932244651913C931C181530A3FC960B65D7 (void);
extern void ARCoreProvider_Start_m7595EF6D1E7A0E2C7D6ADC620BDA5FDB21320E8E (void);
extern void ARCoreProvider_Stop_mA8989B03BBF873DA74AD51D17C3E7AA0A5A8C5EF (void);
extern void ARCoreProvider_GetBoundary_m8ED3BC09C3970185A93CEB06B1B2A63868CC7588 (void);
extern void ARCoreProvider_GetChanges_mD605EB86F5978B7A8B2DC635138BA1ABB95634EF (void);
extern void ARCoreProvider_Destroy_m89EE9E1820BAD6DFDD0F87BCE4515DDAB3E89E57 (void);
extern void ARCoreProvider_get_requestedPlaneDetectionMode_m39697C78C2C6A8644CC860B4D94C4C68B647B19F (void);
extern void ARCoreProvider_set_requestedPlaneDetectionMode_mF523936701717C8EC4B55E19024933A549A535D3 (void);
extern void ARCoreProvider_get_currentPlaneDetectionMode_m2C19714D6760548615A29FC3D3945A008EC1A973 (void);
extern void ARCoreProvider_UnityARCore_planeTracking_startTracking_mF85B05ED58756FEC2107D79C46EA662C810A06D2 (void);
extern void ARCoreProvider_UnityARCore_planeTracking_stopTracking_m14B58477D7EA73778B4155234A5D9633B01C7371 (void);
extern void ARCoreProvider_UnityARCore_planeTracking_acquireChanges_m988991B752D77BACF248256DF174B256DC344A60 (void);
extern void ARCoreProvider_UnityARCore_planeTracking_releaseChanges_m906202B7B7B557FDFF3E5BEA33C3F3913D1295A8 (void);
extern void ARCoreProvider_GetRequestedPlaneDetectionMode_m23F5093A3A701E84AA42C0EA5D9FBAAA4471A74F (void);
extern void ARCoreProvider_SetRequestedPlaneDetectionMode_mD8FD0B8CE75A9514BE694DC9BC71BC7001E4E672 (void);
extern void ARCoreProvider_GetCurrentPlaneDetectionMode_mA46AA7FAC6CE7B1DE8AFC3999F686806A932FD97 (void);
extern void ARCoreProvider_UnityARCore_planeTracking_destroy_mB285E24433D266D2116F3C1C66F10C5F43899114 (void);
extern void ARCoreProvider_UnityARCore_planeTracking_acquireBoundary_m636E3DE646695BE41F746C7F282EB086906DD5C7 (void);
extern void ARCoreProvider_UnityARCore_planeTracking_tryCopyBoundary_m720CFCE25004DD87E94A5ECF8C9D67CA9C0C93B2 (void);
extern void ARCoreProvider__ctor_mCBE59A924070B5444C25F0DDBE7F1D05E118EAA2 (void);
extern void FlipBoundaryWindingJob_Execute_m1A3A43413FBCA81A1ABDC145DC35AA533E86BC9D (void);
extern void FlipBoundaryHandednessJob_Execute_mBDA83F26E0D4DE330FB14E62DDFB7A52219B8CA9 (void);
extern void ARCoreRaycastSubsystem_RegisterDescriptor_mADCD846F7C8BDA4776E7A944C09B606587F86F0B (void);
extern void ARCoreRaycastSubsystem__ctor_mC3D3E27326C788CF7404644A8041D2BCB6FFF2ED (void);
extern void ARCoreProvider_Start_m2C3CFF618C62BC4DB9A1DAB8A23B0407E0E1FE88 (void);
extern void ARCoreProvider_Stop_m6DE4D49F0BD5C936116DCDD2371700266568E58F (void);
extern void ARCoreProvider_Destroy_m3FDF8D419C9B3A7BBBE1ABA88A4588DCCB2C372B (void);
extern void ARCoreProvider_GetChanges_m629D4E64616243D68CA87318EC5346E4122FED72 (void);
extern void ARCoreProvider_TryAddRaycast_m30632AD57E0DAD3CE858EAB81353D8FE151F4541 (void);
extern void ARCoreProvider_RemoveRaycast_m8A81C2C1BAEFEA1EF19764853ABC215D5C67F866 (void);
extern void ARCoreProvider_Raycast_mEEE649E1F6280D29A8623F617D87E406E5C86509 (void);
extern void ARCoreProvider_Raycast_mBF08914AA765BA9C84A022D8E0F04A8B3DCAB105 (void);
extern void ARCoreProvider_UnityARCore_raycast_acquireHitResults_mA0F13622676EB1103DC27EC0008A96A600136388 (void);
extern void ARCoreProvider_UnityARCore_raycast_acquireHitResultsRay_m9B14887BA2FB78A720D7C498FD37E73CB6EE3A25 (void);
extern void ARCoreProvider_UnityARCore_raycast_releaseHitResults_mBB0F35E0A169BDBBDBB5D57924F0E83E8E48A613 (void);
extern void ARCoreProvider_UnityARCore_raycast_tryAddRaycast_m21FF95D0D80788ABEC820730773CF2DABF292F83 (void);
extern void ARCoreProvider_UnityARCore_raycast_removeRaycast_m290E26EDC14DF774F802E69D6466B0CD27E4CD7E (void);
extern void ARCoreProvider_UnityARCore_raycast_startTracking_mDA579FB0BAEFCB2F3AA80EF8BA0D0C4D1D751E28 (void);
extern void ARCoreProvider_UnityARCore_raycast_stopTracking_mF241317EEEA2DBFB896B51DA2F1334D3AD894553 (void);
extern void ARCoreProvider_UnityARCore_raycast_acquireChanges_m54B560915C02A18FEFD93A9FF5F5DE8D60B0357C (void);
extern void ARCoreProvider_UnityARCore_raycast_releaseChanges_m2F0D70AF2BFC888AC491408F55D5665EE21304DE (void);
extern void ARCoreProvider_UnityARCore_raycast_destroy_mBE0BD44C936F2963A9CDC87BCE5C987812FC827B (void);
extern void ARCoreProvider__ctor_mCEED9DE66DCD9BB6AB1F30F80AD6044DF568C209 (void);
extern void ARCoreSessionSubsystem_OnCreate_mF8F1CEBDBBA6C5EB2DE36E7DF3FC114415BB4536 (void);
extern void ARCoreSessionSubsystem_ConfigurationChangedFromProvider_mCDCA9342D2919733A299E64B3DBF4F908C048722 (void);
extern void ARCoreSessionSubsystem_SetConfigurationDirty_m589BAB1CB66E9C17CFEF26FADC620AC122AAFA10 (void);
extern void ARCoreSessionSubsystem_get_session_m30DEF5F507F37ADBA33992040DF96C29A035DE20 (void);
extern void ARCoreSessionSubsystem_StartRecording_m5FEAD096A36F54B317747AC4C7CDFCEED5D73930 (void);
extern void ARCoreSessionSubsystem_StopRecording_m80EBB7C1996C30ACBCF2AD953FA340F2CCC9D00A (void);
extern void ARCoreSessionSubsystem_StartPlayback_mAD4B827E1C37E2BDC536D93A624A8CA4DFA1FBBE (void);
extern void ARCoreSessionSubsystem_StopPlayback_mB7CCEABF538062686665646E7CD89AA6DAAADD63 (void);
extern void ARCoreSessionSubsystem_get_recordingStatus_m407E01258C262F5FF877D8E4BCB52C97AF3431D9 (void);
extern void ARCoreSessionSubsystem_get_playbackStatus_mB849FC26DC9496B81C9B3FEE9BE3352AFD2AA9C7 (void);
extern void ARCoreSessionSubsystem_add_beforeSetConfiguration_mA1649A6E88E8D431B34DC24E6E8C171D8F957B6A (void);
extern void ARCoreSessionSubsystem_remove_beforeSetConfiguration_m21286B32C3760C722A3820C34A5F202C6CD67F16 (void);
extern void ARCoreSessionSubsystem_RegisterDescriptor_m66D5092356E8F4753B04BA36E9FEDBD67C767320 (void);
extern void ARCoreSessionSubsystem__ctor_m5B001429D8D5F84602D8D9B80B1CF759BA0E108E (void);
extern void ARCoreProvider_SetPlaybackDataset_mA5007E7F4190891CBCD26808D1C384940F65D111 (void);
extern void ARCoreProvider__ctor_m4ED89ECF6C019BEEED85579209B5949941D4829D (void);
extern void ARCoreProvider_Start_mEE70994E30DE0D74573DC0B4581D478EEF5214CB (void);
extern void ARCoreProvider_Stop_m6289CF316BA0BECFC520FFAC102ED245BD242711 (void);
extern void ARCoreProvider_Update_m1DD2D8BEB10DDDBE586ADF2833C7E31AE18776CB (void);
extern void ARCoreProvider_StartRecording_m2083E756A5F0C25B984A2D457DCC71790A190D26 (void);
extern void ARCoreProvider_StopRecording_m7C7E4B579AA98397137BCC25C8B4F69A88AEE115 (void);
extern void ARCoreProvider_get_recordingStatus_mFDA170E040FD1AAFBCC0BB35A52E23030A0A7D76 (void);
extern void ARCoreProvider_get_playbackStatus_mB42156FBF5149C57277A8D5EA77D414328411B4A (void);
extern void ARCoreProvider_get_session_m2FF87072719F576B2C36DF66A1AAA185E493646C (void);
extern void ARCoreProvider_GetConfigurationDescriptors_m4738ABEFDAB96DFFA917C9DD12113E29A2D955E2 (void);
extern void ARCoreProvider_add_beforeSetConfiguration_m7782F6ED649007DE9D8526072F90B03335EE5E24 (void);
extern void ARCoreProvider_remove_beforeSetConfiguration_m31C541FCAAC258A6529E5D1F67633C7D0C241840 (void);
extern void ARCoreProvider_SetConfigurationCallback_mF0CB81541E8AED4D6D8C13EEFC28F02C044632BA (void);
extern void ARCoreProvider_Destroy_mC45316C60AC9FB9BF1418DB042F2BB8FA09D44ED (void);
extern void ARCoreProvider_Reset_m4904D98C5694D128705E3B4FFB7B2806340A11AD (void);
extern void ARCoreProvider_OnApplicationPause_m602CC93754E61B3EBBF3BE77BC1C6DFCE607E0B9 (void);
extern void ARCoreProvider_OnApplicationResume_mC57FD74CC28674FD9F27CDFC89D48544101797CA (void);
extern void ARCoreProvider_GetAvailabilityAsync_m979FCD1FFFAFB2C5040E4BFE327677F3C80D6CBF (void);
extern void ARCoreProvider_InstallAsync_m168BAF5F2DE9C10641C221D995E21AB9788CF181 (void);
extern void ARCoreProvider_get_nativePtr_mF93BE814C215A6AFC2F61A7674370E8B48C58CD5 (void);
extern void ARCoreProvider_get_trackingState_mEC4ED0D514E183E746B13D33E18013F3C262380A (void);
extern void ARCoreProvider_get_notTrackingReason_mB7E41389B11F85EEFD09F70CD3C9547690E9B1DC (void);
extern void ARCoreProvider_get_requestedFeatures_m6FEF431FD8F404DFB3C4D123CE6F29FFD0ECF581 (void);
extern void ARCoreProvider_get_requestedTrackingMode_m5A589D5586E0010B62CBDC7C1A2A904F53920E24 (void);
extern void ARCoreProvider_set_requestedTrackingMode_mE071EC7020FD5FE2DE9716583CDC0785B7C294D4 (void);
extern void ARCoreProvider_get_currentTrackingMode_m7787C51BDAEC4C0BECCF949E5167EA9CE2B167F2 (void);
extern void ARCoreProvider_get_matchFrameRateEnabled_m69389E0BF3F0C505377DD41CFD76D6A1171A4336 (void);
extern void ARCoreProvider_get_matchFrameRateRequested_mFAA42AF98B9E52864A2580A4BA69A7F032AA8DD1 (void);
extern void ARCoreProvider_set_matchFrameRateRequested_m4E81D03C9640EC1026C30982C6F9E4D51A3F40F3 (void);
extern void ARCoreProvider_get_frameRate_mA33383747900A3E5A6303CD8B1369E59EE982D25 (void);
extern void ARCoreProvider_OnApkInstallation_mCF471D5BC5E1FE4740696294320C033495CF3274 (void);
extern void ARCoreProvider_OnCheckApkAvailability_mBEC148833875195FFBB34791C412F2DBA1BC2009 (void);
extern void ARCoreProvider_CameraPermissionRequestProvider_m19E1C66D509D30992AD54875823B7257595D9B0C (void);
extern void ARCoreProvider_IssueRenderEventAndWaitForCompletion_mE8F32C2E68F7720D35BD8E2588D711F37DEDA45F (void);
extern void ARCoreProvider_CreateTexture_m2276885716E7619E56DE079F26F3BCD9A8DB0F19 (void);
extern void ARCoreProvider_DeleteTexture_mA1B6EF45277ED86092FA33651D3E71B757D2765E (void);
extern void U3CU3Ec__cctor_m4B21997B813DCFD2418A9CF0A213B5820118E8DE (void);
extern void U3CU3Ec__ctor_m3637F367C80968CD4BF62CC49619A5E47A405FF7 (void);
extern void U3CU3Ec_U3CGetAvailabilityAsyncU3Eb__24_0_m1C4FCBC446E6CE9AA042D2D5C302A7C55C73917C (void);
extern void U3CU3Ec_U3CInstallAsyncU3Eb__25_0_m733F2E5B5EE87A2217388F6D03CE104E7EEFF2EA (void);
extern void U3CU3Ec__DisplayClass49_0__ctor_m117E39D5AB1AD3E8F62AAEBF1A7CBF5FD674C8B8 (void);
extern void U3CU3Ec__DisplayClass49_0_U3CCameraPermissionRequestProviderU3Eb__0_m1552073AEBE7BE5A0FEEFC21A5474D71687EC66F (void);
extern void NativeApi_UnityARCore_session_getNativePtr_m18528F6B459FF784ADD0562AE6A425A8BE2079B7 (void);
extern void NativeApi_ArPresto_checkApkAvailability_m9C8FA059F7751262D17FF19C4C7E1757AD68980F (void);
extern void NativeApi_ArPresto_requestApkInstallation_mDA72B2447273CF392307BD823556F2F6F2886454 (void);
extern void NativeApi_UnityARCore_session_update_mA2AA5E63F4C39138285F49884CC3A104DC959371 (void);
extern void NativeApi_UnityARCore_session_getConfigurationDescriptors_m7BBA66A8BAA48B7EFCAF33F5404836450A8325EE (void);
extern void NativeApi_UnityARCore_session_construct_m308B165426FBA7932F9221609BE5C8A91F5A62FA (void);
extern void NativeApi_UnityARCore_session_destroy_m41490C64B5D8D4CEDC8FBEC9BB2D06A8A070CE1C (void);
extern void NativeApi_UnityARCore_session_resume_m4D4EEAE9E869D3EADFE31BC5E53F428E09D148F9 (void);
extern void NativeApi_UnityARCore_session_pause_mC45FB867DF9B9ACF5F09233C239018D9BA9BCA1E (void);
extern void NativeApi_IsPauseDesired_m58F22A291F4C7D571B0121D9CD199516365FAACC (void);
extern void NativeApi_UnityARCore_session_onApplicationResume_mCF25AF0149C9632F2F46316BFDCF421A452690DD (void);
extern void NativeApi_UnityARCore_session_onApplicationPause_m7A1811EB41333D98CD458BD5595D6E4B0FC8CC3A (void);
extern void NativeApi_UnityARCore_session_reset_m37E67AC859A2BA9139438196FC1EC790A2080E4E (void);
extern void NativeApi_UnityARCore_session_getTrackingState_mF9F59BDD77BD9326255CEBF8BA40EA5FAD8FC676 (void);
extern void NativeApi_UnityARCore_session_getNotTrackingReason_mFF59C5B1997EE25016695ACF56549C193A44A240 (void);
extern void NativeApi_UnityARCore_session_getRenderEventFunc_mC698B51674293447F9E18BBA86B58F16CC283BEC (void);
extern void NativeApi_UnityARCore_session_setRenderEventPending_m1E6CE4FD00F7E301AB5ED17152975F5015DDD658 (void);
extern void NativeApi_UnityARCore_session_waitForRenderEvent_mFBBAF126EA997ADD267458373EFC9E9AFCCD2F21 (void);
extern void NativeApi_UnityARCore_session_createTextureMainThread_mCC71FCAD112F1CBEBC904C66084E9FA93446E540 (void);
extern void NativeApi_UnityARCore_session_deleteTextureMainThread_m05FD30DA04AE9427BADD4C51E9CB30F0DB93985D (void);
extern void NativeApi_UnityARCore_session_getMatchFrameRateEnabled_m89658B22B04EE28F16670CEF04C012CCB1B546E4 (void);
extern void NativeApi_UnityARCore_session_getMatchFrameRateRequested_mB7196E95BFA550632211A1AC0B52153BC88DB410 (void);
extern void NativeApi_UnityARCore_session_setMatchFrameRateRequested_m7A51E698032951BDFAF6AA2D9346E92A5A9DD163 (void);
extern void NativeApi_GetCurrentTrackingMode_m62E19089CE7EEA9504C5E6A4DAE9CE9CF8554126 (void);
extern void NativeApi_UnityARCore_session_setConfigurationDirty_mC6809AB17D1BB813BB6E6E9AD5084D88BBAF495D (void);
extern void NativeApi_UnityARCore_session_setConfigCallback_m80A0DF033301F265194CA1507B45BF9333693120 (void);
extern void CameraPermissionRequestProviderDelegate__ctor_m2FA75F6A973B17CA18B4BF6BAE20702655339E27 (void);
extern void CameraPermissionRequestProviderDelegate_Invoke_m05261FB991CE8933EA8041F56122E659E160E68E (void);
extern void CameraPermissionRequestProviderDelegate_BeginInvoke_m7A8B194D3BB3C4F6431A5D462099362A1AB8F3CA (void);
extern void CameraPermissionRequestProviderDelegate_EndInvoke_mB4D4796BA3D426AA561782B3BF4283FC456A207D (void);
extern void CameraPermissionsResultCallbackDelegate__ctor_mEDD148B83FBC25FEBD30084B824D552055F1A1FC (void);
extern void CameraPermissionsResultCallbackDelegate_Invoke_m1BB16747C0F77B3FB2038F05EFB05E21F9100AE4 (void);
extern void CameraPermissionsResultCallbackDelegate_BeginInvoke_mA3D872A3C5F09EAD519B46A11F43670BFBAE25AA (void);
extern void CameraPermissionsResultCallbackDelegate_EndInvoke_m83C4C5BD7B5353B02F0C0F73FE20B85A2F507C72 (void);
extern void ArPlaybackStatusExtensions_Playing_mAD99AB9C7F2F95BF0E0DEE2B7B47AE416BE4491A (void);
extern void ArPrestoApi_ArPresto_update_m3256D92E173DBE093C9E97E87D75D233B1EC11DC (void);
extern void ArRecordingConfig__ctor_mFB056D108F9F2F1F95700E06ECF338AB67DE5543 (void);
extern void ArRecordingConfig_FromIntPtr_m2B13A3F90996565E8CC5D9BE868CFAADC49B9A76 (void);
extern void ArRecordingConfig_AsIntPtr_m1ACE1150822FE9A799E42E6FEEC115FC67DB59F5 (void);
extern void ArRecordingConfig_op_Explicit_m7ADA4645EDCFA59AA953418CD30A92117AF38D2A (void);
extern void ArRecordingConfig_Equals_m700366138DCCEF48563D46A885A66749A7EAFE8E (void);
extern void ArRecordingConfig_Equals_m46B9FD5ED0BA1F8EB0DE3B0263F86C4EC34D83E5 (void);
extern void ArRecordingConfig_GetHashCode_mC90EE60365D20D0958B7A9AA756D964107BAAA4D (void);
extern void ArRecordingConfig_op_Equality_m7568024F054238F7F637EA2C494C6F72D01837EA (void);
extern void ArRecordingConfig_op_Inequality_mB608AB4CA58A313355257660CB6EB7ED2D862BFE (void);
extern void ArRecordingConfig_op_Equality_mDE220C849425194A089D8B279FF4E7114F177F50 (void);
extern void ArRecordingConfig_op_Inequality_m89D37B4D875994BCE36238FCE735F9BB792CACBF (void);
extern void ArRecordingConfig__ctor_m1FC781FAD6F26382FFB4BD225C6E97D3404F6201 (void);
extern void ArRecordingConfig_Create_mF876B3E757C5DCDA0F8FA7FD18167CCF1F9FB7F3 (void);
extern void ArRecordingConfig_Dispose_mC038E6DF18BFFC5CF53C92981959209499F60F8A (void);
extern void ArRecordingConfig_Destroy_m9168B9B06A55D31577C5A806D3079A5E3345E314 (void);
extern void ArRecordingConfig_GetMp4DatasetFilePath_m4914727C5AB42734B16097D5922DCAD6B018C230 (void);
extern void ArRecordingConfig_GetMp4DatasetFilePath_mBF7DB775C7EEDFC816B86555680E0AAF261133D7 (void);
extern void ArRecordingConfig_SetMp4DatasetFilePath_m4AB1F048A648E5A18330B326231950FCFC562C7D (void);
extern void ArRecordingConfig_SetMp4DatasetFilePath_mD9C075DB1A8848BE801670FC04D95274E61D7C91 (void);
extern void ArRecordingConfig_GetAutoStopOnPause_m9A7A960FBD8F80AEE2BE64F948645BB713FC6589 (void);
extern void ArRecordingConfig_GetAutoStopOnPause_mB986EEC6865278BB57D77DA3381FED172A5932E6 (void);
extern void ArRecordingConfig_SetAutoStopOnPause_m8EA51CD64BE495E44E17F25BF25FDF16496B317E (void);
extern void ArRecordingConfig_SetAutoStopOnPause_mF534EB038A48E1C22DAE9977515FB91A1B991260 (void);
extern void ArRecordingConfig_GetRecordingRotation_mDFD704E14CEC23CC39C9AD1C787CF9AF2D46204B (void);
extern void ArRecordingConfig_GetRecordingRotation_mB2FCAC63699D31840644B992A0F52CC1086DEF50 (void);
extern void ArRecordingConfig_SetRecordingRotation_m0AB920806E19B8313DA5D2D9A5467F753FA7DCEF (void);
extern void ArRecordingConfig_SetRecordingRotation_mC4C443102073377E4DC2A1A4A9A77A4DFE1ED838 (void);
extern void ArRecordingStatusExtensions_Recording_m4746E133574003F69F5E94E619FB09AE49842116 (void);
extern void ArSession__ctor_m71349A9234C25226F1128347856956BE42878C23 (void);
extern void ArSession_FromIntPtr_m02B16B0BF3A816FFBE101F779596072294466A73 (void);
extern void ArSession_get_Null_m33442F5190387AF6E82D1A2C4FEA4E1039AC9892 (void);
extern void ArSession_get_IsNull_m05BB739DEE4AF829CC1D2EFE8E0CEC2771CF01C4 (void);
extern void ArSession_AsIntPtr_m73675297C396024E6179B8D4476CE2DE242B662A (void);
extern void ArSession_op_Explicit_mE2F5B79090DE262FB4F7A2AFF99758ECD81FCC58 (void);
extern void ArSession_Equals_m23B510198BAD7E0B9A1484ECFCF5BF41B7DC7748 (void);
extern void ArSession_Equals_m55E7D8A043929991C0F0874F2AC63F16B4EF8122 (void);
extern void ArSession_GetHashCode_m5D362BBA3F33B01AC2C68D04FD433DB71CBDF21E (void);
extern void ArSession_op_Equality_m3AB32704499A14999D502E8D8F6CDDD6F1B71A59 (void);
extern void ArSession_op_Inequality_mC548DD6078871311DB73111CA3E5451D72CA0EB1 (void);
extern void ArSession_op_Equality_m9835A20A78F5BBF0A9DCDAD84CCDF5BFF002D664 (void);
extern void ArSession_op_Inequality_mBC52B05552579BAECE16E3035658E31D944584BA (void);
extern void ArSession_SetPlaybackDataset_mC0C2D822F6267EA04C3586B10AD7BA96B48D5971 (void);
extern void ArSession_SetPlaybackDataset_m9B1A29260BE38548535400804390C4C4CFD85E2C (void);
extern void ArSession_get_playbackStatus_m532E4AB78BBE269DD6635E21E7C84BAEF28FEF9B (void);
extern void ArSession_GetPlaybackStatus_m8117D2E450374C62CD05B68EE857CEB683923623 (void);
extern void ArSession_StartRecording_m4E1E63E462A28E2582928B7780E77FC91A6B74AD (void);
extern void ArSession_StartRecording_m2C3C67FCFF770590DEDC9F799D31CF6A09CB58AE (void);
extern void ArSession_StopRecording_m29D650C26A1CF6074DD6EAB28D811306CEFFC674 (void);
extern void ArSession_StopRecording_mFF88E937C7E8058EE164060D4F94911D6C074F24 (void);
extern void ArSession_get_recordingStatus_m8D8B1CE7BD5A413E8EAACFFA910548B69273AF3C (void);
extern void ArSession_GetRecordingStatus_m819E2CAD1EC3110AB6F9C41D2B92DEA838F55CCF (void);
extern void ArString_ToString_m58566F4EAB0BEC069B61795B57AF890F9AF05961 (void);
extern void ArString_ToString_m245F2065FC557EDD10CD96632E4BD5B2A7CE63FC (void);
extern void ArString_Dispose_m2D9E29EC3E98FE828F7FEAB806341561A8A193AA (void);
extern void ArString_CalculateNullTerminatedByteCount_m89C0AAFB8D621F7D1A92FF9334DC8CA6FA526EB1 (void);
extern void ArString_Release_mCA1FAA5CAAD96A68BA69EB683E131907F3C9EE35 (void);
extern void HashCodeUtil_Combine_m1421C2AAB5E1692D25706288CE0B50FFAF6826BD (void);
extern void HashCodeUtil_ReferenceHash_mB32987352C0437C837AC7FE8C2C92DB5B0AD29E1 (void);
extern void HashCodeUtil_Combine_m108BBB0A07412E21BE8AFACC56F8E36236319DDC (void);
extern void HashCodeUtil_Combine_m168BC9083D898F7C5E85F17A6463D5FCE21FFFFC (void);
extern void HashCodeUtil_Combine_m1993DD761EBEC51AB4C18A4E375AE3F23A2EAACB (void);
extern void HashCodeUtil_Combine_mEFBEAAA76E18521EE84072BED8114B20E0B1DF9F (void);
extern void HashCodeUtil_Combine_mBF0D491AE9389CA58E90C51C1F2EFB5AE9513552 (void);
extern void HashCodeUtil_Combine_m96524041136F1BEF469012EF409741D043DC3A7E (void);
extern void FlipVerticalJob_Execute_mD0741C407C244AEE8696FF8D8EAB2DCE8EB676EA (void);
extern void ConvertRFloatToGrayscaleJob_Execute_mD576D88F5CA87BA7658352FF375620774927BE7E (void);
extern void ConvertBGRA32ToGrayscaleJob_Execute_mA1B5222E6CE669BFCABC309634A1058FD0318D49 (void);
extern void ConvertARGB32ToGrayscaleJob_Execute_m273542FF7E241207A99C0FC3FAF3070825F9E4FD (void);
extern void ConvertStridedToGrayscaleJob_Execute_m74E512D24E8A627CC8C269B50D359650929B44CF (void);
extern void ConversionJob_Schedule_mDE6FB8E5ABE2AAFD613FD3553FC7B81FE5EB9639 (void);
extern void InputLayoutLoader_RegisterLayouts_mE774BA44F9BC72D9D002EF536CBE49AEDB13745C (void);
extern void InputLayoutLoader__ctor_mE5E843EAFDD7AFCF01BC123D17952E482AD55D23 (void);
extern void ManagedReferenceImage__ctor_m7909E7255E06256DB90E301F1C5A75A834B60738 (void);
extern void ManagedReferenceImage_ToReferenceImage_m6E37905684A97C30C332AEB8BD57E67128CBF3F1 (void);
extern void ManagedReferenceImage_Dispose_mED4FBEB981766408C3E96556E5C311B893760C50 (void);
extern void ManagedReferenceImage_AsSerializedGuid_m11A3D420BD4FA6196F7AE1AAC40527B3FD393073 (void);
extern void ManagedReferenceImageExtensions_ToNativeArray_m268E671B0B45E023DD3466058ECFCEFCBEE4A134 (void);
extern void NativeObject_ArePointersEqual_m78CCE35C1493CF89AD67B679A45687843777B576 (void);
extern void ARCoreXRDepthSubsystem_RegisterDescriptor_m7D8E05D67B36AF81C5FB504C9C5CE1796F1D8782 (void);
extern void ARCoreXRDepthSubsystem__ctor_mEDFE4948894B6C0B169A7960449503CD9D104477 (void);
extern void ARCoreProvider_UnityARCore_pointCloud_Create_mBD2B951BD18D63675474411FAF435B70E7B0A2A9 (void);
extern void ARCoreProvider_UnityARCore_pointCloud_Start_m2BCF9EAB26BBDCD636011ABD568B8832BD0B6851 (void);
extern void ARCoreProvider_UnityARCore_pointCloud_Stop_m1753BDAFFC624FE10E6A3AF9CA453700429FB91D (void);
extern void ARCoreProvider_UnityARCore_pointCloud_Destroy_m253E3762A6202580325187FF5C909320327A83FF (void);
extern void ARCoreProvider_UnityARCore_pointCloud_AcquireChanges_m923F96C1B1B341C0AC41C0C00E9299BE3504F6B1 (void);
extern void ARCoreProvider_UnityARCore_pointCloud_ReleaseChanges_mD4F0CBE860AA297511DAD5089C3818AB8A43CEC8 (void);
extern void ARCoreProvider_UnityARCore_pointCloud_getPointCloudPtrs_m11B79CF2EFFE4ED94DB99E77A6A4DAADD5932994 (void);
extern void ARCoreProvider_GetChanges_m19155AA444BC46A6C38F7BD4B45C5EB1E13A6B58 (void);
extern void ARCoreProvider_GetPointCloudData_m8D457677921836A41A273609B8DFA43314DD8850 (void);
extern void ARCoreProvider_GenerateGuid_mEFBF2DA86959890B2BFF0479F6D989E0A03E64D3 (void);
extern void ARCoreProvider__ctor_m0341088DD918C0C66148DA578DD6FCBD193EC575 (void);
extern void ARCoreProvider_Destroy_mBF4A7CB6ADFACF9EFF6291F20DAB539A8DFD686C (void);
extern void ARCoreProvider_Start_m326B11169F3055941C51D585904C683B3FDA74FE (void);
extern void ARCoreProvider_Stop_m4FAB7B9AF53CE6AB27B68A239F1D7F80456E4B35 (void);
extern void ARCoreProvider__cctor_mDFD0A483D78C63198E41E7B3CE79FB04A0B9590C (void);
extern void CopyIdentifiersJob_Execute_mD5F4057E3560C7ABD74EBCEA89EFF89A64695D3C (void);
extern void ExtractConfidenceValuesJob_Execute_m261E6D89CD79EA5CF89F594039AC47FB4812D6D1 (void);
extern void TransformPositionsJob_Execute_m892589A8D6347D174D643A86AB646B2DFE94A66A (void);
extern void ARCoreXRPointCloudSubsystem_RegisterDescriptor_m0ADF1B7C2FBAC5F51A6770435B045C8FFB2F676B (void);
extern void ARCoreXRPointCloudSubsystem__ctor_m2D1E90079A80190397CB4B812243CC8B436D39BF (void);
extern void ARCoreProvider_UnityARCore_pointCloud_Create_mD9A254457B10E4728C27904C4F5EAA07690B7FA6 (void);
extern void ARCoreProvider_UnityARCore_pointCloud_Start_mAB67ED3E8F03CAD81932933F641E3EFB28C2E1C4 (void);
extern void ARCoreProvider_UnityARCore_pointCloud_Stop_m5B2BC989AA873BF564F5C0C26D12B82E2C19BD00 (void);
extern void ARCoreProvider_UnityARCore_pointCloud_Destroy_mF9B47EDA19181AF01C50ACBFD468244273C43F15 (void);
extern void ARCoreProvider_UnityARCore_pointCloud_AcquireChanges_mF35F6247ACF8FF472C713F21E58BBF3BEEF78171 (void);
extern void ARCoreProvider_UnityARCore_pointCloud_ReleaseChanges_m46034F39622BF5060C96F84BF248F17C52DF5DFE (void);
extern void ARCoreProvider_UnityARCore_pointCloud_getPointCloudPtrs_mCB90A68710744636718AA264122C212FB953AB90 (void);
extern void ARCoreProvider_GetChanges_mB35F864B1182AC2228C64A637A6449BAB5D9DC69 (void);
extern void ARCoreProvider_GetPointCloudData_m9FCD41E13C4DF76BB5F1C2DED96A035203029242 (void);
extern void ARCoreProvider_GenerateGuid_m7C5047E30C06237E3F8A86CE2F29CF71DE056DA0 (void);
extern void ARCoreProvider__ctor_m20F1CDCE0C7CC91A93BD1DD4D844BC6A53AE43C6 (void);
extern void ARCoreProvider_Destroy_m3315E4CFF445532039365F3E9F89B445FC03681E (void);
extern void ARCoreProvider_Start_mA8510D44063B77ACBCB4108DCB6D96790573ABE2 (void);
extern void ARCoreProvider_Stop_mC85AC671C4E3B9EEDAE07621830A4CC01A76BA40 (void);
extern void ARCoreProvider__cctor_mFC0240C25271B13A3FF7D315D24A823C27DB5E3C (void);
extern void CopyIdentifiersJob_Execute_mDFA3C27588F3A349F79AE17292B1AFE77FD80A3C (void);
extern void ExtractConfidenceValuesJob_Execute_mB8B1A284ADBF49B178B1ECEFC7A57FD1286CAA28 (void);
extern void TransformPositionsJob_Execute_m9CB6EF2CCF0635B3C368EEE6774B771C7609F928 (void);
extern void RcoApi_Retain_m582B6C8729C9286A3DAD4DCE213BC4CED88BC775 (void);
extern void RcoApi_Release_m274E1F614C7023ACC6F9216ABDEBC798941538D9 (void);
extern void RcoApi_RetainCount_mD3E57C58D93E97555287BC2643F97B0C0F5535CE (void);
extern void StringExtensions_ToBytes_m87407EBB2A901F266250D1A89C5B7CE55B66490E (void);
static Il2CppMethodPointer s_methodPointers[638] = 
{
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m6F15F3BD33B7D2DEBB889CBB6198147913047B69,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m19B5A5F70EE46E88531B4A04E6B903E9F976ABB8,
	ArCameraConfig__ctor_m03B7CCBE0A6A9F8C9CF14EBBCB1517566DA47015,
	ArCameraConfig_FromIntPtr_mBCBEFFFEAEBB82AD1AFDDDA8B450F161938A83F8,
	ArCameraConfig_get_Null_m24A947EA533C02BC3AE925CDE05BDF0748B053D2,
	ArCameraConfig_get_IsNull_m0E09F4D2B8C7F7159FBF65FCA7486A8C79741ED6,
	ArCameraConfig_AsIntPtr_m319C668BCFE412C0D53FF26D97EDB93249B39444,
	ArCameraConfig__ctor_mE14FEFC3215FE2A052D06E5A43C619D320B9AA19,
	ArCameraConfig_Dispose_m165F78DBFE34B76060059B7BFC7EB367E0D626CC,
	ArCameraConfig_GetCameraId_mE547D5D96CFA540BE2631831CB839D3D907DAC90,
	ArCameraConfig_GetDepthSensorUsage_m5F136C8F9C5B3044C503580EB45DBC2671EC7C4F,
	ArCameraConfig_GetFacingDirection_m3C022FA6AC02ED9BBBE8B4923E9CA397E72FB7A4,
	ArCameraConfig_GetFpsRange_mF9215E84CF0CF2EB05D2ACD0CFDE02899C9F2533,
	ArCameraConfig_GetImageDimensions_m3746D40ABD8902AEA59C72E1F6B23635A1A2C2C6,
	ArCameraConfig_GetTextureDimensions_mA0E9C970FDEE0847B4FC7AABC35D82B0259648AE,
	ArCameraConfig_op_Explicit_m94CDC0BE7224BA7B163E8E27129C92963FFB7F87,
	ArCameraConfig_Equals_m970EB204E68B633C9E34D1ED6A06EDCCD7E461E9,
	ArCameraConfig_Equals_m475848DB0C28F764CAFC0F44E27B06BEB7564041,
	ArCameraConfig_GetHashCode_m12BD55CF30C1466B45155A17224C4AA9AB16C4E0,
	ArCameraConfig_op_Equality_m84667F11E490E27DE28189D00CA36C0575DEBDCD,
	ArCameraConfig_op_Inequality_mF85ED6F7896064432D80B39BF4A42F794108230E,
	ArCameraConfig_op_Equality_mCE55DA201D1AB39471354684350E00BB06DBFA5C,
	ArCameraConfig_op_Inequality_m0122056A9A2B35072F4B6E815FF762268D32DE32,
	ArCameraConfig_Create_m886224169A78B3D22AB043E40F1524CF667F28FC,
	ArCameraConfig_Destroy_mABA8B48536BC128B2F42CA1F8C7381412C11046F,
	ArCameraConfig_GetCameraId_mEDFA5672A4B0455D422FB0C4124606BF5792FE55,
	ArCameraConfig_GetDepthSensorUsage_m1E6D173C6463759F5E3C80848B26598DC419C0DE,
	ArCameraConfig_GetFacingDirection_mF3A1BE5B912348463F8FECFE1EFD6C4660912114,
	ArCameraConfig_GetFpsRange_m288AE232C4A22D6FC87AE2EFA38B4611CE222087,
	ArCameraConfig_GetImageDimensions_m00F158B234F449E732FA04FE3F2962A6B6D3BB7C,
	ArCameraConfig_GetTextureDimensions_mC2ED0976D9D803C8067E13BE4C5311C3300FAAB7,
	XRCameraConfigurationExtensions_AsArCameraConfig_m9D94625F0D83F9421073FEEBC143F5AB4F80E6D5,
	ArCameraConfigFilter__ctor_mAFDDE747B063B59B3DA2DD79FC20A16D497C07FA,
	ArCameraConfigFilter_FromIntPtr_m3216CCE246F8D70DDA692B47E294EF62C43559A7,
	ArCameraConfigFilter_get_Null_m14F7F5F8AAE885AA0E25261991DB627EF6ADA4E0,
	ArCameraConfigFilter__ctor_mF486A6AFC61683CFC3E52B7E5494E983D4DE83E7,
	ArCameraConfigFilter_AsIntPtr_mC26AD4E33C4D07ED7C4970BC7C75456AC84D62BF,
	ArCameraConfigFilter_get_IsNull_m0C4936F2612BC0BB7B4BAF08F9F0947D69EDC383,
	ArCameraConfigFilter_GetDepthSensorUsage_mAD042C592CA038A62E72173B8B0E9224067C7C12,
	ArCameraConfigFilter_SetDepthSensorUsage_m92C45E574F2383B585B96C7FEC9FF2CCEABC68E3,
	ArCameraConfigFilter_GetTargetFps_m8A00967A664541D6702D5256F048DC56EBB74983,
	ArCameraConfigFilter_SetTargetFps_mD09E6CDC671DB61E9FC443F99A26EF4C716C9766,
	ArCameraConfigFilter_Dispose_m71514EB30557871157FCA62921F794E10EC3BD59,
	ArCameraConfigFilter_op_Explicit_m89BE76307F2F39AB124E955E03228A45AD6F50A7,
	ArCameraConfigFilter_Equals_m7A03616AACBC45070F8F65ACE805CF16ADF65494,
	ArCameraConfigFilter_Equals_mF6B858B3A8FC33E7912163F338B1CC71C59744ED,
	ArCameraConfigFilter_GetHashCode_mAB06AE4573E43FB7802536BD3809666832F1348A,
	ArCameraConfigFilter_op_Equality_mA7C1718FBD058D7E2DB53C134BCE698FDF0E9136,
	ArCameraConfigFilter_op_Inequality_m334D48DAF3A4F5F757C12A61B95913F3E560B362,
	ArCameraConfigFilter_op_Equality_mEC832B8112587B3269F1EE3E5F42320B0CE170B9,
	ArCameraConfigFilter_op_Inequality_m077FD4EF3B102E15565FE92111F8D6858BD2CEC6,
	ArCameraConfigFilter_Create_m142C7ADE353D6E38316A3A6EE50CB56FC883AC07,
	ArCameraConfigFilter_Destroy_m9EAD8BB21D88A11EE1ED2E885E92B06FE96BEA6F,
	ArCameraConfigFilter_GetDepthSensorUsage_mD6B774F26D0C2DE2202A91A98F6DBACF90D98E61,
	ArCameraConfigFilter_SetDepthSensorUsage_m5B7D3EC70DBB599EE5F6F6A682D6D55011D30555,
	ArCameraConfigFilter_GetTargetFps_m9973098C5F69BF6DBEB4694352ADF354C213BC02,
	ArCameraConfigFilter_SetTargetFps_mABECEE8F72881F7B6F0CD21C0916DCB68B949FCF,
	ArConfig__ctor_m0E326EEBB3B5154A7BA864AF3B31CA153C4AC072,
	ArConfig__ctor_m807FC812AF15E78B8FD5025A164FF214A030F793,
	ArConfig_FromIntPtr_mE9B04C63D4EE5D8223D76D67FE5DCF2C7071D2AF,
	ArConfig_get_Null_mCAF6C746D44A04E3AE94752CD94D2A4564CB48CE,
	ArConfig_get_IsNull_mC16E8E4A5FD7562D365B3CED6F6195F6D3A4BA98,
	ArConfig_AsIntPtr_m9CFB8A76A4BB209848A58B8CB8CC7481BF763A42,
	ArConfig_op_Explicit_mCCBCE6CB895992DB341D46AE4141049A24D16639,
	ArConfig_Equals_mCE8CA68F242CF012255732C5387E953EFB7C7572,
	ArConfig_Equals_mAF04B9E9552EAF1D2DE31F107EF45BBC9CF8BBA4,
	ArConfig_GetHashCode_m2AA440E48A34FDA2186E99FDF0630578AB433DC4,
	ArConfig_op_Equality_mE6B7E1ACCC7A3F8835FDB29C2652183F5ACAB162,
	ArConfig_op_Inequality_mF17E9CAD6C39547693DB6105CBAB4AD9C1CC511B,
	ArConfig_Dispose_m7AFD805848963702EA520A205BF9E3AC79BFA37B,
	ArConfig_op_Equality_m5078A53EB4B64DB5D3415C642CE14B353E370B7C,
	ArConfig_op_Inequality_m9F0E103ACB9A3EBC2D02B926BE22986213116EB9,
	ArConfig_Create_m165DEBEB687311E29D05FBB31CB1C31916DFDBAA,
	ArConfig_Destroy_m28D7B99179AA968D94454EFF9FD67F44AE354DE3,
	ARCoreAnchorSubsystem_RegisterDescriptor_m0B16D64374E148EB807D101D46503CB91525E57F,
	ARCoreAnchorSubsystem__ctor_mC98DC11B6F123B0FC0EA5ADFF88B8FA0E8A8D08B,
	ARCoreProvider_Start_mD07637BF200496D0C3615BB8CDB276EAF57A9901,
	ARCoreProvider_Stop_mEA426F258151A7C2A8D1E638C02C6208A3583DB1,
	ARCoreProvider_Destroy_m2C54D63343AAAA27AE6CA3A9AD64A45E0DFA5148,
	ARCoreProvider_GetChanges_mDF33BE46E05FE3C966FBA1250EB5025782AD01DA,
	ARCoreProvider_TryAddAnchor_m943778D8A0B7D1A12C866ED6C4C2E8C20749D3EC,
	ARCoreProvider_TryAttachAnchor_m0199986FA5941EE3C5265B5BE24B60EAC0F3526C,
	ARCoreProvider_TryRemoveAnchor_mB6B7A1248155B29CACE8626014BCDD37FDD4FB05,
	ARCoreProvider_UnityARCore_refPoints_start_m3BF0EDD507607790FB6A73B908381CAA98C2B980,
	ARCoreProvider_UnityARCore_refPoints_stop_mC98354069993B42D46D731556296E48E198BCCC2,
	ARCoreProvider_UnityARCore_refPoints_onDestroy_mD8491220209464421E7E61A4A8D2AC7E61093B35,
	ARCoreProvider_UnityARCore_refPoints_acquireChanges_m10A2AA33C27724FF1E27F831A0CFE49A6842F100,
	ARCoreProvider_UnityARCore_refPoints_releaseChanges_m080766056910991717D5B6803BF977E7C28BF6C1,
	ARCoreProvider_UnityARCore_refPoints_tryAdd_m0C7D4AB47E2EC13723A1E950F618E250AC7746D8,
	ARCoreProvider_UnityARCore_refPoints_tryAttach_m646E2CC0804C2F2FEA5A50331CD656295F07F439,
	ARCoreProvider_UnityARCore_refPoints_tryRemove_m50646A941477F2488B14C2435DDC8B1024D70641,
	ARCoreProvider__ctor_m77B4C1ACBC5FE569D98454C3530E26CF11089C6A,
	Api_SetFeatureRequested_m4F773D423194515D322A72241249268319B69472,
	Api_GetRequestedFeatures_mB3FB6A19484851715A59DE76B42F44AB56EA2B26,
	Api_get_platformAndroid_m6AD0B05ABD6C0211049325687C9FFD80128A5479,
	Api_FindLoader_mB06884CB5D22BA3696C18BB324C3012A98A17CAD,
	Api_get_loaderPresent_mA5AF7546F25C9A370DB1E9CF82C5E5BC2B58C301,
	Api__cctor_mD2F47985A010C03E7CD96F715CA0DE566ECCFE0A,
	ARCoreBeforeGetCameraConfigurationEventArgs_get_session_mCC2430CFD657AA39475EC7F94D12E94602318773,
	ARCoreBeforeGetCameraConfigurationEventArgs_set_session_m0E5A8E5B0E3D93A3CAD6F54F7E44C732FCE8686A,
	ARCoreBeforeGetCameraConfigurationEventArgs_get_filter_mF91BB4DDE06DAD980576BE33973865F4CAD2C702,
	ARCoreBeforeGetCameraConfigurationEventArgs_set_filter_m136097E18565BAB7B14635A951EF75F91208BB08,
	ARCoreBeforeGetCameraConfigurationEventArgs_Equals_m967BC71D3DF0590295A53C8BFA897626CC513EFC,
	ARCoreBeforeGetCameraConfigurationEventArgs_Equals_m08904E4BE8373C7A8930D51AF33F2EF8F60BA528,
	ARCoreBeforeGetCameraConfigurationEventArgs_GetHashCode_mBF1B9E848558FCF41FFA006B2A67018D2F405899,
	ARCoreBeforeGetCameraConfigurationEventArgs_op_Equality_mFCBA680669F086A7FF218857E32FB6AE61BCDF1D,
	ARCoreBeforeGetCameraConfigurationEventArgs_op_Inequality_m5B0607B9CAC0D53A85D1F56433AD62FEE4601EED,
	ARCoreBeforeSetConfigurationEventArgs_get_session_mFB401F61257971B12D20BA2C2A13427ADF110405,
	ARCoreBeforeSetConfigurationEventArgs_get_arSession_mFD007DB2BB188809848E2E6F699A87C4041137B9,
	ARCoreBeforeSetConfigurationEventArgs_get_config_mABF124F70ED16AECD48A7AD0758798446A53DF31,
	ARCoreBeforeSetConfigurationEventArgs_get_arConfig_m1EE2C53DDF02FA08DF8EEC1C016B7C4F5BBFC598,
	ARCoreBeforeSetConfigurationEventArgs__ctor_mDEADF5D3E5BD90B0D3715FE63350F8B23475B297,
	ARCoreBeforeSetConfigurationEventArgs__ctor_mE6E334059D83C27B306C1F72CD68C86E6DA4FED2,
	ARCoreBeforeSetConfigurationEventArgs_Equals_m4B4E1CC09BDDC6E0259BAC9D254FE94F81E336BB,
	ARCoreBeforeSetConfigurationEventArgs_GetHashCode_m6E3BC2573D8D619B0E86CCF48EB8F27283F41349,
	ARCoreBeforeSetConfigurationEventArgs_Equals_m8969624DBC58D203BE709765D5623E86DAAAC90B,
	ARCoreBeforeSetConfigurationEventArgs_op_Equality_mEC9AF5E7AEF64485667A015E7C13AA09F0716F44,
	ARCoreBeforeSetConfigurationEventArgs_op_Inequality_m99E23DE462306E6740509568AB1B9286F2ED3E9B,
	ARCoreCameraSubsystem_get_backgroundShaderName_m1AED04C9CE5C791D05FA636FE228E6DD3E29811E,
	ARCoreCameraSubsystem_Register_mDB9444ABD74F698F8DF2C26753B86A8746228DEC,
	ARCoreCameraSubsystem_add_beforeGetCameraConfiguration_mEF3CC67CF983EDDA871ACE63A8F93B6643526F53,
	ARCoreCameraSubsystem_remove_beforeGetCameraConfiguration_mBAD3DB452B748425BECB242DAAE2CDA672B95C06,
	ARCoreCameraSubsystem_TryGetCurrentConfiguration_m334348283EABC372AD15A49B02C504B543B53F78,
	ARCoreCameraSubsystem__ctor_mBD839198513F7B370BA4BC7523726EBF762BC83E,
	ARCoreCameraSubsystem__cctor_mF76DBAF369FE461D54F27CA1C6A6D18BD277DC61,
	ARCoreProvider_get_cameraMaterial_m43BB6CB876819A670E99E2AF819F21F093E825AF,
	ARCoreProvider_get_permissionGranted_m3A14A077F17265C42FE4F0C056EC08AA5BECE474,
	ARCoreProvider_get_invertCulling_m12A9EF15FB1037990513A6E8729B266F38760405,
	ARCoreProvider_get_cpuImageApi_m24D27620E1001943EF0A520D1D3DD21D08BDBFF1,
	ARCoreProvider_get_currentBackgroundRenderingMode_mCEFE851C8BBAA64426C05EA4F80B09D6330E6BE2,
	ARCoreProvider_get_requestedBackgroundRenderingMode_m49F511F80CDD4410F31991788042B8F85668E388,
	ARCoreProvider_set_requestedBackgroundRenderingMode_m1B730EA388B83551B6927485B042938BB6C1CF82,
	ARCoreProvider_get_supportedBackgroundRenderingMode_m7EF47D3103060BCF105F8191E7905F81C2577176,
	ARCoreProvider__ctor_mB3641D95DB0A7353E5F3C987660BA2282151A3A2,
	ARCoreProvider_get_currentCamera_mDFD20E6CA1F40F899A04DF4EC99FE4ADED02BF21,
	ARCoreProvider_get_requestedCamera_m87CE71EFDF07B0D4A00F4ED55E2F58BBE6FA7B4A,
	ARCoreProvider_set_requestedCamera_m20BBB626E437F07057452F1F89441268CA217CA8,
	ARCoreProvider_Start_m7622EA81D53C25844D9EC8990A0927D3E144C8EB,
	ARCoreProvider_Stop_m28914ABC564DB03341B5FA1B384BBBC2FA5B2689,
	ARCoreProvider_Destroy_m97DEF45E2DD070C2DA43C7D0C42B6B119973A484,
	ARCoreProvider_TryGetFrame_m5DBFF8050E393426C025DC027812000FDECDCFD5,
	ARCoreProvider_get_autoFocusRequested_m1DC365A076DD9E57832CAB0D9C8E3AC31807D317,
	ARCoreProvider_set_autoFocusRequested_mC8A88C6393A2738B39DB3DCDB70674BCA83DDC61,
	ARCoreProvider_get_autoFocusEnabled_m048D6D0D465785EB8DEFD70BFC57DCFCFF010EDA,
	ARCoreProvider_OnBeforeBackgroundRender_m93F93BFCE712FC6CF12F3407DA175A0D1A1BB204,
	ARCoreProvider_get_requestedLightEstimation_mA5865DCE9DDC79B34007981126D8C0CC6C0969DF,
	ARCoreProvider_set_requestedLightEstimation_m8D927B6282C8DDA7BD4365148AF597EAC02E9E46,
	ARCoreProvider_get_currentLightEstimation_mA0829B77045555BF2B50BC73BD536AFF48E789B9,
	ARCoreProvider_TryGetIntrinsics_m13E64DDA943B4179334A01D9821C7EB53455EB7D,
	ARCoreProvider_GetConfigurations_m9D7C215C704B94C838518874CA90A3B62D7CCCD3,
	ARCoreProvider_get_currentConfiguration_mD5373EEAA8C48033E3B2F3D892B0C384E2B9D67C,
	ARCoreProvider_set_currentConfiguration_m687F79EC299F69E1A227CE4FE4AE4CE7DC07F305,
	ARCoreProvider_GetTextureDescriptors_m3CDF78C26FAD8C70D183EC23001E4BFA872AC4C5,
	ARCoreProvider_TryAcquireLatestCpuImage_m092583292CFCF7D6AB32DACA59817991B5644839,
	ARCoreProvider_add_m_BeforeGetCameraConfiguration_mB56992655281D83A97D444CDD8CFBCBE843C38AA,
	ARCoreProvider_remove_m_BeforeGetCameraConfiguration_mCC65AEBA7C580EF96F2DBE05418BC9674A5EF417,
	ARCoreProvider_add_beforeGetCameraConfiguration_mD986481F241B18D60730DE4979BDF5214F614453,
	ARCoreProvider_remove_beforeGetCameraConfiguration_m5C92FF8B21BD98B983C858D6707B27E657CF45AC,
	ARCoreProvider_OnBeforeGetCameraConfiguration_mA33A6474A489CA38E45F609013293D679AF4D06A,
	ARCoreProvider__cctor_m421CDDCEC07E8682FEDA13984923CCF415E75D4F,
	NativeApi_SetOnBeforeGetCameraConfigurationCallback_m404BBA78562A936BEB5EB42FE09EB8453F91268D,
	NativeApi_UnityARCore_Camera_Construct_m09C6A5328195C5F09495FC5D0A61F97AB854FDF6,
	NativeApi_UnityARCore_Camera_Destruct_mD0587A807E9B6043FCA14A573F56B6B413E8A4E6,
	NativeApi_UnityARCore_Camera_Start_m18F79AD4568ED1311E5930E06D25B54EC87C1B23,
	NativeApi_UnityARCore_Camera_Stop_m9B882F8A26DF9BE25D69AB2578178834E38F4153,
	NativeApi_UnityARCore_Camera_TryGetFrame_mA83D08077103882819A8A26D9470699A90EDF56B,
	NativeApi_GetAutoFocusEnabled_m2E73F4CBF605540EB4FB0268BE89A38183065453,
	NativeApi_GetCurrentLightEstimation_mD098CD7D0A0582093717DCDA7563EE67B23936C9,
	NativeApi_UnityARCore_Camera_TryGetIntrinsics_m22787C20AE622991207EF3F62214097B3300F3D7,
	NativeApi_UnityARCore_Camera_AcquireConfigurations_m3DFFB1427453B8FF4662B2136C260B62A9742C8F,
	NativeApi_UnityARCore_Camera_ReleaseConfigurations_m23C062833C5DAAC916BBA482B5BB4D4723B31C90,
	NativeApi_UnityARCore_Camera_TryGetCurrentConfiguration_m98FA207339AC6B64F487D3B002FDACB16C5CC1F9,
	NativeApi_UnityARCore_Camera_TrySetCurrentConfiguration_m2145D1E21F1C3C88D921ABEF2C0A17E8CB006D00,
	NativeApi_UnityARCore_Camera_AcquireTextureDescriptors_mF8DCCA2D9A1D47441F10200174B0F244EF67A727,
	NativeApi_UnityARCore_Camera_ReleaseTextureDescriptors_m8F3572CC377BD7DADFECCE83BB39A701AF5011A9,
	NativeApi_UnityARCore_Camera_ShouldInvertCulling_m81401BC5C2C778FDFDAFDBF363E8B33088C0C686,
	NativeApi_GetCurrentFacingDirection_m0C3820870A31AACCDD681A4FE4AE8F13DA409A02,
	NativeApi_UnityARCore_Camera_GetFenceWaitHandler_mB93CED3C6C7F04955BF74F206B941D92AD9A915F,
	ARCoreCpuImageApi_get_instance_mE85E10D548E0FFF2FDB0532444C6D2E33387BEAD,
	ARCoreCpuImageApi_TryAcquireLatestImage_m5C02A41B0E866D8AC99E4F32B25F2D5890ECA073,
	ARCoreCpuImageApi_GetAsyncRequestStatus_m3353734BAFE73783A6400AE409D99D4BD6F95261,
	ARCoreCpuImageApi_DisposeImage_mB7DFA6357901007B40AB836685665DF868A50A48,
	ARCoreCpuImageApi_DisposeAsyncRequest_m1C836B0DFC1351EC84ED75B00C72B36B878E7208,
	ARCoreCpuImageApi_TryGetPlane_m9336AE63158D11FCB0454F8D984B0AA7EF1F07E2,
	ARCoreCpuImageApi_NativeHandleValid_m0A091EF7277486E0AA8CD4AE07DA678D166F73E9,
	ARCoreCpuImageApi_TryGetConvertedDataSize_m2CBCAFAABF0F11B1F5E822CC68B76DF46419D95A,
	ARCoreCpuImageApi_TryConvert_m73099E871C6073CF094D502ACEA729F7608F791E,
	ARCoreCpuImageApi_ConvertAsync_m47508107848E58A4F8D3E2A7269E13253D357BED,
	ARCoreCpuImageApi_TryGetAsyncRequestData_m3518605946425B8BBF80F7B97384F561DB95E9B5,
	ARCoreCpuImageApi_ConvertAsync_mCB3F9F6299DB7CF6812DC1E1FDC38E5A891563D0,
	ARCoreCpuImageApi_FormatSupported_m668336D4CFC1D2A98637ED8524387E67267967E1,
	ARCoreCpuImageApi__ctor_m16F0D8727C47B3BCAA39133E46AF6199C646F427,
	ARCoreCpuImageApi__cctor_mA328729FD7C06E9389F62404F3C18D1797B6E13D,
	NativeApi_UnityARCore_CpuImage_TryAcquireLatestImage_m961556B4A05E02E1E0913A05293B6B38ACDADDD8,
	NativeApi_UnityARCore_CpuImage_GetAsyncRequestStatus_mAA543CADE1A13A202E8073F025888D0285EE3B06,
	NativeApi_UnityARCore_CpuImage_DisposeImage_mE3226ADD096206D705A18DB3856ED66B0B2E689E,
	NativeApi_UnityARCore_CpuImage_DisposeAsyncRequest_m84763E96A20BF94A2EE96A5757ADAE3529A89421,
	NativeApi_UnityARCore_CpuImage_TryGetPlane_mE677AC286DB8A37FD3E116B917C6B2043A0023AD,
	NativeApi_UnityARCore_CpuImage_HandleValid_mE49A1DD268714610CD220AED14D40B387F274BA8,
	NativeApi_UnityARCore_CpuImage_TryGetConvertedDataSize_mE2F9C873F3FAAB9CA0A600E51A9281B5E00BB018,
	NativeApi_UnityARCore_CpuImage_TryConvert_m056AFCB611BC71AC56AE69DCABEC98440387C6A5,
	NativeApi_UnityARCore_CpuImage_CreateAsyncConversionRequest_mD0B2E29B3DB70008E22AD099E3F744BE6607019E,
	NativeApi_UnityARCore_CpuImage_TryGetAsyncRequestData_mC79F97E6877D4D1BEA3D9E8E05F11ED9C24EB205,
	NativeApi_UnityARCore_CpuImage_CreateAsyncConversionRequestWithCallback_mC60CA405C610659CDBCCE983EBBB93AD0B70E027,
	ARCoreEnvironmentProbeSubsystem_Register_m41908310BC181A097546334CA89E74F7E4CDFC11,
	ARCoreEnvironmentProbeSubsystem__ctor_m89DECFC44829DEFA9B37B205F2DCD73080ED78B0,
	ARCoreProvider__ctor_mA174658BF6BFB950970E9076BB5F166FE696C264,
	ARCoreProvider_Start_mD152BD0A62302F3C7AEFB38AB10BDE64D25CDF73,
	ARCoreProvider_Stop_mF4D5343658FFDA68F481D8E998C8859B05E5A353,
	ARCoreProvider_Destroy_m10827FBF253B47F6B6134DC64DBB0A0D6E43F8AD,
	ARCoreProvider_get_automaticPlacementRequested_m8DB8F64671B4DD9C57A7FE7B42F0595E681AC456,
	ARCoreProvider_set_automaticPlacementRequested_m18F7D90E52BBE1656F004B3CAA4F6CBF9B5000D4,
	ARCoreProvider_get_automaticPlacementEnabled_m60D0A0EA74B9C86D7F0EFBF02BF0CA385A2CBC1F,
	ARCoreProvider_get_environmentTextureHDRRequested_mC7E1DEED9E31F26A4089FDCC7BF7DB10352A5732,
	ARCoreProvider_set_environmentTextureHDRRequested_mB9DFB8284C97AC4D77505C872D3E471C436C77E8,
	ARCoreProvider_get_environmentTextureHDREnabled_m7F547DA0179CEBE47CA9A123E2D01007F8429F3F,
	ARCoreProvider_GetChanges_mAC7FEC7A1AD00B7207342CDD2654505031F48728,
	NativeApi_UnityARCore_EnvironmentProbeProvider_Construct_m01ADDDD26A6B93C63294CB7B612181E92154BC9D,
	NativeApi_UnityARCore_EnvironmentProbeProvider_Start_m7B61D1337949AF31C753FC702F4D31A74C0AC06D,
	NativeApi_UnityARCore_EnvironmentProbeProvider_Stop_m3F992A4699AE1C21976F5D7A354671059A169AD4,
	NativeApi_UnityARCore_EnvironmentProbeProvider_Destroy_m80C3C96A2F4E056BD89ACDC7B0C317046DF59756,
	NativeApi_UnityARCore_EnvironmentProbeProvider_SetAutomaticPlacementEnabled_mF051761CA9DF85672D998961422674F675F50BEC,
	NativeApi_UnityARCore_EnvironmentProbeProvider_TrySetEnvironmentTextureHDREnabled_m00678897FC7ECB1C75D6630B3687FB8100147856,
	NativeApi_UnityARCore_EnvironmentProbeProvider_GetChanges_m735B2332E72E233E69CF6569BED3FB8B1D66AC89,
	ARCoreFaceRegionData_get_region_m7CD0AB1C423FD7A964F41C4CCCD14857C08177FE,
	ARCoreFaceRegionData_get_pose_m8B0A57A837D75EC314AE73F2931075A0F10A3775,
	ARCoreFaceRegionData__ctor_m589405112E30EB33BE5D5E10C0F5CC67D24FBB49,
	ARCoreFaceRegionData_Equals_m2056862DD1AD264FBDD1C54E254871098B6995DC,
	ARCoreFaceRegionData_GetHashCode_mC1E01B54503EADB48B6927CF2074C7FA4D426ECE,
	ARCoreFaceRegionData_Equals_m17225C794C37005208BCDAAEB9DDE4F9B664D126,
	ARCoreFaceRegionData_ToString_m01A02559D50A9AB69A98601C0DC8E707ECDDB02F,
	ARCoreFaceRegionData_op_Equality_m0AD73FE00B48C54DE058A86A425B430F4F6E7252,
	ARCoreFaceRegionData_op_Inequality_mC9B7928874917F1AFAF5FBAAD223164B0536E58C,
	ARCoreFaceSubsystem_GetRegionPoses_m86783D41F79D1D4E8811CABE414E1F12437F21C5,
	ARCoreFaceSubsystem_UnityARCore_faceTracking_Start_mE2B22D6EBED2B0A2726BAA6E88691BBA7DD315BD,
	ARCoreFaceSubsystem_UnityARCore_faceTracking_Stop_m65991C5FFE2FEA7DF1FA0515EAB390C00B6F6F64,
	ARCoreFaceSubsystem_UnityARCore_faceTracking_Destroy_m3D9A9C059215ED4839D516843174A9E2D65F56F9,
	ARCoreFaceSubsystem_UnityARCore_faceTracking_TryGetFaceData_mEF3EEAE21430BBB9A1707E1695631CD13B67B9FC,
	ARCoreFaceSubsystem_UnityARCore_faceTracking_AcquireChanges_mCB0CEE9BF0F8E316F02641C04B20B0E1EBB1DBAE,
	ARCoreFaceSubsystem_UnityARCore_faceTracking_ReleaseChanges_mFCEC09D4F7BA73E3C3968BF49042EE48BAD9C4BF,
	ARCoreFaceSubsystem_UnityARCore_faceTracking_acquireRegions_m0886B63972051E8B9B53723699FC4466070BF452,
	ARCoreFaceSubsystem_UnityARCore_faceTracking_deallocateTemp_mD03CDFA5D36E7AF8633A55677CC3C98562EABD68,
	ARCoreFaceSubsystem_RegisterDescriptor_mBAAA288ECADA5B4680BD731E33638DA216DB130F,
	ARCoreFaceSubsystem__ctor_mE994B52DC28CB49FDBC336E54C3510FEDF663138,
	ARCorePose__ctor_m1328D26F798D034CBD7F0402528B873D5994D5AF,
	FaceRegionWithARCorePose__ctor_m1021176D2016468413133E87B6F50884A240E76D,
	TransformPoseJob_Execute_m7EBE008B308EEF99D66204836A8954E1C6F6F1D3,
	ARCoreProvider_Start_m674E115A338FF9DABC486AEF8E2B2B469D68452C,
	ARCoreProvider_Stop_m67BF08E9D1BA2C4C31060C97F2B6324AF74FA4DF,
	ARCoreProvider_Destroy_m3B9CEC2B8C99C1AB440C8434965D002153639BE5,
	ARCoreProvider_GetFaceMesh_mE8C775C1B20E2648524995615154E24D8312559D,
	ARCoreProvider_GetChanges_m3F0274A1E1E2BCFACB2CB91032CD40ACE21E2710,
	ARCoreProvider__ctor_m7212DC6DFC295E14FECF45D31586ED62BE00E52D,
	TransformVerticesJob_Execute_mB25F13AA4AB3152365B51B0C2ACA31FFC756D74F,
	TransformUVsJob_Execute_m3A9C954126E9929C947C66111DC50B1A4EB8478A,
	NULL,
	TransformIndicesJob_Execute_m20C5A48E77A83348D5BD86AD436EBC581C5F0AAB,
	ARCoreImageDatabase_GetLibraryData_m9F28026F0212BA3299F23C27645BD6C66BF478C2,
	ARCoreImageDatabase__ctor_m088924D76C9D933AAD4ACAA904D5077384B9FBE5,
	ARCoreImageDatabase_op_Explicit_m7A53CCF8374A72B15D6B73ACADC5909886E0C92E,
	ARCoreImageDatabase_Finalize_m1A668727FF90C396A2501036A38C4BA412212F8F,
	ARCoreImageDatabase_get_supportsValidation_mCD44728F3EBAD0F56E9B016C009DAADFDB6E3379,
	ARCoreImageDatabase_get_supportedTextureFormatCount_mAED9D8669DE36B2AA73C7143C24BD620548ABC52,
	ARCoreImageDatabase_GetSupportedTextureFormatAtImpl_m1D2F965E4E7F58B0FE7BA6262ECBC994F73BE9C2,
	ARCoreImageDatabase_GetAddReferenceImageJobStatus_m1BBD7DD51F11B3FD80A4E0CD21D085399471BEB7,
	ARCoreImageDatabase_GetUTF8Bytes_mDF50981E4D87ABAC80566B234E197207BCD4074A,
	ARCoreImageDatabase_ScheduleAddImageWithValidationJobImpl_m8458FC816306C31C531AEDF9342441B13FE85577,
	ARCoreImageDatabase_ScheduleAddImageJobImpl_m443DE86ADBC43475425A2C26ABE5EC6B3D78FA20,
	ARCoreImageDatabase_GetReferenceImageAt_m9319449DB183EAA9CB1F16CDF886E7601B798E56,
	ARCoreImageDatabase_get_count_m97A7B403F0B4544C7CD6DE8B860DBEA9AB1E4F99,
	ARCoreImageDatabase_GetHashCode_mA4D7878527E9FC94B5F3AFD8E946C7CBBDDA6DC2,
	ARCoreImageDatabase_Equals_m49780C2062A4B7D7A72D24F93B242DBC1CC8336C,
	ARCoreImageDatabase_Equals_m00A589ECD8847528CCA00E2AC42863D9437C492E,
	ARCoreImageDatabase_op_Equality_m2823B083318E8120CF73F4B4B27BCF2493BC6969,
	ARCoreImageDatabase_op_Inequality_mD705CA7598C95660A54703EB72A0A81CAE6A573C,
	ARCoreImageDatabase_GetReferenceImage_m153C4089F52AEF938CABD2975A16DD9C0AC2E969,
	ARCoreImageDatabase_GetReferenceImageCount_m28772B1CADB95B265D85FDF95EA30D9FC9E0609F,
	ARCoreImageDatabase_Deserialize_mCCEEFCA3F11676EDADB06955019777B6EE1627C8,
	ARCoreImageDatabase_CreateValidator_mE8BC250B5B5421C3C58E64B3140CF6DB1E4D85CD,
	ARCoreImageDatabase_GetStatus_mE6201BFBCDE4FFD9F40F842E0DE11BEE2D66D5EC,
	ARCoreImageDatabase__cctor_mFAD60574CD25D3C475E9CA655B28BDB28D391D7C,
	AddImageJob_Execute_mFF2FF44D70D3647AF25843519E6B767A99BAAF36,
	AddImageJob_AddImage_m04AFBEC870C62812FD6EF08E1A4B6FB85E8AA3EA,
	ARCoreImageTrackingSubsystem_GetPathForLibrary_m42D960137532DA8802302BBC3202D1D05CB63A7A,
	ARCoreImageTrackingSubsystem_RegisterDescriptor_m5B575A92EA6372775F273462FB572D5B236364C6,
	ARCoreImageTrackingSubsystem__ctor_m8B8F027237262EB2944D5678C3B5FEF4389A20C5,
	ARCoreImageTrackingSubsystem__cctor_m655EE8417F9BAE6B9577466A43F074A3ACFB4D4B,
	ARCoreProvider_Start_m95DD106DD54DAAEE54CA17B159AFF4331272FCCC,
	ARCoreProvider_Stop_mEDF427A8C78A9AE6C0F91AD82FC0BC92E127C728,
	ARCoreProvider_set_imageLibrary_m5E48BF05853F210F48F84F820AE48C4B02A63633,
	ARCoreProvider_CreateRuntimeLibrary_m578F5264A70564E1A0B21D060F40CEA4C15657B7,
	ARCoreProvider_GetChanges_mBD6B8435337693F22D58AC045B036DB668985F37,
	ARCoreProvider_Destroy_m3CAE3DD73B251AADAAA18E255A50D979BDD4574E,
	ARCoreProvider_get_requestedMaxNumberOfMovingImages_m9136442725D06CDB5469F07BAF880A163B70266B,
	ARCoreProvider_set_requestedMaxNumberOfMovingImages_m75E4CAEB39E571F7F50F0D77AAF134DC8466912C,
	ARCoreProvider_get_currentMaxNumberOfMovingImages_m40F817E6DB85D560F08C853FA45E9B15AE4D8E90,
	ARCoreProvider_UnityARCore_imageTracking_setDatabase_mB0F8D74CFA251FC79150EA3B5CAAAD0A65600251,
	ARCoreProvider_UnityARCore_imageTracking_destroy_m4EADD7AB13CC0F86B4BED490FEB521BE7534AC20,
	ARCoreProvider_UnityARCore_imageTracking_acquireChanges_m4BF017C6A4F52A546B214540DF7D2F263D1885F6,
	ARCoreProvider_UnityARCore_imageTracking_releaseChanges_m4BD377E7CB1BA6FFA63B744B00DD92144E94AEDF,
	ARCoreProvider_GetNumberOfTrackedImages_mE359250D65D55B04C3089C5AD8D720407D7F37C3,
	ARCoreProvider__ctor_m6D2C8A015DB244077C5C82EA873B7AEE23DC926C,
	ARCoreLoader_get_sessionSubsystem_mB719854EF11ED7529B060881E078347967098818,
	ARCoreLoader_get_cameraSubsystem_mA7423C7021CC4D3D47589BB1374C8C3D2FFAFC5F,
	ARCoreLoader_get_depthSubsystem_m3ED669F0F0517001E746FC4E4CC5E1B5816DD9CD,
	ARCoreLoader_get_pointCloudSubsystem_mEE843FA3D3213B8DA65E81532BE10C19217EDFAB,
	ARCoreLoader_get_planeSubsystem_m795EB938590D1B0D3ACF396C6EF51CCE279D311A,
	ARCoreLoader_get_anchorSubsystem_mC42CCAA64AA29DB7E7AC0D76B6D3979935807A31,
	ARCoreLoader_get_raycastSubsystem_m908C2D2156F387879196FA0ADF530EB02366F6BF,
	ARCoreLoader_get_imageTrackingSubsystem_m3DBC8FB6CAEF6D6BEFC56115C1611BB515647190,
	ARCoreLoader_get_inputSubsystem_mC96B3C65EF908BE1E4B5CC5F3F9C27631C314242,
	ARCoreLoader_get_faceSubsystem_mBE92A8D0FA340647A4BBED7D37EE3E753A4022D5,
	ARCoreLoader_get_environmentProbeSubsystem_m3BF82CA0654C4963BAB3C41DC8CD3CE488C7135E,
	ARCoreLoader_get_occlusionSubsystem_m20523A3C486A422505823C97EABC56AF165358F0,
	ARCoreLoader_Initialize_m7958140B0AD77F780834F1CB4CE76468A1A4EB1F,
	ARCoreLoader_Start_mC2E2B5ED50FD4747F02545B18115DDC30DA4F924,
	ARCoreLoader_Stop_mF234BEB25E607B591CADD9C0AAF511F5F162C9DD,
	ARCoreLoader_Deinitialize_m34E45DD93616D53D953992DB28C1C379CE69DEAF,
	ARCoreLoader__ctor_mA1E8D67717DEB72A3CEEEF54E8F360B2EAAAF1AF,
	ARCoreLoader__cctor_m0D2C1C4AD74B7264423250519676B7F9D7D0C86E,
	ARCoreLoaderSettings__ctor_mBBD3002FC45E720F566E60290D60C3C332739AC9,
	ARCoreOcclusionSubsystem_Register_m7F9EB8509E889D4523D85A5A98301E7EC83F7E85,
	ARCoreOcclusionSubsystem__ctor_m16B5A395AC5676532E8AEBF4360F919348883755,
	ARCoreProvider__ctor_m740EFF84FFF6B9B2C7C4FD5F28A95A78568D2AE7,
	ARCoreProvider_Start_m75EEF8FA8584F7501A752662804FB39A888552D7,
	ARCoreProvider_Stop_m748C215A4A853B42DFC52A223F826A9DD7E638F2,
	ARCoreProvider_Destroy_m1FB0B254074AF688C79F6CBC4F313AF374003F47,
	ARCoreProvider_get_requestedEnvironmentDepthMode_m7285AE1918DD6EF69250D54747E7620CA6FA9166,
	ARCoreProvider_set_requestedEnvironmentDepthMode_m4284137A5F92528C355BB811D43624F722EE3CD3,
	ARCoreProvider_get_currentEnvironmentDepthMode_m13ECB4DF298101AD41CCF0A5EF8F465B029F8B61,
	ARCoreProvider_get_environmentDepthTemporalSmoothingEnabled_m0AAA4E5B48A1217AEC938D2E6F51982382737322,
	ARCoreProvider_get_environmentDepthTemporalSmoothingRequested_m7BE8092F27B40CE259C447BDFA11D3B031EA0645,
	ARCoreProvider_set_environmentDepthTemporalSmoothingRequested_m35D777A806EBC1F3380775E872707026BF49016F,
	ARCoreProvider_get_requestedOcclusionPreferenceMode_m491F11E20BABA570FC52CE167FFFCEA56FDAFE15,
	ARCoreProvider_set_requestedOcclusionPreferenceMode_m4948A8F68DC62CCAC078D15F4D9CE55E4F8007BB,
	ARCoreProvider_get_currentOcclusionPreferenceMode_m124D76BDB3DBD823AC8C8676D741AC742DD47A2B,
	ARCoreProvider_TryGetEnvironmentDepth_m78238FE8CD7D4AAE21D15E288563FFFA94E50890,
	ARCoreProvider_TryAcquireEnvironmentDepthCpuImage_m425D9F8AE24B409C5BDF7E959B30245A9336AE23,
	ARCoreProvider_TryAcquireRawEnvironmentDepthCpuImage_m438FEF338186F8B8D9CF5587B30318168D3C7407,
	ARCoreProvider_TryAcquireSmoothedEnvironmentDepthCpuImage_m90DF281C8FBC87903E3BA776A46F3B6F0F11C22F,
	ARCoreProvider_get_environmentDepthCpuImageApi_m61FB1F4AE16ABCA3AA8A07DCCBB149603E7137B7,
	ARCoreProvider_TryGetEnvironmentDepthConfidence_m944FA850E27746FE2B692979C35BC4EB1C3B21BC,
	ARCoreProvider_TryAcquireEnvironmentDepthConfidenceCpuImage_mB3A215787E01AF6E90DE3E35D23A3E5EFA627F83,
	ARCoreProvider_get_environmentDepthConfidenceCpuImageApi_mA3E737368FBC38050E3620505E826949899B1F36,
	ARCoreProvider_GetTextureDescriptors_m93940B7502A6A2E218259EF3B26C2ABEA5114070,
	ARCoreProvider_GetMaterialKeywords_m830087C8188384A42E71AAA5B2B09C97BCA5D73A,
	ARCoreProvider__cctor_m5BACEE5E109F2DD4DC64190D2420BDD5AEBD5036,
	NativeApi_UnityARCore_OcclusionProvider_DoesSupportEnvironmentDepth_m9FE0E9226362ADC8A84B665DDB63F6D866412EDD,
	NativeApi_UnityARCore_OcclusionProvider_Construct_m4569505915E0A123538C6C77D1CF2255DD2F781A,
	NativeApi_UnityARCore_OcclusionProvider_Start_mECE64FEE6F26E064D5458675944396A8D6B0DB04,
	NativeApi_UnityARCore_OcclusionProvider_Stop_mC3662D7828F0D4C8D334A79EF7F30D83AD3B3D7D,
	NativeApi_UnityARCore_OcclusionProvider_Destruct_mEA7D53D06C212F7BD6FA5B5AF3DA3E9ABA91519A,
	NativeApi_UnityARCore_OcclusionProvider_GetRequestedEnvironmentDepthMode_m3BEFFE79231EADEFDB14CB7ACACBEB4C89C8BA01,
	NativeApi_UnityARCore_OcclusionProvider_SetRequestedEnvironmentDepthMode_m61C9D8DBC93CAB41F9DCCD8C4076C4FB0174C4D4,
	NativeApi_UnityARCore_OcclusionProvider_GetCurrentEnvironmentDepthMode_mC39A09403F460011D6DD5E816F0D6C2496DFE38E,
	NativeApi_UnityARCore_OcclusionProvider_TryGetEnvironmentDepth_m3F29D3D4DD60A77D32BA5405C41E45F25991C874,
	NativeApi_UnityARCore_OcclusionProvider_AcquireTextureDescriptors_m817E98B17DE94B409FE6D826283A31D748FA2C1E,
	NativeApi_UnityARCore_OcclusionProvider_ReleaseTextureDescriptors_mD4DD06BF3DC2D03D31F450BE3DFFB06C371986F3,
	NativeApi_UnityARCore_OcclusionProvider_IsEnvironmentDepthEnabled_m1ED084E2A34A0C10C2432A94970408B5BBC5FABA,
	NativeApi_UnityARCore_OcclusionProvider_GetEnvironmentDepthTemporalSmoothingEnabled_mEC9572769528B85914D99CFBA9241E4B5FBCDECB,
	NativeApi_UnityARCore_OcclusionProvider_TryGetEnvironmentDepthConfidence_m11BB197D49D6ECBA23EBDAC772C15AC31FBCDC3E,
	ARCorePermissionManager_IsPermissionGranted_mF4555A12A77D31E4D3E29E481DF3F53F05F2B836,
	ARCorePermissionManager_RequestPermission_m85D7447D4D95D74B4ECC898D56295392264B1D8C,
	ARCorePermissionManager_CancelPermissionRequest_m6028D958E440B6FF21DE814A37F0EEC9E908539E,
	ARCorePermissionManager_OnPermissionGranted_m6F977C06DFFDA17DB3D82BDD28EBA57ADAE68FD5,
	ARCorePermissionManager_OnPermissionDenied_m84D8B52092834DE931A3D6F8C0AE7F56FAA75091,
	ARCorePermissionManager_OnActivityResult_m7FBB087DEC75D2315A9D06FA5D4A5C238096BB2B,
	ARCorePermissionManager__ctor_m995B52F67EB43FB3BBDC858DFDA0C6FD5D1293AC,
	ARCorePermissionManager_get_instance_m344C4B4A237CDB080CBE74E69C78881244B999EC,
	ARCorePermissionManager_get_activity_mD8500EAC35737E2F9630F84D03C4916BBEB9541F,
	ARCorePermissionManager_get_permissionsService_m255C1DBB66AD92E34654754B7CE146C33037F165,
	ARCorePlaneSubsystem_RegisterDescriptor_m1D8C1EC22E5157CA6C5537E1453C575D4F56E232,
	ARCorePlaneSubsystem__ctor_m1F0CD932244651913C931C181530A3FC960B65D7,
	ARCoreProvider_Start_m7595EF6D1E7A0E2C7D6ADC620BDA5FDB21320E8E,
	ARCoreProvider_Stop_mA8989B03BBF873DA74AD51D17C3E7AA0A5A8C5EF,
	ARCoreProvider_GetBoundary_m8ED3BC09C3970185A93CEB06B1B2A63868CC7588,
	ARCoreProvider_GetChanges_mD605EB86F5978B7A8B2DC635138BA1ABB95634EF,
	ARCoreProvider_Destroy_m89EE9E1820BAD6DFDD0F87BCE4515DDAB3E89E57,
	ARCoreProvider_get_requestedPlaneDetectionMode_m39697C78C2C6A8644CC860B4D94C4C68B647B19F,
	ARCoreProvider_set_requestedPlaneDetectionMode_mF523936701717C8EC4B55E19024933A549A535D3,
	ARCoreProvider_get_currentPlaneDetectionMode_m2C19714D6760548615A29FC3D3945A008EC1A973,
	ARCoreProvider_UnityARCore_planeTracking_startTracking_mF85B05ED58756FEC2107D79C46EA662C810A06D2,
	ARCoreProvider_UnityARCore_planeTracking_stopTracking_m14B58477D7EA73778B4155234A5D9633B01C7371,
	ARCoreProvider_UnityARCore_planeTracking_acquireChanges_m988991B752D77BACF248256DF174B256DC344A60,
	ARCoreProvider_UnityARCore_planeTracking_releaseChanges_m906202B7B7B557FDFF3E5BEA33C3F3913D1295A8,
	ARCoreProvider_GetRequestedPlaneDetectionMode_m23F5093A3A701E84AA42C0EA5D9FBAAA4471A74F,
	ARCoreProvider_SetRequestedPlaneDetectionMode_mD8FD0B8CE75A9514BE694DC9BC71BC7001E4E672,
	ARCoreProvider_GetCurrentPlaneDetectionMode_mA46AA7FAC6CE7B1DE8AFC3999F686806A932FD97,
	ARCoreProvider_UnityARCore_planeTracking_destroy_mB285E24433D266D2116F3C1C66F10C5F43899114,
	ARCoreProvider_UnityARCore_planeTracking_acquireBoundary_m636E3DE646695BE41F746C7F282EB086906DD5C7,
	ARCoreProvider_UnityARCore_planeTracking_tryCopyBoundary_m720CFCE25004DD87E94A5ECF8C9D67CA9C0C93B2,
	ARCoreProvider__ctor_mCBE59A924070B5444C25F0DDBE7F1D05E118EAA2,
	FlipBoundaryWindingJob_Execute_m1A3A43413FBCA81A1ABDC145DC35AA533E86BC9D,
	FlipBoundaryHandednessJob_Execute_mBDA83F26E0D4DE330FB14E62DDFB7A52219B8CA9,
	NULL,
	NULL,
	NULL,
	ARCoreRaycastSubsystem_RegisterDescriptor_mADCD846F7C8BDA4776E7A944C09B606587F86F0B,
	ARCoreRaycastSubsystem__ctor_mC3D3E27326C788CF7404644A8041D2BCB6FFF2ED,
	ARCoreProvider_Start_m2C3CFF618C62BC4DB9A1DAB8A23B0407E0E1FE88,
	ARCoreProvider_Stop_m6DE4D49F0BD5C936116DCDD2371700266568E58F,
	ARCoreProvider_Destroy_m3FDF8D419C9B3A7BBBE1ABA88A4588DCCB2C372B,
	ARCoreProvider_GetChanges_m629D4E64616243D68CA87318EC5346E4122FED72,
	ARCoreProvider_TryAddRaycast_m30632AD57E0DAD3CE858EAB81353D8FE151F4541,
	ARCoreProvider_RemoveRaycast_m8A81C2C1BAEFEA1EF19764853ABC215D5C67F866,
	ARCoreProvider_Raycast_mEEE649E1F6280D29A8623F617D87E406E5C86509,
	ARCoreProvider_Raycast_mBF08914AA765BA9C84A022D8E0F04A8B3DCAB105,
	ARCoreProvider_UnityARCore_raycast_acquireHitResults_mA0F13622676EB1103DC27EC0008A96A600136388,
	ARCoreProvider_UnityARCore_raycast_acquireHitResultsRay_m9B14887BA2FB78A720D7C498FD37E73CB6EE3A25,
	ARCoreProvider_UnityARCore_raycast_releaseHitResults_mBB0F35E0A169BDBBDBB5D57924F0E83E8E48A613,
	ARCoreProvider_UnityARCore_raycast_tryAddRaycast_m21FF95D0D80788ABEC820730773CF2DABF292F83,
	ARCoreProvider_UnityARCore_raycast_removeRaycast_m290E26EDC14DF774F802E69D6466B0CD27E4CD7E,
	ARCoreProvider_UnityARCore_raycast_startTracking_mDA579FB0BAEFCB2F3AA80EF8BA0D0C4D1D751E28,
	ARCoreProvider_UnityARCore_raycast_stopTracking_mF241317EEEA2DBFB896B51DA2F1334D3AD894553,
	ARCoreProvider_UnityARCore_raycast_acquireChanges_m54B560915C02A18FEFD93A9FF5F5DE8D60B0357C,
	ARCoreProvider_UnityARCore_raycast_releaseChanges_m2F0D70AF2BFC888AC491408F55D5665EE21304DE,
	ARCoreProvider_UnityARCore_raycast_destroy_mBE0BD44C936F2963A9CDC87BCE5C987812FC827B,
	ARCoreProvider__ctor_mCEED9DE66DCD9BB6AB1F30F80AD6044DF568C209,
	ARCoreSessionSubsystem_OnCreate_mF8F1CEBDBBA6C5EB2DE36E7DF3FC114415BB4536,
	ARCoreSessionSubsystem_ConfigurationChangedFromProvider_mCDCA9342D2919733A299E64B3DBF4F908C048722,
	ARCoreSessionSubsystem_SetConfigurationDirty_m589BAB1CB66E9C17CFEF26FADC620AC122AAFA10,
	ARCoreSessionSubsystem_get_session_m30DEF5F507F37ADBA33992040DF96C29A035DE20,
	ARCoreSessionSubsystem_StartRecording_m5FEAD096A36F54B317747AC4C7CDFCEED5D73930,
	ARCoreSessionSubsystem_StopRecording_m80EBB7C1996C30ACBCF2AD953FA340F2CCC9D00A,
	ARCoreSessionSubsystem_StartPlayback_mAD4B827E1C37E2BDC536D93A624A8CA4DFA1FBBE,
	ARCoreSessionSubsystem_StopPlayback_mB7CCEABF538062686665646E7CD89AA6DAAADD63,
	ARCoreSessionSubsystem_get_recordingStatus_m407E01258C262F5FF877D8E4BCB52C97AF3431D9,
	ARCoreSessionSubsystem_get_playbackStatus_mB849FC26DC9496B81C9B3FEE9BE3352AFD2AA9C7,
	ARCoreSessionSubsystem_add_beforeSetConfiguration_mA1649A6E88E8D431B34DC24E6E8C171D8F957B6A,
	ARCoreSessionSubsystem_remove_beforeSetConfiguration_m21286B32C3760C722A3820C34A5F202C6CD67F16,
	ARCoreSessionSubsystem_RegisterDescriptor_m66D5092356E8F4753B04BA36E9FEDBD67C767320,
	ARCoreSessionSubsystem__ctor_m5B001429D8D5F84602D8D9B80B1CF759BA0E108E,
	ARCoreProvider_SetPlaybackDataset_mA5007E7F4190891CBCD26808D1C384940F65D111,
	ARCoreProvider__ctor_m4ED89ECF6C019BEEED85579209B5949941D4829D,
	ARCoreProvider_Start_mEE70994E30DE0D74573DC0B4581D478EEF5214CB,
	ARCoreProvider_Stop_m6289CF316BA0BECFC520FFAC102ED245BD242711,
	ARCoreProvider_Update_m1DD2D8BEB10DDDBE586ADF2833C7E31AE18776CB,
	ARCoreProvider_StartRecording_m2083E756A5F0C25B984A2D457DCC71790A190D26,
	ARCoreProvider_StopRecording_m7C7E4B579AA98397137BCC25C8B4F69A88AEE115,
	ARCoreProvider_get_recordingStatus_mFDA170E040FD1AAFBCC0BB35A52E23030A0A7D76,
	ARCoreProvider_get_playbackStatus_mB42156FBF5149C57277A8D5EA77D414328411B4A,
	ARCoreProvider_get_session_m2FF87072719F576B2C36DF66A1AAA185E493646C,
	ARCoreProvider_GetConfigurationDescriptors_m4738ABEFDAB96DFFA917C9DD12113E29A2D955E2,
	ARCoreProvider_add_beforeSetConfiguration_m7782F6ED649007DE9D8526072F90B03335EE5E24,
	ARCoreProvider_remove_beforeSetConfiguration_m31C541FCAAC258A6529E5D1F67633C7D0C241840,
	ARCoreProvider_SetConfigurationCallback_mF0CB81541E8AED4D6D8C13EEFC28F02C044632BA,
	ARCoreProvider_Destroy_mC45316C60AC9FB9BF1418DB042F2BB8FA09D44ED,
	ARCoreProvider_Reset_m4904D98C5694D128705E3B4FFB7B2806340A11AD,
	ARCoreProvider_OnApplicationPause_m602CC93754E61B3EBBF3BE77BC1C6DFCE607E0B9,
	ARCoreProvider_OnApplicationResume_mC57FD74CC28674FD9F27CDFC89D48544101797CA,
	ARCoreProvider_GetAvailabilityAsync_m979FCD1FFFAFB2C5040E4BFE327677F3C80D6CBF,
	ARCoreProvider_InstallAsync_m168BAF5F2DE9C10641C221D995E21AB9788CF181,
	ARCoreProvider_get_nativePtr_mF93BE814C215A6AFC2F61A7674370E8B48C58CD5,
	ARCoreProvider_get_trackingState_mEC4ED0D514E183E746B13D33E18013F3C262380A,
	ARCoreProvider_get_notTrackingReason_mB7E41389B11F85EEFD09F70CD3C9547690E9B1DC,
	ARCoreProvider_get_requestedFeatures_m6FEF431FD8F404DFB3C4D123CE6F29FFD0ECF581,
	ARCoreProvider_get_requestedTrackingMode_m5A589D5586E0010B62CBDC7C1A2A904F53920E24,
	ARCoreProvider_set_requestedTrackingMode_mE071EC7020FD5FE2DE9716583CDC0785B7C294D4,
	ARCoreProvider_get_currentTrackingMode_m7787C51BDAEC4C0BECCF949E5167EA9CE2B167F2,
	ARCoreProvider_get_matchFrameRateEnabled_m69389E0BF3F0C505377DD41CFD76D6A1171A4336,
	ARCoreProvider_get_matchFrameRateRequested_mFAA42AF98B9E52864A2580A4BA69A7F032AA8DD1,
	ARCoreProvider_set_matchFrameRateRequested_m4E81D03C9640EC1026C30982C6F9E4D51A3F40F3,
	ARCoreProvider_get_frameRate_mA33383747900A3E5A6303CD8B1369E59EE982D25,
	NULL,
	ARCoreProvider_OnApkInstallation_mCF471D5BC5E1FE4740696294320C033495CF3274,
	ARCoreProvider_OnCheckApkAvailability_mBEC148833875195FFBB34791C412F2DBA1BC2009,
	ARCoreProvider_CameraPermissionRequestProvider_m19E1C66D509D30992AD54875823B7257595D9B0C,
	NULL,
	ARCoreProvider_IssueRenderEventAndWaitForCompletion_mE8F32C2E68F7720D35BD8E2588D711F37DEDA45F,
	ARCoreProvider_CreateTexture_m2276885716E7619E56DE079F26F3BCD9A8DB0F19,
	ARCoreProvider_DeleteTexture_mA1B6EF45277ED86092FA33651D3E71B757D2765E,
	U3CU3Ec__cctor_m4B21997B813DCFD2418A9CF0A213B5820118E8DE,
	U3CU3Ec__ctor_m3637F367C80968CD4BF62CC49619A5E47A405FF7,
	U3CU3Ec_U3CGetAvailabilityAsyncU3Eb__24_0_m1C4FCBC446E6CE9AA042D2D5C302A7C55C73917C,
	U3CU3Ec_U3CInstallAsyncU3Eb__25_0_m733F2E5B5EE87A2217388F6D03CE104E7EEFF2EA,
	U3CU3Ec__DisplayClass49_0__ctor_m117E39D5AB1AD3E8F62AAEBF1A7CBF5FD674C8B8,
	U3CU3Ec__DisplayClass49_0_U3CCameraPermissionRequestProviderU3Eb__0_m1552073AEBE7BE5A0FEEFC21A5474D71687EC66F,
	NativeApi_UnityARCore_session_getNativePtr_m18528F6B459FF784ADD0562AE6A425A8BE2079B7,
	NativeApi_ArPresto_checkApkAvailability_m9C8FA059F7751262D17FF19C4C7E1757AD68980F,
	NativeApi_ArPresto_requestApkInstallation_mDA72B2447273CF392307BD823556F2F6F2886454,
	NativeApi_UnityARCore_session_update_mA2AA5E63F4C39138285F49884CC3A104DC959371,
	NativeApi_UnityARCore_session_getConfigurationDescriptors_m7BBA66A8BAA48B7EFCAF33F5404836450A8325EE,
	NativeApi_UnityARCore_session_construct_m308B165426FBA7932F9221609BE5C8A91F5A62FA,
	NativeApi_UnityARCore_session_destroy_m41490C64B5D8D4CEDC8FBEC9BB2D06A8A070CE1C,
	NativeApi_UnityARCore_session_resume_m4D4EEAE9E869D3EADFE31BC5E53F428E09D148F9,
	NativeApi_UnityARCore_session_pause_mC45FB867DF9B9ACF5F09233C239018D9BA9BCA1E,
	NativeApi_IsPauseDesired_m58F22A291F4C7D571B0121D9CD199516365FAACC,
	NativeApi_UnityARCore_session_onApplicationResume_mCF25AF0149C9632F2F46316BFDCF421A452690DD,
	NativeApi_UnityARCore_session_onApplicationPause_m7A1811EB41333D98CD458BD5595D6E4B0FC8CC3A,
	NativeApi_UnityARCore_session_reset_m37E67AC859A2BA9139438196FC1EC790A2080E4E,
	NativeApi_UnityARCore_session_getTrackingState_mF9F59BDD77BD9326255CEBF8BA40EA5FAD8FC676,
	NativeApi_UnityARCore_session_getNotTrackingReason_mFF59C5B1997EE25016695ACF56549C193A44A240,
	NativeApi_UnityARCore_session_getRenderEventFunc_mC698B51674293447F9E18BBA86B58F16CC283BEC,
	NativeApi_UnityARCore_session_setRenderEventPending_m1E6CE4FD00F7E301AB5ED17152975F5015DDD658,
	NativeApi_UnityARCore_session_waitForRenderEvent_mFBBAF126EA997ADD267458373EFC9E9AFCCD2F21,
	NativeApi_UnityARCore_session_createTextureMainThread_mCC71FCAD112F1CBEBC904C66084E9FA93446E540,
	NativeApi_UnityARCore_session_deleteTextureMainThread_m05FD30DA04AE9427BADD4C51E9CB30F0DB93985D,
	NativeApi_UnityARCore_session_getMatchFrameRateEnabled_m89658B22B04EE28F16670CEF04C012CCB1B546E4,
	NativeApi_UnityARCore_session_getMatchFrameRateRequested_mB7196E95BFA550632211A1AC0B52153BC88DB410,
	NativeApi_UnityARCore_session_setMatchFrameRateRequested_m7A51E698032951BDFAF6AA2D9346E92A5A9DD163,
	NativeApi_GetCurrentTrackingMode_m62E19089CE7EEA9504C5E6A4DAE9CE9CF8554126,
	NativeApi_UnityARCore_session_setConfigurationDirty_mC6809AB17D1BB813BB6E6E9AD5084D88BBAF495D,
	NativeApi_UnityARCore_session_setConfigCallback_m80A0DF033301F265194CA1507B45BF9333693120,
	CameraPermissionRequestProviderDelegate__ctor_m2FA75F6A973B17CA18B4BF6BAE20702655339E27,
	CameraPermissionRequestProviderDelegate_Invoke_m05261FB991CE8933EA8041F56122E659E160E68E,
	CameraPermissionRequestProviderDelegate_BeginInvoke_m7A8B194D3BB3C4F6431A5D462099362A1AB8F3CA,
	CameraPermissionRequestProviderDelegate_EndInvoke_mB4D4796BA3D426AA561782B3BF4283FC456A207D,
	CameraPermissionsResultCallbackDelegate__ctor_mEDD148B83FBC25FEBD30084B824D552055F1A1FC,
	CameraPermissionsResultCallbackDelegate_Invoke_m1BB16747C0F77B3FB2038F05EFB05E21F9100AE4,
	CameraPermissionsResultCallbackDelegate_BeginInvoke_mA3D872A3C5F09EAD519B46A11F43670BFBAE25AA,
	CameraPermissionsResultCallbackDelegate_EndInvoke_m83C4C5BD7B5353B02F0C0F73FE20B85A2F507C72,
	ArPlaybackStatusExtensions_Playing_mAD99AB9C7F2F95BF0E0DEE2B7B47AE416BE4491A,
	ArPrestoApi_ArPresto_update_m3256D92E173DBE093C9E97E87D75D233B1EC11DC,
	ArRecordingConfig__ctor_mFB056D108F9F2F1F95700E06ECF338AB67DE5543,
	ArRecordingConfig_FromIntPtr_m2B13A3F90996565E8CC5D9BE868CFAADC49B9A76,
	ArRecordingConfig_AsIntPtr_m1ACE1150822FE9A799E42E6FEEC115FC67DB59F5,
	ArRecordingConfig_op_Explicit_m7ADA4645EDCFA59AA953418CD30A92117AF38D2A,
	ArRecordingConfig_Equals_m700366138DCCEF48563D46A885A66749A7EAFE8E,
	ArRecordingConfig_Equals_m46B9FD5ED0BA1F8EB0DE3B0263F86C4EC34D83E5,
	ArRecordingConfig_GetHashCode_mC90EE60365D20D0958B7A9AA756D964107BAAA4D,
	ArRecordingConfig_op_Equality_m7568024F054238F7F637EA2C494C6F72D01837EA,
	ArRecordingConfig_op_Inequality_mB608AB4CA58A313355257660CB6EB7ED2D862BFE,
	ArRecordingConfig_op_Equality_mDE220C849425194A089D8B279FF4E7114F177F50,
	ArRecordingConfig_op_Inequality_m89D37B4D875994BCE36238FCE735F9BB792CACBF,
	ArRecordingConfig__ctor_m1FC781FAD6F26382FFB4BD225C6E97D3404F6201,
	ArRecordingConfig_Create_mF876B3E757C5DCDA0F8FA7FD18167CCF1F9FB7F3,
	ArRecordingConfig_Dispose_mC038E6DF18BFFC5CF53C92981959209499F60F8A,
	ArRecordingConfig_Destroy_m9168B9B06A55D31577C5A806D3079A5E3345E314,
	ArRecordingConfig_GetMp4DatasetFilePath_m4914727C5AB42734B16097D5922DCAD6B018C230,
	ArRecordingConfig_GetMp4DatasetFilePath_mBF7DB775C7EEDFC816B86555680E0AAF261133D7,
	ArRecordingConfig_SetMp4DatasetFilePath_m4AB1F048A648E5A18330B326231950FCFC562C7D,
	ArRecordingConfig_SetMp4DatasetFilePath_mD9C075DB1A8848BE801670FC04D95274E61D7C91,
	ArRecordingConfig_GetAutoStopOnPause_m9A7A960FBD8F80AEE2BE64F948645BB713FC6589,
	ArRecordingConfig_GetAutoStopOnPause_mB986EEC6865278BB57D77DA3381FED172A5932E6,
	ArRecordingConfig_SetAutoStopOnPause_m8EA51CD64BE495E44E17F25BF25FDF16496B317E,
	ArRecordingConfig_SetAutoStopOnPause_mF534EB038A48E1C22DAE9977515FB91A1B991260,
	ArRecordingConfig_GetRecordingRotation_mDFD704E14CEC23CC39C9AD1C787CF9AF2D46204B,
	ArRecordingConfig_GetRecordingRotation_mB2FCAC63699D31840644B992A0F52CC1086DEF50,
	ArRecordingConfig_SetRecordingRotation_m0AB920806E19B8313DA5D2D9A5467F753FA7DCEF,
	ArRecordingConfig_SetRecordingRotation_mC4C443102073377E4DC2A1A4A9A77A4DFE1ED838,
	ArRecordingStatusExtensions_Recording_m4746E133574003F69F5E94E619FB09AE49842116,
	ArSession__ctor_m71349A9234C25226F1128347856956BE42878C23,
	ArSession_FromIntPtr_m02B16B0BF3A816FFBE101F779596072294466A73,
	ArSession_get_Null_m33442F5190387AF6E82D1A2C4FEA4E1039AC9892,
	ArSession_get_IsNull_m05BB739DEE4AF829CC1D2EFE8E0CEC2771CF01C4,
	ArSession_AsIntPtr_m73675297C396024E6179B8D4476CE2DE242B662A,
	ArSession_op_Explicit_mE2F5B79090DE262FB4F7A2AFF99758ECD81FCC58,
	ArSession_Equals_m23B510198BAD7E0B9A1484ECFCF5BF41B7DC7748,
	ArSession_Equals_m55E7D8A043929991C0F0874F2AC63F16B4EF8122,
	ArSession_GetHashCode_m5D362BBA3F33B01AC2C68D04FD433DB71CBDF21E,
	ArSession_op_Equality_m3AB32704499A14999D502E8D8F6CDDD6F1B71A59,
	ArSession_op_Inequality_mC548DD6078871311DB73111CA3E5451D72CA0EB1,
	ArSession_op_Equality_m9835A20A78F5BBF0A9DCDAD84CCDF5BFF002D664,
	ArSession_op_Inequality_mBC52B05552579BAECE16E3035658E31D944584BA,
	ArSession_SetPlaybackDataset_mC0C2D822F6267EA04C3586B10AD7BA96B48D5971,
	ArSession_SetPlaybackDataset_m9B1A29260BE38548535400804390C4C4CFD85E2C,
	ArSession_get_playbackStatus_m532E4AB78BBE269DD6635E21E7C84BAEF28FEF9B,
	ArSession_GetPlaybackStatus_m8117D2E450374C62CD05B68EE857CEB683923623,
	ArSession_StartRecording_m4E1E63E462A28E2582928B7780E77FC91A6B74AD,
	ArSession_StartRecording_m2C3C67FCFF770590DEDC9F799D31CF6A09CB58AE,
	ArSession_StopRecording_m29D650C26A1CF6074DD6EAB28D811306CEFFC674,
	ArSession_StopRecording_mFF88E937C7E8058EE164060D4F94911D6C074F24,
	ArSession_get_recordingStatus_m8D8B1CE7BD5A413E8EAACFFA910548B69273AF3C,
	ArSession_GetRecordingStatus_m819E2CAD1EC3110AB6F9C41D2B92DEA838F55CCF,
	ArString_ToString_m58566F4EAB0BEC069B61795B57AF890F9AF05961,
	ArString_ToString_m245F2065FC557EDD10CD96632E4BD5B2A7CE63FC,
	ArString_Dispose_m2D9E29EC3E98FE828F7FEAB806341561A8A193AA,
	ArString_CalculateNullTerminatedByteCount_m89C0AAFB8D621F7D1A92FF9334DC8CA6FA526EB1,
	ArString_Release_mCA1FAA5CAAD96A68BA69EB683E131907F3C9EE35,
	HashCodeUtil_Combine_m1421C2AAB5E1692D25706288CE0B50FFAF6826BD,
	HashCodeUtil_ReferenceHash_mB32987352C0437C837AC7FE8C2C92DB5B0AD29E1,
	HashCodeUtil_Combine_m108BBB0A07412E21BE8AFACC56F8E36236319DDC,
	HashCodeUtil_Combine_m168BC9083D898F7C5E85F17A6463D5FCE21FFFFC,
	HashCodeUtil_Combine_m1993DD761EBEC51AB4C18A4E375AE3F23A2EAACB,
	HashCodeUtil_Combine_mEFBEAAA76E18521EE84072BED8114B20E0B1DF9F,
	HashCodeUtil_Combine_mBF0D491AE9389CA58E90C51C1F2EFB5AE9513552,
	HashCodeUtil_Combine_m96524041136F1BEF469012EF409741D043DC3A7E,
	FlipVerticalJob_Execute_mD0741C407C244AEE8696FF8D8EAB2DCE8EB676EA,
	ConvertRFloatToGrayscaleJob_Execute_mD576D88F5CA87BA7658352FF375620774927BE7E,
	ConvertBGRA32ToGrayscaleJob_Execute_mA1B5222E6CE669BFCABC309634A1058FD0318D49,
	ConvertARGB32ToGrayscaleJob_Execute_m273542FF7E241207A99C0FC3FAF3070825F9E4FD,
	ConvertStridedToGrayscaleJob_Execute_m74E512D24E8A627CC8C269B50D359650929B44CF,
	ConversionJob_Schedule_mDE6FB8E5ABE2AAFD613FD3553FC7B81FE5EB9639,
	InputLayoutLoader_RegisterLayouts_mE774BA44F9BC72D9D002EF536CBE49AEDB13745C,
	InputLayoutLoader__ctor_mE5E843EAFDD7AFCF01BC123D17952E482AD55D23,
	ManagedReferenceImage__ctor_m7909E7255E06256DB90E301F1C5A75A834B60738,
	ManagedReferenceImage_ToReferenceImage_m6E37905684A97C30C332AEB8BD57E67128CBF3F1,
	ManagedReferenceImage_Dispose_mED4FBEB981766408C3E96556E5C311B893760C50,
	ManagedReferenceImage_AsSerializedGuid_m11A3D420BD4FA6196F7AE1AAC40527B3FD393073,
	NULL,
	ManagedReferenceImageExtensions_ToNativeArray_m268E671B0B45E023DD3466058ECFCEFCBEE4A134,
	NativeObject_ArePointersEqual_m78CCE35C1493CF89AD67B679A45687843777B576,
	NULL,
	NULL,
	ARCoreXRDepthSubsystem_RegisterDescriptor_m7D8E05D67B36AF81C5FB504C9C5CE1796F1D8782,
	ARCoreXRDepthSubsystem__ctor_mEDFE4948894B6C0B169A7960449503CD9D104477,
	ARCoreProvider_UnityARCore_pointCloud_Create_mBD2B951BD18D63675474411FAF435B70E7B0A2A9,
	ARCoreProvider_UnityARCore_pointCloud_Start_m2BCF9EAB26BBDCD636011ABD568B8832BD0B6851,
	ARCoreProvider_UnityARCore_pointCloud_Stop_m1753BDAFFC624FE10E6A3AF9CA453700429FB91D,
	ARCoreProvider_UnityARCore_pointCloud_Destroy_m253E3762A6202580325187FF5C909320327A83FF,
	ARCoreProvider_UnityARCore_pointCloud_AcquireChanges_m923F96C1B1B341C0AC41C0C00E9299BE3504F6B1,
	ARCoreProvider_UnityARCore_pointCloud_ReleaseChanges_mD4F0CBE860AA297511DAD5089C3818AB8A43CEC8,
	ARCoreProvider_UnityARCore_pointCloud_getPointCloudPtrs_m11B79CF2EFFE4ED94DB99E77A6A4DAADD5932994,
	ARCoreProvider_GetChanges_m19155AA444BC46A6C38F7BD4B45C5EB1E13A6B58,
	ARCoreProvider_GetPointCloudData_m8D457677921836A41A273609B8DFA43314DD8850,
	ARCoreProvider_GenerateGuid_mEFBF2DA86959890B2BFF0479F6D989E0A03E64D3,
	ARCoreProvider__ctor_m0341088DD918C0C66148DA578DD6FCBD193EC575,
	ARCoreProvider_Destroy_mBF4A7CB6ADFACF9EFF6291F20DAB539A8DFD686C,
	ARCoreProvider_Start_m326B11169F3055941C51D585904C683B3FDA74FE,
	ARCoreProvider_Stop_m4FAB7B9AF53CE6AB27B68A239F1D7F80456E4B35,
	ARCoreProvider__cctor_mDFD0A483D78C63198E41E7B3CE79FB04A0B9590C,
	CopyIdentifiersJob_Execute_mD5F4057E3560C7ABD74EBCEA89EFF89A64695D3C,
	ExtractConfidenceValuesJob_Execute_m261E6D89CD79EA5CF89F594039AC47FB4812D6D1,
	TransformPositionsJob_Execute_m892589A8D6347D174D643A86AB646B2DFE94A66A,
	ARCoreXRPointCloudSubsystem_RegisterDescriptor_m0ADF1B7C2FBAC5F51A6770435B045C8FFB2F676B,
	ARCoreXRPointCloudSubsystem__ctor_m2D1E90079A80190397CB4B812243CC8B436D39BF,
	ARCoreProvider_UnityARCore_pointCloud_Create_mD9A254457B10E4728C27904C4F5EAA07690B7FA6,
	ARCoreProvider_UnityARCore_pointCloud_Start_mAB67ED3E8F03CAD81932933F641E3EFB28C2E1C4,
	ARCoreProvider_UnityARCore_pointCloud_Stop_m5B2BC989AA873BF564F5C0C26D12B82E2C19BD00,
	ARCoreProvider_UnityARCore_pointCloud_Destroy_mF9B47EDA19181AF01C50ACBFD468244273C43F15,
	ARCoreProvider_UnityARCore_pointCloud_AcquireChanges_mF35F6247ACF8FF472C713F21E58BBF3BEEF78171,
	ARCoreProvider_UnityARCore_pointCloud_ReleaseChanges_m46034F39622BF5060C96F84BF248F17C52DF5DFE,
	ARCoreProvider_UnityARCore_pointCloud_getPointCloudPtrs_mCB90A68710744636718AA264122C212FB953AB90,
	ARCoreProvider_GetChanges_mB35F864B1182AC2228C64A637A6449BAB5D9DC69,
	ARCoreProvider_GetPointCloudData_m9FCD41E13C4DF76BB5F1C2DED96A035203029242,
	ARCoreProvider_GenerateGuid_m7C5047E30C06237E3F8A86CE2F29CF71DE056DA0,
	ARCoreProvider__ctor_m20F1CDCE0C7CC91A93BD1DD4D844BC6A53AE43C6,
	ARCoreProvider_Destroy_m3315E4CFF445532039365F3E9F89B445FC03681E,
	ARCoreProvider_Start_mA8510D44063B77ACBCB4108DCB6D96790573ABE2,
	ARCoreProvider_Stop_mC85AC671C4E3B9EEDAE07621830A4CC01A76BA40,
	ARCoreProvider__cctor_mFC0240C25271B13A3FF7D315D24A823C27DB5E3C,
	CopyIdentifiersJob_Execute_mDFA3C27588F3A349F79AE17292B1AFE77FD80A3C,
	ExtractConfidenceValuesJob_Execute_mB8B1A284ADBF49B178B1ECEFC7A57FD1286CAA28,
	TransformPositionsJob_Execute_m9CB6EF2CCF0635B3C368EEE6774B771C7609F928,
	RcoApi_Retain_m582B6C8729C9286A3DAD4DCE213BC4CED88BC775,
	RcoApi_Release_m274E1F614C7023ACC6F9216ABDEBC798941538D9,
	RcoApi_RetainCount_mD3E57C58D93E97555287BC2643F97B0C0F5535CE,
	StringExtensions_ToBytes_m87407EBB2A901F266250D1A89C5B7CE55B66490E,
};
extern void ArCameraConfig__ctor_m03B7CCBE0A6A9F8C9CF14EBBCB1517566DA47015_AdjustorThunk (void);
extern void ArCameraConfig_get_IsNull_m0E09F4D2B8C7F7159FBF65FCA7486A8C79741ED6_AdjustorThunk (void);
extern void ArCameraConfig_AsIntPtr_m319C668BCFE412C0D53FF26D97EDB93249B39444_AdjustorThunk (void);
extern void ArCameraConfig__ctor_mE14FEFC3215FE2A052D06E5A43C619D320B9AA19_AdjustorThunk (void);
extern void ArCameraConfig_Dispose_m165F78DBFE34B76060059B7BFC7EB367E0D626CC_AdjustorThunk (void);
extern void ArCameraConfig_GetCameraId_mE547D5D96CFA540BE2631831CB839D3D907DAC90_AdjustorThunk (void);
extern void ArCameraConfig_GetDepthSensorUsage_m5F136C8F9C5B3044C503580EB45DBC2671EC7C4F_AdjustorThunk (void);
extern void ArCameraConfig_GetFacingDirection_m3C022FA6AC02ED9BBBE8B4923E9CA397E72FB7A4_AdjustorThunk (void);
extern void ArCameraConfig_GetFpsRange_mF9215E84CF0CF2EB05D2ACD0CFDE02899C9F2533_AdjustorThunk (void);
extern void ArCameraConfig_GetImageDimensions_m3746D40ABD8902AEA59C72E1F6B23635A1A2C2C6_AdjustorThunk (void);
extern void ArCameraConfig_GetTextureDimensions_mA0E9C970FDEE0847B4FC7AABC35D82B0259648AE_AdjustorThunk (void);
extern void ArCameraConfig_Equals_m970EB204E68B633C9E34D1ED6A06EDCCD7E461E9_AdjustorThunk (void);
extern void ArCameraConfig_Equals_m475848DB0C28F764CAFC0F44E27B06BEB7564041_AdjustorThunk (void);
extern void ArCameraConfig_GetHashCode_m12BD55CF30C1466B45155A17224C4AA9AB16C4E0_AdjustorThunk (void);
extern void ArCameraConfigFilter__ctor_mAFDDE747B063B59B3DA2DD79FC20A16D497C07FA_AdjustorThunk (void);
extern void ArCameraConfigFilter__ctor_mF486A6AFC61683CFC3E52B7E5494E983D4DE83E7_AdjustorThunk (void);
extern void ArCameraConfigFilter_AsIntPtr_mC26AD4E33C4D07ED7C4970BC7C75456AC84D62BF_AdjustorThunk (void);
extern void ArCameraConfigFilter_get_IsNull_m0C4936F2612BC0BB7B4BAF08F9F0947D69EDC383_AdjustorThunk (void);
extern void ArCameraConfigFilter_GetDepthSensorUsage_mAD042C592CA038A62E72173B8B0E9224067C7C12_AdjustorThunk (void);
extern void ArCameraConfigFilter_SetDepthSensorUsage_m92C45E574F2383B585B96C7FEC9FF2CCEABC68E3_AdjustorThunk (void);
extern void ArCameraConfigFilter_GetTargetFps_m8A00967A664541D6702D5256F048DC56EBB74983_AdjustorThunk (void);
extern void ArCameraConfigFilter_SetTargetFps_mD09E6CDC671DB61E9FC443F99A26EF4C716C9766_AdjustorThunk (void);
extern void ArCameraConfigFilter_Dispose_m71514EB30557871157FCA62921F794E10EC3BD59_AdjustorThunk (void);
extern void ArCameraConfigFilter_Equals_m7A03616AACBC45070F8F65ACE805CF16ADF65494_AdjustorThunk (void);
extern void ArCameraConfigFilter_Equals_mF6B858B3A8FC33E7912163F338B1CC71C59744ED_AdjustorThunk (void);
extern void ArCameraConfigFilter_GetHashCode_mAB06AE4573E43FB7802536BD3809666832F1348A_AdjustorThunk (void);
extern void ArConfig__ctor_m0E326EEBB3B5154A7BA864AF3B31CA153C4AC072_AdjustorThunk (void);
extern void ArConfig__ctor_m807FC812AF15E78B8FD5025A164FF214A030F793_AdjustorThunk (void);
extern void ArConfig_get_IsNull_mC16E8E4A5FD7562D365B3CED6F6195F6D3A4BA98_AdjustorThunk (void);
extern void ArConfig_AsIntPtr_m9CFB8A76A4BB209848A58B8CB8CC7481BF763A42_AdjustorThunk (void);
extern void ArConfig_Equals_mCE8CA68F242CF012255732C5387E953EFB7C7572_AdjustorThunk (void);
extern void ArConfig_Equals_mAF04B9E9552EAF1D2DE31F107EF45BBC9CF8BBA4_AdjustorThunk (void);
extern void ArConfig_GetHashCode_m2AA440E48A34FDA2186E99FDF0630578AB433DC4_AdjustorThunk (void);
extern void ArConfig_Dispose_m7AFD805848963702EA520A205BF9E3AC79BFA37B_AdjustorThunk (void);
extern void ARCoreBeforeGetCameraConfigurationEventArgs_get_session_mCC2430CFD657AA39475EC7F94D12E94602318773_AdjustorThunk (void);
extern void ARCoreBeforeGetCameraConfigurationEventArgs_set_session_m0E5A8E5B0E3D93A3CAD6F54F7E44C732FCE8686A_AdjustorThunk (void);
extern void ARCoreBeforeGetCameraConfigurationEventArgs_get_filter_mF91BB4DDE06DAD980576BE33973865F4CAD2C702_AdjustorThunk (void);
extern void ARCoreBeforeGetCameraConfigurationEventArgs_set_filter_m136097E18565BAB7B14635A951EF75F91208BB08_AdjustorThunk (void);
extern void ARCoreBeforeGetCameraConfigurationEventArgs_Equals_m967BC71D3DF0590295A53C8BFA897626CC513EFC_AdjustorThunk (void);
extern void ARCoreBeforeGetCameraConfigurationEventArgs_Equals_m08904E4BE8373C7A8930D51AF33F2EF8F60BA528_AdjustorThunk (void);
extern void ARCoreBeforeGetCameraConfigurationEventArgs_GetHashCode_mBF1B9E848558FCF41FFA006B2A67018D2F405899_AdjustorThunk (void);
extern void ARCoreBeforeSetConfigurationEventArgs_get_session_mFB401F61257971B12D20BA2C2A13427ADF110405_AdjustorThunk (void);
extern void ARCoreBeforeSetConfigurationEventArgs_get_arSession_mFD007DB2BB188809848E2E6F699A87C4041137B9_AdjustorThunk (void);
extern void ARCoreBeforeSetConfigurationEventArgs_get_config_mABF124F70ED16AECD48A7AD0758798446A53DF31_AdjustorThunk (void);
extern void ARCoreBeforeSetConfigurationEventArgs_get_arConfig_m1EE2C53DDF02FA08DF8EEC1C016B7C4F5BBFC598_AdjustorThunk (void);
extern void ARCoreBeforeSetConfigurationEventArgs__ctor_mDEADF5D3E5BD90B0D3715FE63350F8B23475B297_AdjustorThunk (void);
extern void ARCoreBeforeSetConfigurationEventArgs__ctor_mE6E334059D83C27B306C1F72CD68C86E6DA4FED2_AdjustorThunk (void);
extern void ARCoreBeforeSetConfigurationEventArgs_Equals_m4B4E1CC09BDDC6E0259BAC9D254FE94F81E336BB_AdjustorThunk (void);
extern void ARCoreBeforeSetConfigurationEventArgs_GetHashCode_m6E3BC2573D8D619B0E86CCF48EB8F27283F41349_AdjustorThunk (void);
extern void ARCoreBeforeSetConfigurationEventArgs_Equals_m8969624DBC58D203BE709765D5623E86DAAAC90B_AdjustorThunk (void);
extern void ARCoreFaceRegionData_get_region_m7CD0AB1C423FD7A964F41C4CCCD14857C08177FE_AdjustorThunk (void);
extern void ARCoreFaceRegionData_get_pose_m8B0A57A837D75EC314AE73F2931075A0F10A3775_AdjustorThunk (void);
extern void ARCoreFaceRegionData__ctor_m589405112E30EB33BE5D5E10C0F5CC67D24FBB49_AdjustorThunk (void);
extern void ARCoreFaceRegionData_Equals_m2056862DD1AD264FBDD1C54E254871098B6995DC_AdjustorThunk (void);
extern void ARCoreFaceRegionData_GetHashCode_mC1E01B54503EADB48B6927CF2074C7FA4D426ECE_AdjustorThunk (void);
extern void ARCoreFaceRegionData_Equals_m17225C794C37005208BCDAAEB9DDE4F9B664D126_AdjustorThunk (void);
extern void ARCoreFaceRegionData_ToString_m01A02559D50A9AB69A98601C0DC8E707ECDDB02F_AdjustorThunk (void);
extern void ARCorePose__ctor_m1328D26F798D034CBD7F0402528B873D5994D5AF_AdjustorThunk (void);
extern void FaceRegionWithARCorePose__ctor_m1021176D2016468413133E87B6F50884A240E76D_AdjustorThunk (void);
extern void TransformPoseJob_Execute_m7EBE008B308EEF99D66204836A8954E1C6F6F1D3_AdjustorThunk (void);
extern void TransformVerticesJob_Execute_mB25F13AA4AB3152365B51B0C2ACA31FFC756D74F_AdjustorThunk (void);
extern void TransformUVsJob_Execute_m3A9C954126E9929C947C66111DC50B1A4EB8478A_AdjustorThunk (void);
extern void TransformIndicesJob_Execute_m20C5A48E77A83348D5BD86AD436EBC581C5F0AAB_AdjustorThunk (void);
extern void AddImageJob_Execute_mFF2FF44D70D3647AF25843519E6B767A99BAAF36_AdjustorThunk (void);
extern void FlipBoundaryWindingJob_Execute_m1A3A43413FBCA81A1ABDC145DC35AA533E86BC9D_AdjustorThunk (void);
extern void FlipBoundaryHandednessJob_Execute_mBDA83F26E0D4DE330FB14E62DDFB7A52219B8CA9_AdjustorThunk (void);
extern void ArRecordingConfig__ctor_mFB056D108F9F2F1F95700E06ECF338AB67DE5543_AdjustorThunk (void);
extern void ArRecordingConfig_AsIntPtr_m1ACE1150822FE9A799E42E6FEEC115FC67DB59F5_AdjustorThunk (void);
extern void ArRecordingConfig_Equals_m700366138DCCEF48563D46A885A66749A7EAFE8E_AdjustorThunk (void);
extern void ArRecordingConfig_Equals_m46B9FD5ED0BA1F8EB0DE3B0263F86C4EC34D83E5_AdjustorThunk (void);
extern void ArRecordingConfig_GetHashCode_mC90EE60365D20D0958B7A9AA756D964107BAAA4D_AdjustorThunk (void);
extern void ArRecordingConfig__ctor_m1FC781FAD6F26382FFB4BD225C6E97D3404F6201_AdjustorThunk (void);
extern void ArRecordingConfig_Dispose_mC038E6DF18BFFC5CF53C92981959209499F60F8A_AdjustorThunk (void);
extern void ArRecordingConfig_GetMp4DatasetFilePath_m4914727C5AB42734B16097D5922DCAD6B018C230_AdjustorThunk (void);
extern void ArRecordingConfig_SetMp4DatasetFilePath_m4AB1F048A648E5A18330B326231950FCFC562C7D_AdjustorThunk (void);
extern void ArRecordingConfig_GetAutoStopOnPause_m9A7A960FBD8F80AEE2BE64F948645BB713FC6589_AdjustorThunk (void);
extern void ArRecordingConfig_SetAutoStopOnPause_m8EA51CD64BE495E44E17F25BF25FDF16496B317E_AdjustorThunk (void);
extern void ArRecordingConfig_GetRecordingRotation_mDFD704E14CEC23CC39C9AD1C787CF9AF2D46204B_AdjustorThunk (void);
extern void ArRecordingConfig_SetRecordingRotation_m0AB920806E19B8313DA5D2D9A5467F753FA7DCEF_AdjustorThunk (void);
extern void ArSession__ctor_m71349A9234C25226F1128347856956BE42878C23_AdjustorThunk (void);
extern void ArSession_get_IsNull_m05BB739DEE4AF829CC1D2EFE8E0CEC2771CF01C4_AdjustorThunk (void);
extern void ArSession_AsIntPtr_m73675297C396024E6179B8D4476CE2DE242B662A_AdjustorThunk (void);
extern void ArSession_Equals_m23B510198BAD7E0B9A1484ECFCF5BF41B7DC7748_AdjustorThunk (void);
extern void ArSession_Equals_m55E7D8A043929991C0F0874F2AC63F16B4EF8122_AdjustorThunk (void);
extern void ArSession_GetHashCode_m5D362BBA3F33B01AC2C68D04FD433DB71CBDF21E_AdjustorThunk (void);
extern void ArSession_SetPlaybackDataset_mC0C2D822F6267EA04C3586B10AD7BA96B48D5971_AdjustorThunk (void);
extern void ArSession_get_playbackStatus_m532E4AB78BBE269DD6635E21E7C84BAEF28FEF9B_AdjustorThunk (void);
extern void ArSession_StartRecording_m4E1E63E462A28E2582928B7780E77FC91A6B74AD_AdjustorThunk (void);
extern void ArSession_StopRecording_m29D650C26A1CF6074DD6EAB28D811306CEFFC674_AdjustorThunk (void);
extern void ArSession_get_recordingStatus_m8D8B1CE7BD5A413E8EAACFFA910548B69273AF3C_AdjustorThunk (void);
extern void ArString_ToString_m58566F4EAB0BEC069B61795B57AF890F9AF05961_AdjustorThunk (void);
extern void ArString_ToString_m245F2065FC557EDD10CD96632E4BD5B2A7CE63FC_AdjustorThunk (void);
extern void ArString_Dispose_m2D9E29EC3E98FE828F7FEAB806341561A8A193AA_AdjustorThunk (void);
extern void ArString_CalculateNullTerminatedByteCount_m89C0AAFB8D621F7D1A92FF9334DC8CA6FA526EB1_AdjustorThunk (void);
extern void FlipVerticalJob_Execute_mD0741C407C244AEE8696FF8D8EAB2DCE8EB676EA_AdjustorThunk (void);
extern void ConvertRFloatToGrayscaleJob_Execute_mD576D88F5CA87BA7658352FF375620774927BE7E_AdjustorThunk (void);
extern void ConvertBGRA32ToGrayscaleJob_Execute_mA1B5222E6CE669BFCABC309634A1058FD0318D49_AdjustorThunk (void);
extern void ConvertARGB32ToGrayscaleJob_Execute_m273542FF7E241207A99C0FC3FAF3070825F9E4FD_AdjustorThunk (void);
extern void ConvertStridedToGrayscaleJob_Execute_m74E512D24E8A627CC8C269B50D359650929B44CF_AdjustorThunk (void);
extern void ManagedReferenceImage__ctor_m7909E7255E06256DB90E301F1C5A75A834B60738_AdjustorThunk (void);
extern void ManagedReferenceImage_ToReferenceImage_m6E37905684A97C30C332AEB8BD57E67128CBF3F1_AdjustorThunk (void);
extern void ManagedReferenceImage_Dispose_mED4FBEB981766408C3E96556E5C311B893760C50_AdjustorThunk (void);
extern void ManagedReferenceImage_AsSerializedGuid_m11A3D420BD4FA6196F7AE1AAC40527B3FD393073_AdjustorThunk (void);
extern void CopyIdentifiersJob_Execute_mD5F4057E3560C7ABD74EBCEA89EFF89A64695D3C_AdjustorThunk (void);
extern void ExtractConfidenceValuesJob_Execute_m261E6D89CD79EA5CF89F594039AC47FB4812D6D1_AdjustorThunk (void);
extern void TransformPositionsJob_Execute_m892589A8D6347D174D643A86AB646B2DFE94A66A_AdjustorThunk (void);
extern void CopyIdentifiersJob_Execute_mDFA3C27588F3A349F79AE17292B1AFE77FD80A3C_AdjustorThunk (void);
extern void ExtractConfidenceValuesJob_Execute_mB8B1A284ADBF49B178B1ECEFC7A57FD1286CAA28_AdjustorThunk (void);
extern void TransformPositionsJob_Execute_m9CB6EF2CCF0635B3C368EEE6774B771C7609F928_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[109] = 
{
	{ 0x06000003, ArCameraConfig__ctor_m03B7CCBE0A6A9F8C9CF14EBBCB1517566DA47015_AdjustorThunk },
	{ 0x06000006, ArCameraConfig_get_IsNull_m0E09F4D2B8C7F7159FBF65FCA7486A8C79741ED6_AdjustorThunk },
	{ 0x06000007, ArCameraConfig_AsIntPtr_m319C668BCFE412C0D53FF26D97EDB93249B39444_AdjustorThunk },
	{ 0x06000008, ArCameraConfig__ctor_mE14FEFC3215FE2A052D06E5A43C619D320B9AA19_AdjustorThunk },
	{ 0x06000009, ArCameraConfig_Dispose_m165F78DBFE34B76060059B7BFC7EB367E0D626CC_AdjustorThunk },
	{ 0x0600000A, ArCameraConfig_GetCameraId_mE547D5D96CFA540BE2631831CB839D3D907DAC90_AdjustorThunk },
	{ 0x0600000B, ArCameraConfig_GetDepthSensorUsage_m5F136C8F9C5B3044C503580EB45DBC2671EC7C4F_AdjustorThunk },
	{ 0x0600000C, ArCameraConfig_GetFacingDirection_m3C022FA6AC02ED9BBBE8B4923E9CA397E72FB7A4_AdjustorThunk },
	{ 0x0600000D, ArCameraConfig_GetFpsRange_mF9215E84CF0CF2EB05D2ACD0CFDE02899C9F2533_AdjustorThunk },
	{ 0x0600000E, ArCameraConfig_GetImageDimensions_m3746D40ABD8902AEA59C72E1F6B23635A1A2C2C6_AdjustorThunk },
	{ 0x0600000F, ArCameraConfig_GetTextureDimensions_mA0E9C970FDEE0847B4FC7AABC35D82B0259648AE_AdjustorThunk },
	{ 0x06000011, ArCameraConfig_Equals_m970EB204E68B633C9E34D1ED6A06EDCCD7E461E9_AdjustorThunk },
	{ 0x06000012, ArCameraConfig_Equals_m475848DB0C28F764CAFC0F44E27B06BEB7564041_AdjustorThunk },
	{ 0x06000013, ArCameraConfig_GetHashCode_m12BD55CF30C1466B45155A17224C4AA9AB16C4E0_AdjustorThunk },
	{ 0x06000021, ArCameraConfigFilter__ctor_mAFDDE747B063B59B3DA2DD79FC20A16D497C07FA_AdjustorThunk },
	{ 0x06000024, ArCameraConfigFilter__ctor_mF486A6AFC61683CFC3E52B7E5494E983D4DE83E7_AdjustorThunk },
	{ 0x06000025, ArCameraConfigFilter_AsIntPtr_mC26AD4E33C4D07ED7C4970BC7C75456AC84D62BF_AdjustorThunk },
	{ 0x06000026, ArCameraConfigFilter_get_IsNull_m0C4936F2612BC0BB7B4BAF08F9F0947D69EDC383_AdjustorThunk },
	{ 0x06000027, ArCameraConfigFilter_GetDepthSensorUsage_mAD042C592CA038A62E72173B8B0E9224067C7C12_AdjustorThunk },
	{ 0x06000028, ArCameraConfigFilter_SetDepthSensorUsage_m92C45E574F2383B585B96C7FEC9FF2CCEABC68E3_AdjustorThunk },
	{ 0x06000029, ArCameraConfigFilter_GetTargetFps_m8A00967A664541D6702D5256F048DC56EBB74983_AdjustorThunk },
	{ 0x0600002A, ArCameraConfigFilter_SetTargetFps_mD09E6CDC671DB61E9FC443F99A26EF4C716C9766_AdjustorThunk },
	{ 0x0600002B, ArCameraConfigFilter_Dispose_m71514EB30557871157FCA62921F794E10EC3BD59_AdjustorThunk },
	{ 0x0600002D, ArCameraConfigFilter_Equals_m7A03616AACBC45070F8F65ACE805CF16ADF65494_AdjustorThunk },
	{ 0x0600002E, ArCameraConfigFilter_Equals_mF6B858B3A8FC33E7912163F338B1CC71C59744ED_AdjustorThunk },
	{ 0x0600002F, ArCameraConfigFilter_GetHashCode_mAB06AE4573E43FB7802536BD3809666832F1348A_AdjustorThunk },
	{ 0x0600003A, ArConfig__ctor_m0E326EEBB3B5154A7BA864AF3B31CA153C4AC072_AdjustorThunk },
	{ 0x0600003B, ArConfig__ctor_m807FC812AF15E78B8FD5025A164FF214A030F793_AdjustorThunk },
	{ 0x0600003E, ArConfig_get_IsNull_mC16E8E4A5FD7562D365B3CED6F6195F6D3A4BA98_AdjustorThunk },
	{ 0x0600003F, ArConfig_AsIntPtr_m9CFB8A76A4BB209848A58B8CB8CC7481BF763A42_AdjustorThunk },
	{ 0x06000041, ArConfig_Equals_mCE8CA68F242CF012255732C5387E953EFB7C7572_AdjustorThunk },
	{ 0x06000042, ArConfig_Equals_mAF04B9E9552EAF1D2DE31F107EF45BBC9CF8BBA4_AdjustorThunk },
	{ 0x06000043, ArConfig_GetHashCode_m2AA440E48A34FDA2186E99FDF0630578AB433DC4_AdjustorThunk },
	{ 0x06000046, ArConfig_Dispose_m7AFD805848963702EA520A205BF9E3AC79BFA37B_AdjustorThunk },
	{ 0x06000063, ARCoreBeforeGetCameraConfigurationEventArgs_get_session_mCC2430CFD657AA39475EC7F94D12E94602318773_AdjustorThunk },
	{ 0x06000064, ARCoreBeforeGetCameraConfigurationEventArgs_set_session_m0E5A8E5B0E3D93A3CAD6F54F7E44C732FCE8686A_AdjustorThunk },
	{ 0x06000065, ARCoreBeforeGetCameraConfigurationEventArgs_get_filter_mF91BB4DDE06DAD980576BE33973865F4CAD2C702_AdjustorThunk },
	{ 0x06000066, ARCoreBeforeGetCameraConfigurationEventArgs_set_filter_m136097E18565BAB7B14635A951EF75F91208BB08_AdjustorThunk },
	{ 0x06000067, ARCoreBeforeGetCameraConfigurationEventArgs_Equals_m967BC71D3DF0590295A53C8BFA897626CC513EFC_AdjustorThunk },
	{ 0x06000068, ARCoreBeforeGetCameraConfigurationEventArgs_Equals_m08904E4BE8373C7A8930D51AF33F2EF8F60BA528_AdjustorThunk },
	{ 0x06000069, ARCoreBeforeGetCameraConfigurationEventArgs_GetHashCode_mBF1B9E848558FCF41FFA006B2A67018D2F405899_AdjustorThunk },
	{ 0x0600006C, ARCoreBeforeSetConfigurationEventArgs_get_session_mFB401F61257971B12D20BA2C2A13427ADF110405_AdjustorThunk },
	{ 0x0600006D, ARCoreBeforeSetConfigurationEventArgs_get_arSession_mFD007DB2BB188809848E2E6F699A87C4041137B9_AdjustorThunk },
	{ 0x0600006E, ARCoreBeforeSetConfigurationEventArgs_get_config_mABF124F70ED16AECD48A7AD0758798446A53DF31_AdjustorThunk },
	{ 0x0600006F, ARCoreBeforeSetConfigurationEventArgs_get_arConfig_m1EE2C53DDF02FA08DF8EEC1C016B7C4F5BBFC598_AdjustorThunk },
	{ 0x06000070, ARCoreBeforeSetConfigurationEventArgs__ctor_mDEADF5D3E5BD90B0D3715FE63350F8B23475B297_AdjustorThunk },
	{ 0x06000071, ARCoreBeforeSetConfigurationEventArgs__ctor_mE6E334059D83C27B306C1F72CD68C86E6DA4FED2_AdjustorThunk },
	{ 0x06000072, ARCoreBeforeSetConfigurationEventArgs_Equals_m4B4E1CC09BDDC6E0259BAC9D254FE94F81E336BB_AdjustorThunk },
	{ 0x06000073, ARCoreBeforeSetConfigurationEventArgs_GetHashCode_m6E3BC2573D8D619B0E86CCF48EB8F27283F41349_AdjustorThunk },
	{ 0x06000074, ARCoreBeforeSetConfigurationEventArgs_Equals_m8969624DBC58D203BE709765D5623E86DAAAC90B_AdjustorThunk },
	{ 0x060000E1, ARCoreFaceRegionData_get_region_m7CD0AB1C423FD7A964F41C4CCCD14857C08177FE_AdjustorThunk },
	{ 0x060000E2, ARCoreFaceRegionData_get_pose_m8B0A57A837D75EC314AE73F2931075A0F10A3775_AdjustorThunk },
	{ 0x060000E3, ARCoreFaceRegionData__ctor_m589405112E30EB33BE5D5E10C0F5CC67D24FBB49_AdjustorThunk },
	{ 0x060000E4, ARCoreFaceRegionData_Equals_m2056862DD1AD264FBDD1C54E254871098B6995DC_AdjustorThunk },
	{ 0x060000E5, ARCoreFaceRegionData_GetHashCode_mC1E01B54503EADB48B6927CF2074C7FA4D426ECE_AdjustorThunk },
	{ 0x060000E6, ARCoreFaceRegionData_Equals_m17225C794C37005208BCDAAEB9DDE4F9B664D126_AdjustorThunk },
	{ 0x060000E7, ARCoreFaceRegionData_ToString_m01A02559D50A9AB69A98601C0DC8E707ECDDB02F_AdjustorThunk },
	{ 0x060000F5, ARCorePose__ctor_m1328D26F798D034CBD7F0402528B873D5994D5AF_AdjustorThunk },
	{ 0x060000F6, FaceRegionWithARCorePose__ctor_m1021176D2016468413133E87B6F50884A240E76D_AdjustorThunk },
	{ 0x060000F7, TransformPoseJob_Execute_m7EBE008B308EEF99D66204836A8954E1C6F6F1D3_AdjustorThunk },
	{ 0x060000FE, TransformVerticesJob_Execute_mB25F13AA4AB3152365B51B0C2ACA31FFC756D74F_AdjustorThunk },
	{ 0x060000FF, TransformUVsJob_Execute_m3A9C954126E9929C947C66111DC50B1A4EB8478A_AdjustorThunk },
	{ 0x06000101, TransformIndicesJob_Execute_m20C5A48E77A83348D5BD86AD436EBC581C5F0AAB_AdjustorThunk },
	{ 0x0600011A, AddImageJob_Execute_mFF2FF44D70D3647AF25843519E6B767A99BAAF36_AdjustorThunk },
	{ 0x06000189, FlipBoundaryWindingJob_Execute_m1A3A43413FBCA81A1ABDC145DC35AA533E86BC9D_AdjustorThunk },
	{ 0x0600018A, FlipBoundaryHandednessJob_Execute_mBDA83F26E0D4DE330FB14E62DDFB7A52219B8CA9_AdjustorThunk },
	{ 0x06000202, ArRecordingConfig__ctor_mFB056D108F9F2F1F95700E06ECF338AB67DE5543_AdjustorThunk },
	{ 0x06000204, ArRecordingConfig_AsIntPtr_m1ACE1150822FE9A799E42E6FEEC115FC67DB59F5_AdjustorThunk },
	{ 0x06000206, ArRecordingConfig_Equals_m700366138DCCEF48563D46A885A66749A7EAFE8E_AdjustorThunk },
	{ 0x06000207, ArRecordingConfig_Equals_m46B9FD5ED0BA1F8EB0DE3B0263F86C4EC34D83E5_AdjustorThunk },
	{ 0x06000208, ArRecordingConfig_GetHashCode_mC90EE60365D20D0958B7A9AA756D964107BAAA4D_AdjustorThunk },
	{ 0x0600020D, ArRecordingConfig__ctor_m1FC781FAD6F26382FFB4BD225C6E97D3404F6201_AdjustorThunk },
	{ 0x0600020F, ArRecordingConfig_Dispose_mC038E6DF18BFFC5CF53C92981959209499F60F8A_AdjustorThunk },
	{ 0x06000211, ArRecordingConfig_GetMp4DatasetFilePath_m4914727C5AB42734B16097D5922DCAD6B018C230_AdjustorThunk },
	{ 0x06000213, ArRecordingConfig_SetMp4DatasetFilePath_m4AB1F048A648E5A18330B326231950FCFC562C7D_AdjustorThunk },
	{ 0x06000215, ArRecordingConfig_GetAutoStopOnPause_m9A7A960FBD8F80AEE2BE64F948645BB713FC6589_AdjustorThunk },
	{ 0x06000217, ArRecordingConfig_SetAutoStopOnPause_m8EA51CD64BE495E44E17F25BF25FDF16496B317E_AdjustorThunk },
	{ 0x06000219, ArRecordingConfig_GetRecordingRotation_mDFD704E14CEC23CC39C9AD1C787CF9AF2D46204B_AdjustorThunk },
	{ 0x0600021B, ArRecordingConfig_SetRecordingRotation_m0AB920806E19B8313DA5D2D9A5467F753FA7DCEF_AdjustorThunk },
	{ 0x0600021E, ArSession__ctor_m71349A9234C25226F1128347856956BE42878C23_AdjustorThunk },
	{ 0x06000221, ArSession_get_IsNull_m05BB739DEE4AF829CC1D2EFE8E0CEC2771CF01C4_AdjustorThunk },
	{ 0x06000222, ArSession_AsIntPtr_m73675297C396024E6179B8D4476CE2DE242B662A_AdjustorThunk },
	{ 0x06000224, ArSession_Equals_m23B510198BAD7E0B9A1484ECFCF5BF41B7DC7748_AdjustorThunk },
	{ 0x06000225, ArSession_Equals_m55E7D8A043929991C0F0874F2AC63F16B4EF8122_AdjustorThunk },
	{ 0x06000226, ArSession_GetHashCode_m5D362BBA3F33B01AC2C68D04FD433DB71CBDF21E_AdjustorThunk },
	{ 0x0600022B, ArSession_SetPlaybackDataset_mC0C2D822F6267EA04C3586B10AD7BA96B48D5971_AdjustorThunk },
	{ 0x0600022D, ArSession_get_playbackStatus_m532E4AB78BBE269DD6635E21E7C84BAEF28FEF9B_AdjustorThunk },
	{ 0x0600022F, ArSession_StartRecording_m4E1E63E462A28E2582928B7780E77FC91A6B74AD_AdjustorThunk },
	{ 0x06000231, ArSession_StopRecording_m29D650C26A1CF6074DD6EAB28D811306CEFFC674_AdjustorThunk },
	{ 0x06000233, ArSession_get_recordingStatus_m8D8B1CE7BD5A413E8EAACFFA910548B69273AF3C_AdjustorThunk },
	{ 0x06000235, ArString_ToString_m58566F4EAB0BEC069B61795B57AF890F9AF05961_AdjustorThunk },
	{ 0x06000236, ArString_ToString_m245F2065FC557EDD10CD96632E4BD5B2A7CE63FC_AdjustorThunk },
	{ 0x06000237, ArString_Dispose_m2D9E29EC3E98FE828F7FEAB806341561A8A193AA_AdjustorThunk },
	{ 0x06000238, ArString_CalculateNullTerminatedByteCount_m89C0AAFB8D621F7D1A92FF9334DC8CA6FA526EB1_AdjustorThunk },
	{ 0x06000242, FlipVerticalJob_Execute_mD0741C407C244AEE8696FF8D8EAB2DCE8EB676EA_AdjustorThunk },
	{ 0x06000243, ConvertRFloatToGrayscaleJob_Execute_mD576D88F5CA87BA7658352FF375620774927BE7E_AdjustorThunk },
	{ 0x06000244, ConvertBGRA32ToGrayscaleJob_Execute_mA1B5222E6CE669BFCABC309634A1058FD0318D49_AdjustorThunk },
	{ 0x06000245, ConvertARGB32ToGrayscaleJob_Execute_m273542FF7E241207A99C0FC3FAF3070825F9E4FD_AdjustorThunk },
	{ 0x06000246, ConvertStridedToGrayscaleJob_Execute_m74E512D24E8A627CC8C269B50D359650929B44CF_AdjustorThunk },
	{ 0x0600024A, ManagedReferenceImage__ctor_m7909E7255E06256DB90E301F1C5A75A834B60738_AdjustorThunk },
	{ 0x0600024B, ManagedReferenceImage_ToReferenceImage_m6E37905684A97C30C332AEB8BD57E67128CBF3F1_AdjustorThunk },
	{ 0x0600024C, ManagedReferenceImage_Dispose_mED4FBEB981766408C3E96556E5C311B893760C50_AdjustorThunk },
	{ 0x0600024D, ManagedReferenceImage_AsSerializedGuid_m11A3D420BD4FA6196F7AE1AAC40527B3FD393073_AdjustorThunk },
	{ 0x06000264, CopyIdentifiersJob_Execute_mD5F4057E3560C7ABD74EBCEA89EFF89A64695D3C_AdjustorThunk },
	{ 0x06000265, ExtractConfidenceValuesJob_Execute_m261E6D89CD79EA5CF89F594039AC47FB4812D6D1_AdjustorThunk },
	{ 0x06000266, TransformPositionsJob_Execute_m892589A8D6347D174D643A86AB646B2DFE94A66A_AdjustorThunk },
	{ 0x06000278, CopyIdentifiersJob_Execute_mDFA3C27588F3A349F79AE17292B1AFE77FD80A3C_AdjustorThunk },
	{ 0x06000279, ExtractConfidenceValuesJob_Execute_mB8B1A284ADBF49B178B1ECEFC7A57FD1286CAA28_AdjustorThunk },
	{ 0x0600027A, TransformPositionsJob_Execute_m9CB6EF2CCF0635B3C368EEE6774B771C7609F928_AdjustorThunk },
};
static const int32_t s_InvokerIndices[638] = 
{
	11831,
	7776,
	6178,
	11069,
	11710,
	7540,
	7620,
	6084,
	7776,
	5443,
	5071,
	5071,
	3900,
	3900,
	3900,
	11244,
	4319,
	4450,
	7618,
	10091,
	10091,
	10025,
	10025,
	10728,
	11560,
	9838,
	9838,
	9838,
	9108,
	9108,
	9108,
	11070,
	6178,
	11071,
	11711,
	6084,
	7620,
	7540,
	5071,
	2813,
	5071,
	2813,
	7776,
	11245,
	4320,
	4450,
	7618,
	10092,
	10092,
	10026,
	10026,
	10728,
	11561,
	9839,
	9840,
	9839,
	9840,
	6178,
	6084,
	11072,
	11712,
	7540,
	7620,
	11246,
	4321,
	4450,
	7618,
	10093,
	10093,
	7776,
	10027,
	10027,
	10728,
	11562,
	11802,
	7776,
	7776,
	7776,
	7776,
	1849,
	2041,
	1316,
	4541,
	11802,
	11802,
	11802,
	8008,
	11558,
	10173,
	9349,
	11106,
	7776,
	10829,
	11796,
	11724,
	11754,
	11724,
	11802,
	7529,
	6084,
	7526,
	6081,
	4286,
	4450,
	7618,
	10067,
	10067,
	7620,
	7529,
	7620,
	7527,
	3381,
	2811,
	4287,
	7618,
	4450,
	10068,
	10068,
	11754,
	11802,
	6213,
	6213,
	11086,
	7776,
	11802,
	7656,
	7540,
	7540,
	7656,
	7540,
	7540,
	6094,
	7540,
	7776,
	7763,
	7763,
	6305,
	7776,
	7776,
	7776,
	2109,
	7540,
	6094,
	7540,
	6176,
	7763,
	6305,
	7763,
	4283,
	1829,
	7358,
	5954,
	1830,
	4283,
	6213,
	6213,
	6213,
	6213,
	9862,
	11802,
	10787,
	11574,
	11802,
	11802,
	11802,
	10236,
	11724,
	11796,
	11086,
	10409,
	11576,
	11086,
	11220,
	10008,
	11558,
	11724,
	11796,
	11574,
	11754,
	10138,
	5130,
	6176,
	6176,
	1258,
	4412,
	610,
	611,
	2298,
	1254,
	1073,
	2110,
	7776,
	11802,
	10138,
	11209,
	11574,
	11574,
	9302,
	11093,
	8670,
	8671,
	10371,
	9301,
	9128,
	11802,
	7776,
	7776,
	7776,
	7776,
	7776,
	7540,
	6094,
	7540,
	7540,
	6094,
	7540,
	1850,
	11574,
	11802,
	11802,
	11802,
	11802,
	11802,
	9072,
	7618,
	7671,
	3182,
	4288,
	7618,
	4450,
	7656,
	10069,
	10069,
	1771,
	11802,
	11802,
	11802,
	8019,
	8008,
	11558,
	10015,
	11558,
	11802,
	7776,
	3532,
	3276,
	6176,
	7776,
	7776,
	7776,
	1771,
	1851,
	7776,
	6176,
	6176,
	0,
	6176,
	11351,
	6213,
	11256,
	7776,
	7540,
	7618,
	5130,
	5069,
	3763,
	325,
	461,
	6490,
	7618,
	7618,
	4450,
	4450,
	10162,
	10162,
	10434,
	11211,
	10416,
	11255,
	11211,
	11802,
	7776,
	8047,
	11351,
	11802,
	7776,
	11802,
	7776,
	7776,
	6213,
	5461,
	1854,
	7776,
	7618,
	6176,
	7618,
	11576,
	11802,
	8008,
	11558,
	11746,
	7776,
	7656,
	7656,
	7656,
	7656,
	7656,
	7656,
	7656,
	7656,
	7656,
	7656,
	7656,
	7656,
	7540,
	7540,
	7540,
	7540,
	7776,
	11802,
	7776,
	11802,
	7776,
	7776,
	7776,
	7776,
	7776,
	7618,
	6176,
	7618,
	7540,
	7540,
	6094,
	7618,
	6176,
	7618,
	4283,
	4283,
	4283,
	4283,
	7656,
	4283,
	4283,
	7656,
	1830,
	2802,
	11802,
	11746,
	10757,
	11802,
	11802,
	11802,
	11746,
	11574,
	11746,
	11086,
	10008,
	11558,
	11724,
	11724,
	11086,
	11096,
	10792,
	11802,
	6213,
	6213,
	7776,
	7776,
	11754,
	11754,
	11754,
	11802,
	7776,
	7776,
	7776,
	1771,
	1848,
	7776,
	7618,
	6176,
	7618,
	11802,
	11802,
	8008,
	11558,
	11746,
	11574,
	11746,
	11802,
	10015,
	10053,
	7776,
	7776,
	6176,
	0,
	0,
	0,
	11802,
	7776,
	7776,
	7776,
	7776,
	1853,
	1329,
	6292,
	597,
	598,
	8616,
	8178,
	11558,
	9354,
	11587,
	11802,
	11802,
	8008,
	11558,
	11802,
	7776,
	7776,
	6056,
	7776,
	7529,
	5070,
	7618,
	5164,
	7618,
	7618,
	7618,
	6213,
	6213,
	11802,
	7776,
	5164,
	7776,
	7776,
	7776,
	3536,
	5070,
	7618,
	7618,
	7618,
	7529,
	3764,
	6213,
	6213,
	9841,
	7776,
	7776,
	7776,
	7776,
	7656,
	7656,
	7620,
	7618,
	7618,
	7763,
	7763,
	6305,
	7763,
	7540,
	7540,
	6094,
	7618,
	0,
	10759,
	10759,
	10787,
	0,
	6176,
	7776,
	7776,
	11802,
	7776,
	6178,
	6178,
	7776,
	3418,
	11748,
	10787,
	9846,
	9127,
	9763,
	11578,
	11802,
	11570,
	11802,
	11724,
	11802,
	11802,
	11802,
	11746,
	11746,
	11748,
	11802,
	11802,
	11802,
	11802,
	11724,
	11724,
	11564,
	11796,
	11802,
	10787,
	3432,
	3432,
	973,
	6213,
	3432,
	2823,
	952,
	6213,
	11093,
	11802,
	6178,
	11073,
	7620,
	11247,
	4322,
	4450,
	7618,
	10094,
	10094,
	10028,
	10028,
	6084,
	10728,
	7776,
	11563,
	5443,
	9842,
	2814,
	9842,
	4323,
	9842,
	2812,
	9843,
	5071,
	9842,
	2813,
	9843,
	11093,
	6178,
	11074,
	11713,
	7540,
	7620,
	11248,
	4323,
	4450,
	7618,
	10095,
	10095,
	10029,
	10029,
	5164,
	10361,
	7618,
	10728,
	5070,
	10362,
	7618,
	11200,
	7618,
	10728,
	7656,
	5461,
	7776,
	7618,
	11558,
	10368,
	11213,
	9417,
	8751,
	8250,
	8094,
	8021,
	7983,
	6176,
	6176,
	6176,
	6176,
	6176,
	8383,
	11802,
	7776,
	6340,
	7796,
	7776,
	5556,
	0,
	9984,
	10030,
	0,
	0,
	11802,
	7776,
	11578,
	11802,
	11802,
	11802,
	8008,
	11558,
	9455,
	1852,
	3561,
	11740,
	7776,
	7776,
	7776,
	7776,
	11802,
	6176,
	6176,
	6176,
	11802,
	7776,
	11578,
	11802,
	11802,
	11802,
	8008,
	11558,
	9455,
	1852,
	3561,
	11740,
	7776,
	7776,
	7776,
	7776,
	11802,
	6176,
	6176,
	6176,
	11211,
	11211,
	11211,
	9203,
};
static const Il2CppTokenIndexMethodTuple s_reversePInvokeIndices[7] = 
{
	{ 0x0600009F, 7,  (void**)&ARCoreProvider_OnBeforeGetCameraConfiguration_mA33A6474A489CA38E45F609013293D679AF4D06A_RuntimeMethod_var, 0 },
	{ 0x060001BE, 11,  (void**)&ARCoreProvider_SetConfigurationCallback_mF0CB81541E8AED4D6D8C13EEFC28F02C044632BA_RuntimeMethod_var, 0 },
	{ 0x060001D1, 9,  (void**)&ARCoreProvider_OnApkInstallation_mCF471D5BC5E1FE4740696294320C033495CF3274_RuntimeMethod_var, 0 },
	{ 0x060001D2, 10,  (void**)&ARCoreProvider_OnCheckApkAvailability_mBEC148833875195FFBB34791C412F2DBA1BC2009_RuntimeMethod_var, 0 },
	{ 0x060001D3, 8,  (void**)&ARCoreProvider_CameraPermissionRequestProvider_m19E1C66D509D30992AD54875823B7257595D9B0C_RuntimeMethod_var, 0 },
	{ 0x0600025E, 12,  (void**)&ARCoreProvider_GenerateGuid_mEFBF2DA86959890B2BFF0479F6D989E0A03E64D3_RuntimeMethod_var, 0 },
	{ 0x06000272, 13,  (void**)&ARCoreProvider_GenerateGuid_m7C5047E30C06237E3F8A86CE2F29CF71DE056DA0_RuntimeMethod_var, 0 },
};
static const Il2CppTokenRangePair s_rgctxIndices[7] = 
{
	{ 0x02000023, { 0, 2 } },
	{ 0x02000034, { 2, 6 } },
	{ 0x060001D0, { 8, 3 } },
	{ 0x060001D4, { 11, 3 } },
	{ 0x0600024E, { 14, 1 } },
	{ 0x06000251, { 15, 4 } },
	{ 0x06000252, { 19, 4 } },
};
extern const uint32_t g_rgctx_T_tC93B8EF28F046D264F92366A6B297457CCEB62E6;
extern const uint32_t g_rgctx_Triangle_1_tD3E528A02E95183159768EEE674119FC841A111B;
extern const uint32_t g_rgctx_ARCorePromise_1_t39173C769B9641ED7F517E029E150FAD6E0B6D86;
extern const uint32_t g_rgctx_ARCorePromise_1_t39173C769B9641ED7F517E029E150FAD6E0B6D86;
extern const uint32_t g_rgctx_Promise_1_t0F357053732069509DB5351016A7B790C898D1A7;
extern const uint32_t g_rgctx_T_tBFC275D545653B2258E6D34C786B453391905F6B;
extern const uint32_t g_rgctx_Promise_1_Resolve_mE3AD425DBD70265B5F2726E09B058A33B6CCD869;
extern const uint32_t g_rgctx_Promise_1__ctor_mF90270485F2AA6FFA057B436D291F61A72FBE631;
extern const uint32_t g_rgctx_ARCorePromise_1_t0653AD3DBDE56F2C320EB2FDB0A02B6ADFEB95A5;
extern const uint32_t g_rgctx_ARCorePromise_1__ctor_m997F3FB2F2B72AFE819DEA7B5EEF13038E860BAA;
extern const uint32_t g_rgctx_Promise_1_tA5B7BC2AA7ADC62A919D520B121D607916FC96B9;
extern const uint32_t g_rgctx_ARCorePromise_1_tCD842FBA93353554BD749262ECA90D6DB5B234CE;
extern const uint32_t g_rgctx_T_tA4622C9D57959DCC87554997E413E664F64EEAD8;
extern const uint32_t g_rgctx_ARCorePromise_1_Resolve_mE9076AEA6F8B6C06BF2AEC809D1EA3D34351EF57;
extern const uint32_t g_rgctx_T_t09B70E3997A358D791962D4B0F57C31F9A4A702D;
extern const uint32_t g_rgctx_NativeArray_1_tA795BD7C5B908F02D296AE263348D611927E12C2;
extern const uint32_t g_rgctx_NativeArrayUnsafeUtility_GetUnsafePtr_TisT_t4F6CDE285BB8E1F3C535FE3C3E23FE176145B31D_m361E2D8784B809CFC5D41C439FCAA4551361ACDB;
extern const uint32_t g_rgctx_NativeArray_1_get_Length_mCBBB0A30975D0EE831B6E7EB8ABE5793B7E94042;
extern const uint32_t g_rgctx_NativeArray_1_tA795BD7C5B908F02D296AE263348D611927E12C2;
extern const uint32_t g_rgctx_NativeSlice_1_tF827BF8D86F72FE8BFE7A6774ECDE83AF11355E4;
extern const uint32_t g_rgctx_NativeSliceUnsafeUtility_GetUnsafePtr_TisT_t8C5CDFC8D8C08F2AA39331F70A857685E967A28F_m823385ADF87EACA9A3C133EA98FE1189F725688C;
extern const uint32_t g_rgctx_NativeSlice_1_get_Length_m39C21DE1BC88DB3B86737F6D19092380544016C4;
extern const uint32_t g_rgctx_NativeSlice_1_tF827BF8D86F72FE8BFE7A6774ECDE83AF11355E4;
static const Il2CppRGCTXDefinition s_rgctxValues[23] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tC93B8EF28F046D264F92366A6B297457CCEB62E6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Triangle_1_tD3E528A02E95183159768EEE674119FC841A111B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ARCorePromise_1_t39173C769B9641ED7F517E029E150FAD6E0B6D86 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ARCorePromise_1_t39173C769B9641ED7F517E029E150FAD6E0B6D86 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Promise_1_t0F357053732069509DB5351016A7B790C898D1A7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tBFC275D545653B2258E6D34C786B453391905F6B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Promise_1_Resolve_mE3AD425DBD70265B5F2726E09B058A33B6CCD869 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Promise_1__ctor_mF90270485F2AA6FFA057B436D291F61A72FBE631 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ARCorePromise_1_t0653AD3DBDE56F2C320EB2FDB0A02B6ADFEB95A5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ARCorePromise_1__ctor_m997F3FB2F2B72AFE819DEA7B5EEF13038E860BAA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Promise_1_tA5B7BC2AA7ADC62A919D520B121D607916FC96B9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ARCorePromise_1_tCD842FBA93353554BD749262ECA90D6DB5B234CE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tA4622C9D57959DCC87554997E413E664F64EEAD8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ARCorePromise_1_Resolve_mE9076AEA6F8B6C06BF2AEC809D1EA3D34351EF57 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t09B70E3997A358D791962D4B0F57C31F9A4A702D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tA795BD7C5B908F02D296AE263348D611927E12C2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArrayUnsafeUtility_GetUnsafePtr_TisT_t4F6CDE285BB8E1F3C535FE3C3E23FE176145B31D_m361E2D8784B809CFC5D41C439FCAA4551361ACDB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Length_mCBBB0A30975D0EE831B6E7EB8ABE5793B7E94042 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tA795BD7C5B908F02D296AE263348D611927E12C2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeSlice_1_tF827BF8D86F72FE8BFE7A6774ECDE83AF11355E4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSliceUnsafeUtility_GetUnsafePtr_TisT_t8C5CDFC8D8C08F2AA39331F70A857685E967A28F_m823385ADF87EACA9A3C133EA98FE1189F725688C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSlice_1_get_Length_m39C21DE1BC88DB3B86737F6D19092380544016C4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeSlice_1_tF827BF8D86F72FE8BFE7A6774ECDE83AF11355E4 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_XR_ARCore_CodeGenModule;
const Il2CppCodeGenModule g_Unity_XR_ARCore_CodeGenModule = 
{
	"Unity.XR.ARCore.dll",
	638,
	s_methodPointers,
	109,
	s_adjustorThunks,
	s_InvokerIndices,
	7,
	s_reversePInvokeIndices,
	7,
	s_rgctxIndices,
	23,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
