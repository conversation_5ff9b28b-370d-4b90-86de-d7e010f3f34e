﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void AndroidJNIHelper_get_debug_m0F67D4E412EBE1D511C42F5F12294C63E669C907 (void);
extern void AndroidJNIHelper_set_debug_m66E40D20DC9DC69BD7B139DA5C367BA1185FCA3F (void);
extern void AndroidJNIHelper_GetConstructorID_mCF5EAC779FFBD1129C2E28FE1C2171E6FF8AAE95 (void);
extern void AndroidJNIHelper_GetConstructorID_m2D883140A087C1CDB74FE9195D14643CB9A854F0 (void);
extern void AndroidJNIHelper_GetMethodID_m5D0526B2FE20191F966D72521647D686980EE06B (void);
extern void AndroidJNIHelper_GetMethodID_m58B68CA9B567BF23064BD8891CEA509FF8FA7C76 (void);
extern void AndroidJNIHelper_GetMethodID_mDB705DC228B1BB30E6595068797FB3F2A1817BB8 (void);
extern void AndroidJNIHelper_GetFieldID_mB209E2F32D04CA5BCD7D6EC10A0C8DC0ED5D0D3B (void);
extern void AndroidJNIHelper_GetFieldID_m736A1E39AEE90F1DD3C352FA3F1988D4CFD9778F (void);
extern void AndroidJNIHelper_GetFieldID_mDA4775DFA37126A7EFEBCA8E68EF9640F3D6EF01 (void);
extern void AndroidJNIHelper_CreateJavaRunnable_mBEE8C5060C69EE5C6922D4BA06C261055A3FF99F (void);
extern void AndroidJNIHelper_CreateJavaProxy_m75CA3C0BF15517CD52658E08F9FCBA1022822E6F (void);
extern void AndroidJNIHelper_ConvertToJNIArray_mBEAE4605FF297D19AFB8CE4E8443C9C0F87E9A13 (void);
extern void AndroidJNIHelper_CreateJNIArgArray_mC12C279EEB43D740F42C5E90301DC54F6E04B876 (void);
extern void AndroidJNIHelper_CreateJNIArgArray_mD8E0CA2404E31F155EDE1A028EC686C17B17730F (void);
extern void AndroidJNIHelper_DeleteJNIArgArray_mBDC874B32FF09E6B48E18B2A58794C0A4DE2FA23 (void);
extern void AndroidJNIHelper_DeleteJNIArgArray_m4FC468F2AD104C5B159A0EF496BF215C7260DCC9 (void);
extern void AndroidJNIHelper_GetConstructorID_m0FDAC24E463246206BA8FBDE44B4A73D6CBF3D40 (void);
extern void AndroidJNIHelper_GetMethodID_m283E294AA8DD3F6721A4173CE4C31038B98AA7E5 (void);
extern void AndroidJNIHelper_GetSignature_mE8DBE8ABB6506BBF2806880DA2FBB876DD88A047 (void);
extern void AndroidJNIHelper_GetSignature_m1B2D7B48C9E9D7BB1F10AD60D541EA7F9BE18F45 (void);
extern void AndroidJNIHelper_Box_mBC04AC9A326AC66A599C56CB1C8EAE11940A72A3 (void);
extern void AndroidJNIHelper_Box_mBFE16B308EDC2E454BB9ED219101EB9248D636B4 (void);
extern void AndroidJNIHelper_Box_mE0E4CB7D7168C7CA04F8B70C3A9C34F2B4CFAF76 (void);
extern void AndroidJNIHelper_Box_m2C8ACD78C001428B06BA5B1E5D7341B68A484EE1 (void);
extern void AndroidJNIHelper_Box_m222D02EBC4F32DCBF9DDE8879EF877762E310B6B (void);
extern void AndroidJNIHelper_Box_m2CDD9646CBB781A968A96F3F38D5D04CD5ACA1AE (void);
extern void AndroidJNIHelper_Box_mA3CD0A28FFA5A1971F9AB097842A45450D0D2300 (void);
extern void AndroidJNIHelper_Box_mB7858568094A96BF66DBF382C890B1343EF6FDF6 (void);
extern void AndroidJNIHelper_Box_m354F5BA4592A9B712951AE557F0B0011B7113C0E (void);
extern void AndroidJNIHelper_GetUnboxMethod_mB1586BDBF8F34EF7EFFDC1635C957330ADB79D85 (void);
extern void AndroidJNIHelper_Unbox_m8A1E1404D6CB2F0A3829E46A72029A76FF912F1A (void);
extern void AndroidJNIHelper_Unbox_mF1E4ABF9304F60C34D7F12F1132AA3BB76A79FBF (void);
extern void AndroidJNIHelper_Unbox_m8C65F084C98FD54EF50C8FB03300DD9004DAD45C (void);
extern void AndroidJNIHelper_Unbox_m56F15898A0377A7891B60F3F9094F59B2AD138C6 (void);
extern void AndroidJNIHelper_Unbox_m81CBC478E8A1124A8887B53DD584F1BEC56054E8 (void);
extern void AndroidJNIHelper_Unbox_mE24347592484B48D93F88221869D4178AAC25C87 (void);
extern void AndroidJNIHelper_Unbox_m48F8AD7593EA47BD6FFE270C88FE6CBA42026504 (void);
extern void AndroidJNIHelper_Unbox_mF84A7086D5E7E04EE635B6C4BF244D2338492BB0 (void);
extern void AndroidJNI_GetJavaVM_m5A3861F5CBDB0352855BE858022BA9BCB2BB6F62 (void);
extern void AndroidJNI_AttachCurrentThread_m412647220ED1F7D42F9D41F692D1D7DC4696B6D4 (void);
extern void AndroidJNI_DetachCurrentThread_mC0F3D8974C0681528B801EF0AC2A54E5C7A2E933 (void);
extern void AndroidJNI_GetVersion_m8609243FCDE009D1C5DEC7C7E40664386D5C4608 (void);
extern void AndroidJNI_FindClass_m6E9908108F8D828563AD510C5A355E71A9A493E9 (void);
extern void AndroidJNI_FromReflectedMethod_m1613634C8A528B0A2A1F6828B609F31792B7DF43 (void);
extern void AndroidJNI_FromReflectedField_m88709F5C3EDD6CB503B1C6EE32CFF6BC328171E8 (void);
extern void AndroidJNI_ToReflectedMethod_m50B8B87366FDAB0506EF2023352AB602E14CA7E2 (void);
extern void AndroidJNI_ToReflectedField_mEE3F5294258CD32FB6066A2EB256B75B04B19D3B (void);
extern void AndroidJNI_GetSuperclass_m755BF9AA2AADBE96A2C64F15AE8114BD1807AD6A (void);
extern void AndroidJNI_IsAssignableFrom_m28DB6B523875A7837F7CC60807F32D58E90F9C25 (void);
extern void AndroidJNI_Throw_m7DE4EC0AF3864EDC05EE828A57B20A1EB0C71F5E (void);
extern void AndroidJNI_ThrowNew_mDEAF3A3716A9F8D6A9DF48B6928D17740C0112AE (void);
extern void AndroidJNI_ExceptionOccurred_mAE2AE7C57E06059383EDCAB956A01BDF7D03F54C (void);
extern void AndroidJNI_ExceptionDescribe_m20B24A293E6A8467E312E028E6AA250934684508 (void);
extern void AndroidJNI_ExceptionClear_m1205CD178ADB8E0E7EBDBE349AFA767D5D7758B8 (void);
extern void AndroidJNI_FatalError_m6F93C97D6802B5FFB6A6A99D16C74234E79AF5D6 (void);
extern void AndroidJNI_PushLocalFrame_m2D8050A3799AEBB4A7E506E6790839EB66932E10 (void);
extern void AndroidJNI_PopLocalFrame_m32AF6F9065F09D80BFDD3F573B62C782F392E609 (void);
extern void AndroidJNI_NewGlobalRef_m9A06F23234FB1ECF3F482AF3A6A6148A5916E9A7 (void);
extern void AndroidJNI_DeleteGlobalRef_mC50B6C056F32BB9F44B800949FA169C728D4C41D (void);
extern void AndroidJNI_QueueDeleteGlobalRef_m1C78D0CD2C81BE9D6D6E57CD6C83CFF70F38CBF1 (void);
extern void AndroidJNI_GetQueueGlobalRefsCount_m0A44BD8FAACE454492208D0614AA18803155FB34 (void);
extern void AndroidJNI_NewWeakGlobalRef_m27D74DAC1D1F0A11796E4FA669D8CB8DBB1AF2BA (void);
extern void AndroidJNI_DeleteWeakGlobalRef_mA1F19C1656B86A22A339497C335C156648736E6D (void);
extern void AndroidJNI_NewLocalRef_m286E59F912B94D07D1CE54DFE93A631B2162CD65 (void);
extern void AndroidJNI_DeleteLocalRef_mD2A2B4F1C17A4F5863BB94F88F268E72FD120DBB (void);
extern void AndroidJNI_IsSameObject_mA37D2BE7C0E40F30E6D438A937B038E7703DFDAB (void);
extern void AndroidJNI_EnsureLocalCapacity_mD27645E03F7F82811D5AFFF6B068E226C9C93227 (void);
extern void AndroidJNI_AllocObject_m5E81D0A72F3DCDFC5D0AD62C2B93816083EE3926 (void);
extern void AndroidJNI_NewObject_mF026198FBA1D5E69719DEB52F41E9FDB8B7F93A4 (void);
extern void AndroidJNI_NewObject_m495F8FDA574E6645C469B34C297768FCF4038C45 (void);
extern void AndroidJNI_NewObjectA_m95F13B24F964C82A9EEDA81EB5120BA818A042AB (void);
extern void AndroidJNI_GetObjectClass_m418C2D7DAE432AD104209833A0DF06B16E1E74B5 (void);
extern void AndroidJNI_IsInstanceOf_m09B386C15D3FBD4A6589D9F6DD3E5F8D259F51B1 (void);
extern void AndroidJNI_GetMethodID_mA7FF961764CA4D68C4789E5A17926CE5FF9B3549 (void);
extern void AndroidJNI_GetFieldID_mC6BFB1F17EF5FC82D45F08D25C11D346E51673F2 (void);
extern void AndroidJNI_GetStaticMethodID_mA13B58796C4E210B46956723FE664B6D0130C5A3 (void);
extern void AndroidJNI_GetStaticFieldID_m89476A442BF57C00C1CBB0DA588077C2B4171654 (void);
extern void AndroidJNI_NewString_m6F3143989EFE907B5D0091850D1754421795A39B (void);
extern void AndroidJNI_NewStringFromStr_mF56D4A6456A326916DEF2E148E826F6EBC93B3CC (void);
extern void AndroidJNI_NewString_m74F9DCF2F56735C9BE6DC3831134A96C7BCA9F9F (void);
extern void AndroidJNI_NewStringUTF_mB6A0A0A1C3F931503A6329D4E232DB501B95B5B0 (void);
extern void AndroidJNI_GetStringChars_mB61E4F713A2457F7DF053DBFE4455A559DA9623C (void);
extern void AndroidJNI_GetStringLength_mD2A44213EB9B131E1DECEF34A315F2B817384760 (void);
extern void AndroidJNI_GetStringUTFLength_m5BE0CC7EE4108BA654A9F7647E66871D831B812A (void);
extern void AndroidJNI_GetStringUTFChars_m61CFD69CF31D17C2728F23656079D3E26D9D2BBB (void);
extern void AndroidJNI_CallStringMethod_m52FAF2826B75AF2AEA8F848AEC973A682216EC4C (void);
extern void AndroidJNI_CallStringMethod_m1255E35A517F4BC178F1FC3CA06A6ECDDCF08490 (void);
extern void AndroidJNI_CallStringMethodUnsafe_m9C19A5FDE56CEE8DF472517EB9F6BA8ECF004614 (void);
extern void AndroidJNI_CallObjectMethod_m21944B19534CA3EB8885BE3F1057E6A72AAC4355 (void);
extern void AndroidJNI_CallObjectMethod_mF3C4822DD3311879591F5BC730DC01ED6F24571F (void);
extern void AndroidJNI_CallObjectMethodUnsafe_m2C9A9AB2D47751AA9F2D9048E0B23ED8057C8B56 (void);
extern void AndroidJNI_CallIntMethod_mD1299CB9F99E26D2734A0F02D376DE6EF49F1741 (void);
extern void AndroidJNI_CallIntMethod_m3895BC6DD12AFFF33323C1878E32864E166D8283 (void);
extern void AndroidJNI_CallIntMethodUnsafe_m41FAB53999456E0A70C8E310BCA63C6310C0B2F6 (void);
extern void AndroidJNI_CallBooleanMethod_m78FC31B29854285F1164C75974AB463FE5716F84 (void);
extern void AndroidJNI_CallBooleanMethod_m61006AE6C02C028A0A43761330FDB9C9AE655184 (void);
extern void AndroidJNI_CallBooleanMethodUnsafe_m3DF3E7781C06330CA520F66BEA96CFF615794A4A (void);
extern void AndroidJNI_CallShortMethod_mC3C11BD9E32604C41710756104D5A8AFCFA792E4 (void);
extern void AndroidJNI_CallShortMethod_mED891B3ED4526E3CEB3C7B133460E61DCF015432 (void);
extern void AndroidJNI_CallShortMethodUnsafe_m211540B39F818D275F1F51268C9B676B809BC8DF (void);
extern void AndroidJNI_CallByteMethod_m7E2B355303C36B0992364D036BFCCF1CB4DD8154 (void);
extern void AndroidJNI_CallSByteMethod_mA98E61BB8186A06CBF4A175E29E2F0F194FB8507 (void);
extern void AndroidJNI_CallSByteMethod_mFF5A7095DF874C9536DE192057C0624F7A500FF6 (void);
extern void AndroidJNI_CallSByteMethodUnsafe_m40F4B14CAAF4B55614D741C32DACCE3D2CAE68A7 (void);
extern void AndroidJNI_CallCharMethod_m560F0E113CA6E4F4A73BDAF93D53DADDA8D2047B (void);
extern void AndroidJNI_CallCharMethod_m6319B0007021421F5EA1D3660F1322AFE56E9547 (void);
extern void AndroidJNI_CallCharMethodUnsafe_m258168724E6F6CC0313A584A885A061E0B543698 (void);
extern void AndroidJNI_CallFloatMethod_mF94056CFCC7E045F7B350D2D3285335482A2AE8E (void);
extern void AndroidJNI_CallFloatMethod_mBED49D3756A1A8FB3038A454AA421734DF87E609 (void);
extern void AndroidJNI_CallFloatMethodUnsafe_mA19B4DA3E878B13479A41063AAA0053D97D25AE5 (void);
extern void AndroidJNI_CallDoubleMethod_m9001B2EF56623D6F17B4E9E87788CDD3E760A897 (void);
extern void AndroidJNI_CallDoubleMethod_m4A66035AEA817B8F0C1C2C0FA009447116E9A686 (void);
extern void AndroidJNI_CallDoubleMethodUnsafe_m211C3DA7C876DD9C5B925230C220F6E51A6D245F (void);
extern void AndroidJNI_CallLongMethod_mDE82FA5F26CBE0E0F8251D3A7698D376524E1A4B (void);
extern void AndroidJNI_CallLongMethod_m63392BAB11BF3A055E39B43A0FB3BFF959AD5253 (void);
extern void AndroidJNI_CallLongMethodUnsafe_m0AE09B468D8A3EB89920E9570731E95725A5F3C8 (void);
extern void AndroidJNI_CallVoidMethod_mFCFF6A5FF4A51305C3D2DBFB3A0699617EF40D48 (void);
extern void AndroidJNI_CallVoidMethod_m5A3A884F6B2257FF6D7FDD769AE54B508EA5CD88 (void);
extern void AndroidJNI_CallVoidMethodUnsafe_mCF5A52E0A9D82069CAAD4365D82E39B467B80DCD (void);
extern void AndroidJNI_GetStringField_m6A2FA57794ADA8735B103FF5D4819F3C4813992F (void);
extern void AndroidJNI_GetObjectField_m5E3C5FF1582F4A62155220FDCB849574C0E36AD6 (void);
extern void AndroidJNI_GetBooleanField_mC900428E2FD1E55BA21808A31B760FB0F10BC047 (void);
extern void AndroidJNI_GetByteField_m675BEFB1024363DE587C000406B8A09C5762B847 (void);
extern void AndroidJNI_GetSByteField_mB81412A4748ABB311535E2B73092569AAE6CB735 (void);
extern void AndroidJNI_GetCharField_m7DA94A98ED33A97EE7874872C9F51515F151F019 (void);
extern void AndroidJNI_GetShortField_m5EEACBB3920623AD54D9DF77499E8BA92B35E3D8 (void);
extern void AndroidJNI_GetIntField_m6B78A3F6F8EE6D1ADEDECF1EC145BC9C5AE37E88 (void);
extern void AndroidJNI_GetLongField_mC2DC315C44320CE9A406B95A4CAA1117A0FF56A8 (void);
extern void AndroidJNI_GetFloatField_m5C92103D7307A19F72F28DD40118F84D91C19A39 (void);
extern void AndroidJNI_GetDoubleField_mE2B23D9F1363B48811B6207BEF8A18B39CB3B22B (void);
extern void AndroidJNI_SetStringField_mE9B2983BC7C61C0EEEA3FA31484B570B48E210DC (void);
extern void AndroidJNI_SetObjectField_m6BA777B66D76ECD1E34D69D800A8F2F51C51026C (void);
extern void AndroidJNI_SetBooleanField_m9A22242BD25A8B3802C05F70C2EB5ACE7E8BF2A0 (void);
extern void AndroidJNI_SetByteField_mA1EBC3D4A3734B8064330782DC240DD1775C7C4B (void);
extern void AndroidJNI_SetSByteField_mA360303CC36670BDC442E1366D64333A5363D09F (void);
extern void AndroidJNI_SetCharField_m86543FBDB219D090EFEA141F679CCD22E195B680 (void);
extern void AndroidJNI_SetShortField_m8811FC677647B47F855FC9533D51437517B53066 (void);
extern void AndroidJNI_SetIntField_mCAB8E0B5C4F1773F6CAF81731DFB224FB78F0138 (void);
extern void AndroidJNI_SetLongField_m763D39D8B341907F54921AF5EFE4E0C37EB44B00 (void);
extern void AndroidJNI_SetFloatField_m953B7DC631E56432E2AB59154CAC15EE10B28E02 (void);
extern void AndroidJNI_SetDoubleField_m30FB81E1DEE48DB3C22117F3D017813A204B474D (void);
extern void AndroidJNI_CallStaticStringMethod_m7E8E3AFF8296764C324060E65B052B23500C18AB (void);
extern void AndroidJNI_CallStaticStringMethod_m42628886D81E2DB3F9802AEC7A872B5EB5044FE7 (void);
extern void AndroidJNI_CallStaticStringMethodUnsafe_m809DC1DE90B6FC3564CCDAB919740DF1916EC552 (void);
extern void AndroidJNI_CallStaticObjectMethod_m5D0C02761602E6ED1AE4FAD90B7762A6376A35D5 (void);
extern void AndroidJNI_CallStaticObjectMethod_m563873CBBD357A5301C122CAF6563B8AA302E43C (void);
extern void AndroidJNI_CallStaticObjectMethodUnsafe_m3892FD7D99D186C1AE028F8E49C660737E1DF89D (void);
extern void AndroidJNI_CallStaticIntMethod_m7AA48D4603F398E99E45DF3E057BB58FB6D374FC (void);
extern void AndroidJNI_CallStaticIntMethod_mD9A45F8ECDE53096DDAC297DEDA6D6309C80BBC3 (void);
extern void AndroidJNI_CallStaticIntMethodUnsafe_mD53EAF407A8B5062744982BFD22F22B340065BEA (void);
extern void AndroidJNI_CallStaticBooleanMethod_m6D035B0525AF900D6BF3F91C174C093FE2531453 (void);
extern void AndroidJNI_CallStaticBooleanMethod_mD168ECFAF2855BE5A4E61B5EF3EFADAAA7297734 (void);
extern void AndroidJNI_CallStaticBooleanMethodUnsafe_m3F99D47A3B64BC4477DCF3228859601094BE1FAF (void);
extern void AndroidJNI_CallStaticShortMethod_m10E61636B448E36034CD6DE902A286B0257C7586 (void);
extern void AndroidJNI_CallStaticShortMethod_m78EEA97BFBC305BCB3B7578A81EF245F71ADA1A9 (void);
extern void AndroidJNI_CallStaticShortMethodUnsafe_m8379FEC6D9133BC9CEA171BE5A82351576CA5C9C (void);
extern void AndroidJNI_CallStaticByteMethod_mFC6A3C4731086DD6CD298C5EDC86C0211B90C69C (void);
extern void AndroidJNI_CallStaticSByteMethod_mA2BA2E72F9CF73A04E15420CDCCFD7CFD809E0DA (void);
extern void AndroidJNI_CallStaticSByteMethod_m0EF2AF8C9395B147DCE092F9A1778BF0FB23ACDB (void);
extern void AndroidJNI_CallStaticSByteMethodUnsafe_m1921CB76340B4B912D290B317CE1D1445A803F65 (void);
extern void AndroidJNI_CallStaticCharMethod_mCC7C287BA2CD34A0526577D194237277F9708353 (void);
extern void AndroidJNI_CallStaticCharMethod_m8A6B5AAB05E1AC468D1E927A02E9ECEA46D933DB (void);
extern void AndroidJNI_CallStaticCharMethodUnsafe_mB7CBD75837AAED45237D624B79F081DC1E60967C (void);
extern void AndroidJNI_CallStaticFloatMethod_m19DBC375D9E707CA40BE8D997C84F553EF71040A (void);
extern void AndroidJNI_CallStaticFloatMethod_mEC10B7CC01D5C2933E9A993D8C9683EE6C5EB5CD (void);
extern void AndroidJNI_CallStaticFloatMethodUnsafe_m68A9454AF94B2DD4F31C2AA29A412D4599010435 (void);
extern void AndroidJNI_CallStaticDoubleMethod_m052484ED56097C439FA22A89CA0FE393BBFFD305 (void);
extern void AndroidJNI_CallStaticDoubleMethod_m3409D4BF5A4801BCAB79E4EE0498CD358B283F4A (void);
extern void AndroidJNI_CallStaticDoubleMethodUnsafe_m2B11C8E55CB6CFB33749CF1F8750F5D7669BFEFB (void);
extern void AndroidJNI_CallStaticLongMethod_mC103D7C5C92E7DC15B7AC043BD5D7FE398F559AC (void);
extern void AndroidJNI_CallStaticLongMethod_mB5EA26617BD31207DFD57C50FD49ECAF659D6CCB (void);
extern void AndroidJNI_CallStaticLongMethodUnsafe_m61FBCF3A08EA05FB3B5E0C3FD54A6E6D64E816FD (void);
extern void AndroidJNI_CallStaticVoidMethod_m2DB4A797A541A547320D853111F8D1E5D27D9C5E (void);
extern void AndroidJNI_CallStaticVoidMethod_m12188877F62FA50C21D16ABEF706410E81651D50 (void);
extern void AndroidJNI_CallStaticVoidMethodUnsafe_mD825FBC0CC3F6D642C2123AC79933C21AE26CA08 (void);
extern void AndroidJNI_GetStaticStringField_mFE7F821C85A677C32C199BB9B23CEB66A523A977 (void);
extern void AndroidJNI_GetStaticObjectField_m52268140CD4BD65B9FAC976669DBBD65D763731C (void);
extern void AndroidJNI_GetStaticBooleanField_m6BC154F7001DA04748F5F96F61878A3D6205ECA4 (void);
extern void AndroidJNI_GetStaticByteField_m1817BBECBE096B84C719026A308F3F0961025070 (void);
extern void AndroidJNI_GetStaticSByteField_m0A5D05E28F47C16783818258361281644C5C6585 (void);
extern void AndroidJNI_GetStaticCharField_m3B1D9B99424A25FB6F665DA504125C0F20CEC0BF (void);
extern void AndroidJNI_GetStaticShortField_m66353DB84BAFDD75B35914D8AA5056AC6B3C0BDB (void);
extern void AndroidJNI_GetStaticIntField_m039F7CB6BD326410250D18A49836F55CD1DD87F9 (void);
extern void AndroidJNI_GetStaticLongField_mD403EAC792740D06B021D1E9D34D25CAFEE59194 (void);
extern void AndroidJNI_GetStaticFloatField_m68704C9BF92DF84E6982FCB03EAC0935F3934399 (void);
extern void AndroidJNI_GetStaticDoubleField_mF882F4F690FE87E2A81D8779BB62C905DC217700 (void);
extern void AndroidJNI_SetStaticStringField_m42D87A914D4AD4DAE0B66661BAE6B708F4ED3AF8 (void);
extern void AndroidJNI_SetStaticObjectField_mFA62D317DCE3F48E5D63FBA0AC3464977C0459A3 (void);
extern void AndroidJNI_SetStaticBooleanField_m18BC2337480DF6ED4F40F9B674D5DAA19225F3D2 (void);
extern void AndroidJNI_SetStaticByteField_m22249B7319EA4C4751995AFAE6CB41317EAF7190 (void);
extern void AndroidJNI_SetStaticSByteField_mCA2EA01B1AD261349CD5BE1E3F7AD43A8596837C (void);
extern void AndroidJNI_SetStaticCharField_m73B891760479DB05E0C3EC3D60F90D4503AA67A0 (void);
extern void AndroidJNI_SetStaticShortField_mBC6DC87D8235408532C23E5CDB19C178C5F2D77E (void);
extern void AndroidJNI_SetStaticIntField_mCBEADFA609B9541779AC51B1FE200B50D51C10F7 (void);
extern void AndroidJNI_SetStaticLongField_mEEDC663D7A85F4DAEE0CA7935EF5E27CD377E5FA (void);
extern void AndroidJNI_SetStaticFloatField_m836FCB6A7FB278526013F0C7BCAFCE33F2D7C016 (void);
extern void AndroidJNI_SetStaticDoubleField_mF7F4869A92C98895DC79F0EEBEB0DA0C576CBDF7 (void);
extern void AndroidJNI_ToBooleanArray_m06017AECA409DC3207C993113FA1F88277F1D71B (void);
extern void AndroidJNI_ToByteArray_mE72C1AF34FE140D36F10A0386454137D4550FBDD (void);
extern void AndroidJNI_ToSByteArray_m1307FD21FE087877599392D80474D56EA03AA0B8 (void);
extern void AndroidJNI_ToSByteArray_mFF630560D87EDF0DECAFDAD991B4F5D97D14F888 (void);
extern void AndroidJNI_ToCharArray_mF58CDE4DA760CD4518E8F5F4DAD7A885B7166C5B (void);
extern void AndroidJNI_ToCharArray_mC6D05FB1B5597173C755B9C722434B2E7EC3F88F (void);
extern void AndroidJNI_ToShortArray_m28E2EB565D92A8A0396646B0E3EBFF80A7E10246 (void);
extern void AndroidJNI_ToShortArray_m1074CE2547E0B7BBB2AEB765578E75D313496DC7 (void);
extern void AndroidJNI_ToIntArray_mA5B1AF83EE6484437CABB1485875E86A5EAA8208 (void);
extern void AndroidJNI_ToIntArray_m81093A2BD5CF7C3B8BFDC2CD3D476D7AC60C1F56 (void);
extern void AndroidJNI_ToLongArray_m53576F1D2526D6021B07FF19F4F3C220B13A4A92 (void);
extern void AndroidJNI_ToLongArray_mECBB804892E33578D9C6ECEF875A8FE3A00803C7 (void);
extern void AndroidJNI_ToFloatArray_m61F723D6040BFC3A3622EE08E0EF9BBCE2E8E88B (void);
extern void AndroidJNI_ToFloatArray_m7EB20DAF4AB85B6B3CBCA81FD28E160D03106E05 (void);
extern void AndroidJNI_ToDoubleArray_m368EFFE8C4387F994423DFC4DA5834A4D4B1EC0E (void);
extern void AndroidJNI_ToDoubleArray_m96775F800752F09E88CC22CDAE6722F833F8685C (void);
extern void AndroidJNI_ToObjectArray_mE2FC617A376102D3F45FE0BD382B59A0B592F182 (void);
extern void AndroidJNI_ToObjectArray_m4843C4E669DDFDA28853BB0D627A4A30DD0E9944 (void);
extern void AndroidJNI_ToObjectArray_m86E80FA6CB35FF8AF0B5611106934EE3C9FC59D3 (void);
extern void AndroidJNI_FromBooleanArray_m95D7BE45F113A7576DF27BF14BBDC35DD7748A67 (void);
extern void AndroidJNI_FromByteArray_m5C52B7F13653B39F42FFB7FEB1B665FAC07F0388 (void);
extern void AndroidJNI_FromSByteArray_mFED4929D339523808AE9C94F3C2AB3A317E9C5E1 (void);
extern void AndroidJNI_FromCharArray_m7149E127743A7D659017D1E1C3B174C3D615C638 (void);
extern void AndroidJNI_FromShortArray_mA89CCCFED02DDFDA91835418DAD8211A4B7BDDC6 (void);
extern void AndroidJNI_FromIntArray_m5B8A47C4B6FDD607B3A67B02D4D1297B4C11CA6A (void);
extern void AndroidJNI_FromLongArray_mB042FE2F3D5AC91673FE72145E98D04B8775BE36 (void);
extern void AndroidJNI_FromFloatArray_mBA9EB0CE3EC9662D669877E2D7DA004B794C4331 (void);
extern void AndroidJNI_FromDoubleArray_mF5D9E2F0D26862F10C98E8ECC8EB436EB9804692 (void);
extern void AndroidJNI_FromObjectArray_mF29F2969BD34276ECCA7ABA7ADDD34C04694E445 (void);
extern void AndroidJNI_GetArrayLength_m7C02A09EAFEC667B3E8EBA9A06177E22E61028CB (void);
extern void AndroidJNI_NewBooleanArray_m9C7018583B95EC216E181204717267902A426029 (void);
extern void AndroidJNI_NewByteArray_mADACAA676D3E057D1C6109D8353EB704D10E7806 (void);
extern void AndroidJNI_NewSByteArray_mFDAF396EF3C3CC7C315C20F1B7E14B2B51714F41 (void);
extern void AndroidJNI_NewCharArray_m801332FB86A2CEBF424B046128C4C0E8F7D5D80C (void);
extern void AndroidJNI_NewShortArray_m6CC9E93F24ED8BFC02A13D89DA95E6F17276BCA6 (void);
extern void AndroidJNI_NewIntArray_m46F3D6CBFA7BB4D79BDBB0971E68DE459A9F5D99 (void);
extern void AndroidJNI_NewLongArray_m707798711EAB93F83F0F2E2489C13C9AFA6886D7 (void);
extern void AndroidJNI_NewFloatArray_mDC04BC46000F25D8D640A2DDAB36F9C81BD496F7 (void);
extern void AndroidJNI_NewDoubleArray_mA3DFC7AC3EC990D498539B59094FB3CEE4229E57 (void);
extern void AndroidJNI_NewObjectArray_m4EAB5EA40119977AAD41793C78A3C19FF19A7043 (void);
extern void AndroidJNI_GetBooleanArrayElement_mCEC9DA5F142E7F4DCF70453B8B0D506720D8F4F6 (void);
extern void AndroidJNI_GetByteArrayElement_m4E66A92347AFB54172A7483F1F224A36C927C913 (void);
extern void AndroidJNI_GetSByteArrayElement_mD8BC0A3483C53C6DB56EEE74274E71F7457B4DC2 (void);
extern void AndroidJNI_GetCharArrayElement_m46AE455A6DB4CE743D19B26986A7340C9EBE4EC4 (void);
extern void AndroidJNI_GetShortArrayElement_mEE788A8EDA8C2D81C30B5783B26983ACD908F1BD (void);
extern void AndroidJNI_GetIntArrayElement_m8D21128A83A398C97034293C4232487F6DEE8B52 (void);
extern void AndroidJNI_GetLongArrayElement_m1043DAB33A9DCA3BF5EDBA12F5D3121DFCD21BBC (void);
extern void AndroidJNI_GetFloatArrayElement_m432455F5B77316342396460CF547335798BA7E64 (void);
extern void AndroidJNI_GetDoubleArrayElement_m2A6B6A5F27DC6CDC378797E525402C238CD65E02 (void);
extern void AndroidJNI_GetObjectArrayElement_mC4CAF9744617F69EFCD95B71D95492DA20A0FACE (void);
extern void AndroidJNI_SetBooleanArrayElement_mADEB936138A96C081CCE560B6F11C427C4729292 (void);
extern void AndroidJNI_SetBooleanArrayElement_m16CF5F014FABEB28253AACFC93D4FF113D13DEC3 (void);
extern void AndroidJNI_SetByteArrayElement_m484197612F5E5C163F9116A0F63B0355823C375F (void);
extern void AndroidJNI_SetSByteArrayElement_m470F13FC7EA450CB5B113641F99EF040E19E708C (void);
extern void AndroidJNI_SetCharArrayElement_m56BE8F363275BF93E558F4D4BF6042DA9CDF1A39 (void);
extern void AndroidJNI_SetShortArrayElement_m8D2E6451D917D5452770325BE62DC667DFA26DBF (void);
extern void AndroidJNI_SetIntArrayElement_m66DF089843878DC016F15596A173906A2804E555 (void);
extern void AndroidJNI_SetLongArrayElement_m54F052B44CF922C9675C31BF32B4B3726E67AC79 (void);
extern void AndroidJNI_SetFloatArrayElement_mF3230F001486735FB129DD4117DD01260C998343 (void);
extern void AndroidJNI_SetDoubleArrayElement_m82F4EBCB94088644F17F30C7AF48475E31BE5211 (void);
extern void AndroidJNI_SetObjectArrayElement_mAEA12A91B1C20BF46CBFB5DC3B1D5AF95AA463B2 (void);
extern void AndroidJNI_NewDirectByteBuffer_m17389ED6D98CC0364180BAB43F2747B48FFDB107 (void);
extern void AndroidJNI_NewDirectByteBuffer_mA3DE61CE618552F4E241D9C027DC7819370D13EB (void);
extern void AndroidJNI_NewDirectByteBuffer_m933D59F86028E7B4AF11006992B2CDDD7BC854F3 (void);
extern void AndroidJNI_GetDirectBufferAddress_m0E0B127BFEB7AAF065829DC6AE11163D5616EBE8 (void);
extern void AndroidJNI_GetDirectBufferCapacity_mCAC8D9C8E45481BE59FB17406E1E16D4F9628183 (void);
extern void AndroidJNI_GetDirectByteBuffer_m69EB5F58FB87F8F6848974E800F98EC4C11E7E50 (void);
extern void AndroidJNI_GetDirectSByteBuffer_m0B37CD4B8AA4309BCA9A17A0F65CC161B48E8017 (void);
extern void AndroidJNI_RegisterNatives_m41F24DA5DCB80C5E593100B4A0166B8505C9F931 (void);
extern void AndroidJNI_RegisterNativesAllocate_m2F86513297C977FF48D7D1EC927AB9E9F8A6BE6A (void);
extern void AndroidJNI_RegisterNativesSet_mAB1A4FD2EB5BE4D1773EA5287C6CD03C8C9DCAC1 (void);
extern void AndroidJNI_RegisterNativesAndFree_m2E2C1C1B3CEC86C3CCCC6D7B8E8A5467538D0518 (void);
extern void AndroidJNI_UnregisterNatives_mFA7685BF971CE1DEC19A6E14EF80EBE6E979E82B (void);
extern void AndroidJNISafe_CheckException_m465A2955F921417ED62C035390E550889E93F8DC (void);
extern void AndroidJNISafe_QueueDeleteGlobalRef_mC800F26B3A689FCEA01B7EB26F9CD7875BAF147B (void);
extern void AndroidJNISafe_DeleteWeakGlobalRef_mBC786B6240AB03EA493A71A43D4297871FFC679A (void);
extern void AndroidJNISafe_DeleteLocalRef_m20303564C88A1B90E3D8D7A7D893392E18967094 (void);
extern void AndroidJNISafe_NewString_m6A9EC18D8B122E7B901DB6BF469BFD38D1E8FE5A (void);
extern void AndroidJNISafe_GetStringChars_mE246814CD8FF4EDDEE6EBF107367C4A8EAF03849 (void);
extern void AndroidJNISafe_GetObjectClass_m6FD815CB0F9760199ACD03D16FC88FED055BC9F3 (void);
extern void AndroidJNISafe_GetStaticMethodID_mAD5134FF6DE446852F3F28B791C15ADBD5E9E5E8 (void);
extern void AndroidJNISafe_GetMethodID_mF095B57A77BE529D51F369D94B66D14C2BC88536 (void);
extern void AndroidJNISafe_GetFieldID_mAD9554C6DCE9389C441A9AB556001211B9B2663D (void);
extern void AndroidJNISafe_GetStaticFieldID_mCCCE792F7BE47B6370951D417CCB1E2713DBF482 (void);
extern void AndroidJNISafe_FromReflectedMethod_mED131988778BF0267C4CE711854D4BC26D0D960B (void);
extern void AndroidJNISafe_FindClass_m2E8072B600873B4D87B2197C1168967050208D1B (void);
extern void AndroidJNISafe_NewObject_m0DEC2DAD0835B99FC58E6B44F14994A7EE05565E (void);
extern void AndroidJNISafe_SetStaticObjectField_m7757F7E30F8122DAF89F138A8AE727CB896BC721 (void);
extern void AndroidJNISafe_SetStaticStringField_m445D977B2374056C6E4607FAEDB7E99A1353E2EE (void);
extern void AndroidJNISafe_SetStaticCharField_m2B8245275C36525798C869B7B1088B25BA663613 (void);
extern void AndroidJNISafe_SetStaticDoubleField_mA0253927D476917D2158A9CE29F1BF535485B956 (void);
extern void AndroidJNISafe_SetStaticFloatField_mB2EDDE632AB2088CD12F1FD12174FB86990BCBEE (void);
extern void AndroidJNISafe_SetStaticLongField_m299AAC2DE8B6747B0B5E109BABB2F3A4FC1F486E (void);
extern void AndroidJNISafe_SetStaticShortField_m92534AAA86D7E1055E12936C8A7BD6B865B7DB81 (void);
extern void AndroidJNISafe_SetStaticSByteField_m242120982A9227E1E8344FFE9F06FD74986D15E9 (void);
extern void AndroidJNISafe_SetStaticBooleanField_mBE4E40DA1B07A29D356AEEE6CB9519F2B3621AC9 (void);
extern void AndroidJNISafe_SetStaticIntField_m1E20F6C72260CAFBF73207DCEC1816B2816EEBE1 (void);
extern void AndroidJNISafe_GetStaticObjectField_mB6B9A9EB2619DFDF1DA56300BF9FEC19BF883867 (void);
extern void AndroidJNISafe_GetStaticStringField_mB3D1325B08A38C7DAF1FA3E6CB52F6D8E0A2CB47 (void);
extern void AndroidJNISafe_GetStaticCharField_mF70F6D197261364AF2A9E875D84DDDA35BD0ED96 (void);
extern void AndroidJNISafe_GetStaticDoubleField_mEB86F2CE1F3879AAA9DEDA4B496F882C0E1DCBC2 (void);
extern void AndroidJNISafe_GetStaticFloatField_mD1456B729026959309A839C2647279C0B6541356 (void);
extern void AndroidJNISafe_GetStaticLongField_mABC2B933CEB757E3FAF1FD6C60AA0C4D38E9C49D (void);
extern void AndroidJNISafe_GetStaticShortField_m83716D4D85B30F26803F866AC47D5C04AAB5D320 (void);
extern void AndroidJNISafe_GetStaticSByteField_m77596E5B1AE58DAFF39268AC954CAD53974A688D (void);
extern void AndroidJNISafe_GetStaticBooleanField_m172BEAA3F0AB6754EA5F1AD30C36DAA0D3D7C666 (void);
extern void AndroidJNISafe_GetStaticIntField_m0698D50C44E490A009E8388C7321630DED5973BD (void);
extern void AndroidJNISafe_CallStaticVoidMethod_m6550C24C8D4E39C18D1D9C97FD2DBEED5452DFC2 (void);
extern void AndroidJNISafe_CallStaticObjectMethod_m545474765D15AC9B0144192760B45BAA963B8F5E (void);
extern void AndroidJNISafe_CallStaticObjectMethod_m3171BFAEF780EEF400AD592B6F040E7BE87C2387 (void);
extern void AndroidJNISafe_CallStaticStringMethod_m8BD92117111558CC00540B45437B4A90222B89BE (void);
extern void AndroidJNISafe_CallStaticStringMethod_m4E150E34CC6DBF27A955F8DAEE5941D6E10879C0 (void);
extern void AndroidJNISafe_CallStaticCharMethod_mC4B40190CE095728E823AB8B724ECDC8F4B36155 (void);
extern void AndroidJNISafe_CallStaticDoubleMethod_m73F1D51601D6849EE480389B4E43AED68C42B2B5 (void);
extern void AndroidJNISafe_CallStaticFloatMethod_m3F5419A10B9DF599352938B2BAD8866F8F112364 (void);
extern void AndroidJNISafe_CallStaticLongMethod_mDDE01239BEFCF007ECE05E51A249B3EB5BB61234 (void);
extern void AndroidJNISafe_CallStaticShortMethod_m8330383670ECCD7E24CDD68C419745E486FA6426 (void);
extern void AndroidJNISafe_CallStaticSByteMethod_m3E1F75978A2D686BC32DBF5A2F1F70F0D746C2B7 (void);
extern void AndroidJNISafe_CallStaticBooleanMethod_m652685AC18F590965249C0F9B107C00C142595BB (void);
extern void AndroidJNISafe_CallStaticIntMethod_m915549FA8FD7FB93B57A9708AD759488EA64418C (void);
extern void AndroidJNISafe_SetObjectField_mFE500926F9C963FF106E8AA30A16F4C671BAA8CA (void);
extern void AndroidJNISafe_SetStringField_m649363D4E87763D6A9760359EAFB29802E90B409 (void);
extern void AndroidJNISafe_SetCharField_m69D09A6A2CEA55D84B240FE32D90300AAB1334F9 (void);
extern void AndroidJNISafe_SetDoubleField_mE93D0C5EC2019A1B657BD32970FE6EFC9B005A58 (void);
extern void AndroidJNISafe_SetFloatField_m589CA6B8DD2BFD4515C5AEAE3772782B293F02C3 (void);
extern void AndroidJNISafe_SetLongField_m13905547F5CDC7E01AB0D8C787BF98DC2870EC35 (void);
extern void AndroidJNISafe_SetShortField_mF95E569C142DEDD604CE8BA7617328B3EDDD2F0D (void);
extern void AndroidJNISafe_SetSByteField_mB021168746571E7CAA8C0EAD7AA7F02C18B5EE33 (void);
extern void AndroidJNISafe_SetBooleanField_m5279EA41B214699E79733DC6C93259CC9DCA1D9E (void);
extern void AndroidJNISafe_SetIntField_mD238DA37BA1B3D7693484237951A6EFEA9C62120 (void);
extern void AndroidJNISafe_GetObjectField_mCF3BB1C38718D6F55081126BC7F6C286B382B275 (void);
extern void AndroidJNISafe_GetStringField_mADFCA05D6DE790600B57E90B20F2E75AFC036B0F (void);
extern void AndroidJNISafe_GetCharField_m8301FA96B40E27C032590FE3F8E84A777A4739C3 (void);
extern void AndroidJNISafe_GetDoubleField_mBCBD5E80223EDECC06FA783F34149E3625219074 (void);
extern void AndroidJNISafe_GetFloatField_m1EAA1ED33002BBA28CA2B630521D6BF1B7D3A2E7 (void);
extern void AndroidJNISafe_GetLongField_m7DD751358D10BB276D8A95D413B9DFB1E8EE81D8 (void);
extern void AndroidJNISafe_GetShortField_m5D21E87061C1DAC89DF58671C53432D0361F0C6E (void);
extern void AndroidJNISafe_GetSByteField_mAD3B08AA8A97F77CAE17DD25B0F389AFAC2023B1 (void);
extern void AndroidJNISafe_GetBooleanField_m34F37B560A6AEC81B9061FB3B72698C84720435D (void);
extern void AndroidJNISafe_GetIntField_mBD983688B73063DE5C55D320F60F266443FAC97C (void);
extern void AndroidJNISafe_CallVoidMethod_mC5385EEE65AD90278C00FE8DD589A63EB2CF32FB (void);
extern void AndroidJNISafe_CallObjectMethod_mBA06053048352614B802E9429FFF50C4A1B56057 (void);
extern void AndroidJNISafe_CallObjectMethod_m12F882542956F2920187AADCD0295D4E32124BEF (void);
extern void AndroidJNISafe_CallStringMethod_m4E40DA54A224C0C10A8C600CAC1C2C838B69264C (void);
extern void AndroidJNISafe_CallCharMethod_mB777FAF5E9D1BFF480B7EDD5AA5352F30797E1DD (void);
extern void AndroidJNISafe_CallDoubleMethod_m01B318F7CA4F90C54D689CF0CD84DF312E68CB5E (void);
extern void AndroidJNISafe_CallFloatMethod_m7437E60E0985885D721F1592E4DACF8246F69BBE (void);
extern void AndroidJNISafe_CallLongMethod_mD04CC840004334A567747BD526F88A813CB833B6 (void);
extern void AndroidJNISafe_CallShortMethod_m7C82D811B75161D4567651B0D85E5F7A2ED83A97 (void);
extern void AndroidJNISafe_CallSByteMethod_m03F9BD1288769A14F5CE8477DACDD62F6D0B77E7 (void);
extern void AndroidJNISafe_CallBooleanMethod_m2F5824C9EA5D1586C7E555F9F8DE01D84757D972 (void);
extern void AndroidJNISafe_CallIntMethod_m60318205A7EAD0C5CC0643106A7044F1563DCC0E (void);
extern void AndroidJNISafe_FromCharArray_m54EDC9D2BE92F9973F4E00EE953EE242B231EA96 (void);
extern void AndroidJNISafe_FromDoubleArray_mDEA8F2C7854101272F3A2733F351B570AAD5D9D9 (void);
extern void AndroidJNISafe_FromFloatArray_mDE02985159EEFD2CB28611C797AC21DE8B6300B8 (void);
extern void AndroidJNISafe_FromLongArray_mC4D73C0DA27F212947AB85AA2030A35BECDF8288 (void);
extern void AndroidJNISafe_FromShortArray_m62C0CB2D0BAE96D4B8CE365630361150EBE884FC (void);
extern void AndroidJNISafe_FromByteArray_mB06EF0FDBF6C738231E8F9D4998C38551131C4C5 (void);
extern void AndroidJNISafe_FromSByteArray_m261D638D8B059AB777BEF0BEFDD0822717DFF2B1 (void);
extern void AndroidJNISafe_FromBooleanArray_m36ED740401185EC0A959CA0F96A324A69E668646 (void);
extern void AndroidJNISafe_FromIntArray_mC4C4DC70FFA39CD6E3E02FDAC7192324E6D4614E (void);
extern void AndroidJNISafe_ToObjectArray_m4C95D999242E900D9C70891E44100A5EB5020C5F (void);
extern void AndroidJNISafe_ToCharArray_mFBF42A984F1C5D618CD0366B3B344E2BF8856B12 (void);
extern void AndroidJNISafe_ToDoubleArray_m6C1716EFF6DCA1AE3E04D292EB38A31E4132C1C1 (void);
extern void AndroidJNISafe_ToFloatArray_m18207119C3AC0C5D71DA537B2CEB21D11301B732 (void);
extern void AndroidJNISafe_ToLongArray_m67486F6D1F467D2354EEB74DACFDA79A1F3F7E0C (void);
extern void AndroidJNISafe_ToShortArray_m170C4D2D7D1ED3A02B4C707FB666BF4F2A9D02ED (void);
extern void AndroidJNISafe_ToByteArray_mB36D6ABE2FF31844554A353E136B2153BFDF0F65 (void);
extern void AndroidJNISafe_ToSByteArray_m10BD1D36C8D2D7F764FD8C87742FD700DB48665C (void);
extern void AndroidJNISafe_ToBooleanArray_m94630C7B69D819D7BE683691B46879C6154B5F3A (void);
extern void AndroidJNISafe_ToIntArray_mE4647AC18D85206D98121752C3B8CD7D52A321EB (void);
extern void AndroidJNISafe_GetObjectArrayElement_m02B6993F13670DD2D1557D75EC31D8D860F10FD0 (void);
extern void AndroidJNISafe_GetArrayLength_m809419F04C9BB93FED5B4A89F0539231C9B90E6F (void);
extern void AndroidJavaRunnable__ctor_m000E4FEB2DE8031A1CD733610D76E2BF60490334 (void);
extern void AndroidJavaRunnable_Invoke_m98CFB1479B942F71BF29F53CFDAC1CB9DAFAEBE1 (void);
extern void AndroidJavaException__ctor_mD4B5992BB074504F8E86D79EA98752D3CB154541 (void);
extern void AndroidJavaException_get_StackTrace_m28AC922BCC16051CCBA4C7E5F69698264AA7CC27 (void);
extern void GlobalJavaObjectRef__ctor_mFE5679D1B51F51CBF11721773C0D767286AC22E8 (void);
extern void GlobalJavaObjectRef_Finalize_m2EE89F98A391773F885A4A312FD4BD134E0D46D8 (void);
extern void GlobalJavaObjectRef_op_Implicit_m444B263750F9B778C87C30EA918CDC0B62271F24 (void);
extern void GlobalJavaObjectRef_Dispose_m45E67345587866D5A50D250D1C17425110703520 (void);
extern void AndroidJavaRunnableProxy__ctor_mB173256AF7629962B226343C4F6F94FFFF7317C3 (void);
extern void AndroidJavaRunnableProxy_run_m014F4E0A8ED56A054096F2BAC90653716D2A0D46 (void);
extern void AndroidJavaRunnableProxy_Invoke_m72839D8A915DA6FB2E25959330E57B4AC884CB77 (void);
extern void AndroidJavaProxy__ctor_m2832886A0E1BBF6702653A7C6A4609F11FB712C7 (void);
extern void AndroidJavaProxy__ctor_mFA05DF6B31FC284C65D378C02A2A34F277DFE6E5 (void);
extern void AndroidJavaProxy_Finalize_m6E4C294F2117D7A07E82A315081C9239AFA217E8 (void);
extern void AndroidJavaProxy_Invoke_m9D765F3E7DC37C5CB14C4884F2873B48D2F96BFB (void);
extern void AndroidJavaProxy_Invoke_mCAE9C5E669AD50DE372494E12224FF1F31A43F1D (void);
extern void AndroidJavaProxy_Invoke_mD2B81C060739751DA8148FB2B8C3DBCBE1EE4FCE (void);
extern void AndroidJavaProxy_equals_mC390139E035408E858940EB523D45ED3C8377110 (void);
extern void AndroidJavaProxy_hashCode_m7991233D3D6D5F994E7BC59C3CB65DBBEDF8CA93 (void);
extern void AndroidJavaProxy_toString_mF77EEDD3BB413F1273D9970BFB0D7C388366B256 (void);
extern void AndroidJavaProxy_GetProxyObject_mBFD2FBEF9ED9D4AE23DECF5836E5C73A886E2109 (void);
extern void AndroidJavaProxy_GetRawProxy_m685E066A4D378B596CD88385B954AE90CBF328A9 (void);
extern void AndroidJavaProxy__cctor_m44B4074B6A6D8193D86E65DB4C165C2371615D63 (void);
extern void AndroidJavaObject__ctor_mB47CA3FC88F645DAB31FB0FAAA32E9159B1DB19E (void);
extern void AndroidJavaObject__ctor_m1F1F88504475490860A246714F36205FB7D53362 (void);
extern void AndroidJavaObject__ctor_m262439771D3A3EFBD18E5D06188D11989D562635 (void);
extern void AndroidJavaObject__ctor_m0F50ADD04B4BEA5ACB6B614BB206EBFA9353CF6B (void);
extern void AndroidJavaObject__ctor_mA61E481C9C0F990FF9BEBFE9E1299612BC174E0E (void);
extern void AndroidJavaObject__ctor_m5A65B5D325C2CEFAC4097A0D3813F8E158178DD7 (void);
extern void AndroidJavaObject__ctor_m0CEE7D570807333CE2C193A82AB3AB8D4F873A6B (void);
extern void AndroidJavaObject__ctor_mF4FA101CAFFEAA5DC312E9A536C907DC54BEB0B4 (void);
extern void AndroidJavaObject_Dispose_m2B1593C20B3CE1C8FF95982F638F50985F9DD9E6 (void);
extern void AndroidJavaObject_Call_mDEF7846E2AB1C5379069BB21049ED55A9D837B1C (void);
extern void AndroidJavaObject_Call_m5B4E5F1C11B70F16B4526DCAC2E008AEE791612F (void);
extern void AndroidJavaObject_CallStatic_mB677DE04369EDD8E6DECAF2F233116EE1F06555C (void);
extern void AndroidJavaObject_CallStatic_m49C3058426CE1F766EBE911D5E127E565806043D (void);
extern void AndroidJavaObject_GetRawObject_m536F043B5CE2C21369FF6173C9D2A9A62136BC48 (void);
extern void AndroidJavaObject_GetRawClass_mE4FB4DC4F856A52E10C6AAD0B65BEBF47B5071F5 (void);
extern void AndroidJavaObject_CloneReference_m6DF6E2BF8D91804B303C93C2026E4A39977E8428 (void);
extern void AndroidJavaObject_DebugPrint_m047934BF3D1E6676FDDBDA038E1AF387C5413533 (void);
extern void AndroidJavaObject_DebugPrint_m41CA713464E773016D31C1B6C1489AC34A542CE6 (void);
extern void AndroidJavaObject__AndroidJavaObject_m1284CB7198514B8C06A2BF794ACDC909DC26443F (void);
extern void AndroidJavaObject__AndroidJavaObject_m19164C456D01B4A1B0D4B186A0E38C12C956ABE5 (void);
extern void AndroidJavaObject__ctor_m67B4EEAB015B123D5A3EDCAD914B4795A3B67F04 (void);
extern void AndroidJavaObject_Finalize_m87374EE46B27BE3559CACED8A1B62475200AB5AA (void);
extern void AndroidJavaObject_Dispose_m87886676A84FA079C0FE45E6C31D790D764652BE (void);
extern void AndroidJavaObject__Call_m4C4D7D7287030773175BDF47681EA018DFA4DF1A (void);
extern void AndroidJavaObject__Call_m2126160FB635069207535BD0E700C3605FDB3308 (void);
extern void AndroidJavaObject__CallStatic_mD63902D30CD5626DAEAD1D6484AF7A9ACA85590E (void);
extern void AndroidJavaObject__CallStatic_mE917E474DB9801610FB7ABE5BE749DF84CEFD48A (void);
extern void AndroidJavaObject_AndroidJavaObjectDeleteLocalRef_m2ECEEAF6389ABB9D6B963B8A98568ECD9413DF3C (void);
extern void AndroidJavaObject_AndroidJavaClassDeleteLocalRef_m56C84D7516BCB51A84E8AFDB3FCA46BAF494548F (void);
extern void AndroidJavaObject__GetRawObject_mC5B8B60BEF515F5EE2A113D60991A433DA740C69 (void);
extern void AndroidJavaObject__GetRawClass_m470EAEBF8B0BD365FD13F1C6F55119836452FDFA (void);
extern void AndroidJavaClass__ctor_mB5466169E1151B8CC44C8FED234D79984B431389 (void);
extern void AndroidJavaClass__AndroidJavaClass_mF481A9584D78F32C64219FDA49CB84B6F0A017DD (void);
extern void AndroidJavaClass__ctor_mB206D3CB990755BD56E308F61CD43BB9EA4421D0 (void);
extern void AndroidReflection_IsPrimitive_mA41A9ECECE3D73679C79DC8B0FDD32B59570DF25 (void);
extern void AndroidReflection_IsAssignableFrom_mBAE0D5121AD208959B89E9BDAF62F0E0BAB917C6 (void);
extern void AndroidReflection_GetStaticMethodID_mF8378F28D71354360CA9F8AAF8EAF24A3B9594CF (void);
extern void AndroidReflection_GetMethodID_m4318CE3C372444B966909092BF046833A9733EE7 (void);
extern void AndroidReflection_GetConstructorMember_m6380904C6B5AF39F973E79B13B3C2B53DFD759B3 (void);
extern void AndroidReflection_GetMethodMember_m2871C6DC2BA1AE5FF3FA448AC36022CC8B19C9EA (void);
extern void AndroidReflection_GetFieldMember_m6E589FB4DEDCFEE84B2CFD9C416D0C21EDB09D2F (void);
extern void AndroidReflection_GetFieldClass_m88D4993FF662A0EF387AADD915A4FD8054F0DF27 (void);
extern void AndroidReflection_GetFieldSignature_mF36DDDDB3E57742AD66487E2D7CECBE81736B259 (void);
extern void AndroidReflection_NewProxyInstance_m052E9828C670A85F3B9DD9D4632A9A6B52FB5EDF (void);
extern void AndroidReflection_CreateInvocationError_m20254A88DCE4AEE011AAC3D42B94DC7B588AB20F (void);
extern void AndroidReflection__cctor_m59365F63F057115EA8FBCB0AFBAF94012DAAC622 (void);
extern void _AndroidJNIHelper_CreateJavaProxy_mAC059F7C2716D4E9099EA7268F5378260E6DDF36 (void);
extern void _AndroidJNIHelper_CreateJavaRunnable_m25981D4249F420FA78240CDC2F7E53B9EAD5F31A (void);
extern void _AndroidJNIHelper_InvokeJavaProxyMethod_mCA9BD35B5AE99B6DFABBFE9968DD1EBE931F70C2 (void);
extern void _AndroidJNIHelper_CreateJNIArgArray_mA3976C6F68E0BF29442CB20FBD69CBA72EAB9D45 (void);
extern void _AndroidJNIHelper_UnboxArray_m5A46896F07017C9B0908AE3D72DA1285071DEA4A (void);
extern void _AndroidJNIHelper_Unbox_m7D786B14E4A90E5411FC9AA7F985451DEBC95731 (void);
extern void _AndroidJNIHelper_Box_mA0B7B8444C6AC2BABBDC7B1A7FACDA3EA6F816B0 (void);
extern void _AndroidJNIHelper_DeleteJNIArgArray_m44D912ADAF5C0E49A914AD7B1DE776C13AC4F61A (void);
extern void _AndroidJNIHelper_ConvertToJNIArray_m6E451CD0C15DE2812F103C604CB1EF1D358D1213 (void);
extern void _AndroidJNIHelper_GetConstructorID_mD9CF17C7FA280BED12A8BB5D6F12E0D2C6778C3F (void);
extern void _AndroidJNIHelper_GetMethodID_m185ABE7B65325FA580775AF81FE17687994C98DD (void);
extern void _AndroidJNIHelper_GetConstructorID_m89A45100B1A05DEBF6586AB234A7DBE2044B2490 (void);
extern void _AndroidJNIHelper_GetMethodID_mECADE8D5FC2712DED913CDECBB9D7E8C46812184 (void);
extern void _AndroidJNIHelper_GetMethodIDFallback_m7155B2DE7C4F4E71C6AE29F856A1A9618ADAAD4F (void);
extern void _AndroidJNIHelper_GetFieldID_m698CE89D968CF7A131861E95FF72272A6A14FB95 (void);
extern void _AndroidJNIHelper_GetSignature_m414A7A6B98FB5565075E4C51F22C482F2BCEEF5F (void);
extern void _AndroidJNIHelper_GetSignature_m309D35CC998B59CFEDA2D6EB5D0E221B3F1374F7 (void);
extern void _AndroidJNIHelper__ctor_m0535562F59B589E117E57B8EA07ECE900848F509 (void);
extern void AndroidApp_get_Context_m72441A446635EF0888D75440465A6E3BF4FE1FBE (void);
extern void AndroidApp_get_Activity_m3325B65D03BA5A73841028AA1E82F1721BDBC379 (void);
extern void AndroidApp_AcquireContextAndActivity_mBCD23D63F072095B881F41AB2877CD78520D7FB5 (void);
extern void AndroidApp_get_UnityPlayerRaw_m17DB30494D24BF3AC1A6B42634EA8C70427C4EB5 (void);
extern void AndroidAssetPackInfo__ctor_m747C0EAD6448BA479BAE3C5EAB67F205E0307972 (void);
extern void AndroidAssetPackState__ctor_m5CB1F078A45558A0966BA32FCFE18CFC46CA941B (void);
extern void AndroidAssetPackUseMobileDataRequestResult__ctor_mB46211F6D3B3A421B1C1D9E05F3FC62858383E8F (void);
extern void AndroidAssetPacks_GetAssetPackManager_m91B486E7EEF9F019B820E11F10E1D877609778A9 (void);
extern void AssetPackManagerDownloadStatusCallback__ctor_m03D0B212EFAEACF611D60596E978DD0468C6D936 (void);
extern void AssetPackManagerDownloadStatusCallback_onStatusUpdate_m4042BE15C2B4A64CE1BAF4734E6A0BF8DD4FFC7A (void);
extern void AssetPackManagerMobileDataConfirmationCallback__ctor_m927FCB784F7D90524C6725B9E64EF8799538999B (void);
extern void AssetPackManagerMobileDataConfirmationCallback_onMobileDataConfirmationResult_m61FEFDED8787D798CF30CB78DC133505B7D3614A (void);
extern void AssetPackManagerStatusQueryCallback__ctor_m66F3B4A8BD8911F07121E2F8EF871741CE3A2CDB (void);
extern void AssetPackManagerStatusQueryCallback_onStatusResult_m8C41C5CC2F37808E5C5C12B023DF31E1DF96C314 (void);
extern void PermissionCallbacks_add_PermissionGranted_m74335D4200D9B1A7C80AB9C133F95C61FCDCDF89 (void);
extern void PermissionCallbacks_remove_PermissionGranted_m4A3F9873FC159F89A2AD35F2FCAFF66A19813AF4 (void);
extern void PermissionCallbacks_add_PermissionDenied_mE0B2826463785B050C999C70F443FCC3822563D0 (void);
extern void PermissionCallbacks_remove_PermissionDenied_mF1A606ADE21F9520909126D3642B0BC2D6E994A1 (void);
extern void PermissionCallbacks_add_PermissionDeniedAndDontAskAgain_mEDE8C00FEF2F649F10A47F30AC4ECB09E52DB9AA (void);
extern void PermissionCallbacks_remove_PermissionDeniedAndDontAskAgain_m55B04AE58C687946BDFA2094ED851518B2A1D68D (void);
extern void PermissionCallbacks__ctor_m91B14BBBC8913C131E400BA0D13576822AAE7A75 (void);
extern void PermissionCallbacks_onPermissionGranted_m723440705B5B21B97AF5206716275BAE2A122E3C (void);
extern void PermissionCallbacks_onPermissionDenied_m0E05122B560DD62BB38178EE601E65854017980A (void);
extern void PermissionCallbacks_onPermissionDeniedAndDontAskAgain_m1191CF6422AFD8E8FE7BDBDBC04721D63718A5D9 (void);
extern void Permission_GetUnityPermissions_m6A22A923A8B036209E6218E24F7AA8CA79AA15BD (void);
extern void Permission_HasUserAuthorizedPermission_mF4C90E13124E28F6F672200E489CC25A9B645B8B (void);
extern void Permission_RequestUserPermission_mF9CF3A21AAF34B311137C4D00B3AD6A6C2694242 (void);
extern void Permission_RequestUserPermissions_m09FE74BF5829C8B48A0E6C9B7F7846FEE763D2DA (void);
static Il2CppMethodPointer s_methodPointers[529] = 
{
	AndroidJNIHelper_get_debug_m0F67D4E412EBE1D511C42F5F12294C63E669C907,
	AndroidJNIHelper_set_debug_m66E40D20DC9DC69BD7B139DA5C367BA1185FCA3F,
	AndroidJNIHelper_GetConstructorID_mCF5EAC779FFBD1129C2E28FE1C2171E6FF8AAE95,
	AndroidJNIHelper_GetConstructorID_m2D883140A087C1CDB74FE9195D14643CB9A854F0,
	AndroidJNIHelper_GetMethodID_m5D0526B2FE20191F966D72521647D686980EE06B,
	AndroidJNIHelper_GetMethodID_m58B68CA9B567BF23064BD8891CEA509FF8FA7C76,
	AndroidJNIHelper_GetMethodID_mDB705DC228B1BB30E6595068797FB3F2A1817BB8,
	AndroidJNIHelper_GetFieldID_mB209E2F32D04CA5BCD7D6EC10A0C8DC0ED5D0D3B,
	AndroidJNIHelper_GetFieldID_m736A1E39AEE90F1DD3C352FA3F1988D4CFD9778F,
	AndroidJNIHelper_GetFieldID_mDA4775DFA37126A7EFEBCA8E68EF9640F3D6EF01,
	AndroidJNIHelper_CreateJavaRunnable_mBEE8C5060C69EE5C6922D4BA06C261055A3FF99F,
	AndroidJNIHelper_CreateJavaProxy_m75CA3C0BF15517CD52658E08F9FCBA1022822E6F,
	AndroidJNIHelper_ConvertToJNIArray_mBEAE4605FF297D19AFB8CE4E8443C9C0F87E9A13,
	AndroidJNIHelper_CreateJNIArgArray_mC12C279EEB43D740F42C5E90301DC54F6E04B876,
	AndroidJNIHelper_CreateJNIArgArray_mD8E0CA2404E31F155EDE1A028EC686C17B17730F,
	AndroidJNIHelper_DeleteJNIArgArray_mBDC874B32FF09E6B48E18B2A58794C0A4DE2FA23,
	AndroidJNIHelper_DeleteJNIArgArray_m4FC468F2AD104C5B159A0EF496BF215C7260DCC9,
	AndroidJNIHelper_GetConstructorID_m0FDAC24E463246206BA8FBDE44B4A73D6CBF3D40,
	AndroidJNIHelper_GetMethodID_m283E294AA8DD3F6721A4173CE4C31038B98AA7E5,
	AndroidJNIHelper_GetSignature_mE8DBE8ABB6506BBF2806880DA2FBB876DD88A047,
	AndroidJNIHelper_GetSignature_m1B2D7B48C9E9D7BB1F10AD60D541EA7F9BE18F45,
	NULL,
	NULL,
	NULL,
	NULL,
	AndroidJNIHelper_Box_mBC04AC9A326AC66A599C56CB1C8EAE11940A72A3,
	AndroidJNIHelper_Box_mBFE16B308EDC2E454BB9ED219101EB9248D636B4,
	AndroidJNIHelper_Box_mE0E4CB7D7168C7CA04F8B70C3A9C34F2B4CFAF76,
	AndroidJNIHelper_Box_m2C8ACD78C001428B06BA5B1E5D7341B68A484EE1,
	AndroidJNIHelper_Box_m222D02EBC4F32DCBF9DDE8879EF877762E310B6B,
	AndroidJNIHelper_Box_m2CDD9646CBB781A968A96F3F38D5D04CD5ACA1AE,
	AndroidJNIHelper_Box_mA3CD0A28FFA5A1971F9AB097842A45450D0D2300,
	AndroidJNIHelper_Box_mB7858568094A96BF66DBF382C890B1343EF6FDF6,
	AndroidJNIHelper_Box_m354F5BA4592A9B712951AE557F0B0011B7113C0E,
	AndroidJNIHelper_GetUnboxMethod_mB1586BDBF8F34EF7EFFDC1635C957330ADB79D85,
	AndroidJNIHelper_Unbox_m8A1E1404D6CB2F0A3829E46A72029A76FF912F1A,
	AndroidJNIHelper_Unbox_mF1E4ABF9304F60C34D7F12F1132AA3BB76A79FBF,
	AndroidJNIHelper_Unbox_m8C65F084C98FD54EF50C8FB03300DD9004DAD45C,
	AndroidJNIHelper_Unbox_m56F15898A0377A7891B60F3F9094F59B2AD138C6,
	AndroidJNIHelper_Unbox_m81CBC478E8A1124A8887B53DD584F1BEC56054E8,
	AndroidJNIHelper_Unbox_mE24347592484B48D93F88221869D4178AAC25C87,
	AndroidJNIHelper_Unbox_m48F8AD7593EA47BD6FFE270C88FE6CBA42026504,
	AndroidJNIHelper_Unbox_mF84A7086D5E7E04EE635B6C4BF244D2338492BB0,
	AndroidJNI_GetJavaVM_m5A3861F5CBDB0352855BE858022BA9BCB2BB6F62,
	AndroidJNI_AttachCurrentThread_m412647220ED1F7D42F9D41F692D1D7DC4696B6D4,
	AndroidJNI_DetachCurrentThread_mC0F3D8974C0681528B801EF0AC2A54E5C7A2E933,
	AndroidJNI_GetVersion_m8609243FCDE009D1C5DEC7C7E40664386D5C4608,
	AndroidJNI_FindClass_m6E9908108F8D828563AD510C5A355E71A9A493E9,
	AndroidJNI_FromReflectedMethod_m1613634C8A528B0A2A1F6828B609F31792B7DF43,
	AndroidJNI_FromReflectedField_m88709F5C3EDD6CB503B1C6EE32CFF6BC328171E8,
	AndroidJNI_ToReflectedMethod_m50B8B87366FDAB0506EF2023352AB602E14CA7E2,
	AndroidJNI_ToReflectedField_mEE3F5294258CD32FB6066A2EB256B75B04B19D3B,
	AndroidJNI_GetSuperclass_m755BF9AA2AADBE96A2C64F15AE8114BD1807AD6A,
	AndroidJNI_IsAssignableFrom_m28DB6B523875A7837F7CC60807F32D58E90F9C25,
	AndroidJNI_Throw_m7DE4EC0AF3864EDC05EE828A57B20A1EB0C71F5E,
	AndroidJNI_ThrowNew_mDEAF3A3716A9F8D6A9DF48B6928D17740C0112AE,
	AndroidJNI_ExceptionOccurred_mAE2AE7C57E06059383EDCAB956A01BDF7D03F54C,
	AndroidJNI_ExceptionDescribe_m20B24A293E6A8467E312E028E6AA250934684508,
	AndroidJNI_ExceptionClear_m1205CD178ADB8E0E7EBDBE349AFA767D5D7758B8,
	AndroidJNI_FatalError_m6F93C97D6802B5FFB6A6A99D16C74234E79AF5D6,
	AndroidJNI_PushLocalFrame_m2D8050A3799AEBB4A7E506E6790839EB66932E10,
	AndroidJNI_PopLocalFrame_m32AF6F9065F09D80BFDD3F573B62C782F392E609,
	AndroidJNI_NewGlobalRef_m9A06F23234FB1ECF3F482AF3A6A6148A5916E9A7,
	AndroidJNI_DeleteGlobalRef_mC50B6C056F32BB9F44B800949FA169C728D4C41D,
	AndroidJNI_QueueDeleteGlobalRef_m1C78D0CD2C81BE9D6D6E57CD6C83CFF70F38CBF1,
	AndroidJNI_GetQueueGlobalRefsCount_m0A44BD8FAACE454492208D0614AA18803155FB34,
	AndroidJNI_NewWeakGlobalRef_m27D74DAC1D1F0A11796E4FA669D8CB8DBB1AF2BA,
	AndroidJNI_DeleteWeakGlobalRef_mA1F19C1656B86A22A339497C335C156648736E6D,
	AndroidJNI_NewLocalRef_m286E59F912B94D07D1CE54DFE93A631B2162CD65,
	AndroidJNI_DeleteLocalRef_mD2A2B4F1C17A4F5863BB94F88F268E72FD120DBB,
	AndroidJNI_IsSameObject_mA37D2BE7C0E40F30E6D438A937B038E7703DFDAB,
	AndroidJNI_EnsureLocalCapacity_mD27645E03F7F82811D5AFFF6B068E226C9C93227,
	AndroidJNI_AllocObject_m5E81D0A72F3DCDFC5D0AD62C2B93816083EE3926,
	AndroidJNI_NewObject_mF026198FBA1D5E69719DEB52F41E9FDB8B7F93A4,
	AndroidJNI_NewObject_m495F8FDA574E6645C469B34C297768FCF4038C45,
	AndroidJNI_NewObjectA_m95F13B24F964C82A9EEDA81EB5120BA818A042AB,
	AndroidJNI_GetObjectClass_m418C2D7DAE432AD104209833A0DF06B16E1E74B5,
	AndroidJNI_IsInstanceOf_m09B386C15D3FBD4A6589D9F6DD3E5F8D259F51B1,
	AndroidJNI_GetMethodID_mA7FF961764CA4D68C4789E5A17926CE5FF9B3549,
	AndroidJNI_GetFieldID_mC6BFB1F17EF5FC82D45F08D25C11D346E51673F2,
	AndroidJNI_GetStaticMethodID_mA13B58796C4E210B46956723FE664B6D0130C5A3,
	AndroidJNI_GetStaticFieldID_m89476A442BF57C00C1CBB0DA588077C2B4171654,
	AndroidJNI_NewString_m6F3143989EFE907B5D0091850D1754421795A39B,
	AndroidJNI_NewStringFromStr_mF56D4A6456A326916DEF2E148E826F6EBC93B3CC,
	AndroidJNI_NewString_m74F9DCF2F56735C9BE6DC3831134A96C7BCA9F9F,
	AndroidJNI_NewStringUTF_mB6A0A0A1C3F931503A6329D4E232DB501B95B5B0,
	AndroidJNI_GetStringChars_mB61E4F713A2457F7DF053DBFE4455A559DA9623C,
	AndroidJNI_GetStringLength_mD2A44213EB9B131E1DECEF34A315F2B817384760,
	AndroidJNI_GetStringUTFLength_m5BE0CC7EE4108BA654A9F7647E66871D831B812A,
	AndroidJNI_GetStringUTFChars_m61CFD69CF31D17C2728F23656079D3E26D9D2BBB,
	AndroidJNI_CallStringMethod_m52FAF2826B75AF2AEA8F848AEC973A682216EC4C,
	AndroidJNI_CallStringMethod_m1255E35A517F4BC178F1FC3CA06A6ECDDCF08490,
	AndroidJNI_CallStringMethodUnsafe_m9C19A5FDE56CEE8DF472517EB9F6BA8ECF004614,
	AndroidJNI_CallObjectMethod_m21944B19534CA3EB8885BE3F1057E6A72AAC4355,
	AndroidJNI_CallObjectMethod_mF3C4822DD3311879591F5BC730DC01ED6F24571F,
	AndroidJNI_CallObjectMethodUnsafe_m2C9A9AB2D47751AA9F2D9048E0B23ED8057C8B56,
	AndroidJNI_CallIntMethod_mD1299CB9F99E26D2734A0F02D376DE6EF49F1741,
	AndroidJNI_CallIntMethod_m3895BC6DD12AFFF33323C1878E32864E166D8283,
	AndroidJNI_CallIntMethodUnsafe_m41FAB53999456E0A70C8E310BCA63C6310C0B2F6,
	AndroidJNI_CallBooleanMethod_m78FC31B29854285F1164C75974AB463FE5716F84,
	AndroidJNI_CallBooleanMethod_m61006AE6C02C028A0A43761330FDB9C9AE655184,
	AndroidJNI_CallBooleanMethodUnsafe_m3DF3E7781C06330CA520F66BEA96CFF615794A4A,
	AndroidJNI_CallShortMethod_mC3C11BD9E32604C41710756104D5A8AFCFA792E4,
	AndroidJNI_CallShortMethod_mED891B3ED4526E3CEB3C7B133460E61DCF015432,
	AndroidJNI_CallShortMethodUnsafe_m211540B39F818D275F1F51268C9B676B809BC8DF,
	AndroidJNI_CallByteMethod_m7E2B355303C36B0992364D036BFCCF1CB4DD8154,
	AndroidJNI_CallSByteMethod_mA98E61BB8186A06CBF4A175E29E2F0F194FB8507,
	AndroidJNI_CallSByteMethod_mFF5A7095DF874C9536DE192057C0624F7A500FF6,
	AndroidJNI_CallSByteMethodUnsafe_m40F4B14CAAF4B55614D741C32DACCE3D2CAE68A7,
	AndroidJNI_CallCharMethod_m560F0E113CA6E4F4A73BDAF93D53DADDA8D2047B,
	AndroidJNI_CallCharMethod_m6319B0007021421F5EA1D3660F1322AFE56E9547,
	AndroidJNI_CallCharMethodUnsafe_m258168724E6F6CC0313A584A885A061E0B543698,
	AndroidJNI_CallFloatMethod_mF94056CFCC7E045F7B350D2D3285335482A2AE8E,
	AndroidJNI_CallFloatMethod_mBED49D3756A1A8FB3038A454AA421734DF87E609,
	AndroidJNI_CallFloatMethodUnsafe_mA19B4DA3E878B13479A41063AAA0053D97D25AE5,
	AndroidJNI_CallDoubleMethod_m9001B2EF56623D6F17B4E9E87788CDD3E760A897,
	AndroidJNI_CallDoubleMethod_m4A66035AEA817B8F0C1C2C0FA009447116E9A686,
	AndroidJNI_CallDoubleMethodUnsafe_m211C3DA7C876DD9C5B925230C220F6E51A6D245F,
	AndroidJNI_CallLongMethod_mDE82FA5F26CBE0E0F8251D3A7698D376524E1A4B,
	AndroidJNI_CallLongMethod_m63392BAB11BF3A055E39B43A0FB3BFF959AD5253,
	AndroidJNI_CallLongMethodUnsafe_m0AE09B468D8A3EB89920E9570731E95725A5F3C8,
	AndroidJNI_CallVoidMethod_mFCFF6A5FF4A51305C3D2DBFB3A0699617EF40D48,
	AndroidJNI_CallVoidMethod_m5A3A884F6B2257FF6D7FDD769AE54B508EA5CD88,
	AndroidJNI_CallVoidMethodUnsafe_mCF5A52E0A9D82069CAAD4365D82E39B467B80DCD,
	AndroidJNI_GetStringField_m6A2FA57794ADA8735B103FF5D4819F3C4813992F,
	AndroidJNI_GetObjectField_m5E3C5FF1582F4A62155220FDCB849574C0E36AD6,
	AndroidJNI_GetBooleanField_mC900428E2FD1E55BA21808A31B760FB0F10BC047,
	AndroidJNI_GetByteField_m675BEFB1024363DE587C000406B8A09C5762B847,
	AndroidJNI_GetSByteField_mB81412A4748ABB311535E2B73092569AAE6CB735,
	AndroidJNI_GetCharField_m7DA94A98ED33A97EE7874872C9F51515F151F019,
	AndroidJNI_GetShortField_m5EEACBB3920623AD54D9DF77499E8BA92B35E3D8,
	AndroidJNI_GetIntField_m6B78A3F6F8EE6D1ADEDECF1EC145BC9C5AE37E88,
	AndroidJNI_GetLongField_mC2DC315C44320CE9A406B95A4CAA1117A0FF56A8,
	AndroidJNI_GetFloatField_m5C92103D7307A19F72F28DD40118F84D91C19A39,
	AndroidJNI_GetDoubleField_mE2B23D9F1363B48811B6207BEF8A18B39CB3B22B,
	AndroidJNI_SetStringField_mE9B2983BC7C61C0EEEA3FA31484B570B48E210DC,
	AndroidJNI_SetObjectField_m6BA777B66D76ECD1E34D69D800A8F2F51C51026C,
	AndroidJNI_SetBooleanField_m9A22242BD25A8B3802C05F70C2EB5ACE7E8BF2A0,
	AndroidJNI_SetByteField_mA1EBC3D4A3734B8064330782DC240DD1775C7C4B,
	AndroidJNI_SetSByteField_mA360303CC36670BDC442E1366D64333A5363D09F,
	AndroidJNI_SetCharField_m86543FBDB219D090EFEA141F679CCD22E195B680,
	AndroidJNI_SetShortField_m8811FC677647B47F855FC9533D51437517B53066,
	AndroidJNI_SetIntField_mCAB8E0B5C4F1773F6CAF81731DFB224FB78F0138,
	AndroidJNI_SetLongField_m763D39D8B341907F54921AF5EFE4E0C37EB44B00,
	AndroidJNI_SetFloatField_m953B7DC631E56432E2AB59154CAC15EE10B28E02,
	AndroidJNI_SetDoubleField_m30FB81E1DEE48DB3C22117F3D017813A204B474D,
	AndroidJNI_CallStaticStringMethod_m7E8E3AFF8296764C324060E65B052B23500C18AB,
	AndroidJNI_CallStaticStringMethod_m42628886D81E2DB3F9802AEC7A872B5EB5044FE7,
	AndroidJNI_CallStaticStringMethodUnsafe_m809DC1DE90B6FC3564CCDAB919740DF1916EC552,
	AndroidJNI_CallStaticObjectMethod_m5D0C02761602E6ED1AE4FAD90B7762A6376A35D5,
	AndroidJNI_CallStaticObjectMethod_m563873CBBD357A5301C122CAF6563B8AA302E43C,
	AndroidJNI_CallStaticObjectMethodUnsafe_m3892FD7D99D186C1AE028F8E49C660737E1DF89D,
	AndroidJNI_CallStaticIntMethod_m7AA48D4603F398E99E45DF3E057BB58FB6D374FC,
	AndroidJNI_CallStaticIntMethod_mD9A45F8ECDE53096DDAC297DEDA6D6309C80BBC3,
	AndroidJNI_CallStaticIntMethodUnsafe_mD53EAF407A8B5062744982BFD22F22B340065BEA,
	AndroidJNI_CallStaticBooleanMethod_m6D035B0525AF900D6BF3F91C174C093FE2531453,
	AndroidJNI_CallStaticBooleanMethod_mD168ECFAF2855BE5A4E61B5EF3EFADAAA7297734,
	AndroidJNI_CallStaticBooleanMethodUnsafe_m3F99D47A3B64BC4477DCF3228859601094BE1FAF,
	AndroidJNI_CallStaticShortMethod_m10E61636B448E36034CD6DE902A286B0257C7586,
	AndroidJNI_CallStaticShortMethod_m78EEA97BFBC305BCB3B7578A81EF245F71ADA1A9,
	AndroidJNI_CallStaticShortMethodUnsafe_m8379FEC6D9133BC9CEA171BE5A82351576CA5C9C,
	AndroidJNI_CallStaticByteMethod_mFC6A3C4731086DD6CD298C5EDC86C0211B90C69C,
	AndroidJNI_CallStaticSByteMethod_mA2BA2E72F9CF73A04E15420CDCCFD7CFD809E0DA,
	AndroidJNI_CallStaticSByteMethod_m0EF2AF8C9395B147DCE092F9A1778BF0FB23ACDB,
	AndroidJNI_CallStaticSByteMethodUnsafe_m1921CB76340B4B912D290B317CE1D1445A803F65,
	AndroidJNI_CallStaticCharMethod_mCC7C287BA2CD34A0526577D194237277F9708353,
	AndroidJNI_CallStaticCharMethod_m8A6B5AAB05E1AC468D1E927A02E9ECEA46D933DB,
	AndroidJNI_CallStaticCharMethodUnsafe_mB7CBD75837AAED45237D624B79F081DC1E60967C,
	AndroidJNI_CallStaticFloatMethod_m19DBC375D9E707CA40BE8D997C84F553EF71040A,
	AndroidJNI_CallStaticFloatMethod_mEC10B7CC01D5C2933E9A993D8C9683EE6C5EB5CD,
	AndroidJNI_CallStaticFloatMethodUnsafe_m68A9454AF94B2DD4F31C2AA29A412D4599010435,
	AndroidJNI_CallStaticDoubleMethod_m052484ED56097C439FA22A89CA0FE393BBFFD305,
	AndroidJNI_CallStaticDoubleMethod_m3409D4BF5A4801BCAB79E4EE0498CD358B283F4A,
	AndroidJNI_CallStaticDoubleMethodUnsafe_m2B11C8E55CB6CFB33749CF1F8750F5D7669BFEFB,
	AndroidJNI_CallStaticLongMethod_mC103D7C5C92E7DC15B7AC043BD5D7FE398F559AC,
	AndroidJNI_CallStaticLongMethod_mB5EA26617BD31207DFD57C50FD49ECAF659D6CCB,
	AndroidJNI_CallStaticLongMethodUnsafe_m61FBCF3A08EA05FB3B5E0C3FD54A6E6D64E816FD,
	AndroidJNI_CallStaticVoidMethod_m2DB4A797A541A547320D853111F8D1E5D27D9C5E,
	AndroidJNI_CallStaticVoidMethod_m12188877F62FA50C21D16ABEF706410E81651D50,
	AndroidJNI_CallStaticVoidMethodUnsafe_mD825FBC0CC3F6D642C2123AC79933C21AE26CA08,
	AndroidJNI_GetStaticStringField_mFE7F821C85A677C32C199BB9B23CEB66A523A977,
	AndroidJNI_GetStaticObjectField_m52268140CD4BD65B9FAC976669DBBD65D763731C,
	AndroidJNI_GetStaticBooleanField_m6BC154F7001DA04748F5F96F61878A3D6205ECA4,
	AndroidJNI_GetStaticByteField_m1817BBECBE096B84C719026A308F3F0961025070,
	AndroidJNI_GetStaticSByteField_m0A5D05E28F47C16783818258361281644C5C6585,
	AndroidJNI_GetStaticCharField_m3B1D9B99424A25FB6F665DA504125C0F20CEC0BF,
	AndroidJNI_GetStaticShortField_m66353DB84BAFDD75B35914D8AA5056AC6B3C0BDB,
	AndroidJNI_GetStaticIntField_m039F7CB6BD326410250D18A49836F55CD1DD87F9,
	AndroidJNI_GetStaticLongField_mD403EAC792740D06B021D1E9D34D25CAFEE59194,
	AndroidJNI_GetStaticFloatField_m68704C9BF92DF84E6982FCB03EAC0935F3934399,
	AndroidJNI_GetStaticDoubleField_mF882F4F690FE87E2A81D8779BB62C905DC217700,
	AndroidJNI_SetStaticStringField_m42D87A914D4AD4DAE0B66661BAE6B708F4ED3AF8,
	AndroidJNI_SetStaticObjectField_mFA62D317DCE3F48E5D63FBA0AC3464977C0459A3,
	AndroidJNI_SetStaticBooleanField_m18BC2337480DF6ED4F40F9B674D5DAA19225F3D2,
	AndroidJNI_SetStaticByteField_m22249B7319EA4C4751995AFAE6CB41317EAF7190,
	AndroidJNI_SetStaticSByteField_mCA2EA01B1AD261349CD5BE1E3F7AD43A8596837C,
	AndroidJNI_SetStaticCharField_m73B891760479DB05E0C3EC3D60F90D4503AA67A0,
	AndroidJNI_SetStaticShortField_mBC6DC87D8235408532C23E5CDB19C178C5F2D77E,
	AndroidJNI_SetStaticIntField_mCBEADFA609B9541779AC51B1FE200B50D51C10F7,
	AndroidJNI_SetStaticLongField_mEEDC663D7A85F4DAEE0CA7935EF5E27CD377E5FA,
	AndroidJNI_SetStaticFloatField_m836FCB6A7FB278526013F0C7BCAFCE33F2D7C016,
	AndroidJNI_SetStaticDoubleField_mF7F4869A92C98895DC79F0EEBEB0DA0C576CBDF7,
	AndroidJNI_ToBooleanArray_m06017AECA409DC3207C993113FA1F88277F1D71B,
	AndroidJNI_ToByteArray_mE72C1AF34FE140D36F10A0386454137D4550FBDD,
	AndroidJNI_ToSByteArray_m1307FD21FE087877599392D80474D56EA03AA0B8,
	AndroidJNI_ToSByteArray_mFF630560D87EDF0DECAFDAD991B4F5D97D14F888,
	AndroidJNI_ToCharArray_mF58CDE4DA760CD4518E8F5F4DAD7A885B7166C5B,
	AndroidJNI_ToCharArray_mC6D05FB1B5597173C755B9C722434B2E7EC3F88F,
	AndroidJNI_ToShortArray_m28E2EB565D92A8A0396646B0E3EBFF80A7E10246,
	AndroidJNI_ToShortArray_m1074CE2547E0B7BBB2AEB765578E75D313496DC7,
	AndroidJNI_ToIntArray_mA5B1AF83EE6484437CABB1485875E86A5EAA8208,
	AndroidJNI_ToIntArray_m81093A2BD5CF7C3B8BFDC2CD3D476D7AC60C1F56,
	AndroidJNI_ToLongArray_m53576F1D2526D6021B07FF19F4F3C220B13A4A92,
	AndroidJNI_ToLongArray_mECBB804892E33578D9C6ECEF875A8FE3A00803C7,
	AndroidJNI_ToFloatArray_m61F723D6040BFC3A3622EE08E0EF9BBCE2E8E88B,
	AndroidJNI_ToFloatArray_m7EB20DAF4AB85B6B3CBCA81FD28E160D03106E05,
	AndroidJNI_ToDoubleArray_m368EFFE8C4387F994423DFC4DA5834A4D4B1EC0E,
	AndroidJNI_ToDoubleArray_m96775F800752F09E88CC22CDAE6722F833F8685C,
	AndroidJNI_ToObjectArray_mE2FC617A376102D3F45FE0BD382B59A0B592F182,
	AndroidJNI_ToObjectArray_m4843C4E669DDFDA28853BB0D627A4A30DD0E9944,
	AndroidJNI_ToObjectArray_m86E80FA6CB35FF8AF0B5611106934EE3C9FC59D3,
	AndroidJNI_FromBooleanArray_m95D7BE45F113A7576DF27BF14BBDC35DD7748A67,
	AndroidJNI_FromByteArray_m5C52B7F13653B39F42FFB7FEB1B665FAC07F0388,
	AndroidJNI_FromSByteArray_mFED4929D339523808AE9C94F3C2AB3A317E9C5E1,
	AndroidJNI_FromCharArray_m7149E127743A7D659017D1E1C3B174C3D615C638,
	AndroidJNI_FromShortArray_mA89CCCFED02DDFDA91835418DAD8211A4B7BDDC6,
	AndroidJNI_FromIntArray_m5B8A47C4B6FDD607B3A67B02D4D1297B4C11CA6A,
	AndroidJNI_FromLongArray_mB042FE2F3D5AC91673FE72145E98D04B8775BE36,
	AndroidJNI_FromFloatArray_mBA9EB0CE3EC9662D669877E2D7DA004B794C4331,
	AndroidJNI_FromDoubleArray_mF5D9E2F0D26862F10C98E8ECC8EB436EB9804692,
	AndroidJNI_FromObjectArray_mF29F2969BD34276ECCA7ABA7ADDD34C04694E445,
	AndroidJNI_GetArrayLength_m7C02A09EAFEC667B3E8EBA9A06177E22E61028CB,
	AndroidJNI_NewBooleanArray_m9C7018583B95EC216E181204717267902A426029,
	AndroidJNI_NewByteArray_mADACAA676D3E057D1C6109D8353EB704D10E7806,
	AndroidJNI_NewSByteArray_mFDAF396EF3C3CC7C315C20F1B7E14B2B51714F41,
	AndroidJNI_NewCharArray_m801332FB86A2CEBF424B046128C4C0E8F7D5D80C,
	AndroidJNI_NewShortArray_m6CC9E93F24ED8BFC02A13D89DA95E6F17276BCA6,
	AndroidJNI_NewIntArray_m46F3D6CBFA7BB4D79BDBB0971E68DE459A9F5D99,
	AndroidJNI_NewLongArray_m707798711EAB93F83F0F2E2489C13C9AFA6886D7,
	AndroidJNI_NewFloatArray_mDC04BC46000F25D8D640A2DDAB36F9C81BD496F7,
	AndroidJNI_NewDoubleArray_mA3DFC7AC3EC990D498539B59094FB3CEE4229E57,
	AndroidJNI_NewObjectArray_m4EAB5EA40119977AAD41793C78A3C19FF19A7043,
	AndroidJNI_GetBooleanArrayElement_mCEC9DA5F142E7F4DCF70453B8B0D506720D8F4F6,
	AndroidJNI_GetByteArrayElement_m4E66A92347AFB54172A7483F1F224A36C927C913,
	AndroidJNI_GetSByteArrayElement_mD8BC0A3483C53C6DB56EEE74274E71F7457B4DC2,
	AndroidJNI_GetCharArrayElement_m46AE455A6DB4CE743D19B26986A7340C9EBE4EC4,
	AndroidJNI_GetShortArrayElement_mEE788A8EDA8C2D81C30B5783B26983ACD908F1BD,
	AndroidJNI_GetIntArrayElement_m8D21128A83A398C97034293C4232487F6DEE8B52,
	AndroidJNI_GetLongArrayElement_m1043DAB33A9DCA3BF5EDBA12F5D3121DFCD21BBC,
	AndroidJNI_GetFloatArrayElement_m432455F5B77316342396460CF547335798BA7E64,
	AndroidJNI_GetDoubleArrayElement_m2A6B6A5F27DC6CDC378797E525402C238CD65E02,
	AndroidJNI_GetObjectArrayElement_mC4CAF9744617F69EFCD95B71D95492DA20A0FACE,
	AndroidJNI_SetBooleanArrayElement_mADEB936138A96C081CCE560B6F11C427C4729292,
	AndroidJNI_SetBooleanArrayElement_m16CF5F014FABEB28253AACFC93D4FF113D13DEC3,
	AndroidJNI_SetByteArrayElement_m484197612F5E5C163F9116A0F63B0355823C375F,
	AndroidJNI_SetSByteArrayElement_m470F13FC7EA450CB5B113641F99EF040E19E708C,
	AndroidJNI_SetCharArrayElement_m56BE8F363275BF93E558F4D4BF6042DA9CDF1A39,
	AndroidJNI_SetShortArrayElement_m8D2E6451D917D5452770325BE62DC667DFA26DBF,
	AndroidJNI_SetIntArrayElement_m66DF089843878DC016F15596A173906A2804E555,
	AndroidJNI_SetLongArrayElement_m54F052B44CF922C9675C31BF32B4B3726E67AC79,
	AndroidJNI_SetFloatArrayElement_mF3230F001486735FB129DD4117DD01260C998343,
	AndroidJNI_SetDoubleArrayElement_m82F4EBCB94088644F17F30C7AF48475E31BE5211,
	AndroidJNI_SetObjectArrayElement_mAEA12A91B1C20BF46CBFB5DC3B1D5AF95AA463B2,
	AndroidJNI_NewDirectByteBuffer_m17389ED6D98CC0364180BAB43F2747B48FFDB107,
	AndroidJNI_NewDirectByteBuffer_mA3DE61CE618552F4E241D9C027DC7819370D13EB,
	AndroidJNI_NewDirectByteBuffer_m933D59F86028E7B4AF11006992B2CDDD7BC854F3,
	NULL,
	AndroidJNI_GetDirectBufferAddress_m0E0B127BFEB7AAF065829DC6AE11163D5616EBE8,
	AndroidJNI_GetDirectBufferCapacity_mCAC8D9C8E45481BE59FB17406E1E16D4F9628183,
	NULL,
	AndroidJNI_GetDirectByteBuffer_m69EB5F58FB87F8F6848974E800F98EC4C11E7E50,
	AndroidJNI_GetDirectSByteBuffer_m0B37CD4B8AA4309BCA9A17A0F65CC161B48E8017,
	AndroidJNI_RegisterNatives_m41F24DA5DCB80C5E593100B4A0166B8505C9F931,
	AndroidJNI_RegisterNativesAllocate_m2F86513297C977FF48D7D1EC927AB9E9F8A6BE6A,
	AndroidJNI_RegisterNativesSet_mAB1A4FD2EB5BE4D1773EA5287C6CD03C8C9DCAC1,
	AndroidJNI_RegisterNativesAndFree_m2E2C1C1B3CEC86C3CCCC6D7B8E8A5467538D0518,
	AndroidJNI_UnregisterNatives_mFA7685BF971CE1DEC19A6E14EF80EBE6E979E82B,
	AndroidJNISafe_CheckException_m465A2955F921417ED62C035390E550889E93F8DC,
	AndroidJNISafe_QueueDeleteGlobalRef_mC800F26B3A689FCEA01B7EB26F9CD7875BAF147B,
	AndroidJNISafe_DeleteWeakGlobalRef_mBC786B6240AB03EA493A71A43D4297871FFC679A,
	AndroidJNISafe_DeleteLocalRef_m20303564C88A1B90E3D8D7A7D893392E18967094,
	AndroidJNISafe_NewString_m6A9EC18D8B122E7B901DB6BF469BFD38D1E8FE5A,
	AndroidJNISafe_GetStringChars_mE246814CD8FF4EDDEE6EBF107367C4A8EAF03849,
	AndroidJNISafe_GetObjectClass_m6FD815CB0F9760199ACD03D16FC88FED055BC9F3,
	AndroidJNISafe_GetStaticMethodID_mAD5134FF6DE446852F3F28B791C15ADBD5E9E5E8,
	AndroidJNISafe_GetMethodID_mF095B57A77BE529D51F369D94B66D14C2BC88536,
	AndroidJNISafe_GetFieldID_mAD9554C6DCE9389C441A9AB556001211B9B2663D,
	AndroidJNISafe_GetStaticFieldID_mCCCE792F7BE47B6370951D417CCB1E2713DBF482,
	AndroidJNISafe_FromReflectedMethod_mED131988778BF0267C4CE711854D4BC26D0D960B,
	AndroidJNISafe_FindClass_m2E8072B600873B4D87B2197C1168967050208D1B,
	AndroidJNISafe_NewObject_m0DEC2DAD0835B99FC58E6B44F14994A7EE05565E,
	AndroidJNISafe_SetStaticObjectField_m7757F7E30F8122DAF89F138A8AE727CB896BC721,
	AndroidJNISafe_SetStaticStringField_m445D977B2374056C6E4607FAEDB7E99A1353E2EE,
	AndroidJNISafe_SetStaticCharField_m2B8245275C36525798C869B7B1088B25BA663613,
	AndroidJNISafe_SetStaticDoubleField_mA0253927D476917D2158A9CE29F1BF535485B956,
	AndroidJNISafe_SetStaticFloatField_mB2EDDE632AB2088CD12F1FD12174FB86990BCBEE,
	AndroidJNISafe_SetStaticLongField_m299AAC2DE8B6747B0B5E109BABB2F3A4FC1F486E,
	AndroidJNISafe_SetStaticShortField_m92534AAA86D7E1055E12936C8A7BD6B865B7DB81,
	AndroidJNISafe_SetStaticSByteField_m242120982A9227E1E8344FFE9F06FD74986D15E9,
	AndroidJNISafe_SetStaticBooleanField_mBE4E40DA1B07A29D356AEEE6CB9519F2B3621AC9,
	AndroidJNISafe_SetStaticIntField_m1E20F6C72260CAFBF73207DCEC1816B2816EEBE1,
	AndroidJNISafe_GetStaticObjectField_mB6B9A9EB2619DFDF1DA56300BF9FEC19BF883867,
	AndroidJNISafe_GetStaticStringField_mB3D1325B08A38C7DAF1FA3E6CB52F6D8E0A2CB47,
	AndroidJNISafe_GetStaticCharField_mF70F6D197261364AF2A9E875D84DDDA35BD0ED96,
	AndroidJNISafe_GetStaticDoubleField_mEB86F2CE1F3879AAA9DEDA4B496F882C0E1DCBC2,
	AndroidJNISafe_GetStaticFloatField_mD1456B729026959309A839C2647279C0B6541356,
	AndroidJNISafe_GetStaticLongField_mABC2B933CEB757E3FAF1FD6C60AA0C4D38E9C49D,
	AndroidJNISafe_GetStaticShortField_m83716D4D85B30F26803F866AC47D5C04AAB5D320,
	AndroidJNISafe_GetStaticSByteField_m77596E5B1AE58DAFF39268AC954CAD53974A688D,
	AndroidJNISafe_GetStaticBooleanField_m172BEAA3F0AB6754EA5F1AD30C36DAA0D3D7C666,
	AndroidJNISafe_GetStaticIntField_m0698D50C44E490A009E8388C7321630DED5973BD,
	AndroidJNISafe_CallStaticVoidMethod_m6550C24C8D4E39C18D1D9C97FD2DBEED5452DFC2,
	AndroidJNISafe_CallStaticObjectMethod_m545474765D15AC9B0144192760B45BAA963B8F5E,
	AndroidJNISafe_CallStaticObjectMethod_m3171BFAEF780EEF400AD592B6F040E7BE87C2387,
	AndroidJNISafe_CallStaticStringMethod_m8BD92117111558CC00540B45437B4A90222B89BE,
	AndroidJNISafe_CallStaticStringMethod_m4E150E34CC6DBF27A955F8DAEE5941D6E10879C0,
	AndroidJNISafe_CallStaticCharMethod_mC4B40190CE095728E823AB8B724ECDC8F4B36155,
	AndroidJNISafe_CallStaticDoubleMethod_m73F1D51601D6849EE480389B4E43AED68C42B2B5,
	AndroidJNISafe_CallStaticFloatMethod_m3F5419A10B9DF599352938B2BAD8866F8F112364,
	AndroidJNISafe_CallStaticLongMethod_mDDE01239BEFCF007ECE05E51A249B3EB5BB61234,
	AndroidJNISafe_CallStaticShortMethod_m8330383670ECCD7E24CDD68C419745E486FA6426,
	AndroidJNISafe_CallStaticSByteMethod_m3E1F75978A2D686BC32DBF5A2F1F70F0D746C2B7,
	AndroidJNISafe_CallStaticBooleanMethod_m652685AC18F590965249C0F9B107C00C142595BB,
	AndroidJNISafe_CallStaticIntMethod_m915549FA8FD7FB93B57A9708AD759488EA64418C,
	AndroidJNISafe_SetObjectField_mFE500926F9C963FF106E8AA30A16F4C671BAA8CA,
	AndroidJNISafe_SetStringField_m649363D4E87763D6A9760359EAFB29802E90B409,
	AndroidJNISafe_SetCharField_m69D09A6A2CEA55D84B240FE32D90300AAB1334F9,
	AndroidJNISafe_SetDoubleField_mE93D0C5EC2019A1B657BD32970FE6EFC9B005A58,
	AndroidJNISafe_SetFloatField_m589CA6B8DD2BFD4515C5AEAE3772782B293F02C3,
	AndroidJNISafe_SetLongField_m13905547F5CDC7E01AB0D8C787BF98DC2870EC35,
	AndroidJNISafe_SetShortField_mF95E569C142DEDD604CE8BA7617328B3EDDD2F0D,
	AndroidJNISafe_SetSByteField_mB021168746571E7CAA8C0EAD7AA7F02C18B5EE33,
	AndroidJNISafe_SetBooleanField_m5279EA41B214699E79733DC6C93259CC9DCA1D9E,
	AndroidJNISafe_SetIntField_mD238DA37BA1B3D7693484237951A6EFEA9C62120,
	AndroidJNISafe_GetObjectField_mCF3BB1C38718D6F55081126BC7F6C286B382B275,
	AndroidJNISafe_GetStringField_mADFCA05D6DE790600B57E90B20F2E75AFC036B0F,
	AndroidJNISafe_GetCharField_m8301FA96B40E27C032590FE3F8E84A777A4739C3,
	AndroidJNISafe_GetDoubleField_mBCBD5E80223EDECC06FA783F34149E3625219074,
	AndroidJNISafe_GetFloatField_m1EAA1ED33002BBA28CA2B630521D6BF1B7D3A2E7,
	AndroidJNISafe_GetLongField_m7DD751358D10BB276D8A95D413B9DFB1E8EE81D8,
	AndroidJNISafe_GetShortField_m5D21E87061C1DAC89DF58671C53432D0361F0C6E,
	AndroidJNISafe_GetSByteField_mAD3B08AA8A97F77CAE17DD25B0F389AFAC2023B1,
	AndroidJNISafe_GetBooleanField_m34F37B560A6AEC81B9061FB3B72698C84720435D,
	AndroidJNISafe_GetIntField_mBD983688B73063DE5C55D320F60F266443FAC97C,
	AndroidJNISafe_CallVoidMethod_mC5385EEE65AD90278C00FE8DD589A63EB2CF32FB,
	AndroidJNISafe_CallObjectMethod_mBA06053048352614B802E9429FFF50C4A1B56057,
	AndroidJNISafe_CallObjectMethod_m12F882542956F2920187AADCD0295D4E32124BEF,
	AndroidJNISafe_CallStringMethod_m4E40DA54A224C0C10A8C600CAC1C2C838B69264C,
	AndroidJNISafe_CallCharMethod_mB777FAF5E9D1BFF480B7EDD5AA5352F30797E1DD,
	AndroidJNISafe_CallDoubleMethod_m01B318F7CA4F90C54D689CF0CD84DF312E68CB5E,
	AndroidJNISafe_CallFloatMethod_m7437E60E0985885D721F1592E4DACF8246F69BBE,
	AndroidJNISafe_CallLongMethod_mD04CC840004334A567747BD526F88A813CB833B6,
	AndroidJNISafe_CallShortMethod_m7C82D811B75161D4567651B0D85E5F7A2ED83A97,
	AndroidJNISafe_CallSByteMethod_m03F9BD1288769A14F5CE8477DACDD62F6D0B77E7,
	AndroidJNISafe_CallBooleanMethod_m2F5824C9EA5D1586C7E555F9F8DE01D84757D972,
	AndroidJNISafe_CallIntMethod_m60318205A7EAD0C5CC0643106A7044F1563DCC0E,
	AndroidJNISafe_FromCharArray_m54EDC9D2BE92F9973F4E00EE953EE242B231EA96,
	AndroidJNISafe_FromDoubleArray_mDEA8F2C7854101272F3A2733F351B570AAD5D9D9,
	AndroidJNISafe_FromFloatArray_mDE02985159EEFD2CB28611C797AC21DE8B6300B8,
	AndroidJNISafe_FromLongArray_mC4D73C0DA27F212947AB85AA2030A35BECDF8288,
	AndroidJNISafe_FromShortArray_m62C0CB2D0BAE96D4B8CE365630361150EBE884FC,
	AndroidJNISafe_FromByteArray_mB06EF0FDBF6C738231E8F9D4998C38551131C4C5,
	AndroidJNISafe_FromSByteArray_m261D638D8B059AB777BEF0BEFDD0822717DFF2B1,
	AndroidJNISafe_FromBooleanArray_m36ED740401185EC0A959CA0F96A324A69E668646,
	AndroidJNISafe_FromIntArray_mC4C4DC70FFA39CD6E3E02FDAC7192324E6D4614E,
	AndroidJNISafe_ToObjectArray_m4C95D999242E900D9C70891E44100A5EB5020C5F,
	AndroidJNISafe_ToCharArray_mFBF42A984F1C5D618CD0366B3B344E2BF8856B12,
	AndroidJNISafe_ToDoubleArray_m6C1716EFF6DCA1AE3E04D292EB38A31E4132C1C1,
	AndroidJNISafe_ToFloatArray_m18207119C3AC0C5D71DA537B2CEB21D11301B732,
	AndroidJNISafe_ToLongArray_m67486F6D1F467D2354EEB74DACFDA79A1F3F7E0C,
	AndroidJNISafe_ToShortArray_m170C4D2D7D1ED3A02B4C707FB666BF4F2A9D02ED,
	AndroidJNISafe_ToByteArray_mB36D6ABE2FF31844554A353E136B2153BFDF0F65,
	AndroidJNISafe_ToSByteArray_m10BD1D36C8D2D7F764FD8C87742FD700DB48665C,
	AndroidJNISafe_ToBooleanArray_m94630C7B69D819D7BE683691B46879C6154B5F3A,
	AndroidJNISafe_ToIntArray_mE4647AC18D85206D98121752C3B8CD7D52A321EB,
	AndroidJNISafe_GetObjectArrayElement_m02B6993F13670DD2D1557D75EC31D8D860F10FD0,
	AndroidJNISafe_GetArrayLength_m809419F04C9BB93FED5B4A89F0539231C9B90E6F,
	AndroidJavaRunnable__ctor_m000E4FEB2DE8031A1CD733610D76E2BF60490334,
	AndroidJavaRunnable_Invoke_m98CFB1479B942F71BF29F53CFDAC1CB9DAFAEBE1,
	AndroidJavaException__ctor_mD4B5992BB074504F8E86D79EA98752D3CB154541,
	AndroidJavaException_get_StackTrace_m28AC922BCC16051CCBA4C7E5F69698264AA7CC27,
	GlobalJavaObjectRef__ctor_mFE5679D1B51F51CBF11721773C0D767286AC22E8,
	GlobalJavaObjectRef_Finalize_m2EE89F98A391773F885A4A312FD4BD134E0D46D8,
	GlobalJavaObjectRef_op_Implicit_m444B263750F9B778C87C30EA918CDC0B62271F24,
	GlobalJavaObjectRef_Dispose_m45E67345587866D5A50D250D1C17425110703520,
	AndroidJavaRunnableProxy__ctor_mB173256AF7629962B226343C4F6F94FFFF7317C3,
	AndroidJavaRunnableProxy_run_m014F4E0A8ED56A054096F2BAC90653716D2A0D46,
	AndroidJavaRunnableProxy_Invoke_m72839D8A915DA6FB2E25959330E57B4AC884CB77,
	AndroidJavaProxy__ctor_m2832886A0E1BBF6702653A7C6A4609F11FB712C7,
	AndroidJavaProxy__ctor_mFA05DF6B31FC284C65D378C02A2A34F277DFE6E5,
	AndroidJavaProxy_Finalize_m6E4C294F2117D7A07E82A315081C9239AFA217E8,
	AndroidJavaProxy_Invoke_m9D765F3E7DC37C5CB14C4884F2873B48D2F96BFB,
	AndroidJavaProxy_Invoke_mCAE9C5E669AD50DE372494E12224FF1F31A43F1D,
	AndroidJavaProxy_Invoke_mD2B81C060739751DA8148FB2B8C3DBCBE1EE4FCE,
	AndroidJavaProxy_equals_mC390139E035408E858940EB523D45ED3C8377110,
	AndroidJavaProxy_hashCode_m7991233D3D6D5F994E7BC59C3CB65DBBEDF8CA93,
	AndroidJavaProxy_toString_mF77EEDD3BB413F1273D9970BFB0D7C388366B256,
	AndroidJavaProxy_GetProxyObject_mBFD2FBEF9ED9D4AE23DECF5836E5C73A886E2109,
	AndroidJavaProxy_GetRawProxy_m685E066A4D378B596CD88385B954AE90CBF328A9,
	AndroidJavaProxy__cctor_m44B4074B6A6D8193D86E65DB4C165C2371615D63,
	AndroidJavaObject__ctor_mB47CA3FC88F645DAB31FB0FAAA32E9159B1DB19E,
	AndroidJavaObject__ctor_m1F1F88504475490860A246714F36205FB7D53362,
	AndroidJavaObject__ctor_m262439771D3A3EFBD18E5D06188D11989D562635,
	AndroidJavaObject__ctor_m0F50ADD04B4BEA5ACB6B614BB206EBFA9353CF6B,
	AndroidJavaObject__ctor_mA61E481C9C0F990FF9BEBFE9E1299612BC174E0E,
	AndroidJavaObject__ctor_m5A65B5D325C2CEFAC4097A0D3813F8E158178DD7,
	AndroidJavaObject__ctor_m0CEE7D570807333CE2C193A82AB3AB8D4F873A6B,
	AndroidJavaObject__ctor_mF4FA101CAFFEAA5DC312E9A536C907DC54BEB0B4,
	AndroidJavaObject_Dispose_m2B1593C20B3CE1C8FF95982F638F50985F9DD9E6,
	NULL,
	NULL,
	AndroidJavaObject_Call_mDEF7846E2AB1C5379069BB21049ED55A9D837B1C,
	AndroidJavaObject_Call_m5B4E5F1C11B70F16B4526DCAC2E008AEE791612F,
	NULL,
	NULL,
	AndroidJavaObject_CallStatic_mB677DE04369EDD8E6DECAF2F233116EE1F06555C,
	AndroidJavaObject_CallStatic_m49C3058426CE1F766EBE911D5E127E565806043D,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	AndroidJavaObject_GetRawObject_m536F043B5CE2C21369FF6173C9D2A9A62136BC48,
	AndroidJavaObject_GetRawClass_mE4FB4DC4F856A52E10C6AAD0B65BEBF47B5071F5,
	AndroidJavaObject_CloneReference_m6DF6E2BF8D91804B303C93C2026E4A39977E8428,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	AndroidJavaObject_DebugPrint_m047934BF3D1E6676FDDBDA038E1AF387C5413533,
	AndroidJavaObject_DebugPrint_m41CA713464E773016D31C1B6C1489AC34A542CE6,
	AndroidJavaObject__AndroidJavaObject_m1284CB7198514B8C06A2BF794ACDC909DC26443F,
	AndroidJavaObject__AndroidJavaObject_m19164C456D01B4A1B0D4B186A0E38C12C956ABE5,
	AndroidJavaObject__ctor_m67B4EEAB015B123D5A3EDCAD914B4795A3B67F04,
	AndroidJavaObject_Finalize_m87374EE46B27BE3559CACED8A1B62475200AB5AA,
	AndroidJavaObject_Dispose_m87886676A84FA079C0FE45E6C31D790D764652BE,
	AndroidJavaObject__Call_m4C4D7D7287030773175BDF47681EA018DFA4DF1A,
	AndroidJavaObject__Call_m2126160FB635069207535BD0E700C3605FDB3308,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	AndroidJavaObject__CallStatic_mD63902D30CD5626DAEAD1D6484AF7A9ACA85590E,
	AndroidJavaObject__CallStatic_mE917E474DB9801610FB7ABE5BE749DF84CEFD48A,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	AndroidJavaObject_AndroidJavaObjectDeleteLocalRef_m2ECEEAF6389ABB9D6B963B8A98568ECD9413DF3C,
	AndroidJavaObject_AndroidJavaClassDeleteLocalRef_m56C84D7516BCB51A84E8AFDB3FCA46BAF494548F,
	NULL,
	AndroidJavaObject__GetRawObject_mC5B8B60BEF515F5EE2A113D60991A433DA740C69,
	AndroidJavaObject__GetRawClass_m470EAEBF8B0BD365FD13F1C6F55119836452FDFA,
	AndroidJavaClass__ctor_mB5466169E1151B8CC44C8FED234D79984B431389,
	AndroidJavaClass__AndroidJavaClass_mF481A9584D78F32C64219FDA49CB84B6F0A017DD,
	AndroidJavaClass__ctor_mB206D3CB990755BD56E308F61CD43BB9EA4421D0,
	AndroidReflection_IsPrimitive_mA41A9ECECE3D73679C79DC8B0FDD32B59570DF25,
	AndroidReflection_IsAssignableFrom_mBAE0D5121AD208959B89E9BDAF62F0E0BAB917C6,
	AndroidReflection_GetStaticMethodID_mF8378F28D71354360CA9F8AAF8EAF24A3B9594CF,
	AndroidReflection_GetMethodID_m4318CE3C372444B966909092BF046833A9733EE7,
	AndroidReflection_GetConstructorMember_m6380904C6B5AF39F973E79B13B3C2B53DFD759B3,
	AndroidReflection_GetMethodMember_m2871C6DC2BA1AE5FF3FA448AC36022CC8B19C9EA,
	AndroidReflection_GetFieldMember_m6E589FB4DEDCFEE84B2CFD9C416D0C21EDB09D2F,
	AndroidReflection_GetFieldClass_m88D4993FF662A0EF387AADD915A4FD8054F0DF27,
	AndroidReflection_GetFieldSignature_mF36DDDDB3E57742AD66487E2D7CECBE81736B259,
	AndroidReflection_NewProxyInstance_m052E9828C670A85F3B9DD9D4632A9A6B52FB5EDF,
	AndroidReflection_CreateInvocationError_m20254A88DCE4AEE011AAC3D42B94DC7B588AB20F,
	AndroidReflection__cctor_m59365F63F057115EA8FBCB0AFBAF94012DAAC622,
	_AndroidJNIHelper_CreateJavaProxy_mAC059F7C2716D4E9099EA7268F5378260E6DDF36,
	_AndroidJNIHelper_CreateJavaRunnable_m25981D4249F420FA78240CDC2F7E53B9EAD5F31A,
	_AndroidJNIHelper_InvokeJavaProxyMethod_mCA9BD35B5AE99B6DFABBFE9968DD1EBE931F70C2,
	_AndroidJNIHelper_CreateJNIArgArray_mA3976C6F68E0BF29442CB20FBD69CBA72EAB9D45,
	_AndroidJNIHelper_UnboxArray_m5A46896F07017C9B0908AE3D72DA1285071DEA4A,
	_AndroidJNIHelper_Unbox_m7D786B14E4A90E5411FC9AA7F985451DEBC95731,
	_AndroidJNIHelper_Box_mA0B7B8444C6AC2BABBDC7B1A7FACDA3EA6F816B0,
	_AndroidJNIHelper_DeleteJNIArgArray_m44D912ADAF5C0E49A914AD7B1DE776C13AC4F61A,
	_AndroidJNIHelper_ConvertToJNIArray_m6E451CD0C15DE2812F103C604CB1EF1D358D1213,
	NULL,
	_AndroidJNIHelper_GetConstructorID_mD9CF17C7FA280BED12A8BB5D6F12E0D2C6778C3F,
	_AndroidJNIHelper_GetMethodID_m185ABE7B65325FA580775AF81FE17687994C98DD,
	NULL,
	NULL,
	_AndroidJNIHelper_GetConstructorID_m89A45100B1A05DEBF6586AB234A7DBE2044B2490,
	_AndroidJNIHelper_GetMethodID_mECADE8D5FC2712DED913CDECBB9D7E8C46812184,
	_AndroidJNIHelper_GetMethodIDFallback_m7155B2DE7C4F4E71C6AE29F856A1A9618ADAAD4F,
	_AndroidJNIHelper_GetFieldID_m698CE89D968CF7A131861E95FF72272A6A14FB95,
	_AndroidJNIHelper_GetSignature_m414A7A6B98FB5565075E4C51F22C482F2BCEEF5F,
	_AndroidJNIHelper_GetSignature_m309D35CC998B59CFEDA2D6EB5D0E221B3F1374F7,
	NULL,
	_AndroidJNIHelper__ctor_m0535562F59B589E117E57B8EA07ECE900848F509,
	AndroidApp_get_Context_m72441A446635EF0888D75440465A6E3BF4FE1FBE,
	AndroidApp_get_Activity_m3325B65D03BA5A73841028AA1E82F1721BDBC379,
	AndroidApp_AcquireContextAndActivity_mBCD23D63F072095B881F41AB2877CD78520D7FB5,
	AndroidApp_get_UnityPlayerRaw_m17DB30494D24BF3AC1A6B42634EA8C70427C4EB5,
	AndroidAssetPackInfo__ctor_m747C0EAD6448BA479BAE3C5EAB67F205E0307972,
	AndroidAssetPackState__ctor_m5CB1F078A45558A0966BA32FCFE18CFC46CA941B,
	AndroidAssetPackUseMobileDataRequestResult__ctor_mB46211F6D3B3A421B1C1D9E05F3FC62858383E8F,
	AndroidAssetPacks_GetAssetPackManager_m91B486E7EEF9F019B820E11F10E1D877609778A9,
	AssetPackManagerDownloadStatusCallback__ctor_m03D0B212EFAEACF611D60596E978DD0468C6D936,
	AssetPackManagerDownloadStatusCallback_onStatusUpdate_m4042BE15C2B4A64CE1BAF4734E6A0BF8DD4FFC7A,
	AssetPackManagerMobileDataConfirmationCallback__ctor_m927FCB784F7D90524C6725B9E64EF8799538999B,
	AssetPackManagerMobileDataConfirmationCallback_onMobileDataConfirmationResult_m61FEFDED8787D798CF30CB78DC133505B7D3614A,
	AssetPackManagerStatusQueryCallback__ctor_m66F3B4A8BD8911F07121E2F8EF871741CE3A2CDB,
	AssetPackManagerStatusQueryCallback_onStatusResult_m8C41C5CC2F37808E5C5C12B023DF31E1DF96C314,
	PermissionCallbacks_add_PermissionGranted_m74335D4200D9B1A7C80AB9C133F95C61FCDCDF89,
	PermissionCallbacks_remove_PermissionGranted_m4A3F9873FC159F89A2AD35F2FCAFF66A19813AF4,
	PermissionCallbacks_add_PermissionDenied_mE0B2826463785B050C999C70F443FCC3822563D0,
	PermissionCallbacks_remove_PermissionDenied_mF1A606ADE21F9520909126D3642B0BC2D6E994A1,
	PermissionCallbacks_add_PermissionDeniedAndDontAskAgain_mEDE8C00FEF2F649F10A47F30AC4ECB09E52DB9AA,
	PermissionCallbacks_remove_PermissionDeniedAndDontAskAgain_m55B04AE58C687946BDFA2094ED851518B2A1D68D,
	PermissionCallbacks__ctor_m91B14BBBC8913C131E400BA0D13576822AAE7A75,
	PermissionCallbacks_onPermissionGranted_m723440705B5B21B97AF5206716275BAE2A122E3C,
	PermissionCallbacks_onPermissionDenied_m0E05122B560DD62BB38178EE601E65854017980A,
	PermissionCallbacks_onPermissionDeniedAndDontAskAgain_m1191CF6422AFD8E8FE7BDBDBC04721D63718A5D9,
	Permission_GetUnityPermissions_m6A22A923A8B036209E6218E24F7AA8CA79AA15BD,
	Permission_HasUserAuthorizedPermission_mF4C90E13124E28F6F672200E489CC25A9B645B8B,
	Permission_RequestUserPermission_mF9CF3A21AAF34B311137C4D00B3AD6A6C2694242,
	Permission_RequestUserPermissions_m09FE74BF5829C8B48A0E6C9B7F7846FEE763D2DA,
};
static const int32_t s_InvokerIndices[529] = 
{
	11724,
	11564,
	11255,
	10415,
	10415,
	9480,
	8910,
	10415,
	9480,
	8910,
	11256,
	11256,
	11256,
	11351,
	10776,
	10792,
	10776,
	10415,
	8910,
	11351,
	11351,
	0,
	0,
	0,
	0,
	9484,
	11257,
	11252,
	11253,
	11254,
	11259,
	11250,
	11261,
	11249,
	9480,
	10765,
	10765,
	10765,
	10765,
	10765,
	10765,
	10765,
	10765,
	11748,
	11746,
	11746,
	11746,
	11256,
	11255,
	11255,
	9476,
	9476,
	11255,
	10146,
	11211,
	10376,
	11748,
	11802,
	11802,
	11578,
	11209,
	11255,
	11255,
	11576,
	11576,
	11795,
	11255,
	11576,
	11255,
	11576,
	10146,
	11209,
	11255,
	9478,
	9474,
	9475,
	11255,
	10146,
	9480,
	9480,
	9480,
	9480,
	11256,
	11256,
	11256,
	11256,
	11348,
	11211,
	11211,
	11348,
	9525,
	9522,
	9523,
	9478,
	9474,
	9475,
	9425,
	9422,
	9423,
	9311,
	9309,
	9310,
	9395,
	9393,
	9394,
	9311,
	9593,
	9591,
	9592,
	9629,
	9627,
	9628,
	9604,
	9602,
	9603,
	9381,
	9379,
	9380,
	9464,
	9462,
	9463,
	9881,
	9873,
	9874,
	10469,
	10414,
	10146,
	10146,
	10554,
	10592,
	10334,
	10375,
	10404,
	10563,
	10318,
	9881,
	9880,
	9875,
	9875,
	9882,
	9884,
	9877,
	9878,
	9879,
	9883,
	9876,
	9525,
	9522,
	9523,
	9478,
	9474,
	9475,
	9425,
	9422,
	9423,
	9311,
	9309,
	9310,
	9395,
	9393,
	9394,
	9311,
	9593,
	9591,
	9592,
	9629,
	9627,
	9628,
	9604,
	9602,
	9603,
	9381,
	9379,
	9380,
	9464,
	9462,
	9463,
	9881,
	9873,
	9874,
	10469,
	10414,
	10146,
	10146,
	10554,
	10592,
	10334,
	10375,
	10404,
	10563,
	10318,
	9881,
	9880,
	9875,
	9875,
	9882,
	9884,
	9877,
	9878,
	9879,
	9883,
	9876,
	11256,
	11256,
	11256,
	10410,
	11256,
	10410,
	11256,
	10410,
	11256,
	10410,
	11256,
	10410,
	11256,
	10410,
	11256,
	10410,
	9468,
	10418,
	11256,
	11348,
	11348,
	11348,
	11348,
	11348,
	11348,
	11348,
	11348,
	11348,
	11348,
	11211,
	11253,
	11253,
	11253,
	11253,
	11253,
	11253,
	11253,
	11253,
	11253,
	9473,
	10145,
	10145,
	10553,
	10591,
	10333,
	10374,
	10403,
	10562,
	10317,
	10413,
	9864,
	9864,
	9870,
	9870,
	9872,
	9866,
	9867,
	9868,
	9871,
	9865,
	9869,
	10411,
	11239,
	11240,
	0,
	11062,
	11231,
	0,
	10879,
	10881,
	10376,
	11253,
	8586,
	9424,
	11211,
	11802,
	11576,
	11576,
	11576,
	11256,
	11348,
	11255,
	9480,
	9480,
	9480,
	9480,
	11255,
	11256,
	9474,
	9880,
	9881,
	9884,
	9876,
	9883,
	9879,
	9877,
	9882,
	9875,
	9878,
	10414,
	10469,
	10592,
	10318,
	10563,
	10404,
	10334,
	10554,
	10146,
	10375,
	9873,
	9478,
	9474,
	9525,
	9522,
	9627,
	9379,
	9602,
	9462,
	9393,
	9591,
	9309,
	9422,
	9880,
	9881,
	9884,
	9876,
	9883,
	9879,
	9877,
	9882,
	9875,
	9878,
	10414,
	10469,
	10592,
	10318,
	10563,
	10404,
	10334,
	10554,
	10146,
	10375,
	9873,
	9478,
	9474,
	9522,
	9627,
	9379,
	9602,
	9462,
	9393,
	9591,
	9309,
	9422,
	11348,
	11348,
	11348,
	11348,
	11348,
	11348,
	11348,
	11348,
	11348,
	10418,
	11256,
	11256,
	11256,
	11256,
	11256,
	11256,
	11256,
	11256,
	11256,
	10413,
	11211,
	3432,
	7776,
	3435,
	7656,
	6178,
	7776,
	11256,
	7776,
	6213,
	7776,
	2500,
	6213,
	6213,
	7776,
	2592,
	2592,
	2500,
	4450,
	7618,
	7656,
	7656,
	7620,
	11802,
	3435,
	3435,
	3435,
	3435,
	3435,
	3435,
	6178,
	1678,
	7776,
	0,
	0,
	3435,
	3382,
	0,
	0,
	3435,
	3382,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	7620,
	7620,
	7656,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	6213,
	1161,
	3435,
	3382,
	7776,
	7776,
	6094,
	3435,
	3382,
	0,
	0,
	0,
	0,
	0,
	0,
	3435,
	3382,
	0,
	0,
	0,
	0,
	0,
	0,
	11348,
	11348,
	0,
	7620,
	7620,
	6213,
	6213,
	6178,
	11096,
	10162,
	9483,
	9483,
	10415,
	8910,
	8910,
	11255,
	11348,
	9477,
	10417,
	11802,
	9478,
	11256,
	9482,
	10776,
	11351,
	11351,
	11351,
	10776,
	11256,
	0,
	10415,
	8910,
	0,
	0,
	10415,
	8910,
	8910,
	8910,
	11351,
	11351,
	0,
	7776,
	11754,
	11754,
	11802,
	11748,
	293,
	1707,
	6094,
	11754,
	3435,
	289,
	6213,
	6094,
	3435,
	1076,
	6213,
	6213,
	6213,
	6213,
	6213,
	6213,
	7776,
	6213,
	6213,
	6213,
	11754,
	11096,
	11578,
	10792,
};
static const Il2CppTokenRangePair s_rgctxIndices[43] = 
{
	{ 0x06000016, { 0, 2 } },
	{ 0x06000017, { 2, 1 } },
	{ 0x06000018, { 3, 1 } },
	{ 0x06000019, { 4, 1 } },
	{ 0x0600010B, { 5, 5 } },
	{ 0x0600010E, { 10, 2 } },
	{ 0x0600019A, { 12, 1 } },
	{ 0x0600019B, { 13, 1 } },
	{ 0x0600019E, { 14, 1 } },
	{ 0x0600019F, { 15, 1 } },
	{ 0x060001A2, { 16, 2 } },
	{ 0x060001A3, { 18, 2 } },
	{ 0x060001A4, { 20, 2 } },
	{ 0x060001A5, { 22, 2 } },
	{ 0x060001A6, { 24, 2 } },
	{ 0x060001A7, { 26, 2 } },
	{ 0x060001A8, { 28, 2 } },
	{ 0x060001A9, { 30, 2 } },
	{ 0x060001AD, { 32, 3 } },
	{ 0x060001AE, { 35, 3 } },
	{ 0x060001AF, { 38, 2 } },
	{ 0x060001B0, { 40, 2 } },
	{ 0x060001B1, { 42, 3 } },
	{ 0x060001B2, { 45, 3 } },
	{ 0x060001B3, { 48, 2 } },
	{ 0x060001B4, { 50, 2 } },
	{ 0x060001BE, { 52, 3 } },
	{ 0x060001BF, { 55, 3 } },
	{ 0x060001C0, { 58, 3 } },
	{ 0x060001C1, { 61, 3 } },
	{ 0x060001C2, { 64, 3 } },
	{ 0x060001C3, { 67, 2 } },
	{ 0x060001C6, { 69, 3 } },
	{ 0x060001C7, { 72, 3 } },
	{ 0x060001C8, { 75, 3 } },
	{ 0x060001C9, { 78, 3 } },
	{ 0x060001CA, { 81, 3 } },
	{ 0x060001CB, { 84, 2 } },
	{ 0x060001CE, { 86, 2 } },
	{ 0x060001E9, { 88, 2 } },
	{ 0x060001EC, { 90, 1 } },
	{ 0x060001ED, { 91, 1 } },
	{ 0x060001F4, { 92, 1 } },
};
extern const uint32_t g_rgctx__AndroidJNIHelper_ConvertFromJNIArray_TisArrayType_t33FA9A7F66F041B4E2878FF619DA2A8FCDD39085_m2DE197CE4F8061C7658B6308D28812D6AE662FE5;
extern const uint32_t g_rgctx_ArrayType_t33FA9A7F66F041B4E2878FF619DA2A8FCDD39085;
extern const uint32_t g_rgctx__AndroidJNIHelper_GetMethodID_TisReturnType_tD2A6EF0F4156D493768CADDC35C177D5FF55C4DE_m7AC36AC0446E8D34546FE4D002F9C8B56D25A8B6;
extern const uint32_t g_rgctx__AndroidJNIHelper_GetFieldID_TisFieldType_tC112CF857F2F9A16A01117BD8DE4B420B806AB6D_mFA7B428C4344302ADCD1670CEB890B397DAD9227;
extern const uint32_t g_rgctx__AndroidJNIHelper_GetSignature_TisReturnType_t9A95B9FC093A340C4D5AEABCB66770F89AE0048D_mE373542209BEBEDCBECCAB5C5B944D0778370DEC;
extern const uint32_t g_rgctx_NativeArray_1_t16CB0CB1C7AC7DC247B530183CDF43ACF7838729;
extern const uint32_t g_rgctx_NativeArray_1_get_IsCreated_mB57B6B3595999F0F78DC5640BA825CA8FBAF563E;
extern const uint32_t g_rgctx_NativeArray_1_t16CB0CB1C7AC7DC247B530183CDF43ACF7838729;
extern const uint32_t g_rgctx_NativeArray_1_get_Length_m865315721EAA672471E22BDB976489499169BEA9;
extern const uint32_t g_rgctx_NativeArrayUnsafeUtility_GetUnsafePtr_TisT_t96DC1C87A94E8E20D42E25A7ADDFD198F460AEF9_mA379B333C68D94BB6783639230F4213CCFC0CFDA;
extern const uint32_t g_rgctx_NativeArray_1_t0DEDE16EA341DF331CA57F6EE535A595111735D3;
extern const uint32_t g_rgctx_NativeArrayUnsafeUtility_ConvertExistingDataToNativeArray_TisT_t95BE45B46464B33538EF221E8410FB8747D94A8A_mC6DC23FD7873700310ACE1D943D2CDAC368F812C;
extern const uint32_t g_rgctx_TU5BU5D_tD127995738FFC823AB95C22AB082E5E8100A89FA;
extern const uint32_t g_rgctx_TU5BU5D_t51E5677E8B68A84E5D8B8D93D4649D9FFE54EAF4;
extern const uint32_t g_rgctx_TU5BU5D_t125EC412AD4EAF9F540D9FEE4FBE333887757BD3;
extern const uint32_t g_rgctx_TU5BU5D_t98C49705DF539DAE36E9273D717504910A8913CB;
extern const uint32_t g_rgctx_AndroidJavaObject__Get_TisFieldType_tE541E61DC1EE486A3DDC10DCA2A2DD9A2A3BADE8_m06134DE4CC6EA8E3189542B792E94123E91E03C6;
extern const uint32_t g_rgctx_FieldType_tE541E61DC1EE486A3DDC10DCA2A2DD9A2A3BADE8;
extern const uint32_t g_rgctx_AndroidJavaObject__Get_TisFieldType_t3D88A0B7A2BB1C07E18A9AA7AB32582B0FB37337_mA93D48F3759004A792161BF419A99D9FD777CD43;
extern const uint32_t g_rgctx_FieldType_t3D88A0B7A2BB1C07E18A9AA7AB32582B0FB37337;
extern const uint32_t g_rgctx_FieldType_t5C6F84F3CFFB0874A4DA0D1C58053A25B835551E;
extern const uint32_t g_rgctx_AndroidJavaObject__Set_TisFieldType_t5C6F84F3CFFB0874A4DA0D1C58053A25B835551E_m462146FCC7D2B3F83FF6E8999553D33E4959F5BE;
extern const uint32_t g_rgctx_FieldType_tD083F29D6E32E63E26B83C297746967559FB5289;
extern const uint32_t g_rgctx_AndroidJavaObject__Set_TisFieldType_tD083F29D6E32E63E26B83C297746967559FB5289_mDE6A8DCAE2CB50FC2B805F1D4DE98761DFEB83D9;
extern const uint32_t g_rgctx_AndroidJavaObject__GetStatic_TisFieldType_tBB418E296327456981AAF34C2BEB510AEC3C4E4D_m287CA0417FCCBDC7C54BA521F00661EE781AE83E;
extern const uint32_t g_rgctx_FieldType_tBB418E296327456981AAF34C2BEB510AEC3C4E4D;
extern const uint32_t g_rgctx_AndroidJavaObject__GetStatic_TisFieldType_t4E8B4431DA3B0620A6961D0830B049CE0FA25BB3_m05D4249781BCDEAA2C845A1F9D487BBE529C7F65;
extern const uint32_t g_rgctx_FieldType_t4E8B4431DA3B0620A6961D0830B049CE0FA25BB3;
extern const uint32_t g_rgctx_FieldType_t6076CC06F19BCC0301AE8C734F26CA429D2DB469;
extern const uint32_t g_rgctx_AndroidJavaObject__SetStatic_TisFieldType_t6076CC06F19BCC0301AE8C734F26CA429D2DB469_m63581F10344537651B9090C50B7BAFA8B9CDB1A3;
extern const uint32_t g_rgctx_FieldType_tD9FB37412CCB11866A06B8D683ACBBD34ED06C6B;
extern const uint32_t g_rgctx_AndroidJavaObject__SetStatic_TisFieldType_tD9FB37412CCB11866A06B8D683ACBBD34ED06C6B_m6D1D734C7ECA5B4E0F1707990715B5F6E199F51F;
extern const uint32_t g_rgctx_TU5BU5D_t2D0810F438CE188B9FDA75ED2E8B60C28FD63BE6;
extern const uint32_t g_rgctx_AndroidJavaObject__Call_TisReturnType_t6A981EFC55AACA8DB2914A6B9C24AF2C1F0D86E3_mBA92180AE68E07DA29AB2ED05D52AA22E1ECEC52;
extern const uint32_t g_rgctx_ReturnType_t6A981EFC55AACA8DB2914A6B9C24AF2C1F0D86E3;
extern const uint32_t g_rgctx_TU5BU5D_tF1AED0F0B8DCF565C07B4BF3D77EFB4814B44AC0;
extern const uint32_t g_rgctx_AndroidJavaObject__Call_TisReturnType_tE77199CFC11B9237A747C8E23BCDA97324CD4769_m939ABCC227D3F47C42038223BDDC8143684EFEF3;
extern const uint32_t g_rgctx_ReturnType_tE77199CFC11B9237A747C8E23BCDA97324CD4769;
extern const uint32_t g_rgctx_AndroidJavaObject__Call_TisReturnType_t7C9CEFF53F7F785E3B0A2AA52BF0599DB9E4C7A7_m53C8581E9FA8AB194F9919FD0B5F910D4D0EDEDF;
extern const uint32_t g_rgctx_ReturnType_t7C9CEFF53F7F785E3B0A2AA52BF0599DB9E4C7A7;
extern const uint32_t g_rgctx_AndroidJavaObject__Call_TisReturnType_t7ECE3320BEFB3505409302EC3AA6B828AE7E1DB5_m370E365D1367373DBFCFAC4418C18118A8825073;
extern const uint32_t g_rgctx_ReturnType_t7ECE3320BEFB3505409302EC3AA6B828AE7E1DB5;
extern const uint32_t g_rgctx_TU5BU5D_t3553B3A68B7CC38DCA3828A5BB17E68D7A256089;
extern const uint32_t g_rgctx_AndroidJavaObject__CallStatic_TisReturnType_t41223B870DEBD9DC66C7F3F6FDDF2CF6D061E4EB_mA272E4A1258A5DB68113DF8B90DF5B5B3A724832;
extern const uint32_t g_rgctx_ReturnType_t41223B870DEBD9DC66C7F3F6FDDF2CF6D061E4EB;
extern const uint32_t g_rgctx_TU5BU5D_t57FCD3B7614A641CACF7B0823E2458C79927EC24;
extern const uint32_t g_rgctx_AndroidJavaObject__CallStatic_TisReturnType_t3F35C7C5E6E91ECEB1E553CA45292DBBEC6E5501_m6B6FF1D17E7D4CA7A348601815BC5E194E1215F4;
extern const uint32_t g_rgctx_ReturnType_t3F35C7C5E6E91ECEB1E553CA45292DBBEC6E5501;
extern const uint32_t g_rgctx_AndroidJavaObject__CallStatic_TisReturnType_t94E13999E45FF70AA5DA5E427955FC4E439412B2_m7F6D9361C675DD63CF53A9BE7588D79625F85B4D;
extern const uint32_t g_rgctx_ReturnType_t94E13999E45FF70AA5DA5E427955FC4E439412B2;
extern const uint32_t g_rgctx_AndroidJavaObject__CallStatic_TisReturnType_t955BFF9AB851C80203957B433FC00E2046696241_mBD7D20A96A02F1EEC83A23C3065874556A832801;
extern const uint32_t g_rgctx_ReturnType_t955BFF9AB851C80203957B433FC00E2046696241;
extern const uint32_t g_rgctx_AndroidJNIHelper_GetMethodID_TisReturnType_t0FD1385ACD92B5652F803E183304929EDB7632D9_mAB39D5C2ACF4BF7098D1D76F31302A29A45D3825;
extern const uint32_t g_rgctx_AndroidJavaObject__Call_TisReturnType_t0FD1385ACD92B5652F803E183304929EDB7632D9_m8AC46895CB418A0C6E533D3B85F0A7E215C5DF13;
extern const uint32_t g_rgctx_ReturnType_t0FD1385ACD92B5652F803E183304929EDB7632D9;
extern const uint32_t g_rgctx_ReturnType_t79ECD6DB4ABC339D39F04EE50C832B65A84F5147;
extern const uint32_t g_rgctx_ReturnType_t79ECD6DB4ABC339D39F04EE50C832B65A84F5147;
extern const uint32_t g_rgctx_AndroidJavaObject_FromJavaArrayDeleteLocalRef_TisReturnType_t79ECD6DB4ABC339D39F04EE50C832B65A84F5147_mC821623012C4DEC54879B5ED4EBDE68072706E25;
extern const uint32_t g_rgctx_AndroidJNIHelper_GetFieldID_TisFieldType_tBD81DD51AB3076BC0134BD255EBBAC34E341C535_m715A2F9AB6249CE2205B84D8BBABD111EB27AFA6;
extern const uint32_t g_rgctx_AndroidJavaObject__Get_TisFieldType_tBD81DD51AB3076BC0134BD255EBBAC34E341C535_mB880B82C1375741C2F32BADCF42CE43A22630BBA;
extern const uint32_t g_rgctx_FieldType_tBD81DD51AB3076BC0134BD255EBBAC34E341C535;
extern const uint32_t g_rgctx_FieldType_t1266B1AF802837275B234C725395054CAC857D29;
extern const uint32_t g_rgctx_FieldType_t1266B1AF802837275B234C725395054CAC857D29;
extern const uint32_t g_rgctx_AndroidJavaObject_FromJavaArrayDeleteLocalRef_TisFieldType_t1266B1AF802837275B234C725395054CAC857D29_m39A16BD90DEEAA423B02705847D4672CEDDAC80B;
extern const uint32_t g_rgctx_AndroidJNIHelper_GetFieldID_TisFieldType_t2F905C7C598CE04C35E1297452F5AE8BFD208DAE_mFB00C8B4BED9EBCFA629A59166C7EFC55D5D4085;
extern const uint32_t g_rgctx_FieldType_t2F905C7C598CE04C35E1297452F5AE8BFD208DAE;
extern const uint32_t g_rgctx_AndroidJavaObject__Set_TisFieldType_t2F905C7C598CE04C35E1297452F5AE8BFD208DAE_mB60F838F278F88BFAEC883D8204F98BE4D07414A;
extern const uint32_t g_rgctx_FieldType_tC72607BAF758371FA7A7DDA2D0873E433EE11D19;
extern const uint32_t g_rgctx_FieldType_tC72607BAF758371FA7A7DDA2D0873E433EE11D19;
extern const uint32_t g_rgctx_AndroidJNIHelper_GetMethodID_TisReturnType_t2DAB0875DF34A21B532F695F9B7329A0B5BBCB27_m872C06BDFAA7CC5369EABC3850D99E950C4E9323;
extern const uint32_t g_rgctx_AndroidJavaObject__CallStatic_TisReturnType_t2DAB0875DF34A21B532F695F9B7329A0B5BBCB27_m48CE4CF2840315B19CD807FEB9B9901A9B978556;
extern const uint32_t g_rgctx_ReturnType_t2DAB0875DF34A21B532F695F9B7329A0B5BBCB27;
extern const uint32_t g_rgctx_ReturnType_tB840B385B71FF8E350FF799B5A7DF58E03C1A69B;
extern const uint32_t g_rgctx_ReturnType_tB840B385B71FF8E350FF799B5A7DF58E03C1A69B;
extern const uint32_t g_rgctx_AndroidJavaObject_FromJavaArrayDeleteLocalRef_TisReturnType_tB840B385B71FF8E350FF799B5A7DF58E03C1A69B_m73B01F92C85FB777B0F58ECB5A1AF6A057832B1D;
extern const uint32_t g_rgctx_AndroidJNIHelper_GetFieldID_TisFieldType_t31FF3BC94504224433BABB9DAD6CE4032EA80634_m323B9856DC209DF10953B764C89892283C6AE581;
extern const uint32_t g_rgctx_AndroidJavaObject__GetStatic_TisFieldType_t31FF3BC94504224433BABB9DAD6CE4032EA80634_mBB852ECC557259281593B962B4B61886216BC1AA;
extern const uint32_t g_rgctx_FieldType_t31FF3BC94504224433BABB9DAD6CE4032EA80634;
extern const uint32_t g_rgctx_FieldType_t4130D50F8DF6CBFB2A17DDF9CA62DF84750A5FF3;
extern const uint32_t g_rgctx_FieldType_t4130D50F8DF6CBFB2A17DDF9CA62DF84750A5FF3;
extern const uint32_t g_rgctx_AndroidJavaObject_FromJavaArrayDeleteLocalRef_TisFieldType_t4130D50F8DF6CBFB2A17DDF9CA62DF84750A5FF3_mF38D082B9A0FF10F6490E1369ACCC538C66D86C3;
extern const uint32_t g_rgctx_AndroidJNIHelper_GetFieldID_TisFieldType_t0374C27CAE86F7DA1E32463BA4A51CE6B68D02E1_mB578307426A086429411417C9DC1C11863EE3FEC;
extern const uint32_t g_rgctx_FieldType_t0374C27CAE86F7DA1E32463BA4A51CE6B68D02E1;
extern const uint32_t g_rgctx_AndroidJavaObject__SetStatic_TisFieldType_t0374C27CAE86F7DA1E32463BA4A51CE6B68D02E1_m9E04FE586EA534022B733443447CD79B17CEC72E;
extern const uint32_t g_rgctx_FieldType_tCE4037CFD91325CEB0ECC8FFA61A181595DF67F7;
extern const uint32_t g_rgctx_FieldType_tCE4037CFD91325CEB0ECC8FFA61A181595DF67F7;
extern const uint32_t g_rgctx_ReturnType_t7F4379E8D6E3B3545F3D77660B8E3F6DA1DC4DB9;
extern const uint32_t g_rgctx_AndroidJNIHelper_ConvertFromJNIArray_TisReturnType_t7F4379E8D6E3B3545F3D77660B8E3F6DA1DC4DB9_mEE171E15AE5E0AD6E114212C9E6F2EF42DEB024C;
extern const uint32_t g_rgctx_ArrayType_t15B09198CBBCA055D6DA2DC75D66B572B87D0FBF;
extern const uint32_t g_rgctx_ArrayType_t15B09198CBBCA055D6DA2DC75D66B572B87D0FBF;
extern const uint32_t g_rgctx__AndroidJNIHelper_GetSignature_TisReturnType_tA157ECD42EBFB71C50CF7B157BCCB1CDBF4A0B6A_m1DE78CA1017A9BF3BEE85712A53DFEBE683DC87F;
extern const uint32_t g_rgctx_ReturnType_tA4A9F806902AD20F830C4E904C1531989B535F95;
extern const uint32_t g_rgctx_ReturnType_t26D5E94BFCB8FB3CF5FF8C82163A3BE8EACEA9A4;
static const Il2CppRGCTXDefinition s_rgctxValues[93] = 
{
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__AndroidJNIHelper_ConvertFromJNIArray_TisArrayType_t33FA9A7F66F041B4E2878FF619DA2A8FCDD39085_m2DE197CE4F8061C7658B6308D28812D6AE662FE5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ArrayType_t33FA9A7F66F041B4E2878FF619DA2A8FCDD39085 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__AndroidJNIHelper_GetMethodID_TisReturnType_tD2A6EF0F4156D493768CADDC35C177D5FF55C4DE_m7AC36AC0446E8D34546FE4D002F9C8B56D25A8B6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__AndroidJNIHelper_GetFieldID_TisFieldType_tC112CF857F2F9A16A01117BD8DE4B420B806AB6D_mFA7B428C4344302ADCD1670CEB890B397DAD9227 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__AndroidJNIHelper_GetSignature_TisReturnType_t9A95B9FC093A340C4D5AEABCB66770F89AE0048D_mE373542209BEBEDCBECCAB5C5B944D0778370DEC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t16CB0CB1C7AC7DC247B530183CDF43ACF7838729 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_IsCreated_mB57B6B3595999F0F78DC5640BA825CA8FBAF563E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t16CB0CB1C7AC7DC247B530183CDF43ACF7838729 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Length_m865315721EAA672471E22BDB976489499169BEA9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArrayUnsafeUtility_GetUnsafePtr_TisT_t96DC1C87A94E8E20D42E25A7ADDFD198F460AEF9_mA379B333C68D94BB6783639230F4213CCFC0CFDA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t0DEDE16EA341DF331CA57F6EE535A595111735D3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArrayUnsafeUtility_ConvertExistingDataToNativeArray_TisT_t95BE45B46464B33538EF221E8410FB8747D94A8A_mC6DC23FD7873700310ACE1D943D2CDAC368F812C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tD127995738FFC823AB95C22AB082E5E8100A89FA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t51E5677E8B68A84E5D8B8D93D4649D9FFE54EAF4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t125EC412AD4EAF9F540D9FEE4FBE333887757BD3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t98C49705DF539DAE36E9273D717504910A8913CB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject__Get_TisFieldType_tE541E61DC1EE486A3DDC10DCA2A2DD9A2A3BADE8_m06134DE4CC6EA8E3189542B792E94123E91E03C6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FieldType_tE541E61DC1EE486A3DDC10DCA2A2DD9A2A3BADE8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject__Get_TisFieldType_t3D88A0B7A2BB1C07E18A9AA7AB32582B0FB37337_mA93D48F3759004A792161BF419A99D9FD777CD43 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FieldType_t3D88A0B7A2BB1C07E18A9AA7AB32582B0FB37337 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FieldType_t5C6F84F3CFFB0874A4DA0D1C58053A25B835551E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject__Set_TisFieldType_t5C6F84F3CFFB0874A4DA0D1C58053A25B835551E_m462146FCC7D2B3F83FF6E8999553D33E4959F5BE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FieldType_tD083F29D6E32E63E26B83C297746967559FB5289 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject__Set_TisFieldType_tD083F29D6E32E63E26B83C297746967559FB5289_mDE6A8DCAE2CB50FC2B805F1D4DE98761DFEB83D9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject__GetStatic_TisFieldType_tBB418E296327456981AAF34C2BEB510AEC3C4E4D_m287CA0417FCCBDC7C54BA521F00661EE781AE83E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FieldType_tBB418E296327456981AAF34C2BEB510AEC3C4E4D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject__GetStatic_TisFieldType_t4E8B4431DA3B0620A6961D0830B049CE0FA25BB3_m05D4249781BCDEAA2C845A1F9D487BBE529C7F65 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FieldType_t4E8B4431DA3B0620A6961D0830B049CE0FA25BB3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FieldType_t6076CC06F19BCC0301AE8C734F26CA429D2DB469 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject__SetStatic_TisFieldType_t6076CC06F19BCC0301AE8C734F26CA429D2DB469_m63581F10344537651B9090C50B7BAFA8B9CDB1A3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FieldType_tD9FB37412CCB11866A06B8D683ACBBD34ED06C6B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject__SetStatic_TisFieldType_tD9FB37412CCB11866A06B8D683ACBBD34ED06C6B_m6D1D734C7ECA5B4E0F1707990715B5F6E199F51F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t2D0810F438CE188B9FDA75ED2E8B60C28FD63BE6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject__Call_TisReturnType_t6A981EFC55AACA8DB2914A6B9C24AF2C1F0D86E3_mBA92180AE68E07DA29AB2ED05D52AA22E1ECEC52 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ReturnType_t6A981EFC55AACA8DB2914A6B9C24AF2C1F0D86E3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tF1AED0F0B8DCF565C07B4BF3D77EFB4814B44AC0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject__Call_TisReturnType_tE77199CFC11B9237A747C8E23BCDA97324CD4769_m939ABCC227D3F47C42038223BDDC8143684EFEF3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ReturnType_tE77199CFC11B9237A747C8E23BCDA97324CD4769 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject__Call_TisReturnType_t7C9CEFF53F7F785E3B0A2AA52BF0599DB9E4C7A7_m53C8581E9FA8AB194F9919FD0B5F910D4D0EDEDF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ReturnType_t7C9CEFF53F7F785E3B0A2AA52BF0599DB9E4C7A7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject__Call_TisReturnType_t7ECE3320BEFB3505409302EC3AA6B828AE7E1DB5_m370E365D1367373DBFCFAC4418C18118A8825073 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ReturnType_t7ECE3320BEFB3505409302EC3AA6B828AE7E1DB5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t3553B3A68B7CC38DCA3828A5BB17E68D7A256089 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject__CallStatic_TisReturnType_t41223B870DEBD9DC66C7F3F6FDDF2CF6D061E4EB_mA272E4A1258A5DB68113DF8B90DF5B5B3A724832 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ReturnType_t41223B870DEBD9DC66C7F3F6FDDF2CF6D061E4EB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t57FCD3B7614A641CACF7B0823E2458C79927EC24 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject__CallStatic_TisReturnType_t3F35C7C5E6E91ECEB1E553CA45292DBBEC6E5501_m6B6FF1D17E7D4CA7A348601815BC5E194E1215F4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ReturnType_t3F35C7C5E6E91ECEB1E553CA45292DBBEC6E5501 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject__CallStatic_TisReturnType_t94E13999E45FF70AA5DA5E427955FC4E439412B2_m7F6D9361C675DD63CF53A9BE7588D79625F85B4D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ReturnType_t94E13999E45FF70AA5DA5E427955FC4E439412B2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject__CallStatic_TisReturnType_t955BFF9AB851C80203957B433FC00E2046696241_mBD7D20A96A02F1EEC83A23C3065874556A832801 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ReturnType_t955BFF9AB851C80203957B433FC00E2046696241 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJNIHelper_GetMethodID_TisReturnType_t0FD1385ACD92B5652F803E183304929EDB7632D9_mAB39D5C2ACF4BF7098D1D76F31302A29A45D3825 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject__Call_TisReturnType_t0FD1385ACD92B5652F803E183304929EDB7632D9_m8AC46895CB418A0C6E533D3B85F0A7E215C5DF13 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ReturnType_t0FD1385ACD92B5652F803E183304929EDB7632D9 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_ReturnType_t79ECD6DB4ABC339D39F04EE50C832B65A84F5147 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ReturnType_t79ECD6DB4ABC339D39F04EE50C832B65A84F5147 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject_FromJavaArrayDeleteLocalRef_TisReturnType_t79ECD6DB4ABC339D39F04EE50C832B65A84F5147_mC821623012C4DEC54879B5ED4EBDE68072706E25 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJNIHelper_GetFieldID_TisFieldType_tBD81DD51AB3076BC0134BD255EBBAC34E341C535_m715A2F9AB6249CE2205B84D8BBABD111EB27AFA6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject__Get_TisFieldType_tBD81DD51AB3076BC0134BD255EBBAC34E341C535_mB880B82C1375741C2F32BADCF42CE43A22630BBA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FieldType_tBD81DD51AB3076BC0134BD255EBBAC34E341C535 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_FieldType_t1266B1AF802837275B234C725395054CAC857D29 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FieldType_t1266B1AF802837275B234C725395054CAC857D29 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject_FromJavaArrayDeleteLocalRef_TisFieldType_t1266B1AF802837275B234C725395054CAC857D29_m39A16BD90DEEAA423B02705847D4672CEDDAC80B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJNIHelper_GetFieldID_TisFieldType_t2F905C7C598CE04C35E1297452F5AE8BFD208DAE_mFB00C8B4BED9EBCFA629A59166C7EFC55D5D4085 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FieldType_t2F905C7C598CE04C35E1297452F5AE8BFD208DAE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject__Set_TisFieldType_t2F905C7C598CE04C35E1297452F5AE8BFD208DAE_mB60F838F278F88BFAEC883D8204F98BE4D07414A },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_FieldType_tC72607BAF758371FA7A7DDA2D0873E433EE11D19 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FieldType_tC72607BAF758371FA7A7DDA2D0873E433EE11D19 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJNIHelper_GetMethodID_TisReturnType_t2DAB0875DF34A21B532F695F9B7329A0B5BBCB27_m872C06BDFAA7CC5369EABC3850D99E950C4E9323 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject__CallStatic_TisReturnType_t2DAB0875DF34A21B532F695F9B7329A0B5BBCB27_m48CE4CF2840315B19CD807FEB9B9901A9B978556 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ReturnType_t2DAB0875DF34A21B532F695F9B7329A0B5BBCB27 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_ReturnType_tB840B385B71FF8E350FF799B5A7DF58E03C1A69B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ReturnType_tB840B385B71FF8E350FF799B5A7DF58E03C1A69B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject_FromJavaArrayDeleteLocalRef_TisReturnType_tB840B385B71FF8E350FF799B5A7DF58E03C1A69B_m73B01F92C85FB777B0F58ECB5A1AF6A057832B1D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJNIHelper_GetFieldID_TisFieldType_t31FF3BC94504224433BABB9DAD6CE4032EA80634_m323B9856DC209DF10953B764C89892283C6AE581 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject__GetStatic_TisFieldType_t31FF3BC94504224433BABB9DAD6CE4032EA80634_mBB852ECC557259281593B962B4B61886216BC1AA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FieldType_t31FF3BC94504224433BABB9DAD6CE4032EA80634 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_FieldType_t4130D50F8DF6CBFB2A17DDF9CA62DF84750A5FF3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FieldType_t4130D50F8DF6CBFB2A17DDF9CA62DF84750A5FF3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject_FromJavaArrayDeleteLocalRef_TisFieldType_t4130D50F8DF6CBFB2A17DDF9CA62DF84750A5FF3_mF38D082B9A0FF10F6490E1369ACCC538C66D86C3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJNIHelper_GetFieldID_TisFieldType_t0374C27CAE86F7DA1E32463BA4A51CE6B68D02E1_mB578307426A086429411417C9DC1C11863EE3FEC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FieldType_t0374C27CAE86F7DA1E32463BA4A51CE6B68D02E1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject__SetStatic_TisFieldType_t0374C27CAE86F7DA1E32463BA4A51CE6B68D02E1_m9E04FE586EA534022B733443447CD79B17CEC72E },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_FieldType_tCE4037CFD91325CEB0ECC8FFA61A181595DF67F7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FieldType_tCE4037CFD91325CEB0ECC8FFA61A181595DF67F7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ReturnType_t7F4379E8D6E3B3545F3D77660B8E3F6DA1DC4DB9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJNIHelper_ConvertFromJNIArray_TisReturnType_t7F4379E8D6E3B3545F3D77660B8E3F6DA1DC4DB9_mEE171E15AE5E0AD6E114212C9E6F2EF42DEB024C },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_ArrayType_t15B09198CBBCA055D6DA2DC75D66B572B87D0FBF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ArrayType_t15B09198CBBCA055D6DA2DC75D66B572B87D0FBF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__AndroidJNIHelper_GetSignature_TisReturnType_tA157ECD42EBFB71C50CF7B157BCCB1CDBF4A0B6A_m1DE78CA1017A9BF3BEE85712A53DFEBE683DC87F },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_ReturnType_tA4A9F806902AD20F830C4E904C1531989B535F95 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_ReturnType_t26D5E94BFCB8FB3CF5FF8C82163A3BE8EACEA9A4 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_AndroidJNIModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_AndroidJNIModule_CodeGenModule = 
{
	"UnityEngine.AndroidJNIModule.dll",
	529,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	43,
	s_rgctxIndices,
	93,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
