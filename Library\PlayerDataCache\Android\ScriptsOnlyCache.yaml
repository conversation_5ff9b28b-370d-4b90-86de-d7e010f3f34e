ScriptsOnlyBuild:
  usedScripts:
    Assembly-CSharp.dll:
    - ARCursor
    - FaceCamera
    - NeverSleepCode
    - PressStartContollerNew
    - TimelimeControllerNew
    DOTween.dll:
    - DG.Tweening.Core.DOTweenSettings
    Unity.InputSystem.dll:
    - UnityEngine.InputSystem.InputActionAsset
    - UnityEngine.InputSystem.InputActionReference
    - UnityEngine.InputSystem.InputSettings
    - UnityEngine.InputSystem.InputSystemObject
    - UnityEngine.InputSystem.RemoteInputPlayerConnection
    - UnityEngine.InputSystem.UI.InputSystemUIInputModule
    - UnityEngine.InputSystem.XR.TrackedPoseDriver
    Unity.Rider.Editor.dll:
    - Packages.Rider.Editor.UnitTesting.CallbackData
    Unity.TextMeshPro:
    - TMPro.TMP_FontAsset
    - TMPro.TMP_Settings
    - TMPro.TMP_SpriteAsset
    - TMPro.TMP_StyleSheet
    Unity.TextMeshPro.dll:
    - TMPro.TMP_FontAsset
    - TMPro.TMP_Settings
    - TMPro.TMP_SpriteAsset
    - TMPro.TMP_StyleSheet
    - TMPro.TextMeshProUGUI
    Unity.Timeline.Editor.dll:
    - TimelinePreferences
    - UnityEditor.Timeline.SequenceHierarchy
    - UnityEditor.Timeline.TimelineWindow
    Unity.Timeline.dll:
    - UnityEngine.Timeline.ActivationPlayableAsset
    - UnityEngine.Timeline.ActivationTrack
    - UnityEngine.Timeline.AnimationTrack
    - UnityEngine.Timeline.AudioPlayableAsset
    - UnityEngine.Timeline.AudioTrack
    - UnityEngine.Timeline.TimelineAsset
    Unity.XR.ARCore.Editor.dll:
    - UnityEditor.XR.ARCore.ARCoreSettings
    Unity.XR.ARCore.dll:
    - UnityEngine.XR.ARCore.ARCoreLoader
    Unity.XR.ARFoundation.dll:
    - UnityEngine.XR.ARFoundation.ARCameraBackground
    - UnityEngine.XR.ARFoundation.ARCameraManager
    - UnityEngine.XR.ARFoundation.ARInputManager
    - UnityEngine.XR.ARFoundation.ARPlane
    - UnityEngine.XR.ARFoundation.ARPlaneManager
    - UnityEngine.XR.ARFoundation.ARPlaneMeshVisualizer
    - UnityEngine.XR.ARFoundation.ARRaycastManager
    - UnityEngine.XR.ARFoundation.ARSession
    Unity.XR.ARKit.Editor.dll:
    - UnityEditor.XR.ARKit.ARKitSettings
    Unity.XR.ARKit.dll:
    - UnityEngine.XR.ARKit.ARKitLoader
    Unity.XR.CoreUtils.dll:
    - Unity.XR.CoreUtils.XROrigin
    Unity.XR.Management.Editor.dll:
    - UnityEditor.XR.Management.XRGeneralSettingsPerBuildTarget
    Unity.XR.Management.dll:
    - UnityEngine.XR.Management.XRGeneralSettings
    - UnityEngine.XR.Management.XRManagerSettings
    Unity.XR.Simulation.Editor.dll:
    - UnityEditor.XR.Simulation.SimulationEnvironmentAssetsManager
    - UnityEditor.XR.Simulation.XREnvironmentViewManager
    - UnityEditor.XR.Simulation.XRSimulationSettings
    Unity.XR.Simulation.dll:
    - UnityEngine.XR.Simulation.SimulatedBoundedPlane
    - UnityEngine.XR.Simulation.SimulatedEnvironmentProbe
    - UnityEngine.XR.Simulation.SimulatedMeshClassification
    - UnityEngine.XR.Simulation.SimulatedTrackedImage
    - UnityEngine.XR.Simulation.SimulationEnvironment
    - UnityEngine.XR.Simulation.XRSimulationPreferences
    - UnityEngine.XR.Simulation.XRSimulationRuntimeSettings
    UnityEditor.Graphs.dll:
    - UnityEditor.Graphs.AnimationBlendTree.Graph
    - UnityEditor.Graphs.AnimationBlendTree.GraphGUI
    - UnityEditor.Graphs.AnimationStateMachine.AnyStateNode
    - UnityEditor.Graphs.AnimationStateMachine.Graph
    - UnityEditor.Graphs.AnimationStateMachine.GraphGUI
    - UnityEditor.Graphs.AnimationStateMachine.StateNode
    - UnityEditor.Graphs.AnimatorControllerTool
    UnityEditor.TestRunner.dll:
    - UnityEditor.TestTools.TestRunner.Api.CallbacksHolder
    - UnityEditor.TestTools.TestRunner.TestListCacheData
    - UnityEditor.TestTools.TestRunner.TestRun.TestJobDataHolder
    UnityEngine.UI.dll:
    - UnityEngine.EventSystems.EventSystem
    - UnityEngine.UI.Button
    - UnityEngine.UI.CanvasScaler
    - UnityEngine.UI.GraphicRaycaster
    - UnityEngine.UI.Image
  serializedClasses:
    DOTween:
    - DG.Tweening.Core.DOTweenSettings/ModulesSetup
    - DG.Tweening.Core.DOTweenSettings/SafeModeOptions
    Unity.InputSystem:
    - UnityEngine.InputSystem.InputAction
    - UnityEngine.InputSystem.InputActionMap
    - UnityEngine.InputSystem.InputActionProperty
    - UnityEngine.InputSystem.InputBinding
    - UnityEngine.InputSystem.InputControlScheme
    - UnityEngine.InputSystem.InputControlScheme/DeviceRequirement
    Unity.TextMeshPro:
    - TMPro.FaceInfo_Legacy
    - TMPro.FontAssetCreationSettings
    - TMPro.KerningTable
    - TMPro.TMP_Character
    - TMPro.TMP_FontFeatureTable
    - TMPro.TMP_FontWeightPair
    - TMPro.TMP_GlyphAdjustmentRecord
    - TMPro.TMP_GlyphPairAdjustmentRecord
    - TMPro.TMP_GlyphValueRecord
    - TMPro.TMP_Sprite
    - TMPro.TMP_SpriteCharacter
    - TMPro.TMP_SpriteGlyph
    - TMPro.TMP_Style
    - TMPro.VertexGradient
    Unity.Timeline:
    - UnityEngine.Timeline.AudioClipProperties
    - UnityEngine.Timeline.AudioMixerProperties
    - UnityEngine.Timeline.MarkerList
    - UnityEngine.Timeline.TimelineAsset/EditorSettings
    - UnityEngine.Timeline.TimelineClip
    UnityEngine.CoreModule:
    - UnityEngine.Events.ArgumentCache
    - UnityEngine.Events.PersistentCallGroup
    - UnityEngine.Events.PersistentListenerMode
    UnityEngine.TextCoreFontEngineModule:
    - UnityEngine.TextCore.FaceInfo
    - UnityEngine.TextCore.Glyph
    - UnityEngine.TextCore.GlyphMetrics
    - UnityEngine.TextCore.GlyphRect
    UnityEngine.UI:
    - UnityEngine.UI.AnimationTriggers
    - UnityEngine.UI.Button/ButtonClickedEvent
    - UnityEngine.UI.ColorBlock
    - UnityEngine.UI.MaskableGraphic/CullStateChangedEvent
    - UnityEngine.UI.Navigation
    - UnityEngine.UI.SpriteState
  methodsToPreserve:
  - assembly: Assembly-CSharp
    fullTypeName: PressStartContollerNew
    methodName: PressPlayButton
  - assembly: Assembly-CSharp
    fullTypeName: PressStartContollerNew
    methodName: PressReplayButton
  sceneClasses:
    Assets/Scenes/MainForTimeline.unity:
    - Class: 1
      Script: {instanceID: 0}
    - Class: 4
      Script: {instanceID: 0}
    - Class: 20
      Script: {instanceID: 0}
    - Class: 21
      Script: {instanceID: 0}
    - Class: 23
      Script: {instanceID: 0}
    - Class: 28
      Script: {instanceID: 0}
    - Class: 33
      Script: {instanceID: 0}
    - Class: 43
      Script: {instanceID: 0}
    - Class: 48
      Script: {instanceID: 0}
    - Class: 49
      Script: {instanceID: 0}
    - Class: 64
      Script: {instanceID: 0}
    - Class: 74
      Script: {instanceID: 0}
    - Class: 82
      Script: {instanceID: 0}
    - Class: 83
      Script: {instanceID: 0}
    - Class: 89
      Script: {instanceID: 0}
    - Class: 91
      Script: {instanceID: 0}
    - Class: 95
      Script: {instanceID: 0}
    - Class: 104
      Script: {instanceID: 0}
    - Class: 108
      Script: {instanceID: 0}
    - Class: 114
      Script: {instanceID: 16720}
    - Class: 114
      Script: {instanceID: 16746}
    - Class: 114
      Script: {instanceID: 16782}
    - Class: 114
      Script: {instanceID: 16900}
    - Class: 114
      Script: {instanceID: 17602}
    - Class: 114
      Script: {instanceID: 18212}
    - Class: 114
      Script: {instanceID: 18230}
    - Class: 114
      Script: {instanceID: 18234}
    - Class: 114
      Script: {instanceID: 18488}
    - Class: 114
      Script: {instanceID: 18896}
    - Class: 114
      Script: {instanceID: 19142}
    - Class: 114
      Script: {instanceID: 19384}
    - Class: 114
      Script: {instanceID: 19484}
    - Class: 114
      Script: {instanceID: 19548}
    - Class: 114
      Script: {instanceID: 19674}
    - Class: 114
      Script: {instanceID: 19760}
    - Class: 114
      Script: {instanceID: 19780}
    - Class: 114
      Script: {instanceID: 19816}
    - Class: 114
      Script: {instanceID: 19874}
    - Class: 114
      Script: {instanceID: 20262}
    - Class: 114
      Script: {instanceID: 20340}
    - Class: 114
      Script: {instanceID: 20706}
    - Class: 114
      Script: {instanceID: 21096}
    - Class: 114
      Script: {instanceID: 21192}
    - Class: 114
      Script: {instanceID: 21718}
    - Class: 114
      Script: {instanceID: 22566}
    - Class: 114
      Script: {instanceID: 22756}
    - Class: 114
      Script: {instanceID: 23784}
    - Class: 114
      Script: {instanceID: 24170}
    - Class: 114
      Script: {instanceID: 24434}
    - Class: 114
      Script: {instanceID: 24788}
    - Class: 114
      Script: {instanceID: 24814}
    - Class: 114
      Script: {instanceID: 25156}
    - Class: 114
      Script: {instanceID: 25332}
    - Class: 114
      Script: {instanceID: 25522}
    - Class: 115
      Script: {instanceID: 0}
    - Class: 120
      Script: {instanceID: 0}
    - Class: 128
      Script: {instanceID: 0}
    - Class: 137
      Script: {instanceID: 0}
    - Class: 157
      Script: {instanceID: 0}
    - Class: 196
      Script: {instanceID: 0}
    - Class: 212
      Script: {instanceID: 0}
    - Class: 213
      Script: {instanceID: 0}
    - Class: 222
      Script: {instanceID: 0}
    - Class: 223
      Script: {instanceID: 0}
    - Class: 224
      Script: {instanceID: 0}
    - Class: 320
      Script: {instanceID: 0}
  scriptHashData:
  - hash:
      serializedVersion: 2
      Hash: 67f2e533bbfe68206e21fd8cf233561c
    assemblyName: UnityEngine.SpatialTracking
    namespaceName: UnityEngine.SpatialTracking
    className: TrackedPoseDriver
  - hash:
      serializedVersion: 2
      Hash: 540882762544409cc4530e60db00e951
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: CanvasScaler
  - hash:
      serializedVersion: 2
      Hash: 55060a7bf226ac385ce19ebd450f199f
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UIElements
    className: PanelRaycaster
  - hash:
      serializedVersion: 2
      Hash: 618ecb40e054243112723145dc97c647
    assemblyName: Unity.XR.ARFoundation
    namespaceName: UnityEngine.XR.ARFoundation
    className: ARPlane
  - hash:
      serializedVersion: 2
      Hash: dd9dda1dfd39e605c49e72003342ef5d
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Image
  - hash:
      serializedVersion: 2
      Hash: 0f19765290d81f9e93eff7828a091d40
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem
    className: PlayerInput
  - hash:
      serializedVersion: 2
      Hash: 43359b9981659dd11a035003a415a66d
    assemblyName: Unity.XR.ARFoundation.VisualScripting
    namespaceName: UnityEngine.XR.ARFoundation.VisualScripting
    className: ARHumanBodyManagerListener
  - hash:
      serializedVersion: 2
      Hash: 40bdadcd1cc14ab9d74347f31c1d4dd2
    assemblyName: UnityEngine.XR.LegacyInputHelpers
    namespaceName: UnityEngine.XR.LegacyInputHelpers
    className: ArmModel
  - hash:
      serializedVersion: 2
      Hash: f61f64d765fc51085902c242d52fb1fe
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: QREncodeTest
  - hash:
      serializedVersion: 2
      Hash: e46ef85fefbd22ad17e337d5e8704e26
    assemblyName: Unity.XR.ARFoundation
    namespaceName: UnityEngine.XR.ARFoundation
    className: ARInputManager
  - hash:
      serializedVersion: 2
      Hash: f45600bcc886206dd55a3208474aad61
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnScrollbarValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: a3901985eb076c1c9b7562fca82ca3e0
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: SignalAsset
  - hash:
      serializedVersion: 2
      Hash: 8dff8e9853297d341afa0c11f9091b59
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: LayoutElement
  - hash:
      serializedVersion: 2
      Hash: 00bbd9db8634496d079d17711b2ff7c4
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Selectable
  - hash:
      serializedVersion: 2
      Hash: 157d3cf40178d244cd11ae1c9791ead7
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.HID.Editor
    className: HIDDescriptorWindow
  - hash:
      serializedVersion: 2
      Hash: ed8e62f07ec878233f5493a335003493
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.UI
    className: VirtualMouseInput
  - hash:
      serializedVersion: 2
      Hash: 8703b6ae2b0eefd9dafbc44d8d333fb1
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Shadow
  - hash:
      serializedVersion: 2
      Hash: f778e23b6ff26b8de4721be0eb1fa240
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionStayMessageListener
  - hash:
      serializedVersion: 2
      Hash: f854af586011dd194574f89546642e29
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseExitMessageListener
  - hash:
      serializedVersion: 2
      Hash: 136f80fb6429f04c776ade9abcf4a740
    assemblyName: Unity.XR.ARFoundation
    namespaceName: UnityEngine.XR.ARFoundation
    className: ARPointCloudMeshVisualizer
  - hash:
      serializedVersion: 2
      Hash: b12640dfdb5966813e9dd838893e6c4a
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerEnterMessageListener
  - hash:
      serializedVersion: 2
      Hash: 13f2fac9f3c6f3efa9575deacd0f768d
    assemblyName: Unity.XR.ARFoundation
    namespaceName: UnityEngine.XR.ARFoundation
    className: ARHumanBodyManager
  - hash:
      serializedVersion: 2
      Hash: 0c6de72eaf8c1070da824268c4bfad9d
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_StyleSheet
  - hash:
      serializedVersion: 2
      Hash: ad49e20f31ed572e586aa8bbaafb591b
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnDropMessageListener
  - hash:
      serializedVersion: 2
      Hash: 1bc6ff8d02d2d68019aa6a028f8d409d
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerUpMessageListener
  - hash:
      serializedVersion: 2
      Hash: b40f8ffcaa6b71545b8d6ff04b849814
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: AdvancedDropdownWindow
  - hash:
      serializedVersion: 2
      Hash: 7e93247d600e115dac0cdd880de94e99
    assemblyName: UnityEngine.TestRunner
    namespaceName: UnityEngine.TestTools.TestRunner
    className: PlaymodeTestsController
  - hash:
      serializedVersion: 2
      Hash: 83cfda82082c233d96cc677c19f57e07
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnJointBreakMessageListener
  - hash:
      serializedVersion: 2
      Hash: 1ab136900c0edaeeb49424c852595030
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: RawImage
  - hash:
      serializedVersion: 2
      Hash: f682427c7e918d86eb99e878b9ed6d1f
    assemblyName: Unity.XR.ARFoundation
    namespaceName: UnityEngine.XR.ARFoundation
    className: ARParticipantManager
  - hash:
      serializedVersion: 2
      Hash: 1321993db8249e1e05e1e0492b2bbdc3
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnBecameVisibleMessageListener
  - hash:
      serializedVersion: 2
      Hash: 963c15793a023cb75c6ab6c2968822e9
    assemblyName: Unity.XR.ARFoundation
    namespaceName: UnityEngine.XR.ARFoundation
    className: ARFace
  - hash:
      serializedVersion: 2
      Hash: e987953190eb4c12cf19df790566dc2c
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: EventSystem
  - hash:
      serializedVersion: 2
      Hash: 52db48e3be0a2b4f8b06f918fd1ebfbc
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_Dropdown
  - hash:
      serializedVersion: 2
      Hash: f295412bc00b58b9095a71e9764cc886
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: AudioTrack
  - hash:
      serializedVersion: 2
      Hash: 04b6685b6f724c0d6024e6b6f96c8202
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: TimelineAsset
  - hash:
      serializedVersion: 2
      Hash: ada873da50974d1e7ce247ffb296b0f3
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionEnterMessageListener
  - hash:
      serializedVersion: 2
      Hash: 7e12302f66e3c8ed5bcfea4ce65fccea
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityMessageListener
  - hash:
      serializedVersion: 2
      Hash: 45a3a99f1ccfc7ed828e777c5b9b3d4e
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: AnimController
  - hash:
      serializedVersion: 2
      Hash: be013717eaccd24cc99fc5694926ac90
    assemblyName: Unity.XR.ARSubsystems
    namespaceName: UnityEngine.XR.ARSubsystems
    className: XRReferenceObjectLibrary
  - hash:
      serializedVersion: 2
      Hash: fce9b835d1e8b08e7ecea8da19f21986
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: CoroutineRunner
  - hash:
      serializedVersion: 2
      Hash: 6ddf94363c6ce4b02d9fdd51290cb0f9
    assemblyName: UnityEngine.TestRunner
    namespaceName: UnityEngine.TestRunner.Utils
    className: TestRunCallbackListener
  - hash:
      serializedVersion: 2
      Hash: 5a4aa2850fe11587f47e34c270c7fd22
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: GraphicRaycaster
  - hash:
      serializedVersion: 2
      Hash: 3b5263a98bcc71d9a9d1d78a31b34ced
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_SelectionCaret
  - hash:
      serializedVersion: 2
      Hash: 8c03d6ee69f5dc5ed9b56c1be75f5b62
    assemblyName: Unity.XR.CoreUtils
    namespaceName: Unity.XR.CoreUtils
    className: OnDestroyNotifier
  - hash:
      serializedVersion: 2
      Hash: 28fd3d045acc780719a17f02073eb448
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: InputField
  - hash:
      serializedVersion: 2
      Hash: 8e6dc3d79712ca4934f6d50c80f010b1
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_SpriteAnimator
  - hash:
      serializedVersion: 2
      Hash: 6d394de691f6d0187f61c08889353d0e
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnButtonClickMessageListener
  - hash:
      serializedVersion: 2
      Hash: bf40c7a9dc15bc219687c9838aa9b41f
    assemblyName: UnityEngine.TestRunner
    namespaceName: UnityEngine.TestTools.TestRunner.Callbacks
    className: TestResultRendererCallback
  - hash:
      serializedVersion: 2
      Hash: e23d2210701e34e6850023cabe554c1f
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnInputFieldEndEditMessageListener
  - hash:
      serializedVersion: 2
      Hash: cd9f713a5accf004a8abc251aac5fb7d
    assemblyName: Unity.XR.ARFoundation
    namespaceName: UnityEngine.XR.ARFoundation
    className: AREnvironmentProbeManager
  - hash:
      serializedVersion: 2
      Hash: b98001682cba83786775c9e415b89a61
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnDropdownValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: 3bddb15053118dcd72c355335d7af7f3
    assemblyName: Unity.XR.ARFoundation
    namespaceName: UnityEngine.XR.ARFoundation
    className: AROcclusionManager
  - hash:
      serializedVersion: 2
      Hash: b838cbada0b03f1cfbaebc8e124f4f39
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.OnScreen
    className: OnScreenButton
  - hash:
      serializedVersion: 2
      Hash: a1ac4c50e55874de16eee3db71aa9784
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: ToggleGroup
  - hash:
      serializedVersion: 2
      Hash: 915204dac8acad99f076d6e72ada0a0f
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Button
  - hash:
      serializedVersion: 2
      Hash: 942cafc8a9011951514d173a77b7b089
    assemblyName: Unity.XR.ARFoundation
    namespaceName: UnityEngine.XR.ARFoundation
    className: ARDebugMenu
  - hash:
      serializedVersion: 2
      Hash: 2f196f403872bc33baecfe5fbd25c368
    assemblyName: Unity.XR.ARFoundation.VisualScripting
    namespaceName: UnityEngine.XR.ARFoundation.VisualScripting
    className: ARPlaneManagerListener
  - hash:
      serializedVersion: 2
      Hash: d90629714fa6b0145fcaffc87f5c6559
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnInputFieldValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: bb8d968ef4474e71aaad4a4dce279f90
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseDownMessageListener
  - hash:
      serializedVersion: 2
      Hash: b6307dfc54cca9ba44f47185c578b0ce
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseDragMessageListener
  - hash:
      serializedVersion: 2
      Hash: d7c8092511063e0dae8106bfc8ed63dd
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnScrollMessageListener
  - hash:
      serializedVersion: 2
      Hash: 715f7928b39881049ce8c46350cb8681
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTransformParentChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: 4c660d1fba3901cdb510fb1848c1fe4f
    assemblyName: Unity.XR.ARFoundation.VisualScripting
    namespaceName: UnityEngine.XR.ARFoundation.VisualScripting
    className: ARParticipantManagerListener
  - hash:
      serializedVersion: 2
      Hash: c5bb89ad5c39f682cd52dd0f08974c33
    assemblyName: Unity.XR.ARFoundation
    namespaceName: UnityEngine.XR.ARFoundation
    className: ARCameraBackground
  - hash:
      serializedVersion: 2
      Hash: d41ab9b5ea92474f9c0a6937a0607ce1
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: ContentSizeFitter
  - hash:
      serializedVersion: 2
      Hash: 86344100c716167fb86137c127ca9eee
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputActionImporterEditor
  - hash:
      serializedVersion: 2
      Hash: c6369b4cc3be620d6c9300e920bc3d52
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: VariablesAsset
  - hash:
      serializedVersion: 2
      Hash: 7bdff9e91edfd9600f92a185def7e34a
    assemblyName: Unity.VisualScripting.Flow
    namespaceName: Unity.VisualScripting
    className: ScriptMachine
  - hash:
      serializedVersion: 2
      Hash: 43ebd945b04bced376c791411cf5a289
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnSelectMessageListener
  - hash:
      serializedVersion: 2
      Hash: 5b8b7230ad968399f6726aa1269dcdcb
    assemblyName: UnityEngine.TestRunner
    namespaceName: UnityEngine.TestTools.TestRunner.Callbacks
    className: PlayerQuitHandler
  - hash:
      serializedVersion: 2
      Hash: 2a4bf9bfa4fc85b7c3fd76767f844af6
    assemblyName: DOTween.dll
    namespaceName: DG.Tweening.Core
    className: DOTweenComponent
  - hash:
      serializedVersion: 2
      Hash: 4f1dec93fa2a0b0e7b8b47518da31b1c
    assemblyName: DOTween.dll
    namespaceName: DG.Tweening.Core
    className: DOTweenSettings
  - hash:
      serializedVersion: 2
      Hash: cf32d98954e63b8e30e9334b152e9b8e
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: BaseInput
  - hash:
      serializedVersion: 2
      Hash: e4fd567ac15efb8b16f5ddf87bbc4c39
    assemblyName: Unity.XR.ARFoundation
    namespaceName: UnityEngine.XR.ARFoundation
    className: ARTrackedImage
  - hash:
      serializedVersion: 2
      Hash: e6f8333cc8a800e2866870a5ef7efc35
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnControllerColliderHitMessageListener
  - hash:
      serializedVersion: 2
      Hash: 6c01942c09c5da16765021323bc1a12b
    assemblyName: Unity.XR.ARFoundation.VisualScripting
    namespaceName: UnityEngine.XR.ARFoundation.VisualScripting
    className: ARPointCloudManagerListener
  - hash:
      serializedVersion: 2
      Hash: aaa7f6ceec18761a146044d8c31f185f
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ARCursor
  - hash:
      serializedVersion: 2
      Hash: 731480b83236435ed9fb94ca37cf5283
    assemblyName: Unity.XR.Management
    namespaceName: UnityEngine.XR.Management
    className: XRGeneralSettings
  - hash:
      serializedVersion: 2
      Hash: 960935b102b3064f4b924de16426e340
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TextMeshPro
  - hash:
      serializedVersion: 2
      Hash: b2d2a01a2a85d9ed12e80cb33a9a6044
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: AnimationTrack
  - hash:
      serializedVersion: 2
      Hash: 6217b1287eca7eed313cf0b864249a30
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionExit2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: a99dbe9637c5f15c386354cfdb880b12
    assemblyName: Unity.XR.ARFoundation
    namespaceName: UnityEngine.XR.ARFoundation
    className: DebugSlider
  - hash:
      serializedVersion: 2
      Hash: 0ed7dfcb5356681e6627b207891cb849
    assemblyName: Unity.XR.Management
    namespaceName: UnityEngine.XR.Management
    className: XRManagerSettings
  - hash:
      serializedVersion: 2
      Hash: 36011f0a95d05fc59acadcc3e436f7df
    assemblyName: Unity.XR.ARFoundation
    namespaceName: UnityEngine.XR.ARFoundation
    className: ARPlaneManager
  - hash:
      serializedVersion: 2
      Hash: 394fa6b8c9c897835232e3b8079f160f
    assemblyName: Unity.XR.ARFoundation.VisualScripting
    namespaceName: UnityEngine.XR.ARFoundation.VisualScripting
    className: ARTrackedImageManagerListener
  - hash:
      serializedVersion: 2
      Hash: 0a3f328f3ea906bbbcf794f4c27ee59d
    assemblyName: Unity.VisualScripting.Flow
    namespaceName: Unity.VisualScripting
    className: ScriptGraphAsset
  - hash:
      serializedVersion: 2
      Hash: 776903a262591f2540a3bf11e8543daf
    assemblyName: Unity.XR.ARFoundation
    namespaceName: UnityEngine.XR.ARFoundation
    className: ARSession
  - hash:
      serializedVersion: 2
      Hash: c295f30afa84e01403208fa0cae6cd3a
    assemblyName: Unity.XR.CoreUtils
    namespaceName: Unity.XR.CoreUtils.Datums
    className: StringDatum
  - hash:
      serializedVersion: 2
      Hash: 42284ecb06b05bfc619be3758fa5dd7a
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem
    className: InputActionAsset
  - hash:
      serializedVersion: 2
      Hash: fb79e95da8891fc7ac6c36ca6967af23
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: ActivationTrack
  - hash:
      serializedVersion: 2
      Hash: 3e4d800cee21c1250c5253c6acfbe524
    assemblyName: Unity.XR.ARCore
    namespaceName: UnityEngine.XR.ARCore
    className: ARCoreLoaderSettings
  - hash:
      serializedVersion: 2
      Hash: 552dca338f20933ce362ebbe559a6aa6
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerExitMessageListener
  - hash:
      serializedVersion: 2
      Hash: cbaf979d047774a0c18ce60cb0321b1b
    assemblyName: Unity.XR.CoreUtils
    namespaceName: Unity.XR.CoreUtils
    className: XROrigin
  - hash:
      serializedVersion: 2
      Hash: f5cea0409b1f4168b3d309e27cdcd78b
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RenderListenerUtility
  - hash:
      serializedVersion: 2
      Hash: 95426d463074947dcbffc797c44e779c
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.UI.Editor
    className: StandaloneInputModuleModuleEditor
  - hash:
      serializedVersion: 2
      Hash: 5835aa044b7ea3413cc6d9d819e95dd9
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputActionDebuggerWindow
  - hash:
      serializedVersion: 2
      Hash: b747f5d40674055dd70e18552f1b0447
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Mask
  - hash:
      serializedVersion: 2
      Hash: 164a37e0bc379311785dd54beac44331
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputDebuggerWindow
  - hash:
      serializedVersion: 2
      Hash: 32bfadf4ab14c1e4298d945ac7dbee8f
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputStateWindow
  - hash:
      serializedVersion: 2
      Hash: 0bc7ef1b7516f21320ec49bfd31eef2e
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.UI
    className: TrackedDeviceRaycaster
  - hash:
      serializedVersion: 2
      Hash: 4718916586b3818eb69bf65df61f0f3a
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionStay2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: 185e7bacfbbc5defefa609c70f7ff4ce
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_ColorGradient
  - hash:
      serializedVersion: 2
      Hash: 682dfc653b22feea53b38adb3bc66414
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: SignalReceiver
  - hash:
      serializedVersion: 2
      Hash: c78ed13f6b5a632d70d4f73f56ad81f2
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: VariablesSaver
  - hash:
      serializedVersion: 2
      Hash: 963e9236848bf8f326a3b6c79f6c7a63
    assemblyName: Unity.XR.ARFoundation
    namespaceName: UnityEngine.XR.ARFoundation
    className: ARRaycast
  - hash:
      serializedVersion: 2
      Hash: 7053e305cdc1eb560f4c3be49d3b5136
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: PhysicsRaycaster
  - hash:
      serializedVersion: 2
      Hash: e31ea17b921ee466a19650dc5fa9f3ce
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: NeverSleepCode
  - hash:
      serializedVersion: 2
      Hash: 042785b832afb73fd0585521aa7baf43
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: ControlPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: 1a680d9a08de79b0d2c779e78c9843b4
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: RectMask2D
  - hash:
      serializedVersion: 2
      Hash: c933b590b3badb5918fa01e73cd5cc0c
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_Settings
  - hash:
      serializedVersion: 2
      Hash: 0a589759540ac7dd7e8eb9f48c7ec9d5
    assemblyName: Unity.XR.ARFoundation
    namespaceName: UnityEngine.XR.ARFoundation
    className: ARHumanBody
  - hash:
      serializedVersion: 2
      Hash: ca7965796a6fc0e5baad4cb19f5b5ded
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnJointBreak2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: 29ab9dd3a847afc14e2b50f1cb769607
    assemblyName: Unity.XR.ARFoundation
    namespaceName: UnityEngine.XR.ARFoundation
    className: ARTrackedObjectManager
  - hash:
      serializedVersion: 2
      Hash: 7fe4cc923578d80b246471921934cdf2
    assemblyName: Unity.XR.ARFoundation
    namespaceName: UnityEngine.XR.ARFoundation
    className: ARPointCloudParticleVisualizer
  - hash:
      serializedVersion: 2
      Hash: 9ebc0a3016506a0cbb7e0306dfcb9497
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: DictionaryAsset
  - hash:
      serializedVersion: 2
      Hash: 3c9169fda20c63cfd6006bfc181838d3
    assemblyName: Unity.XR.ARSubsystems
    namespaceName: UnityEngine.XR.ARSubsystems
    className: XRReferenceImageLibrary
  - hash:
      serializedVersion: 2
      Hash: e7e95903ca66d58e1a7cbd71f824e16d
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_SubMeshUI
  - hash:
      serializedVersion: 2
      Hash: b600f260c8488b6664a096bf204ea07c
    assemblyName: Unity.XR.ARFoundation
    namespaceName: UnityEngine.XR.ARFoundation
    className: ARMeshManager
  - hash:
      serializedVersion: 2
      Hash: 215d2dc6ec6ea06728398ea39a103cb3
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.OnScreen
    className: OnScreenStick
  - hash:
      serializedVersion: 2
      Hash: 41cc7fffbed3f39916dde454cdcb7bd4
    assemblyName: Unity.XR.ARFoundation
    namespaceName: UnityEngine.XR.ARFoundation
    className: ARParticipant
  - hash:
      serializedVersion: 2
      Hash: 44a8a804622d104ac63dfd639931fa23
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: QRCodeContoller
  - hash:
      serializedVersion: 2
      Hash: 9078325c2a6f6a2d9612ade897511860
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_SpriteAsset
  - hash:
      serializedVersion: 2
      Hash: 96e7bcfd2d072992dab64ac44058b930
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCancelMessageListener
  - hash:
      serializedVersion: 2
      Hash: 5f7aadf2ee5f054ef03ca439f3012daa
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: GalleryController
  - hash:
      serializedVersion: 2
      Hash: 8787da68acbcc2f439d01ee75c474214
    assemblyName: Unity.XR.ARFoundation
    namespaceName: UnityEngine.XR.ARFoundation
    className: ARAnchor
  - hash:
      serializedVersion: 2
      Hash: ea1b87a3bfed09792e7720338241c7a0
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputDeviceDebuggerWindow
  - hash:
      serializedVersion: 2
      Hash: ff71db1a48de41d3a8a5ffdfae1ea607
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem
    className: InputSystemObject
  - hash:
      serializedVersion: 2
      Hash: 7e9cad7f71f9cc52f699bbd2d2a75734
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerClickMessageListener
  - hash:
      serializedVersion: 2
      Hash: 8dd7c0d5fe08cd5dce07400b145a963d
    assemblyName: Unity.XR.ARFoundation
    namespaceName: UnityEngine.XR.ARFoundation
    className: ARTrackedImageManager
  - hash:
      serializedVersion: 2
      Hash: 1b31b1223130f1670e8697f18fdad548
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: PressStartContollerNew
  - hash:
      serializedVersion: 2
      Hash: 0b63f6d3335e11939ce8790b904c8691
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: ActivationPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: e402a382f213255c22bda9254dd831b3
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: SceneVariables
  - hash:
      serializedVersion: 2
      Hash: e88d71ef93aba4e7faf243430f6a10ce
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: MacroScriptableObject
  - hash:
      serializedVersion: 2
      Hash: 8dc40a01667ca02b3354a4e7a61be082
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: GridLayoutGroup
  - hash:
      serializedVersion: 2
      Hash: 90cf308600fb4aeb677786843453ec55
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem
    className: RemoteInputPlayerConnection
  - hash:
      serializedVersion: 2
      Hash: f2098fcd8ee983a05ec192f63904298f
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnEndDragMessageListener
  - hash:
      serializedVersion: 2
      Hash: db9b308769d19e1f9e161df0392c23ca
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTransformChildrenChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: a47182e2cc4ad3e76939204f5ef61278
    assemblyName: UnityEngine.XR.LegacyInputHelpers
    namespaceName: UnityEditor.XR.LegacyInputHelpers
    className: CameraOffset
  - hash:
      serializedVersion: 2
      Hash: 6d94138b60a9299798dda96c1ee6725f
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnBeginDragMessageListener
  - hash:
      serializedVersion: 2
      Hash: 51b80d3734ea86b38e3101db98029e15
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: AnimationPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: 0370b9f95798139b666659c7e1be6147
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.UI
    className: BaseInputOverride
  - hash:
      serializedVersion: 2
      Hash: 47ee34eb8b6021efa0289dbbb17a5a85
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_InputField
  - hash:
      serializedVersion: 2
      Hash: ec6cf673540b2d4e42c612611eeafe4d
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerStayMessageListener
  - hash:
      serializedVersion: 2
      Hash: cabe23ac61342d288876f79ef43569cd
    assemblyName: Unity.XR.ARFoundation.VisualScripting
    namespaceName: UnityEngine.XR.ARFoundation.VisualScripting
    className: ARFaceManagerListener
  - hash:
      serializedVersion: 2
      Hash: eb3689ba55543e2ebdd1aed67d41538a
    assemblyName: UnityEngine.XR.LegacyInputHelpers
    namespaceName: UnityEngine.XR.LegacyInputHelpers
    className: TransitionArmModel
  - hash:
      serializedVersion: 2
      Hash: 10c17b309bdca62b4d7fbaf113ed4ca0
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Toggle
  - hash:
      serializedVersion: 2
      Hash: 522f6865b4ec12522e602206785309f8
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: GlobalMessageListener
  - hash:
      serializedVersion: 2
      Hash: 33f160a5c07e580c29aba2bd1e4db811
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: HorizontalLayoutGroup
  - hash:
      serializedVersion: 2
      Hash: 53e329840e6d03aebec76623c3b054db
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Slider
  - hash:
      serializedVersion: 2
      Hash: 191cd500c627cd6682ef6f70b8e47f2c
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionEnter2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: 0c0b64d103fe555d830e8ca206abd12a
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerExitMessageListener
  - hash:
      serializedVersion: 2
      Hash: 17f82e3e7d8f8b07a270a4aad9bfe79d
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_FontAsset
  - hash:
      serializedVersion: 2
      Hash: 8ff9c0ea15d6485f6ec72d1ed04365b7
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: AudioPlayer
  - hash:
      serializedVersion: 2
      Hash: d82086405f46de14829f6b4bcbdd8fc9
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnBecameInvisibleMessageListener
  - hash:
      serializedVersion: 2
      Hash: 5874b789cd9832847c61ebfa803213af
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UIElements
    className: PanelEventHandler
  - hash:
      serializedVersion: 2
      Hash: 9dd54ce33440072d6692a67e0c384ad1
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: GroupTrack
  - hash:
      serializedVersion: 2
      Hash: bf0842d88681628b29beb00b5e2ab785
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnDeselectMessageListener
  - hash:
      serializedVersion: 2
      Hash: b5007673bbbdb9d12597a1d8201370f5
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Text
  - hash:
      serializedVersion: 2
      Hash: c2d92dc9dbc7fb6c17b9c57d759a7cc0
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: QRCodeDecodeController
  - hash:
      serializedVersion: 2
      Hash: 41dc9ff6a7e53e6fc1ae7a03e0266779
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: DeviceCameraController
  - hash:
      serializedVersion: 2
      Hash: 2071f7ac454ae14eafecfd4041edae3a
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputActionEditorWindow
  - hash:
      serializedVersion: 2
      Hash: 1ce63d97d1ca25cba2d91d5f32fd5f79
    assemblyName: Unity.VisualScripting.State
    namespaceName: Unity.VisualScripting
    className: StateGraphAsset
  - hash:
      serializedVersion: 2
      Hash: 6cf63e9dc888f92a3672d2e4db631c8e
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: PlayerInputManagerEditor
  - hash:
      serializedVersion: 2
      Hash: f575854bcd9263bcceb3a0f2b8f26e82
    assemblyName: Unity.XR.ARFoundation
    namespaceName: UnityEngine.XR.ARFoundation
    className: ARCameraManager
  - hash:
      serializedVersion: 2
      Hash: ef38c1407dba8c1f5d69f5db61a694fe
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: AnimatorMessageListener
  - hash:
      serializedVersion: 2
      Hash: 33656f83bb0079184b2206a69690ec46
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: AspectRatioFitter
  - hash:
      serializedVersion: 2
      Hash: f98fc2b4d2a93c4cf9f753dc912f9477
    assemblyName: Unity.XR.ARFoundation
    namespaceName: UnityEngine.XR.ARFoundation
    className: ARTrackedObject
  - hash:
      serializedVersion: 2
      Hash: 9637dcdf3068a91dfe653e61bb82f13d
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TextMeshProUGUI
  - hash:
      serializedVersion: 2
      Hash: 46624fea8a4af2d9e344d87df99d29c4
    assemblyName: Unity.XR.ARFoundation.VisualScripting
    namespaceName: UnityEngine.XR.ARFoundation.VisualScripting
    className: ARTrackedObjectManagerListener
  - hash:
      serializedVersion: 2
      Hash: da5a949a0bbd2ff91bc51c5805c2c41f
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseUpAsButtonMessageListener
  - hash:
      serializedVersion: 2
      Hash: 49a2510725c4699325c7cc6a8eca7f69
    assemblyName: Unity.XR.CoreUtils
    namespaceName: Unity.XR.CoreUtils.Datums
    className: IntDatum
  - hash:
      serializedVersion: 2
      Hash: 4f353524ce6564e40764f1ea080b2d85
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerStay2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: f39cb99c51c3f8dd6b51f2320f14babf
    assemblyName: Unity.XR.ARFoundation.VisualScripting
    namespaceName: UnityEngine.XR.ARFoundation.VisualScripting
    className: ARFaceListener
  - hash:
      serializedVersion: 2
      Hash: 9104447b683bf70406fa01f12ecb564a
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: StandaloneInputModule
  - hash:
      serializedVersion: 2
      Hash: a90f6a00b23520788be7d89ee3b0773d
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Scrollbar
  - hash:
      serializedVersion: 2
      Hash: 13f262cc0741566a7ab72682984bc3e1
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_SubMesh
  - hash:
      serializedVersion: 2
      Hash: 24504949f899cc7e6cbeba00c99bc77d
    assemblyName: Assembly-CSharp
    namespaceName: AnimationUtility
    className: AnimationPlayer
  - hash:
      serializedVersion: 2
      Hash: febcfbf0a0d85e37808a3740e3c2c6fa
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: MarkerTrack
  - hash:
      serializedVersion: 2
      Hash: 672716ae97f6ddb615fa5ac3ec9da349
    assemblyName: UnityEngine.TestRunner
    namespaceName: UnityEngine.TestTools
    className: BeforeAfterTestCommandState
  - hash:
      serializedVersion: 2
      Hash: a2aa17d6010b80daa9a5e10515459ff1
    assemblyName: Unity.XR.ARFoundation
    namespaceName: UnityEngine.XR.ARFoundation
    className: ARPointCloudManager
  - hash:
      serializedVersion: 2
      Hash: ed9657e47372a10e38bf1bebcf5e71c7
    assemblyName: Unity.XR.ARFoundation
    namespaceName: UnityEngine.XR.ARFoundation
    className: AREnvironmentProbe
  - hash:
      serializedVersion: 2
      Hash: bc75926bfd3609757f7bf33ff766f026
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.EnhancedTouch
    className: TouchSimulation
  - hash:
      serializedVersion: 2
      Hash: 8ed5daadd237bb82e132790f75ffccaf
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: PlayerInputEditor
  - hash:
      serializedVersion: 2
      Hash: a92561298b80715aa69e8fa770123cb5
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.UI.Editor
    className: InputSystemUIInputModuleEditor
  - hash:
      serializedVersion: 2
      Hash: 8bbce9932f67ad8e7f4eefec87800f94
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TextContainer
  - hash:
      serializedVersion: 2
      Hash: 3074afe1b03a0fb081e176a4ef1b9d09
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputActionAssetEditor
  - hash:
      serializedVersion: 2
      Hash: e63fe08659e7e4647a51098c666f8845
    assemblyName: Unity.InputSystem
    namespaceName: 
    className: DownloadableSample
  - hash:
      serializedVersion: 2
      Hash: 58c07d4e6331af1546687658f5e3a5c3
    assemblyName: Unity.XR.ARFoundation
    namespaceName: UnityEngine.XR.ARFoundation
    className: ARPoseDriver
  - hash:
      serializedVersion: 2
      Hash: 1ccbe501b98ca759ca0ddb02fcd3a4ae
    assemblyName: Unity.XR.ARFoundation
    namespaceName: UnityEngine.XR.ARFoundation
    className: ARAnchorManager
  - hash:
      serializedVersion: 2
      Hash: f669be52680c88846b2093fd41d1e06e
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnDragMessageListener
  - hash:
      serializedVersion: 2
      Hash: 20a120e55ad3ab65556328d5fd8309dd
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Outline
  - hash:
      serializedVersion: 2
      Hash: 33eb495d99aa9bea4dbe8b4c4e02c7bb
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem
    className: InputActionReference
  - hash:
      serializedVersion: 2
      Hash: 17142b03663387290480c82303274c02
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerEnterMessageListener
  - hash:
      serializedVersion: 2
      Hash: f2305da2400dfe6f069770a1a63f0f7c
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: PlayableTrack
  - hash:
      serializedVersion: 2
      Hash: 4878e735441f4041539853759559c86f
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: VerticalLayoutGroup
  - hash:
      serializedVersion: 2
      Hash: c4b85a67513b4f0cf7b5fb2d5ded04b0
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionExitMessageListener
  - hash:
      serializedVersion: 2
      Hash: 64e60d7de7e623f3792340e8b57b61f4
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: QRDecodeTest
  - hash:
      serializedVersion: 2
      Hash: d2ecd94550edf1b56aff07b1f4e41c57
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerEnter2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: 7bb32d6e4c7614a97d9723ce7e26588c
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: BasicPlayableBehaviour
  - hash:
      serializedVersion: 2
      Hash: e8d390bd9ecacc453a7500b90d0c0292
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: Physics2DRaycaster
  - hash:
      serializedVersion: 2
      Hash: 19824dd35debb3fff0983df49f8359ab
    assemblyName: UnityEngine.TestRunner
    namespaceName: UnityEngine.TestTools.TestRunner.Callbacks
    className: PlayModeRunnerCallback
  - hash:
      serializedVersion: 2
      Hash: aec084fb46cffb74b42af2adf1f1a1c0
    assemblyName: Unity.XR.ARFoundation.VisualScripting
    namespaceName: UnityEngine.XR.ARFoundation.VisualScripting
    className: ARCameraManagerListener
  - hash:
      serializedVersion: 2
      Hash: afb7b65d28d5a7423e69a1b076be0064
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: PositionAsUV1
  - hash:
      serializedVersion: 2
      Hash: 16821af11f07769603c0a4f542b60e72
    assemblyName: Unity.XR.ARFoundation.VisualScripting
    namespaceName: UnityEngine.XR.ARFoundation.VisualScripting
    className: ARAnchorManagerListener
  - hash:
      serializedVersion: 2
      Hash: 6a0b5b415c00f783202fd1ae2d556cb2
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: AudioPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: b14e186823458f549289789cccbb77ce
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerDownMessageListener
  - hash:
      serializedVersion: 2
      Hash: ee34255e3e86289bde9bad5e07b84cbc
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnScrollRectValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: 830c3b545f6e6ee60a4cebab90516f2c
    assemblyName: UnityEngine.TestRunner
    namespaceName: UnityEngine.TestTools.TestRunner.Callbacks
    className: RemoteTestResultSender
  - hash:
      serializedVersion: 2
      Hash: d0cdd713f9bf4e6a625d9e7804deca42
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: EventTrigger
  - hash:
      serializedVersion: 2
      Hash: 4895209f48657ff949db7815a6062743
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem
    className: InputSettings
  - hash:
      serializedVersion: 2
      Hash: a89b0448af4e1cd103a77f9fec0a961f
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnSubmitMessageListener
  - hash:
      serializedVersion: 2
      Hash: 5ebc58ec194dd44630b25fcc270ad4af
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Dropdown
  - hash:
      serializedVersion: 2
      Hash: cfa9bf09a5b4859447ad34e7f8d0b820
    assemblyName: Unity.XR.ARFoundation
    namespaceName: UnityEngine.XR.ARFoundation
    className: ARBackgroundRendererFeature
  - hash:
      serializedVersion: 2
      Hash: ad2859395bfa7c5f6efe850f3c56fe0c
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: FaceCamera
  - hash:
      serializedVersion: 2
      Hash: 4728e3d21cc2a12e391e71ec9b7eed0b
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: SignalTrack
  - hash:
      serializedVersion: 2
      Hash: 086db860c4e105d0348d0b716cfce562
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: Loom
  - hash:
      serializedVersion: 2
      Hash: cac40d2f764f526c6d35c42606cd2f9f
    assemblyName: Unity.XR.ARCore
    namespaceName: UnityEngine.XR.ARCore
    className: ARCoreLoader
  - hash:
      serializedVersion: 2
      Hash: 67272c9c30c50b68ef1ce72ac0191c57
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMoveMessageListener
  - hash:
      serializedVersion: 2
      Hash: 4e3c4ed66ca536796a30acb96e6526d6
    assemblyName: Unity.XR.CoreUtils
    namespaceName: Unity.XR.CoreUtils.Datums
    className: AnimationCurveDatum
  - hash:
      serializedVersion: 2
      Hash: dde6292521f7f104a36f9c12f5cfe65b
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: PressStartContoller
  - hash:
      serializedVersion: 2
      Hash: 37bf71057ea34aaf9f08953f6af3fa79
    assemblyName: Unity.XR.ARFoundation
    namespaceName: UnityEngine.XR.ARFoundation
    className: ARSessionOrigin
  - hash:
      serializedVersion: 2
      Hash: d0392b7f3f52d5af537ade186b4f51f1
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: ControlTrack
  - hash:
      serializedVersion: 2
      Hash: dbe80c9dc024d18e75107a0b4da06133
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.UI
    className: InputSystemUIInputModule
  - hash:
      serializedVersion: 2
      Hash: 88879554ff19f28e3700ed6220a52ea4
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: TimelimeControllerNew
  - hash:
      serializedVersion: 2
      Hash: d5cd7f207e91ca8336c65a577a6fb2a0
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: ScrollRect
  - hash:
      serializedVersion: 2
      Hash: 83447eb14ba5f9222c0f703aabb89c38
    assemblyName: UnityEngine.XR.LegacyInputHelpers
    namespaceName: UnityEngine.XR.LegacyInputHelpers
    className: SwingArmModel
  - hash:
      serializedVersion: 2
      Hash: 9a174471d4db2c0c6d3ac47d2f818db1
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnParticleCollisionMessageListener
  - hash:
      serializedVersion: 2
      Hash: 40be2ad159aa1a2b72ecc74cb38c7824
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.UI
    className: MultiplayerEventSystem
  - hash:
      serializedVersion: 2
      Hash: 5f40a959550e36f2066049f8b9f9107f
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: QRCodeEncodeController
  - hash:
      serializedVersion: 2
      Hash: 2bbd05175d4cdd21e24f9716cdd24f83
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.XR
    className: TrackedPoseDriver
  - hash:
      serializedVersion: 2
      Hash: bafe8cc4728a743857f9628625365806
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: MaterialAlphaAnimator
  - hash:
      serializedVersion: 2
      Hash: 0f5a086d4d028363983ba6ca30a01397
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseOverMessageListener
  - hash:
      serializedVersion: 2
      Hash: 2c583ad0ac85d03e8700dd05fdcb46cb
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: Variables
  - hash:
      serializedVersion: 2
      Hash: b30c8635462e66ac20f4241106119f77
    assemblyName: Unity.VisualScripting.State
    namespaceName: Unity.VisualScripting
    className: StateMachine
  - hash:
      serializedVersion: 2
      Hash: 6e50832591b96c599d94d7ffe31b2b8c
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ObjectAnimator
  - hash:
      serializedVersion: 2
      Hash: 82a7c77ba1da2d34c81569e79eeeed9d
    assemblyName: Unity.XR.ARFoundation
    namespaceName: UnityEngine.XR.ARFoundation
    className: ARPlaneMeshVisualizer
  - hash:
      serializedVersion: 2
      Hash: 568292fb7ea6c4f7a667e5ff76bc7e85
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseUpMessageListener
  - hash:
      serializedVersion: 2
      Hash: 1553f209ae0b2d5d3a35685fca55f9c3
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_ScrollbarEventHandler
  - hash:
      serializedVersion: 2
      Hash: 4eed6cbb7e35dc1c168f67943a4ceba3
    assemblyName: Unity.XR.ARFoundation
    namespaceName: UnityEngine.XR.ARFoundation
    className: ARFaceManager
  - hash:
      serializedVersion: 2
      Hash: 8aa53a78f29ddf9b15dee823fcbd2c6f
    assemblyName: Unity.XR.ARFoundation
    namespaceName: UnityEngine.XR.ARFoundation
    className: ToolButton
  - hash:
      serializedVersion: 2
      Hash: 616f54790a7a5efa942990a82bb6abbb
    assemblyName: Unity.XR.ARFoundation
    namespaceName: UnityEngine.XR.ARFoundation
    className: ARRaycastManager
  - hash:
      serializedVersion: 2
      Hash: 0763e9b439042fa7ebcfe5eb2850a1a5
    assemblyName: Unity.XR.CoreUtils
    namespaceName: Unity.XR.CoreUtils.Datums
    className: FloatDatum
  - hash:
      serializedVersion: 2
      Hash: 711350058a1e622134b2695eb8d9cc0e
    assemblyName: Unity.XR.ARFoundation
    namespaceName: UnityEngine.XR.ARFoundation
    className: ARPointCloud
  - hash:
      serializedVersion: 2
      Hash: d433f69faface7a723652a74be2559e0
    assemblyName: Unity.XR.ARFoundation
    namespaceName: UnityEngine.XR.ARFoundation
    className: ARFaceMeshVisualizer
  - hash:
      serializedVersion: 2
      Hash: ff76cf6cf52b4db7b95af6ffadca9288
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: SignalEmitter
  - hash:
      serializedVersion: 2
      Hash: c4cdb902228df56d5b2b639d6a6bbd3c
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem
    className: PlayerInputManager
  - hash:
      serializedVersion: 2
      Hash: 02a47340661d2ea7c6d30e292cfef403
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnToggleValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: 8bf800a764665b085fe469e8aea7f167
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseEnterMessageListener
  - hash:
      serializedVersion: 2
      Hash: 4fcf0bb4aff1d14a1dfcdedc4b07bd05
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: TouchInputModule
  - hash:
      serializedVersion: 2
      Hash: 00984d91cf997dd99a9f01c5d1f0b404
    assemblyName: Assembly-CSharp
    namespaceName: TBEasyWebCam
    className: EasyWebCam
  - hash:
      serializedVersion: 2
      Hash: 5f76ada1d147da9a197cd20aec88c700
    assemblyName: Unity.XR.ARFoundation.VisualScripting
    namespaceName: UnityEngine.XR.ARFoundation.VisualScripting
    className: AREnvironmentProbeManagerListener
  - hash:
      serializedVersion: 2
      Hash: 00eef92bf387da2c9009267123d9d674
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerExit2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: 8b15e35eb98fc258ec2e839df7c3c9b0
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnSliderValueChangedMessageListener
  platform: 13
  scenePathNames:
  - Assets/Scenes/MainForTimeline.unity
  playerPath: D:/Unity_Project/SurAnim/BUILD/SLSURam.apk
