using UnityEngine;
using System.Collections;

/// <summary>
/// Animates an object from its parent position with configurable distance and time.
/// </summary>
public class ObjectAnimator : MonoBehaviour
{
    [Header("Animation Settings")]
    [Tooltip("Duration of the animation in seconds")]
    public float animationDuration = 2.0f;

    [Tooltip("Distance to move from parent in local space")]
    public Vector3 animationDistance = new Vector3(0, 0, 6);

    [Tooltip("Animation curve to control movement (default is linear)")]
    public AnimationCurve animationCurve = AnimationCurve.Linear(0, 0, 1, 1);

    [Header("Animation Controls")]
    [Tooltip("Play animation automatically on start")]
    public bool playOnStart = true;

    [Tooltip("Return to original position when animation completes")]
    public bool pingPong = false;

    // Private variables
    private Vector3 startPosition;
    private Vector3 targetPosition;
    private bool isAnimating = false;
    private bool isReturning = false;

    private void Start()
    {
        // Initialize positions
        startPosition = transform.localPosition;
        targetPosition = startPosition + animationDistance;

        if (playOnStart)
        {
            StartAnimation();
        }
    }

    /// <summary>
    /// Starts the animation from current position to target position
    /// </summary>
    public void StartAnimation()
    {
        if (!isAnimating)
        {
            startPosition = transform.localPosition;
            targetPosition = startPosition + animationDistance;
            StartCoroutine(AnimateObject(startPosition, targetPosition));
        }
    }

    /// <summary>
    /// Returns the object to its original position
    /// </summary>
    public void ReturnToStart()
    {
        if (!isAnimating)
        {
            Vector3 currentPos = transform.localPosition;
            StartCoroutine(AnimateObject(currentPos, startPosition));
        }
    }

    /// <summary>
    /// Coroutine that handles the actual animation
    /// </summary>
    private IEnumerator AnimateObject(Vector3 from, Vector3 to)
    {
        isAnimating = true;
        float elapsed = 0;

        while (elapsed < animationDuration)
        {
            // Calculate progress and apply animation curve
            float progress = elapsed / animationDuration;
            float curveValue = animationCurve.Evaluate(progress);

            // Update position
            transform.localPosition = Vector3.Lerp(from, to, curveValue);

            // Update elapsed time
            elapsed += Time.deltaTime;
            yield return null;
        }

        // Ensure final position is exact
        transform.localPosition = to;
        isAnimating = false;

        // Handle ping-pong behavior
        if (pingPong && !isReturning)
        {
            isReturning = true;
            yield return new WaitForSeconds(0.5f); // Optional delay before returning
            StartCoroutine(AnimateObject(to, from));
            isReturning = false;
        }
    }

    /// <summary>
    /// Immediately moves the object to its target position
    /// </summary>
    public void SkipToTarget()
    {
        StopAllCoroutines();
        isAnimating = false;
        transform.localPosition = targetPosition;
    }

    /// <summary>
    /// Immediately returns the object to its start position
    /// </summary>
    public void SkipToStart()
    {
        StopAllCoroutines();
        isAnimating = false;
        transform.localPosition = startPosition;
    }

    /// <summary>
    /// Sets new animation distance and updates target position
    /// </summary>
    public void SetAnimationDistance(Vector3 newDistance)
    {
        animationDistance = newDistance;
        targetPosition = startPosition + animationDistance;
    }

    /// <summary>
    /// Sets new animation duration
    /// </summary>
    public void SetAnimationDuration(float newDuration)
    {
        if (newDuration > 0)
        {
            animationDuration = newDuration;
        }
    }
}