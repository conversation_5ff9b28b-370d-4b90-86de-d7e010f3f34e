{ "pid": 20040, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 20040, "tid": 1, "ts": 1751453315814921, "dur": 1530, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 20040, "tid": 1, "ts": 1751453315816454, "dur": 80570, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 20040, "tid": 1, "ts": 1751453315897026, "dur": 20673, "ph": "X", "name": "Write<PERSON><PERSON>", "args": {} },
{ "pid": 20040, "tid": 288657, "ts": 1751453317368603, "dur": 12, "ph": "X", "name": "", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315814898, "dur": 15647, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315830546, "dur": 1537681, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315830555, "dur": 31, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315830590, "dur": 302, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315830896, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315830898, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315830928, "dur": 3, "ph": "X", "name": "ProcessMessages 41", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315830933, "dur": 2451, "ph": "X", "name": "ReadAsync 41", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315833387, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315833389, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315833426, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315833428, "dur": 31, "ph": "X", "name": "ReadAsync 555", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315833462, "dur": 1, "ph": "X", "name": "ProcessMessages 674", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315833465, "dur": 47, "ph": "X", "name": "ReadAsync 674", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315833514, "dur": 1, "ph": "X", "name": "ProcessMessages 1055", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315833517, "dur": 30, "ph": "X", "name": "ReadAsync 1055", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315833549, "dur": 1, "ph": "X", "name": "ProcessMessages 860", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315833551, "dur": 27, "ph": "X", "name": "ReadAsync 860", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315833580, "dur": 1, "ph": "X", "name": "ProcessMessages 767", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315833582, "dur": 33, "ph": "X", "name": "ReadAsync 767", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315833617, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315833620, "dur": 27, "ph": "X", "name": "ReadAsync 577", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315833649, "dur": 1, "ph": "X", "name": "ProcessMessages 672", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315833650, "dur": 18, "ph": "X", "name": "ReadAsync 672", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315833671, "dur": 32, "ph": "X", "name": "ReadAsync 89", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315833706, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315833732, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315833734, "dur": 21, "ph": "X", "name": "ReadAsync 636", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315833758, "dur": 36, "ph": "X", "name": "ReadAsync 475", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315833798, "dur": 35, "ph": "X", "name": "ReadAsync 464", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315833835, "dur": 1, "ph": "X", "name": "ProcessMessages 902", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315833837, "dur": 26, "ph": "X", "name": "ReadAsync 902", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315833866, "dur": 29, "ph": "X", "name": "ReadAsync 574", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315833898, "dur": 1, "ph": "X", "name": "ProcessMessages 633", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315833899, "dur": 33, "ph": "X", "name": "ReadAsync 633", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315833934, "dur": 1, "ph": "X", "name": "ProcessMessages 777", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315833936, "dur": 27, "ph": "X", "name": "ReadAsync 777", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315833965, "dur": 1, "ph": "X", "name": "ProcessMessages 684", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315833967, "dur": 23, "ph": "X", "name": "ReadAsync 684", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315833993, "dur": 28, "ph": "X", "name": "ReadAsync 438", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834025, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834065, "dur": 1, "ph": "X", "name": "ProcessMessages 953", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834067, "dur": 25, "ph": "X", "name": "ReadAsync 953", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834095, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834096, "dur": 39, "ph": "X", "name": "ReadAsync 486", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834140, "dur": 36, "ph": "X", "name": "ReadAsync 83", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834178, "dur": 1, "ph": "X", "name": "ProcessMessages 809", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834180, "dur": 36, "ph": "X", "name": "ReadAsync 809", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834219, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834246, "dur": 1, "ph": "X", "name": "ProcessMessages 425", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834248, "dur": 28, "ph": "X", "name": "ReadAsync 425", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834278, "dur": 41, "ph": "X", "name": "ReadAsync 629", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834323, "dur": 24, "ph": "X", "name": "ReadAsync 76", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834349, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834351, "dur": 25, "ph": "X", "name": "ReadAsync 526", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834378, "dur": 24, "ph": "X", "name": "ReadAsync 554", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834406, "dur": 35, "ph": "X", "name": "ReadAsync 566", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834442, "dur": 1, "ph": "X", "name": "ProcessMessages 754", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834444, "dur": 23, "ph": "X", "name": "ReadAsync 754", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834472, "dur": 19, "ph": "X", "name": "ReadAsync 517", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834493, "dur": 22, "ph": "X", "name": "ReadAsync 461", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834519, "dur": 37, "ph": "X", "name": "ReadAsync 344", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834558, "dur": 1, "ph": "X", "name": "ProcessMessages 862", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834559, "dur": 24, "ph": "X", "name": "ReadAsync 862", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834587, "dur": 25, "ph": "X", "name": "ReadAsync 641", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834615, "dur": 25, "ph": "X", "name": "ReadAsync 612", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834641, "dur": 1, "ph": "X", "name": "ProcessMessages 571", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834642, "dur": 23, "ph": "X", "name": "ReadAsync 571", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834668, "dur": 23, "ph": "X", "name": "ReadAsync 561", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834693, "dur": 1, "ph": "X", "name": "ProcessMessages 414", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834696, "dur": 24, "ph": "X", "name": "ReadAsync 414", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834723, "dur": 20, "ph": "X", "name": "ReadAsync 627", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834745, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834746, "dur": 31, "ph": "X", "name": "ReadAsync 390", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834780, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834808, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834809, "dur": 25, "ph": "X", "name": "ReadAsync 566", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834838, "dur": 28, "ph": "X", "name": "ReadAsync 292", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834869, "dur": 25, "ph": "X", "name": "ReadAsync 430", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834896, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834897, "dur": 19, "ph": "X", "name": "ReadAsync 443", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834919, "dur": 19, "ph": "X", "name": "ReadAsync 154", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834940, "dur": 23, "ph": "X", "name": "ReadAsync 131", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834966, "dur": 22, "ph": "X", "name": "ReadAsync 488", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315834991, "dur": 26, "ph": "X", "name": "ReadAsync 477", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835021, "dur": 23, "ph": "X", "name": "ReadAsync 309", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835047, "dur": 20, "ph": "X", "name": "ReadAsync 488", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835070, "dur": 60, "ph": "X", "name": "ReadAsync 70", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835133, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835159, "dur": 25, "ph": "X", "name": "ReadAsync 342", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835187, "dur": 22, "ph": "X", "name": "ReadAsync 581", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835211, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835213, "dur": 32, "ph": "X", "name": "ReadAsync 356", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835246, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835248, "dur": 20, "ph": "X", "name": "ReadAsync 591", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835270, "dur": 22, "ph": "X", "name": "ReadAsync 301", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835296, "dur": 25, "ph": "X", "name": "ReadAsync 466", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835322, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835324, "dur": 26, "ph": "X", "name": "ReadAsync 524", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835351, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835354, "dur": 25, "ph": "X", "name": "ReadAsync 408", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835383, "dur": 21, "ph": "X", "name": "ReadAsync 465", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835406, "dur": 25, "ph": "X", "name": "ReadAsync 298", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835435, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835465, "dur": 1, "ph": "X", "name": "ProcessMessages 635", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835467, "dur": 31, "ph": "X", "name": "ReadAsync 635", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835500, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835501, "dur": 27, "ph": "X", "name": "ReadAsync 566", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835531, "dur": 20, "ph": "X", "name": "ReadAsync 555", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835554, "dur": 21, "ph": "X", "name": "ReadAsync 283", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835578, "dur": 23, "ph": "X", "name": "ReadAsync 322", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835604, "dur": 24, "ph": "X", "name": "ReadAsync 433", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835632, "dur": 26, "ph": "X", "name": "ReadAsync 412", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835660, "dur": 21, "ph": "X", "name": "ReadAsync 478", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835683, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835685, "dur": 19, "ph": "X", "name": "ReadAsync 362", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835707, "dur": 18, "ph": "X", "name": "ReadAsync 244", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835727, "dur": 25, "ph": "X", "name": "ReadAsync 83", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835755, "dur": 35, "ph": "X", "name": "ReadAsync 527", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835792, "dur": 1, "ph": "X", "name": "ProcessMessages 567", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835794, "dur": 25, "ph": "X", "name": "ReadAsync 567", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835823, "dur": 16, "ph": "X", "name": "ReadAsync 543", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835842, "dur": 25, "ph": "X", "name": "ReadAsync 21", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835870, "dur": 22, "ph": "X", "name": "ReadAsync 631", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835894, "dur": 24, "ph": "X", "name": "ReadAsync 379", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835922, "dur": 29, "ph": "X", "name": "ReadAsync 479", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835953, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835955, "dur": 20, "ph": "X", "name": "ReadAsync 494", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315835978, "dur": 20, "ph": "X", "name": "ReadAsync 178", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836001, "dur": 22, "ph": "X", "name": "ReadAsync 378", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836026, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836055, "dur": 1, "ph": "X", "name": "ProcessMessages 710", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836056, "dur": 24, "ph": "X", "name": "ReadAsync 710", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836083, "dur": 22, "ph": "X", "name": "ReadAsync 524", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836107, "dur": 1, "ph": "X", "name": "ProcessMessages 402", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836108, "dur": 21, "ph": "X", "name": "ReadAsync 402", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836134, "dur": 24, "ph": "X", "name": "ReadAsync 333", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836161, "dur": 23, "ph": "X", "name": "ReadAsync 526", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836185, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836187, "dur": 29, "ph": "X", "name": "ReadAsync 434", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836217, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836219, "dur": 25, "ph": "X", "name": "ReadAsync 509", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836247, "dur": 20, "ph": "X", "name": "ReadAsync 561", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836270, "dur": 22, "ph": "X", "name": "ReadAsync 21", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836294, "dur": 23, "ph": "X", "name": "ReadAsync 342", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836320, "dur": 23, "ph": "X", "name": "ReadAsync 547", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836345, "dur": 2, "ph": "X", "name": "ProcessMessages 330", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836348, "dur": 22, "ph": "X", "name": "ReadAsync 330", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836373, "dur": 25, "ph": "X", "name": "ReadAsync 404", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836400, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836401, "dur": 23, "ph": "X", "name": "ReadAsync 472", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836428, "dur": 27, "ph": "X", "name": "ReadAsync 364", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836457, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836458, "dur": 26, "ph": "X", "name": "ReadAsync 556", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836487, "dur": 31, "ph": "X", "name": "ReadAsync 477", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836519, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836521, "dur": 19, "ph": "X", "name": "ReadAsync 566", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836543, "dur": 24, "ph": "X", "name": "ReadAsync 324", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836571, "dur": 28, "ph": "X", "name": "ReadAsync 576", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836602, "dur": 24, "ph": "X", "name": "ReadAsync 445", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836630, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836631, "dur": 20, "ph": "X", "name": "ReadAsync 347", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836655, "dur": 25, "ph": "X", "name": "ReadAsync 271", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836681, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836683, "dur": 24, "ph": "X", "name": "ReadAsync 598", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836709, "dur": 23, "ph": "X", "name": "ReadAsync 468", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836735, "dur": 1, "ph": "X", "name": "ProcessMessages 291", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836736, "dur": 26, "ph": "X", "name": "ReadAsync 291", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836765, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836766, "dur": 22, "ph": "X", "name": "ReadAsync 540", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836791, "dur": 19, "ph": "X", "name": "ReadAsync 398", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836814, "dur": 23, "ph": "X", "name": "ReadAsync 291", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836839, "dur": 23, "ph": "X", "name": "ReadAsync 581", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836865, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836866, "dur": 24, "ph": "X", "name": "ReadAsync 295", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836893, "dur": 17, "ph": "X", "name": "ReadAsync 598", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836914, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836945, "dur": 24, "ph": "X", "name": "ReadAsync 605", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836972, "dur": 25, "ph": "X", "name": "ReadAsync 477", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315836999, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837000, "dur": 26, "ph": "X", "name": "ReadAsync 401", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837030, "dur": 18, "ph": "X", "name": "ReadAsync 373", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837050, "dur": 29, "ph": "X", "name": "ReadAsync 63", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837082, "dur": 1, "ph": "X", "name": "ProcessMessages 738", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837084, "dur": 26, "ph": "X", "name": "ReadAsync 738", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837112, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837113, "dur": 24, "ph": "X", "name": "ReadAsync 382", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837140, "dur": 23, "ph": "X", "name": "ReadAsync 525", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837167, "dur": 21, "ph": "X", "name": "ReadAsync 427", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837192, "dur": 23, "ph": "X", "name": "ReadAsync 331", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837219, "dur": 23, "ph": "X", "name": "ReadAsync 591", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837245, "dur": 24, "ph": "X", "name": "ReadAsync 533", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837272, "dur": 26, "ph": "X", "name": "ReadAsync 455", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837300, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837302, "dur": 21, "ph": "X", "name": "ReadAsync 457", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837325, "dur": 24, "ph": "X", "name": "ReadAsync 279", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837353, "dur": 23, "ph": "X", "name": "ReadAsync 571", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837379, "dur": 23, "ph": "X", "name": "ReadAsync 502", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837405, "dur": 27, "ph": "X", "name": "ReadAsync 329", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837436, "dur": 19, "ph": "X", "name": "ReadAsync 567", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837457, "dur": 26, "ph": "X", "name": "ReadAsync 61", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837486, "dur": 27, "ph": "X", "name": "ReadAsync 448", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837517, "dur": 25, "ph": "X", "name": "ReadAsync 452", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837545, "dur": 23, "ph": "X", "name": "ReadAsync 453", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837570, "dur": 1, "ph": "X", "name": "ProcessMessages 396", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837572, "dur": 28, "ph": "X", "name": "ReadAsync 396", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837603, "dur": 24, "ph": "X", "name": "ReadAsync 421", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837629, "dur": 1, "ph": "X", "name": "ProcessMessages 432", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837630, "dur": 19, "ph": "X", "name": "ReadAsync 432", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837653, "dur": 25, "ph": "X", "name": "ReadAsync 235", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837681, "dur": 27, "ph": "X", "name": "ReadAsync 518", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837711, "dur": 30, "ph": "X", "name": "ReadAsync 510", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837744, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837745, "dur": 24, "ph": "X", "name": "ReadAsync 484", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837771, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837772, "dur": 26, "ph": "X", "name": "ReadAsync 343", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837802, "dur": 24, "ph": "X", "name": "ReadAsync 494", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837829, "dur": 26, "ph": "X", "name": "ReadAsync 437", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837857, "dur": 1, "ph": "X", "name": "ProcessMessages 549", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837858, "dur": 24, "ph": "X", "name": "ReadAsync 549", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837884, "dur": 2, "ph": "X", "name": "ProcessMessages 457", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837886, "dur": 25, "ph": "X", "name": "ReadAsync 457", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837915, "dur": 29, "ph": "X", "name": "ReadAsync 394", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837945, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837947, "dur": 21, "ph": "X", "name": "ReadAsync 530", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837971, "dur": 25, "ph": "X", "name": "ReadAsync 295", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837997, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315837999, "dur": 25, "ph": "X", "name": "ReadAsync 542", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315838026, "dur": 27, "ph": "X", "name": "ReadAsync 448", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315838055, "dur": 1, "ph": "X", "name": "ProcessMessages 576", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315838058, "dur": 29, "ph": "X", "name": "ReadAsync 576", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315838088, "dur": 2, "ph": "X", "name": "ProcessMessages 557", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315838091, "dur": 22, "ph": "X", "name": "ReadAsync 557", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315838115, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315838117, "dur": 23, "ph": "X", "name": "ReadAsync 484", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315838143, "dur": 28, "ph": "X", "name": "ReadAsync 473", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315838174, "dur": 20, "ph": "X", "name": "ReadAsync 553", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315838197, "dur": 25, "ph": "X", "name": "ReadAsync 73", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315838225, "dur": 20, "ph": "X", "name": "ReadAsync 500", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315838248, "dur": 21, "ph": "X", "name": "ReadAsync 287", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315838272, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315838300, "dur": 27, "ph": "X", "name": "ReadAsync 510", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315838330, "dur": 21, "ph": "X", "name": "ReadAsync 452", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315838354, "dur": 24, "ph": "X", "name": "ReadAsync 340", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315838382, "dur": 19, "ph": "X", "name": "ReadAsync 445", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315838405, "dur": 331, "ph": "X", "name": "ReadAsync 85", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315838742, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315838779, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315838780, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315838826, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315838829, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315838861, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315838863, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315838895, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315838897, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315838942, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315838944, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315839010, "dur": 2, "ph": "X", "name": "ProcessMessages 148", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315839019, "dur": 31, "ph": "X", "name": "ReadAsync 148", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315839052, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315839055, "dur": 32, "ph": "X", "name": "ReadAsync 112", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315839090, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315839093, "dur": 40, "ph": "X", "name": "ReadAsync 160", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315839137, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315839140, "dur": 31, "ph": "X", "name": "ReadAsync 108", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315839173, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315839176, "dur": 37, "ph": "X", "name": "ReadAsync 84", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315839216, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315839219, "dur": 34, "ph": "X", "name": "ReadAsync 128", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315839255, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315839258, "dur": 37, "ph": "X", "name": "ReadAsync 224", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315839299, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315839302, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315839340, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315839343, "dur": 36, "ph": "X", "name": "ReadAsync 192", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315839382, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315839386, "dur": 33, "ph": "X", "name": "ReadAsync 176", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315839422, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315839426, "dur": 36, "ph": "X", "name": "ReadAsync 224", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315839464, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315839467, "dur": 34, "ph": "X", "name": "ReadAsync 176", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315839505, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315839507, "dur": 29, "ph": "X", "name": "ReadAsync 192", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315839539, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315839541, "dur": 30, "ph": "X", "name": "ReadAsync 112", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315839574, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315839576, "dur": 30, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315839609, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315839612, "dur": 3147, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315842763, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315842765, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315842789, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315842791, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315842827, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315842829, "dur": 118, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315842951, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315842985, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315842988, "dur": 90, "ph": "X", "name": "ReadAsync 84", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315843082, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315843086, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315843121, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315843125, "dur": 126, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315843261, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315843263, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315843300, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315843303, "dur": 47, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315843353, "dur": 2, "ph": "X", "name": "ProcessMessages 108", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315843357, "dur": 32, "ph": "X", "name": "ReadAsync 108", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315843392, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315843397, "dur": 27, "ph": "X", "name": "ReadAsync 128", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315843428, "dur": 2, "ph": "X", "name": "ProcessMessages 92", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315843431, "dur": 28, "ph": "X", "name": "ReadAsync 92", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315843462, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315843465, "dur": 227, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315843697, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315843730, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315843733, "dur": 27, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315843763, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315843767, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315843799, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315843801, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315843840, "dur": 2, "ph": "X", "name": "ProcessMessages 40", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315843844, "dur": 271, "ph": "X", "name": "ReadAsync 40", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315844118, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315844121, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315844159, "dur": 2, "ph": "X", "name": "ProcessMessages 92", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315844163, "dur": 221, "ph": "X", "name": "ReadAsync 92", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315844389, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315844417, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315844462, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315844465, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315844503, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315844506, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315844537, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315844567, "dur": 11, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315844579, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315844606, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315844608, "dur": 283, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315844895, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315844926, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315844928, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315844974, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315844976, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315845015, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315845019, "dur": 260, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315845282, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315845285, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315845317, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315845319, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315845362, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315845364, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315845396, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315845398, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315845424, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315845426, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315845456, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315845459, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315845533, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315845568, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315845570, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315845626, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315845629, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315848797, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315848803, "dur": 110, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315848918, "dur": 109, "ph": "X", "name": "ProcessMessages 1382", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315849030, "dur": 357, "ph": "X", "name": "ReadAsync 1382", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315849390, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315849392, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315849434, "dur": 16, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315849452, "dur": 36, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315849492, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315849501, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315849538, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315849542, "dur": 280, "ph": "X", "name": "ReadAsync 28", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315849826, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315849828, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315849865, "dur": 14, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315849880, "dur": 98, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315849983, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315850016, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315850019, "dur": 1122, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315851359, "dur": 63, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315851586, "dur": 345, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315851988, "dur": 325, "ph": "X", "name": "ProcessMessages 166", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315852316, "dur": 153, "ph": "X", "name": "ReadAsync 166", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315852533, "dur": 199, "ph": "X", "name": "ProcessMessages 322", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315852735, "dur": 190, "ph": "X", "name": "ReadAsync 322", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315852929, "dur": 158, "ph": "X", "name": "ProcessMessages 158", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315853089, "dur": 132, "ph": "X", "name": "ReadAsync 158", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315853252, "dur": 109, "ph": "X", "name": "ProcessMessages 184", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315853363, "dur": 66, "ph": "X", "name": "ReadAsync 184", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315853432, "dur": 254, "ph": "X", "name": "ProcessMessages 138", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315853689, "dur": 102, "ph": "X", "name": "ReadAsync 138", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315853812, "dur": 38, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315853851, "dur": 134452, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315988311, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315988315, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315988357, "dur": 2948, "ph": "X", "name": "ProcessMessages 471", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315991307, "dur": 3591, "ph": "X", "name": "ReadAsync 471", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315994905, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315994908, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315994974, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315994977, "dur": 654, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315995637, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315995668, "dur": 15, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453315995683, "dur": 49402, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453316045093, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453316045097, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453316045134, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453316045138, "dur": 739, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453316045881, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453316045882, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453316045910, "dur": 17, "ph": "X", "name": "ProcessMessages 50", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453316045928, "dur": 825792, "ph": "X", "name": "ReadAsync 50", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453316871728, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453316871731, "dur": 108, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453316871842, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453316871845, "dur": 475935, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453317347787, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453317347791, "dur": 188, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453317347983, "dur": 22, "ph": "X", "name": "ProcessMessages 3577", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453317348007, "dur": 136, "ph": "X", "name": "ReadAsync 3577", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453317348147, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453317348149, "dur": 446, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453317348601, "dur": 88, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453317348692, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751453317348694, "dur": 19529, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 20040, "tid": 288657, "ts": 1751453317368618, "dur": 630, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 20040, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 20040, "tid": 21474836480, "ts": 1751453315814873, "dur": 102877, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 20040, "tid": 21474836480, "ts": 1751453315917751, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 20040, "tid": 21474836480, "ts": 1751453315917753, "dur": 102, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 20040, "tid": 288657, "ts": 1751453317369250, "dur": 5, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 20040, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 20040, "tid": 17179869184, "ts": 1751453315813040, "dur": 1555216, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 20040, "tid": 17179869184, "ts": 1751453315813122, "dur": 1717, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 20040, "tid": 17179869184, "ts": 1751453317368259, "dur": 50, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 20040, "tid": 17179869184, "ts": 1751453317368274, "dur": 12, "ph": "X", "name": "await ScriptUpdaters", "args": {} },
{ "pid": 20040, "tid": 17179869184, "ts": 1751453317368309, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 20040, "tid": 288657, "ts": 1751453317369261, "dur": 7, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1751453315831949, "dur":1696, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751453315833662, "dur":565, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751453315834292, "dur":453, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751453315835478, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_2B505061646D001A.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751453315835551, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_5D330BE2472E6120.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751453315835654, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_76022B412CAC8CD9.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751453315834762, "dur":5020, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751453315839786, "dur":1509497, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751453317349284, "dur":364, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751453317349733, "dur":87, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751453317349958, "dur":14872, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1751453315834953, "dur":4858, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751453315839821, "dur":67, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.AssetBundleModule.dll" }}
,{ "pid":12345, "tid":1, "ts":1751453315839890, "dur":323, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":1, "ts":1751453315839814, "dur":400, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_50E4708A0D51538A.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1751453315840232, "dur":85, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_C3615301B5E24071.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1751453315840341, "dur":81, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.SpriteMaskModule.dll" }}
,{ "pid":12345, "tid":1, "ts":1751453315840339, "dur":85, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_F285A58078E6790D.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1751453315840453, "dur":68, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.UnityAnalyticsModule.dll" }}
,{ "pid":12345, "tid":1, "ts":1751453315840451, "dur":72, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_25C5CF5537F2DDBD.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1751453315840657, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751453315840889, "dur":97, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.XR.ARFoundation.VisualScripting.rsp" }}
,{ "pid":12345, "tid":1, "ts":1751453315840987, "dur":308, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751453315841295, "dur":445, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751453315841741, "dur":273, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751453315842014, "dur":277, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751453315842292, "dur":262, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751453315842555, "dur":309, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751453315842877, "dur":1279, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751453315844765, "dur":4324, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":1, "ts":1751453315849093, "dur":408, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751453315849516, "dur":4013, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.pdb" }}
,{ "pid":12345, "tid":1, "ts":1751453315853531, "dur":1495755, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751453315834750, "dur":5041, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751453315839806, "dur":78, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.AndroidJNIModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1751453315839885, "dur":213, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":2, "ts":1751453315839799, "dur":300, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_DF0BE8D59135F2A2.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1751453315840125, "dur":51, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_6663F7B034CDA840.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1751453315840192, "dur":52, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.ParticleSystemModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1751453315840191, "dur":55, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_06265E8B8BCB8BC1.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1751453315840261, "dur":66, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.PhysicsModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1751453315840260, "dur":69, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_4273EA0E1245435F.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1751453315840407, "dur":89, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.TLSModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1751453315840405, "dur":93, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_96F36902702D2473.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1751453315840606, "dur":55, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/UnityEngine.SpatialTracking.rsp" }}
,{ "pid":12345, "tid":2, "ts":1751453315840662, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751453315840741, "dur":171, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/UnityEngine.SpatialTracking.rsp" }}
,{ "pid":12345, "tid":2, "ts":1751453315840912, "dur":536, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751453315841448, "dur":295, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751453315841744, "dur":273, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751453315842018, "dur":265, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751453315842283, "dur":356, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751453315842639, "dur":362, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751453315843001, "dur":1163, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751453315844165, "dur":60, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751453315844225, "dur":435, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751453315844681, "dur":58, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751453315844743, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751453315844811, "dur":4318, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Mathematics.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751453315849133, "dur":390, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751453315849564, "dur":4053, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.ForUI.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751453315853618, "dur":1495677, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751453315834807, "dur":4992, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751453315839811, "dur":74, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.AnimationModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1751453315839886, "dur":257, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":3, "ts":1751453315839802, "dur":341, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_76BB631114E548DC.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1751453315840265, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751453315840331, "dur":62, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.ScreenCaptureModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1751453315840330, "dur":65, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_804BA5354939D4A2.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1751453315840410, "dur":55, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_804BA5354939D4A2.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1751453315840468, "dur":75, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.UnityWebRequestModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1751453315840467, "dur":78, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_884FEC98B571C428.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1751453315840628, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751453315840762, "dur":113, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.rsp2" }}
,{ "pid":12345, "tid":3, "ts":1751453315840920, "dur":352, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751453315841273, "dur":377, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751453315841651, "dur":272, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751453315841923, "dur":266, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751453315842190, "dur":260, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751453315842451, "dur":401, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751453315842852, "dur":855, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751453315843707, "dur":509, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751453315844216, "dur":443, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751453315844746, "dur":4103, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":3, "ts":1751453315848902, "dur":2323, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.ForUI.dll" }}
,{ "pid":12345, "tid":3, "ts":1751453315851229, "dur":123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751453315851366, "dur":3276, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.XR.ARFoundation.InternalUtils.pdb" }}
,{ "pid":12345, "tid":3, "ts":1751453315854643, "dur":1494737, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751453315834873, "dur":4932, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751453315839815, "dur":77, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.ARModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1751453315839893, "dur":357, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":4, "ts":1751453315839807, "dur":444, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_AD1D75F736DB19AD.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1751453315840272, "dur":51, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.PropertiesModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1751453315840271, "dur":55, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_3694722ECA6247CF.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1751453315840348, "dur":100, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.StreamingModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1751453315840346, "dur":105, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_2B505061646D001A.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1751453315840500, "dur":66, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.UnityWebRequestTextureModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1751453315840498, "dur":70, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_81B816008B521027.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1751453315840587, "dur":61, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.rsp" }}
,{ "pid":12345, "tid":4, "ts":1751453315840648, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751453315840720, "dur":61, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.rsp" }}
,{ "pid":12345, "tid":4, "ts":1751453315840877, "dur":59, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":4, "ts":1751453315840937, "dur":304, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751453315841241, "dur":450, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751453315841691, "dur":277, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751453315841968, "dur":298, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751453315842266, "dur":282, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751453315842548, "dur":305, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751453315842853, "dur":690, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751453315843543, "dur":636, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751453315844224, "dur":427, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751453315844690, "dur":4215, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.XR.Management.dll" }}
,{ "pid":12345, "tid":4, "ts":1751453315848972, "dur":4349, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.XR.ARFoundation.InternalUtils.dll" }}
,{ "pid":12345, "tid":4, "ts":1751453315853331, "dur":67471, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751453315920803, "dur":1428491, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751453315834979, "dur":4838, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751453315839827, "dur":74, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.AudioModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1751453315839902, "dur":338, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":5, "ts":1751453315839820, "dur":421, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_A71CBF99130712CE.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1751453315840269, "dur":65, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.ProfilerModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1751453315840268, "dur":69, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_EFB11536E77075A5.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1751453315840373, "dur":90, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.TerrainModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1751453315840372, "dur":93, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_5CBA657CE8038C4D.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1751453315840480, "dur":76, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_5CBA657CE8038C4D.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1751453315840561, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751453315840667, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751453315840873, "dur":55, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.rsp" }}
,{ "pid":12345, "tid":5, "ts":1751453315840929, "dur":351, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751453315841281, "dur":430, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751453315841711, "dur":268, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751453315841980, "dur":351, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751453315842331, "dur":448, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751453315842780, "dur":291, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751453315843071, "dur":1105, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751453315844210, "dur":446, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751453315844711, "dur":4169, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/UnityEngine.SpatialTracking.pdb" }}
,{ "pid":12345, "tid":5, "ts":1751453315848884, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751453315848965, "dur":4383, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":5, "ts":1751453315853349, "dur":142695, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751453315996046, "dur":175, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\1300b0aP.dag\\Assembly-CSharp.pdb" }}
,{ "pid":12345, "tid":5, "ts":1751453315996045, "dur":177, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.pdb" }}
,{ "pid":12345, "tid":5, "ts":1751453315996252, "dur":743, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.pdb" }}
,{ "pid":12345, "tid":5, "ts":1751453315996997, "dur":1352290, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751453315835038, "dur":4785, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751453315839835, "dur":74, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.ClothModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315839911, "dur":412, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":6, "ts":1751453315839826, "dur":498, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_0E051C93ED23F1B2.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1751453315840349, "dur":79, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.SubstanceModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315840348, "dur":82, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_07FB64AAAFBFFE28.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1751453315840464, "dur":119, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.UnityWebRequestAssetBundleModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315840462, "dur":123, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_9506B0100723864E.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1751453315840630, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751453315840783, "dur":82, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.rsp" }}
,{ "pid":12345, "tid":6, "ts":1751453315840880, "dur":73, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/UnityEngine.TestRunner.rsp2" }}
,{ "pid":12345, "tid":6, "ts":1751453315840956, "dur":87, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\mscorlib.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315841045, "dur":68, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315841115, "dur":79, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Core.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315841196, "dur":51, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Data.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315841249, "dur":72, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315841323, "dur":207, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Drawing.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315841533, "dur":86, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315841621, "dur":66, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Numerics.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315841688, "dur":54, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315841743, "dur":66, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315841811, "dur":51, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315841864, "dur":53, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Web.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315841919, "dur":59, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Windows.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315841980, "dur":60, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315842041, "dur":78, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Linq.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315842120, "dur":72, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315842193, "dur":52, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315842247, "dur":54, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315842302, "dur":56, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Buffers.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315842359, "dur":51, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315842418, "dur":57, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315842476, "dur":54, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315842531, "dur":60, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315842593, "dur":62, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315842657, "dur":77, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315842736, "dur":83, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315842820, "dur":53, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315842875, "dur":61, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315842937, "dur":62, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315843000, "dur":56, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315843106, "dur":52, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315843159, "dur":67, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315843237, "dur":50, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315843384, "dur":57, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Calendars.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315843631, "dur":74, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315843735, "dur":54, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Extensions.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315840954, "dur":3203, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/UnityEngine.TestRunner.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1751453315844202, "dur":67, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1300b0aP.dag/Unity.XR.Management.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1751453315844303, "dur":289, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.XR.Management.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1751453315844673, "dur":105, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751453315844779, "dur":360, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751453315845140, "dur":204, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751453315845435, "dur":303, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.XR.CoreUtils.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1751453315845848, "dur":780, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1751453315846748, "dur":1146, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1751453315847969, "dur":74, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1300b0aP.dag/Unity.XR.ARFoundation.VisualScripting.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1751453315848061, "dur":70, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.State.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1751453315848132, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751453315848231, "dur":401, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.State.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1751453315848674, "dur":419, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.XR.ARFoundation.VisualScripting.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1751453315849141, "dur":379, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751453315849525, "dur":84, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp-firstpass.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1751453315849631, "dur":393, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp-firstpass.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1751453315850065, "dur":377, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1751453315850799, "dur":66, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751453315850906, "dur":138778, "ph":"X", "name": "Csc",  "args": { "detail":"Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1751453315996011, "dur":50417, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\1300b0aP.dag\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453315996010, "dur":50420, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453316046447, "dur":791, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453316047253, "dur":69674, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453316116928, "dur":146, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Assembly-CSharp-firstpass.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453316117075, "dur":76346, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.InputSystem.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453316193423, "dur":163, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.InputSystem.ForUI.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453316193587, "dur":48427, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453316242016, "dur":766, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453316242784, "dur":58112, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.Timeline.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453316300898, "dur":58704, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453316359604, "dur":21312, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453316380917, "dur":37556, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453316418475, "dur":172, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.XR.ARCore.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453316418649, "dur":84076, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.XR.ARFoundation.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453316502727, "dur":134, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.XR.ARFoundation.InternalUtils.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453316502862, "dur":116, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.XR.ARFoundation.VisualScripting.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453316502978, "dur":57355, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.XR.ARSubsystems.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453316560334, "dur":74952, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.XR.CoreUtils.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453316635288, "dur":135, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.XR.Management.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453316635424, "dur":23856, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\UnityEngine.SpatialTracking.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453316659282, "dur":80021, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\UnityEngine.TestRunner.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453316739304, "dur":98899, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453316838204, "dur":34704, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\UnityEngine.XR.LegacyInputHelpers.dll" }}
,{ "pid":12345, "tid":6, "ts":1751453316047246, "dur":825668, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Player/RuntimeInitializeOnLoads.json (+1 other)" }}
,{ "pid":12345, "tid":6, "ts":1751453316873044, "dur":476064, "ph":"X", "name": "BuildPlayerDataGenerator",  "args": { "detail":"Library/BuildPlayerData/Player/RuntimeInitializeOnLoads.json (+1 other)" }}
,{ "pid":12345, "tid":7, "ts":1751453315835095, "dur":4735, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751453315839844, "dur":63, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.ContentLoadModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1751453315839908, "dur":410, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":7, "ts":1751453315839833, "dur":486, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_E9FAC9CADBD3FB77.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1751453315840346, "dur":71, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.SpriteShapeModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1751453315840344, "dur":76, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_6A459DE4C2F38B94.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1751453315840446, "dur":71, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.UnityAnalyticsCommonModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1751453315840445, "dur":74, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_4114231CF36E37D8.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1751453315840542, "dur":68, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.AIModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1751453315840541, "dur":72, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_F3AAB0F3D9BDBAE4.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1751453315840628, "dur":70, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_F3AAB0F3D9BDBAE4.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1751453315840881, "dur":393, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751453315841275, "dur":463, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751453315841738, "dur":268, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751453315842006, "dur":299, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751453315842306, "dur":292, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751453315842598, "dur":276, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751453315842875, "dur":1283, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751453315844505, "dur":4459, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.pdb" }}
,{ "pid":12345, "tid":7, "ts":1751453315848968, "dur":529, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751453315849516, "dur":4324, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.XR.ARSubsystems.dll" }}
,{ "pid":12345, "tid":7, "ts":1751453315853849, "dur":1495432, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751453315835151, "dur":4685, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751453315839847, "dur":68, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.CoreModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1751453315839916, "dur":348, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":8, "ts":1751453315839839, "dur":426, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_706364592D3B89BB.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1751453315840265, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751453315840329, "dur":77, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1751453315840327, "dur":82, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_1B45C2772CBE133A.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1751453315840458, "dur":74, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.UnityCurlModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1751453315840457, "dur":77, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_A9D8044284F93AC9.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1751453315840555, "dur":53, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_A9D8044284F93AC9.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1751453315840633, "dur":60, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.XR.ARSubsystems.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":8, "ts":1751453315840806, "dur":99, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":8, "ts":1751453315840906, "dur":384, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751453315841290, "dur":398, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751453315841688, "dur":279, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751453315841968, "dur":282, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751453315842250, "dur":280, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751453315842531, "dur":259, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751453315842791, "dur":285, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751453315843076, "dur":1086, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751453315844201, "dur":64, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1300b0aP.dag/UnityEngine.SpatialTracking.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1751453315844299, "dur":298, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/UnityEngine.SpatialTracking.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1751453315844695, "dur":82, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1300b0aP.dag/UnityEngine.XR.LegacyInputHelpers.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1751453315844794, "dur":290, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/UnityEngine.XR.LegacyInputHelpers.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1751453315845154, "dur":186, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751453315845392, "dur":4174, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.pdb" }}
,{ "pid":12345, "tid":8, "ts":1751453315849570, "dur":100, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751453315849681, "dur":4362, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.XR.ARFoundation.VisualScripting.dll" }}
,{ "pid":12345, "tid":8, "ts":1751453315854052, "dur":1495327, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751453315835180, "dur":4664, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751453315839855, "dur":72, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.CrashReportingModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1751453315839928, "dur":350, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":9, "ts":1751453315839847, "dur":432, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_B314153F1DBA0033.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1751453315840337, "dur":62, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.SharedInternalsModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1751453315840335, "dur":66, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_FAC223C421AB24F9.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1751453315840473, "dur":71, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.UnityWebRequestWWWModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1751453315840471, "dur":76, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_88ACA70EABF00263.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1751453315840625, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751453315840887, "dur":93, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":9, "ts":1751453315840981, "dur":485, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751453315841467, "dur":269, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751453315841736, "dur":259, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751453315841995, "dur":298, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751453315842294, "dur":274, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751453315842568, "dur":407, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751453315842975, "dur":1192, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751453315853841, "dur":1096, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/UnityEngine.TestRunner.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751453315854938, "dur":1494439, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751453315835203, "dur":4650, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751453315839867, "dur":83, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.DirectorModule.dll" }}
,{ "pid":12345, "tid":10, "ts":1751453315839951, "dur":371, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":10, "ts":1751453315839857, "dur":465, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_BD6ADC004E16EE98.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1751453315840354, "dur":70, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.SubsystemsModule.dll" }}
,{ "pid":12345, "tid":10, "ts":1751453315840353, "dur":73, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_5D330BE2472E6120.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1751453315840487, "dur":86, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.VehiclesModule.dll" }}
,{ "pid":12345, "tid":10, "ts":1751453315840486, "dur":90, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_352DD57FC840BE33.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1751453315840598, "dur":71, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_352DD57FC840BE33.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1751453315840677, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751453315840754, "dur":62, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":10, "ts":1751453315840899, "dur":324, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751453315841223, "dur":517, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751453315841740, "dur":284, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751453315842024, "dur":260, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751453315842285, "dur":270, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751453315842555, "dur":343, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751453315842898, "dur":1261, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751453315844159, "dur":55, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751453315844214, "dur":440, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751453315844719, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751453315844779, "dur":4079, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.pdb" }}
,{ "pid":12345, "tid":10, "ts":1751453315848921, "dur":3542, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.XR.ARFoundation.dll" }}
,{ "pid":12345, "tid":10, "ts":1751453315852468, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751453315852560, "dur":2193, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.XR.CoreUtils.pdb" }}
,{ "pid":12345, "tid":10, "ts":1751453315854755, "dur":1494630, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751453315835241, "dur":4620, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751453315839873, "dur":79, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.dll" }}
,{ "pid":12345, "tid":11, "ts":1751453315839953, "dur":372, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":11, "ts":1751453315839864, "dur":462, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_A2E8D4FF6A11D2EA.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1751453315840359, "dur":83, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_A2E8D4FF6A11D2EA.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1751453315840445, "dur":68, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.UmbraModule.dll" }}
,{ "pid":12345, "tid":11, "ts":1751453315840443, "dur":72, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_21F16BB1A02DBE41.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1751453315840535, "dur":69, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.XRModule.dll" }}
,{ "pid":12345, "tid":11, "ts":1751453315840533, "dur":73, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_74AF38AB60F99366.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1751453315840674, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751453315840899, "dur":378, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751453315841278, "dur":376, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751453315841654, "dur":263, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751453315841918, "dur":280, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751453315842199, "dur":254, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751453315842453, "dur":480, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751453315842933, "dur":1233, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751453315844166, "dur":54, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751453315844220, "dur":458, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751453315844679, "dur":104, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751453315844783, "dur":372, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751453315845156, "dur":183, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751453315845419, "dur":4714, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.dll" }}
,{ "pid":12345, "tid":11, "ts":1751453315850177, "dur":4209, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Assembly-CSharp-firstpass.dll" }}
,{ "pid":12345, "tid":11, "ts":1751453315854387, "dur":1494906, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751453315835268, "dur":4601, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751453315839880, "dur":104, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.DSPGraphModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1751453315839985, "dur":368, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":12, "ts":1751453315839872, "dur":482, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_C2BC3D69C077BFB9.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1751453315840377, "dur":77, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_C2BC3D69C077BFB9.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1751453315840457, "dur":104, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.UnityConnectModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1751453315840456, "dur":107, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_A401D76D980215F1.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1751453315840597, "dur":88, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.rsp2" }}
,{ "pid":12345, "tid":12, "ts":1751453315840905, "dur":360, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751453315841266, "dur":390, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751453315841657, "dur":550, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751453315842207, "dur":302, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751453315842509, "dur":289, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751453315842798, "dur":147, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751453315842945, "dur":1232, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751453315844214, "dur":438, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751453315844693, "dur":4111, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.XR.Management.pdb" }}
,{ "pid":12345, "tid":12, "ts":1751453315848806, "dur":56, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":12, "ts":1751453315848916, "dur":3726, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":12, "ts":1751453315852644, "dur":66549, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751453315919194, "dur":1571, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751453315920765, "dur":1428616, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751453315835299, "dur":4578, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751453315839889, "dur":86, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.GameCenterModule.dll" }}
,{ "pid":12345, "tid":13, "ts":1751453315839977, "dur":368, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":13, "ts":1751453315839880, "dur":466, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_095932F05776E393.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1751453315840388, "dur":92, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.TextCoreFontEngineModule.dll" }}
,{ "pid":12345, "tid":13, "ts":1751453315840386, "dur":97, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_76022B412CAC8CD9.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1751453315840532, "dur":70, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.WindModule.dll" }}
,{ "pid":12345, "tid":13, "ts":1751453315840531, "dur":74, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_E6B23F81BE720824.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1751453315840662, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751453315840803, "dur":165, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":13, "ts":1751453315840969, "dur":343, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751453315841312, "dur":455, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751453315841768, "dur":262, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751453315842030, "dur":275, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751453315842306, "dur":294, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751453315842600, "dur":357, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751453315842957, "dur":1203, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751453315844205, "dur":60, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1300b0aP.dag/Unity.Mathematics.dll.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1751453315844287, "dur":391, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.Mathematics.dll (+2 others)" }}
,{ "pid":12345, "tid":13, "ts":1751453315844802, "dur":4229, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Timeline.dll" }}
,{ "pid":12345, "tid":13, "ts":1751453315849035, "dur":458, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751453315849509, "dur":4039, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.XR.CoreUtils.dll" }}
,{ "pid":12345, "tid":13, "ts":1751453315853550, "dur":1495832, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751453315835318, "dur":4568, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751453315839901, "dur":88, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.GIModule.dll" }}
,{ "pid":12345, "tid":14, "ts":1751453315839991, "dur":389, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":14, "ts":1751453315839890, "dur":491, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_8BE6FA01756CA2FE.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1751453315840402, "dur":80, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.TilemapModule.dll" }}
,{ "pid":12345, "tid":14, "ts":1751453315840400, "dur":84, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_FBB6CCFDFFC06D56.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1751453315840505, "dur":70, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.VFXModule.dll" }}
,{ "pid":12345, "tid":14, "ts":1751453315840504, "dur":73, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_17D20171469A8DE0.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1751453315840619, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751453315840756, "dur":67, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.rsp" }}
,{ "pid":12345, "tid":14, "ts":1751453315840882, "dur":290, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751453315841172, "dur":453, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751453315841626, "dur":294, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751453315841920, "dur":654, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751453315842574, "dur":265, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751453315842839, "dur":954, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751453315843794, "dur":412, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751453315845083, "dur":4479, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/UnityEngine.TestRunner.dll" }}
,{ "pid":12345, "tid":14, "ts":1751453315849564, "dur":84, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.dll.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1751453315849670, "dur":93, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751453315849773, "dur":4509, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.pdb" }}
,{ "pid":12345, "tid":14, "ts":1751453315854284, "dur":1495008, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751453315835348, "dur":4546, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751453315839905, "dur":82, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.GridModule.dll" }}
,{ "pid":12345, "tid":15, "ts":1751453315839989, "dur":380, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":15, "ts":1751453315839897, "dur":473, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_4DD4804745901FB2.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1751453315840395, "dur":172, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.TextCoreTextEngineModule.dll" }}
,{ "pid":12345, "tid":15, "ts":1751453315840393, "dur":176, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_BDEA61B2BC3045F7.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1751453315840622, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751453315840727, "dur":235, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.XR.ARFoundation.rsp2" }}
,{ "pid":12345, "tid":15, "ts":1751453315840963, "dur":345, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751453315841309, "dur":356, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751453315841665, "dur":311, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751453315841976, "dur":281, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751453315842257, "dur":259, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751453315842517, "dur":283, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751453315842800, "dur":54, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751453315842854, "dur":387, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751453315843241, "dur":927, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751453315844215, "dur":444, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751453315844728, "dur":4068, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/UnityEngine.SpatialTracking.dll" }}
,{ "pid":12345, "tid":15, "ts":1751453315848882, "dur":3270, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":15, "ts":1751453315852207, "dur":2479, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.XR.ARSubsystems.pdb" }}
,{ "pid":12345, "tid":15, "ts":1751453315854687, "dur":1494706, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751453315835379, "dur":4522, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751453315839913, "dur":85, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.HotReloadModule.dll" }}
,{ "pid":12345, "tid":16, "ts":1751453315839999, "dur":392, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":16, "ts":1751453315839904, "dur":488, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_F8042116F018645E.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1751453315840462, "dur":80, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.UnityTestProtocolModule.dll" }}
,{ "pid":12345, "tid":16, "ts":1751453315840460, "dur":84, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_48C7ED199519B3B7.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1751453315840664, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751453315840723, "dur":197, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":16, "ts":1751453315840921, "dur":384, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751453315841306, "dur":467, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751453315841773, "dur":261, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751453315842034, "dur":254, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751453315842288, "dur":269, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751453315842557, "dur":260, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751453315842834, "dur":877, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751453315843712, "dur":453, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751453315844166, "dur":63, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751453315844229, "dur":427, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751453315844677, "dur":111, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751453315844788, "dur":344, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751453315845193, "dur":4407, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/UnityEngine.XR.LegacyInputHelpers.dll" }}
,{ "pid":12345, "tid":16, "ts":1751453315849604, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751453315849687, "dur":4348, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.XR.ARFoundation.pdb" }}
,{ "pid":12345, "tid":16, "ts":1751453315854036, "dur":1495353, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751453315835457, "dur":4452, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751453315839920, "dur":76, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.IMGUIModule.dll" }}
,{ "pid":12345, "tid":17, "ts":1751453315839998, "dur":387, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":17, "ts":1751453315839912, "dur":474, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_DF7C5EFFD938A6AF.mvfrm" }}
,{ "pid":12345, "tid":17, "ts":1751453315840409, "dur":91, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.UIElementsModule.dll" }}
,{ "pid":12345, "tid":17, "ts":1751453315840407, "dur":95, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_61716D4A4A5A9D17.mvfrm" }}
,{ "pid":12345, "tid":17, "ts":1751453315840540, "dur":93, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.AccessibilityModule.dll" }}
,{ "pid":12345, "tid":17, "ts":1751453315840539, "dur":96, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_064C6E74359247B6.mvfrm" }}
,{ "pid":12345, "tid":17, "ts":1751453315840636, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751453315840709, "dur":58, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.dll.mvfrm" }}
,{ "pid":12345, "tid":17, "ts":1751453315840784, "dur":160, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.dll.mvfrm" }}
,{ "pid":12345, "tid":17, "ts":1751453315840985, "dur":78, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\mscorlib.dll" }}
,{ "pid":12345, "tid":17, "ts":1751453315841065, "dur":65, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll" }}
,{ "pid":12345, "tid":17, "ts":1751453315841131, "dur":91, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Core.dll" }}
,{ "pid":12345, "tid":17, "ts":1751453315841224, "dur":53, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Data.dll" }}
,{ "pid":12345, "tid":17, "ts":1751453315841278, "dur":50, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.dll" }}
,{ "pid":12345, "tid":17, "ts":1751453315841329, "dur":129, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Drawing.dll" }}
,{ "pid":12345, "tid":17, "ts":1751453315841460, "dur":69, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll" }}
,{ "pid":12345, "tid":17, "ts":1751453315841531, "dur":88, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll" }}
,{ "pid":12345, "tid":17, "ts":1751453315841621, "dur":92, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Numerics.dll" }}
,{ "pid":12345, "tid":17, "ts":1751453315841714, "dur":50, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll" }}
,{ "pid":12345, "tid":17, "ts":1751453315841766, "dur":86, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll" }}
,{ "pid":12345, "tid":17, "ts":1751453315841854, "dur":56, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll" }}
,{ "pid":12345, "tid":17, "ts":1751453315841911, "dur":54, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Web.dll" }}
,{ "pid":12345, "tid":17, "ts":1751453315842066, "dur":53, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Linq.dll" }}
,{ "pid":12345, "tid":17, "ts":1751453315842120, "dur":406, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll" }}
,{ "pid":12345, "tid":17, "ts":1751453315842538, "dur":62, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll" }}
,{ "pid":12345, "tid":17, "ts":1751453315842601, "dur":57, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll" }}
,{ "pid":12345, "tid":17, "ts":1751453315842659, "dur":75, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll" }}
,{ "pid":12345, "tid":17, "ts":1751453315842736, "dur":102, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll" }}
,{ "pid":12345, "tid":17, "ts":1751453315842842, "dur":77, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll" }}
,{ "pid":12345, "tid":17, "ts":1751453315842920, "dur":71, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll" }}
,{ "pid":12345, "tid":17, "ts":1751453315842992, "dur":53, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll" }}
,{ "pid":12345, "tid":17, "ts":1751453315843163, "dur":73, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll" }}
,{ "pid":12345, "tid":17, "ts":1751453315843243, "dur":55, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll" }}
,{ "pid":12345, "tid":17, "ts":1751453315843385, "dur":56, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Calendars.dll" }}
,{ "pid":12345, "tid":17, "ts":1751453315843442, "dur":61, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.dll" }}
,{ "pid":12345, "tid":17, "ts":1751453315843629, "dur":73, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll" }}
,{ "pid":12345, "tid":17, "ts":1751453315843735, "dur":55, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Extensions.dll" }}
,{ "pid":12345, "tid":17, "ts":1751453315840945, "dur":3152, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.dll (+2 others)" }}
,{ "pid":12345, "tid":17, "ts":1751453315844200, "dur":77, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.dll.mvfrm" }}
,{ "pid":12345, "tid":17, "ts":1751453315844294, "dur":990, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.dll (+2 others)" }}
,{ "pid":12345, "tid":17, "ts":1751453315845362, "dur":69, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1300b0aP.dag/Unity.XR.ARSubsystems.dll.mvfrm" }}
,{ "pid":12345, "tid":17, "ts":1751453315845449, "dur":374, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.XR.ARSubsystems.dll (+2 others)" }}
,{ "pid":12345, "tid":17, "ts":1751453315845887, "dur":54, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1300b0aP.dag/Unity.XR.ARFoundation.InternalUtils.dll.mvfrm" }}
,{ "pid":12345, "tid":17, "ts":1751453315845958, "dur":286, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.XR.ARFoundation.InternalUtils.dll (+2 others)" }}
,{ "pid":12345, "tid":17, "ts":1751453315846293, "dur":50, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1300b0aP.dag/Unity.XR.ARFoundation.dll.mvfrm" }}
,{ "pid":12345, "tid":17, "ts":1751453315846362, "dur":395, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.XR.ARFoundation.dll (+2 others)" }}
,{ "pid":12345, "tid":17, "ts":1751453315846822, "dur":73, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1300b0aP.dag/Unity.XR.ARCore.dll.mvfrm" }}
,{ "pid":12345, "tid":17, "ts":1751453315846918, "dur":66, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.dll.mvfrm" }}
,{ "pid":12345, "tid":17, "ts":1751453315847010, "dur":381, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.dll (+2 others)" }}
,{ "pid":12345, "tid":17, "ts":1751453315847446, "dur":374, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.XR.ARCore.dll (+2 others)" }}
,{ "pid":12345, "tid":17, "ts":1751453315847959, "dur":2847, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.XR.ARCore.dll" }}
,{ "pid":12345, "tid":17, "ts":1751453315850811, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751453315850895, "dur":3600, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Assembly-CSharp-firstpass.pdb" }}
,{ "pid":12345, "tid":17, "ts":1751453315854496, "dur":1494790, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751453315835481, "dur":4435, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751453315839929, "dur":79, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.InputLegacyModule.dll" }}
,{ "pid":12345, "tid":18, "ts":1751453315840009, "dur":330, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":18, "ts":1751453315839918, "dur":422, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_E2298BA8A543097B.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1751453315840401, "dur":83, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.TextRenderingModule.dll" }}
,{ "pid":12345, "tid":18, "ts":1751453315840399, "dur":88, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_A98B174000715497.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1751453315840508, "dur":66, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.VideoModule.dll" }}
,{ "pid":12345, "tid":18, "ts":1751453315840507, "dur":69, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_CC264403D5992E73.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1751453315840597, "dur":78, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_CC264403D5992E73.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1751453315840678, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751453315840763, "dur":86, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Mathematics.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":18, "ts":1751453315840937, "dur":331, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751453315841462, "dur":660, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\int4x3.gen.cs" }}
,{ "pid":12345, "tid":18, "ts":1751453315841269, "dur":1020, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751453315842289, "dur":317, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751453315842607, "dur":320, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751453315842927, "dur":1245, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751453315844205, "dur":64, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1300b0aP.dag/Unity.TextMeshPro.dll.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1751453315844293, "dur":363, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.TextMeshPro.dll (+2 others)" }}
,{ "pid":12345, "tid":18, "ts":1751453315844770, "dur":4301, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":18, "ts":1751453315849076, "dur":439, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751453315849545, "dur":3817, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.pdb" }}
,{ "pid":12345, "tid":18, "ts":1751453315853363, "dur":193944, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751453316047309, "dur":1301982, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751453315835509, "dur":4422, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751453315839941, "dur":69, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.ImageConversionModule.dll" }}
,{ "pid":12345, "tid":19, "ts":1751453315840011, "dur":320, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":19, "ts":1751453315839933, "dur":399, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_76B0E4EEAA7D29A9.mvfrm" }}
,{ "pid":12345, "tid":19, "ts":1751453315840375, "dur":71, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.TerrainPhysicsModule.dll" }}
,{ "pid":12345, "tid":19, "ts":1751453315840374, "dur":74, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_06A1075EA50E864E.mvfrm" }}
,{ "pid":12345, "tid":19, "ts":1751453315840467, "dur":68, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.UnityWebRequestAudioModule.dll" }}
,{ "pid":12345, "tid":19, "ts":1751453315840466, "dur":71, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_1DD1B9B75FF06412.mvfrm" }}
,{ "pid":12345, "tid":19, "ts":1751453315840632, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751453315840720, "dur":117, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.XR.ARFoundation.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":19, "ts":1751453315840891, "dur":100, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp-firstpass.rsp" }}
,{ "pid":12345, "tid":19, "ts":1751453315841588, "dur":534, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_TextElement.cs" }}
,{ "pid":12345, "tid":19, "ts":1751453315840991, "dur":1131, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751453315842122, "dur":309, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751453315842431, "dur":327, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751453315842758, "dur":297, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751453315843056, "dur":1107, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751453315844163, "dur":50, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751453315844213, "dur":442, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751453315844678, "dur":99, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751453315844822, "dur":4756, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Timeline.pdb" }}
,{ "pid":12345, "tid":19, "ts":1751453315849582, "dur":89, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751453315849682, "dur":4557, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.XR.ARFoundation.VisualScripting.pdb" }}
,{ "pid":12345, "tid":19, "ts":1751453315854240, "dur":1495136, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751453315835536, "dur":4404, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751453315839950, "dur":64, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.InputModule.dll" }}
,{ "pid":12345, "tid":20, "ts":1751453315840015, "dur":376, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":20, "ts":1751453315839941, "dur":451, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_0D928749893F3772.mvfrm" }}
,{ "pid":12345, "tid":20, "ts":1751453315840411, "dur":75, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.UIModule.dll" }}
,{ "pid":12345, "tid":20, "ts":1751453315840409, "dur":79, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_EF1E4F213604C090.mvfrm" }}
,{ "pid":12345, "tid":20, "ts":1751453315840511, "dur":85, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.VRModule.dll" }}
,{ "pid":12345, "tid":20, "ts":1751453315840510, "dur":88, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_41F075A6D7BF4608.mvfrm" }}
,{ "pid":12345, "tid":20, "ts":1751453315840640, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751453315840886, "dur":86, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.rsp2" }}
,{ "pid":12345, "tid":20, "ts":1751453315840973, "dur":356, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751453315841329, "dur":403, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751453315841732, "dur":269, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751453315842002, "dur":269, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751453315842271, "dur":290, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751453315842561, "dur":271, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751453315842832, "dur":256, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751453315843089, "dur":1085, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751453315844214, "dur":54, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.dll.mvfrm" }}
,{ "pid":12345, "tid":20, "ts":1751453315844288, "dur":428, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.dll (+2 others)" }}
,{ "pid":12345, "tid":20, "ts":1751453315844789, "dur":347, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751453315845140, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751453315845207, "dur":3895, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/UnityEngine.XR.LegacyInputHelpers.pdb" }}
,{ "pid":12345, "tid":20, "ts":1751453315849107, "dur":402, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751453315849521, "dur":4059, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.XR.ARCore.pdb" }}
,{ "pid":12345, "tid":20, "ts":1751453315853582, "dur":1495702, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751453317367628, "dur":1588, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 20040, "tid": 288657, "ts": 1751453317369297, "dur": 820, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 20040, "tid": 288657, "ts": 1751453317370152, "dur": 6236, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 20040, "tid": 288657, "ts": 1751453317368600, "dur": 7825, "ph": "X", "name": "Write chrome-trace events", "args": {} },
