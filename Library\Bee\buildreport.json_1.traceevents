{ "pid": 20040, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 20040, "tid": 1, "ts": 1751450378904766, "dur": 5166, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 20040, "tid": 1, "ts": 1751450378909936, "dur": 97586, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 20040, "tid": 1, "ts": 1751450379007526, "dur": 17740, "ph": "X", "name": "Write<PERSON><PERSON>", "args": {} },
{ "pid": 20040, "tid": 20231, "ts": 1751450380444646, "dur": 10, "ph": "X", "name": "", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378904745, "dur": 14871, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378919617, "dur": 1524630, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378919625, "dur": 66, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378919696, "dur": 260, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378919960, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378919991, "dur": 4, "ph": "X", "name": "ProcessMessages 41", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378919997, "dur": 2572, "ph": "X", "name": "ReadAsync 41", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378922574, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378922619, "dur": 1, "ph": "X", "name": "ProcessMessages 1551", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378922621, "dur": 37, "ph": "X", "name": "ReadAsync 1551", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378922663, "dur": 92, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378922758, "dur": 1, "ph": "X", "name": "ProcessMessages 1007", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378922760, "dur": 87, "ph": "X", "name": "ReadAsync 1007", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378922849, "dur": 1, "ph": "X", "name": "ProcessMessages 1260", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378922851, "dur": 27, "ph": "X", "name": "ReadAsync 1260", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378922880, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378922882, "dur": 29, "ph": "X", "name": "ReadAsync 441", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378922914, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378922916, "dur": 30, "ph": "X", "name": "ReadAsync 613", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378922949, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378922950, "dur": 64, "ph": "X", "name": "ReadAsync 365", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378923019, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378923060, "dur": 1, "ph": "X", "name": "ProcessMessages 2016", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378923062, "dur": 31, "ph": "X", "name": "ReadAsync 2016", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378923095, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378923162, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378923164, "dur": 89, "ph": "X", "name": "ReadAsync 575", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378923256, "dur": 2, "ph": "X", "name": "ProcessMessages 1758", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378923259, "dur": 33, "ph": "X", "name": "ReadAsync 1758", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378923294, "dur": 1, "ph": "X", "name": "ProcessMessages 1092", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378923296, "dur": 79, "ph": "X", "name": "ReadAsync 1092", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378923378, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378923380, "dur": 30, "ph": "X", "name": "ReadAsync 1", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378923413, "dur": 1, "ph": "X", "name": "ProcessMessages 887", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378923416, "dur": 79, "ph": "X", "name": "ReadAsync 887", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378923498, "dur": 40, "ph": "X", "name": "ReadAsync 649", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378923540, "dur": 1, "ph": "X", "name": "ProcessMessages 2083", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378923542, "dur": 29, "ph": "X", "name": "ReadAsync 2083", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378923575, "dur": 25, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378923604, "dur": 25, "ph": "X", "name": "ReadAsync 500", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378923631, "dur": 1, "ph": "X", "name": "ProcessMessages 559", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378923632, "dur": 22, "ph": "X", "name": "ReadAsync 559", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378923658, "dur": 26, "ph": "X", "name": "ReadAsync 324", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378923688, "dur": 22, "ph": "X", "name": "ReadAsync 580", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378923712, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378923713, "dur": 16, "ph": "X", "name": "ReadAsync 408", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378923732, "dur": 26, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378923762, "dur": 22, "ph": "X", "name": "ReadAsync 634", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378923787, "dur": 21, "ph": "X", "name": "ReadAsync 349", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378923810, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378923811, "dur": 23, "ph": "X", "name": "ReadAsync 442", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378923837, "dur": 23, "ph": "X", "name": "ReadAsync 639", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378923863, "dur": 25, "ph": "X", "name": "ReadAsync 264", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378923890, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378923892, "dur": 25, "ph": "X", "name": "ReadAsync 591", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378923919, "dur": 1, "ph": "X", "name": "ProcessMessages 742", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378923921, "dur": 25, "ph": "X", "name": "ReadAsync 742", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378923949, "dur": 23, "ph": "X", "name": "ReadAsync 473", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378923976, "dur": 23, "ph": "X", "name": "ReadAsync 322", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378924003, "dur": 30, "ph": "X", "name": "ReadAsync 437", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378924036, "dur": 23, "ph": "X", "name": "ReadAsync 417", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378924062, "dur": 22, "ph": "X", "name": "ReadAsync 457", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378924087, "dur": 29, "ph": "X", "name": "ReadAsync 233", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378924120, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378924148, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378924150, "dur": 23, "ph": "X", "name": "ReadAsync 586", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378924176, "dur": 26, "ph": "X", "name": "ReadAsync 395", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378924204, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378924206, "dur": 23, "ph": "X", "name": "ReadAsync 364", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378924231, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378924232, "dur": 20, "ph": "X", "name": "ReadAsync 540", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378924254, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378924283, "dur": 21, "ph": "X", "name": "ReadAsync 586", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378924306, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378924308, "dur": 27, "ph": "X", "name": "ReadAsync 322", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378924337, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378924339, "dur": 28, "ph": "X", "name": "ReadAsync 430", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378924370, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378924372, "dur": 20, "ph": "X", "name": "ReadAsync 538", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378924395, "dur": 42, "ph": "X", "name": "ReadAsync 75", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378924440, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378924463, "dur": 28, "ph": "X", "name": "ReadAsync 342", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378924495, "dur": 26, "ph": "X", "name": "ReadAsync 666", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378924523, "dur": 22, "ph": "X", "name": "ReadAsync 479", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378924548, "dur": 20, "ph": "X", "name": "ReadAsync 383", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378924571, "dur": 23, "ph": "X", "name": "ReadAsync 301", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378924598, "dur": 24, "ph": "X", "name": "ReadAsync 331", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378924625, "dur": 26, "ph": "X", "name": "ReadAsync 486", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378924653, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378924655, "dur": 26, "ph": "X", "name": "ReadAsync 461", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378924684, "dur": 22, "ph": "X", "name": "ReadAsync 503", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378924709, "dur": 31, "ph": "X", "name": "ReadAsync 380", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378924743, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378924774, "dur": 26, "ph": "X", "name": "ReadAsync 635", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378924804, "dur": 23, "ph": "X", "name": "ReadAsync 503", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378924896, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378924898, "dur": 117, "ph": "X", "name": "ReadAsync 359", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378925018, "dur": 2, "ph": "X", "name": "ProcessMessages 1912", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378925021, "dur": 64, "ph": "X", "name": "ReadAsync 1912", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378925103, "dur": 2, "ph": "X", "name": "ProcessMessages 2348", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378925106, "dur": 63, "ph": "X", "name": "ReadAsync 2348", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378925171, "dur": 1, "ph": "X", "name": "ProcessMessages 625", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378925173, "dur": 79, "ph": "X", "name": "ReadAsync 625", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378925254, "dur": 1, "ph": "X", "name": "ProcessMessages 1810", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378925257, "dur": 123, "ph": "X", "name": "ReadAsync 1810", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378925383, "dur": 46, "ph": "X", "name": "ReadAsync 378", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378925432, "dur": 2, "ph": "X", "name": "ProcessMessages 3140", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378925434, "dur": 23, "ph": "X", "name": "ReadAsync 3140", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378925461, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378925463, "dur": 36, "ph": "X", "name": "ReadAsync 354", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378925501, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378925503, "dur": 33, "ph": "X", "name": "ReadAsync 538", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378925539, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378925541, "dur": 63, "ph": "X", "name": "ReadAsync 541", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378925683, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378925685, "dur": 124, "ph": "X", "name": "ReadAsync 545", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378925811, "dur": 2, "ph": "X", "name": "ProcessMessages 2972", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378925814, "dur": 69, "ph": "X", "name": "ReadAsync 2972", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378925885, "dur": 1, "ph": "X", "name": "ProcessMessages 1963", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378925887, "dur": 73, "ph": "X", "name": "ReadAsync 1963", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378926029, "dur": 1, "ph": "X", "name": "ProcessMessages 1300", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378926032, "dur": 55, "ph": "X", "name": "ReadAsync 1300", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378926089, "dur": 65, "ph": "X", "name": "ProcessMessages 2780", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378926156, "dur": 80, "ph": "X", "name": "ReadAsync 2780", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378926237, "dur": 1, "ph": "X", "name": "ProcessMessages 1919", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378926239, "dur": 77, "ph": "X", "name": "ReadAsync 1919", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378926318, "dur": 1, "ph": "X", "name": "ProcessMessages 1542", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378926320, "dur": 37, "ph": "X", "name": "ReadAsync 1542", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378926359, "dur": 1, "ph": "X", "name": "ProcessMessages 1592", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378926361, "dur": 23, "ph": "X", "name": "ReadAsync 1592", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378926386, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378926388, "dur": 24, "ph": "X", "name": "ReadAsync 563", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378926414, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378926416, "dur": 25, "ph": "X", "name": "ReadAsync 367", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378926444, "dur": 1, "ph": "X", "name": "ProcessMessages 633", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378926446, "dur": 19, "ph": "X", "name": "ReadAsync 633", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378926523, "dur": 101, "ph": "X", "name": "ReadAsync 131", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378926625, "dur": 1, "ph": "X", "name": "ProcessMessages 1594", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378926627, "dur": 35, "ph": "X", "name": "ReadAsync 1594", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378926705, "dur": 2, "ph": "X", "name": "ProcessMessages 1664", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378926707, "dur": 40, "ph": "X", "name": "ReadAsync 1664", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378926749, "dur": 1, "ph": "X", "name": "ProcessMessages 1364", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378926751, "dur": 20, "ph": "X", "name": "ReadAsync 1364", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378926775, "dur": 27, "ph": "X", "name": "ReadAsync 135", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378926804, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378926866, "dur": 126, "ph": "X", "name": "ReadAsync 629", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378927064, "dur": 2, "ph": "X", "name": "ProcessMessages 2208", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378927067, "dur": 65, "ph": "X", "name": "ReadAsync 2208", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378927134, "dur": 3, "ph": "X", "name": "ProcessMessages 4408", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378927138, "dur": 22, "ph": "X", "name": "ReadAsync 4408", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378927163, "dur": 23, "ph": "X", "name": "ReadAsync 451", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378927187, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378927189, "dur": 30, "ph": "X", "name": "ReadAsync 415", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378927222, "dur": 20, "ph": "X", "name": "ReadAsync 538", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378927244, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378927246, "dur": 108, "ph": "X", "name": "ReadAsync 176", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378927356, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378927358, "dur": 310, "ph": "X", "name": "ReadAsync 580", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378927738, "dur": 3, "ph": "X", "name": "ProcessMessages 1943", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378927833, "dur": 44, "ph": "X", "name": "ReadAsync 1943", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378927881, "dur": 3, "ph": "X", "name": "ProcessMessages 288", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378927886, "dur": 37, "ph": "X", "name": "ReadAsync 288", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378927927, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378927930, "dur": 32, "ph": "X", "name": "ReadAsync 128", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378927964, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378927967, "dur": 30, "ph": "X", "name": "ReadAsync 144", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378928000, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378928003, "dur": 125, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378928131, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378928134, "dur": 116, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378928253, "dur": 4, "ph": "X", "name": "ProcessMessages 480", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378928259, "dur": 44, "ph": "X", "name": "ReadAsync 480", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378928306, "dur": 4, "ph": "X", "name": "ProcessMessages 576", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378928312, "dur": 35, "ph": "X", "name": "ReadAsync 576", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378928349, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378928352, "dur": 25, "ph": "X", "name": "ReadAsync 192", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378928379, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378928382, "dur": 29, "ph": "X", "name": "ReadAsync 176", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378928414, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378928417, "dur": 119, "ph": "X", "name": "ReadAsync 112", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378928539, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378928542, "dur": 2980, "ph": "X", "name": "ReadAsync 160", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378931526, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378931528, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378931552, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378931578, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378931610, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378931612, "dur": 66, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378931682, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378931716, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378931718, "dur": 69, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378931792, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378931821, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378931823, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378931848, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378931851, "dur": 143, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378931999, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378932029, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378932030, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378932058, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378932159, "dur": 36, "ph": "X", "name": "ReadAsync 76", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378932198, "dur": 6, "ph": "X", "name": "ProcessMessages 216", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378932206, "dur": 58, "ph": "X", "name": "ReadAsync 216", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378932269, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378932303, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378932305, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378932336, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378932339, "dur": 27, "ph": "X", "name": "ReadAsync 60", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378932370, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378932372, "dur": 100, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378932476, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378932478, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378932509, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378932511, "dur": 121, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378932636, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378932639, "dur": 130, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378932772, "dur": 2, "ph": "X", "name": "ProcessMessages 108", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378932774, "dur": 215, "ph": "X", "name": "ReadAsync 108", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378932994, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378933032, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378933067, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378933069, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378933101, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378933102, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378933143, "dur": 241, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378933388, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378933414, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378933546, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378933570, "dur": 252, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378933826, "dur": 125, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378933955, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378933957, "dur": 150, "ph": "X", "name": "ReadAsync 84", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378934110, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378934112, "dur": 228, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378934345, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378934379, "dur": 391, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378934773, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378934775, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378934810, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378934813, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378934846, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378934849, "dur": 189, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378935244, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378935247, "dur": 249, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378935500, "dur": 3, "ph": "X", "name": "ProcessMessages 88", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378935505, "dur": 33, "ph": "X", "name": "ReadAsync 88", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378935542, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378935545, "dur": 290, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378935840, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378935876, "dur": 132, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378936115, "dur": 177, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378936402, "dur": 260, "ph": "X", "name": "ProcessMessages 568", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378936665, "dur": 50, "ph": "X", "name": "ReadAsync 568", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378936816, "dur": 429, "ph": "X", "name": "ProcessMessages 416", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378937447, "dur": 353, "ph": "X", "name": "ReadAsync 416", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378937804, "dur": 319, "ph": "X", "name": "ProcessMessages 142", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378938127, "dur": 527, "ph": "X", "name": "ReadAsync 142", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378938763, "dur": 671, "ph": "X", "name": "ProcessMessages 132", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378939438, "dur": 306, "ph": "X", "name": "ReadAsync 132", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378939995, "dur": 762, "ph": "X", "name": "ProcessMessages 66", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378940761, "dur": 126, "ph": "X", "name": "ReadAsync 66", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378940915, "dur": 196, "ph": "X", "name": "ProcessMessages 244", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378941114, "dur": 153, "ph": "X", "name": "ReadAsync 244", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378941272, "dur": 208, "ph": "X", "name": "ProcessMessages 322", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378941482, "dur": 77, "ph": "X", "name": "ReadAsync 322", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378941611, "dur": 113, "ph": "X", "name": "ProcessMessages 138", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378941727, "dur": 102, "ph": "X", "name": "ReadAsync 138", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378941867, "dur": 48, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378942000, "dur": 177, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378942180, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378942182, "dur": 155, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378942341, "dur": 129, "ph": "X", "name": "ProcessMessages 126", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378942473, "dur": 41, "ph": "X", "name": "ReadAsync 126", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378942555, "dur": 200, "ph": "X", "name": "ProcessMessages 138", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450378942758, "dur": 216005, "ph": "X", "name": "ReadAsync 138", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450379158772, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450379158775, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450379158800, "dur": 2960, "ph": "X", "name": "ProcessMessages 471", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450379161762, "dur": 3468, "ph": "X", "name": "ReadAsync 471", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450379165236, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450379165238, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450379165276, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450379165279, "dur": 709, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450379165993, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450379166024, "dur": 15, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450379166041, "dur": 46691, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450379212739, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450379212744, "dur": 98, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450379212845, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450379212849, "dur": 632, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450379213486, "dur": 125, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450379213614, "dur": 16, "ph": "X", "name": "ProcessMessages 50", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450379213631, "dur": 737155, "ph": "X", "name": "ReadAsync 50", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450379950795, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450379950799, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450379950867, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450379950871, "dur": 473356, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450380424233, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450380424236, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450380424311, "dur": 22, "ph": "X", "name": "ProcessMessages 3577", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450380424335, "dur": 41, "ph": "X", "name": "ReadAsync 3577", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450380424380, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450380424382, "dur": 695, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450380425082, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450380425103, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 20040, "tid": 25769803776, "ts": 1751450380425105, "dur": 19137, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 20040, "tid": 20231, "ts": 1751450380444658, "dur": 487, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 20040, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 20040, "tid": 21474836480, "ts": 1751450378904712, "dur": 120612, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 20040, "tid": 21474836480, "ts": 1751450379025326, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 20040, "tid": 21474836480, "ts": 1751450379025328, "dur": 143, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 20040, "tid": 20231, "ts": 1751450380445148, "dur": 6, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 20040, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 20040, "tid": 17179869184, "ts": 1751450378903038, "dur": 1541250, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 20040, "tid": 17179869184, "ts": 1751450378903112, "dur": 1576, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 20040, "tid": 17179869184, "ts": 1751450380444292, "dur": 53, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 20040, "tid": 17179869184, "ts": 1751450380444311, "dur": 12, "ph": "X", "name": "await ScriptUpdaters", "args": {} },
{ "pid": 20040, "tid": 20231, "ts": 1751450380445156, "dur": 6, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1751450378920437, "dur":1782, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751450378922226, "dur":590, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751450378922887, "dur":470, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751450378923442, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_76BB631114E548DC.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751450378923539, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_0E051C93ED23F1B2.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751450378923629, "dur":80, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_B314153F1DBA0033.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751450378923893, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_0D928749893F3772.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751450378924033, "dur":70, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_4273EA0E1245435F.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751450378924127, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_1B45C2772CBE133A.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751450378924363, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_76022B412CAC8CD9.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751450378927934, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp-firstpass.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1751450378923375, "dur":4879, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751450378928258, "dur":1496971, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751450380425230, "dur":342, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751450380425572, "dur":117, "ph":"X", "name": "ThreadStateDestroy",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751450380425736, "dur":66, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751450380425931, "dur":14631, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1751450378923606, "dur":4690, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751450378928306, "dur":64, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.AudioModule.dll" }}
,{ "pid":12345, "tid":1, "ts":1751450378928371, "dur":290, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":1, "ts":1751450378928299, "dur":364, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_A71CBF99130712CE.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1751450378928698, "dur":55, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.SubstanceModule.dll" }}
,{ "pid":12345, "tid":1, "ts":1751450378928696, "dur":59, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_07FB64AAAFBFFE28.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1751450378928789, "dur":78, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.UnityConnectModule.dll" }}
,{ "pid":12345, "tid":1, "ts":1751450378928788, "dur":81, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_A401D76D980215F1.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1751450378928978, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751450378929150, "dur":140, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Mathematics.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":1, "ts":1751450378929291, "dur":322, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751450378929613, "dur":538, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751450378930151, "dur":273, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751450378930424, "dur":312, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751450378930736, "dur":277, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751450378931013, "dur":320, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751450378931334, "dur":1066, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751450378932451, "dur":82, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1300b0aP.dag/Unity.XR.Management.dll.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1751450378932551, "dur":263, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.XR.Management.dll (+2 others)" }}
,{ "pid":12345, "tid":1, "ts":1751450378933009, "dur":4389, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Timeline.pdb" }}
,{ "pid":12345, "tid":1, "ts":1751450378937467, "dur":4443, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.XR.ARFoundation.VisualScripting.pdb" }}
,{ "pid":12345, "tid":1, "ts":1751450378941911, "dur":1483256, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751450378923341, "dur":4922, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751450378928280, "dur":65, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.AndroidJNIModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1751450378928347, "dur":127, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":2, "ts":1751450378928272, "dur":203, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_DF0BE8D59135F2A2.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1751450378928523, "dur":56, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.JSONSerializeModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1751450378928521, "dur":60, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_6663F7B034CDA840.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1751450378928613, "dur":58, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.PropertiesModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1751450378928612, "dur":62, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_3694722ECA6247CF.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1751450378928757, "dur":72, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.UIElementsModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1751450378928756, "dur":85, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_61716D4A4A5A9D17.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1751450378928868, "dur":71, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.AccessibilityModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1751450378928866, "dur":76, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_064C6E74359247B6.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1751450378928988, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751450378929099, "dur":119, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.rsp2" }}
,{ "pid":12345, "tid":2, "ts":1751450378929277, "dur":289, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751450378929566, "dur":421, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751450378929987, "dur":303, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751450378930290, "dur":428, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751450378930719, "dur":293, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751450378931012, "dur":437, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751450378931450, "dur":955, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751450378932405, "dur":56, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751450378932462, "dur":397, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751450378932881, "dur":52, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751450378932972, "dur":3825, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":2, "ts":1751450378936801, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751450378936871, "dur":3887, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":2, "ts":1751450378940816, "dur":2278, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.XR.ARSubsystems.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751450378943095, "dur":1482135, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751450378923539, "dur":4744, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751450378928294, "dur":65, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.ARModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1751450378928360, "dur":301, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":3, "ts":1751450378928286, "dur":375, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_AD1D75F736DB19AD.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1751450378928709, "dur":63, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.TerrainModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1751450378928707, "dur":67, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_5CBA657CE8038C4D.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1751450378928799, "dur":82, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.UnityWebRequestAssetBundleModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1751450378928797, "dur":87, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_9506B0100723864E.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1751450378928981, "dur":64, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.XR.Management.rsp2" }}
,{ "pid":12345, "tid":3, "ts":1751450378929242, "dur":336, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751450378929578, "dur":420, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751450378929998, "dur":307, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751450378930305, "dur":273, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751450378930579, "dur":315, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751450378930895, "dur":315, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751450378931211, "dur":854, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751450378932066, "dur":341, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751450378932457, "dur":401, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751450378932880, "dur":56, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751450378932969, "dur":155, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751450378933125, "dur":205, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751450378933396, "dur":4113, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/UnityEngine.XR.LegacyInputHelpers.pdb" }}
,{ "pid":12345, "tid":3, "ts":1751450378937513, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751450378937593, "dur":4706, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.XR.ARFoundation.pdb" }}
,{ "pid":12345, "tid":3, "ts":1751450378942300, "dur":1482882, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751450378923416, "dur":4859, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751450378928288, "dur":68, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.AnimationModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1751450378928357, "dur":229, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":4, "ts":1751450378928278, "dur":308, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_76BB631114E548DC.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1751450378928621, "dur":54, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1751450378928619, "dur":58, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_1B45C2772CBE133A.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1751450378928741, "dur":65, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.TilemapModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1751450378928739, "dur":69, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_FBB6CCFDFFC06D56.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1751450378928808, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751450378928904, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751450378929007, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751450378929279, "dur":548, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751450378929827, "dur":349, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751450378930177, "dur":284, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751450378930461, "dur":310, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751450378930771, "dur":264, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751450378931035, "dur":336, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751450378931371, "dur":1020, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751450378932743, "dur":4158, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":4, "ts":1751450378936959, "dur":4700, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":4, "ts":1751450378941661, "dur":86337, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751450379027999, "dur":1397171, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751450378923572, "dur":4717, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751450378928300, "dur":64, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.AssetBundleModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1751450378928365, "dur":258, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":5, "ts":1751450378928292, "dur":332, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_50E4708A0D51538A.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1751450378928675, "dur":60, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.SpriteShapeModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1751450378928673, "dur":67, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_6A459DE4C2F38B94.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1751450378928763, "dur":70, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.UIModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1751450378928762, "dur":73, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_EF1E4F213604C090.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1751450378928871, "dur":64, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.AIModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1751450378928870, "dur":68, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_F3AAB0F3D9BDBAE4.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1751450378928970, "dur":90, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.XR.ARSubsystems.rsp2" }}
,{ "pid":12345, "tid":5, "ts":1751450378929147, "dur":81, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":5, "ts":1751450378929229, "dur":287, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751450378929516, "dur":452, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751450378929968, "dur":380, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751450378930349, "dur":332, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751450378930681, "dur":359, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751450378931041, "dur":287, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751450378931328, "dur":1066, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751450378932394, "dur":56, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751450378932456, "dur":55, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1300b0aP.dag/Unity.TextMeshPro.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1751450378932534, "dur":339, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.TextMeshPro.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1751450378932973, "dur":149, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751450378933168, "dur":4246, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":5, "ts":1751450378937484, "dur":4671, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.pdb" }}
,{ "pid":12345, "tid":5, "ts":1751450378942157, "dur":1483036, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751450378923663, "dur":4649, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751450378928325, "dur":75, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.ContentLoadModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378928401, "dur":286, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":6, "ts":1751450378928315, "dur":373, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_E9FAC9CADBD3FB77.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1751450378928734, "dur":66, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.TextCoreTextEngineModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378928732, "dur":71, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_BDEA61B2BC3045F7.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1751450378928843, "dur":95, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.VideoModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378928842, "dur":99, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_CC264403D5992E73.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1751450378929019, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751450378929228, "dur":82, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1300b0aP.dag/UnityEngine.TestRunner.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1751450378929333, "dur":58, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Core.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378929392, "dur":51, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Data.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378929444, "dur":50, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378929495, "dur":57, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Drawing.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378929602, "dur":182, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378929786, "dur":71, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Numerics.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378929859, "dur":71, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378929932, "dur":55, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378929988, "dur":58, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378930048, "dur":73, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Web.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378930123, "dur":146, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Windows.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378930271, "dur":60, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378930383, "dur":63, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378930497, "dur":119, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378930635, "dur":120, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378930756, "dur":54, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378930811, "dur":56, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378930868, "dur":50, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378930968, "dur":50, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378931020, "dur":55, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378931129, "dur":52, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378931182, "dur":58, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378931242, "dur":66, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378931309, "dur":82, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378931392, "dur":58, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378931452, "dur":55, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378931517, "dur":53, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378931571, "dur":54, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Drawing.Primitives.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378931626, "dur":54, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378931681, "dur":58, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Calendars.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378931740, "dur":54, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378931795, "dur":53, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Extensions.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378931881, "dur":64, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378931946, "dur":53, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378932000, "dur":50, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Http.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378929329, "dur":3068, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/UnityEngine.TestRunner.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1751450378933159, "dur":4261, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/UnityEngine.TestRunner.dll" }}
,{ "pid":12345, "tid":6, "ts":1751450378937471, "dur":4389, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.XR.ARCore.pdb" }}
,{ "pid":12345, "tid":6, "ts":1751450378941862, "dur":1483312, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751450378923635, "dur":4670, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751450378928315, "dur":70, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.ClothModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1751450378928386, "dur":289, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":7, "ts":1751450378928308, "dur":367, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_0E051C93ED23F1B2.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1751450378928733, "dur":66, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.TextCoreFontEngineModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1751450378928731, "dur":70, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_76022B412CAC8CD9.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1751450378928837, "dur":60, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.VFXModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1751450378928836, "dur":63, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_17D20171469A8DE0.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1751450378929052, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751450378929236, "dur":65, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.XR.ARFoundation.VisualScripting.rsp" }}
,{ "pid":12345, "tid":7, "ts":1751450378929301, "dur":324, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751450378929625, "dur":458, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751450378930083, "dur":315, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751450378930398, "dur":311, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751450378930710, "dur":302, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751450378931012, "dur":319, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751450378931332, "dur":1067, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751450378932399, "dur":57, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751450378932456, "dur":427, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751450378932883, "dur":51, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751450378932973, "dur":3960, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.pdb" }}
,{ "pid":12345, "tid":7, "ts":1751450378936937, "dur":420, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751450378937369, "dur":4477, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.XR.ARFoundation.InternalUtils.dll" }}
,{ "pid":12345, "tid":7, "ts":1751450378941848, "dur":1483321, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751450378923749, "dur":4570, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751450378928332, "dur":75, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.CoreModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1751450378928409, "dur":289, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":8, "ts":1751450378928322, "dur":376, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_706364592D3B89BB.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1751450378928737, "dur":65, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.TextRenderingModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1751450378928735, "dur":69, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_A98B174000715497.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1751450378928848, "dur":59, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.VRModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1751450378928846, "dur":63, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_41F075A6D7BF4608.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1751450378929002, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751450378929143, "dur":65, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.XR.ARCore.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":8, "ts":1751450378929257, "dur":320, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751450378929577, "dur":504, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751450378930082, "dur":275, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751450378930357, "dur":290, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751450378930647, "dur":278, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751450378930926, "dur":326, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751450378931252, "dur":1145, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751450378932397, "dur":52, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751450378932450, "dur":80, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1300b0aP.dag/Unity.Mathematics.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1751450378932531, "dur":103, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751450378932640, "dur":417, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.Mathematics.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1751450378933058, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751450378933125, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751450378933189, "dur":140, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751450378933377, "dur":4068, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/UnityEngine.XR.LegacyInputHelpers.dll" }}
,{ "pid":12345, "tid":8, "ts":1751450378937449, "dur":135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751450378937595, "dur":4537, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.ForUI.pdb" }}
,{ "pid":12345, "tid":8, "ts":1751450378942133, "dur":1483048, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751450378923843, "dur":4482, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751450378928336, "dur":85, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.CrashReportingModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1751450378928422, "dur":289, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":9, "ts":1751450378928327, "dur":385, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_B314153F1DBA0033.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1751450378928777, "dur":98, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.UmbraModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1751450378928775, "dur":103, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_21F16BB1A02DBE41.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1751450378928967, "dur":86, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751450378929223, "dur":360, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751450378929583, "dur":429, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751450378930012, "dur":340, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751450378930352, "dur":285, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751450378930637, "dur":263, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751450378930900, "dur":441, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751450378931342, "dur":1061, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751450378932403, "dur":51, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751450378932454, "dur":407, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751450378932935, "dur":3916, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/UnityEngine.SpatialTracking.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751450378936943, "dur":4967, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.XR.ARFoundation.dll" }}
,{ "pid":12345, "tid":9, "ts":1751450378941912, "dur":1483320, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751450378923866, "dur":4466, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751450378928344, "dur":79, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.DirectorModule.dll" }}
,{ "pid":12345, "tid":10, "ts":1751450378928424, "dur":315, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":10, "ts":1751450378928335, "dur":405, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_BD6ADC004E16EE98.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1751450378928769, "dur":81, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_BD6ADC004E16EE98.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1751450378928852, "dur":89, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.WindModule.dll" }}
,{ "pid":12345, "tid":10, "ts":1751450378928851, "dur":92, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_E6B23F81BE720824.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1751450378928973, "dur":57, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_E6B23F81BE720824.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1751450378929038, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751450378929166, "dur":120, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.rsp" }}
,{ "pid":12345, "tid":10, "ts":1751450378929287, "dur":357, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751450378929644, "dur":519, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751450378930164, "dur":303, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751450378930467, "dur":308, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751450378930775, "dur":324, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751450378931100, "dur":261, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751450378931362, "dur":1034, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751450378932396, "dur":51, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751450378932448, "dur":62, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1300b0aP.dag/UnityEngine.SpatialTracking.dll.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1751450378932532, "dur":278, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/UnityEngine.SpatialTracking.dll (+2 others)" }}
,{ "pid":12345, "tid":10, "ts":1751450378932922, "dur":74, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1300b0aP.dag/UnityEngine.XR.LegacyInputHelpers.dll.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1751450378933014, "dur":264, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/UnityEngine.XR.LegacyInputHelpers.dll (+2 others)" }}
,{ "pid":12345, "tid":10, "ts":1751450378933372, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751450378933504, "dur":4067, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.pdb" }}
,{ "pid":12345, "tid":10, "ts":1751450378937615, "dur":4848, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.pdb" }}
,{ "pid":12345, "tid":10, "ts":1751450378942465, "dur":1482726, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751450378923917, "dur":4430, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751450378928361, "dur":92, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.DSPGraphModule.dll" }}
,{ "pid":12345, "tid":11, "ts":1751450378928455, "dur":311, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":11, "ts":1751450378928350, "dur":416, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_C2BC3D69C077BFB9.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1751450378928807, "dur":130, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.UnityWebRequestWWWModule.dll" }}
,{ "pid":12345, "tid":11, "ts":1751450378928805, "dur":136, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_88ACA70EABF00263.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1751450378928992, "dur":103, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751450378929164, "dur":55, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.rsp" }}
,{ "pid":12345, "tid":11, "ts":1751450378929235, "dur":107, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.rsp" }}
,{ "pid":12345, "tid":11, "ts":1751450378929343, "dur":282, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751450378929626, "dur":521, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751450378930148, "dur":285, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751450378930433, "dur":303, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751450378930736, "dur":338, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751450378931112, "dur":884, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751450378931996, "dur":404, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751450378932401, "dur":54, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751450378932455, "dur":401, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751450378932888, "dur":3958, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.XR.Management.dll" }}
,{ "pid":12345, "tid":11, "ts":1751450378936932, "dur":4294, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.ForUI.dll" }}
,{ "pid":12345, "tid":11, "ts":1751450378941228, "dur":85090, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751450379026319, "dur":1643, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751450379027963, "dur":1397269, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751450378923891, "dur":4449, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751450378928354, "dur":75, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.dll" }}
,{ "pid":12345, "tid":12, "ts":1751450378928430, "dur":295, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":12, "ts":1751450378928343, "dur":383, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_A2E8D4FF6A11D2EA.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1751450378928786, "dur":84, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.UnityAnalyticsCommonModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1751450378928784, "dur":89, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_4114231CF36E37D8.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1751450378928976, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751450378929084, "dur":98, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751450378929245, "dur":522, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751450378929767, "dur":341, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751450378930108, "dur":279, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751450378930388, "dur":320, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751450378930709, "dur":283, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751450378930992, "dur":265, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751450378931257, "dur":1135, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751450378932904, "dur":3919, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.pdb" }}
,{ "pid":12345, "tid":12, "ts":1751450378936825, "dur":99, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.dll.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1751450378936950, "dur":408, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751450378937374, "dur":4445, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.XR.CoreUtils.dll" }}
,{ "pid":12345, "tid":12, "ts":1751450378941821, "dur":272512, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751450379214334, "dur":1210846, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751450378923941, "dur":4413, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751450378928369, "dur":81, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.GameCenterModule.dll" }}
,{ "pid":12345, "tid":13, "ts":1751450378928451, "dur":296, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":13, "ts":1751450378928357, "dur":391, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_095932F05776E393.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1751450378928787, "dur":69, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.UnityAnalyticsModule.dll" }}
,{ "pid":12345, "tid":13, "ts":1751450378928785, "dur":73, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_25C5CF5537F2DDBD.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1751450378928996, "dur":126, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751450378929251, "dur":351, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751450378929603, "dur":429, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751450378930033, "dur":362, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751450378930396, "dur":315, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751450378930711, "dur":346, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751450378931057, "dur":382, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751450378931440, "dur":969, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751450378932409, "dur":56, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751450378932466, "dur":423, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751450378932967, "dur":158, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751450378933126, "dur":206, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751450378933372, "dur":102, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751450378933508, "dur":5118, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.dll" }}
,{ "pid":12345, "tid":13, "ts":1751450378938670, "dur":4307, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Assembly-CSharp-firstpass.dll" }}
,{ "pid":12345, "tid":13, "ts":1751450378942997, "dur":1482186, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751450378924027, "dur":4349, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751450378928388, "dur":84, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.HotReloadModule.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378928379, "dur":96, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_F8042116F018645E.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1751450378928530, "dur":63, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.ParticleSystemModule.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378928529, "dur":65, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_06265E8B8BCB8BC1.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1751450378928622, "dur":52, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_804BA5354939D4A2.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1751450378928710, "dur":54, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.TerrainPhysicsModule.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378928709, "dur":58, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_06A1075EA50E864E.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1751450378928805, "dur":137, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.UnityWebRequestModule.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378928804, "dur":140, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_884FEC98B571C428.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1751450378928981, "dur":56, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_884FEC98B571C428.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1751450378929040, "dur":101, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.dll.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1751450378929193, "dur":50, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\mscorlib.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378929244, "dur":55, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378929300, "dur":55, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Core.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378929357, "dur":53, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Data.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378929411, "dur":54, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378929467, "dur":58, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Drawing.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378929527, "dur":62, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378929590, "dur":193, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378929784, "dur":68, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Numerics.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378929854, "dur":104, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378929960, "dur":50, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378930012, "dur":52, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378930065, "dur":60, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Web.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378930126, "dur":99, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Windows.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378930226, "dur":73, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378930301, "dur":56, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Linq.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378930358, "dur":53, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378930414, "dur":59, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378930474, "dur":70, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378930546, "dur":61, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Buffers.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378930608, "dur":106, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378930715, "dur":56, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378930772, "dur":53, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378930827, "dur":53, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378930881, "dur":51, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378930934, "dur":56, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378930991, "dur":57, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378931049, "dur":55, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378931106, "dur":50, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378931157, "dur":83, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378931242, "dur":72, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378931316, "dur":80, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378931397, "dur":57, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378931455, "dur":58, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378931519, "dur":55, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378931575, "dur":53, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Drawing.Primitives.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378931629, "dur":50, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378931680, "dur":58, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Calendars.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378931740, "dur":53, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378931794, "dur":54, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Extensions.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378931882, "dur":60, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378931943, "dur":55, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378932000, "dur":50, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Http.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378929169, "dur":3165, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.dll (+2 others)" }}
,{ "pid":12345, "tid":14, "ts":1751450378932456, "dur":66, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.dll.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1751450378932539, "dur":770, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.dll (+2 others)" }}
,{ "pid":12345, "tid":14, "ts":1751450378933408, "dur":57, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1300b0aP.dag/Unity.XR.ARSubsystems.dll.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1751450378933481, "dur":380, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.XR.ARSubsystems.dll (+2 others)" }}
,{ "pid":12345, "tid":14, "ts":1751450378933975, "dur":226, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.XR.ARFoundation.InternalUtils.dll (+2 others)" }}
,{ "pid":12345, "tid":14, "ts":1751450378934317, "dur":325, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.XR.ARFoundation.dll (+2 others)" }}
,{ "pid":12345, "tid":14, "ts":1751450378934712, "dur":50, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1300b0aP.dag/Unity.XR.ARCore.dll.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1751450378934835, "dur":306, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.dll (+2 others)" }}
,{ "pid":12345, "tid":14, "ts":1751450378935189, "dur":375, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.XR.ARCore.dll (+2 others)" }}
,{ "pid":12345, "tid":14, "ts":1751450378935665, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751450378935795, "dur":3190, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.XR.ARCore.dll" }}
,{ "pid":12345, "tid":14, "ts":1751450378939054, "dur":3901, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Assembly-CSharp-firstpass.pdb" }}
,{ "pid":12345, "tid":14, "ts":1751450378942956, "dur":1482221, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751450378923972, "dur":4389, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751450378928373, "dur":93, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.GIModule.dll" }}
,{ "pid":12345, "tid":15, "ts":1751450378928468, "dur":309, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":15, "ts":1751450378928365, "dur":413, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_8BE6FA01756CA2FE.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1751450378928820, "dur":63, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.VehiclesModule.dll" }}
,{ "pid":12345, "tid":15, "ts":1751450378928819, "dur":66, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_352DD57FC840BE33.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1751450378929000, "dur":128, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751450378929175, "dur":61, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Mathematics.rsp" }}
,{ "pid":12345, "tid":15, "ts":1751450378929253, "dur":345, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751450378929598, "dur":420, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751450378930018, "dur":453, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751450378930472, "dur":344, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751450378930816, "dur":398, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751450378931214, "dur":666, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751450378931881, "dur":530, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751450378932412, "dur":52, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751450378932464, "dur":398, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751450378932879, "dur":62, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751450378933013, "dur":3923, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Timeline.dll" }}
,{ "pid":12345, "tid":15, "ts":1751450378936941, "dur":417, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751450378937379, "dur":4425, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.XR.ARSubsystems.dll" }}
,{ "pid":12345, "tid":15, "ts":1751450378941807, "dur":224028, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751450379165836, "dur":161, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\1300b0aP.dag\\Assembly-CSharp.pdb" }}
,{ "pid":12345, "tid":15, "ts":1751450379165836, "dur":162, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.pdb" }}
,{ "pid":12345, "tid":15, "ts":1751450379166028, "dur":752, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.pdb" }}
,{ "pid":12345, "tid":15, "ts":1751450379166782, "dur":1258396, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751450378924001, "dur":4368, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751450378928380, "dur":84, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.GridModule.dll" }}
,{ "pid":12345, "tid":16, "ts":1751450378928465, "dur":294, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":16, "ts":1751450378928372, "dur":388, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_4DD4804745901FB2.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1751450378928797, "dur":72, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.UnityTestProtocolModule.dll" }}
,{ "pid":12345, "tid":16, "ts":1751450378928795, "dur":76, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_48C7ED199519B3B7.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1751450378928968, "dur":84, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.rsp" }}
,{ "pid":12345, "tid":16, "ts":1751450378929120, "dur":149, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":16, "ts":1751450378929270, "dur":329, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751450378929600, "dur":467, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751450378930072, "dur":301, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751450378930374, "dur":353, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751450378930727, "dur":315, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751450378931042, "dur":243, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751450378931285, "dur":1113, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751450378932398, "dur":73, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751450378932471, "dur":395, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751450378932969, "dur":154, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751450378933162, "dur":4242, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Mathematics.pdb" }}
,{ "pid":12345, "tid":16, "ts":1751450378937474, "dur":4443, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.pdb" }}
,{ "pid":12345, "tid":16, "ts":1751450378941919, "dur":1483270, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751450378924060, "dur":4323, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751450378928395, "dur":84, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.IMGUIModule.dll" }}
,{ "pid":12345, "tid":17, "ts":1751450378928386, "dur":95, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_DF7C5EFFD938A6AF.mvfrm" }}
,{ "pid":12345, "tid":17, "ts":1751450378928525, "dur":58, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.LocalizationModule.dll" }}
,{ "pid":12345, "tid":17, "ts":1751450378928524, "dur":61, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_748B92C6E67DDB05.mvfrm" }}
,{ "pid":12345, "tid":17, "ts":1751450378928608, "dur":59, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.ProfilerModule.dll" }}
,{ "pid":12345, "tid":17, "ts":1751450378928607, "dur":63, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_EFB11536E77075A5.mvfrm" }}
,{ "pid":12345, "tid":17, "ts":1751450378928703, "dur":58, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.SubsystemsModule.dll" }}
,{ "pid":12345, "tid":17, "ts":1751450378928702, "dur":61, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_5D330BE2472E6120.mvfrm" }}
,{ "pid":12345, "tid":17, "ts":1751450378928802, "dur":72, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.UnityWebRequestAudioModule.dll" }}
,{ "pid":12345, "tid":17, "ts":1751450378928800, "dur":76, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_1DD1B9B75FF06412.mvfrm" }}
,{ "pid":12345, "tid":17, "ts":1751450378928986, "dur":80, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.XR.Management.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":17, "ts":1751450378929243, "dur":335, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751450378929579, "dur":537, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751450378930116, "dur":304, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751450378930421, "dur":295, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751450378930716, "dur":344, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751450378931061, "dur":171, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751450378931248, "dur":1147, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751450378932395, "dur":58, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751450378941316, "dur":1878, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/UnityEngine.TestRunner.pdb" }}
,{ "pid":12345, "tid":17, "ts":1751450378943196, "dur":1481990, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751450378924082, "dur":4308, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751450378928401, "dur":75, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.InputLegacyModule.dll" }}
,{ "pid":12345, "tid":18, "ts":1751450378928393, "dur":85, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_E2298BA8A543097B.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1751450378928478, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751450378928567, "dur":53, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.PhysicsModule.dll" }}
,{ "pid":12345, "tid":18, "ts":1751450378928566, "dur":55, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_4273EA0E1245435F.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1751450378928669, "dur":62, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.SpriteMaskModule.dll" }}
,{ "pid":12345, "tid":18, "ts":1751450378928667, "dur":66, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_F285A58078E6790D.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1751450378928796, "dur":90, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.UnityCurlModule.dll" }}
,{ "pid":12345, "tid":18, "ts":1751450378928794, "dur":94, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_A9D8044284F93AC9.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1751450378929016, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751450378929230, "dur":68, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":18, "ts":1751450378929299, "dur":265, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751450378929564, "dur":426, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751450378929990, "dur":380, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751450378930370, "dur":266, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751450378930636, "dur":300, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751450378930936, "dur":324, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751450378931260, "dur":1154, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751450378932453, "dur":62, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.dll.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1751450378932537, "dur":370, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.dll (+2 others)" }}
,{ "pid":12345, "tid":18, "ts":1751450378933005, "dur":122, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751450378933127, "dur":204, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751450378933413, "dur":50, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1300b0aP.dag/Unity.XR.CoreUtils.dll.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1751450378933486, "dur":320, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.XR.CoreUtils.dll (+2 others)" }}
,{ "pid":12345, "tid":18, "ts":1751450378933929, "dur":707, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.dll (+2 others)" }}
,{ "pid":12345, "tid":18, "ts":1751450378934703, "dur":60, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.dll.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1751450378934779, "dur":1008, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.dll (+2 others)" }}
,{ "pid":12345, "tid":18, "ts":1751450378935861, "dur":53, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1300b0aP.dag/Unity.XR.ARFoundation.VisualScripting.dll.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1751450378935952, "dur":57, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.State.dll.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1751450378936027, "dur":286, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.State.dll (+2 others)" }}
,{ "pid":12345, "tid":18, "ts":1751450378936353, "dur":359, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.XR.ARFoundation.VisualScripting.dll (+2 others)" }}
,{ "pid":12345, "tid":18, "ts":1751450378936806, "dur":87, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp-firstpass.dll.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1751450378936984, "dur":367, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Extensions.dll" }}
,{ "pid":12345, "tid":18, "ts":1751450378936914, "dur":735, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp-firstpass.dll (+2 others)" }}
,{ "pid":12345, "tid":18, "ts":1751450378937683, "dur":250, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.dll (+2 others)" }}
,{ "pid":12345, "tid":18, "ts":1751450378938283, "dur":221286, "ph":"X", "name": "Csc",  "args": { "detail":"Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.dll (+2 others)" }}
,{ "pid":12345, "tid":18, "ts":1751450379165801, "dur":47670, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\1300b0aP.dag\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":18, "ts":1751450379165800, "dur":47672, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":18, "ts":1751450379213490, "dur":788, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":18, "ts":1751450379214293, "dur":65663, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":18, "ts":1751450379279958, "dur":166, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Assembly-CSharp-firstpass.dll" }}
,{ "pid":12345, "tid":18, "ts":1751450379280125, "dur":73206, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.InputSystem.dll" }}
,{ "pid":12345, "tid":18, "ts":1751450379353333, "dur":148, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.InputSystem.ForUI.dll" }}
,{ "pid":12345, "tid":18, "ts":1751450379353481, "dur":47885, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":18, "ts":1751450379401367, "dur":147480, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":18, "ts":1751450379548852, "dur":59484, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.Timeline.dll" }}
,{ "pid":12345, "tid":18, "ts":1751450379608338, "dur":970, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":18, "ts":1751450379609310, "dur":392, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":18, "ts":1751450379609703, "dur":141, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":18, "ts":1751450379609844, "dur":173, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.XR.ARCore.dll" }}
,{ "pid":12345, "tid":18, "ts":1751450379610018, "dur":86399, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.XR.ARFoundation.dll" }}
,{ "pid":12345, "tid":18, "ts":1751450379696419, "dur":857, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.XR.ARFoundation.InternalUtils.dll" }}
,{ "pid":12345, "tid":18, "ts":1751450379697278, "dur":48581, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.XR.ARFoundation.VisualScripting.dll" }}
,{ "pid":12345, "tid":18, "ts":1751450379745861, "dur":226, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.XR.ARSubsystems.dll" }}
,{ "pid":12345, "tid":18, "ts":1751450379746088, "dur":72734, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.XR.CoreUtils.dll" }}
,{ "pid":12345, "tid":18, "ts":1751450379818823, "dur":152, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.XR.Management.dll" }}
,{ "pid":12345, "tid":18, "ts":1751450379818976, "dur":22683, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\UnityEngine.SpatialTracking.dll" }}
,{ "pid":12345, "tid":18, "ts":1751450379841661, "dur":77088, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\UnityEngine.TestRunner.dll" }}
,{ "pid":12345, "tid":18, "ts":1751450379918751, "dur":263, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":18, "ts":1751450379919015, "dur":32422, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\UnityEngine.XR.LegacyInputHelpers.dll" }}
,{ "pid":12345, "tid":18, "ts":1751450379214287, "dur":737157, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Player/RuntimeInitializeOnLoads.json (+1 other)" }}
,{ "pid":12345, "tid":18, "ts":1751450379951586, "dur":473409, "ph":"X", "name": "BuildPlayerDataGenerator",  "args": { "detail":"Library/BuildPlayerData/Player/RuntimeInitializeOnLoads.json (+1 other)" }}
,{ "pid":12345, "tid":19, "ts":1751450378924151, "dur":4255, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751450378928419, "dur":70, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.InputModule.dll" }}
,{ "pid":12345, "tid":19, "ts":1751450378928407, "dur":84, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_0D928749893F3772.mvfrm" }}
,{ "pid":12345, "tid":19, "ts":1751450378928534, "dur":66, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.PerformanceReportingModule.dll" }}
,{ "pid":12345, "tid":19, "ts":1751450378928533, "dur":69, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_A5D565D8D985997A.mvfrm" }}
,{ "pid":12345, "tid":19, "ts":1751450378928630, "dur":53, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.SharedInternalsModule.dll" }}
,{ "pid":12345, "tid":19, "ts":1751450378928629, "dur":56, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_FAC223C421AB24F9.mvfrm" }}
,{ "pid":12345, "tid":19, "ts":1751450378928758, "dur":63, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.TLSModule.dll" }}
,{ "pid":12345, "tid":19, "ts":1751450378928756, "dur":67, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_96F36902702D2473.mvfrm" }}
,{ "pid":12345, "tid":19, "ts":1751450378928866, "dur":63, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.XRModule.dll" }}
,{ "pid":12345, "tid":19, "ts":1751450378928864, "dur":71, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_74AF38AB60F99366.mvfrm" }}
,{ "pid":12345, "tid":19, "ts":1751450378928999, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751450378929163, "dur":121, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.XR.ARCore.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":19, "ts":1751450378929285, "dur":295, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751450378929581, "dur":407, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751450378929988, "dur":331, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751450378930320, "dur":309, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751450378930629, "dur":276, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751450378930906, "dur":309, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751450378931216, "dur":257, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751450378931474, "dur":930, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751450378932404, "dur":65, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751450378932469, "dur":391, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751450378932928, "dur":3792, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/UnityEngine.SpatialTracking.dll" }}
,{ "pid":12345, "tid":19, "ts":1751450378936779, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751450378936882, "dur":2434, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.XR.ARFoundation.VisualScripting.dll" }}
,{ "pid":12345, "tid":19, "ts":1751450378939373, "dur":3624, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.XR.ARFoundation.InternalUtils.pdb" }}
,{ "pid":12345, "tid":19, "ts":1751450378942998, "dur":1482237, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751450378924103, "dur":4294, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751450378928412, "dur":76, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.ImageConversionModule.dll" }}
,{ "pid":12345, "tid":20, "ts":1751450378928400, "dur":91, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_76B0E4EEAA7D29A9.mvfrm" }}
,{ "pid":12345, "tid":20, "ts":1751450378928491, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751450378928565, "dur":64, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.Physics2DModule.dll" }}
,{ "pid":12345, "tid":20, "ts":1751450378928564, "dur":68, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_C3615301B5E24071.mvfrm" }}
,{ "pid":12345, "tid":20, "ts":1751450378928678, "dur":63, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.StreamingModule.dll" }}
,{ "pid":12345, "tid":20, "ts":1751450378928676, "dur":68, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_2B505061646D001A.mvfrm" }}
,{ "pid":12345, "tid":20, "ts":1751450378928775, "dur":57, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_2B505061646D001A.mvfrm" }}
,{ "pid":12345, "tid":20, "ts":1751450378928835, "dur":54, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.UnityWebRequestTextureModule.dll" }}
,{ "pid":12345, "tid":20, "ts":1751450378928833, "dur":58, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_81B816008B521027.mvfrm" }}
,{ "pid":12345, "tid":20, "ts":1751450378928925, "dur":53, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.rsp" }}
,{ "pid":12345, "tid":20, "ts":1751450378928978, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751450378929115, "dur":86, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.TextMeshPro.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":20, "ts":1751450378929248, "dur":631, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751450378929879, "dur":317, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751450378930197, "dur":287, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751450378930484, "dur":344, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751450378930828, "dur":336, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751450378931164, "dur":860, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751450378932024, "dur":377, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751450378932402, "dur":58, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751450378932460, "dur":397, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751450378932894, "dur":3896, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.XR.Management.pdb" }}
,{ "pid":12345, "tid":20, "ts":1751450378936794, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751450378936875, "dur":4236, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":20, "ts":1751450378941171, "dur":2016, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.XR.CoreUtils.pdb" }}
,{ "pid":12345, "tid":20, "ts":1751450378943188, "dur":1482040, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751450380443174, "dur":1536, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 20040, "tid": 20231, "ts": 1751450380445194, "dur": 859, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 20040, "tid": 20231, "ts": 1751450380446086, "dur": 7212, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 20040, "tid": 20231, "ts": 1751450380444643, "dur": 8687, "ph": "X", "name": "Write chrome-trace events", "args": {} },
