<lint-module
    format="1"
    dir="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\launcher"
    name=":launcher"
    type="APP"
    maven="Gradle:launcher:"
    gradle="7.4.2"
    buildFolder="build"
    bootClassPath="C:\Program Files\Unity\Hub\Editor\2022.3.43f1\Editor\Data\PlaybackEngines\AndroidPlayer\SDK\platforms\android-34\android.jar;C:\Program Files\Unity\Hub\Editor\2022.3.43f1\Editor\Data\PlaybackEngines\AndroidPlayer\SDK\build-tools\32.0.0\core-lambda-stubs.jar"
    javaSourceLevel="11"
    compileTarget="android-34"
    neverShrinking="true">
  <lintOptions
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
