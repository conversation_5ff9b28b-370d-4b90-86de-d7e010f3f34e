<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":unityLibrary:xrmanifest.androidlib" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\xrmanifest.androidlib\build\intermediates\library_assets\release\out"/></dataSet><dataSet config=":unityLibrary" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out"><file name="bin/Data/boot.config" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\boot.config"/><file name="bin/Data/data.unity3d" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\data.unity3d"/><file name="bin/Data/Managed/Assembly-CSharp.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\Assembly-CSharp.dll"/><file name="bin/Data/Managed/Mono.Security.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\Mono.Security.dll"/><file name="bin/Data/Managed/mscorlib.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\mscorlib.dll"/><file name="bin/Data/Managed/netstandard.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\netstandard.dll"/><file name="bin/Data/Managed/System.ComponentModel.Composition.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\System.ComponentModel.Composition.dll"/><file name="bin/Data/Managed/System.Configuration.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\System.Configuration.dll"/><file name="bin/Data/Managed/System.Core.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\System.Core.dll"/><file name="bin/Data/Managed/System.Data.DataSetExtensions.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\System.Data.DataSetExtensions.dll"/><file name="bin/Data/Managed/System.Data.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\System.Data.dll"/><file name="bin/Data/Managed/System.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\System.dll"/><file name="bin/Data/Managed/System.Drawing.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\System.Drawing.dll"/><file name="bin/Data/Managed/System.EnterpriseServices.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\System.EnterpriseServices.dll"/><file name="bin/Data/Managed/System.IO.Compression.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\System.IO.Compression.dll"/><file name="bin/Data/Managed/System.IO.Compression.FileSystem.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\System.IO.Compression.FileSystem.dll"/><file name="bin/Data/Managed/System.Net.Http.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\System.Net.Http.dll"/><file name="bin/Data/Managed/System.Numerics.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\System.Numerics.dll"/><file name="bin/Data/Managed/System.Runtime.Serialization.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\System.Runtime.Serialization.dll"/><file name="bin/Data/Managed/System.Security.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\System.Security.dll"/><file name="bin/Data/Managed/System.ServiceModel.Internals.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\System.ServiceModel.Internals.dll"/><file name="bin/Data/Managed/System.Transactions.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\System.Transactions.dll"/><file name="bin/Data/Managed/System.Xml.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\System.Xml.dll"/><file name="bin/Data/Managed/System.Xml.Linq.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\System.Xml.Linq.dll"/><file name="bin/Data/Managed/Unity.InputSystem.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\Unity.InputSystem.dll"/><file name="bin/Data/Managed/Unity.InputSystem.ForUI.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\Unity.InputSystem.ForUI.dll"/><file name="bin/Data/Managed/Unity.Mathematics.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\Unity.Mathematics.dll"/><file name="bin/Data/Managed/Unity.TextMeshPro.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\Unity.TextMeshPro.dll"/><file name="bin/Data/Managed/Unity.Timeline.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\Unity.Timeline.dll"/><file name="bin/Data/Managed/Unity.VisualScripting.Antlr3.Runtime.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\Unity.VisualScripting.Antlr3.Runtime.dll"/><file name="bin/Data/Managed/Unity.VisualScripting.Core.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\Unity.VisualScripting.Core.dll"/><file name="bin/Data/Managed/Unity.VisualScripting.Flow.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\Unity.VisualScripting.Flow.dll"/><file name="bin/Data/Managed/Unity.VisualScripting.State.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\Unity.VisualScripting.State.dll"/><file name="bin/Data/Managed/Unity.XR.ARCore.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\Unity.XR.ARCore.dll"/><file name="bin/Data/Managed/Unity.XR.ARFoundation.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\Unity.XR.ARFoundation.dll"/><file name="bin/Data/Managed/Unity.XR.ARFoundation.InternalUtils.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\Unity.XR.ARFoundation.InternalUtils.dll"/><file name="bin/Data/Managed/Unity.XR.ARFoundation.VisualScripting.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\Unity.XR.ARFoundation.VisualScripting.dll"/><file name="bin/Data/Managed/Unity.XR.ARSubsystems.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\Unity.XR.ARSubsystems.dll"/><file name="bin/Data/Managed/Unity.XR.CoreUtils.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\Unity.XR.CoreUtils.dll"/><file name="bin/Data/Managed/Unity.XR.Management.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\Unity.XR.Management.dll"/><file name="bin/Data/Managed/UnityEngine.AccessibilityModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.AccessibilityModule.dll"/><file name="bin/Data/Managed/UnityEngine.AIModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.AIModule.dll"/><file name="bin/Data/Managed/UnityEngine.AndroidJNIModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.AndroidJNIModule.dll"/><file name="bin/Data/Managed/UnityEngine.AnimationModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.AnimationModule.dll"/><file name="bin/Data/Managed/UnityEngine.ARModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.ARModule.dll"/><file name="bin/Data/Managed/UnityEngine.AssetBundleModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.AssetBundleModule.dll"/><file name="bin/Data/Managed/UnityEngine.AudioModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.AudioModule.dll"/><file name="bin/Data/Managed/UnityEngine.ClothModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.ClothModule.dll"/><file name="bin/Data/Managed/UnityEngine.ContentLoadModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.ContentLoadModule.dll"/><file name="bin/Data/Managed/UnityEngine.CoreModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.CoreModule.dll"/><file name="bin/Data/Managed/UnityEngine.CrashReportingModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.CrashReportingModule.dll"/><file name="bin/Data/Managed/UnityEngine.DirectorModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.DirectorModule.dll"/><file name="bin/Data/Managed/UnityEngine.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.dll"/><file name="bin/Data/Managed/UnityEngine.DSPGraphModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.DSPGraphModule.dll"/><file name="bin/Data/Managed/UnityEngine.GameCenterModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.GameCenterModule.dll"/><file name="bin/Data/Managed/UnityEngine.GIModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.GIModule.dll"/><file name="bin/Data/Managed/UnityEngine.GridModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.GridModule.dll"/><file name="bin/Data/Managed/UnityEngine.HotReloadModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.HotReloadModule.dll"/><file name="bin/Data/Managed/UnityEngine.ImageConversionModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.ImageConversionModule.dll"/><file name="bin/Data/Managed/UnityEngine.IMGUIModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.IMGUIModule.dll"/><file name="bin/Data/Managed/UnityEngine.InputLegacyModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.InputLegacyModule.dll"/><file name="bin/Data/Managed/UnityEngine.InputModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.InputModule.dll"/><file name="bin/Data/Managed/UnityEngine.JSONSerializeModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.JSONSerializeModule.dll"/><file name="bin/Data/Managed/UnityEngine.LocalizationModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.LocalizationModule.dll"/><file name="bin/Data/Managed/UnityEngine.ParticleSystemModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.ParticleSystemModule.dll"/><file name="bin/Data/Managed/UnityEngine.PerformanceReportingModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.PerformanceReportingModule.dll"/><file name="bin/Data/Managed/UnityEngine.Physics2DModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.Physics2DModule.dll"/><file name="bin/Data/Managed/UnityEngine.PhysicsModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.PhysicsModule.dll"/><file name="bin/Data/Managed/UnityEngine.ProfilerModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.ProfilerModule.dll"/><file name="bin/Data/Managed/UnityEngine.PropertiesModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.PropertiesModule.dll"/><file name="bin/Data/Managed/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"/><file name="bin/Data/Managed/UnityEngine.ScreenCaptureModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.ScreenCaptureModule.dll"/><file name="bin/Data/Managed/UnityEngine.SharedInternalsModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.SharedInternalsModule.dll"/><file name="bin/Data/Managed/UnityEngine.SpatialTracking.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.SpatialTracking.dll"/><file name="bin/Data/Managed/UnityEngine.SpriteMaskModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.SpriteMaskModule.dll"/><file name="bin/Data/Managed/UnityEngine.SpriteShapeModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.SpriteShapeModule.dll"/><file name="bin/Data/Managed/UnityEngine.StreamingModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.StreamingModule.dll"/><file name="bin/Data/Managed/UnityEngine.SubstanceModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.SubstanceModule.dll"/><file name="bin/Data/Managed/UnityEngine.SubsystemsModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.SubsystemsModule.dll"/><file name="bin/Data/Managed/UnityEngine.TerrainModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.TerrainModule.dll"/><file name="bin/Data/Managed/UnityEngine.TerrainPhysicsModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.TerrainPhysicsModule.dll"/><file name="bin/Data/Managed/UnityEngine.TextCoreFontEngineModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.TextCoreFontEngineModule.dll"/><file name="bin/Data/Managed/UnityEngine.TextCoreTextEngineModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.TextCoreTextEngineModule.dll"/><file name="bin/Data/Managed/UnityEngine.TextRenderingModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.TextRenderingModule.dll"/><file name="bin/Data/Managed/UnityEngine.TilemapModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.TilemapModule.dll"/><file name="bin/Data/Managed/UnityEngine.TLSModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.TLSModule.dll"/><file name="bin/Data/Managed/UnityEngine.UI.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.UI.dll"/><file name="bin/Data/Managed/UnityEngine.UIElementsModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.UIElementsModule.dll"/><file name="bin/Data/Managed/UnityEngine.UIModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.UIModule.dll"/><file name="bin/Data/Managed/UnityEngine.UmbraModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.UmbraModule.dll"/><file name="bin/Data/Managed/UnityEngine.UnityAnalyticsCommonModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.UnityAnalyticsCommonModule.dll"/><file name="bin/Data/Managed/UnityEngine.UnityAnalyticsModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.UnityAnalyticsModule.dll"/><file name="bin/Data/Managed/UnityEngine.UnityConnectModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.UnityConnectModule.dll"/><file name="bin/Data/Managed/UnityEngine.UnityCurlModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.UnityCurlModule.dll"/><file name="bin/Data/Managed/UnityEngine.UnityTestProtocolModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.UnityTestProtocolModule.dll"/><file name="bin/Data/Managed/UnityEngine.UnityWebRequestAssetBundleModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.UnityWebRequestAssetBundleModule.dll"/><file name="bin/Data/Managed/UnityEngine.UnityWebRequestAudioModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.UnityWebRequestAudioModule.dll"/><file name="bin/Data/Managed/UnityEngine.UnityWebRequestModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.UnityWebRequestModule.dll"/><file name="bin/Data/Managed/UnityEngine.UnityWebRequestTextureModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.UnityWebRequestTextureModule.dll"/><file name="bin/Data/Managed/UnityEngine.UnityWebRequestWWWModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.UnityWebRequestWWWModule.dll"/><file name="bin/Data/Managed/UnityEngine.VehiclesModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.VehiclesModule.dll"/><file name="bin/Data/Managed/UnityEngine.VFXModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.VFXModule.dll"/><file name="bin/Data/Managed/UnityEngine.VideoModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.VideoModule.dll"/><file name="bin/Data/Managed/UnityEngine.VRModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.VRModule.dll"/><file name="bin/Data/Managed/UnityEngine.WindModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.WindModule.dll"/><file name="bin/Data/Managed/UnityEngine.XR.LegacyInputHelpers.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.XR.LegacyInputHelpers.dll"/><file name="bin/Data/Managed/UnityEngine.XRModule.dll" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\Managed\UnityEngine.XRModule.dll"/><file name="bin/Data/RuntimeInitializeOnLoads.json" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\RuntimeInitializeOnLoads.json"/><file name="bin/Data/ScriptingAssemblies.json" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\ScriptingAssemblies.json"/><file name="bin/Data/unity default resources" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\unity default resources"/><file name="bin/Data/UnitySubsystems/UnityARCore/UnitySubsystemsManifest.json" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\UnitySubsystems\UnityARCore\UnitySubsystemsManifest.json"/><file name="bin/Data/unity_app_guid" path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\intermediates\library_assets\release\out\bin\Data\unity_app_guid"/></source></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\launcher\src\main\assets"/><source path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\launcher\build\intermediates\shader_assets\release\out"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\launcher\src\release\assets"/></dataSet></merger>