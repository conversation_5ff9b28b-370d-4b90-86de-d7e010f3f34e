{"root": [{"assemblyName": "Unity.InputSystem", "nameSpace": "UnityEngine.InputSystem", "className": "InputSystem", "methodName": "RunInitializeInPlayer", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.InputSystem", "nameSpace": "UnityEngine.InputSystem", "className": "InputSystem", "methodName": "RunInitialUpdate", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Unity.VisualScripting.Core", "nameSpace": "Unity.VisualScripting", "className": "RuntimeVSUsageUtility", "methodName": "RuntimeInitializeOnLoadBeforeSceneLoad", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Unity.XR.ARCore", "nameSpace": "UnityEngine.XR.ARCore", "className": "ARCoreAnchorSubsystem", "methodName": "RegisterDescriptor", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.XR.ARCore", "nameSpace": "UnityEngine.XR.ARCore", "className": "ARCoreCameraSubsystem", "methodName": "Register", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.XR.ARCore", "nameSpace": "UnityEngine.XR.ARCore", "className": "ARCoreEnvironmentProbeSubsystem", "methodName": "Register", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.XR.ARCore", "nameSpace": "UnityEngine.XR.ARCore", "className": "ARCoreFaceSubsystem", "methodName": "RegisterDescriptor", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.XR.ARCore", "nameSpace": "UnityEngine.XR.ARCore", "className": "ARCoreImageTrackingSubsystem", "methodName": "RegisterDescriptor", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.XR.ARCore", "nameSpace": "UnityEngine.XR.ARCore", "className": "ARCoreOcclusionSubsystem", "methodName": "Register", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.XR.ARCore", "nameSpace": "UnityEngine.XR.ARCore", "className": "ARCorePlaneSubsystem", "methodName": "RegisterDescriptor", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.XR.ARCore", "nameSpace": "UnityEngine.XR.ARCore", "className": "ARCoreRaycastSubsystem", "methodName": "RegisterDescriptor", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.XR.ARCore", "nameSpace": "UnityEngine.XR.ARCore", "className": "ARCoreSessionSubsystem", "methodName": "RegisterDescriptor", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.XR.ARCore", "nameSpace": "UnityEngine.XR.ARCore", "className": "InputLayoutLoader", "methodName": "RegisterLayouts", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Unity.XR.ARCore", "nameSpace": "UnityEngine.XR.ARCore", "className": "ARCoreXRDepthSubsystem", "methodName": "RegisterDescriptor", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.XR.ARCore", "nameSpace": "UnityEngine.XR.ARCore", "className": "ARCoreXRPointCloudSubsystem", "methodName": "RegisterDescriptor", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.XR.Management", "nameSpace": "UnityEngine.XR.Management", "className": "XRGeneralSettings", "methodName": "AttemptInitializeXRSDKOnLoad", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.XR.Management", "nameSpace": "UnityEngine.XR.Management", "className": "XRGeneralSettings", "methodName": "AttemptStartXRSDKOnBeforeSplashScreen", "loadTypes": 3, "isUnityClass": true}]}