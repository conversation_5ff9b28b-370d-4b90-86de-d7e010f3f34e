using System.Collections;
using System.Collections.Generic;
using UnityEngine;



public class AudioPlayer : MonoBehaviour
{
    // Drag your audio clip here in the inspector
    public AudioClip soundToPlay;

    // Reference to the AudioSource component
    private AudioSource audioSource;

    void Start()
    {
        // Get or add an AudioSource component
        audioSource = GetComponent<AudioSource>();
        if (audioSource == null)
        {
            audioSource = gameObject.AddComponent<AudioSource>();
        }

        // Optional: Configure the AudioSource
        audioSource.playOnAwake = false;
        audioSource.loop = false;

        // If you assigned a default clip in the inspector
        if (soundToPlay != null)
        {
            audioSource.clip = soundToPlay;
        }
    }

    // Call this method to play the default audio clip
    public void PlaySound()
    {
        if (audioSource != null && audioSource.clip != null)
        {
            audioSource.Play();
        }
        else
        {
            Debug.LogWarning("AudioSource or AudioClip is not assigned!");
        }
    }

    // Call this method to play a specific audio clip
    public void PlaySound(AudioClip clip)
    {
        if (audioSource != null && clip != null)
        {
            audioSource.clip = clip;
            audioSource.Play();
        }
        else
        {
            Debug.LogWarning("AudioSource or AudioClip is not assigned!");
        }
    }

    // Play a sound at a specific position in 3D space (one-shot method)
    public void PlaySoundAtPosition(AudioClip clip, Vector3 position)
    {
        if (clip != null)
        {
            AudioSource.PlayClipAtPoint(clip, position);
        }
    }
}