{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 20040, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 20040, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 20040, "tid": 284376, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 20040, "tid": 284376, "ts": 1751453260517677, "dur": 523, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 20040, "tid": 284376, "ts": 1751453260518223, "dur": 23, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 20040, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 20040, "tid": 1, "ts": 1751453260246365, "dur": 1559, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 20040, "tid": 1, "ts": 1751453260247927, "dur": 54731, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 20040, "tid": 1, "ts": 1751453260302659, "dur": 39435, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 20040, "tid": 284376, "ts": 1751453260518248, "dur": 7, "ph": "X", "name": "", "args": {}}, {"pid": 20040, "tid": 47244640256, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260246335, "dur": 14217, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260260554, "dur": 254731, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260260563, "dur": 26, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260260593, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260260595, "dur": 322, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260260923, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260260951, "dur": 5, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260260957, "dur": 2975, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260263938, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260263941, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260263988, "dur": 2, "ph": "X", "name": "ProcessMessages 1096", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260263990, "dur": 65, "ph": "X", "name": "ReadAsync 1096", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264059, "dur": 1, "ph": "X", "name": "ProcessMessages 778", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264061, "dur": 46, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264110, "dur": 2, "ph": "X", "name": "ProcessMessages 1946", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264112, "dur": 28, "ph": "X", "name": "ReadAsync 1946", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264144, "dur": 1, "ph": "X", "name": "ProcessMessages 870", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264146, "dur": 25, "ph": "X", "name": "ReadAsync 870", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264174, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264175, "dur": 54, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264234, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264237, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264277, "dur": 1, "ph": "X", "name": "ProcessMessages 1059", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264280, "dur": 30, "ph": "X", "name": "ReadAsync 1059", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264312, "dur": 1, "ph": "X", "name": "ProcessMessages 793", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264313, "dur": 25, "ph": "X", "name": "ReadAsync 793", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264341, "dur": 1, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264343, "dur": 28, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264373, "dur": 1, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264375, "dur": 30, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264408, "dur": 1, "ph": "X", "name": "ProcessMessages 690", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264410, "dur": 28, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264440, "dur": 1, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264442, "dur": 27, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264471, "dur": 1, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264473, "dur": 26, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264502, "dur": 18, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264522, "dur": 1, "ph": "X", "name": "ProcessMessages 89", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264524, "dur": 56, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264585, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264625, "dur": 1, "ph": "X", "name": "ProcessMessages 958", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264627, "dur": 29, "ph": "X", "name": "ReadAsync 958", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264658, "dur": 1, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264661, "dur": 27, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264691, "dur": 1, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264693, "dur": 30, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264726, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264728, "dur": 31, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264761, "dur": 1, "ph": "X", "name": "ProcessMessages 865", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264763, "dur": 25, "ph": "X", "name": "ReadAsync 865", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264789, "dur": 1, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264791, "dur": 26, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264819, "dur": 1, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264821, "dur": 28, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264851, "dur": 1, "ph": "X", "name": "ProcessMessages 792", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264853, "dur": 26, "ph": "X", "name": "ReadAsync 792", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264881, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264882, "dur": 25, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264910, "dur": 25, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264937, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264939, "dur": 26, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264968, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264970, "dur": 25, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264997, "dur": 1, "ph": "X", "name": "ProcessMessages 726", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260264998, "dur": 27, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265028, "dur": 33, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265064, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265066, "dur": 29, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265097, "dur": 1, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265099, "dur": 20, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265122, "dur": 64, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265191, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265240, "dur": 2, "ph": "X", "name": "ProcessMessages 2526", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265242, "dur": 29, "ph": "X", "name": "ReadAsync 2526", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265274, "dur": 1, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265276, "dur": 30, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265308, "dur": 1, "ph": "X", "name": "ProcessMessages 727", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265310, "dur": 28, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265341, "dur": 1, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265343, "dur": 67, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265413, "dur": 1, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265416, "dur": 27, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265444, "dur": 1, "ph": "X", "name": "ProcessMessages 1481", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265446, "dur": 21, "ph": "X", "name": "ReadAsync 1481", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265470, "dur": 1, "ph": "X", "name": "ProcessMessages 267", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265471, "dur": 27, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265500, "dur": 1, "ph": "X", "name": "ProcessMessages 702", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265501, "dur": 25, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265528, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265530, "dur": 27, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265562, "dur": 60, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265625, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265627, "dur": 38, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265668, "dur": 2, "ph": "X", "name": "ProcessMessages 1991", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265670, "dur": 24, "ph": "X", "name": "ReadAsync 1991", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265698, "dur": 27, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265727, "dur": 1, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265729, "dur": 26, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265757, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265759, "dur": 62, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265824, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265826, "dur": 40, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265869, "dur": 1, "ph": "X", "name": "ProcessMessages 1746", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265871, "dur": 25, "ph": "X", "name": "ReadAsync 1746", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265899, "dur": 1, "ph": "X", "name": "ProcessMessages 631", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265900, "dur": 26, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265930, "dur": 21, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265953, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265955, "dur": 23, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265981, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260265983, "dur": 30, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266016, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266018, "dur": 29, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266050, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266052, "dur": 26, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266080, "dur": 1, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266081, "dur": 20, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266105, "dur": 53, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266162, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266192, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266194, "dur": 26, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266222, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266223, "dur": 21, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266247, "dur": 24, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266275, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266277, "dur": 20, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266301, "dur": 67, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266371, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266396, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266398, "dur": 26, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266427, "dur": 1, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266428, "dur": 26, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266456, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266459, "dur": 27, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266489, "dur": 20, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266513, "dur": 20, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266538, "dur": 26, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266567, "dur": 1, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266569, "dur": 26, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266597, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266598, "dur": 23, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266623, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266625, "dur": 23, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266650, "dur": 1, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266652, "dur": 23, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266679, "dur": 18, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266700, "dur": 33, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266735, "dur": 1, "ph": "X", "name": "ProcessMessages 772", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266737, "dur": 24, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266763, "dur": 2, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266765, "dur": 28, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266796, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266797, "dur": 21, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266822, "dur": 22, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266846, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266848, "dur": 25, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266875, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266877, "dur": 28, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266907, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266909, "dur": 20, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266931, "dur": 1, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266933, "dur": 21, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266957, "dur": 30, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266989, "dur": 1, "ph": "X", "name": "ProcessMessages 834", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260266990, "dur": 27, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267020, "dur": 1, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267021, "dur": 25, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267048, "dur": 1, "ph": "X", "name": "ProcessMessages 742", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267050, "dur": 16, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267070, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267104, "dur": 1, "ph": "X", "name": "ProcessMessages 927", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267106, "dur": 27, "ph": "X", "name": "ReadAsync 927", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267136, "dur": 1, "ph": "X", "name": "ProcessMessages 785", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267137, "dur": 20, "ph": "X", "name": "ReadAsync 785", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267159, "dur": 1, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267161, "dur": 27, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267190, "dur": 1, "ph": "X", "name": "ProcessMessages 802", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267191, "dur": 34, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267228, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267230, "dur": 34, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267266, "dur": 1, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267269, "dur": 34, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267305, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267307, "dur": 24, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267332, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267334, "dur": 28, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267365, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267367, "dur": 27, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267396, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267398, "dur": 23, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267423, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267425, "dur": 22, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267450, "dur": 20, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267474, "dur": 21, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267499, "dur": 25, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267527, "dur": 26, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267554, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267556, "dur": 29, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267588, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267590, "dur": 23, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267616, "dur": 24, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267642, "dur": 1, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267643, "dur": 24, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267671, "dur": 23, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267696, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267698, "dur": 23, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267723, "dur": 1, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267725, "dur": 22, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267751, "dur": 25, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267780, "dur": 28, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267810, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267812, "dur": 27, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267841, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267843, "dur": 20, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267866, "dur": 20, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267890, "dur": 25, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267919, "dur": 27, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267949, "dur": 1, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267951, "dur": 32, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267985, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260267987, "dur": 19, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268008, "dur": 23, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268034, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268036, "dur": 28, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268067, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268069, "dur": 25, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268097, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268099, "dur": 23, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268126, "dur": 21, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268150, "dur": 27, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268180, "dur": 1, "ph": "X", "name": "ProcessMessages 659", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268182, "dur": 25, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268209, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268210, "dur": 20, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268234, "dur": 21, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268257, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268259, "dur": 30, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268292, "dur": 1, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268293, "dur": 24, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268319, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268321, "dur": 23, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268346, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268349, "dur": 27, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268378, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268380, "dur": 24, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268407, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268415, "dur": 30, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268447, "dur": 1, "ph": "X", "name": "ProcessMessages 997", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268448, "dur": 23, "ph": "X", "name": "ReadAsync 997", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268475, "dur": 18, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268497, "dur": 22, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268522, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268552, "dur": 185, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268741, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268775, "dur": 1, "ph": "X", "name": "ProcessMessages 809", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268777, "dur": 33, "ph": "X", "name": "ReadAsync 809", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268813, "dur": 1, "ph": "X", "name": "ProcessMessages 663", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268815, "dur": 29, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268847, "dur": 1, "ph": "X", "name": "ProcessMessages 677", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268850, "dur": 22, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268876, "dur": 31, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268909, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268911, "dur": 35, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268948, "dur": 1, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268950, "dur": 21, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268974, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268975, "dur": 21, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260268999, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269000, "dur": 23, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269026, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269028, "dur": 26, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269056, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269058, "dur": 26, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269086, "dur": 1, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269088, "dur": 23, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269113, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269117, "dur": 20, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269141, "dur": 27, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269170, "dur": 1, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269172, "dur": 27, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269201, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269203, "dur": 25, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269232, "dur": 20, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269255, "dur": 22, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269279, "dur": 1, "ph": "X", "name": "ProcessMessages 167", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269281, "dur": 29, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269313, "dur": 1, "ph": "X", "name": "ProcessMessages 725", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269314, "dur": 24, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269341, "dur": 25, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269369, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269371, "dur": 26, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269399, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269401, "dur": 18, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269422, "dur": 25, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269449, "dur": 1, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269451, "dur": 23, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269476, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269478, "dur": 25, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269505, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269507, "dur": 32, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269541, "dur": 1, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269543, "dur": 21, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269567, "dur": 54, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269623, "dur": 2, "ph": "X", "name": "ProcessMessages 1079", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269626, "dur": 28, "ph": "X", "name": "ReadAsync 1079", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269656, "dur": 2, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269659, "dur": 21, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269683, "dur": 24, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269710, "dur": 25, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269737, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269739, "dur": 27, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269768, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269770, "dur": 26, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269798, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269800, "dur": 25, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269827, "dur": 1, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269829, "dur": 25, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269856, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269858, "dur": 27, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269887, "dur": 1, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269889, "dur": 24, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269915, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269916, "dur": 17, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269937, "dur": 26, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269965, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269967, "dur": 27, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269996, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260269998, "dur": 26, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270027, "dur": 1, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270028, "dur": 24, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270056, "dur": 18, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270078, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270108, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270110, "dur": 28, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270139, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270141, "dur": 22, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270165, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270166, "dur": 23, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270193, "dur": 20, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270217, "dur": 23, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270244, "dur": 26, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270273, "dur": 25, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270301, "dur": 1, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270303, "dur": 22, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270330, "dur": 20, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270353, "dur": 21, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270378, "dur": 25, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270405, "dur": 1, "ph": "X", "name": "ProcessMessages 715", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270407, "dur": 28, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270438, "dur": 1, "ph": "X", "name": "ProcessMessages 579", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270439, "dur": 24, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270466, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270468, "dur": 20, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270491, "dur": 22, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270516, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270517, "dur": 29, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270550, "dur": 27, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270581, "dur": 27, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270617, "dur": 26, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270645, "dur": 1, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270648, "dur": 32, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270682, "dur": 1, "ph": "X", "name": "ProcessMessages 780", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270684, "dur": 21, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270708, "dur": 24, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270736, "dur": 23, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270762, "dur": 18, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270784, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270817, "dur": 1, "ph": "X", "name": "ProcessMessages 641", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270818, "dur": 30, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270851, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270853, "dur": 21, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270878, "dur": 24, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270904, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270906, "dur": 20, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270930, "dur": 25, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270958, "dur": 1, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270959, "dur": 26, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270987, "dur": 1, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260270988, "dur": 22, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271013, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271015, "dur": 24, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271040, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271042, "dur": 21, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271066, "dur": 1, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271068, "dur": 29, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271099, "dur": 1, "ph": "X", "name": "ProcessMessages 808", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271101, "dur": 21, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271125, "dur": 35, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271163, "dur": 1, "ph": "X", "name": "ProcessMessages 770", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271165, "dur": 20, "ph": "X", "name": "ReadAsync 770", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271189, "dur": 25, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271219, "dur": 25, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271246, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271248, "dur": 23, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271274, "dur": 20, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271296, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271298, "dur": 23, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271323, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271324, "dur": 25, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271352, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271354, "dur": 26, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271383, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271384, "dur": 25, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271413, "dur": 25, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271440, "dur": 2, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271443, "dur": 21, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271467, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271470, "dur": 28, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271500, "dur": 1, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271502, "dur": 30, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271534, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271536, "dur": 31, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271569, "dur": 1, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271570, "dur": 21, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271596, "dur": 24, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271623, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271625, "dur": 34, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271661, "dur": 1, "ph": "X", "name": "ProcessMessages 714", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271662, "dur": 18, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271685, "dur": 27, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271714, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271716, "dur": 23, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271744, "dur": 27, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271775, "dur": 30, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271806, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271808, "dur": 31, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271841, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271844, "dur": 21, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271870, "dur": 24, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271898, "dur": 24, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271925, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271926, "dur": 22, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271951, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271954, "dur": 27, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271982, "dur": 1, "ph": "X", "name": "ProcessMessages 687", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260271984, "dur": 19, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272007, "dur": 26, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272036, "dur": 27, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272066, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272068, "dur": 21, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272108, "dur": 42, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272151, "dur": 1, "ph": "X", "name": "ProcessMessages 1070", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272154, "dur": 29, "ph": "X", "name": "ReadAsync 1070", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272186, "dur": 1, "ph": "X", "name": "ProcessMessages 979", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272188, "dur": 23, "ph": "X", "name": "ReadAsync 979", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272214, "dur": 1, "ph": "X", "name": "ProcessMessages 213", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272216, "dur": 27, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272245, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272247, "dur": 22, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272271, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272273, "dur": 22, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272300, "dur": 22, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272326, "dur": 27, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272356, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272358, "dur": 26, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272386, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272389, "dur": 22, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272414, "dur": 26, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272442, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272444, "dur": 25, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272472, "dur": 1, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272473, "dur": 24, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272500, "dur": 1, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272501, "dur": 26, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272538, "dur": 21, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272563, "dur": 27, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272593, "dur": 1, "ph": "X", "name": "ProcessMessages 739", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272595, "dur": 27, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272624, "dur": 1, "ph": "X", "name": "ProcessMessages 765", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272626, "dur": 22, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272652, "dur": 24, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272679, "dur": 1, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272681, "dur": 24, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272708, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272709, "dur": 25, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272737, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272738, "dur": 26, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272767, "dur": 1, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272769, "dur": 29, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272800, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272802, "dur": 20, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272827, "dur": 26, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272856, "dur": 1, "ph": "X", "name": "ProcessMessages 733", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272858, "dur": 24, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272885, "dur": 26, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272913, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272916, "dur": 19, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272936, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272939, "dur": 22, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272963, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272965, "dur": 20, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260272988, "dur": 32, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273024, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273054, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273056, "dur": 24, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273082, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273084, "dur": 26, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273113, "dur": 24, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273140, "dur": 1, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273142, "dur": 28, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273172, "dur": 1, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273174, "dur": 24, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273200, "dur": 1, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273202, "dur": 24, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273228, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273229, "dur": 22, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273255, "dur": 23, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273281, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273283, "dur": 25, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273309, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273311, "dur": 24, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273338, "dur": 20, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273361, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273363, "dur": 27, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273393, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273395, "dur": 25, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273422, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273424, "dur": 24, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273450, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273452, "dur": 23, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273478, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273480, "dur": 30, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273513, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273515, "dur": 25, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273542, "dur": 2, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273545, "dur": 23, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273572, "dur": 23, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273598, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273599, "dur": 32, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273634, "dur": 1, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273636, "dur": 26, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273664, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273666, "dur": 25, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273693, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273695, "dur": 26, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273724, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273726, "dur": 27, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273756, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273758, "dur": 26, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273785, "dur": 1, "ph": "X", "name": "ProcessMessages 522", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273787, "dur": 24, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273812, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273814, "dur": 27, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273843, "dur": 1, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273845, "dur": 24, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273872, "dur": 1, "ph": "X", "name": "ProcessMessages 706", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273874, "dur": 26, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273902, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273904, "dur": 26, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273932, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273934, "dur": 26, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273963, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273965, "dur": 30, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260273998, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274000, "dur": 30, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274033, "dur": 1, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274035, "dur": 24, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274060, "dur": 1, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274062, "dur": 25, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274091, "dur": 26, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274119, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274121, "dur": 22, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274145, "dur": 1, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274146, "dur": 21, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274170, "dur": 23, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274198, "dur": 29, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274229, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274231, "dur": 25, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274257, "dur": 1, "ph": "X", "name": "ProcessMessages 734", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274259, "dur": 24, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274287, "dur": 25, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274314, "dur": 1, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274316, "dur": 21, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274339, "dur": 1, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274340, "dur": 23, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274365, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274367, "dur": 25, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274394, "dur": 1, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274397, "dur": 30, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274429, "dur": 1, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274431, "dur": 24, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274457, "dur": 1, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274459, "dur": 24, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274485, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274487, "dur": 27, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274517, "dur": 27, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274546, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274548, "dur": 23, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274574, "dur": 24, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274600, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274602, "dur": 27, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274632, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274634, "dur": 26, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274662, "dur": 1, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274664, "dur": 37, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274704, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274706, "dur": 38, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274746, "dur": 1, "ph": "X", "name": "ProcessMessages 786", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274748, "dur": 26, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274776, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274779, "dur": 21, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274804, "dur": 39, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274847, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274885, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274887, "dur": 31, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274919, "dur": 1, "ph": "X", "name": "ProcessMessages 671", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274921, "dur": 36, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274960, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260274962, "dur": 39, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275004, "dur": 1, "ph": "X", "name": "ProcessMessages 829", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275007, "dur": 31, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275040, "dur": 1, "ph": "X", "name": "ProcessMessages 721", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275042, "dur": 39, "ph": "X", "name": "ReadAsync 721", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275083, "dur": 1, "ph": "X", "name": "ProcessMessages 673", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275086, "dur": 32, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275122, "dur": 1, "ph": "X", "name": "ProcessMessages 659", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275125, "dur": 29, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275156, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275159, "dur": 28, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275189, "dur": 1, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275191, "dur": 26, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275220, "dur": 1, "ph": "X", "name": "ProcessMessages 491", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275222, "dur": 27, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275251, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275253, "dur": 28, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275283, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275285, "dur": 27, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275315, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275317, "dur": 27, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275346, "dur": 1, "ph": "X", "name": "ProcessMessages 522", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275348, "dur": 25, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275374, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275377, "dur": 25, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275404, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275405, "dur": 24, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275432, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275434, "dur": 22, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275460, "dur": 26, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275489, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275490, "dur": 24, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275516, "dur": 1, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275518, "dur": 23, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275544, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275546, "dur": 23, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275572, "dur": 23, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275597, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275599, "dur": 29, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275630, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275632, "dur": 25, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275659, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275661, "dur": 23, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275687, "dur": 19, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275708, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275710, "dur": 22, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275735, "dur": 34, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275771, "dur": 1, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275773, "dur": 30, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275804, "dur": 1, "ph": "X", "name": "ProcessMessages 631", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275806, "dur": 29, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275837, "dur": 1, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275839, "dur": 28, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275869, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275871, "dur": 20, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260275895, "dur": 325, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260276225, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260276257, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260276259, "dur": 68, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260276330, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260276333, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260276370, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260276372, "dur": 36, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260276412, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260276414, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260276455, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260276458, "dur": 43, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260276505, "dur": 3, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260276509, "dur": 36, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260276549, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260276551, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260276586, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260276588, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260276623, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260276626, "dur": 34, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260276663, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260276666, "dur": 31, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260276701, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260276703, "dur": 30, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260276736, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260276738, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260276771, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260276774, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260276804, "dur": 59, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260276866, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260276868, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260276913, "dur": 2, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260276916, "dur": 35, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260276954, "dur": 2, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260276957, "dur": 38, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260276999, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277002, "dur": 35, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277040, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277043, "dur": 37, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277084, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277087, "dur": 39, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277129, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277133, "dur": 38, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277174, "dur": 3, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277178, "dur": 34, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277215, "dur": 7, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277224, "dur": 33, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277259, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277262, "dur": 30, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277295, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277297, "dur": 30, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277330, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277332, "dur": 27, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277362, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277365, "dur": 33, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277401, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277405, "dur": 33, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277440, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277443, "dur": 36, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277482, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277486, "dur": 33, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277523, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277527, "dur": 41, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277570, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277576, "dur": 36, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277616, "dur": 2, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277619, "dur": 38, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277661, "dur": 2, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277665, "dur": 35, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277703, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277706, "dur": 38, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277747, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277751, "dur": 39, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277793, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277797, "dur": 32, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277831, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277834, "dur": 30, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277867, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277870, "dur": 30, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277903, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277906, "dur": 40, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277950, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277952, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277980, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260277982, "dur": 9555, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260287544, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260287547, "dur": 88, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260287639, "dur": 17, "ph": "X", "name": "ProcessMessages 2356", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260287657, "dur": 33, "ph": "X", "name": "ReadAsync 2356", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260287694, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260287696, "dur": 69, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260287768, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260287770, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260287801, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260287806, "dur": 26, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260287836, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260287871, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260287873, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260287910, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260287912, "dur": 110, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260288033, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260288063, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260288064, "dur": 73, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260288143, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260288171, "dur": 103, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260288277, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260288280, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260288310, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260288313, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260288347, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260288349, "dur": 26, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260288380, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260288381, "dur": 27, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260288412, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260288414, "dur": 26, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260288444, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260288468, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260288492, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260288522, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260288524, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260288556, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260288581, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260288684, "dur": 146, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260288887, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260288889, "dur": 98, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260289025, "dur": 49, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260289114, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260289257, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260289299, "dur": 22, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260289357, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260289450, "dur": 95, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260289579, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260289581, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260289662, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260289731, "dur": 54, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260289813, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260289815, "dur": 19, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260289890, "dur": 25, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260289916, "dur": 138, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260290112, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260290139, "dur": 273, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260290446, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260290520, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260290522, "dur": 244, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260290812, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260290814, "dur": 187, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260291004, "dur": 55, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260291061, "dur": 66, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260291219, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260291222, "dur": 69, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260291380, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260291423, "dur": 197, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260291677, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260291679, "dur": 109, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260291791, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260291793, "dur": 783, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260292765, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260292768, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260292891, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260292895, "dur": 142692, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260435595, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260435598, "dur": 98, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260435700, "dur": 204, "ph": "X", "name": "ProcessMessages 1012", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260435906, "dur": 7295, "ph": "X", "name": "ReadAsync 1012", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260443209, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260443212, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260443277, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260443281, "dur": 583, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260443868, "dur": 78, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260443950, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260443951, "dur": 782, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260444738, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260444803, "dur": 17, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260444822, "dur": 46458, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260491287, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260491290, "dur": 84, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260491378, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260491381, "dur": 834, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260492219, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260492221, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260492262, "dur": 19, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260492283, "dur": 1247, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260493533, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260493536, "dur": 94, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260493633, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 20040, "tid": 47244640256, "ts": 1751453260493635, "dur": 21645, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 20040, "tid": 284376, "ts": 1751453260518257, "dur": 8032, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 20040, "tid": 42949672960, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 20040, "tid": 42949672960, "ts": 1751453260246296, "dur": 95814, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 20040, "tid": 42949672960, "ts": 1751453260342111, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 20040, "tid": 42949672960, "ts": 1751453260342112, "dur": 78, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 20040, "tid": 284376, "ts": 1751453260526291, "dur": 11, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 20040, "tid": 38654705664, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 20040, "tid": 38654705664, "ts": 1751453260243528, "dur": 271792, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 20040, "tid": 38654705664, "ts": 1751453260243627, "dur": 2229, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 20040, "tid": 38654705664, "ts": 1751453260515322, "dur": 45, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 20040, "tid": 38654705664, "ts": 1751453260515339, "dur": 16, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 20040, "tid": 284376, "ts": 1751453260526309, "dur": 5, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751453260260635, "dur": 1836, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751453260262479, "dur": 874, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751453260263463, "dur": 59, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751453260263523, "dur": 519, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751453260274957, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.XR.ARFoundation.InternalUtils.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751453260264063, "dur": 11985, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751453260276058, "dur": 216323, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751453260492382, "dur": 458, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751453260492851, "dur": 217, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751453260493287, "dur": 14847, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751453260264007, "dur": 12087, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751453260276118, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260276220, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1751453260276106, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_E4AC68B67EFEC4DD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751453260276343, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751453260276595, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\iOSSupport\\UnityEditor.iOS.Extensions.Common.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260276594, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_37743AF6A5E3F6D9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751453260276707, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubstanceModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260276706, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_1DC8AFE6C7A826A4.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751453260276780, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751453260276858, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260276857, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_0659794E1AF48F77.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751453260276924, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751453260277251, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751453260277374, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751453260277611, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\mscorlib.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260277893, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260278038, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260278104, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260278164, "dur": 152, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260278317, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Web.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260278392, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Windows.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260278527, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260278591, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260278647, "dur": 175, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260278823, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260278889, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Buffers.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260278948, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260279007, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260279063, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260279120, "dur": 191, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260279321, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260279442, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260279522, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260279584, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260279638, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260279691, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260279754, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260279812, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260279863, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260279918, "dur": 351, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260280271, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260280335, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260280391, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260280444, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260280496, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260280548, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260280600, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260280658, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260280718, "dur": 421, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260281140, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260281205, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260281263, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260281315, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260281388, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260281458, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260281527, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260281588, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260281659, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260281740, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260281808, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260281871, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260281929, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260282025, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260282113, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260282204, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260282286, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260282356, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260282445, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260282521, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260282594, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260282671, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260282772, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260282839, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260282893, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260282949, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260283086, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260283141, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260283199, "dur": 183, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751453260277465, "dur": 6083, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751453260283691, "dur": 594, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751453260284285, "dur": 397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751453260284683, "dur": 564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751453260285247, "dur": 923, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751453260286212, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751453260286420, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751453260286676, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751453260286799, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751453260287043, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751453260287125, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751453260287287, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751453260287614, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751453260287817, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751453260287921, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751453260287987, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751453260288043, "dur": 422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751453260288609, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751453260288702, "dur": 475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751453260289178, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751453260289298, "dur": 340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751453260289638, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751453260289743, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751453260289846, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751453260290124, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751453260290174, "dur": 296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751453260290504, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751453260290989, "dur": 396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751453260291412, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751453260291815, "dur": 151207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751453260443022, "dur": 49460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260264571, "dur": 11710, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260276291, "dur": 185, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751453260276283, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_128BDF40C52915E6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751453260276527, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751453260276526, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_486B4E27CA46B2F7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751453260276632, "dur": 188, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ProfilerModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751453260276631, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_C0DCB9053A2446E9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751453260276863, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751453260276861, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_4A837DB03C61DFDC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751453260276922, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260277097, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.NVIDIAModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751453260277096, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_50C4BCFC618A644B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751453260277165, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260277301, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751453260277559, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751453260277692, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751453260277806, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751453260277960, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260278381, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260278693, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260279020, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260279282, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260279554, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260279845, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260280417, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260280715, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260280985, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260281256, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260281786, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260282081, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260282410, "dur": 1202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260283612, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260283680, "dur": 583, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260284264, "dur": 962, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260285227, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751453260285433, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751453260285935, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260286185, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260286411, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260286662, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260286814, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260287051, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260287122, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260287215, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260287299, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260287603, "dur": 219, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260287853, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260287913, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260287985, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260288056, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260288471, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260288596, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Simulation.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751453260288729, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260288875, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Simulation.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751453260289327, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Simulation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751453260289432, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Simulation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751453260289876, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260290177, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260290502, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260290937, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260290997, "dur": 369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260291410, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751453260291530, "dur": 291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260291821, "dur": 151209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751453260443030, "dur": 49354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260264165, "dur": 11954, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260276133, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.GradleProject.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751453260276233, "dur": 165, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1751453260276123, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.GradleProject.dll_29F2B64BD1A1DCB9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751453260276436, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751453260276435, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_977DF1413C425466.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751453260276502, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260276612, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751453260276611, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_3977A7D73E1EE0EE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751453260276741, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751453260276740, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_F7240E92754989BB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751453260276808, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260276903, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\UnityEditor.Android.Extensions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751453260276902, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_6421414B18C06C7C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751453260277025, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260277329, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.CoreUtils.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751453260277948, "dur": 368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260278316, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260278595, "dur": 905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260279501, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260279799, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260280355, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260280628, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260280905, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260281330, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260281742, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260282043, "dur": 1157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260283200, "dur": 436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260283636, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260283693, "dur": 624, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260284318, "dur": 358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260284676, "dur": 554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260285232, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751453260285442, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260285544, "dur": 687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751453260286231, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260286456, "dur": 226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260286682, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260286773, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260286943, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260287006, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260287059, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260287124, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260287230, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260287298, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260287615, "dur": 198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260287850, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260287924, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260287983, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260288036, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARSubsystems.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751453260288140, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARSubsystems.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751453260288605, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260288682, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260288763, "dur": 398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260289173, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260289293, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260289636, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260289746, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260289853, "dur": 269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260290122, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260290174, "dur": 318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260290493, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260290987, "dur": 372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260291412, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260291812, "dur": 151205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751453260443018, "dur": 49459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260264134, "dur": 11979, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260276128, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Gradle.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751453260276222, "dur": 204, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1751453260276122, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_6BA57B641639B2BC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751453260276428, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260276570, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GameCenterModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751453260276568, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_C1CB3B5C02834557.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751453260276682, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751453260276681, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_2DFC1E35CC3D93F5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751453260276837, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751453260276836, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_9C11D1C715218856.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751453260276900, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260277351, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1751453260277482, "dur": 215, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751453260277754, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VSCode.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751453260277925, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751453260277999, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260278444, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260278745, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260279034, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260279295, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260279609, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260279922, "dur": 537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260280460, "dur": 398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260280858, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260281129, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260281417, "dur": 377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260281794, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260282121, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260282405, "dur": 1205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260283610, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260283682, "dur": 580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260284283, "dur": 936, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260285232, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751453260285520, "dur": 746, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751453260286267, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260286415, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751453260286559, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260286779, "dur": 709, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751453260287603, "dur": 209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260287916, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260287994, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260288045, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260288603, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260288681, "dur": 475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260289175, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260289290, "dur": 338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260289628, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260289688, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260289740, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260289860, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260290165, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260290498, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260290935, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260291002, "dur": 358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260291409, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260291827, "dur": 151189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751453260443016, "dur": 49469, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260264194, "dur": 11932, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260276136, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Types.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751453260276218, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1751453260276129, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_9B13BE4380FB0740.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751453260276327, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260276458, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751453260276456, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_8C88AE4D2CCD7158.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751453260276539, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DirectorModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751453260276538, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_8E967FCEA0CABFDC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751453260276601, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260276691, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751453260276690, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_AAF7ADEC23F2DAD1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751453260276808, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751453260276806, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_C3A7C19B07D61835.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751453260276908, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\iOSSupport\\UnityEditor.iOS.Extensions.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751453260276907, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_2A03BB7F03493A3C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751453260277226, "dur": 273, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.SpatialTracking.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751453260277604, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751453260277892, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Simulation.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751453260277972, "dur": 633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260278605, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260278960, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260279223, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260279513, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260279808, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260280372, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260280645, "dur": 399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260281044, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260281311, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260281838, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260282146, "dur": 977, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260283123, "dur": 498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260283621, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260283692, "dur": 589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260284281, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260284673, "dur": 560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260285234, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751453260285438, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\Demigiant\\DOTween\\Editor\\DOTweenEditor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751453260285435, "dur": 672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751453260286108, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260286387, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.SpatialTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751453260286528, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.SpatialTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751453260287060, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260287160, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260287216, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260287274, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.CoreUtils.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751453260287411, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.CoreUtils.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751453260287937, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.CoreUtils.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751453260288035, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.CoreUtils.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751453260288450, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260288565, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260288620, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260288693, "dur": 467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260289176, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260289295, "dur": 331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260289626, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260289739, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260289852, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260290164, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260290495, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260290928, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260290983, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260291409, "dur": 398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260291826, "dur": 151195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751453260443022, "dur": 49368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751453260264239, "dur": 11901, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751453260276150, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751453260276230, "dur": 344, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1751453260276142, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_8360DAA503BDFED4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751453260276617, "dur": 209, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751453260276616, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_94317CF45651DD49.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751453260276856, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751453260276854, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_C1E653EE65094627.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751453260276921, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751453260277138, "dur": 190, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751453260277367, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751453260277758, "dur": 154, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751453260277938, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751453260278028, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751453260278472, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751453260278734, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751453260279016, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751453260279447, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751453260279754, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751453260280397, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751453260280692, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751453260280950, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751453260281219, "dur": 377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751453260281596, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751453260282130, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751453260282472, "dur": 1147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751453260283619, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751453260283684, "dur": 580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751453260284264, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751453260284670, "dur": 517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751453260285209, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARAnalytics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751453260285532, "dur": 568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARAnalytics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751453260286100, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751453260286414, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751453260286664, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751453260286818, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751453260287044, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751453260287148, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751453260287225, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751453260287278, "dur": 240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751453260287562, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751453260287613, "dur": 198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751453260287852, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751453260287903, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751453260287982, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751453260288037, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751453260288147, "dur": 411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751453260288637, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751453260288795, "dur": 981, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751453260289879, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751453260289960, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751453260290489, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751453260290579, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751453260291017, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751453260291089, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751453260291416, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751453260291525, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751453260291821, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751453260292749, "dur": 142972, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751453260443317, "dur": 572, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751453260443006, "dur": 941, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751453260444009, "dur": 48465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751453260264217, "dur": 11917, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751453260276144, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751453260276229, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1751453260276137, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_24157DE88EB6BDBE.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751453260276355, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751453260276514, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751453260276513, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_096F3F085FA84B73.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751453260276628, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PhysicsModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751453260276627, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_3C9063487E7B345B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751453260276746, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TilemapModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751453260276745, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_F5D346386BF65E52.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751453260276813, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751453260276872, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751453260276870, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_078592F870AFC5CC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751453260277141, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751453260277263, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751453260277366, "dur": 160, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARSubsystems.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751453260277527, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751453260277601, "dur": 175, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751453260277800, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751453260277889, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751453260277965, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751453260278416, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751453260278678, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751453260279010, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751453260279292, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751453260279608, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751453260279915, "dur": 575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751453260280491, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751453260280792, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751453260281057, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751453260281331, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751453260281775, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751453260281954, "dur": 1224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751453260283179, "dur": 445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751453260283624, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751453260283689, "dur": 606, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751453260284295, "dur": 929, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751453260285225, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751453260285467, "dur": 1269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751453260286843, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751453260286954, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751453260287266, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751453260287602, "dur": 206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751453260287910, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751453260287981, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751453260288040, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.FaceTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751453260288142, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.FaceTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751453260288570, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751453260288621, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751453260288694, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751453260289170, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751453260289244, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751453260289354, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751453260289782, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751453260289848, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751453260290165, "dur": 337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751453260290502, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751453260290939, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751453260290991, "dur": 398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751453260291417, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751453260291818, "dur": 151208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751453260443026, "dur": 49447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260264265, "dur": 11913, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260276189, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751453260276260, "dur": 182, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 8, "ts": 1751453260276182, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_384C1F3E632FD32D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751453260276444, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260276591, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751453260276590, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_0DD16C5308008CDA.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751453260276704, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751453260276703, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_411D9B9D7C552896.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751453260276825, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751453260276823, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_A06F1EA8D8FA1A33.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751453260276889, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260277017, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_A06F1EA8D8FA1A33.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751453260277125, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751453260277359, "dur": 182, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751453260277605, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260277668, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.2D.Sprite.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751453260277950, "dur": 476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260278426, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260278691, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260278986, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260279248, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260279932, "dur": 636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260280568, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260280852, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260281101, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260281434, "dur": 413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260281847, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260282141, "dur": 1280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260283421, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260283622, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260283713, "dur": 564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260284278, "dur": 944, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260285223, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751453260285372, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260285569, "dur": 497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751453260286067, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260286453, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260286670, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260286794, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260286991, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260287061, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260287160, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260287220, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260287275, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751453260287389, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751453260287865, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260287923, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260288005, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260288055, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260288604, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260288682, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260289174, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260289310, "dur": 324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260289634, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260289745, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260289845, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260290162, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260290492, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260290929, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260291001, "dur": 363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260291413, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260291824, "dur": 151217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751453260443042, "dur": 49441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260264297, "dur": 11889, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260276196, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751453260276273, "dur": 157, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1751453260276189, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_A16031C8D5E7F23B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751453260276485, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751453260276484, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_D301E026C7E6BC4A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751453260276620, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751453260276619, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_9B7D977438583E85.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751453260276744, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751453260276743, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_99C9B018C409BC13.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751453260276849, "dur": 166, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751453260276847, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_39AD4EBB1D1424EB.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751453260277091, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751453260277090, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_55E4B2A1BED5EA50.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751453260277324, "dur": 220, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751453260277593, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Simulation.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1751453260277669, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1751453260277937, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1751453260278019, "dur": 393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260278413, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260278672, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260278983, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260279241, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260279512, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260279819, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260280398, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260280650, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260280957, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260281273, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260281767, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260282046, "dur": 516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260282563, "dur": 1043, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260283638, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260283695, "dur": 577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260284272, "dur": 396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260284695, "dur": 525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260285222, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751453260285465, "dur": 700, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751453260286166, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260286410, "dur": 240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260286664, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260286794, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260287064, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260287141, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260287279, "dur": 269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260287601, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260287849, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260287905, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260288046, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260288628, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260288698, "dur": 465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260289173, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260289312, "dur": 317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260289629, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260289695, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260289747, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260289851, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260290166, "dur": 339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260290505, "dur": 395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260290930, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260290996, "dur": 374, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260291416, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260291819, "dur": 151205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751453260443025, "dur": 49444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260264320, "dur": 11878, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260276212, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751453260276290, "dur": 242, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": 1751453260276203, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_CA136C2D17F2F281.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751453260276534, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260276640, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751453260276639, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_EF067A9A756EA993.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751453260276765, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751453260276764, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_19E375B5F566FEDB.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751453260276876, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751453260276875, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_1CD089203CF9E868.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751453260277114, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751453260277112, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_CBE4C2A7CAF71BDD.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751453260277409, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Simulation.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1751453260277593, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.FaceTracking.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1751453260277975, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260278417, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260278677, "dur": 339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260279016, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260279279, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260279544, "dur": 397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260279941, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260280509, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260280784, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260281056, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260281334, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260281679, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260281952, "dur": 371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260282323, "dur": 1300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260283623, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260283687, "dur": 574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260284282, "dur": 396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260284678, "dur": 554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260285236, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751453260285587, "dur": 699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751453260286414, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260286687, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260286798, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260287045, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260287142, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260287276, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751453260287445, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751453260287776, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751453260287918, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260288045, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260288629, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260288696, "dur": 472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260289194, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260289291, "dur": 340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260289631, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260289740, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260289839, "dur": 276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260290179, "dur": 324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260290503, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260290931, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260291000, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260291416, "dur": 402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260291818, "dur": 151188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751453260443032, "dur": 48295, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751453260443030, "dur": 48299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751453260491347, "dur": 987, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751453260264353, "dur": 11854, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751453260276221, "dur": 155, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751453260276211, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_A0462AF7F9C7DD5D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751453260276562, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DSPGraphModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751453260276561, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_93E7E138132B90FD.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751453260276719, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751453260276718, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_15A5144DF23AC2E4.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751453260276840, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751453260276839, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_BB89E70A60043324.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751453260276910, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751453260277092, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751453260277091, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_3DF8E1AFD2EAE0B1.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751453260277189, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_3DF8E1AFD2EAE0B1.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751453260277286, "dur": 154, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1751453260277590, "dur": 155, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.VisualScripting.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751453260277760, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.VisualScripting.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751453260277924, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751453260277990, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751453260278439, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751453260278698, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751453260278987, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751453260279263, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751453260279541, "dur": 414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751453260279955, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751453260280555, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751453260280852, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751453260281115, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751453260281404, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751453260281662, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751453260282134, "dur": 609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751453260282744, "dur": 876, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751453260283620, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751453260283690, "dur": 600, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751453260284290, "dur": 915, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751453260285217, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751453260285506, "dur": 576, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751453260286087, "dur": 521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751453260286609, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751453260286801, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751453260287242, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751453260286998, "dur": 523, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751453260287522, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751453260287637, "dur": 181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751453260287851, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751453260287918, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751453260288047, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751453260288607, "dur": 85, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751453260288692, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751453260289175, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751453260289295, "dur": 337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751453260289633, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751453260289752, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751453260289862, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751453260290162, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751453260290492, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751453260290993, "dur": 385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751453260291420, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751453260291820, "dur": 151207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751453260443028, "dur": 49364, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260264378, "dur": 11836, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260276228, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751453260276217, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_BE03322452709CB2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751453260276311, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260276380, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751453260276379, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_A9EADF60550F98A5.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751453260276436, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260276625, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751453260276624, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_D92E9E3DCDCA68ED.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751453260276766, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260276827, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751453260276825, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_01045232C2BAAC12.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751453260276901, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260277279, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1751453260277633, "dur": 151, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1751453260277964, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260278474, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260278901, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260279201, "dur": 391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260279593, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260279893, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260280453, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260280720, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260280985, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260281240, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260281488, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260281983, "dur": 1278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260283261, "dur": 350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260283611, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260283707, "dur": 560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260284268, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260284676, "dur": 578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260285254, "dur": 914, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260286411, "dur": 240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260286696, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260286796, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260287066, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260287122, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260287281, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260287621, "dur": 194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260287857, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260287914, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260287993, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260288044, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260288611, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260288683, "dur": 472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260289180, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260289296, "dur": 339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260289635, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260289697, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260289748, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260289842, "dur": 269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260290177, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260290493, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260290933, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260290984, "dur": 378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260291410, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260291809, "dur": 50506, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260342316, "dur": 1991, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260344307, "dur": 98705, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751453260443012, "dur": 49370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260264408, "dur": 11814, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260276234, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1751453260276226, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_79696B0654C8F4B7.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751453260276339, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260276488, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1751453260276487, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_39EA3D21A2039B2E.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751453260276615, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1751453260276613, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_56A29088F8660FBC.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751453260276696, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260276768, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1751453260276767, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_B4B36C72CEF54054.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751453260276832, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260277273, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Management.Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1751453260277408, "dur": 190, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Simulation.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1751453260277617, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.SpatialTracking.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1751453260277730, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751453260277948, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260278430, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260278704, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260278990, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260279272, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260279560, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260279831, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260280397, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260280680, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260280974, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260281232, "dur": 507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260281739, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260282043, "dur": 1158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260283201, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260283617, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260283694, "dur": 582, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260284276, "dur": 398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260284674, "dur": 577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260285251, "dur": 918, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260286171, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Unity.EditorCoroutines.Editor.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1751453260286170, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1751453260286417, "dur": 231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260286680, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260286793, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260287040, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260287121, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260287214, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260287277, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751453260287418, "dur": 441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751453260287920, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260287992, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260288042, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260288607, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260288683, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260289176, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260289292, "dur": 339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260289631, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260289751, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260289857, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260290172, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260290499, "dur": 433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260290932, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260290992, "dur": 390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260291415, "dur": 402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260291817, "dur": 151206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751453260443024, "dur": 49456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260264434, "dur": 11796, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260276246, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1751453260276235, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_2C6FB2D62EBEEE94.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751453260276327, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260276468, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1751453260276467, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_72B29348CFCB9971.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751453260276520, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260276611, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1751453260276610, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_019E00E629175977.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751453260276694, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260276758, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1751453260276756, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_C9A4E54165F4766F.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751453260276830, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260277199, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Management.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1751453260277361, "dur": 206, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751453260277670, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1751453260277940, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751453260278102, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260278706, "dur": 754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260279461, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260279909, "dur": 770, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Platforms\\AotStubWriter.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751453260279742, "dur": 1091, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260280833, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260281108, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260281362, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260281670, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260281980, "dur": 1183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260283163, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260283629, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260283693, "dur": 623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260284317, "dur": 364, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260284681, "dur": 472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260285158, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751453260286151, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\Noise\\cellular3D.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751453260285375, "dur": 916, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751453260286292, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260286395, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751453260286541, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751453260286917, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260287124, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260287242, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260287300, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260287610, "dur": 199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260287906, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260287981, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260288041, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260288625, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260288685, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260289171, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260289294, "dur": 329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260289759, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260289843, "dur": 280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260290170, "dur": 326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260290496, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260290990, "dur": 436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260291426, "dur": 397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260291823, "dur": 151209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751453260443032, "dur": 49348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260264490, "dur": 11762, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260276265, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1751453260276257, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_FCB3152C479369C0.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751453260276415, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260276549, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll"}}, {"pid": 12345, "tid": 15, "ts": 1751453260276547, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_FCB7B30576667F66.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751453260276660, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1751453260276658, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_708A3E0E0CD23C1E.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751453260276777, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1751453260276776, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_4DD68C3F550D67BD.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751453260276840, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260277102, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1751453260277222, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.CoreUtils.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1751453260277410, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARCore.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1751453260277634, "dur": 232, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.XR.LegacyInputHelpers.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1751453260277940, "dur": 169, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751453260278110, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260278527, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260278826, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260279128, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260279430, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260279708, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260280287, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260280547, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260280859, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260281126, "dur": 339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260281465, "dur": 373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260281839, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260282125, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260282378, "dur": 1231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260283609, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260283681, "dur": 582, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260284263, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260284672, "dur": 435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260285124, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.SpatialTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751453260285417, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260285473, "dur": 578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.SpatialTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751453260286052, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260286387, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751453260286511, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751453260286924, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260287042, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260287131, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260287238, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260287293, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260287626, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260287868, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260287924, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260287986, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260288039, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARCore.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751453260288138, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260288262, "dur": 366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARCore.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751453260288720, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARCore.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751453260288810, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARCore.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751453260289190, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260289296, "dur": 341, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260289637, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260289742, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260289849, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260290127, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260290178, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260290450, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260290500, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260290988, "dur": 433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260291421, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260291822, "dur": 151211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751453260443033, "dur": 49417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751453260264462, "dur": 11779, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751453260276256, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260276245, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_C3E59BB5F8B48795.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751453260276382, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260276381, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_D6483D8C0D4E178A.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751453260276442, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751453260276578, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260276577, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_517C17530E5D22C2.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751453260276676, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751453260276740, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260276738, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_AF3BD820E5C5D09D.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751453260276807, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751453260276871, "dur": 140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260276869, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_F80B64918A78D7EF.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751453260277015, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751453260277086, "dur": 279, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_F80B64918A78D7EF.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751453260277373, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751453260277573, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\mscorlib.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260277642, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260277725, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Core.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260277782, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Data.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260277881, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Drawing.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260277984, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260278086, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260278139, "dur": 147, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260278337, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Web.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260278395, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Windows.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260278467, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260278522, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260278577, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260278639, "dur": 279, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260278921, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Buffers.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260278984, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260279038, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260279094, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260279146, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260279203, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260279261, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260279320, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260279441, "dur": 366, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260279825, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260279887, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260279939, "dur": 747, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260280732, "dur": 461, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260281194, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260281290, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260281343, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260281397, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260281458, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260281531, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260281597, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260281655, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260281735, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260281799, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260281864, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260281929, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260282025, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260282108, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260282211, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260282291, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260282371, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260282438, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260282511, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260282590, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260282669, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260282767, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260282835, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260282895, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260282953, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260283092, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260283207, "dur": 218, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751453260277459, "dur": 6159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751453260283723, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751453260283796, "dur": 417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751453260284295, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751453260284374, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751453260285118, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751453260285416, "dur": 881, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751453260286301, "dur": 859, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751453260287271, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751453260287447, "dur": 1088, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751453260288635, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751453260288744, "dur": 823, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751453260289666, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751453260289780, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751453260290167, "dur": 340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751453260290508, "dur": 391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751453260290992, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751453260291411, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751453260291810, "dur": 52500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751453260344310, "dur": 98709, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751453260443019, "dur": 49432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751453260264524, "dur": 11740, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751453260276277, "dur": 294, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1751453260276268, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_30DC5CCDD2C811D2.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751453260276574, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751453260276656, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1751453260276655, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_8C9666DB874133B6.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751453260276734, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751453260276805, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1751453260276804, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_BB1D083DE47F9081.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751453260276892, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751453260277135, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1751453260277231, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1751453260277342, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751453260277529, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751453260277716, "dur": 163, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.Editor.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1751453260277932, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751453260278009, "dur": 395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751453260278404, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751453260278695, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751453260278978, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751453260279237, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751453260279542, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751453260279825, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751453260280370, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751453260280654, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751453260280938, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751453260281220, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751453260281564, "dur": 371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751453260281935, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751453260282288, "dur": 1327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751453260283615, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751453260283685, "dur": 580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751453260284266, "dur": 405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751453260284671, "dur": 489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751453260285165, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751453260285380, "dur": 690, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1751453260286070, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751453260286190, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751453260286418, "dur": 229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751453260286672, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751453260286803, "dur": 190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751453260287039, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751453260287123, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751453260287214, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751453260287272, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARSubsystems.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751453260287439, "dur": 486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARSubsystems.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1751453260288043, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.InternalUtils.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751453260288166, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.InternalUtils.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1751453260288594, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751453260288694, "dur": 431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1751453260289204, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751453260289304, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1751453260289685, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.VisualScripting.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751453260289802, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.VisualScripting.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1751453260290203, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751453260290491, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751453260290578, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1751453260290930, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751453260290984, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751453260291408, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751453260291816, "dur": 151204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751453260443020, "dur": 49375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260264548, "dur": 11726, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260276286, "dur": 250, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1751453260276278, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_78C4933F624BCAF0.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751453260276581, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1751453260276580, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_83F2C9E5C417FFF6.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751453260276698, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1751453260276697, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_89BA132246C7DB90.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751453260276790, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1751453260276788, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_BF543B2CA89F5C1C.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751453260276907, "dur": 155, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 18, "ts": 1751453260276899, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_1182C7970F80B506.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751453260277101, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_1182C7970F80B506.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751453260277198, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751453260277364, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1751453260277589, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Simulation.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1751453260277940, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1751453260278035, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260278492, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260278746, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260279062, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260279307, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260279584, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260279865, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260280443, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260280722, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260280997, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260281271, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260281676, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260281976, "dur": 1276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260283252, "dur": 361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260283613, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260283704, "dur": 574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260284278, "dur": 397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260284675, "dur": 453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260285165, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Management.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751453260285521, "dur": 492, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260286167, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1751453260286017, "dur": 578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Management.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1751453260286719, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Management.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751453260287242, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.xr.management@4.4.0\\Editor\\Metadata\\XRPackageMetadata.cs"}}, {"pid": 12345, "tid": 18, "ts": 1751453260286884, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Management.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1751453260287337, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260287559, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260287639, "dur": 184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260287862, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260287915, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260288047, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260288605, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260288684, "dur": 481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260289188, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260289297, "dur": 337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260289634, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260289742, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260289850, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260290175, "dur": 288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260290498, "dur": 405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260290985, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260291415, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260291814, "dur": 151195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751453260443011, "dur": 251, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 18, "ts": 1751453260443010, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 18, "ts": 1751453260443300, "dur": 1597, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 18, "ts": 1751453260444899, "dur": 47533, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260264107, "dur": 12000, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260276120, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1751453260276211, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 19, "ts": 1751453260276110, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_3A4455FCF9EA132A.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751453260276311, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260276432, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_B93FCE4572AE3AA8.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751453260276520, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CoreModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1751453260276519, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_A681ADF7ABC369FF.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751453260276608, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1751453260276607, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_0B617F8D8E18502B.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751453260276721, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1751453260276720, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_56D6DEB754397D49.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751453260276823, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1751453260276822, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_BFA54F7E9CE16345.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751453260276926, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\WebGLSupport\\UnityEditor.WebGL.Extensions.dll"}}, {"pid": 12345, "tid": 19, "ts": 1751453260276925, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_30584634827FBF1C.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751453260277097, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_30584634827FBF1C.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751453260277709, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1751453260277977, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751453260278115, "dur": 389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260278504, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260278773, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260279056, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260279324, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260279623, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260279950, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260280549, "dur": 340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260280890, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260281165, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260281419, "dur": 414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260281833, "dur": 352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260282185, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260282737, "dur": 886, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260283623, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260283712, "dur": 553, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260284265, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260284689, "dur": 528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260285220, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751453260285523, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 19, "ts": 1751453260285504, "dur": 736, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1751453260286241, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260286462, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751453260286609, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1751453260287151, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260287234, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260287284, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260287608, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260287816, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260287908, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260288045, "dur": 407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260288686, "dur": 488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260289174, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260289289, "dur": 338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260289627, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260289689, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260289741, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260289840, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260290113, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260290163, "dur": 280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260290502, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.VisualScripting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751453260290591, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.VisualScripting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1751453260290995, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260291407, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751453260291523, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260291811, "dur": 151199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260443011, "dur": 1018, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751453260444030, "dur": 48393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260264598, "dur": 11689, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260276296, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751453260276288, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_1935086DF322E590.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751453260276368, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260276610, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\iOSSupport\\UnityEditor.iOS.Extensions.Xcode.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751453260276609, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_32B1888F61CF7983.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751453260276729, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainPhysicsModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751453260276728, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_9BF84047519B3912.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751453260276801, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260276890, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751453260276888, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_C7FE3A462D97940C.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751453260277018, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260277245, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751453260277363, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARAnalytics.Editor.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1751453260277623, "dur": 169, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1751453260277853, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.VisualScripting.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751453260277954, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260278411, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260278673, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260279000, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260279260, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260279555, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260279853, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260280418, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260280693, "dur": 376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260281069, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260281429, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260282070, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260282396, "dur": 1212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260283635, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260283691, "dur": 592, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260284283, "dur": 953, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260285237, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751453260285597, "dur": 739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751453260286336, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260286695, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260286802, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260287044, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260287146, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260287280, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260287609, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260287868, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260287922, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260288042, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260288606, "dur": 85, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260288691, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260289177, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260289292, "dur": 333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260289757, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260289841, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260290161, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260290491, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260290931, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260290986, "dur": 436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260291422, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260291821, "dur": 151213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751453260443035, "dur": 49408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751453260511548, "dur": 3343, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 20040, "tid": 284376, "ts": 1751453260526346, "dur": 14, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 20040, "tid": 284376, "ts": 1751453260526389, "dur": 950, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 20040, "tid": 284376, "ts": 1751453260518208, "dur": 9157, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}