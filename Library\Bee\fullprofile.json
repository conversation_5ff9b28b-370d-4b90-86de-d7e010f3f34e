{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 20040, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 20040, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 20040, "tid": 203803, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 20040, "tid": 203803, "ts": 1751452367529401, "dur": 621, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 20040, "tid": 203803, "ts": 1751452367533456, "dur": 897, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 20040, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 20040, "tid": 1, "ts": 1751452366338243, "dur": 5111, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 20040, "tid": 1, "ts": 1751452366343359, "dur": 59511, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 20040, "tid": 1, "ts": 1751452366402879, "dur": 42283, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 20040, "tid": 203803, "ts": 1751452367534363, "dur": 12, "ph": "X", "name": "", "args": {}}, {"pid": 20040, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366336307, "dur": 8508, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366344817, "dur": 1177495, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366345877, "dur": 2733, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366348616, "dur": 1417, "ph": "X", "name": "ProcessMessages 20512", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366350036, "dur": 254, "ph": "X", "name": "ReadAsync 20512", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366350294, "dur": 19, "ph": "X", "name": "ProcessMessages 20481", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366350316, "dur": 75, "ph": "X", "name": "ReadAsync 20481", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366350393, "dur": 1, "ph": "X", "name": "ProcessMessages 1186", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366350396, "dur": 26, "ph": "X", "name": "ReadAsync 1186", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366350425, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366350426, "dur": 59, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366350490, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366350534, "dur": 1, "ph": "X", "name": "ProcessMessages 1711", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366350537, "dur": 22, "ph": "X", "name": "ReadAsync 1711", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366350561, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366350562, "dur": 24, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366350589, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366350619, "dur": 25, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366350648, "dur": 23, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366350673, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366350675, "dur": 25, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366350703, "dur": 17, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366350723, "dur": 18, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366350745, "dur": 25, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366350772, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366350773, "dur": 22, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366350799, "dur": 22, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366350824, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366350825, "dur": 21, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366350850, "dur": 21, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366350873, "dur": 25, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366350902, "dur": 24, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366350928, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366350929, "dur": 26, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366350959, "dur": 25, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366350986, "dur": 21, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351011, "dur": 24, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351038, "dur": 22, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351064, "dur": 25, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351091, "dur": 21, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351114, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351115, "dur": 21, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351139, "dur": 28, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351170, "dur": 1, "ph": "X", "name": "ProcessMessages 904", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351171, "dur": 23, "ph": "X", "name": "ReadAsync 904", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351198, "dur": 23, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351224, "dur": 19, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351247, "dur": 23, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351273, "dur": 21, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351298, "dur": 24, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351324, "dur": 22, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351349, "dur": 20, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351372, "dur": 22, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351398, "dur": 23, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351424, "dur": 21, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351449, "dur": 25, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351477, "dur": 23, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351504, "dur": 24, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351531, "dur": 26, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351559, "dur": 1, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351560, "dur": 26, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351589, "dur": 22, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351613, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351614, "dur": 23, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351640, "dur": 17, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351661, "dur": 21, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351685, "dur": 27, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351714, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351716, "dur": 24, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351743, "dur": 20, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351765, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351767, "dur": 19, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351789, "dur": 26, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351818, "dur": 22, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351843, "dur": 23, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351869, "dur": 23, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351894, "dur": 1, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351896, "dur": 20, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351919, "dur": 19, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351940, "dur": 22, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351964, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351965, "dur": 23, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366351991, "dur": 21, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352016, "dur": 20, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352039, "dur": 19, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352061, "dur": 21, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352085, "dur": 23, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352111, "dur": 22, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352136, "dur": 24, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352164, "dur": 21, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352188, "dur": 20, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352211, "dur": 24, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352238, "dur": 5, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352244, "dur": 27, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352274, "dur": 1, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352275, "dur": 19, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352297, "dur": 20, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352320, "dur": 23, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352346, "dur": 22, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352371, "dur": 23, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352396, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352398, "dur": 23, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352424, "dur": 20, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352447, "dur": 20, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352471, "dur": 25, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352499, "dur": 24, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352526, "dur": 22, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352552, "dur": 27, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352582, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352606, "dur": 147, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352757, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352788, "dur": 24, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352815, "dur": 23, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352843, "dur": 23, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352867, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352869, "dur": 20, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352891, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352918, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352920, "dur": 24, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352947, "dur": 24, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352973, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352975, "dur": 20, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366352998, "dur": 20, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353022, "dur": 23, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353049, "dur": 30, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353083, "dur": 25, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353110, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353111, "dur": 24, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353137, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353139, "dur": 23, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353164, "dur": 24, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353190, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353191, "dur": 25, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353220, "dur": 22, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353246, "dur": 20, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353269, "dur": 21, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353292, "dur": 23, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353318, "dur": 24, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353344, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353346, "dur": 22, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353371, "dur": 20, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353395, "dur": 19, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353417, "dur": 22, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353442, "dur": 24, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353468, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353470, "dur": 24, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353498, "dur": 18, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353519, "dur": 25, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353547, "dur": 15, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353566, "dur": 21, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353590, "dur": 24, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353617, "dur": 22, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353640, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353641, "dur": 25, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353669, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353670, "dur": 18, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353692, "dur": 21, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353715, "dur": 27, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353744, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353746, "dur": 24, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353773, "dur": 20, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353796, "dur": 18, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353817, "dur": 18, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353837, "dur": 20, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353861, "dur": 31, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353895, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353920, "dur": 23, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353945, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353946, "dur": 24, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353974, "dur": 20, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366353997, "dur": 20, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354019, "dur": 21, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354044, "dur": 20, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354066, "dur": 21, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354091, "dur": 19, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354113, "dur": 21, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354137, "dur": 21, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354162, "dur": 20, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354184, "dur": 29, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354215, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354216, "dur": 27, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354246, "dur": 20, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354274, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354275, "dur": 22, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354300, "dur": 21, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354323, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354325, "dur": 22, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354351, "dur": 19, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354372, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354373, "dur": 18, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354394, "dur": 22, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354419, "dur": 24, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354445, "dur": 1, "ph": "X", "name": "ProcessMessages 715", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354447, "dur": 23, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354473, "dur": 28, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354503, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354505, "dur": 22, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354530, "dur": 22, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354555, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354557, "dur": 26, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354585, "dur": 1, "ph": "X", "name": "ProcessMessages 857", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354587, "dur": 22, "ph": "X", "name": "ReadAsync 857", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354612, "dur": 27, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354642, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354643, "dur": 19, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354665, "dur": 21, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354689, "dur": 21, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354714, "dur": 22, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354739, "dur": 25, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354767, "dur": 19, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354790, "dur": 19, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354812, "dur": 19, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354834, "dur": 22, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354859, "dur": 18, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354880, "dur": 19, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354902, "dur": 23, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354928, "dur": 19, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354950, "dur": 23, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354975, "dur": 20, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354997, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366354998, "dur": 22, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366355023, "dur": 21, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366355047, "dur": 20, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366355069, "dur": 20, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366355092, "dur": 22, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366355118, "dur": 17, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366355138, "dur": 23, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366355164, "dur": 24, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366355191, "dur": 20, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366355214, "dur": 16, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366355232, "dur": 18, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366355254, "dur": 23, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366355280, "dur": 23, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366355306, "dur": 16, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366355326, "dur": 23, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366355352, "dur": 21, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366355377, "dur": 20, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366355400, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366355402, "dur": 26, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366355430, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366355432, "dur": 19, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366355454, "dur": 57, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366355514, "dur": 74, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366355589, "dur": 1, "ph": "X", "name": "ProcessMessages 1246", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366355591, "dur": 67, "ph": "X", "name": "ReadAsync 1246", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366355660, "dur": 1, "ph": "X", "name": "ProcessMessages 1022", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366355662, "dur": 64, "ph": "X", "name": "ReadAsync 1022", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366355728, "dur": 1, "ph": "X", "name": "ProcessMessages 933", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366355730, "dur": 69, "ph": "X", "name": "ReadAsync 933", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366355801, "dur": 1, "ph": "X", "name": "ProcessMessages 823", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366355802, "dur": 71, "ph": "X", "name": "ReadAsync 823", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366355875, "dur": 1, "ph": "X", "name": "ProcessMessages 1489", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366355877, "dur": 67, "ph": "X", "name": "ReadAsync 1489", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366355946, "dur": 1, "ph": "X", "name": "ProcessMessages 1364", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366355948, "dur": 69, "ph": "X", "name": "ReadAsync 1364", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366356019, "dur": 1, "ph": "X", "name": "ProcessMessages 1137", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366356020, "dur": 66, "ph": "X", "name": "ReadAsync 1137", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366356088, "dur": 1, "ph": "X", "name": "ProcessMessages 1362", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366356090, "dur": 60, "ph": "X", "name": "ReadAsync 1362", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366356168, "dur": 53, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366356223, "dur": 2, "ph": "X", "name": "ProcessMessages 2834", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366356226, "dur": 22, "ph": "X", "name": "ReadAsync 2834", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366356250, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366356252, "dur": 23, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366356279, "dur": 27, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366356307, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366356309, "dur": 26, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366356338, "dur": 22, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366356365, "dur": 20, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366356388, "dur": 22, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366356414, "dur": 33, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366356448, "dur": 1, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366356450, "dur": 35, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366356487, "dur": 1, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366356490, "dur": 45, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366356545, "dur": 1, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366356547, "dur": 91, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366356641, "dur": 1, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366356656, "dur": 83, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366356742, "dur": 1, "ph": "X", "name": "ProcessMessages 1235", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366356745, "dur": 204, "ph": "X", "name": "ReadAsync 1235", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366356951, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366356953, "dur": 25, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366356980, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366356982, "dur": 64, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357049, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357051, "dur": 42, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357095, "dur": 1, "ph": "X", "name": "ProcessMessages 1404", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357097, "dur": 65, "ph": "X", "name": "ReadAsync 1404", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357165, "dur": 1, "ph": "X", "name": "ProcessMessages 825", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357167, "dur": 106, "ph": "X", "name": "ReadAsync 825", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357276, "dur": 1, "ph": "X", "name": "ProcessMessages 1358", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357278, "dur": 37, "ph": "X", "name": "ReadAsync 1358", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357317, "dur": 1, "ph": "X", "name": "ProcessMessages 1479", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357320, "dur": 27, "ph": "X", "name": "ReadAsync 1479", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357349, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357351, "dur": 18, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357373, "dur": 23, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357399, "dur": 24, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357426, "dur": 1, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357427, "dur": 25, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357456, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357458, "dur": 59, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357519, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357520, "dur": 26, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357548, "dur": 1, "ph": "X", "name": "ProcessMessages 1080", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357550, "dur": 23, "ph": "X", "name": "ReadAsync 1080", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357576, "dur": 22, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357600, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357601, "dur": 20, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357625, "dur": 23, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357651, "dur": 24, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357677, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357679, "dur": 24, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357705, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357706, "dur": 24, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357733, "dur": 21, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357757, "dur": 22, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357781, "dur": 1, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357783, "dur": 24, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357810, "dur": 19, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357833, "dur": 28, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357863, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357865, "dur": 25, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357893, "dur": 22, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357917, "dur": 25, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357946, "dur": 25, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357972, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357973, "dur": 24, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366357999, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366358001, "dur": 25, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366358027, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366358029, "dur": 24, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366358055, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366358056, "dur": 24, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366358083, "dur": 28, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366358113, "dur": 1, "ph": "X", "name": "ProcessMessages 694", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366358115, "dur": 19, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366358137, "dur": 22, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366358161, "dur": 1, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366358163, "dur": 23, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366358189, "dur": 29, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366358220, "dur": 1, "ph": "X", "name": "ProcessMessages 522", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366358221, "dur": 26, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366358250, "dur": 22, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366358275, "dur": 20, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366358299, "dur": 23, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366358326, "dur": 23, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366358352, "dur": 23, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366358377, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366358378, "dur": 19, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366358400, "dur": 19, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366358422, "dur": 20, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366358446, "dur": 22, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366358471, "dur": 22, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366358495, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366358496, "dur": 24, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366358523, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366358524, "dur": 23, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366358550, "dur": 20, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366358572, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366358573, "dur": 338, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366358915, "dur": 109, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366359025, "dur": 7, "ph": "X", "name": "ProcessMessages 8308", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366359033, "dur": 56, "ph": "X", "name": "ReadAsync 8308", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366359092, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366359095, "dur": 36, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366359133, "dur": 1, "ph": "X", "name": "ProcessMessages 1287", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366359135, "dur": 64, "ph": "X", "name": "ReadAsync 1287", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366359202, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366359204, "dur": 36, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366359242, "dur": 1, "ph": "X", "name": "ProcessMessages 1148", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366359244, "dur": 60, "ph": "X", "name": "ReadAsync 1148", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366359307, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366359309, "dur": 38, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366359349, "dur": 1, "ph": "X", "name": "ProcessMessages 1627", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366359351, "dur": 61, "ph": "X", "name": "ReadAsync 1627", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366359415, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366359417, "dur": 40, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366359459, "dur": 1, "ph": "X", "name": "ProcessMessages 1614", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366359461, "dur": 64, "ph": "X", "name": "ReadAsync 1614", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366359528, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366359530, "dur": 38, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366359570, "dur": 2, "ph": "X", "name": "ProcessMessages 1791", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366359574, "dur": 58, "ph": "X", "name": "ReadAsync 1791", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366359635, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366359637, "dur": 35, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366359674, "dur": 1, "ph": "X", "name": "ProcessMessages 1350", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366359676, "dur": 53, "ph": "X", "name": "ReadAsync 1350", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366359732, "dur": 1, "ph": "X", "name": "ProcessMessages 147", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366359734, "dur": 38, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366359774, "dur": 1, "ph": "X", "name": "ProcessMessages 1274", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366359775, "dur": 59, "ph": "X", "name": "ReadAsync 1274", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366359837, "dur": 1, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366359839, "dur": 38, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366359880, "dur": 1, "ph": "X", "name": "ProcessMessages 824", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366359883, "dur": 288, "ph": "X", "name": "ReadAsync 824", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366360175, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366360200, "dur": 288, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366360491, "dur": 54, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366360548, "dur": 5, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366360555, "dur": 36, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366360595, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366360598, "dur": 37, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366360640, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366360644, "dur": 32, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366360680, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366360745, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366360780, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366360783, "dur": 25, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366360810, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366360812, "dur": 32, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366360847, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366360849, "dur": 35, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366360887, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366360890, "dur": 38, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366360931, "dur": 4, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366360937, "dur": 33, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366360973, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366360976, "dur": 38, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366361017, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366361020, "dur": 38, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366361062, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366361066, "dur": 44, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366361113, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366361117, "dur": 35, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366361156, "dur": 2, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366361159, "dur": 34, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366361196, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366361199, "dur": 38, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366361240, "dur": 1, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366361243, "dur": 35, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366361281, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366361285, "dur": 43, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366361331, "dur": 2, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366361342, "dur": 83, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366361428, "dur": 2, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366361431, "dur": 32, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366361468, "dur": 3, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366361472, "dur": 31, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366361506, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366361509, "dur": 46, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366361559, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366361562, "dur": 34, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366361599, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366361602, "dur": 36, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366361641, "dur": 2, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366361645, "dur": 31, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366361679, "dur": 2, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366361682, "dur": 37, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366361722, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366361727, "dur": 26, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366361756, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366361758, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366361810, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366361839, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366361841, "dur": 5976, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366367826, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366367831, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366367914, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366367917, "dur": 60, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366367981, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366367983, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366368015, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366368084, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366368110, "dur": 430, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366368544, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366368577, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366368638, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366368697, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366368755, "dur": 278, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366369038, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366369066, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366369107, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366369139, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366369142, "dur": 206, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366369352, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366369380, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366369385, "dur": 23, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366369411, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366369413, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366369444, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366369446, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366369475, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366369502, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366369528, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366369555, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366369557, "dur": 393, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366369954, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366369980, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366369981, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366370007, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366370009, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366370044, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366370069, "dur": 161, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366370234, "dur": 455, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366370694, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366370752, "dur": 6, "ph": "X", "name": "ProcessMessages 736", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366370759, "dur": 57, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366370820, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366370822, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366370852, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366370854, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366370882, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366370911, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366370914, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366370946, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366370948, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366370977, "dur": 55, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366371036, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366371065, "dur": 91, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366371161, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366371189, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366371221, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366371250, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366371252, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366371287, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366371289, "dur": 69, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366371361, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366371364, "dur": 34, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366371403, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366371435, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366371437, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366371465, "dur": 148, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366371618, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366371689, "dur": 2, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366371692, "dur": 31, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366371727, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366371729, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366371762, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366371772, "dur": 60, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366371835, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366371837, "dur": 62, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366371902, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366371903, "dur": 81, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366371988, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366372018, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366372045, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366372080, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366372082, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366372111, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366372113, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366372144, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366372146, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366372179, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366372182, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366372218, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366372250, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366372253, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366372286, "dur": 96, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366372388, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366372426, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366372428, "dur": 55, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366372486, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366372488, "dur": 221, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366372713, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366372715, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366372750, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366372752, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366372786, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366372788, "dur": 40, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366372834, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366372869, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366372872, "dur": 35, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366372909, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366372911, "dur": 18, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366372932, "dur": 48, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366372983, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366373044, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366373046, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366373077, "dur": 93, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366373173, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366373175, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366373208, "dur": 254, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366373468, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366373499, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366373502, "dur": 27, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366373533, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366373559, "dur": 55, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366373618, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366373640, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366373661, "dur": 114, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366373781, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366373813, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366373842, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366373865, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366373937, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366373960, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366374006, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366374040, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366374045, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366374075, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366374077, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366374099, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366374138, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366374163, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366374193, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366374195, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366374219, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366374275, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366374305, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366374334, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366374336, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366374369, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366374371, "dur": 24, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366374397, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366374399, "dur": 252, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366374654, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366374677, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366374704, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366374729, "dur": 111, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366374844, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366374868, "dur": 286, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366375158, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366375193, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366375219, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366375221, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366375252, "dur": 58, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366375313, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366375316, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366375342, "dur": 58, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366375404, "dur": 271, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366375681, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366375708, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366375710, "dur": 78, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366375792, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366375815, "dur": 86, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366375905, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366375929, "dur": 132, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366376065, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366376094, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366376122, "dur": 946, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366377073, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366377097, "dur": 432, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452366377532, "dur": 958436, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367335976, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367335978, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367336009, "dur": 6946, "ph": "X", "name": "ProcessMessages 1012", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367342959, "dur": 85, "ph": "X", "name": "ReadAsync 1012", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367343049, "dur": 100, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367343152, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367343155, "dur": 1760, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367344920, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367344921, "dur": 94, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367345019, "dur": 15, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367345036, "dur": 130, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367345171, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367345237, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367345239, "dur": 48726, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367393972, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367393975, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367394018, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367394024, "dur": 642, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367394670, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367394672, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367394710, "dur": 25, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367394737, "dur": 93591, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367488336, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367488339, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367488378, "dur": 229, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367488609, "dur": 6890, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367495506, "dur": 98, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367495607, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367495609, "dur": 834, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367496448, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367496513, "dur": 15, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367496530, "dur": 2741, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367499277, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367499307, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367499310, "dur": 605, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367499920, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367499947, "dur": 21, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367499970, "dur": 667, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367500642, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367500668, "dur": 375, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452367501048, "dur": 21218, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 20040, "tid": 203803, "ts": 1751452367534376, "dur": 908, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 20040, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 20040, "tid": 8589934592, "ts": 1751452366333637, "dur": 111619, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 20040, "tid": 8589934592, "ts": 1751452366445258, "dur": 3, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 20040, "tid": 8589934592, "ts": 1751452366445263, "dur": 1177, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 20040, "tid": 203803, "ts": 1751452367535287, "dur": 5, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 20040, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 20040, "tid": 4294967296, "ts": 1751452366316788, "dur": 1206513, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 20040, "tid": 4294967296, "ts": 1751452366320991, "dur": 6592, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 20040, "tid": 4294967296, "ts": 1751452367523316, "dur": 3709, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 20040, "tid": 4294967296, "ts": 1751452367525576, "dur": 87, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 20040, "tid": 4294967296, "ts": 1751452367527127, "dur": 11, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 20040, "tid": 203803, "ts": 1751452367535293, "dur": 7, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751452366343313, "dur": 1947, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751452366345267, "dur": 846, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751452366346234, "dur": 57, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751452366346291, "dur": 451, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751452366347583, "dur": 1546, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_F7240E92754989BB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751452366349971, "dur": 1664, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_0174E068A5FA251B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751452366355198, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751452366357967, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.VisualScripting.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751452366358101, "dur": 108, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.VisualScripting.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751452366360270, "dur": 105, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.XR.ARSubsystems.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751452366346826, "dur": 14363, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751452366361198, "dur": 1140109, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751452367501309, "dur": 157, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751452367501466, "dur": 236, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751452367501870, "dur": 50, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751452367501939, "dur": 14857, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751452366347117, "dur": 14104, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366361232, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Gradle.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751452366361319, "dur": 156, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1751452366361224, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_6BA57B641639B2BC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751452366361530, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751452366361528, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_72B29348CFCB9971.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751452366361609, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366361689, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751452366361688, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_019E00E629175977.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751452366361812, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainPhysicsModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751452366361811, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_9BF84047519B3912.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751452366361919, "dur": 198, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751452366361918, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_39AD4EBB1D1424EB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751452366362432, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751452366362845, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751452366362997, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366363410, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366363683, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366364439, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366364718, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366365002, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366365631, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366365924, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366366200, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366366505, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366366956, "dur": 777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366367733, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366368045, "dur": 1131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366369177, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366369250, "dur": 651, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366369901, "dur": 578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366370490, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751452366371450, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751452366370803, "dur": 844, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751452366371648, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366371881, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366371942, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366372078, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366372284, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366372611, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366372713, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366372818, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366372954, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366373105, "dur": 245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366373350, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366373475, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366373540, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366373612, "dur": 452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366374097, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366374223, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366374311, "dur": 473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366374820, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751452366374970, "dur": 471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751452366375530, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366375701, "dur": 323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366376024, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366376088, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366376538, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366376599, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366377022, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366377429, "dur": 70363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366447793, "dur": 3178, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452366450972, "dur": 893110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452367344083, "dur": 157240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366346973, "dur": 14232, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366361230, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751452366361316, "dur": 242, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1751452366361216, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_E4AC68B67EFEC4DD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751452366361615, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DirectorModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751452366361614, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_8E967FCEA0CABFDC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751452366361728, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751452366361726, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_8C9666DB874133B6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751452366361801, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366361897, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751452366361896, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_C3A7C19B07D61835.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751452366361987, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366362248, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751452366362390, "dur": 169, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751452366362614, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.VisualScripting.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751452366362716, "dur": 181, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751452366362959, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751452366363020, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366363896, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366364172, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366364451, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366364775, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366365041, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366365583, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366366083, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366366506, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366366985, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366367541, "dur": 1253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366368795, "dur": 378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366369173, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366369245, "dur": 668, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366369914, "dur": 524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366370475, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751452366370782, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751452366370713, "dur": 802, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751452366371516, "dur": 379, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366371958, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751452366372119, "dur": 512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751452366372632, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366372776, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366372828, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366372954, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366373075, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366373346, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366373447, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366373608, "dur": 455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366374113, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366374212, "dur": 85, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366374329, "dur": 461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366374844, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366375159, "dur": 207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366375410, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366375510, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366375652, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366375710, "dur": 310, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366376020, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366376089, "dur": 428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366376517, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366376595, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366376977, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366377028, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452366377439, "dur": 966574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452367344014, "dur": 152659, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452367496675, "dur": 3857, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751452367496674, "dur": 3859, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751452367500547, "dur": 716, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751452366347209, "dur": 14064, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366361284, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751452366361363, "dur": 243, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1751452366361276, "dur": 331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_24157DE88EB6BDBE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751452366361607, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366361701, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751452366361700, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_9B7D977438583E85.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751452366361810, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751452366361809, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_56D6DEB754397D49.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751452366361907, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751452366361905, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_BFA54F7E9CE16345.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751452366361977, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366362105, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366362387, "dur": 130, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751452366362721, "dur": 190, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751452366362995, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366363464, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366363745, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366364057, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366364315, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366364570, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366364857, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366365605, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366365895, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366366223, "dur": 576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366366799, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366367227, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366367501, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366367819, "dur": 597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366368417, "dur": 753, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366369170, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366369239, "dur": 660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366369899, "dur": 577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366370477, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751452366370694, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366370775, "dur": 874, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751452366371650, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366371754, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751452366371897, "dur": 564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751452366372461, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366372642, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366372711, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366372819, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366372962, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366373073, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366373145, "dur": 207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366373352, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366373489, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366373544, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366373603, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARSubsystems.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751452366373763, "dur": 464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARSubsystems.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751452366374339, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARCore.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751452366374471, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARCore.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751452366374882, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366375149, "dur": 209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366375387, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366375499, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366375641, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366375705, "dur": 328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366376034, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366376105, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366376604, "dur": 374, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366377023, "dur": 431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452366377454, "dur": 966624, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452367344079, "dur": 157234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366347328, "dur": 14005, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366361344, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751452366361430, "dur": 234, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1751452366361336, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_CA136C2D17F2F281.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751452366361704, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751452366361702, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_D92E9E3DCDCA68ED.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751452366361839, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751452366361838, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_C9A4E54165F4766F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751452366361944, "dur": 158, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751452366361943, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_1CD089203CF9E868.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751452366362108, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366362312, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751452366362384, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751452366362619, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751452366362691, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751452366362961, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1751452366363041, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366363525, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366363797, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366364089, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366364349, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366364616, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366365110, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366365637, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366365902, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366366221, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366366704, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366367266, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366367551, "dur": 1158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366368709, "dur": 458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366369168, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366369264, "dur": 647, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366369912, "dur": 531, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366370492, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751452366370618, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366370715, "dur": 748, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751452366371464, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366371576, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366371803, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366371948, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366372056, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366372204, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366372281, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366372560, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366372614, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366372763, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366372822, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366372947, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366373077, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366373343, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366373451, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366373547, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366373616, "dur": 444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366374083, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366374213, "dur": 85, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366374299, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366374842, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366375156, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366375373, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366375503, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366375705, "dur": 337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366376042, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366376100, "dur": 442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366376620, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366377029, "dur": 412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452366377441, "dur": 966574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452367344015, "dur": 157286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452366347036, "dur": 14180, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452366361231, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366361315, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1751452366361218, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_3A4455FCF9EA132A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751452366361438, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452366361534, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366361532, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7058E5C79125A386.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751452366361609, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452366361707, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PhysicsModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366361706, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_3C9063487E7B345B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751452366361802, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452366361875, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_3C9063487E7B345B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751452366361939, "dur": 165, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366361937, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_4A837DB03C61DFDC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751452366362107, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452366362174, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_4A837DB03C61DFDC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751452366362269, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751452366362547, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\mscorlib.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366362612, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366362687, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Core.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366362784, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366362843, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Drawing.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366362912, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366362985, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366363104, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Numerics.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366363197, "dur": 158, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366363356, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366363425, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366363562, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Web.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366363631, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Windows.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366363707, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366363792, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366363850, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366363911, "dur": 135, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366364049, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366364125, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Buffers.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366364199, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366364263, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366364320, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366364379, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366364443, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366364502, "dur": 505, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366365008, "dur": 458, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366365468, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366365536, "dur": 262, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366365800, "dur": 389, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366366191, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366366303, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366366354, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366366415, "dur": 240, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366366668, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366366733, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366366785, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366366838, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366366942, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366366995, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366367046, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366367164, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366367218, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366367326, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366367384, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366367449, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366367552, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366367617, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366367689, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366367753, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366367842, "dur": 223, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366368066, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366368121, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366368187, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366368249, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366368313, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366368392, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366368471, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366368586, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366368691, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366368747, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366362423, "dur": 6714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751452366369139, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452366369291, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751452366369388, "dur": 455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751452366369944, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751452366370015, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751452366370300, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452366370468, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751452366370623, "dur": 659, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452366371493, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Drawing.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452366371288, "dur": 1404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751452366372813, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751452366372967, "dur": 1034, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751452366374111, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751452366374206, "dur": 868, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751452366375181, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751452366375280, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751452366375721, "dur": 305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452366376027, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452366376099, "dur": 426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452366376525, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452366376605, "dur": 371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452366376976, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452366377026, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452366377432, "dur": 73575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452366451008, "dur": 893018, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452367344027, "dur": 157282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366347144, "dur": 14115, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366361273, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.GradleProject.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751452366361339, "dur": 232, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1751452366361263, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.GradleProject.dll_29F2B64BD1A1DCB9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751452366361606, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CoreModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751452366361605, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_A681ADF7ABC369FF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751452366361695, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751452366361694, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_3977A7D73E1EE0EE.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751452366361771, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366361844, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751452366361842, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_19E375B5F566FEDB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751452366361956, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\iOSSupport\\UnityEditor.iOS.Extensions.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751452366361955, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_2A03BB7F03493A3C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751452366362169, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751452366362167, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_CBE4C2A7CAF71BDD.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751452366362434, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751452366362564, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARSubsystems.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751452366362721, "dur": 156, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751452366362998, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366363492, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366363764, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366364100, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366364359, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366364640, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366364910, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366365490, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366365744, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366366030, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366366304, "dur": 413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366366717, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366367157, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366367787, "dur": 1031, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366368819, "dur": 356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366369175, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366369243, "dur": 662, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366369905, "dur": 572, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366370478, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751452366370674, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366370763, "dur": 924, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751452366371688, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366371785, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751452366371925, "dur": 891, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751452366372817, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366373107, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366373409, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366373478, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366373539, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366373605, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.FaceTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751452366373725, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.FaceTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751452366374056, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366374119, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.FaceTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751452366374255, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366374314, "dur": 516, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366374841, "dur": 298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366375139, "dur": 231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366375370, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366375495, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366375634, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366375696, "dur": 332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366376028, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366376082, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.VisualScripting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751452366376204, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.VisualScripting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751452366376602, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366377027, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452366377442, "dur": 966638, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452367344080, "dur": 157240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366347296, "dur": 14022, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366361330, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751452366361402, "dur": 226, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1751452366361322, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_384C1F3E632FD32D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751452366361629, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366361697, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751452366361696, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_56A29088F8660FBC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751452366361834, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TilemapModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751452366361832, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_F5D346386BF65E52.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751452366361940, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751452366361939, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_F80B64918A78D7EF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751452366362291, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751452366362520, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751452366362716, "dur": 151, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751452366362961, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751452366363058, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366363473, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366363738, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366364037, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366364305, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366364670, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366364931, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366365468, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366365722, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366365993, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366366255, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366366832, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366367421, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366367682, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366368425, "dur": 737, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366369162, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366369261, "dur": 641, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366369902, "dur": 505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366370482, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARAnalytics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751452366370805, "dur": 511, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366371424, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DSPGraphModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751452366371548, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ProfilerModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751452366371320, "dur": 747, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARAnalytics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751452366372068, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366372281, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366372559, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366372610, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366372769, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366372824, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366372969, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366373095, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366373345, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366373464, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366373542, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366373602, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751452366373718, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751452366374253, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Simulation.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751452366374392, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Simulation.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751452366374852, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Simulation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751452366374959, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Simulation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751452366375395, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366375502, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366375642, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366375695, "dur": 324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366376093, "dur": 431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366376591, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366377022, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751452366377211, "dur": 242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452366377454, "dur": 966629, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452367344083, "dur": 157234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452366347168, "dur": 14099, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452366361282, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Types.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366361355, "dur": 234, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 8, "ts": 1751452366361270, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_9B13BE4380FB0740.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751452366361590, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452366361650, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366361648, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_517C17530E5D22C2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751452366361753, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366361752, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_89BA132246C7DB90.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751452366361850, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366361849, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_4DD68C3F550D67BD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751452366361954, "dur": 133, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\UnityEditor.Android.Extensions.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366361952, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_6421414B18C06C7C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751452366362162, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.NVIDIAModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366362161, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_50C4BCFC618A644B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751452366362272, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751452366362563, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\mscorlib.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366362620, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366362689, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Core.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366362784, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366362843, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Drawing.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366362911, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366362982, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366363081, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Numerics.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366363181, "dur": 180, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366363362, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366363426, "dur": 135, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366363563, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Web.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366363638, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Windows.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366363727, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366363784, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366363844, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366363913, "dur": 135, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366364050, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366364125, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Buffers.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366364193, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366364258, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366364313, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366364378, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366364435, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366364502, "dur": 504, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366365007, "dur": 455, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366365464, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366365539, "dur": 259, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366365800, "dur": 392, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366366194, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366366255, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366366312, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366366365, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366366420, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366366472, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366366579, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366366632, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366366685, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366366738, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366366790, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366366846, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366366901, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366366963, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366367020, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366367080, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366367140, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366367194, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366367248, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366367303, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366367357, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366367413, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366367520, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366367582, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366367651, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366367723, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366367832, "dur": 192, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366368025, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366368101, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366368164, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366368222, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366368289, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366368349, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366368444, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366368504, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366368569, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366368643, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366368701, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452366362409, "dur": 6669, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751452366369236, "dur": 683, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452366369920, "dur": 567, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452366370489, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751452366370831, "dur": 771, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751452366371603, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452366371950, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452366372088, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452366372278, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452366372611, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452366372707, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452366372763, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452366372816, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751452366372955, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751452366373266, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452366373377, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452366373460, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452366373556, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452366373620, "dur": 455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452366374075, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452366374219, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452366374312, "dur": 480, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452366374843, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452366375145, "dur": 212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452366375384, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452366375496, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452366375712, "dur": 324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452366376037, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452366376097, "dur": 435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452366376532, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452366376603, "dur": 381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452366377025, "dur": 402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452366377456, "dur": 966625, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452367344081, "dur": 157245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366347269, "dur": 14042, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366361325, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751452366361391, "dur": 228, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1751452366361315, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_8360DAA503BDFED4.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751452366361647, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GameCenterModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751452366361646, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_C1CB3B5C02834557.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751452366361770, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751452366361769, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_411D9B9D7C552896.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751452366361872, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_411D9B9D7C552896.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751452366361934, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751452366361933, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_C1E653EE65094627.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751452366362129, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366362253, "dur": 349, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1751452366362704, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1751452366363017, "dur": 408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366363426, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366363724, "dur": 850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366364575, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366364850, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366365148, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366365818, "dur": 384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366366202, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366366746, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366367190, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366367631, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366368193, "dur": 979, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366369172, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366369242, "dur": 664, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366369906, "dur": 574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366370482, "dur": 331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751452366370843, "dur": 757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751452366371601, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366371809, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366371860, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366371946, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366372076, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366372282, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366372646, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366372823, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751452366372977, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751452366373385, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366373484, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366373546, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366373625, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366374088, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366374234, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366374304, "dur": 487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366374870, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366375155, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366375372, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366375508, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366375646, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366375708, "dur": 317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366376026, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366376091, "dur": 422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366376535, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366376593, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366377030, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452366377455, "dur": 966638, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452367344094, "dur": 157224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366347324, "dur": 14001, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366361336, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751452366361428, "dur": 207, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": 1751452366361328, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_A16031C8D5E7F23B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751452366361636, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366361709, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ProfilerModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751452366361708, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_C0DCB9053A2446E9.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751452366361848, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751452366361846, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_B4B36C72CEF54054.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751452366362006, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366362091, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\DotNetZip\\Unity.VisualScripting.IonicZip.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751452366362090, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751452366362711, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1751452366362948, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751452366363001, "dur": 408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366363409, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366363742, "dur": 374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366364117, "dur": 397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366364514, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366364792, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366365102, "dur": 539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366365642, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366365976, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366366275, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366366906, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366367492, "dur": 358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366367928, "dur": 1241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366369169, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366369241, "dur": 656, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366369921, "dur": 491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366370491, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751452366370767, "dur": 791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751452366371559, "dur": 616, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366372283, "dur": 280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366372632, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366372820, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366372951, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366373074, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366373354, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366373459, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366373610, "dur": 451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366374093, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366374229, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366374299, "dur": 494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366374841, "dur": 296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366375175, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366375375, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366375502, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366375690, "dur": 327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366376038, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366376103, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366376592, "dur": 388, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366376980, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366377035, "dur": 402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452366377437, "dur": 966592, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452367344029, "dur": 157271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366347419, "dur": 13928, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366361360, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751452366361350, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_BE03322452709CB2.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751452366361449, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366361524, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751452366361523, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_977DF1413C425466.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751452366361616, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366361700, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751452366361698, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_94317CF45651DD49.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751452366361821, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751452366361820, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_AF3BD820E5C5D09D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751452366361936, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751452366361934, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_0659794E1AF48F77.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751452366362399, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1751452366362618, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1751452366362721, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1751452366363001, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366363461, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366363754, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366364040, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366364311, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366364595, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366364859, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366365146, "dur": 537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366365683, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366365951, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366366254, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366366758, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366367214, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366367517, "dur": 518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366368035, "dur": 1121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366369180, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366369252, "dur": 652, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366369904, "dur": 581, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366370487, "dur": 336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751452366370840, "dur": 786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751452366371627, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366371761, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366371883, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366371949, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366372077, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366372278, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366372609, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366372709, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366372772, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366372826, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366372950, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366373076, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366373362, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366373487, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366373548, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366373615, "dur": 502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366374117, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366374298, "dur": 488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366374786, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366374839, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366375142, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366375376, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366375519, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366375683, "dur": 339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366376022, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366376085, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366376517, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366376594, "dur": 378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366377019, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751452366377132, "dur": 311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452366377443, "dur": 966575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452367344018, "dur": 157287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452366347393, "dur": 13947, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452366361353, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751452366361429, "dur": 208, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 12, "ts": 1751452366361343, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_A0462AF7F9C7DD5D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751452366361675, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751452366361674, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_0DD16C5308008CDA.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751452366361783, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubstanceModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751452366361782, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_1DC8AFE6C7A826A4.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751452366361876, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_1DC8AFE6C7A826A4.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751452366361948, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751452366361946, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_C7FE3A462D97940C.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751452366362095, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452366362324, "dur": 323, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751452366362795, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.FaceTracking.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751452366362930, "dur": 219, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751452366363150, "dur": 468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452366363618, "dur": 861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452366364479, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452366364769, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452366365111, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452366365723, "dur": 511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452366366234, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452366366701, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452366367001, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452366367618, "dur": 1278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452366368896, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452366369167, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452366369258, "dur": 659, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452366369918, "dur": 504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452366370469, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.SpatialTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751452366370686, "dur": 662, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.SpatialTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751452366371349, "dur": 913, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452366372280, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452366372377, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751452366372527, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751452366372999, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751452366373139, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751452366373606, "dur": 458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452366374103, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452366374226, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452366374310, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452366374817, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452366374885, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452366375143, "dur": 235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452366375378, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452366375501, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452366375699, "dur": 336, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452366376036, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452366376094, "dur": 449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452366376593, "dur": 376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452366377028, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452366377434, "dur": 966582, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452367344017, "dur": 157286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366347468, "dur": 13896, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366361376, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1751452366361367, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_2C6FB2D62EBEEE94.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751452366361455, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366361514, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1751452366361513, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_D6483D8C0D4E178A.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751452366361610, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366361687, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\iOSSupport\\UnityEditor.iOS.Extensions.Xcode.dll"}}, {"pid": 12345, "tid": 13, "ts": 1751452366361686, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_32B1888F61CF7983.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751452366361824, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1751452366361823, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_F7240E92754989BB.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751452366361949, "dur": 133, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 13, "ts": 1751452366361948, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_1182C7970F80B506.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751452366362148, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1751452366362147, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_55E4B2A1BED5EA50.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751452366362436, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.VisualScripting.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1751452366362557, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751452366362630, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751452366362807, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751452366362960, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.XR.LegacyInputHelpers.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751452366363067, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366363532, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366363809, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366364097, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366364357, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366364625, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366364916, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366365484, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366365758, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366366051, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366366509, "dur": 378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366366887, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366367219, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366367613, "dur": 1321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366368935, "dur": 226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366369161, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366369235, "dur": 661, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366369919, "dur": 498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366370417, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366370506, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751452366370690, "dur": 682, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366371548, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1751452366371378, "dur": 614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751452366372057, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366372204, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366372304, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366372615, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366372721, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366372774, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366372825, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366372952, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366373074, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366373356, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366373461, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366373543, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366373617, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARCore.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751452366373740, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARCore.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751452366374231, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366374308, "dur": 481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366374844, "dur": 307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366375152, "dur": 224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366375376, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366375506, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366375689, "dur": 332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366376021, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366376086, "dur": 430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366376516, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366376590, "dur": 381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366377020, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452366377438, "dur": 966573, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452367344016, "dur": 312, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1751452367344016, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1751452367344358, "dur": 1860, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1751452367346221, "dur": 155100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452366347441, "dur": 13914, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452366361368, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1751452366361358, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_79696B0654C8F4B7.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751452366361512, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1751452366361511, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_A9EADF60550F98A5.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751452366361589, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452366361685, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1751452366361684, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_0B617F8D8E18502B.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751452366361827, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1751452366361825, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_99C9B018C409BC13.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751452366361942, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1751452366361941, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_078592F870AFC5CC.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751452366362303, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.InternalUtils.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1751452366362569, "dur": 303, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.VisualScripting.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751452366362972, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1751452366363026, "dur": 768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452366363794, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452366364100, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452366364348, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452366364948, "dur": 874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452366365823, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452366366083, "dur": 406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452366366490, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452366366918, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452366367453, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452366367754, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452366368076, "dur": 1083, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452366369160, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452366369238, "dur": 665, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452366369903, "dur": 609, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452366370512, "dur": 1075, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452366371589, "dur": 177, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Unity.VSCode.Editor.dll"}}, {"pid": 12345, "tid": 14, "ts": 1751452366371588, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VSCode.Editor.dll"}}, {"pid": 12345, "tid": 14, "ts": 1751452366371860, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452366371928, "dur": 280, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452366372281, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452366372630, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452366372713, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452366372814, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.CoreUtils.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751452366372959, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.CoreUtils.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751452366373483, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.CoreUtils.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751452366373643, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.CoreUtils.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751452366374114, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452366374216, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452366374307, "dur": 518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452366374838, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452366375140, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452366375414, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452366375497, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452366375692, "dur": 331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452366376023, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452366376089, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751452366376184, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751452366376589, "dur": 442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452366377031, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452366377449, "dur": 966570, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452367344020, "dur": 157292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452366347491, "dur": 13879, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452366361381, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1751452366361373, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_C3E59BB5F8B48795.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751452366361528, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1751452366361527, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_8C88AE4D2CCD7158.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751452366361644, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DSPGraphModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1751452366361642, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_93E7E138132B90FD.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751452366361751, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1751452366361749, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_AAF7ADEC23F2DAD1.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751452366361889, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1751452366361887, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_BB1D083DE47F9081.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751452366361976, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452366362339, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1751452366362481, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1751452366362641, "dur": 193, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.FaceTracking.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1751452366362835, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751452366362974, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751452366363033, "dur": 514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452366363547, "dur": 1124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452366364671, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452366364940, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452366365501, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452366365770, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452366366061, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452366366622, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452366367055, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452366367773, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452366368460, "dur": 716, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452366369176, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452366369251, "dur": 649, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452366369900, "dur": 584, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452366370485, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751452366370868, "dur": 1325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751452366372290, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751452366372428, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452366372638, "dur": 419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751452366373058, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452366373152, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452366373360, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452366373463, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452366373545, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452366373617, "dur": 488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452366374105, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452366374217, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452366374320, "dur": 467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452366374844, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452366375184, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.VisualScripting.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751452366375277, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.VisualScripting.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751452366375707, "dur": 324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452366376031, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452366376090, "dur": 430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452366376521, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452366376612, "dur": 358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452366376971, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452366377023, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452366377448, "dur": 966583, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452367344031, "dur": 157294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366347549, "dur": 13836, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366361396, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452366361388, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_30DC5CCDD2C811D2.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751452366361519, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452366361517, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_B93FCE4572AE3AA8.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751452366361642, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452366361640, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_FCB7B30576667F66.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751452366361739, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452366361738, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_708A3E0E0CD23C1E.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751452366361804, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366361909, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452366361907, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_A06F1EA8D8FA1A33.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751452366361987, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366362152, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452366362151, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_3DF8E1AFD2EAE0B1.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751452366362340, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751452366362431, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARAnalytics.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1751452366362719, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1751452366363036, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366363483, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366363740, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366364030, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366364488, "dur": 521, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.xr.core-utils@2.3.0\\Editor\\ScriptableSettingsProvider.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751452366364282, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366365048, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366365593, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366365895, "dur": 391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366366286, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366366780, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366367322, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366367677, "dur": 511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366368189, "dur": 966, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366369183, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366369249, "dur": 663, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366369912, "dur": 537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366370471, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751452366370700, "dur": 842, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751452366371543, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366371916, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751452366372124, "dur": 385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751452366372608, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366372765, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366372974, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366373079, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366373344, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366373474, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366373551, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366373634, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366374095, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366374217, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366374306, "dur": 477, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366374840, "dur": 306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366375147, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366375375, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366375494, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366375635, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366375703, "dur": 320, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366376024, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366376085, "dur": 438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366376523, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366376596, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366377021, "dur": 421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452366377442, "dur": 966662, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452367344105, "dur": 157210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366347514, "dur": 13863, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366361387, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1751452366361380, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_FCB3152C479369C0.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751452366361515, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1751452366361514, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_F68B074CF92AAF32.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751452366361612, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1751452366361610, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_486B4E27CA46B2F7.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751452366361715, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1751452366361714, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_EF067A9A756EA993.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751452366361795, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366361852, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1751452366361851, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_BF543B2CA89F5C1C.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751452366361958, "dur": 152, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\WebGLSupport\\UnityEditor.WebGL.Extensions.dll"}}, {"pid": 12345, "tid": 17, "ts": 1751452366361957, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_30584634827FBF1C.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751452366362112, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366362298, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1751452366362424, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751452366362620, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1751452366362953, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751452366363008, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366363471, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366363736, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366364024, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366364288, "dur": 361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366364649, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366364911, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366365668, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366365942, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366366220, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366366800, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366367265, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366367616, "dur": 1252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366368868, "dur": 311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366369179, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366369247, "dur": 663, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366369911, "dur": 785, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366370697, "dur": 891, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366371589, "dur": 197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366371867, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366371938, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366372075, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366372279, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366372614, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366372714, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366372771, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366372822, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366372944, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366373150, "dur": 206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366373356, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366373458, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366373563, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366373639, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366374100, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366374219, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366374309, "dur": 479, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366374868, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366375153, "dur": 218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366375372, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366375514, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366375647, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366375700, "dur": 339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366376039, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366376098, "dur": 438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366376536, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366376603, "dur": 421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366377024, "dur": 426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452366377450, "dur": 966634, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452367344085, "dur": 157231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452366347605, "dur": 13789, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452366361406, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1751452366361397, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_78C4933F624BCAF0.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751452366361550, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1751452366361549, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_39EA3D21A2039B2E.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751452366361669, "dur": 192, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1751452366361668, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_83F2C9E5C417FFF6.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751452366361913, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1751452366361912, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_9C11D1C715218856.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751452366361992, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452366362649, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.SpatialTracking.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1751452366362883, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARCore.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751452366363014, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452366363504, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452366363822, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452366364129, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452366364425, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452366364719, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452366364984, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452366365552, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452366365842, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452366366107, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452366366594, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452366366908, "dur": 371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452366367279, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452366367542, "dur": 1215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452366368757, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452366369158, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452366369237, "dur": 678, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452366369915, "dur": 511, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452366370427, "dur": 269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452366370696, "dur": 891, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452366371588, "dur": 180, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Unity.VSCode.Editor.pdb"}}, {"pid": 12345, "tid": 18, "ts": 1751452366371587, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VSCode.Editor.pdb"}}, {"pid": 12345, "tid": 18, "ts": 1751452366371806, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452366371952, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452366372097, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452366372184, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452366372296, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452366372635, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452366372723, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452366372814, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARSubsystems.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751452366372965, "dur": 521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARSubsystems.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1751452366373600, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.InternalUtils.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751452366373738, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.InternalUtils.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1751452366374116, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.InternalUtils.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1751452366374250, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751452366374347, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1751452366374820, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751452366374982, "dur": 466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1751452366375448, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452366375689, "dur": 341, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452366376030, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452366376093, "dur": 431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452366376524, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452366376596, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452366376976, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452366377030, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452366377436, "dur": 966573, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452367344011, "dur": 51211, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 18, "ts": 1751452367344010, "dur": 51213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 18, "ts": 1751452367395241, "dur": 766, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 18, "ts": 1751452367396010, "dur": 105305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366347794, "dur": 13617, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366361422, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1751452366361412, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_1935086DF322E590.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751452366361556, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1751452366361555, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_096F3F085FA84B73.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751452366361620, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366361684, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\iOSSupport\\UnityEditor.iOS.Extensions.Common.dll"}}, {"pid": 12345, "tid": 19, "ts": 1751452366361682, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_37743AF6A5E3F6D9.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751452366361803, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1751452366361802, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_15A5144DF23AC2E4.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751452366361910, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1751452366361909, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_01045232C2BAAC12.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751452366361993, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366362569, "dur": 166, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARAnalytics.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751452366362972, "dur": 171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751452366363144, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366363581, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366364262, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366364566, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366364830, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366365092, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366365964, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366366236, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366366767, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366367332, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366367681, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366367991, "dur": 1174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366369166, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366369244, "dur": 669, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366369913, "dur": 515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366370471, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Management.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751452366370712, "dur": 689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Management.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1751452366371402, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366371675, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Management.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751452366371879, "dur": 593, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Management.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1751452366372473, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366372645, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366372715, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366372773, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366372832, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366372968, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366373081, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366373359, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366373483, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366373545, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366373614, "dur": 468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366374082, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366374224, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366374303, "dur": 487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366374843, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366375159, "dur": 202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366375373, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366375500, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366375638, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366375697, "dur": 330, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366376028, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366376087, "dur": 439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366376526, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366376610, "dur": 422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366377032, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452366377446, "dur": 966574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452367344021, "dur": 157290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452366347655, "dur": 13749, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452366361416, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452366361407, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_128BDF40C52915E6.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751452366361535, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452366361534, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_D301E026C7E6BC4A.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751452366361660, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_D301E026C7E6BC4A.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751452366361745, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452366361744, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_2DFC1E35CC3D93F5.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751452366361917, "dur": 158, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452366361916, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_BB89E70A60043324.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751452366362336, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Management.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1751452366362428, "dur": 201, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARSubsystems.Editor.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1751452366362720, "dur": 235, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VSCode.Editor.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1751452366363032, "dur": 753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452366363785, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452366364107, "dur": 413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452366364521, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452366364784, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452366365450, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452366365720, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452366366035, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452366366287, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452366366850, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452366367294, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452366367470, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452366367779, "dur": 1165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452366368945, "dur": 229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452366369175, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452366369240, "dur": 674, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452366369914, "dur": 518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452366370472, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751452366370741, "dur": 689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751452366371431, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452366371812, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452366371934, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452366372079, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452366372277, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452366372379, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.SpatialTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751452366372498, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.SpatialTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751452366372971, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452366373077, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452366373364, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452366373478, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452366373549, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452366373630, "dur": 481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452366374114, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751452366374270, "dur": 997, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751452366375268, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452366375413, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751452366375516, "dur": 447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751452366376092, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751452366376182, "dur": 341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751452366376624, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751452366376697, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751452366377021, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751452366377134, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751452366377427, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751452366378405, "dur": 958844, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751452367344305, "dur": 1793, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452367344010, "dur": 2145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751452367346436, "dur": 60, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452367346505, "dur": 143143, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751452367496669, "dur": 150, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 20, "ts": 1751452367496668, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 20, "ts": 1751452367496834, "dur": 894, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 20, "ts": 1751452367497731, "dur": 3573, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751452367519969, "dur": 3249, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 20040, "tid": 203803, "ts": 1751452367535943, "dur": 38942, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 20040, "tid": 203803, "ts": 1751452367574924, "dur": 2247, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 20040, "tid": 203803, "ts": 1751452367532028, "dur": 46010, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}