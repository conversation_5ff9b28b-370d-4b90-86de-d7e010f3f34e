{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 20040, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 20040, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 20040, "tid": 111792, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 20040, "tid": 111792, "ts": 1751451371064986, "dur": 571, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 20040, "tid": 111792, "ts": 1751451371069142, "dur": 841, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 20040, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 20040, "tid": 1, "ts": 1751451370797880, "dur": 4799, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 20040, "tid": 1, "ts": 1751451370802682, "dur": 46614, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 20040, "tid": 1, "ts": 1751451370849307, "dur": 50911, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 20040, "tid": 111792, "ts": 1751451371069988, "dur": 12, "ph": "X", "name": "", "args": {}}, {"pid": 20040, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370796078, "dur": 7962, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370804042, "dur": 253976, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370805146, "dur": 2813, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370807965, "dur": 1373, "ph": "X", "name": "ProcessMessages 20512", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370809341, "dur": 907, "ph": "X", "name": "ReadAsync 20512", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370810251, "dur": 13, "ph": "X", "name": "ProcessMessages 20481", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370810266, "dur": 160, "ph": "X", "name": "ReadAsync 20481", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370810428, "dur": 9, "ph": "X", "name": "ProcessMessages 13913", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370810439, "dur": 62, "ph": "X", "name": "ReadAsync 13913", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370810503, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370810506, "dur": 36, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370810544, "dur": 1, "ph": "X", "name": "ProcessMessages 1571", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370810547, "dur": 29, "ph": "X", "name": "ReadAsync 1571", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370810578, "dur": 44, "ph": "X", "name": "ReadAsync 899", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370810627, "dur": 61, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370810691, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370810693, "dur": 25, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370810719, "dur": 1, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370810721, "dur": 49, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370810774, "dur": 61, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370810837, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370810839, "dur": 33, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370810874, "dur": 1, "ph": "X", "name": "ProcessMessages 1134", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370810876, "dur": 84, "ph": "X", "name": "ReadAsync 1134", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370810965, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370810999, "dur": 1, "ph": "X", "name": "ProcessMessages 1314", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370811002, "dur": 70, "ph": "X", "name": "ReadAsync 1314", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370811075, "dur": 7, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370811084, "dur": 32, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370811118, "dur": 1, "ph": "X", "name": "ProcessMessages 1053", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370811119, "dur": 61, "ph": "X", "name": "ReadAsync 1053", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370811183, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370811185, "dur": 34, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370811221, "dur": 1, "ph": "X", "name": "ProcessMessages 1356", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370811223, "dur": 55, "ph": "X", "name": "ReadAsync 1356", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370811283, "dur": 1, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370811285, "dur": 33, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370811320, "dur": 1, "ph": "X", "name": "ProcessMessages 1278", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370811322, "dur": 70, "ph": "X", "name": "ReadAsync 1278", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370811396, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370811426, "dur": 1, "ph": "X", "name": "ProcessMessages 871", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370811428, "dur": 73, "ph": "X", "name": "ReadAsync 871", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370811504, "dur": 1, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370811506, "dur": 34, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370811542, "dur": 1, "ph": "X", "name": "ProcessMessages 1404", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370811544, "dur": 24, "ph": "X", "name": "ReadAsync 1404", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370811572, "dur": 42, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370811616, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370811618, "dur": 20, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370811641, "dur": 23, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370811667, "dur": 19, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370811688, "dur": 20, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370811711, "dur": 24, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370811740, "dur": 25, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370811768, "dur": 21, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370811794, "dur": 24, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370811821, "dur": 17, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370811842, "dur": 20, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370811864, "dur": 25, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370811892, "dur": 23, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370811919, "dur": 22, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370811943, "dur": 22, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370811968, "dur": 22, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370811993, "dur": 24, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812020, "dur": 19, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812042, "dur": 19, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812064, "dur": 32, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812099, "dur": 27, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812130, "dur": 25, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812158, "dur": 18, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812178, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812203, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812224, "dur": 131, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812357, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812387, "dur": 1, "ph": "X", "name": "ProcessMessages 670", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812389, "dur": 24, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812415, "dur": 21, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812438, "dur": 21, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812462, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812463, "dur": 20, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812488, "dur": 20, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812511, "dur": 24, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812537, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812539, "dur": 23, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812564, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812565, "dur": 24, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812593, "dur": 18, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812614, "dur": 20, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812637, "dur": 19, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812658, "dur": 23, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812685, "dur": 23, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812710, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812711, "dur": 25, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812739, "dur": 28, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812769, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812771, "dur": 25, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812800, "dur": 23, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812826, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812827, "dur": 25, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812856, "dur": 20, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812879, "dur": 19, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812901, "dur": 22, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812931, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812961, "dur": 1, "ph": "X", "name": "ProcessMessages 876", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812962, "dur": 23, "ph": "X", "name": "ReadAsync 876", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370812988, "dur": 23, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813013, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813015, "dur": 22, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813040, "dur": 16, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813060, "dur": 24, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813087, "dur": 25, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813114, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813116, "dur": 23, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813143, "dur": 20, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813166, "dur": 25, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813196, "dur": 17, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813215, "dur": 23, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813242, "dur": 22, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813268, "dur": 28, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813306, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813308, "dur": 24, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813334, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813336, "dur": 25, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813364, "dur": 22, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813388, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813390, "dur": 23, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813416, "dur": 17, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813437, "dur": 24, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813464, "dur": 16, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813483, "dur": 20, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813506, "dur": 20, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813529, "dur": 21, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813554, "dur": 23, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813580, "dur": 22, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813604, "dur": 17, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813625, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813649, "dur": 26, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813678, "dur": 21, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813702, "dur": 17, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813722, "dur": 23, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813749, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813751, "dur": 36, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813791, "dur": 24, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813817, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813818, "dur": 25, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813846, "dur": 24, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813874, "dur": 22, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813899, "dur": 16, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813919, "dur": 21, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813942, "dur": 23, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813969, "dur": 25, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813997, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370813998, "dur": 33, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814033, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814035, "dur": 20, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814057, "dur": 19, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814079, "dur": 25, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814107, "dur": 17, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814127, "dur": 18, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814148, "dur": 23, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814174, "dur": 19, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814197, "dur": 32, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814233, "dur": 22, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814256, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814258, "dur": 26, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814288, "dur": 17, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814308, "dur": 26, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814336, "dur": 1, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814338, "dur": 17, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814358, "dur": 33, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814393, "dur": 1, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814395, "dur": 25, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814422, "dur": 21, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814445, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814447, "dur": 19, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814467, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814470, "dur": 21, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814494, "dur": 25, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814521, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814523, "dur": 21, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814548, "dur": 17, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814568, "dur": 21, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814592, "dur": 15, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814611, "dur": 19, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814633, "dur": 22, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814659, "dur": 23, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814685, "dur": 20, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814707, "dur": 5, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814714, "dur": 21, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814738, "dur": 17, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814758, "dur": 21, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814780, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814782, "dur": 19, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814805, "dur": 21, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814829, "dur": 24, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814855, "dur": 1, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814856, "dur": 18, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814878, "dur": 19, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814899, "dur": 20, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814923, "dur": 21, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814947, "dur": 20, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814970, "dur": 20, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370814993, "dur": 25, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815021, "dur": 23, "ph": "X", "name": "ReadAsync 162", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815047, "dur": 21, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815071, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815072, "dur": 18, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815093, "dur": 22, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815118, "dur": 22, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815143, "dur": 19, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815166, "dur": 28, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815196, "dur": 1, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815198, "dur": 26, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815226, "dur": 25, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815255, "dur": 21, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815278, "dur": 1, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815281, "dur": 29, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815312, "dur": 30, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815344, "dur": 1, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815345, "dur": 21, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815369, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815371, "dur": 32, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815404, "dur": 1, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815406, "dur": 25, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815433, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815435, "dur": 20, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815458, "dur": 24, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815484, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815485, "dur": 22, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815511, "dur": 21, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815533, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815535, "dur": 21, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815559, "dur": 18, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815581, "dur": 19, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815604, "dur": 22, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815629, "dur": 24, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815654, "dur": 1, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815656, "dur": 24, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815683, "dur": 21, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815708, "dur": 18, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815729, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815755, "dur": 22, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815780, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815781, "dur": 18, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815808, "dur": 34, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815845, "dur": 1, "ph": "X", "name": "ProcessMessages 1070", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815847, "dur": 24, "ph": "X", "name": "ReadAsync 1070", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815874, "dur": 20, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815897, "dur": 26, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815926, "dur": 31, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815960, "dur": 1, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815961, "dur": 17, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370815981, "dur": 24, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816008, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816009, "dur": 66, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816078, "dur": 1, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816079, "dur": 35, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816116, "dur": 1, "ph": "X", "name": "ProcessMessages 1111", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816118, "dur": 24, "ph": "X", "name": "ReadAsync 1111", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816145, "dur": 23, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816172, "dur": 23, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816198, "dur": 29, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816230, "dur": 1, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816231, "dur": 21, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816256, "dur": 29, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816287, "dur": 1, "ph": "X", "name": "ProcessMessages 808", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816289, "dur": 25, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816317, "dur": 23, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816343, "dur": 23, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816370, "dur": 20, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816393, "dur": 22, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816418, "dur": 22, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816443, "dur": 25, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816472, "dur": 27, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816501, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816503, "dur": 16, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816521, "dur": 5, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816527, "dur": 26, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816555, "dur": 1, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816556, "dur": 27, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816586, "dur": 30, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816618, "dur": 1, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816620, "dur": 26, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816648, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816651, "dur": 24, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816679, "dur": 18, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816700, "dur": 18, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816721, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816750, "dur": 27, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816779, "dur": 20, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816802, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816803, "dur": 22, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816829, "dur": 21, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816853, "dur": 20, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816875, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816876, "dur": 24, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816904, "dur": 23, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816929, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816930, "dur": 19, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816952, "dur": 18, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816973, "dur": 20, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370816996, "dur": 19, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817018, "dur": 24, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817045, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817047, "dur": 23, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817074, "dur": 22, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817097, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817099, "dur": 20, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817121, "dur": 5, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817127, "dur": 27, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817155, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817157, "dur": 23, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817183, "dur": 28, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817214, "dur": 21, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817237, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817238, "dur": 26, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817266, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817267, "dur": 26, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817298, "dur": 29, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817329, "dur": 1, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817331, "dur": 46, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817380, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817382, "dur": 35, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817420, "dur": 1, "ph": "X", "name": "ProcessMessages 746", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817422, "dur": 28, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817452, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817455, "dur": 27, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817483, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817485, "dur": 21, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817510, "dur": 25, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817537, "dur": 1, "ph": "X", "name": "ProcessMessages 414", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817539, "dur": 24, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817565, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817566, "dur": 26, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817594, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817596, "dur": 110, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817710, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817754, "dur": 2, "ph": "X", "name": "ProcessMessages 2178", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817756, "dur": 25, "ph": "X", "name": "ReadAsync 2178", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817783, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817785, "dur": 25, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817811, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817813, "dur": 21, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817837, "dur": 23, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817862, "dur": 20, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817886, "dur": 24, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817913, "dur": 31, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817947, "dur": 22, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817972, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370817974, "dur": 31, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818007, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818009, "dur": 25, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818036, "dur": 1, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818037, "dur": 24, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818064, "dur": 19, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818087, "dur": 23, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818117, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818119, "dur": 26, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818147, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818148, "dur": 36, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818186, "dur": 1, "ph": "X", "name": "ProcessMessages 694", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818188, "dur": 23, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818215, "dur": 27, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818245, "dur": 23, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818271, "dur": 27, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818301, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818303, "dur": 22, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818326, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818328, "dur": 22, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818354, "dur": 24, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818379, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818381, "dur": 26, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818409, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818410, "dur": 24, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818438, "dur": 25, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818466, "dur": 24, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818494, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818496, "dur": 29, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818527, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818529, "dur": 27, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818559, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818560, "dur": 30, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818592, "dur": 1, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818594, "dur": 25, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818621, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818623, "dur": 26, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818651, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818652, "dur": 23, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818679, "dur": 39, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818720, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818722, "dur": 29, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818753, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818754, "dur": 31, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818789, "dur": 24, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818815, "dur": 1, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818816, "dur": 24, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818843, "dur": 27, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818873, "dur": 1, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818874, "dur": 21, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818897, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818898, "dur": 25, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818926, "dur": 1, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818927, "dur": 24, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818954, "dur": 27, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818983, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370818985, "dur": 23, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819010, "dur": 1, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819012, "dur": 24, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819039, "dur": 21, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819063, "dur": 43, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819107, "dur": 1, "ph": "X", "name": "ProcessMessages 836", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819109, "dur": 21, "ph": "X", "name": "ReadAsync 836", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819131, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819133, "dur": 23, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819160, "dur": 23, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819185, "dur": 1, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819187, "dur": 21, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819210, "dur": 19, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819231, "dur": 23, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819256, "dur": 2, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819259, "dur": 24, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819287, "dur": 28, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819318, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819320, "dur": 27, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819349, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819351, "dur": 27, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819380, "dur": 1, "ph": "X", "name": "ProcessMessages 760", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819382, "dur": 25, "ph": "X", "name": "ReadAsync 760", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819408, "dur": 1, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819409, "dur": 25, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819438, "dur": 17, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819458, "dur": 26, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819486, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819488, "dur": 27, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819519, "dur": 21, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819542, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819543, "dur": 20, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819566, "dur": 22, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819591, "dur": 19, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819613, "dur": 19, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819635, "dur": 32, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819672, "dur": 24, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819698, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819700, "dur": 27, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819731, "dur": 21, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819754, "dur": 23, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819781, "dur": 19, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819802, "dur": 1, "ph": "X", "name": "ProcessMessages 59", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819804, "dur": 177, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370819985, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370820016, "dur": 546, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370820565, "dur": 60, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370820628, "dur": 9, "ph": "X", "name": "ProcessMessages 1152", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370820638, "dur": 35, "ph": "X", "name": "ReadAsync 1152", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370820677, "dur": 2, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370820680, "dur": 37, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370820719, "dur": 2, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370820723, "dur": 28, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370820754, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370820756, "dur": 31, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370820790, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370820794, "dur": 33, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370820830, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370820833, "dur": 38, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370820873, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370820877, "dur": 32, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370820912, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370820915, "dur": 35, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370820952, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370820955, "dur": 30, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370820989, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370820991, "dur": 19, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821012, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821013, "dur": 28, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821043, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821045, "dur": 27, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821074, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821076, "dur": 19, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821097, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821099, "dur": 22, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821123, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821125, "dur": 28, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821156, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821158, "dur": 34, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821195, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821197, "dur": 34, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821234, "dur": 2, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821237, "dur": 77, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821319, "dur": 2, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821322, "dur": 38, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821363, "dur": 3, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821367, "dur": 30, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821401, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821423, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821426, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821450, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821471, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821490, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821493, "dur": 24, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821519, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821521, "dur": 26, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821550, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821552, "dur": 24, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821578, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821580, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821615, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821617, "dur": 29, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821649, "dur": 2, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821652, "dur": 32, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821687, "dur": 2, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821690, "dur": 30, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821729, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821732, "dur": 30, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821765, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821768, "dur": 27, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821799, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370821801, "dur": 6277, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370828082, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370828085, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370828120, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370828123, "dur": 171, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370828299, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370828327, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370828354, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370828357, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370828389, "dur": 235, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370828630, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370828632, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370828673, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370828675, "dur": 27, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370828704, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370828707, "dur": 20, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370828730, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370828756, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370828758, "dur": 476, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370829304, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370829306, "dur": 149, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370829541, "dur": 54, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370829668, "dur": 268, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370829939, "dur": 7, "ph": "X", "name": "ProcessMessages 704", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370829948, "dur": 416, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370830445, "dur": 3, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370830449, "dur": 37, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370830912, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370830915, "dur": 90, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370831007, "dur": 3, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370831011, "dur": 91, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370831193, "dur": 122, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370831358, "dur": 126, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370831486, "dur": 2, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370831535, "dur": 112, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370831649, "dur": 28, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370831740, "dur": 170, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370831912, "dur": 98, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370832050, "dur": 97, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370832150, "dur": 2, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370832298, "dur": 60, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370832361, "dur": 151, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370832549, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370832554, "dur": 445, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370833069, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370833072, "dur": 290, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370833414, "dur": 46, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370833479, "dur": 26, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370833562, "dur": 28, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370833632, "dur": 60, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370833694, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370833725, "dur": 136, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370833885, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370833887, "dur": 79, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370833997, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370833999, "dur": 200, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370834287, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370834289, "dur": 84, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370834392, "dur": 24, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370834417, "dur": 83, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370838126, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370838130, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370838180, "dur": 479, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370838661, "dur": 138887, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370977554, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370977558, "dur": 125, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370977687, "dur": 7036, "ph": "X", "name": "ProcessMessages 1012", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370984730, "dur": 106, "ph": "X", "name": "ReadAsync 1012", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370984839, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370984842, "dur": 950, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370985796, "dur": 110, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370985910, "dur": 14, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370985925, "dur": 4063, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370989994, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370989997, "dur": 134, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370990134, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451370990136, "dur": 43273, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451371033417, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451371033420, "dur": 112, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451371033536, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451371033540, "dur": 659, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451371034204, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451371034206, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451371034246, "dur": 206, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451371034454, "dur": 1097, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451371035556, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451371035619, "dur": 320, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751451371035942, "dur": 22031, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 20040, "tid": 111792, "ts": 1751451371070001, "dur": 836, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 20040, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 20040, "tid": 8589934592, "ts": 1751451370793537, "dur": 106720, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 20040, "tid": 8589934592, "ts": 1751451370900259, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 20040, "tid": 8589934592, "ts": 1751451370900265, "dur": 1049, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 20040, "tid": 111792, "ts": 1751451371070839, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 20040, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 20040, "tid": 4294967296, "ts": 1751451370776368, "dur": 282585, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 20040, "tid": 4294967296, "ts": 1751451370780423, "dur": 7185, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 20040, "tid": 4294967296, "ts": 1751451371058967, "dur": 3901, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 20040, "tid": 4294967296, "ts": 1751451371061249, "dur": 208, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 20040, "tid": 4294967296, "ts": 1751451371062935, "dur": 10, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 20040, "tid": 111792, "ts": 1751451371070845, "dur": 7, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751451370801343, "dur": 1827, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751451370803179, "dur": 813, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751451370804107, "dur": 57, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751451370804165, "dur": 488, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751451370805446, "dur": 1658, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_F7240E92754989BB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751451370807964, "dur": 1565, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_0174E068A5FA251B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751451370810189, "dur": 180, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751451370810476, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751451370810624, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_16F8EA6B67022FB5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751451370810776, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751451370810895, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.SpatialTracking.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751451370811218, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751451370817499, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751451370817812, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751451370818650, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.XR.ARKit.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751451370804670, "dur": 15028, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751451370819707, "dur": 214458, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751451371034167, "dur": 941, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751451371035154, "dur": 90, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751451371035455, "dur": 14897, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751451370804733, "dur": 14981, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370819733, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751451370819724, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_E4AC68B67EFEC4DD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751451370819897, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_384C1F3E632FD32D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751451370819986, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751451370819984, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_B93FCE4572AE3AA8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751451370820087, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751451370820086, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_83F2C9E5C417FFF6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751451370820157, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370820225, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751451370820224, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_F7240E92754989BB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751451370820300, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370820354, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751451370820353, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_C7FE3A462D97940C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751451370820416, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370820765, "dur": 238, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.CoreUtils.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751451370821266, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751451370821611, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370822062, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370822379, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370822697, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370822983, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370823320, "dur": 569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370823890, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370824179, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370824461, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370824718, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370825160, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370825475, "dur": 371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370825846, "dur": 1070, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370826916, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370827177, "dur": 568, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370827745, "dur": 487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370828232, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370828306, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751451370828512, "dur": 672, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370829342, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DSPGraphModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751451370829188, "dur": 688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751451370829877, "dur": 380, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370830269, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370830367, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370830542, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370831030, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370831192, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370831271, "dur": 447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370831718, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370831812, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370831914, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370831965, "dur": 510, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370832475, "dur": 455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370832930, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370833033, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370833171, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370833348, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370833462, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370833519, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370833708, "dur": 386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370834145, "dur": 493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370834638, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370834691, "dur": 361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370835052, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370835123, "dur": 378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370835501, "dur": 148435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751451370983937, "dur": 50422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751451370804737, "dur": 15020, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751451370819768, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370819760, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_3A4455FCF9EA132A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751451370819906, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_CA136C2D17F2F281.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751451370819998, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370819996, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_8C88AE4D2CCD7158.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751451370820106, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370820104, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_0B617F8D8E18502B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751451370820215, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370820214, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_56D6DEB754397D49.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751451370820311, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370820309, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_BB89E70A60043324.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751451370820434, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751451370820572, "dur": 139, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751451370820927, "dur": 151, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751451370821080, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751451370821181, "dur": 311, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751451370821739, "dur": 188, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\mscorlib.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370821929, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370821987, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Core.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370822040, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Data.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370822111, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370822176, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Drawing.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370822229, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370822283, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370822336, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Numerics.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370822395, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370822455, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370822512, "dur": 353, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370822875, "dur": 186, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370823069, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370823135, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370823187, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370823243, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370823295, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370823350, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370823403, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370823456, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370823508, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370823560, "dur": 307, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370823868, "dur": 243, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370824126, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370824212, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370824277, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370824335, "dur": 264, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370824607, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370824703, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370824763, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370824822, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370824878, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370824934, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370825002, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370825063, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370825122, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370825211, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370825274, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370825350, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370825431, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370825498, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370825569, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370825631, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370825687, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370825746, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370825809, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370825875, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370825944, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370826010, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370826074, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370826139, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370826235, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370826296, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370826359, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370826422, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370826610, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370826774, "dur": 133, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751451370821493, "dur": 5571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751451370827190, "dur": 607, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751451370827797, "dur": 444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751451370828242, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751451370828297, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751451370829272, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage@1.2.6\\Editor\\CoverageFormats\\OpenCover\\Model\\Module.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751451370828565, "dur": 808, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751451370829374, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751451370829614, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751451370829742, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751451370829864, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751451370830516, "dur": 439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751451370831031, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751451370831255, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751451370831705, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751451370831793, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751451370831962, "dur": 508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751451370832471, "dur": 453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751451370832948, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751451370833052, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751451370833164, "dur": 200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751451370833364, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751451370833508, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751451370833632, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751451370833712, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751451370834145, "dur": 523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751451370834671, "dur": 366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751451370835098, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751451370835516, "dur": 148476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751451370983992, "dur": 50180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370804743, "dur": 15021, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370819775, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Gradle.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751451370819767, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_6BA57B641639B2BC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751451370819916, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751451370819915, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_BE03322452709CB2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751451370820017, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751451370820015, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_39EA3D21A2039B2E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751451370820124, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751451370820122, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_3977A7D73E1EE0EE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751451370820230, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TilemapModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751451370820229, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_F5D346386BF65E52.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751451370820302, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370820399, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\WebGLSupport\\UnityEditor.WebGL.Extensions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751451370820398, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_30584634827FBF1C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751451370820729, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.CoreUtils.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751451370821061, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Simulation.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751451370821261, "dur": 243, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751451370821583, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751451370821664, "dur": 379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370822044, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370822354, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370822648, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370822934, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370823200, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370823502, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370824115, "dur": 375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370824491, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370824786, "dur": 352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370825138, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370825451, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370825721, "dur": 371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370826092, "dur": 1031, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370827177, "dur": 586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370827763, "dur": 467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370828230, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370828286, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Management.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751451370828583, "dur": 657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Management.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751451370829241, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370829448, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Management.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751451370829692, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Management.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751451370830277, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370830453, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370830519, "dur": 431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370830974, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370831053, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370831196, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370831260, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370831700, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370831784, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370831950, "dur": 491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370832462, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370832934, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370833032, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370833162, "dur": 179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370833341, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370833526, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370833643, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370833713, "dur": 449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370834163, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370834675, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370835048, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370835102, "dur": 393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370835495, "dur": 148448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751451370983944, "dur": 50411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370805081, "dur": 14828, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370819922, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751451370819913, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_A0462AF7F9C7DD5D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751451370820055, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751451370820053, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_FCB7B30576667F66.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751451370820165, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ProfilerModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751451370820164, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_C0DCB9053A2446E9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751451370820275, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751451370820273, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_B4B36C72CEF54054.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751451370820332, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370820414, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370820946, "dur": 186, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1751451370821169, "dur": 292, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.XR.LegacyInputHelpers.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751451370821463, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751451370821624, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370822091, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370822420, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370822672, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370822968, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370823281, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370823580, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370824118, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370824365, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370824681, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370825106, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370825610, "dur": 1144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370826755, "dur": 394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370827149, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370827201, "dur": 549, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370827750, "dur": 474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370828246, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370828303, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751451370828593, "dur": 732, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751451370829469, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370829578, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370829668, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370829918, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370830153, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370830225, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370830516, "dur": 295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370830811, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370831063, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370831191, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370831254, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370831681, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370831803, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370831948, "dur": 508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370832456, "dur": 481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370832937, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370833058, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370833163, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370833340, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370833460, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370833513, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370833650, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370833710, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370834142, "dur": 470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370834666, "dur": 434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370835100, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370835500, "dur": 148437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751451370983938, "dur": 50262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751451370804866, "dur": 14980, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751451370819860, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Types.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370819849, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_9B13BE4380FB0740.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751451370819958, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370819957, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_78C4933F624BCAF0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751451370820023, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751451370820090, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370820089, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_0DD16C5308008CDA.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751451370820204, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubstanceModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370820202, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_1DC8AFE6C7A826A4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751451370820294, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751451370820374, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\UnityEditor.Android.Extensions.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370820372, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_6421414B18C06C7C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751451370820515, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751451370820573, "dur": 499, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_6421414B18C06C7C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751451370821079, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751451370821180, "dur": 352, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751451370821723, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\mscorlib.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370821810, "dur": 150, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370821961, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Core.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370822038, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Data.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370822107, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370822216, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Drawing.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370822276, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370822328, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370822386, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Numerics.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370822447, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370822513, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370822594, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Web.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370822655, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Windows.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370822807, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370822861, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370822928, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370822984, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Buffers.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370823036, "dur": 230, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370823280, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370823340, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370823392, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370823445, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370823544, "dur": 318, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370823871, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370823997, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370824049, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370824125, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370824206, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370824274, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370824333, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370824410, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370824467, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370824580, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370824686, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370824750, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370824806, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370824865, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370824933, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370824998, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370825067, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370825124, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370825213, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370825280, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370825351, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370825437, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370825500, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370825557, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370825614, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370825680, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370825758, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370825818, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370825905, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370825964, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370826030, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370826106, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370826209, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370826272, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370826347, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370826406, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370826468, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370826542, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370826617, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370826783, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370821533, "dur": 5578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751451370827209, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751451370827276, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751451370827783, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751451370827853, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751451370828123, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751451370828284, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751451370828509, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751451370829194, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\iOSSupport\\UnityEditor.iOS.Extensions.Common.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370829692, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Editor\\ControlPicker\\InputControlPickerDropdown.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751451370828586, "dur": 1510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751451370830193, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751451370830424, "dur": 353, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751451370830904, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751451370830780, "dur": 947, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751451370831825, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751451370832808, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Vector4\\Vector4DotProduct.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751451370831916, "dur": 1164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751451370833179, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751451370833268, "dur": 310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751451370833710, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751451370834150, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751451370834643, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751451370834699, "dur": 361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751451370835098, "dur": 396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751451370835494, "dur": 148434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751451370983929, "dur": 5896, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751451370989854, "dur": 44378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370804840, "dur": 14967, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370819820, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.GradleProject.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751451370819811, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.GradleProject.dll_29F2B64BD1A1DCB9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751451370819923, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751451370819922, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_2C6FB2D62EBEEE94.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751451370820030, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751451370820028, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_096F3F085FA84B73.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751451370820132, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751451370820131, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_56A29088F8660FBC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751451370820226, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751451370820225, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_99C9B018C409BC13.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751451370820324, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751451370820322, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_39AD4EBB1D1424EB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751451370820423, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370820523, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370820606, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.NVIDIAModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751451370820605, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_50C4BCFC618A644B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751451370820770, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.InternalUtils.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751451370820901, "dur": 245, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751451370821185, "dur": 353, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751451370821554, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751451370821634, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370822177, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370822497, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370822779, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370823118, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370823441, "dur": 625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370824066, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370824355, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370824629, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370825102, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370825466, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370825641, "dur": 1112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370826753, "dur": 438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370827191, "dur": 605, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370827796, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370828294, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751451370828591, "dur": 581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751451370829173, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370829474, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370829543, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370829838, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370829917, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370830223, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370830520, "dur": 442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370831037, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370831202, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370831261, "dur": 419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370831710, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370831791, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370831963, "dur": 482, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370832446, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370832932, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370833032, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370833168, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370833363, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370833480, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370833534, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370833650, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370833729, "dur": 435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370834164, "dur": 518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370834682, "dur": 369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370835052, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370835107, "dur": 390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370835497, "dur": 148489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751451370983987, "dur": 50342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370804922, "dur": 14931, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370819863, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751451370819856, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_24157DE88EB6BDBE.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751451370819973, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751451370819972, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_A9EADF60550F98A5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751451370820081, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GameCenterModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751451370820080, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_C1CB3B5C02834557.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751451370820187, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751451370820186, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_AAF7ADEC23F2DAD1.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751451370820287, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751451370820286, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_BF543B2CA89F5C1C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751451370820394, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\iOSSupport\\UnityEditor.iOS.Extensions.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751451370820393, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_2A03BB7F03493A3C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751451370821131, "dur": 183, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751451370821495, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Simulation.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751451370821617, "dur": 403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370822021, "dur": 388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370822409, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370822670, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370822978, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370823235, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370823535, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370824095, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370824416, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370824669, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370825036, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370825304, "dur": 404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370825708, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370826044, "dur": 1101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370827145, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370827195, "dur": 553, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370827749, "dur": 484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370828233, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370828310, "dur": 949, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370829259, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370829461, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370829581, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370829703, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370829920, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370830268, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370830445, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370830518, "dur": 434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370830964, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370831033, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370831195, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370831256, "dur": 453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370831709, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370831787, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370831947, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370832491, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751451370832784, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DirectorModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751451370832711, "dur": 489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751451370833201, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370833512, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370833639, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370833726, "dur": 372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370834140, "dur": 475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370834688, "dur": 356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370835091, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751451370835203, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370835486, "dur": 148437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751451370983934, "dur": 49264, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751451370983933, "dur": 49267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751451371033218, "dur": 893, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751451370804980, "dur": 14914, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370819906, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751451370819898, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_8360DAA503BDFED4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751451370820012, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751451370820011, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_D301E026C7E6BC4A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751451370820080, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370820152, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751451370820151, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_D92E9E3DCDCA68ED.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751451370820217, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370820339, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751451370820338, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_F80B64918A78D7EF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751451370820408, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370820591, "dur": 201, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751451370820875, "dur": 223, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.CoreUtils.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751451370821132, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751451370821273, "dur": 253, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751451370821583, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370822075, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370822378, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370822642, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370822959, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370823259, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370823932, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370824252, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370824536, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370824806, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370825388, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370825666, "dur": 1118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370826784, "dur": 360, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370827145, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370827196, "dur": 598, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370827795, "dur": 514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370828310, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751451370829251, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@3.0.31\\Rider\\Editor\\ProjectGeneration\\IGenerator.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751451370828578, "dur": 864, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751451370829443, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370829576, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370829687, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370829915, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370830282, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370830538, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370831035, "dur": 158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370831194, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370831252, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARCore.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751451370831395, "dur": 490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARCore.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751451370831980, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARCore.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751451370832125, "dur": 581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARCore.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751451370832707, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370832958, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370833064, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370833167, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370833332, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370833510, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370833640, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370833722, "dur": 439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370834162, "dur": 524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370834686, "dur": 363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370835049, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370835105, "dur": 391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370835496, "dur": 148440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751451370983936, "dur": 50432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370805007, "dur": 14895, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370819915, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751451370819905, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_A16031C8D5E7F23B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751451370820040, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CoreModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751451370820039, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_A681ADF7ABC369FF.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751451370820191, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751451370820190, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_89BA132246C7DB90.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751451370820293, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751451370820291, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_BB1D083DE47F9081.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751451370820354, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370820429, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370820512, "dur": 246, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1751451370820897, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1751451370821139, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.FaceTracking.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1751451370821265, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751451370821581, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370822041, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370822332, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370822637, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370822925, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370823220, "dur": 344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370823565, "dur": 505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370824070, "dur": 361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370824431, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370824711, "dur": 502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370825213, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370825518, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370825946, "dur": 1193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370827182, "dur": 562, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370827772, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370828239, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370828303, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751451370828564, "dur": 786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751451370829458, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370829582, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370829661, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370829954, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370830242, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370830361, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370830466, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370830521, "dur": 428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370830971, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370831047, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370831207, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370831262, "dur": 455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370831717, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370831799, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370831947, "dur": 509, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370832456, "dur": 473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370832929, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370833027, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370833146, "dur": 182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370833356, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370833466, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370833534, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370833765, "dur": 345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370834152, "dur": 532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370834684, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370835094, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370835504, "dur": 148438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751451370983943, "dur": 50413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751451370805143, "dur": 14775, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751451370819930, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751451370819922, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_79696B0654C8F4B7.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751451370820051, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751451370820050, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_486B4E27CA46B2F7.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751451370820171, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751451370820170, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_708A3E0E0CD23C1E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751451370820295, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751451370820294, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_C3A7C19B07D61835.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751451370820363, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751451370820431, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751451370820571, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1751451370820814, "dur": 623, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1751451370821580, "dur": 383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751451370821964, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751451370822268, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751451370822556, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751451370822841, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751451370823160, "dur": 384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751451370823545, "dur": 623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751451370824168, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751451370824467, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751451370824759, "dur": 369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751451370825128, "dur": 344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751451370825473, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751451370825781, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751451370826117, "dur": 1008, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751451370827146, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751451370827199, "dur": 558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751451370827758, "dur": 478, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751451370828236, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751451370828295, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751451370828568, "dur": 662, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751451370829234, "dur": 571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751451370829806, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751451370829916, "dur": 545, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751451370830465, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751451370830589, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751451370831045, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751451370831193, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751451370831251, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751451370831349, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751451370831721, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751451370831948, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751451370832469, "dur": 464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751451370832933, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751451370833036, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751451370833184, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.VisualScripting.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751451370833284, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.VisualScripting.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751451370833717, "dur": 386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751451370834138, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.VisualScripting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751451370834273, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.VisualScripting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751451370834667, "dur": 371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751451370835097, "dur": 390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751451370835488, "dur": 148441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751451370983930, "dur": 50423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370805178, "dur": 14747, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370819937, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751451370819929, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_C3E59BB5F8B48795.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751451370820053, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DirectorModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751451370820051, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_8E967FCEA0CABFDC.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751451370820171, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751451370820169, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_8C9666DB874133B6.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751451370820304, "dur": 208, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751451370820302, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_A06F1EA8D8FA1A33.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751451370820769, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.InternalUtils.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1751451370820894, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARAnalytics.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1751451370821130, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1751451370821261, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751451370821616, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370822097, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370822368, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370822651, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370822966, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370823326, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370823897, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370824231, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370824513, "dur": 389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370824903, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370825336, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370825625, "dur": 1265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370826890, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370827144, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370827200, "dur": 595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370827796, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370828228, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370828288, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751451370828560, "dur": 752, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751451370829312, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370829477, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751451370829636, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370829930, "dur": 540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751451370830518, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370830686, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370830964, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370831033, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370831211, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370831264, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370831696, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370831799, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370831967, "dur": 494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370832461, "dur": 474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370832935, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370833038, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370833151, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370833338, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370833529, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370833708, "dur": 402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370834144, "dur": 494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370834683, "dur": 358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370835041, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370835092, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751451370835211, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370835489, "dur": 148443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751451370983933, "dur": 50363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370805200, "dur": 14735, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370819950, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751451370819938, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_FCB3152C479369C0.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751451370820077, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DSPGraphModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751451370820076, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_93E7E138132B90FD.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751451370820180, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751451370820179, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_2DFC1E35CC3D93F5.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751451370820348, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751451370820347, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_1CD089203CF9E868.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751451370820412, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370820519, "dur": 172, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_1CD089203CF9E868.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751451370820723, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARSubsystems.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751451370820855, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751451370820945, "dur": 180, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1751451370821175, "dur": 372, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1751451370821583, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751451370821674, "dur": 409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370822083, "dur": 352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370822435, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370822692, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370822954, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370823269, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370823548, "dur": 507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370824055, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370824385, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370824671, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370824972, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370825470, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370825783, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370826191, "dur": 935, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370827175, "dur": 571, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370827746, "dur": 496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370828242, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370828308, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751451370828651, "dur": 672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751451370829323, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370829492, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370829568, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370829658, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370829915, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370830197, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751451370830371, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370830448, "dur": 448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751451370831032, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370831195, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370831258, "dur": 424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370831687, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370831807, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370831869, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370831951, "dur": 489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370832470, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370832932, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370833056, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370833154, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370833350, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370833512, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370833707, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370834141, "dur": 473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370834683, "dur": 412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370835096, "dur": 385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370835502, "dur": 148486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751451370983990, "dur": 50288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751451370805317, "dur": 14650, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751451370819981, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1751451370819971, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_1935086DF322E590.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751451370820096, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\iOSSupport\\UnityEditor.iOS.Extensions.Common.dll"}}, {"pid": 12345, "tid": 13, "ts": 1751451370820095, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_37743AF6A5E3F6D9.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751451370820162, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751451370820218, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainPhysicsModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1751451370820217, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_9BF84047519B3912.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751451370820336, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1751451370820335, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_4A837DB03C61DFDC.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751451370820398, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751451370820513, "dur": 155, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_4A837DB03C61DFDC.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751451370820770, "dur": 338, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Management.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751451370821235, "dur": 245, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1751451370821608, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751451370822030, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751451370822296, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751451370822611, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751451370822859, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751451370823184, "dur": 392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751451370823576, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751451370824120, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751451370824400, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751451370824725, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751451370825132, "dur": 400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751451370825532, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751451370826219, "dur": 910, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751451370827192, "dur": 567, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751451370827760, "dur": 535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751451370828301, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751451370828621, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1751451370828549, "dur": 886, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751451370829435, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751451370829543, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751451370829601, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751451370829760, "dur": 905, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751451370830666, "dur": 353, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751451370831061, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751451370831199, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751451370831262, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARSubsystems.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751451370831411, "dur": 366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARSubsystems.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751451370831846, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751451370831975, "dur": 1471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751451370833548, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751451370833635, "dur": 409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751451370834143, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751451370834276, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751451370834702, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751451370834775, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751451370835096, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751451370835208, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751451370835481, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751451370836358, "dur": 141069, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751451370984219, "dur": 5479, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 13, "ts": 1751451370983923, "dur": 5842, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751451370989838, "dur": 44329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751451370805229, "dur": 14717, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751451370819964, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1751451370819957, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_30DC5CCDD2C811D2.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751451370820084, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1751451370820083, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_517C17530E5D22C2.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751451370820196, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1751451370820195, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_411D9B9D7C552896.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751451370820309, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1751451370820307, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_9C11D1C715218856.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751451370820404, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751451370820507, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_9C11D1C715218856.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751451370820601, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1751451370820599, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_55E4B2A1BED5EA50.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751451370820710, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_55E4B2A1BED5EA50.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751451370820926, "dur": 243, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Simulation.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1751451370821234, "dur": 238, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARCore.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1751451370821473, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751451370821565, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751451370821642, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751451370822100, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751451370822443, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751451370822725, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751451370822995, "dur": 348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751451370823343, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751451370823955, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751451370824254, "dur": 526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751451370824781, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751451370825060, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751451370825513, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751451370825879, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751451370826311, "dur": 825, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751451370827183, "dur": 566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751451370827749, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751451370828252, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751451370828330, "dur": 925, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751451370829255, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751451370829471, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751451370829588, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751451370829659, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751451370829914, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751451370830197, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARSubsystems.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751451370830904, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 14, "ts": 1751451370830430, "dur": 702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARSubsystems.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751451370831249, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.InternalUtils.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751451370831390, "dur": 448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.InternalUtils.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751451370831961, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751451370832055, "dur": 336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751451370832488, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751451370832784, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1751451370832738, "dur": 526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751451370833366, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751451370833464, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751451370833515, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751451370833706, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751451370834140, "dur": 475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751451370834687, "dur": 367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751451370835099, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751451370835492, "dur": 148441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751451370983933, "dur": 50231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751451370805259, "dur": 14698, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751451370819972, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1751451370819961, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_128BDF40C52915E6.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751451370820083, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751451370820168, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1751451370820167, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_EF067A9A756EA993.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751451370820298, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1751451370820296, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_BFA54F7E9CE16345.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751451370820370, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751451370820603, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_3DF8E1AFD2EAE0B1.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751451370820711, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751451370820813, "dur": 348, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1751451370821256, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751451370821513, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.SpatialTracking.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751451370821609, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751451370822096, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751451370822611, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751451370822909, "dur": 379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751451370823289, "dur": 622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751451370823911, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751451370824183, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751451370824475, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751451370824799, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751451370825105, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751451370825437, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751451370825707, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751451370826177, "dur": 950, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751451370827176, "dur": 577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751451370827753, "dur": 484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751451370828237, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751451370828300, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751451370829274, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 15, "ts": 1751451370828635, "dur": 1452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751451370830087, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751451370830243, "dur": 377, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751451370830624, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751451370830777, "dur": 369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751451370831254, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751451370831714, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751451370831788, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751451370831959, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Simulation.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751451370832583, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.xr.arfoundation@5.1.6\\Runtime\\Simulation\\Environment\\XREnvironmentViewCamera.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751451370832718, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.xr.arfoundation@5.1.6\\Runtime\\Simulation\\Subsystems\\ImageTracking\\TrackedImageDiscoveryParams.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751451370832856, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.xr.arfoundation@5.1.6\\Runtime\\Simulation\\Subsystems\\ImageTracking\\TrackedImageDiscoveryStrategy.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751451370832096, "dur": 875, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Simulation.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751451370833029, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751451370833158, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Simulation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751451370833308, "dur": 354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Simulation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751451370833734, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751451370834144, "dur": 467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751451370834667, "dur": 426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751451370835093, "dur": 391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751451370835484, "dur": 148447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751451370983931, "dur": 50232, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751451370805346, "dur": 14629, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751451370819988, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751451370819978, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_D6483D8C0D4E178A.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751451370820142, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751451370820140, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_94317CF45651DD49.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751451370820233, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751451370820232, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_C9A4E54165F4766F.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751451370820332, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751451370820331, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_C1E653EE65094627.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751451370820395, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751451370820614, "dur": 194, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751451370820876, "dur": 567, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751451370821518, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1751451370821656, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751451370822223, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751451370822635, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751451370822930, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751451370823188, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751451370823574, "dur": 590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751451370824165, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751451370824479, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751451370824772, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751451370825263, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751451370825601, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751451370826731, "dur": 448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751451370827179, "dur": 563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751451370827763, "dur": 470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751451370828233, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751451370828285, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.SpatialTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751451370828492, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751451370828606, "dur": 610, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.SpatialTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751451370829217, "dur": 420, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751451370829711, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751451370829882, "dur": 466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751451370830473, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751451370830610, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751451370831032, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751451370831190, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751451370831253, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.FaceTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751451370831393, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.FaceTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751451370831954, "dur": 517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751451370832471, "dur": 445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751451370832969, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751451370833037, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751451370833150, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751451370833330, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751451370833507, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751451370833713, "dur": 394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751451370834141, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751451370834216, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751451370834282, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751451370834674, "dur": 383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751451370835102, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751451370835516, "dur": 148469, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751451370983986, "dur": 50372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370805383, "dur": 14599, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370819995, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1751451370819986, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_F68B074CF92AAF32.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751451370820112, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\iOSSupport\\UnityEditor.iOS.Extensions.Xcode.dll"}}, {"pid": 12345, "tid": 17, "ts": 1751451370820111, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_32B1888F61CF7983.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751451370820208, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1751451370820207, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_15A5144DF23AC2E4.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751451370820306, "dur": 184, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1751451370820304, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_01045232C2BAAC12.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751451370820613, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_CBE4C2A7CAF71BDD.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751451370820693, "dur": 473, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_CBE4C2A7CAF71BDD.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751451370821292, "dur": 276, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751451370821585, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1751451370821678, "dur": 391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370822069, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370822402, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370822650, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370822921, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370823208, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370823565, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370824115, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370824403, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370824670, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370825005, "dur": 468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370825474, "dur": 361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370825835, "dur": 514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370826349, "dur": 783, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370827181, "dur": 574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370827755, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370828231, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370828293, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARAnalytics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751451370828535, "dur": 517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARAnalytics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1751451370829053, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370829273, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370829461, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370829564, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370829657, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370829916, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370830321, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370830444, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370830519, "dur": 434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370830964, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370831059, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370831212, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370831265, "dur": 446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370831711, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370831789, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370831961, "dur": 499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370832461, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370832927, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370833029, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370833148, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370833336, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370833517, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370833641, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370833714, "dur": 449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370834164, "dur": 454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370834673, "dur": 367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370835040, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370835095, "dur": 388, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370835483, "dur": 65753, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370901238, "dur": 1941, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370903180, "dur": 80755, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751451370983935, "dur": 50226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370805411, "dur": 14579, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370820003, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1751451370819993, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_977DF1413C425466.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751451370820117, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1751451370820116, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_019E00E629175977.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751451370820222, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1751451370820221, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_AF3BD820E5C5D09D.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751451370820344, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1751451370820342, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_078592F870AFC5CC.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751451370820405, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370820513, "dur": 167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_078592F870AFC5CC.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751451370820800, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.InternalUtils.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751451370820890, "dur": 195, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARSubsystems.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1751451370821236, "dur": 363, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.Editor.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1751451370821600, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370822133, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370822430, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370822718, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370823007, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370823278, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370823922, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370824344, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370824707, "dur": 380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370825088, "dur": 348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370825436, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370825684, "dur": 507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370826191, "dur": 950, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370827141, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370827200, "dur": 562, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370827762, "dur": 573, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370828335, "dur": 830, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370829215, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370829483, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370829567, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370829660, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370829956, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370830198, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751451370830400, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1751451370830807, "dur": 158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370831034, "dur": 169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370831270, "dur": 422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370831692, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370831808, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370831959, "dur": 502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370832462, "dur": 464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370832926, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370833028, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370833147, "dur": 184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370833331, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370833458, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370833509, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370833634, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370833711, "dur": 449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370834161, "dur": 518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370834679, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370835061, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370835123, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370835503, "dur": 148481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751451370983984, "dur": 50380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370805479, "dur": 14522, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370820009, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1751451370820002, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7058E5C79125A386.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751451370820102, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370820164, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PhysicsModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1751451370820162, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_3C9063487E7B345B.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751451370820277, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1751451370820276, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_4DD68C3F550D67BD.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751451370820370, "dur": 153, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 19, "ts": 1751451370820369, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_1182C7970F80B506.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751451370820698, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751451370820769, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751451370821130, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.VisualScripting.Editor.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1751451370821255, "dur": 196, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.VisualScripting.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1751451370821516, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751451370821650, "dur": 401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370822051, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370822309, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370822602, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370822873, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370823218, "dur": 339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370823558, "dur": 522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370824080, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370824398, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370824673, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370824959, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370825476, "dur": 372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370825849, "dur": 875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370826724, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370827213, "dur": 534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370827747, "dur": 480, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370828228, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370828289, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751451370828814, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1751451370828585, "dur": 739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1751451370829325, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370829390, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370829481, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370829604, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370829662, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370829925, "dur": 229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370830197, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.CoreUtils.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751451370830408, "dur": 577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.CoreUtils.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1751451370831066, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.CoreUtils.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751451370831154, "dur": 449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.CoreUtils.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1751451370831603, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370831716, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370831786, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370831912, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370831966, "dur": 493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370832459, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370832925, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370833050, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370833149, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370833349, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370833510, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370833653, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370833706, "dur": 390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370834141, "dur": 475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370834681, "dur": 366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370835096, "dur": 387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370835483, "dur": 67700, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370903183, "dur": 80743, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751451370983928, "dur": 332, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 19, "ts": 1751451370983927, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 19, "ts": 1751451370984288, "dur": 1312, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 19, "ts": 1751451370985603, "dur": 48566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370805432, "dur": 14565, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370820010, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751451370819999, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_72B29348CFCB9971.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751451370820085, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370820145, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751451370820143, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_9B7D977438583E85.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751451370820256, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751451370820255, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_19E375B5F566FEDB.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751451370820334, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751451370820333, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_0659794E1AF48F77.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751451370820398, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370820508, "dur": 242, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_0659794E1AF48F77.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751451370820815, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1751451370820897, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARSubsystems.Editor.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1751451370821148, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.FaceTracking.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1751451370821277, "dur": 283, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751451370821602, "dur": 384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370821986, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370822287, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370822579, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370822838, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370823194, "dur": 370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370823564, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370824137, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370824454, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370824711, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370825053, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370825418, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370825677, "dur": 1162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370826839, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370827175, "dur": 572, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370827748, "dur": 478, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370828227, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370828305, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751451370828609, "dur": 660, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751451370829270, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370829458, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370829568, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370829656, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370829719, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.SpatialTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751451370829856, "dur": 499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.SpatialTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751451370830356, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370830522, "dur": 451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370830973, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370831030, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370831208, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370831263, "dur": 438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370831701, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370831796, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370831953, "dur": 500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370832453, "dur": 478, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370832931, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370833030, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370833151, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370833334, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370833457, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370833508, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370833712, "dur": 453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370834165, "dur": 452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370834677, "dur": 372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370835097, "dur": 385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370835513, "dur": 148474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751451370983988, "dur": 50267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751451371053395, "dur": 4055, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 20040, "tid": 111792, "ts": 1751451371071422, "dur": 39713, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 20040, "tid": 111792, "ts": 1751451371111174, "dur": 2221, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 20040, "tid": 111792, "ts": 1751451371067761, "dur": 46443, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}