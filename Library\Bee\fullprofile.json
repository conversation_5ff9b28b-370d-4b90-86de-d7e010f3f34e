{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 20040, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 20040, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 20040, "tid": 229864, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 20040, "tid": 229864, "ts": 1751452658406792, "dur": 563, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 20040, "tid": 229864, "ts": 1751452658410937, "dur": 877, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 20040, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 20040, "tid": 1, "ts": 1751452658001345, "dur": 5044, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 20040, "tid": 1, "ts": 1751452658006392, "dur": 60227, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 20040, "tid": 1, "ts": 1751452658066629, "dur": 50830, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 20040, "tid": 229864, "ts": 1751452658411818, "dur": 10, "ph": "X", "name": "", "args": {}}, {"pid": 20040, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452657999406, "dur": 8370, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658007778, "dur": 392015, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658008933, "dur": 2688, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658011626, "dur": 1281, "ph": "X", "name": "ProcessMessages 20512", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658012910, "dur": 295, "ph": "X", "name": "ReadAsync 20512", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658013209, "dur": 13, "ph": "X", "name": "ProcessMessages 20481", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658013223, "dur": 41, "ph": "X", "name": "ReadAsync 20481", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658013267, "dur": 2, "ph": "X", "name": "ProcessMessages 1528", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658013269, "dur": 58, "ph": "X", "name": "ReadAsync 1528", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658013331, "dur": 1, "ph": "X", "name": "ProcessMessages 250", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658013333, "dur": 28, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658013363, "dur": 1, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658013365, "dur": 68, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658013436, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658013438, "dur": 27, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658013467, "dur": 1, "ph": "X", "name": "ProcessMessages 856", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658013468, "dur": 54, "ph": "X", "name": "ReadAsync 856", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658013526, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658013528, "dur": 37, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658013566, "dur": 1, "ph": "X", "name": "ProcessMessages 1210", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658013568, "dur": 22, "ph": "X", "name": "ReadAsync 1210", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658013593, "dur": 20, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658013616, "dur": 333, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658013952, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658013954, "dur": 109, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014065, "dur": 5, "ph": "X", "name": "ProcessMessages 7774", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014072, "dur": 27, "ph": "X", "name": "ReadAsync 7774", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014102, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014104, "dur": 26, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014134, "dur": 24, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014161, "dur": 1, "ph": "X", "name": "ProcessMessages 778", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014163, "dur": 25, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014190, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014192, "dur": 67, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014262, "dur": 1, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014264, "dur": 71, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014336, "dur": 1, "ph": "X", "name": "ProcessMessages 1063", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014338, "dur": 37, "ph": "X", "name": "ReadAsync 1063", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014377, "dur": 1, "ph": "X", "name": "ProcessMessages 1369", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014380, "dur": 28, "ph": "X", "name": "ReadAsync 1369", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014411, "dur": 1, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014413, "dur": 23, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014439, "dur": 45, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014486, "dur": 1, "ph": "X", "name": "ProcessMessages 1000", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014488, "dur": 22, "ph": "X", "name": "ReadAsync 1000", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014514, "dur": 24, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014540, "dur": 19, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014563, "dur": 34, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014599, "dur": 1, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014601, "dur": 26, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014630, "dur": 62, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014695, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014697, "dur": 37, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014736, "dur": 1, "ph": "X", "name": "ProcessMessages 817", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014739, "dur": 30, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014771, "dur": 1, "ph": "X", "name": "ProcessMessages 809", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014773, "dur": 25, "ph": "X", "name": "ReadAsync 809", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014803, "dur": 20, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014825, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014826, "dur": 17, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014845, "dur": 23, "ph": "X", "name": "ReadAsync 125", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014872, "dur": 22, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014896, "dur": 23, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014921, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014923, "dur": 21, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014948, "dur": 22, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658014973, "dur": 27, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015003, "dur": 17, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015024, "dur": 23, "ph": "X", "name": "ReadAsync 115", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015048, "dur": 1, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015050, "dur": 22, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015076, "dur": 25, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015103, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015104, "dur": 18, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015125, "dur": 19, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015147, "dur": 25, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015174, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015175, "dur": 30, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015207, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015208, "dur": 21, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015232, "dur": 17, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015253, "dur": 18, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015274, "dur": 20, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015296, "dur": 32, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015330, "dur": 1, "ph": "X", "name": "ProcessMessages 795", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015333, "dur": 59, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015396, "dur": 23, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015422, "dur": 25, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015450, "dur": 1, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015451, "dur": 19, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015473, "dur": 17, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015493, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015519, "dur": 21, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015543, "dur": 24, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015570, "dur": 27, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015601, "dur": 19, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015622, "dur": 39, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015664, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015686, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015688, "dur": 135, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015826, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015852, "dur": 19, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015874, "dur": 22, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015899, "dur": 21, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015924, "dur": 23, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015948, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015950, "dur": 20, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015973, "dur": 18, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658015993, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016017, "dur": 19, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016039, "dur": 23, "ph": "X", "name": "ReadAsync 152", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016065, "dur": 25, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016093, "dur": 27, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016123, "dur": 20, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016147, "dur": 17, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016166, "dur": 24, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016192, "dur": 1, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016193, "dur": 22, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016218, "dur": 25, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016245, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016246, "dur": 23, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016273, "dur": 18, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016294, "dur": 21, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016318, "dur": 26, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016346, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016347, "dur": 24, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016375, "dur": 25, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016403, "dur": 19, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016424, "dur": 18, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016445, "dur": 23, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016471, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016473, "dur": 23, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016499, "dur": 22, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016523, "dur": 22, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016550, "dur": 17, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016570, "dur": 22, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016595, "dur": 21, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016619, "dur": 20, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016642, "dur": 20, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016666, "dur": 25, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016694, "dur": 17, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016714, "dur": 19, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016736, "dur": 22, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016761, "dur": 32, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016794, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016796, "dur": 22, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016821, "dur": 19, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016843, "dur": 20, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016866, "dur": 22, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016891, "dur": 24, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016917, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016920, "dur": 21, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016943, "dur": 18, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016963, "dur": 16, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658016983, "dur": 20, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017006, "dur": 24, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017033, "dur": 21, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017056, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017057, "dur": 31, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017090, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017091, "dur": 22, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017117, "dur": 19, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017139, "dur": 24, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017166, "dur": 22, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017191, "dur": 21, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017213, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017215, "dur": 19, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017237, "dur": 30, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017269, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017270, "dur": 15, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017288, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017316, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017318, "dur": 23, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017344, "dur": 21, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017366, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017368, "dur": 22, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017393, "dur": 17, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017413, "dur": 25, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017442, "dur": 22, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017467, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017468, "dur": 24, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017495, "dur": 19, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017516, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017517, "dur": 18, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017538, "dur": 18, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017559, "dur": 24, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017587, "dur": 22, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017612, "dur": 24, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017639, "dur": 24, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017666, "dur": 17, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017685, "dur": 24, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017711, "dur": 2, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017714, "dur": 24, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017741, "dur": 22, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017765, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017766, "dur": 19, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017788, "dur": 21, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017812, "dur": 17, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017833, "dur": 19, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017855, "dur": 22, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017881, "dur": 20, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017902, "dur": 1, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017903, "dur": 20, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017927, "dur": 26, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017955, "dur": 1, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017957, "dur": 17, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658017977, "dur": 21, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018001, "dur": 23, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018028, "dur": 20, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018050, "dur": 18, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018071, "dur": 24, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018099, "dur": 19, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018121, "dur": 21, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018144, "dur": 5, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018150, "dur": 22, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018174, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018176, "dur": 21, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018199, "dur": 22, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018224, "dur": 23, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018251, "dur": 16, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018270, "dur": 21, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018294, "dur": 24, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018320, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018321, "dur": 20, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018344, "dur": 23, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018370, "dur": 20, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018391, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018393, "dur": 48, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018443, "dur": 1, "ph": "X", "name": "ProcessMessages 752", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018445, "dur": 22, "ph": "X", "name": "ReadAsync 752", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018469, "dur": 2, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018472, "dur": 25, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018500, "dur": 19, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018522, "dur": 29, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018554, "dur": 23, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018579, "dur": 23, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018605, "dur": 17, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018625, "dur": 22, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018650, "dur": 19, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018672, "dur": 19, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018694, "dur": 26, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018721, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018723, "dur": 20, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018746, "dur": 22, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018771, "dur": 19, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018794, "dur": 19, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018815, "dur": 20, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018838, "dur": 20, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018859, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018860, "dur": 18, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018881, "dur": 20, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018904, "dur": 21, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018929, "dur": 18, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018950, "dur": 24, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658018977, "dur": 33, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019014, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019017, "dur": 36, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019056, "dur": 1, "ph": "X", "name": "ProcessMessages 692", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019058, "dur": 35, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019095, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019097, "dur": 22, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019122, "dur": 1, "ph": "X", "name": "ProcessMessages 77", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019124, "dur": 29, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019155, "dur": 1, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019156, "dur": 62, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019221, "dur": 1, "ph": "X", "name": "ProcessMessages 682", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019222, "dur": 33, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019258, "dur": 1, "ph": "X", "name": "ProcessMessages 1246", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019261, "dur": 27, "ph": "X", "name": "ReadAsync 1246", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019292, "dur": 22, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019316, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019335, "dur": 28, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019365, "dur": 1, "ph": "X", "name": "ProcessMessages 1062", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019367, "dur": 16, "ph": "X", "name": "ReadAsync 1062", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019386, "dur": 26, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019416, "dur": 21, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019439, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019441, "dur": 28, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019473, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019475, "dur": 25, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019502, "dur": 27, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019533, "dur": 34, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019568, "dur": 1, "ph": "X", "name": "ProcessMessages 690", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019570, "dur": 23, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019597, "dur": 22, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019620, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019622, "dur": 60, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019687, "dur": 32, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019721, "dur": 1, "ph": "X", "name": "ProcessMessages 1084", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019723, "dur": 28, "ph": "X", "name": "ReadAsync 1084", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019752, "dur": 1, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019754, "dur": 19, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019777, "dur": 22, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019801, "dur": 23, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019827, "dur": 40, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019869, "dur": 1, "ph": "X", "name": "ProcessMessages 744", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019871, "dur": 23, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019896, "dur": 1, "ph": "X", "name": "ProcessMessages 720", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019897, "dur": 21, "ph": "X", "name": "ReadAsync 720", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019921, "dur": 18, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019942, "dur": 22, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019966, "dur": 21, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019989, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658019990, "dur": 22, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020014, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020015, "dur": 15, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020033, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020058, "dur": 24, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020084, "dur": 1, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020086, "dur": 24, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020113, "dur": 24, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020140, "dur": 17, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020160, "dur": 22, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020184, "dur": 18, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020205, "dur": 28, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020236, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020265, "dur": 21, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020288, "dur": 22, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020314, "dur": 16, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020333, "dur": 28, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020364, "dur": 1, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020366, "dur": 31, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020398, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020400, "dur": 22, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020425, "dur": 18, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020446, "dur": 19, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020468, "dur": 92, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020562, "dur": 1, "ph": "X", "name": "ProcessMessages 1943", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020564, "dur": 23, "ph": "X", "name": "ReadAsync 1943", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020590, "dur": 22, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020615, "dur": 23, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020640, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020643, "dur": 23, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020670, "dur": 119, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020792, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020793, "dur": 48, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020844, "dur": 2, "ph": "X", "name": "ProcessMessages 2354", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020847, "dur": 32, "ph": "X", "name": "ReadAsync 2354", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020881, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020883, "dur": 26, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020911, "dur": 1, "ph": "X", "name": "ProcessMessages 743", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020912, "dur": 21, "ph": "X", "name": "ReadAsync 743", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020937, "dur": 22, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020962, "dur": 20, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658020985, "dur": 22, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021010, "dur": 22, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021034, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021036, "dur": 19, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021058, "dur": 23, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021084, "dur": 22, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021107, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021109, "dur": 22, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021134, "dur": 23, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021160, "dur": 21, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021184, "dur": 23, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021210, "dur": 20, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021233, "dur": 21, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021257, "dur": 21, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021281, "dur": 21, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021303, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021304, "dur": 23, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021330, "dur": 18, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021351, "dur": 19, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021373, "dur": 23, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021399, "dur": 23, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021423, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021425, "dur": 24, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021451, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021452, "dur": 22, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021477, "dur": 19, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021500, "dur": 19, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021522, "dur": 24, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021549, "dur": 38, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021589, "dur": 1, "ph": "X", "name": "ProcessMessages 770", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021591, "dur": 25, "ph": "X", "name": "ReadAsync 770", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021618, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021620, "dur": 22, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021644, "dur": 21, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021669, "dur": 20, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021692, "dur": 21, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021716, "dur": 23, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021742, "dur": 22, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021766, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021767, "dur": 21, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021791, "dur": 22, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021821, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021822, "dur": 24, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021849, "dur": 23, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021876, "dur": 40, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021917, "dur": 1, "ph": "X", "name": "ProcessMessages 773", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021919, "dur": 22, "ph": "X", "name": "ReadAsync 773", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021944, "dur": 19, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658021967, "dur": 31, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022001, "dur": 1, "ph": "X", "name": "ProcessMessages 619", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022003, "dur": 27, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022033, "dur": 24, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022059, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022061, "dur": 23, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022086, "dur": 20, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022108, "dur": 21, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022131, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022132, "dur": 22, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022158, "dur": 25, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022185, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022187, "dur": 24, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022213, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022214, "dur": 23, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022240, "dur": 22, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022265, "dur": 19, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022287, "dur": 21, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022311, "dur": 22, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022336, "dur": 21, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022360, "dur": 26, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022390, "dur": 24, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022416, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022418, "dur": 23, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022444, "dur": 22, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022468, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022469, "dur": 25, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022497, "dur": 24, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022524, "dur": 23, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022549, "dur": 1, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022551, "dur": 20, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022573, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022574, "dur": 21, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022598, "dur": 22, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022622, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022624, "dur": 25, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022652, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022653, "dur": 26, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022681, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022682, "dur": 25, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022710, "dur": 21, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022734, "dur": 21, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022758, "dur": 24, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022785, "dur": 22, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022809, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022811, "dur": 19, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022833, "dur": 20, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022856, "dur": 24, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022882, "dur": 21, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022906, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022907, "dur": 23, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022934, "dur": 21, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022956, "dur": 2, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022959, "dur": 24, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658022986, "dur": 21, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658023010, "dur": 306, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658023320, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658023353, "dur": 308, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658023664, "dur": 64, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658023731, "dur": 6, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658023739, "dur": 37, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658023780, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658023783, "dur": 32, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658023818, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658023819, "dur": 64, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658023887, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658023889, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658023932, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658023935, "dur": 35, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658023974, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658023977, "dur": 28, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024008, "dur": 2, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024011, "dur": 32, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024047, "dur": 2, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024050, "dur": 38, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024091, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024094, "dur": 30, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024126, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024128, "dur": 32, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024164, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024167, "dur": 31, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024200, "dur": 1, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024203, "dur": 34, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024241, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024271, "dur": 28, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024303, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024306, "dur": 28, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024342, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024346, "dur": 28, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024376, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024377, "dur": 27, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024406, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024408, "dur": 25, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024435, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024438, "dur": 35, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024476, "dur": 2, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024479, "dur": 31, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024512, "dur": 2, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024515, "dur": 33, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024552, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024555, "dur": 25, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024582, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024585, "dur": 32, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024620, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024622, "dur": 32, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024657, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024660, "dur": 34, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024698, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024701, "dur": 42, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024746, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024750, "dur": 31, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024783, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024785, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024812, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024814, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024844, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024846, "dur": 31, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024880, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024882, "dur": 34, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024920, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024923, "dur": 35, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024963, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024965, "dur": 26, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024994, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658024996, "dur": 21, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658025028, "dur": 5498, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658030535, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658030539, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658030594, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658030596, "dur": 28, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658030628, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658030630, "dur": 68, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658030702, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658030729, "dur": 457, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658031191, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658031223, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658031226, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658031254, "dur": 63, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658031320, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658031343, "dur": 279, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658031626, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658031666, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658031668, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658031693, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658031696, "dur": 196, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658031897, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658031923, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658031925, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658031953, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658031955, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658031984, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658031985, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658032031, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658032033, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658032070, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658032072, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658032101, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658032102, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658032126, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658032130, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658032171, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658032204, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658032230, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658032273, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658032297, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658032345, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658032369, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658032373, "dur": 158, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658032536, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658032566, "dur": 184, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658032754, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658032778, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658032780, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658032809, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658032835, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658032837, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658032868, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658032871, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658032898, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658032927, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658032929, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658032952, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658032954, "dur": 23, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658032979, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658032981, "dur": 20, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658033005, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658033028, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658033030, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658033054, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658033057, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658033086, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658033088, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658033115, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658033118, "dur": 33, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658033153, "dur": 3, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658033157, "dur": 30, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658033190, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658033192, "dur": 38, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658033235, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658033266, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658033268, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658033297, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658033300, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658033330, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658033352, "dur": 119, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658033474, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658033477, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658033516, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658033518, "dur": 26, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658033547, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658033549, "dur": 113, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658033669, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658033701, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658033703, "dur": 27, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658033733, "dur": 59, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658033797, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658033826, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658033828, "dur": 63, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658033893, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658033895, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658033921, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658033924, "dur": 293, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658034222, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658034256, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658034258, "dur": 131, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658034400, "dur": 5, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658034413, "dur": 69, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658034487, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658034518, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658034546, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658034548, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658034578, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658034614, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658034617, "dur": 28, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658034647, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658034649, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658034681, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658034683, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658034724, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658034792, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658034855, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658034883, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658034910, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658034912, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658034942, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658034944, "dur": 93, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658035042, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658035070, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658035099, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658035127, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658035151, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658035153, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658035184, "dur": 110, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658035299, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658035327, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658035329, "dur": 365, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658035699, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658035734, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658035736, "dur": 24, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658035762, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658035763, "dur": 59, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658035825, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658035827, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658035846, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658035877, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658035942, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658035968, "dur": 66, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658036038, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658036072, "dur": 112, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658036187, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658036254, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658036301, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658036330, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658036333, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658036364, "dur": 27, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658036394, "dur": 71, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658036470, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658036495, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658036521, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658036523, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658036552, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658036575, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658036602, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658036636, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658036667, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658036691, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658036714, "dur": 144, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658036869, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658036906, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658036933, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658036935, "dur": 23, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658036963, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658037003, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658037036, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658037038, "dur": 52, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658037094, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658037131, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658037133, "dur": 31, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658037166, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658037168, "dur": 61, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658037234, "dur": 248, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658037486, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658037514, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658037516, "dur": 25, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658037545, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658037623, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658037649, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658037651, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658037681, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658037712, "dur": 86, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658037801, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658037827, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658037854, "dur": 249, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658038108, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658038135, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658038161, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658038187, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658038191, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658038218, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658038278, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658038281, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658038311, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658038368, "dur": 228, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658038602, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658038628, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658038630, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658038662, "dur": 100, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658038766, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658038788, "dur": 242, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658039033, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658039063, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658039065, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658039127, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658039153, "dur": 756, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658039913, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658039937, "dur": 807, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658040753, "dur": 186380, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658227142, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658227146, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658227233, "dur": 7145, "ph": "X", "name": "ProcessMessages 1012", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658234383, "dur": 119, "ph": "X", "name": "ReadAsync 1012", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658234506, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658234510, "dur": 1121, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658235636, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658235702, "dur": 12, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658235715, "dur": 9754, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658245475, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658245478, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658245512, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658245515, "dur": 49663, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658295185, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658295188, "dur": 352, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658295545, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658295549, "dur": 317, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658295871, "dur": 106, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658295980, "dur": 16, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658295997, "dur": 70095, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658366100, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658366103, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658366142, "dur": 188, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658366333, "dur": 7068, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658373404, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658373406, "dur": 112, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658373520, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658373523, "dur": 541, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658374070, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658374143, "dur": 28, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658374173, "dur": 2647, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658376824, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658376827, "dur": 103, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658376933, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658376936, "dur": 736, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658377677, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658377708, "dur": 13, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658377722, "dur": 826, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658378552, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658378580, "dur": 345, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 20040, "tid": 12884901888, "ts": 1751452658378928, "dur": 20814, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 20040, "tid": 229864, "ts": 1751452658411829, "dur": 1004, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 20040, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 20040, "tid": 8589934592, "ts": 1751452657996720, "dur": 120769, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 20040, "tid": 8589934592, "ts": 1751452658117491, "dur": 5, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 20040, "tid": 8589934592, "ts": 1751452658117497, "dur": 1040, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 20040, "tid": 229864, "ts": 1751452658412836, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 20040, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 20040, "tid": 4294967296, "ts": 1751452657978641, "dur": 422133, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 20040, "tid": 4294967296, "ts": 1751452657983261, "dur": 7549, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 20040, "tid": 4294967296, "ts": 1751452658400790, "dur": 3787, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 20040, "tid": 4294967296, "ts": 1751452658403009, "dur": 87, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 20040, "tid": 4294967296, "ts": 1751452658404647, "dur": 12, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 20040, "tid": 229864, "ts": 1751452658412841, "dur": 7, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751452658007238, "dur": 1839, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751452658009086, "dur": 840, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751452658010034, "dur": 59, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751452658010093, "dur": 471, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751452658011329, "dur": 1447, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_F7240E92754989BB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751452658013661, "dur": 1416, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_0174E068A5FA251B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751452658015866, "dur": 107, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751452658017207, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.XR.LegacyInputHelpers.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751452658010583, "dur": 14307, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751452658024899, "dur": 354713, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751452658379612, "dur": 495, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751452658380153, "dur": 69, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751452658380407, "dur": 14703, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751452658010597, "dur": 14309, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658024926, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751452658025018, "dur": 153, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1751452658024917, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_E4AC68B67EFEC4DD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751452658025172, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658025347, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GameCenterModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751452658025346, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_C1CB3B5C02834557.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751452658025485, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubstanceModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751452658025484, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_1DC8AFE6C7A826A4.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751452658025633, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751452658025702, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1751452658025631, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_9C11D1C715218856.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751452658025853, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751452658025911, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658026003, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751452658026100, "dur": 214, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Management.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751452658026354, "dur": 231, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751452658026780, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658027199, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658027683, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658027992, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658028294, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658028603, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658029244, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658029582, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658029849, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658030214, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658030474, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658030770, "dur": 1199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658031970, "dur": 447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658032417, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658032474, "dur": 637, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658033111, "dur": 448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658033559, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658033651, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751452658034218, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Gradle.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751452658034617, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751452658033995, "dur": 838, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751452658034834, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658035057, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658035209, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658035421, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658035603, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658035761, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658035823, "dur": 562, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658036386, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658036448, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658036509, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658036772, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658037046, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751452658037143, "dur": 421, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658037570, "dur": 529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751452658038100, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658038250, "dur": 181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658038432, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658038543, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658038801, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658038884, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658039031, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658039375, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658039562, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658039620, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658040025, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658040110, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658040490, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658040553, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658040962, "dur": 194811, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751452658235773, "dur": 143910, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658010630, "dur": 14293, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658024934, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Gradle.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751452658025016, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1751452658024927, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_6BA57B641639B2BC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751452658025146, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658025261, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751452658025260, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_8C88AE4D2CCD7158.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751452658025352, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751452658025351, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_83F2C9E5C417FFF6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751452658025470, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751452658025468, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_AAF7ADEC23F2DAD1.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751452658025627, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751452658025625, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_A06F1EA8D8FA1A33.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751452658025694, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658025972, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.SpatialTracking.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751452658026082, "dur": 182, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751452658026289, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751452658026606, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751452658026740, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751452658026824, "dur": 537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658027361, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658027678, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658027959, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658028221, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658028559, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658029127, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658029383, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658029685, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658030020, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658030339, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658030589, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658030877, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658031244, "dur": 1177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658032421, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658032504, "dur": 611, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658033116, "dur": 444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658033560, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658033627, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751452658033947, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\Demigiant\\DOTween\\DOTween.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751452658034720, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751452658033945, "dur": 1038, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751452658034984, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658035167, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658035413, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751452658036184, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage@1.2.6\\Editor\\CoverageWindow\\PathToAddHandler.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751452658035555, "dur": 833, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751452658036505, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658036765, "dur": 181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658037052, "dur": 515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658037657, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658037871, "dur": 334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658038234, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658038431, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658038545, "dur": 221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658038787, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658038869, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658039023, "dur": 349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658039392, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658039548, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658039613, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658040040, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658040107, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658040488, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658040551, "dur": 398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658040970, "dur": 194787, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751452658235758, "dur": 143908, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658010624, "dur": 14292, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658024930, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751452658025013, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1751452658024919, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_3A4455FCF9EA132A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751452658025208, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751452658025207, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_A9EADF60550F98A5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751452658025270, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658025333, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DirectorModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751452658025332, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_8E967FCEA0CABFDC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751452658025406, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658025464, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751452658025463, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_2DFC1E35CC3D93F5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751452658025576, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751452658025575, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_BFA54F7E9CE16345.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751452658025640, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658025803, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658025963, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Management.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751452658026101, "dur": 155, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751452658026353, "dur": 240, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARCore.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751452658026594, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751452658026760, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751452658026864, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658027514, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658027806, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658028090, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658028382, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658028658, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658029199, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658029462, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658029926, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658030268, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658030541, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658030898, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658031202, "dur": 1215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658032417, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658032498, "dur": 613, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658033112, "dur": 449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658033561, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658033790, "dur": 929, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658034721, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Unity.EditorCoroutines.Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751452658034720, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751452658034825, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751452658034912, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658035003, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658035208, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658035424, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658035606, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658035763, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658035827, "dur": 572, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658036399, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658036558, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658036769, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658037051, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658037593, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658037649, "dur": 209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658037898, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658038259, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658038435, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658038553, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658038781, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658039011, "dur": 357, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658039376, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658039540, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658039604, "dur": 415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658040019, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658040103, "dur": 381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658040484, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658040547, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658040955, "dur": 194795, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751452658235761, "dur": 61211, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751452658235760, "dur": 61213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751452658296992, "dur": 703, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751452658297698, "dur": 81963, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658010687, "dur": 14283, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658024982, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Types.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751452658025047, "dur": 221, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1751452658024973, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_9B13BE4380FB0740.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751452658025319, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751452658025318, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_39EA3D21A2039B2E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751452658025441, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PhysicsModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751452658025439, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_3C9063487E7B345B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751452658025560, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751452658025558, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_4DD68C3F550D67BD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751452658025679, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751452658025678, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_1182C7970F80B506.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751452658025845, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751452658025844, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_CBE4C2A7CAF71BDD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751452658025954, "dur": 192, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_CBE4C2A7CAF71BDD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751452658026184, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARSubsystems.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1751452658026588, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751452658026656, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751452658026761, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751452658026874, "dur": 391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658027265, "dur": 352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658027617, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658027952, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658028229, "dur": 380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658028610, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658029169, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658029456, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658029731, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658030182, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658030513, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658030804, "dur": 1100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658031904, "dur": 518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658032423, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658032493, "dur": 602, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658033121, "dur": 435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658033557, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658033775, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751452658033973, "dur": 666, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658034645, "dur": 665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751452658035414, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658035600, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658035817, "dur": 571, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658036388, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658036451, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658036504, "dur": 261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658036766, "dur": 184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658037057, "dur": 522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658037647, "dur": 216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658037864, "dur": 346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658038234, "dur": 204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658038438, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658038551, "dur": 221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658038804, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658038881, "dur": 85, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658038966, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658039020, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658039378, "dur": 169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658039547, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658039618, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.VisualScripting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751452658039717, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.VisualScripting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751452658040122, "dur": 359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658040481, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658040544, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751452658040670, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658040952, "dur": 81657, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658122610, "dur": 113148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751452658235759, "dur": 143852, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658010667, "dur": 14295, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658024976, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.GradleProject.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452658025044, "dur": 180, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1751452658024966, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.GradleProject.dll_29F2B64BD1A1DCB9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751452658025263, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452658025263, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_72B29348CFCB9971.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751452658025324, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658025383, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452658025381, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_3977A7D73E1EE0EE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751452658025522, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452658025521, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_AF3BD820E5C5D09D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751452658025591, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658025677, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452658025675, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_C7FE3A462D97940C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751452658025787, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658025977, "dur": 226, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.CoreUtils.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751452658026261, "dur": 260, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.CoreUtils.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751452658026599, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.2D.Sprite.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751452658026784, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751452658026893, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658027339, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658027626, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658027932, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658028215, "dur": 376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658028591, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658029149, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658029426, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658029736, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658030063, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658030357, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658030623, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658030927, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658031208, "dur": 1207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658032416, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658032494, "dur": 606, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658033100, "dur": 441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658033542, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658033627, "dur": 486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751452658034308, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452658034164, "dur": 899, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751452658035063, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658035432, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.SpatialTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751452658035869, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Primitives.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452658036184, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\UnityEngine.SpatialTracking.ref.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751452658035586, "dur": 785, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.SpatialTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751452658036372, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658036507, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658036792, "dur": 193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658037058, "dur": 539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658037654, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658037862, "dur": 350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658038235, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658038446, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658038546, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658038790, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658038877, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658039034, "dur": 332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658039380, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658039544, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658039610, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658040027, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658040105, "dur": 378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658040483, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658040544, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751452658040657, "dur": 296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658040954, "dur": 194852, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751452658235808, "dur": 143874, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658010717, "dur": 14289, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658025018, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751452658025091, "dur": 185, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1751452658025010, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_24157DE88EB6BDBE.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751452658025327, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751452658025326, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_096F3F085FA84B73.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751452658025446, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ProfilerModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751452658025445, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_C0DCB9053A2446E9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751452658025555, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751452658025554, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_B4B36C72CEF54054.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751452658025675, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751452658025673, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_1CD089203CF9E868.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751452658025797, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658026047, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751452658026227, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751452658026396, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751452658026470, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751452658026594, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VSCode.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751452658026794, "dur": 476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658027270, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658027702, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658028233, "dur": 361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658028595, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658029178, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658029431, "dur": 404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658029836, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658030183, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658030461, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658030721, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658031212, "dur": 1199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658032411, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658032468, "dur": 629, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658033098, "dur": 447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658033545, "dur": 240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658033785, "dur": 933, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658034776, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751452658034854, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751452658035009, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658035212, "dur": 191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658035403, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658035630, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658035762, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658035815, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751452658036184, "dur": 161, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751452658035964, "dur": 735, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751452658036803, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658037058, "dur": 515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658037659, "dur": 202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658037861, "dur": 342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658038239, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658038434, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658038568, "dur": 222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658038790, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658038873, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658039016, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658039384, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658039560, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658039622, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658040023, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658040102, "dur": 395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658040497, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658040560, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658040969, "dur": 194840, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751452658235809, "dur": 143878, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658010749, "dur": 14265, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658025024, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751452658025103, "dur": 184, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1751452658025017, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_8360DAA503BDFED4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751452658025289, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658025425, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751452658025423, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_94317CF45651DD49.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751452658025520, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainPhysicsModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751452658025518, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_9BF84047519B3912.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751452658025634, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751452658025633, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_BB89E70A60043324.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751452658025699, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658025837, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751452658025835, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_3DF8E1AFD2EAE0B1.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751452658026044, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751452658026328, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751452658026432, "dur": 178, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.2D.Sprite.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751452658026612, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.SpatialTracking.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751452658026728, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751452658026795, "dur": 384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658027179, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658027535, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658027865, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658028208, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658028634, "dur": 540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658029174, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658029428, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658029721, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658030087, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658030406, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658030672, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658031004, "dur": 1156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658032160, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658032415, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658032547, "dur": 555, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658033103, "dur": 436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658033622, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751452658033844, "dur": 863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751452658034708, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658034776, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751452658034871, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658034972, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_16F8EA6B67022FB5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751452658035023, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751452658035167, "dur": 1272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751452658036539, "dur": 235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658036774, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658037058, "dur": 519, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658037653, "dur": 206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658037892, "dur": 321, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658038238, "dur": 191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658038479, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751452658038599, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751452658038919, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658039043, "dur": 332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658039375, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658039541, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658039604, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658040019, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658040114, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658040489, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658040548, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658040985, "dur": 194820, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751452658235806, "dur": 143828, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658010777, "dur": 14244, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658025032, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452658025107, "dur": 197, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 8, "ts": 1751452658025024, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_384C1F3E632FD32D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751452658025305, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658025369, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\iOSSupport\\UnityEditor.iOS.Extensions.Xcode.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452658025368, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_32B1888F61CF7983.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751452658025443, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658025524, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452658025523, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_F7240E92754989BB.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751452658025628, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452658025627, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_01045232C2BAAC12.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751452658025682, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658025790, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751452658025931, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751452658026113, "dur": 221, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751452658026464, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751452658026589, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751452658026740, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751452658026816, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658027277, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658027631, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658027996, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658028280, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658028592, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658029138, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658029388, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658029682, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658030101, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658030405, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658030670, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658030983, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658031576, "dur": 844, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658032495, "dur": 603, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658033098, "dur": 442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658033568, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658033626, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751452658033787, "dur": 183, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751452658034561, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\SolutionProjectEntry.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751452658033786, "dur": 884, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751452658034670, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658034819, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658035017, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658035237, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658035433, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658035591, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658035659, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658035764, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658035824, "dur": 559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658036415, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658036556, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658036781, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658037056, "dur": 518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658037688, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Simulation.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751452658037782, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Simulation.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751452658038270, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Simulation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751452658038362, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Simulation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751452658038816, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658038872, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658038979, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658039029, "dur": 344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658039373, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658039544, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658039603, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751452658039691, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751452658040098, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658040487, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658040569, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658040969, "dur": 194787, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751452658235757, "dur": 143907, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658010803, "dur": 14224, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658025038, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751452658025115, "dur": 202, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1751452658025030, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_A16031C8D5E7F23B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751452658025366, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751452658025365, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_0B617F8D8E18502B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751452658025442, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658025553, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751452658025551, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_19E375B5F566FEDB.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751452658025649, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751452658025648, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_0659794E1AF48F77.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751452658025839, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.NVIDIAModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751452658025837, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_50C4BCFC618A644B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751452658026021, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751452658026091, "dur": 154, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751452658026300, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751452658026598, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751452658026739, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1751452658026810, "dur": 396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658027206, "dur": 359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658027565, "dur": 361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658027927, "dur": 339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658028266, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658028615, "dur": 593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658029208, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658029488, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658029818, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658030334, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658030599, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658030773, "dur": 1251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658032025, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658032425, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658032552, "dur": 577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658033129, "dur": 415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658033545, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658033629, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751452658033872, "dur": 827, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751452658034700, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658035045, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658035214, "dur": 185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658035399, "dur": 184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658035612, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658035821, "dur": 568, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658036389, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658036510, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658036775, "dur": 179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658037049, "dur": 522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658037656, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658037866, "dur": 370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658038236, "dur": 207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658038443, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658038559, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658038786, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658038872, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658039015, "dur": 349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658039377, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658039543, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658039606, "dur": 415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658040021, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658040096, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658040481, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658040546, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658040957, "dur": 194887, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751452658235845, "dur": 143839, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658010854, "dur": 14191, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658025057, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751452658025128, "dur": 194, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": 1751452658025048, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_A0462AF7F9C7DD5D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751452658025350, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751452658025349, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_517C17530E5D22C2.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751452658025479, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751452658025477, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_411D9B9D7C552896.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751452658025545, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658025597, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_411D9B9D7C552896.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751452658025658, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751452658025657, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_078592F870AFC5CC.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751452658025775, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658025956, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751452658026047, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751452658026218, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARCore.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1751452658026612, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751452658026774, "dur": 371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658027146, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658027581, "dur": 394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658027976, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658028248, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658028586, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658029134, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658029387, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658029671, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658029958, "dur": 408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658030366, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658030846, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658031109, "dur": 922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658032032, "dur": 381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658032413, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658032486, "dur": 627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658033113, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658033546, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658033624, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751452658033829, "dur": 1489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751452658035420, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751452658036185, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Lib\\Editor\\log4netPlastic.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751452658035594, "dur": 780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751452658036374, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658036534, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658036778, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658036959, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658037059, "dur": 517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658037666, "dur": 198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658037864, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658038252, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658038436, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658038556, "dur": 232, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658038789, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658038958, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658039009, "dur": 354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658039377, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658039542, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658039614, "dur": 402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658040042, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658040101, "dur": 376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658040498, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658040556, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658040967, "dur": 194793, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751452658235761, "dur": 143879, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658010825, "dur": 14211, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658025050, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751452658025040, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_CA136C2D17F2F281.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751452658025146, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658025355, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751452658025353, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_0DD16C5308008CDA.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751452658025473, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751452658025471, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_89BA132246C7DB90.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751452658025572, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751452658025571, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_C3A7C19B07D61835.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751452658025642, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658025823, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658026002, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Management.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751452658026202, "dur": 183, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.XR.LegacyInputHelpers.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1751452658026423, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.FaceTracking.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1751452658026647, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751452658026779, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658027219, "dur": 384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658027603, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658027961, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658028227, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658028542, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658028806, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658029362, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658029674, "dur": 408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658030083, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658030365, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658030616, "dur": 638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658031254, "dur": 1153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658032437, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658032566, "dur": 544, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658033110, "dur": 433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658033544, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658033616, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARAnalytics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751452658033796, "dur": 510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARAnalytics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751452658034307, "dur": 612, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658035010, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658035210, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658035404, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658035613, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658035760, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658035819, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751452658036038, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658036139, "dur": 535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751452658036675, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658036806, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658037011, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658037074, "dur": 494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658037674, "dur": 202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658037876, "dur": 333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658038235, "dur": 192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658038450, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658038548, "dur": 240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658038788, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658038874, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658039018, "dur": 359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658039377, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658039559, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658039618, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658040035, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658040094, "dur": 385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658040479, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658040545, "dur": 407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658040953, "dur": 194800, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658235754, "dur": 139342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751452658375098, "dur": 3560, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751452658375098, "dur": 3561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751452658378673, "dur": 900, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751452658010875, "dur": 14177, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658025063, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751452658025055, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_BE03322452709CB2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751452658025200, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658025330, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751452658025329, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_486B4E27CA46B2F7.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751452658025416, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658025501, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_486B4E27CA46B2F7.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751452658025569, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751452658025568, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_BF543B2CA89F5C1C.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751452658025636, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658025706, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658025810, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658026086, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751452658026296, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751452658026618, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751452658026742, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751452658026832, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658027272, "dur": 368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658027641, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658027935, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658028206, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658028499, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658028783, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658029315, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658029599, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658029892, "dur": 388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658030280, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658030540, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658030802, "dur": 1244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658032047, "dur": 509, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658032557, "dur": 544, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658033102, "dur": 445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658033547, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658033606, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.SpatialTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751452658033829, "dur": 586, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658034634, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751452658034422, "dur": 724, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.SpatialTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751452658035210, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658035430, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751452658036053, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\UnityEngine.UI.ref.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751452658035563, "dur": 577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751452658036141, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658036389, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658036459, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751452658036617, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751452658037050, "dur": 516, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658037667, "dur": 198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658037865, "dur": 341, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658038232, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658038456, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658038544, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658038789, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658038878, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658038968, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658039026, "dur": 345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658039395, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658039545, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658039609, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658040022, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658040097, "dur": 388, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658040485, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658040552, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658040956, "dur": 194796, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751452658235754, "dur": 237, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1751452658235753, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1751452658236018, "dur": 1478, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1751452658237498, "dur": 142183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452658010901, "dur": 14159, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452658025070, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1751452658025135, "dur": 191, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 13, "ts": 1751452658025063, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_79696B0654C8F4B7.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751452658025374, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1751452658025372, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_019E00E629175977.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751452658025500, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1751452658025498, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_56D6DEB754397D49.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751452658025586, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_56D6DEB754397D49.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751452658025641, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1751452658025640, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_C1E653EE65094627.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751452658025900, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751452658026397, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1751452658026598, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751452658026806, "dur": 387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452658027193, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452658027556, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452658027985, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452658028237, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452658028570, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452658029147, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452658029414, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452658029718, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452658030018, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452658030331, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452658030750, "dur": 1345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452658032095, "dur": 323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452658032418, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452658032475, "dur": 621, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452658033124, "dur": 433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452658033558, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452658033622, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751452658033836, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452658034070, "dur": 628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751452658034699, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452658034865, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452658035021, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452658035211, "dur": 191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452658035402, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452658035602, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452658035758, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452658035822, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.CoreUtils.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751452658035950, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452658036185, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1751452658036146, "dur": 745, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.CoreUtils.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751452658037055, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.InternalUtils.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751452658037164, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.InternalUtils.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751452658037685, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751452658037802, "dur": 550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751452658038353, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452658038476, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751452658038579, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751452658039012, "dur": 345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452658039381, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452658039561, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452658039619, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452658040033, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452658040112, "dur": 383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452658040495, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452658040557, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452658040963, "dur": 194803, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751452658235766, "dur": 143854, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658010930, "dur": 14137, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658025079, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1751452658025071, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_2C6FB2D62EBEEE94.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751452658025154, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658025243, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1751452658025242, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_B93FCE4572AE3AA8.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751452658025362, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\iOSSupport\\UnityEditor.iOS.Extensions.Common.dll"}}, {"pid": 12345, "tid": 14, "ts": 1751452658025361, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_37743AF6A5E3F6D9.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751452658025496, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1751452658025494, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_15A5144DF23AC2E4.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751452658025600, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_15A5144DF23AC2E4.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751452658025693, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\WebGLSupport\\UnityEditor.WebGL.Extensions.dll"}}, {"pid": 12345, "tid": 14, "ts": 1751452658025691, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_30584634827FBF1C.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751452658025951, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.SpatialTracking.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1751452658026097, "dur": 187, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751452658026471, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1751452658026604, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.FaceTracking.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751452658026665, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658026763, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1751452658026888, "dur": 404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658027292, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658027568, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658027886, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658028171, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658028473, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658028727, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658029375, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658029760, "dur": 381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658030141, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658030446, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658030717, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658030990, "dur": 1146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658032136, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658032423, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658032570, "dur": 545, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658033115, "dur": 436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658033552, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658033611, "dur": 372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751452658033984, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658034090, "dur": 558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751452658034827, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.dll"}}, {"pid": 12345, "tid": 14, "ts": 1751452658035012, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658035212, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658035406, "dur": 184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658035609, "dur": 173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658035824, "dur": 560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658036508, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658036809, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658037057, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.CoreUtils.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751452658037210, "dur": 498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.CoreUtils.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751452658037709, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658037904, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARCore.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751452658038044, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARCore.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751452658038575, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658038786, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658038868, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658038924, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.VisualScripting.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751452658039028, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARFoundation.VisualScripting.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751452658039397, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658039550, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658039616, "dur": 415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658040032, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658040104, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658040486, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658040555, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658040959, "dur": 194803, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751452658235763, "dur": 143923, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658010961, "dur": 14114, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658025086, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1751452658025078, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_C3E59BB5F8B48795.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751452658025159, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658025297, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1751452658025295, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7058E5C79125A386.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751452658025437, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1751452658025435, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_D92E9E3DCDCA68ED.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751452658025506, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658025570, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1751452658025568, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_BB1D083DE47F9081.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751452658025687, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\iOSSupport\\UnityEditor.iOS.Extensions.dll"}}, {"pid": 12345, "tid": 15, "ts": 1751452658025685, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_2A03BB7F03493A3C.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751452658025814, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_2A03BB7F03493A3C.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751452658026044, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Management.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1751452658026224, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Simulation.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1751452658026498, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1751452658026619, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751452658026743, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751452658026852, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658027397, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658027688, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658027992, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658028247, "dur": 348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658028595, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658029153, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658029419, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658029697, "dur": 390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658030087, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658030386, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658030686, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658031121, "dur": 380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658031502, "dur": 922, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658032425, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658032511, "dur": 597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658033108, "dur": 442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658033550, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658033617, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751452658033751, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658033966, "dur": 621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751452658034587, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658034813, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751452658034996, "dur": 525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751452658035583, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658035766, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658035826, "dur": 564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658036391, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658036449, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658036521, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658036777, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658037048, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.FaceTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751452658037153, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARKit.FaceTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751452658037449, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658037666, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751452658037764, "dur": 954, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751452658038866, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658039008, "dur": 352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658039379, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658039607, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658040026, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658040100, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658040492, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658040574, "dur": 388, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658040962, "dur": 194797, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751452658235760, "dur": 143887, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452658010993, "dur": 14091, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452658025097, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658025088, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_FCB3152C479369C0.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751452658025219, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658025218, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_D6483D8C0D4E178A.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751452658025275, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452658025341, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DSPGraphModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658025339, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_93E7E138132B90FD.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751452658025451, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658025450, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_EF067A9A756EA993.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751452658025550, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658025549, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_C9A4E54165F4766F.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751452658025653, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658025652, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_F80B64918A78D7EF.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751452658025776, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452658025865, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_F80B64918A78D7EF.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751452658025950, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751452658026383, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Drawing.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658026451, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658026527, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658026585, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Numerics.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658026698, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658026769, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658026845, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Web.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658026926, "dur": 152, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Windows.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658027079, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658027134, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658027190, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658027245, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658027304, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658027379, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Buffers.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658027445, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658027524, "dur": 224, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658027753, "dur": 163, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658027918, "dur": 389, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658028308, "dur": 451, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658028760, "dur": 354, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658029116, "dur": 386, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658029504, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658029641, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658029701, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658029820, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658029883, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658029948, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658030015, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658030087, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658030164, "dur": 462, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658030628, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658030696, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658030769, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658030860, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658030941, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658031068, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658031146, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658031240, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658031312, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658031405, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658031483, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658031570, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658031646, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658031741, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658031822, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658031904, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658032006, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658032086, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658026064, "dur": 6341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751452658032510, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751452658032590, "dur": 456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751452658033138, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751452658033215, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751452658033606, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751452658033816, "dur": 410, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452658034232, "dur": 1464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751452658035812, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751452658036069, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452658036184, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658036153, "dur": 1403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751452658037665, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751452658037806, "dur": 1003, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751452658038922, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751452658039036, "dur": 450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751452658039601, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751452658039694, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751452658040128, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751452658040203, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751452658040541, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751452658040659, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751452658040948, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751452658041839, "dur": 187195, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751452658236048, "dur": 10834, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751452658235751, "dur": 11191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751452658247227, "dur": 71, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751452658247310, "dur": 120597, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751452658375037, "dur": 168, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1751452658375036, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1751452658375221, "dur": 615, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1751452658375839, "dur": 3841, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658011029, "dur": 14063, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658025101, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1751452658025095, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_30DC5CCDD2C811D2.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751452658025247, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1751452658025247, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_977DF1413C425466.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751452658025337, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll"}}, {"pid": 12345, "tid": 17, "ts": 1751452658025336, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_FCB7B30576667F66.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751452658025462, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1751452658025460, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_708A3E0E0CD23C1E.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751452658025528, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658025588, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_708A3E0E0CD23C1E.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751452658025651, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1751452658025650, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_4A837DB03C61DFDC.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751452658025777, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658025835, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1751452658025833, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_55E4B2A1BED5EA50.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751452658025907, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658026112, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751452658026405, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Simulation.Editor.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1751452658026582, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751452658026728, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751452658026803, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658027228, "dur": 406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658027634, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658027963, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658028250, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658028543, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658029172, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658029452, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658029749, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658030095, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658030363, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658030623, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658030935, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658031262, "dur": 1150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658032412, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658032468, "dur": 641, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658033109, "dur": 441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658033550, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658033609, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751452658033852, "dur": 714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1751452658034567, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658034825, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.ref.dll_66DE4C1C9A03AD08.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751452658034958, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751452658035167, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1751452658035656, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658035812, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARSubsystems.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751452658035988, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658036184, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1751452658036147, "dur": 776, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARSubsystems.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1751452658037048, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARSubsystems.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751452658037205, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARSubsystems.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1751452658037654, "dur": 216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658037870, "dur": 344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658038237, "dur": 205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658038442, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658038543, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658038782, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658038863, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658038958, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658039010, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658039379, "dur": 173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658039553, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658039617, "dur": 415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658040032, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658040107, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658040491, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658040553, "dur": 405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658040958, "dur": 194850, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751452658235808, "dur": 143819, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658011051, "dur": 14048, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658025111, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1751452658025103, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_78C4933F624BCAF0.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751452658025330, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CoreModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1751452658025328, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_A681ADF7ABC369FF.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751452658025432, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1751452658025430, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_9B7D977438583E85.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751452658025538, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TilemapModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1751452658025537, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_F5D346386BF65E52.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751452658025640, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1751452658025639, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_39AD4EBB1D1424EB.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751452658025766, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658025957, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARSubsystems.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751452658026026, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARSubsystems.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751452658026190, "dur": 159, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1751452658026427, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.SpatialTracking.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1751452658026538, "dur": 200, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751452658026754, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1751452658026859, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658027279, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658027564, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658027858, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658028121, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658028450, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658028724, "dur": 614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658029339, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658029633, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658029914, "dur": 917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658030831, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658031329, "dur": 1080, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658032475, "dur": 639, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658033114, "dur": 435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658033549, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658033614, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751452658033921, "dur": 711, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1751452658034632, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658034807, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658034887, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658034968, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658035030, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658035207, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658035410, "dur": 197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658035607, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658035764, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658035822, "dur": 565, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658036387, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658036528, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658036783, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658037050, "dur": 546, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658037651, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658037869, "dur": 339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658038233, "dur": 207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658038440, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658038565, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658038782, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658038865, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658038938, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751452658039056, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1751452658039410, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658039558, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658039615, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658040025, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658040095, "dur": 383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658040500, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658040556, "dur": 405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658040961, "dur": 194802, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751452658235763, "dur": 143890, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658011070, "dur": 14037, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658025125, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1751452658025110, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_128BDF40C52915E6.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751452658025203, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658025306, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1751452658025305, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_D301E026C7E6BC4A.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751452658025402, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658025459, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1751452658025457, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_8C9666DB874133B6.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751452658025524, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658025603, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_8C9666DB874133B6.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751452658025685, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\UnityEditor.Android.Extensions.dll"}}, {"pid": 12345, "tid": 19, "ts": 1751452658025683, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_6421414B18C06C7C.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751452658026049, "dur": 292, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Management.Editor.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1751452658026360, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1751452658026542, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751452658026617, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751452658026774, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658027124, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658027562, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658027983, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658028239, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658028502, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658028772, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658029340, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658029636, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658029939, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658030231, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658030492, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658030766, "dur": 1274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658032040, "dur": 398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658032472, "dur": 640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658033112, "dur": 434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658033547, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658033619, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Management.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751452658033848, "dur": 179, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1751452658033793, "dur": 826, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Management.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1751452658034620, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658034788, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Management.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751452658035129, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.Management.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1751452658035593, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658035691, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658035765, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658035828, "dur": 563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658036391, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658036505, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658036782, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658036962, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658037060, "dur": 509, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658037671, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658037872, "dur": 344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658038237, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658038433, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658038541, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658038780, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658038864, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658038982, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658039035, "dur": 327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658039379, "dur": 168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658039548, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658039612, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658040029, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658040109, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658040489, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658040561, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658040971, "dur": 194842, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751452658235813, "dur": 143835, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658011099, "dur": 14017, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658025127, "dur": 201, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658025117, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_1935086DF322E590.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751452658025384, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658025383, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_56A29088F8660FBC.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751452658025453, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658025528, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658025526, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_99C9B018C409BC13.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751452658025596, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658025713, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658025825, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658025971, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751452658026090, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751452658026383, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Drawing.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658026452, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658026519, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658026587, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Numerics.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658026643, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658026694, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658026770, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658026846, "dur": 251, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Web.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658027100, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658027210, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658027264, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658027318, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658027392, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Buffers.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658027445, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658027519, "dur": 165, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658027686, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658027753, "dur": 162, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658027916, "dur": 390, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658028307, "dur": 452, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658028760, "dur": 356, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658029120, "dur": 382, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658029503, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658029589, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658029645, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658029699, "dur": 153, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658029854, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658029966, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658030018, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658030091, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658030163, "dur": 474, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658030638, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658030706, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658030766, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658030883, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658030951, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658031064, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658031146, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658031229, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658031314, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658031405, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658031481, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658031570, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658031638, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658031724, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658031821, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658031902, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658032004, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658032086, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.43f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751452658026220, "dur": 6127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751452658032485, "dur": 614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658033099, "dur": 443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658033542, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658033790, "dur": 928, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658034721, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658034859, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658035050, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658035212, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658035398, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658035632, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658035759, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658035820, "dur": 575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658036509, "dur": 262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658036771, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658037046, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARCore.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751452658037155, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.XR.ARCore.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751452658037505, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658037655, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658037870, "dur": 334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658038233, "dur": 200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658038434, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658038553, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658038796, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658038879, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658038976, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658039032, "dur": 341, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658039373, "dur": 194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658039605, "dur": 415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658040020, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658040093, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658040482, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658040545, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658040951, "dur": 79523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658120475, "dur": 2130, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658122606, "dur": 113149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751452658235756, "dur": 143857, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751452658397907, "dur": 3356, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 20040, "tid": 229864, "ts": 1751452658413377, "dur": 38212, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 20040, "tid": 229864, "ts": 1751452658451635, "dur": 2236, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 20040, "tid": 229864, "ts": 1751452658409569, "dur": 45173, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}