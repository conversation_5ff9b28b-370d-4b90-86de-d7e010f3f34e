using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.Playables;

public class PressStartContollerNew : MonoBehaviour   
{
    public TextMeshProUGUI helpText;


    public GameObject playGameObject;
    public GameObject replayGameObject;



    public static UnityEvent onStartButtonPressedNew = new UnityEvent();

    public static UnityEvent onReplayButton = new UnityEvent();

    public static UnityEvent onPauseButton = new UnityEvent();

    public static UnityEvent onUnPauseButton = new UnityEvent();

    // Start is called before the first frame update
    void Start()
    {

        replayGameObject.SetActive(false);
       
        playGameObject.SetActive(false);

        StartCoroutine(CallStartButton());

    }


    IEnumerator CallStartButton()
    {
        yield return new WaitForSeconds(2f);
        playGameObject.SetActive(true);
       
    }


    public void PressPlayButton()
    {
        helpText.gameObject.SetActive(false);



        onStartButtonPressedNew.Invoke();
        playGameObject.SetActive(false);
        replayGameObject.SetActive(true);

        onReplayButton.Invoke();
        playGameObject.SetActive(true);
        replayGameObject.SetActive(false);

    }

    public void PressPauseButton()
    {
        onReplayButton.Invoke();
    }

    public void PressReplayButton() { 
       
 
    }

}
