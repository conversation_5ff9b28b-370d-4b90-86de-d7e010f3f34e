using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.Playables;

public class PressStartContollerNew : MonoBehaviour   
{
    public TextMeshProUGUI helpText;


    public GameObject playGameObject;
    public GameObject replayGameObject;



    public static UnityEvent onStartButtonPressedNew = new UnityEvent();

    public static UnityEvent onReplayButton = new UnityEvent();

    public static UnityEvent onPauseButton = new UnityEvent();

    public static UnityEvent onUnPauseButton = new UnityEvent();

    private bool hasStarted = false;
    private bool isPaused = false;

    // Start is called before the first frame update
    void Start()
    {

        replayGameObject.SetActive(false);
       
        playGameObject.SetActive(false);

        StartCoroutine(CallStartButton());

    }


    IEnumerator CallStartButton()
    {
        yield return new WaitForSeconds(2f);
        playGameObject.SetActive(true);
       
    }


    public void PressPlayButton()
    {
        if (!hasStarted)
        {
            // First time pressing - start the timeline
            Debug.Log("First press - Starting timeline");
            helpText.gameObject.SetActive(false);
            replayGameObject.SetActive(true);
            playGameObject.SetActive(false);

            onStartButtonPressedNew.Invoke();
            hasStarted = true;
            isPaused = false;
        }
        else
        {
            // Toggle between pause and unpause
            if (isPaused)
            {
                Debug.Log("Unpausing timeline");
                onUnPauseButton.Invoke();
                isPaused = false;
            }
            else
            {
                Debug.Log("Pausing timeline");
                onPauseButton.Invoke();
                isPaused = true;
            }
        }
    }

  

    public void PressReplayButton() { 
       
        onReplayButton.Invoke();
    }

}
