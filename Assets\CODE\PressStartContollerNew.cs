using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.Playables;

public class PressStartContollerNew : MonoBehaviour   
{
    public TextMeshProUGUI helpText;


    public GameObject playGameObject;
    public GameObject replayGameObject;



    public static UnityEvent onStartButtonPressedNew = new UnityEvent();

    public static UnityEvent onReplayButton = new UnityEvent();

    public static UnityEvent onPauseButton = new UnityEvent();

    public static UnityEvent onUnPauseButton = new UnityEvent();

    private bool hasStarted = false;
    private bool isPaused = false;

 
    void Start()
    {
        replayGameObject.SetActive(false);
        playGameObject.SetActive(false);
    }

    void OnEnable()
    {  
        ARCursor.onARObjectPlaced.AddListener(OnARObjectPlaced);
    }

    void OnDisable()
    {
        ARCursor.onARObjectPlaced.RemoveListener(OnARObjectPlaced);
    }

    void OnARObjectPlaced()
    {
        playGameObject.SetActive(true);
    }


    public void PressPlayButton()
    {
        if (!hasStarted)
        {
          
            helpText.gameObject.SetActive(false);
            replayGameObject.SetActive(true);
            playGameObject.SetActive(true);

            onStartButtonPressedNew.Invoke();
            hasStarted = true;
            isPaused = false;
        }
        else
        {
            if (isPaused)
            {
                Debug.Log("Unpausing timeline");
                onUnPauseButton.Invoke();
                isPaused = false;
            }
            else
            {
                Debug.Log("Pausing timeline");
                onPauseButton.Invoke();
                isPaused = true;
            }
        }
    }

  

    public void PressReplayButton() {
        Debug.Log("Replay button pressed - restarting timeline");
        onReplayButton.Invoke();
        hasStarted = true; 
        isPaused = false;   
    }

}
