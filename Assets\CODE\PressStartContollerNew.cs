using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.Playables;

public class PressStartContollerNew : MonoBehaviour   
{
    public TextMeshProUGUI helpText;


    public GameObject playGameObject;
    public GameObject replayGameObject;



    public static UnityEvent onStartButtonPressedNew = new UnityEvent();

    public static UnityEvent onReplayButton = new UnityEvent();

    public static UnityEvent onPauseButton = new UnityEvent();

    public static UnityEvent onUnPauseButton = new UnityEvent();

    private bool hasStarted = false;
    private bool isPaused = false;

    // Start is called before the first frame update
    void Start()
    {
        replayGameObject.SetActive(false);
        playGameObject.SetActive(false);
    }

    void OnEnable()
    {
        // Subscribe to AR object placement event
        ARCursor.onARObjectPlaced.AddListener(OnARObjectPlaced);
    }

    void OnDisable()
    {
        // Unsubscribe from AR object placement event
        ARCursor.onARObjectPlaced.RemoveListener(OnARObjectPlaced);
    }

    void OnARObjectPlaced()
    {
        Debug.Log("AR Object detected in scene - showing play button");
        playGameObject.SetActive(true);
    }


    public void PressPlayButton()
    {
        if (!hasStarted)
        {
            // First time pressing - start the timeline
            Debug.Log("First press - Starting timeline");
            helpText.gameObject.SetActive(false);
            replayGameObject.SetActive(true);
            playGameObject.SetActive(false);

            onStartButtonPressedNew.Invoke();
            hasStarted = true;
            isPaused = false;
        }
        else
        {
            // Toggle between pause and unpause
            if (isPaused)
            {
                Debug.Log("Unpausing timeline");
                onUnPauseButton.Invoke();
                isPaused = false;
            }
            else
            {
                Debug.Log("Pausing timeline");
                onPauseButton.Invoke();
                isPaused = true;
            }
        }
    }

  

    public void PressReplayButton() {
        Debug.Log("Replay button pressed - restarting timeline");
        onReplayButton.Invoke();

        // Reset the state since timeline is restarting and will be playing
        hasStarted = true;  // Timeline has started
        isPaused = false;   // Timeline is playing (not paused)
    }

}
