<libraries>
  <library
      name="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle@@:unityLibrary::release"
      jars="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\.transforms\39253d86aaeef21b06330afd2eaf1eb4\transformed\out\jars\classes.jar;D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\.transforms\39253d86aaeef21b06330afd2eaf1eb4\transformed\out\jars\libs\R.jar;D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\.transforms\39253d86aaeef21b06330afd2eaf1eb4\transformed\out\jars\libs\unity-classes.jar"
      resolved="Gradle:unityLibrary:unspecified"
      folder="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\build\.transforms\39253d86aaeef21b06330afd2eaf1eb4\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="__local_aars__:D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\libs\unity-classes.jar:unspecified@jar"
      jars="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\libs\unity-classes.jar"
      resolved="__local_aars__:D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\libs\unity-classes.jar:unspecified"/>
  <library
      name=":arcore_client:@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\8f56f7bd0e5bdc0c456829721fdd8247\transformed\jetified-arcore_client\jars\classes.jar"
      resolved=":arcore_client:"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\8f56f7bd0e5bdc0c456829721fdd8247\transformed\jetified-arcore_client"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":ARPresto:@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\844d4a1d20367ffb4a6794cca10a59b6\transformed\jetified-ARPresto\jars\classes.jar"
      resolved=":ARPresto:"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\844d4a1d20367ffb4a6794cca10a59b6\transformed\jetified-ARPresto"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":UnityARCore:@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\09678f7bda27986b3fca7ae5e31d96bb\transformed\jetified-UnityARCore\jars\classes.jar"
      resolved=":UnityARCore:"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\09678f7bda27986b3fca7ae5e31d96bb\transformed\jetified-UnityARCore"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":unityandroidpermissions:@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\fc180837684a54e724ff65ec8e166ead\transformed\jetified-unityandroidpermissions\jars\classes.jar"
      resolved=":unityandroidpermissions:"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\fc180837684a54e724ff65ec8e166ead\transformed\jetified-unityandroidpermissions"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle@@:unityLibrary:xrmanifest.androidlib::release"
      jars="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\xrmanifest.androidlib\build\.transforms\601fc2477f3d43dcdf43527446b8546e\transformed\out\jars\classes.jar;D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\xrmanifest.androidlib\build\.transforms\601fc2477f3d43dcdf43527446b8546e\transformed\out\jars\libs\R.jar"
      resolved="Gradle.unityLibrary:xrmanifest.androidlib:unspecified"
      folder="D:\Unity_Project\SurAnim\Library\Bee\Android\Prj\Mono2x\Gradle\unityLibrary\xrmanifest.androidlib\build\.transforms\601fc2477f3d43dcdf43527446b8546e\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
</libraries>
