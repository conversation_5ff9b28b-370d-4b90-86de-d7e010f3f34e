﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>


template <typename R>
struct VirtualFuncInvoker0
{
	typedef R (*Func)(void*, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		return ((Func)invokeData.methodPtr)(obj, invokeData.method);
	}
};

struct Action_1_t11340E5174173030E076A75316E18A395082F8A5;
struct Action_1_t54327BAFA15E24E002EFAE48D1AFFC020D368735;
struct Action_1_tE3E0B091088607877804833E23BFDBBF5594B830;
struct Action_1_t30DB722576CDAD6BB3F09BD60358BF49AA947548;
struct Action_1_t0A16CC637B74FE43A20D2784BCA3172E7D13789C;
struct Action_1_t78DC7DD8CB74A1B21E4419996B784C6E51821247;
struct Action_1_tE5F1AC110E5F6610E8B460B753F57ACC9B4524E3;
struct Action_1_t009680BF19017ECA60753C7F605CBD85C56C6560;
struct Action_1_t053CCF2809F9B0B61EA8298154533C5FACB724F0;
struct Action_1_t325C602D999E964DB72210E493635E83F8D6CE68;
struct Action_1_tE7B3D1070B99EFDFA92BD4DBEF95BF881DEB591F;
struct Action_1_t34AAB95B35DF66496184F8421A8AC5F317B7D8AB;
struct Action_1_tB4285954D4854A1287BC3C7667C0DFC4D3A5F9A1;
struct Dictionary_2_t02017471B47A6BEB88F94FACE526F622A42AEE49;
struct Dictionary_2_t2A895F6D5510191F016166DBE21C821944195F02;
struct Dictionary_2_t3D5C4496A22EAD3D8DD55DCDFBB4F4E9BE8311FF;
struct Dictionary_2_tA348003A3C1CEFB3096E9D2A0BC7F1AC8EC4F710;
struct Dictionary_2_t525DE08A369658A5AE7D624D571ED15216817535;
struct Dictionary_2_t7993FC827CBC9BA9CA6B912A2DF915B143970FD4;
struct Dictionary_2_t24DD0A560FD522E53E9814CD1A0AD91C3AD5FCCC;
struct Dictionary_2_t26E20BE1EB8A93CA83F951C6E2C3E93D20E1EA30;
struct Dictionary_2_t30E6C815332BDA64B92237BE387396E9362813BE;
struct Dictionary_2_t26A4820BA6F451105138232941B7A9466E583A6C;
struct Dictionary_2_t9647896A9433E05337C4FDD2755A5C5C81C52F6C;
struct Dictionary_2_t13B80190D7DD5A74B43A63EC63B97E079A0B2FC2;
struct Dictionary_2_t7DA79BD2AB092CBEF953D24BF7B5C6154A0F04D2;
struct EventUnit_1_tA2B0E6295E58DB46B38F944529B0DF5E40ABF917;
struct EventUnit_1_t0E2A788AF4E4FA2B47C39B7930B50D725B69338D;
struct Func_2_tCF3029BCCA647C1B54318B688881E1742A230E8C;
struct Func_2_tE299C631F0CB2E8E61444D749D446C27349C90B8;
struct Func_2_tFB5F2454526EA057FC21D7028A8B1EADDF2F531F;
struct Func_2_tB98BB93B60A3562C54694B9F37AEBD38DE206A9C;
struct Func_2_tAF4123F8D69F1A665BBB864E1C97131C05AEBBEB;
struct Func_2_tC5B959A6DD6FDE84607DD9ECC529B9BE485C63C5;
struct Func_2_tACBF5A1656250800CE861707354491F0611F6624;
struct GameObjectEventUnit_1_tA12F0B3D1C478378C11E341089186ED62766AF93;
struct GameObjectEventUnit_1_tD98F9076C76336C1A30A327C151C4BC55CBE45FE;
struct GetTrackablesUnit_6_tE3FF81AB62ABB3500474D301EF59B13BF4A1685E;
struct GetTrackablesUnit_6_tCE90D2E00FA14B5E936843082D488242C7463304;
struct GetTrackablesUnit_6_tB48912D7C44FC073BE0CCC0B19CCC4A33E066B77;
struct GetTrackablesUnit_6_t3DE4D256458C27210703957091C3684A86E12198;
struct GetTrackablesUnit_6_t35B2538FE2BE005439F84D8E3DD2098F607A881B;
struct GetTrackablesUnit_6_t016BB0C74695D0122C8B6B3599A71F89D32BCF77;
struct GetTrackablesUnit_6_tBEEA64178AC6F947BE154CE4D41420AED64DC0AE;
struct GetTrackablesUnit_6_tA1B41E03CB6324ED319781B3AA151D349922B24A;
struct GetTrackablesUnit_6_t5FAD854E9B61FD3E7409D18C4DED57BC373BACE7;
struct GetTrackablesUnit_6_t60E470A25938E95AA9CD62B873EA6C36FC34D2FF;
struct GetTrackablesUnit_6_t53BDD6CA0F569B84218F5FD0AE07A3F5A12B4681;
struct GetTrackablesUnit_6_tF13014734999E8A2395BF31956B595A732B18759;
struct GetTrackablesUnit_6_tD4A938F1E77E1BAE7C41C15D337E459E3CB21C85;
struct GetTrackablesUnit_6_t7CDABC4A4629CF084D0C402FEABCB091691C0EC3;
struct GetTrackablesUnit_6_t394D5BEFC7488634A560E3C112D1AA5F0B2597B8;
struct GetTrackablesUnit_6_t40700C5453679D51CCE89F1B99C1B4EBCF9F8144;
struct GetTrackablesUnit_6_tA252AFCA8D6AD9170BE371DD63B4E1A3D1DC456B;
struct GetTrackablesUnit_6_tE93598F0E5891061730B627819A5B94E3EC137C8;
struct GetTrackablesUnit_6_t5CCB2F47ECD9360ADBCC2038E60046BB7E753997;
struct GetTrackablesUnit_6_tEE2BC0E2559CB40B4104C870DF982285700AAA7E;
struct GlobalEventUnit_1_tFCC2A75C905819911151CA0BD2FEE0E3A24D5FE6;
struct GlobalEventUnit_1_tFA67B258020D820C41BB840BDBB4B4C3FEA8794B;
struct HashSet_1_t015F1F38EE02675ACF63C72E1E1D832249BB06DA;
struct HashSet_1_tAE2F12E55878645F5BE7C4D5603228A6FAB429C7;
struct ICollection_1_tAEF1C056C36E7D5FEA74D8B3050426267827D51F;
struct IConnectionCollection_3_t3FEDE29075183F9ACBC556BA0F14BF8852E55CD8;
struct IUnitPortCollection_1_t3F6779B00496B9FD84FEF2783F509958D57F354E;
struct IUnitPortCollection_1_t867A0E5526AAABA203704CD692FBEA4174C71C05;
struct IUnitPortCollection_1_t3839C272AE6727AE9A1BAC110A75614EBFE42B31;
struct IUnitPortCollection_1_t0F2047F1654080ECEB25DFDFAE47ED18B2E4C0F7;
struct IUnitPortCollection_1_t3B86C97EE8F1E1DB05CEFB161850DF05862EAFD4;
struct IUnitPortCollection_1_tA11C7DC2E90483113DB83CE13759F647978FFF63;
struct List_1_t5C2EBF017989E44F3D1D11AF502E10F6F8D9AB53;
struct List_1_t6F14EE6179A9A8E042C5D65E902F72E24597029B;
struct List_1_tA11EBED64210DD13BB4303415D948E6CD964AAF9;
struct List_1_t64B1B4ABB789350AE017F3EAF4D3647AF59244C7;
struct List_1_t87D9AC2D6B4A4860E01575669DEB0D164D4B33B3;
struct List_1_t10BA05B555C92BD54800DFF82DCAAFC9DE44A077;
struct List_1_tB9CD639EB4FE5EA0A8B417168BB9BD2D29AEE271;
struct List_1_tBEE41A3C84ED348850866AD76B66E9D9B19767FF;
struct List_1_tFA730335FF46512E4245B2F46892CA318B394FAE;
struct List_1_tF1201F2F725A8AE529D436F38A4771FB6790F3C6;
struct List_1_tE97B7994B98675F18C70DDD8DEB61E4FE5491DA1;
struct List_1_tF1785B2080B8536D64E747DAE8C2DE0D44A75E27;
struct List_1_t1715D0D9950CCCC39A77E9597CA69B346CD7E3E5;
struct List_1_tF8892BD96CF6B23CD95F24F5381A6E7237540FD1;
struct List_1_t645DF2A1E7B0FB808EDE07ED53B8C56194AF35E6;
struct List_1_t05915E9237850A58106982B7FE4BC5DA4E872E73;
struct List_1_tF470A3BE5C1B5B68E1325EF3F109D172E60BD7CD;
struct List_1_t0F231C3F13EBA1FF9081BD61489D01AA3CBE59D4;
struct List_1_t9F2B0C5D9A34683317CA96A0A733BA6B33CC089A;
struct List_1_t65387FE114D984E6DCFF71774BFC19CEB318A9BE;
struct List_1_t1C988AEFF3BCCFEF103EACA601923CD56DBB593C;
struct List_1_tB62E7DBB1A743CF013D1542F79F59C2BB105B7AC;
struct List_1_t4B337AB9321BAB1DBB08B7AC7AAA57B1FA9B3A79;
struct List_1_tDE8C3B426AF72FF013BD4E2E4C4660F30F1371DB;
struct List_1_t0C2BEA4603C8A892F136360A713FE07EBC51A010;
struct List_1_t5E8878D20C1B753B569DF68C6CCE3159D9AB04A6;
struct List_1_t647C6DA33EBE425BA395C4BBB09B4494DD875198;
struct List_1_t5EDD6B438D214F2BE1FEC4BEBC8341CA330D7512;
struct List_1_t447913E715E9E298A8BBACADFBAA1CB629A73DD1;
struct List_1_tFAD70D497835B4EB99F6C04DFD87EA9406E796ED;
struct List_1_tF236FF325A33D8570EBBBEC67532DE39F1E3F98A;
struct List_1_tCD3E505D75F3A61FA85FC4FBBAE146ACC1A844AA;
struct List_1_tCE2DE33D09B801655AC776675438A1D19533C827;
struct List_1_t4EB1B73C8DDBD534099637F220FA7D8AFC3D2DE4;
struct List_1_t6630EC2D13FD2DC6D86447667371501266C9B3E8;
struct List_1_tD705D57B270C006E488F9C9B55BA0768A11125D0;
struct List_1_t7628A8BB9829B862564998B09CB85AD696996DBE;
struct List_1_tC217BA96D4ED5CDFFBE952CBFC55733D9DE83A7F;
struct Recursion_1_tDFF1B549E70648F1694033FADF89F8F0EB28587D;
struct Stack_1_t3197E0F5EA36E611B259A88751D31FC2396FE4B6;
struct TrackableManagerListener_1_t368727D76E305857E6524137ED96D00A18546BAC;
struct TrackableManagerListener_1_t60BDFBD938C7CBEAEEEC4ED167E4D757936C28E8;
struct TrackableManagerListener_1_tAE0B39F766687B7392BB26FC0CEBCAA679BEB365;
struct TrackableManagerListener_1_t8EF347E8BB7B8747C247403C9E911620DE36B242;
struct TrackableManagerListener_1_tE4BF78B1F882C85C4BC3496DE722CE244D42E779;
struct TrackableManagerListener_1_t8673B1A6156F98541B1B39B98473DFF37AC0CC90;
struct TrackableManagerListener_1_tFCE62DCE9ECC332FFBFBF7949494CC4DF7DA37E9;
struct TrackableManagerListener_1_t7C3219B15D46C0E0276CF4D8E06F12DC969F5D56;
struct TrackableManagerListener_1_t2D0E0BEBA98DF361C2EB9261ED0310BF3DFF8FD5;
struct TrackableManagerListener_1_tF5FD12C1482D0B415F386E1BC32077FEBAA40B8D;
struct TrackablesChangedEventUnit_8_t937DCA5FB2B32B242842AD0C1F2F5EE5F6ADF227;
struct TrackablesChangedEventUnit_8_t7E2C88EBC443BD6C1236769AD6E11B6822447A38;
struct TrackablesChangedEventUnit_8_t49CC6AB3D8E782C8141D1915452029E22C2213F0;
struct TrackablesChangedEventUnit_8_tBEDFC346D94F264473B89BF51CB942BCC3D7A3BC;
struct TrackablesChangedEventUnit_8_tD6C8D29C0AC7F5F97EF5642B923DFE86702323F8;
struct TrackablesChangedEventUnit_8_tA9240E148EC702D556AA0394A8591794B81864EE;
struct TrackablesChangedEventUnit_8_tEB3E8F04C93B5D5BA458A74D225A0598224B0211;
struct TrackablesChangedEventUnit_8_t9B0C0052BA572E6D5D816B31AE40C31151AB58B2;
struct TrackablesChangedEventUnit_8_t421739B4DF339B26394C3BAFA5BEDF14102691BB;
struct TrackablesChangedEventUnit_8_t80ACB5EC468FCE7380B5D1FFB60D34852FDDECEB;
struct TrackablesChangedEventUnit_8_t1733B9D53AC7A75201E66A3C4299F9EEAF6D9AC9;
struct TrackablesChangedEventUnit_8_t32C53D3043B80B877FC2C13D865611AE1870707B;
struct TrackablesChangedEventUnit_8_t3ED65845C697D5F6365D653761D50033F9DEA043;
struct TrackablesChangedEventUnit_8_tAD7DEECE198B73643980718B72E5D18B9AE18A29;
struct TrackablesChangedEventUnit_8_tE5B851C314A74199250431CA9EF82851FE17AF1E;
struct TrackablesChangedEventUnit_8_t2B87F30FA4D65ECE396E4A074036A8F6A0F95654;
struct TrackablesChangedEventUnit_8_t9D9E1B534664684D3799239F14EA6394D5EA7102;
struct TrackablesChangedEventUnit_8_t65F4F7BA35861764870385ED28CF9DA4366517DB;
struct ARAnchorU5BU5D_t589CB7678527B30DBFABED0E377BC500C9D3454A;
struct AREnvironmentProbeU5BU5D_t172E5E7D75D1738636091C776C8EEC8BAAE3839A;
struct ARFaceU5BU5D_t6FC6B0063C2B88AFFF728161A36C9B4402F65711;
struct ARHumanBodyU5BU5D_t765862C91212D61EA388CB29F8895BC1FDB1EB0D;
struct ARParticipantU5BU5D_tE306DE8EAC060B47D21D12030F3E59D78AE4BF9D;
struct ARPlaneU5BU5D_t8C555584F9BAD4FB2BD4DB6D7933EBBBD61D6FD4;
struct ARPointCloudU5BU5D_tD64665DDD0344C5780D4BDC332717DA27CEF1CE0;
struct ARTrackedImageU5BU5D_tD227987C766F9B880B8EF0E9ADF5918CDFB72367;
struct ARTrackedObjectU5BU5D_t432458E1432DA3112081C0414632055D92813EDE;
struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;
struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
struct DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771;
struct IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832;
struct StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF;
struct TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB;
struct ARAnchorManager_tF68C46300BD6F2E83E3360BF9297C3C50F5D6B3E;
struct ARAnchorManagerListener_t566E40F47C375A0FCD517AEAF5732FA1E9FB486A;
struct ARCameraManager_t6E957EA922911B8447D086D9FB1F860EC967BD9F;
struct ARCameraManagerListener_t9CDC77A2AAA0062BF970292523A3AD20D76BB3AE;
struct AREnvironmentProbeManager_tE457B73006F0D5136D9F2BDDEB072C1A1DB407F8;
struct AREnvironmentProbeManagerListener_t919D09DA2D7FF7958381E8B7494E6C5A1D6D1533;
struct ARFace_t73C068C9C8422F79A858EC41C322D399305B8453;
struct ARFaceListener_tCC12AA9BE708DFBC6BE058D621A5B173162E8A59;
struct ARFaceManager_tE5955187C9D7C9C4C4139F522DA0DEA69EFCFD8F;
struct ARFaceManagerListener_t8043177537F4EAE01597D66B7EB8968FBAA5811A;
struct ARHumanBodyManager_tA5246205B817A3D4A87BFC54FC018FB513867BFB;
struct ARHumanBodyManagerListener_tD428DC6513EF466039758E70361DF5E6F429589A;
struct ARParticipantManager_t759CB1A6961D7C7F99E47B8AFFF7BCA4761C99C4;
struct ARParticipantManagerListener_tD1B316B50151D4E2F897C85D37E9EA0CE76DD180;
struct ARPlaneManager_t9DA6C512BDCFFBEE8B3DAA7170EB3BFFDE26A928;
struct ARPlaneManagerListener_t48EF97E316472A73C564EB0B7A71B067D0BBCE76;
struct ARPointCloudManager_t28D1833BCE1A48657E504109A89A3B75D0AD7D9F;
struct ARPointCloudManagerListener_t79362431A1598431A74D9AB30FF9CAC2C7D6FB40;
struct ARTrackedImageManager_tCBA77F0B8A7172CC6508CA29176EAE1DE5DC4BD4;
struct ARTrackedImageManagerListener_t41A4A505BF375F0C883703891EB4C03EE271F4E1;
struct ARTrackedObjectManager_tD667E7CCD95E9D25449BD578F256F728D8619FC7;
struct ARTrackedObjectManagerListener_t67F1931020F75B64E0AEF23B15F033285A6D3A7A;
struct Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07;
struct AnchorsChangedEventUnit_t1CDE435C1ABCADB9DEB2117F2E8C2943E274045F;
struct ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263;
struct ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F;
struct Binder_t91BFCE95A7057FADF4D8A1A342AFE52872246235;
struct Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184;
struct CameraFrameReceivedEventUnit_t5C258A06DEEF9DBF3CE6AD4FD0B51313F1DEF858;
struct CancellationTokenSource_tAAE1E0033BCFC233801F8CB4CED5C852B350CB7B;
struct Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3;
struct ControlInput_t1AD434958538C865F55A62C66B9C24DA82AD31FE;
struct ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9;
struct DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E;
struct EnvironmentProbesChangedEventUnit_t9F93FC318A1011F92882E76B8284810D2B98ABE3;
struct Exception_t;
struct FaceUpdatedEventUnit_tBAF617E9D7CA035FC4D40FDA435F6BC35A39D426;
struct FacesChangedEventUnit_tBA9644CD496CD26CC75569DEC718E5E845635BAC;
struct Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582;
struct FlowGraph_tF9E42FECF535CD5BAB10F75672B5BCE285627A45;
struct GameObject_t76FEDD663AB33C991A9C9A23129337651094216F;
struct GetAnchorsUnit_tFD7107C908473E8F1558046830DD41C686447CEE;
struct GetEnvironmentProbesUnit_tD69DF71B014B1CA82754C08D6A7233473D6E6C8C;
struct GetFacesUnit_t58BA2C20DC7900EE832A781F627CB24B4C73F541;
struct GetHumanBodiesUnit_t4DB36755728EF8A8E329590C254060A3A1604B65;
struct GetParticipantsUnit_t573F6B1CED19A0DD19FA4BDF729BDACCCDBEF062;
struct GetPlanesUnit_t0D6951EA13B7C5AB100439F6E16959FF7F229D80;
struct GetPointCloudsUnit_tE8EBA3C1C2C0250EBA3D742FC6D2EDEAE6100AB3;
struct GetRaycastsUnit_t9B59D89C15171736A9685CADE27468DF2787A886;
struct GetTrackedImagesUnit_tA4BCED5E1DC9EFA11B9BE049594D085A71B44239;
struct GetTrackedObjectsUnit_t06C3877B12306C2ED474293DD059EEB537795E42;
struct GraphStack_tD9049F5FB5F8D9E9CD80764D18067D0F87343E7C;
struct HumanBodiesChangedEventUnit_t7E27E223B038B0174E95448F4286BD590A46D54F;
struct IDictionary_t6D03155AF1FA9083817AA5B6AD7DEEACC26AB220;
struct IEnumerator_t7B609C2FFA6EB5167D9C62A0C32A21DE2F666DAA;
struct IGraphRoot_t84A87B1D402F522D84DF0B9D1FBCA3FBDEEFCC81;
struct IUnit_tDD8B7D0F06EA3DB55DEBE4DF3CC85F4A63FBB98E;
struct IUnitValuePort_t066179230A251E8FFC4D9091EE2D9B4B95F649A9;
struct InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB;
struct MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553;
struct MessageListener_t50FA00662CCB169F065100293E953B3024E0BCAE;
struct MethodInfo_t;
struct MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71;
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C;
struct ParticipantsChangedEventUnit_tCB5A32ECD288DCDFDC4AC059E40E7552B424D1F8;
struct PlanesChangedEventUnit_t2FE36340891DD55C64AD4A68C956533AFDB7D116;
struct PointCloudsChangedEventUnit_t70FDC749BF87C070761B2EEC7F80045BCBC542E9;
struct Regex_tE773142C2BE45C5D362B0F815AFF831707A51772;
struct SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6;
struct SessionStateChangedEventUnit_tB622F7C1B352062C2D5D67A3EA1E1E6DBD910631;
struct SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8;
struct String_t;
struct StringBuilder_t;
struct Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700;
struct TrackedImagesChangedEventUnit_tEDE47E3521D6A7321912CFFCB5C10500CD949D29;
struct TrackedObjectsChangedEventUnit_t8EA5435947DF665B0FC12920629B4E6F1A787CF2;
struct Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1;
struct Type_t;
struct Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191;
struct UnitySourceGeneratedAssemblyMonoScriptTypes_v1_t8D822E9B7C7E0474CF4A1283070CD2A45E6A5C71;
struct ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392;
struct ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B;
struct VariableDeclarations_t7701E0B99E3AED5CDC693ABFBDD6D1C73945B405;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;
struct XRAnchorSubsystem_tB8B108EFBD93D4AFD53DCE7BD599009F79D214AF;
struct XRCameraSubsystem_t1F15E4BAEF03223F63AB57E49D1B5DE06749D140;
struct XREnvironmentProbeSubsystem_t1C339A3A0B8601507D2438158D6632271557CC0B;
struct XRFaceSubsystem_t93DD9DDD21C06E5A591BB0094512BC8FD8A890D3;
struct XRHumanBodySubsystem_tF7BE6EF91D4B632389B2F818E516F94213281526;
struct XRImageTrackingSubsystem_t5EEEA714F4273F686F836517EE4957AEDFFD7BEB;
struct XRObjectTrackingSubsystem_t0094478B41F73E26F73943C09ECD134874B68ED5;
struct XROrigin_t5E534A6D886991AF3C1C64127E19A3A510009CFE;
struct XRParticipantSubsystem_tA44C77BC9562FCF6714E6B69322B2CF7F552AF92;
struct XRPlaneSubsystem_t6FC18D5B1FF10EE9B4A751977B526F44A16EDB62;
struct XRPointCloudSubsystem_t997D1C035B61DD47C2235712FF0679F716850708;
struct XRReferenceImageLibrary_t039233790C16E76ED95AABD95D9A35014081EA42;
struct XRReferenceObjectLibrary_t035F7FE264C24BC98A58CA2F24D047BB99BC8487;

IL2CPP_EXTERN_C RuntimeClass* ARCameraFrameEventArgs_t4060608C37C2B7248AFC908876309CC36D53E0EC_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ARSessionState_tAAADC649886D67F7134A0D9A665953D764A115D7_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Action_1_t009680BF19017ECA60753C7F605CBD85C56C6560_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Action_1_t053CCF2809F9B0B61EA8298154533C5FACB724F0_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Action_1_t0A16CC637B74FE43A20D2784BCA3172E7D13789C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Action_1_t11340E5174173030E076A75316E18A395082F8A5_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Action_1_t30DB722576CDAD6BB3F09BD60358BF49AA947548_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Action_1_t325C602D999E964DB72210E493635E83F8D6CE68_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Action_1_t34AAB95B35DF66496184F8421A8AC5F317B7D8AB_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Action_1_t54327BAFA15E24E002EFAE48D1AFFC020D368735_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Action_1_t78DC7DD8CB74A1B21E4419996B784C6E51821247_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Action_1_tE3E0B091088607877804833E23BFDBBF5594B830_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Action_1_tE5F1AC110E5F6610E8B460B753F57ACC9B4524E3_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Action_1_tE7B3D1070B99EFDFA92BD4DBEF95BF881DEB591F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* EventBus_t6457FCB216134244ECE28921407E893BC1D38056_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Func_2_tE299C631F0CB2E8E61444D749D446C27349C90B8_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* GetTrackablesUnit_6_t016BB0C74695D0122C8B6B3599A71F89D32BCF77_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* GetTrackablesUnit_6_t35B2538FE2BE005439F84D8E3DD2098F607A881B_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* GetTrackablesUnit_6_t3DE4D256458C27210703957091C3684A86E12198_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* GetTrackablesUnit_6_t5FAD854E9B61FD3E7409D18C4DED57BC373BACE7_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* GetTrackablesUnit_6_t60E470A25938E95AA9CD62B873EA6C36FC34D2FF_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* GetTrackablesUnit_6_tA1B41E03CB6324ED319781B3AA151D349922B24A_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* GetTrackablesUnit_6_tB48912D7C44FC073BE0CCC0B19CCC4A33E066B77_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* GetTrackablesUnit_6_tBEEA64178AC6F947BE154CE4D41420AED64DC0AE_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* GetTrackablesUnit_6_tCE90D2E00FA14B5E936843082D488242C7463304_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* GetTrackablesUnit_6_tE3FF81AB62ABB3500474D301EF59B13BF4A1685E_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* SessionStateChangedEventUnit_tB622F7C1B352062C2D5D67A3EA1E1E6DBD910631_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* StringBuilder_t_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Type_t_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3E_tDDCC949E3B043757E8B2BF5CEA983FB5D64C65A5____06B4533828E721283278BAD55C65B14F692386B2BF93E99FAA017FD993F5D9B3_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3E_tDDCC949E3B043757E8B2BF5CEA983FB5D64C65A5____4C23045B4C42C8C05E9FC1DE66F40E8CBE11D66FC46CEDF30183BEAF834C5B0B_FieldInfo_var;
IL2CPP_EXTERN_C String_t* _stringLiteral0AEA2C46AAEB233C6D2DF0275EF92FB2E56BAFE9;
IL2CPP_EXTERN_C String_t* _stringLiteral0C2C7A9E7436DA4E0013C1BB4B0A6143CE0F38ED;
IL2CPP_EXTERN_C String_t* _stringLiteral0EAF6D0EBB42EC97177547351019DB3DC3130C07;
IL2CPP_EXTERN_C String_t* _stringLiteral105B4D37F778AEF4041E8AB742473DC80494D94B;
IL2CPP_EXTERN_C String_t* _stringLiteral1B610611D3BE45C25E5133155F9D83AF5F3832ED;
IL2CPP_EXTERN_C String_t* _stringLiteral1E79F5F1B507D82BF4BE0B3BC9BE5CCC5A0A1C69;
IL2CPP_EXTERN_C String_t* _stringLiteral22841DF93BF106671B9C411695F73A63FA8C4A0A;
IL2CPP_EXTERN_C String_t* _stringLiteral241F175A1A7F5FA16826BDB6ABABA5CAF688AF00;
IL2CPP_EXTERN_C String_t* _stringLiteral296249B54F612A320F18BA11A9DB197EFE4BB84A;
IL2CPP_EXTERN_C String_t* _stringLiteral2CFD1985F0B0EF58F786512B364283578445FAC5;
IL2CPP_EXTERN_C String_t* _stringLiteral2E09EF05EED3B12F6A56A89C2BC22D0E77DC5C9E;
IL2CPP_EXTERN_C String_t* _stringLiteral32EE50C3CB7212B57D1B34DC4D1E5D299F118C16;
IL2CPP_EXTERN_C String_t* _stringLiteral3366B219987D5EE59C199D05D8DDF5CACB099288;
IL2CPP_EXTERN_C String_t* _stringLiteral37D6BA2FAF7F2FCCC8DD58518A3048E66432C10B;
IL2CPP_EXTERN_C String_t* _stringLiteral43452BBEA0638F454FF0D37B4FD3C2DEBA3C06FF;
IL2CPP_EXTERN_C String_t* _stringLiteral472B198FF9B13CC3CEB5817E644011EF36F83EF9;
IL2CPP_EXTERN_C String_t* _stringLiteral699D09C7B53816C9B0927B8BD5FA1AE027F1C3C1;
IL2CPP_EXTERN_C String_t* _stringLiteral6C60DEBFC7F1B80F535AA96A50E69DC5071AE93D;
IL2CPP_EXTERN_C String_t* _stringLiteral6E449B10A312A589CEC8243A12BBA89445ED50DE;
IL2CPP_EXTERN_C String_t* _stringLiteral74A7350AADB2E77B2FEC504172F6A7570C2061B4;
IL2CPP_EXTERN_C String_t* _stringLiteral7B82EC15E841121B1695710D448FAF4971871672;
IL2CPP_EXTERN_C String_t* _stringLiteral7DC363E2C257BFAA3463F6A31F8F519A7D6E18F3;
IL2CPP_EXTERN_C String_t* _stringLiteral873B4196DB10C0F07B11703CABABB6C8664BDBF4;
IL2CPP_EXTERN_C String_t* _stringLiteral88C714A35938E8B6F0BAFDF2B7AD9DBCB055A63D;
IL2CPP_EXTERN_C String_t* _stringLiteral8A8FE283442D993E502B69A3252F69EFFAE40786;
IL2CPP_EXTERN_C String_t* _stringLiteralA0AC1B410D80766301FC82938739173E5BCB79A1;
IL2CPP_EXTERN_C String_t* _stringLiteralA783A5CEF05EBCA80DF7AA6E558B8F8514ED24EC;
IL2CPP_EXTERN_C String_t* _stringLiteralA8CFAE0A1486E64AE5F73A446DEF27C6B7E9B54F;
IL2CPP_EXTERN_C String_t* _stringLiteralB724CA1414F1B6A0513232FC2A7137B8A9657B4D;
IL2CPP_EXTERN_C String_t* _stringLiteralBC7D1F049771153528B6E1B4730B5DD939FF7F47;
IL2CPP_EXTERN_C String_t* _stringLiteralDDE2D0A68406372F193061A2914ACA00C88A93D3;
IL2CPP_EXTERN_C String_t* _stringLiteralE44178F8CBCB7518AC61AD2FF65D32E8798D89A3;
IL2CPP_EXTERN_C String_t* _stringLiteralE6FB67187B5B59D4A24904A7962B4BDD32C3A56E;
IL2CPP_EXTERN_C String_t* _stringLiteralE9AD4157A19CF89606B880798B83375D3DB7E6C1;
IL2CPP_EXTERN_C String_t* _stringLiteralF3E84B722399601AD7E281754E917478AA9AD48D;
IL2CPP_EXTERN_C String_t* _stringLiteralF78A6EBA0E7E34457C9FFEC3652864FEDD473081;
IL2CPP_EXTERN_C const RuntimeMethod* ARAnchorManagerListener_OnTrackablesChanged_m4334EFEB7FF640C2029A8567B7F33FEA2F6012BF_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ARCameraManagerListener_OnEnable_m3FCCA525327452EC89BEB0B6192E785675354BBD_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ARCameraManagerListener_OnFrameReceived_mC4DF9EC830D8147C5E4604BBE32DD5D289C7E901_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AREnvironmentProbeManagerListener_OnTrackablesChanged_mFFE922C02832EEF6AC8F645762C471B3BEF8B93D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ARFaceListener_OnEnable_mFB7CEACFC165E077636A31D574F41CAEFB2737A1_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ARFaceListener_OnFaceUpdated_mEB4873976DAAC601F816A95E0E36C93CC6C43D51_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ARFaceManagerListener_OnTrackablesChanged_m5F42EC3570B5D34BAE69E833677980E334D97E84_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ARHumanBodyManagerListener_OnTrackablesChanged_mC8B24E64F660D8C4F29C2567E233795C7905E8D8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ARParticipantManagerListener_OnTrackablesChanged_mFBD305D70AFFBB9ADDE54456B499CEE28B7586F0_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ARPlaneManagerListener_OnTrackablesChanged_mB2C63B0FEA0C321CE64E042C4CC659F3858F7406_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ARPointCloudManagerListener_OnTrackablesChanged_m37F4B0894E58642522DD88A5B70B2D6425D171F2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ARTrackedImageManagerListener_OnTrackablesChanged_mB7342F42759F03B7495C3863A66339D21A50B229_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ARTrackedObjectManagerListener_OnTrackablesChanged_m63FB84E742A86FFC4D163A0B17B8989491A51EE6_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Component_GetComponent_TisARCameraManager_t6E957EA922911B8447D086D9FB1F860EC967BD9F_mD0C05CBA43EFDC472C6C4BB588C366709A88B182_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Component_GetComponent_TisARFace_t73C068C9C8422F79A858EC41C322D399305B8453_mDBDD6B87463FFFEC97691FFEDD402BE1F8D1BC07_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* EventBus_Trigger_TisARAnchorsChangedEventArgs_t755E6208C84304CAC448C133B0A8660CD5750A75_mE9B6D9EEA77C3264949C5BA781F1278767B8D982_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* EventBus_Trigger_TisARCameraFrameEventArgs_t4060608C37C2B7248AFC908876309CC36D53E0EC_m147D0F4E1AEA08F04B42A886DD7FD396ABE2448B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* EventBus_Trigger_TisAREnvironmentProbesChangedEvent_t8757E98E11F3D37F0CFC4589FCA5CD2107F8375C_mF3EF7B9EF8367C19AE598C0BB4C5C819E5E9DDA3_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* EventBus_Trigger_TisARFaceUpdatedEventArgs_tC20D17255858678A1A8230EA32D7C38287221FF6_m36EBB182855F50B6EA52073A9C5843D3FE22B837_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* EventBus_Trigger_TisARFacesChangedEventArgs_tC05D4423CB2096FF5955C3A0A00056EE2B6301AE_mF3D39EAC80939C5697B3EE63307DD1AFB338914D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* EventBus_Trigger_TisARHumanBodiesChangedEventArgs_t19088E0D3384A22C5D8A542957705222C834720B_m053372DF7FDC0D251BE56C17199E478393C48C13_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* EventBus_Trigger_TisARParticipantsChangedEventArgs_t2EAD9041A074A7ACEFF280662784747A4E85E527_m1AFA7138412723D9A851AE84D192CF53B2FD2CEF_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* EventBus_Trigger_TisARPlanesChangedEventArgs_t8D63E0257BF9942EF8F8C0445F2FD46421017872_m95B4A178E0E6B6E98EE3F2CCBA22EF47977C9BCB_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* EventBus_Trigger_TisARPointCloudChangedEventArgs_t7DE4CA4D30D369496221061F89CC4BF84E42CC1B_mE0DDE4D49AE7A3FB982B04E07E2475F9D93977DE_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* EventBus_Trigger_TisARSessionState_tAAADC649886D67F7134A0D9A665953D764A115D7_mE75403E46DB2EDDF96E1BF00CAB17E2C12C9BE79_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* EventBus_Trigger_TisARTrackedImagesChangedEventArgs_tD7718E40EE25D22E9143DA4F556C44980E18BE2F_m543E103E3042925BB06EAC45E637282A71E1F853_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* EventBus_Trigger_TisARTrackedObjectsChangedEventArgs_t378A63B91AD22EA4BA9C68805C72DDA4B7C01509_m2D44CB18B7DE92A8A28C2336767883E782C70CD8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* EventUnit_1_Definition_m4F542628DD546D8F60E22C3A609D7E4D9A980F8D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* EventUnit_1_StartListening_m988584D9E3245AAED2407FA0A7C0700E827FCAA8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* EventUnit_1_StopListening_m3291AC7B5A0B2228C69FF95A85772282389B48A6_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GameObjectEventUnit_1_Definition_m993A9F33D4AB3731B9BF811F804CA9BA0E75096E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GameObjectEventUnit_1_Definition_mF8BEB75340AEF0E43AD3F4BC254109BD339F4E09_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GameObjectEventUnit_1__ctor_m12AD4CC830381744782178A62EDD22E8BF924894_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GameObjectEventUnit_1__ctor_mBA789D715816EDBF30E72C14CE07C03030B2FFD2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GetTrackablesUnit_6__ctor_m42135B0FC7FB4509F6F5630CB0F1A9B235D0C4AF_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GetTrackablesUnit_6__ctor_m4A8A971CAFBC181DC2C00076B9A07CA14F9C2830_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GetTrackablesUnit_6__ctor_m80FFBEC9D57E6A6AC206595E20191765D636E72D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GetTrackablesUnit_6__ctor_m8F8058DE37A8268EABD8551E5CBB014EC6A1FC39_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GetTrackablesUnit_6__ctor_m95F5607653302ED365908C5FD4E47E8F6C046A35_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GetTrackablesUnit_6__ctor_m993C351689850B54D4D415D6555E6963296402E6_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GetTrackablesUnit_6__ctor_m9BC4C628CE4264EFA7586A6658299BF686ADE2CA_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GetTrackablesUnit_6__ctor_mC724CF523FCF0A0CA43758DC60F0E05E4B30EE32_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GetTrackablesUnit_6__ctor_mE4E69DCE572AA63C76CCEBB851DE56E31BD02020_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GetTrackablesUnit_6__ctor_mFE5AAECF852800E5E7743015509249E62860CB62_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GlobalEventUnit_1__ctor_m8D2A36FE71F45881F8560975E9F957C36A08539E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Object_FindObjectOfType_TisARCameraManager_t6E957EA922911B8447D086D9FB1F860EC967BD9F_mF55990D2494651CA4DEEE92470F8B341AFF1A089_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SessionStateChangedEventUnit_HandleStateChange_m59A2A9D8AE5794385BC8A62BFA35EEF1183F4770_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SessionStateSwitchUnit_ProcessFlow_m57E488DEC7E1E2F26E074FB49BB7F19DA905C410_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackableManagerListener_1__ctor_m07B978E212E0DC99C7A64CE3576F87A983D1676A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackableManagerListener_1__ctor_m2E928AC462404314A3931061A1142F70B3236122_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackableManagerListener_1__ctor_m35FD529767C9EB77928CBC0BDC28A26273050D7B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackableManagerListener_1__ctor_m3DE0DE01F5CEAED2E7F5AD649EB4E55205725541_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackableManagerListener_1__ctor_m6D8E3E517736CC5906267C97680E382F66641F03_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackableManagerListener_1__ctor_m9D5A585C61BEE09064B5EB54CC3D5DF04A38A279_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackableManagerListener_1__ctor_mBEE631CBF51F52534707E2938E971864799D3EDA_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackableManagerListener_1__ctor_mC84640FEC1BBC643B3517C43EC073EEA79FC8667_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackableManagerListener_1__ctor_mF10CA320B096183174E077D4F6F5A996C3B43363_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackablesChangedEventUnit_8__ctor_m1F8C079F96721DC237C11DA4A5D51E8B6FF363D6_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackablesChangedEventUnit_8__ctor_m2BD406EFCF14C551D318E6422DE19A60B50B4B0E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackablesChangedEventUnit_8__ctor_m4E7FEDDC8BF5DEFA8C29AC317BD812195B30A839_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackablesChangedEventUnit_8__ctor_m6D98CBE8151864AF5BBBDA138AE0D0E8DAAD995C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackablesChangedEventUnit_8__ctor_m832218ED199936D78FC2DCAE7AFB8F4A557D210C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackablesChangedEventUnit_8__ctor_m894F00CFC3DAF8C67CBB694581F43FB8D07DA5DE_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackablesChangedEventUnit_8__ctor_m924504E4BCE32657BE00FDA2558786DE79FE541C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackablesChangedEventUnit_8__ctor_mA9A06C3435A47571B7483CD98FC6FC1F3938890F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackablesChangedEventUnit_8__ctor_mAA11F3EE9A29DA1A20480633CA21003789A43AC2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackablesChangedEventUnit_8_get_added_m12B5AD020EE9860130BC183B984643D5242089D9_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackablesChangedEventUnit_8_get_added_m69D43E4E5EE721CF125D2EE3539284002A700B74_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackablesChangedEventUnit_8_get_added_m7BC749EABE3673D303FAB3CAA1C3746AE97E71A3_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackablesChangedEventUnit_8_get_added_m98C7465E257E576752368FE41D6911A596B9BD08_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackablesChangedEventUnit_8_get_added_mB2970C7A1E0D78B2D6C4EE5E9DF55E8FB61F0414_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackablesChangedEventUnit_8_get_added_mB590546762CE6A56E4C8B1EB8F24BD6CAA573B17_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackablesChangedEventUnit_8_get_added_mC83DA1B5E5EEB690C2E89C2703CADE9E51A740C7_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackablesChangedEventUnit_8_get_added_mD3E5CD7DD84402EC4A7054A36FF7678FB8A067F0_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackablesChangedEventUnit_8_get_added_mF1D86A14D162BBD5DC42FB80A7C0FA7BC769AF97_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackablesChangedEventUnit_8_get_removed_m00993034CD9C3DEBE2D7B5D8BCEF7D1F4AC6BB76_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackablesChangedEventUnit_8_get_removed_m0519BF012BE3FC785FF687EA5655A30E7B566A67_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackablesChangedEventUnit_8_get_removed_m0AFD1317BBE14E4A88D0D90E190EA056ABD7DE2B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackablesChangedEventUnit_8_get_removed_m1D98456EEB97142B45C4EE15B8443BA8499F97CB_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackablesChangedEventUnit_8_get_removed_m53656F8942539C9E8684FF4379871CFC068B1A19_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackablesChangedEventUnit_8_get_removed_m8F35AD75A58329A64455EA693E3AB62EDAA066C9_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackablesChangedEventUnit_8_get_removed_m91B8BEF101F7958D60725FFFA15EF7D255467B59_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackablesChangedEventUnit_8_get_removed_m9C8736E9632CDA45DADC1A4FB415361148A8A543_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackablesChangedEventUnit_8_get_removed_mA9FEE7FB7606E374551FE70A1B72D8199E237F70_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackablesChangedEventUnit_8_get_updated_m2B11703016B3C1EB9A5003ACC43AA9507300A5B5_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackablesChangedEventUnit_8_get_updated_m2C3941B76B820CCC7161CA63CDCB10D6FBA498F1_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackablesChangedEventUnit_8_get_updated_m4155AE83417E658B126553D3D5BE9E51691A2561_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackablesChangedEventUnit_8_get_updated_m64D143FBFBC17DC316E6C6F700D961D64B9E0AFE_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackablesChangedEventUnit_8_get_updated_m990FD05AD7C047671F91787AFD37A2C9E856E2F7_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackablesChangedEventUnit_8_get_updated_mAE3D67A922C0F1E37CC29792138000F23621BCDD_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackablesChangedEventUnit_8_get_updated_mB0E1D4283CE77BC4F2A15B4BCF2351BB08321517_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackablesChangedEventUnit_8_get_updated_mD57DCBD8B6B61E09CC5E8AE90F6D728B4B99F008_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TrackablesChangedEventUnit_8_get_updated_mDAC4907DBB50BF9C8B7A68F589F2827CB92E054C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Unit_ValueInput_TisARSessionState_tAAADC649886D67F7134A0D9A665953D764A115D7_mBD32544DF033F6AA05190E690F3F77D3420E6CF4_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Unit_ValueOutput_TisARCameraFrameEventArgs_t4060608C37C2B7248AFC908876309CC36D53E0EC_m179CC70EB8D39BBFB29E25C4D83DB538FBF3C6FC_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Unit_ValueOutput_TisARFace_t73C068C9C8422F79A858EC41C322D399305B8453_mC94C38A35E16A3EA152B5E1D430FC6D2419FD31E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Unit_ValueOutput_TisARSessionState_tAAADC649886D67F7134A0D9A665953D764A115D7_m4BE5C8B6E8E7823F32C58B256BA74C4C5956BF1E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeType* ARCameraManagerListener_t9CDC77A2AAA0062BF970292523A3AD20D76BB3AE_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* ARFaceListener_tCC12AA9BE708DFBC6BE058D621A5B173162E8A59_0_0_0_var;
struct Delegate_t_marshaled_com;
struct Delegate_t_marshaled_pinvoke;
struct Exception_t_marshaled_com;
struct Exception_t_marshaled_pinvoke;

struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;

IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_tC3A40CB23800DC97799A5A5EE940F6619E460169 
{
};
struct List_1_t5C2EBF017989E44F3D1D11AF502E10F6F8D9AB53  : public RuntimeObject
{
	ARAnchorU5BU5D_t589CB7678527B30DBFABED0E377BC500C9D3454A* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct List_1_t6F14EE6179A9A8E042C5D65E902F72E24597029B  : public RuntimeObject
{
	AREnvironmentProbeU5BU5D_t172E5E7D75D1738636091C776C8EEC8BAAE3839A* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct List_1_tA11EBED64210DD13BB4303415D948E6CD964AAF9  : public RuntimeObject
{
	ARFaceU5BU5D_t6FC6B0063C2B88AFFF728161A36C9B4402F65711* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct List_1_t64B1B4ABB789350AE017F3EAF4D3647AF59244C7  : public RuntimeObject
{
	ARHumanBodyU5BU5D_t765862C91212D61EA388CB29F8895BC1FDB1EB0D* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct List_1_t87D9AC2D6B4A4860E01575669DEB0D164D4B33B3  : public RuntimeObject
{
	ARParticipantU5BU5D_tE306DE8EAC060B47D21D12030F3E59D78AE4BF9D* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct List_1_t10BA05B555C92BD54800DFF82DCAAFC9DE44A077  : public RuntimeObject
{
	ARPlaneU5BU5D_t8C555584F9BAD4FB2BD4DB6D7933EBBBD61D6FD4* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct List_1_tB9CD639EB4FE5EA0A8B417168BB9BD2D29AEE271  : public RuntimeObject
{
	ARPointCloudU5BU5D_tD64665DDD0344C5780D4BDC332717DA27CEF1CE0* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct List_1_tFA730335FF46512E4245B2F46892CA318B394FAE  : public RuntimeObject
{
	ARTrackedImageU5BU5D_tD227987C766F9B880B8EF0E9ADF5918CDFB72367* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct List_1_tF1201F2F725A8AE529D436F38A4771FB6790F3C6  : public RuntimeObject
{
	ARTrackedObjectU5BU5D_t432458E1432DA3112081C0414632055D92813EDE* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct UnitPort_3_t5D4915F350030D5D193DB8ABE809742B4E0A3816  : public RuntimeObject
{
	RuntimeObject* ___U3CunitU3Ek__BackingField;
	String_t* ___U3CkeyU3Ek__BackingField;
};
struct UnitPort_3_t0F0D7E0DAEC017C7CF07420E3F9BF8EA1BF4CA88  : public RuntimeObject
{
	RuntimeObject* ___U3CunitU3Ek__BackingField;
	String_t* ___U3CkeyU3Ek__BackingField;
};
struct UnitPort_3_t3120713700769DD514D6ED9ACFD105D4B583C09E  : public RuntimeObject
{
	RuntimeObject* ___U3CunitU3Ek__BackingField;
	String_t* ___U3CkeyU3Ek__BackingField;
};
struct UnitPort_3_t3BDB6AF6AAF9927A0C940F08CFD19E645813CFB0  : public RuntimeObject
{
	RuntimeObject* ___U3CunitU3Ek__BackingField;
	String_t* ___U3CkeyU3Ek__BackingField;
};
struct U3CPrivateImplementationDetailsU3E_tDDCC949E3B043757E8B2BF5CEA983FB5D64C65A5  : public RuntimeObject
{
};
struct Constants_t52BA4E19D4E4E7D84188778875CA3F198AB036F1  : public RuntimeObject
{
};
struct Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582  : public RuntimeObject
{
	GraphStack_tD9049F5FB5F8D9E9CD80764D18067D0F87343E7C* ___U3CstackU3Ek__BackingField;
	Recursion_1_tDFF1B549E70648F1694033FADF89F8F0EB28587D* ___recursion;
	Dictionary_2_t3D5C4496A22EAD3D8DD55DCDFBB4F4E9BE8311FF* ___locals;
	VariableDeclarations_t7701E0B99E3AED5CDC693ABFBDD6D1C73945B405* ___variables;
	Stack_1_t3197E0F5EA36E611B259A88751D31FC2396FE4B6* ___loops;
	HashSet_1_t015F1F38EE02675ACF63C72E1E1D832249BB06DA* ___preservedStacks;
	MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71* ___U3CcoroutineRunnerU3Ek__BackingField;
	RuntimeObject* ___activeCoroutinesRegistry;
	bool ___coroutineStopRequested;
	bool ___U3CisCoroutineU3Ek__BackingField;
	RuntimeObject* ___coroutineEnumerator;
	bool ___U3CisPredictionU3Ek__BackingField;
	bool ___disposed;
	int32_t ___loopIdentifier;
};
struct GraphPointer_t01C98F06D1A7635CB867665044B7DC1FE03D1C0B  : public RuntimeObject
{
	RuntimeObject* ___U3CrootU3Ek__BackingField;
	GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___U3CgameObjectU3Ek__BackingField;
	List_1_tF8892BD96CF6B23CD95F24F5381A6E7237540FD1* ___parentStack;
	List_1_t645DF2A1E7B0FB808EDE07ED53B8C56194AF35E6* ___parentElementStack;
	List_1_tE97B7994B98675F18C70DDD8DEB61E4FE5491DA1* ___graphStack;
	List_1_tF1785B2080B8536D64E747DAE8C2DE0D44A75E27* ___dataStack;
	List_1_t1715D0D9950CCCC39A77E9597CA69B346CD7E3E5* ___debugDataStack;
};
struct MemberInfo_t  : public RuntimeObject
{
};
struct String_t  : public RuntimeObject
{
	int32_t ____stringLength;
	Il2CppChar ____firstChar;
};
struct StringBuilder_t  : public RuntimeObject
{
	CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB* ___m_ChunkChars;
	StringBuilder_t* ___m_ChunkPrevious;
	int32_t ___m_ChunkLength;
	int32_t ___m_ChunkOffset;
	int32_t ___m_MaxCapacity;
};
struct UnitySourceGeneratedAssemblyMonoScriptTypes_v1_t8D822E9B7C7E0474CF4A1283070CD2A45E6A5C71  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct EventHookNames_t49BA8F41C143EB4F50618AC00C53C843D4045BC6  : public RuntimeObject
{
};
struct Nullable_1_t6E154519A812D040E3016229CD7638843A2CC165 
{
	bool ___hasValue;
	double ___value;
};
struct Nullable_1_t365991B3904FDA7642A788423B28692FDC7CDB17 
{
	bool ___hasValue;
	int64_t ___value;
};
struct Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75 
{
	bool ___hasValue;
	float ___value;
};
struct ARAnchorsChangedEventArgs_t755E6208C84304CAC448C133B0A8660CD5750A75 
{
	List_1_t5C2EBF017989E44F3D1D11AF502E10F6F8D9AB53* ___U3CaddedU3Ek__BackingField;
	List_1_t5C2EBF017989E44F3D1D11AF502E10F6F8D9AB53* ___U3CupdatedU3Ek__BackingField;
	List_1_t5C2EBF017989E44F3D1D11AF502E10F6F8D9AB53* ___U3CremovedU3Ek__BackingField;
};
struct ARAnchorsChangedEventArgs_t755E6208C84304CAC448C133B0A8660CD5750A75_marshaled_pinvoke
{
	List_1_t5C2EBF017989E44F3D1D11AF502E10F6F8D9AB53* ___U3CaddedU3Ek__BackingField;
	List_1_t5C2EBF017989E44F3D1D11AF502E10F6F8D9AB53* ___U3CupdatedU3Ek__BackingField;
	List_1_t5C2EBF017989E44F3D1D11AF502E10F6F8D9AB53* ___U3CremovedU3Ek__BackingField;
};
struct ARAnchorsChangedEventArgs_t755E6208C84304CAC448C133B0A8660CD5750A75_marshaled_com
{
	List_1_t5C2EBF017989E44F3D1D11AF502E10F6F8D9AB53* ___U3CaddedU3Ek__BackingField;
	List_1_t5C2EBF017989E44F3D1D11AF502E10F6F8D9AB53* ___U3CupdatedU3Ek__BackingField;
	List_1_t5C2EBF017989E44F3D1D11AF502E10F6F8D9AB53* ___U3CremovedU3Ek__BackingField;
};
struct AREnvironmentProbesChangedEvent_t8757E98E11F3D37F0CFC4589FCA5CD2107F8375C 
{
	List_1_t6F14EE6179A9A8E042C5D65E902F72E24597029B* ___U3CaddedU3Ek__BackingField;
	List_1_t6F14EE6179A9A8E042C5D65E902F72E24597029B* ___U3CupdatedU3Ek__BackingField;
	List_1_t6F14EE6179A9A8E042C5D65E902F72E24597029B* ___U3CremovedU3Ek__BackingField;
};
struct AREnvironmentProbesChangedEvent_t8757E98E11F3D37F0CFC4589FCA5CD2107F8375C_marshaled_pinvoke
{
	List_1_t6F14EE6179A9A8E042C5D65E902F72E24597029B* ___U3CaddedU3Ek__BackingField;
	List_1_t6F14EE6179A9A8E042C5D65E902F72E24597029B* ___U3CupdatedU3Ek__BackingField;
	List_1_t6F14EE6179A9A8E042C5D65E902F72E24597029B* ___U3CremovedU3Ek__BackingField;
};
struct AREnvironmentProbesChangedEvent_t8757E98E11F3D37F0CFC4589FCA5CD2107F8375C_marshaled_com
{
	List_1_t6F14EE6179A9A8E042C5D65E902F72E24597029B* ___U3CaddedU3Ek__BackingField;
	List_1_t6F14EE6179A9A8E042C5D65E902F72E24597029B* ___U3CupdatedU3Ek__BackingField;
	List_1_t6F14EE6179A9A8E042C5D65E902F72E24597029B* ___U3CremovedU3Ek__BackingField;
};
struct ARFaceUpdatedEventArgs_tC20D17255858678A1A8230EA32D7C38287221FF6 
{
	ARFace_t73C068C9C8422F79A858EC41C322D399305B8453* ___U3CfaceU3Ek__BackingField;
};
struct ARFaceUpdatedEventArgs_tC20D17255858678A1A8230EA32D7C38287221FF6_marshaled_pinvoke
{
	ARFace_t73C068C9C8422F79A858EC41C322D399305B8453* ___U3CfaceU3Ek__BackingField;
};
struct ARFaceUpdatedEventArgs_tC20D17255858678A1A8230EA32D7C38287221FF6_marshaled_com
{
	ARFace_t73C068C9C8422F79A858EC41C322D399305B8453* ___U3CfaceU3Ek__BackingField;
};
struct ARFacesChangedEventArgs_tC05D4423CB2096FF5955C3A0A00056EE2B6301AE 
{
	List_1_tA11EBED64210DD13BB4303415D948E6CD964AAF9* ___U3CaddedU3Ek__BackingField;
	List_1_tA11EBED64210DD13BB4303415D948E6CD964AAF9* ___U3CupdatedU3Ek__BackingField;
	List_1_tA11EBED64210DD13BB4303415D948E6CD964AAF9* ___U3CremovedU3Ek__BackingField;
};
struct ARFacesChangedEventArgs_tC05D4423CB2096FF5955C3A0A00056EE2B6301AE_marshaled_pinvoke
{
	List_1_tA11EBED64210DD13BB4303415D948E6CD964AAF9* ___U3CaddedU3Ek__BackingField;
	List_1_tA11EBED64210DD13BB4303415D948E6CD964AAF9* ___U3CupdatedU3Ek__BackingField;
	List_1_tA11EBED64210DD13BB4303415D948E6CD964AAF9* ___U3CremovedU3Ek__BackingField;
};
struct ARFacesChangedEventArgs_tC05D4423CB2096FF5955C3A0A00056EE2B6301AE_marshaled_com
{
	List_1_tA11EBED64210DD13BB4303415D948E6CD964AAF9* ___U3CaddedU3Ek__BackingField;
	List_1_tA11EBED64210DD13BB4303415D948E6CD964AAF9* ___U3CupdatedU3Ek__BackingField;
	List_1_tA11EBED64210DD13BB4303415D948E6CD964AAF9* ___U3CremovedU3Ek__BackingField;
};
struct ARHumanBodiesChangedEventArgs_t19088E0D3384A22C5D8A542957705222C834720B 
{
	List_1_t64B1B4ABB789350AE017F3EAF4D3647AF59244C7* ___U3CaddedU3Ek__BackingField;
	List_1_t64B1B4ABB789350AE017F3EAF4D3647AF59244C7* ___U3CupdatedU3Ek__BackingField;
	List_1_t64B1B4ABB789350AE017F3EAF4D3647AF59244C7* ___U3CremovedU3Ek__BackingField;
};
struct ARHumanBodiesChangedEventArgs_t19088E0D3384A22C5D8A542957705222C834720B_marshaled_pinvoke
{
	List_1_t64B1B4ABB789350AE017F3EAF4D3647AF59244C7* ___U3CaddedU3Ek__BackingField;
	List_1_t64B1B4ABB789350AE017F3EAF4D3647AF59244C7* ___U3CupdatedU3Ek__BackingField;
	List_1_t64B1B4ABB789350AE017F3EAF4D3647AF59244C7* ___U3CremovedU3Ek__BackingField;
};
struct ARHumanBodiesChangedEventArgs_t19088E0D3384A22C5D8A542957705222C834720B_marshaled_com
{
	List_1_t64B1B4ABB789350AE017F3EAF4D3647AF59244C7* ___U3CaddedU3Ek__BackingField;
	List_1_t64B1B4ABB789350AE017F3EAF4D3647AF59244C7* ___U3CupdatedU3Ek__BackingField;
	List_1_t64B1B4ABB789350AE017F3EAF4D3647AF59244C7* ___U3CremovedU3Ek__BackingField;
};
struct ARParticipantsChangedEventArgs_t2EAD9041A074A7ACEFF280662784747A4E85E527 
{
	List_1_t87D9AC2D6B4A4860E01575669DEB0D164D4B33B3* ___U3CaddedU3Ek__BackingField;
	List_1_t87D9AC2D6B4A4860E01575669DEB0D164D4B33B3* ___U3CupdatedU3Ek__BackingField;
	List_1_t87D9AC2D6B4A4860E01575669DEB0D164D4B33B3* ___U3CremovedU3Ek__BackingField;
};
struct ARParticipantsChangedEventArgs_t2EAD9041A074A7ACEFF280662784747A4E85E527_marshaled_pinvoke
{
	List_1_t87D9AC2D6B4A4860E01575669DEB0D164D4B33B3* ___U3CaddedU3Ek__BackingField;
	List_1_t87D9AC2D6B4A4860E01575669DEB0D164D4B33B3* ___U3CupdatedU3Ek__BackingField;
	List_1_t87D9AC2D6B4A4860E01575669DEB0D164D4B33B3* ___U3CremovedU3Ek__BackingField;
};
struct ARParticipantsChangedEventArgs_t2EAD9041A074A7ACEFF280662784747A4E85E527_marshaled_com
{
	List_1_t87D9AC2D6B4A4860E01575669DEB0D164D4B33B3* ___U3CaddedU3Ek__BackingField;
	List_1_t87D9AC2D6B4A4860E01575669DEB0D164D4B33B3* ___U3CupdatedU3Ek__BackingField;
	List_1_t87D9AC2D6B4A4860E01575669DEB0D164D4B33B3* ___U3CremovedU3Ek__BackingField;
};
struct ARPlanesChangedEventArgs_t8D63E0257BF9942EF8F8C0445F2FD46421017872 
{
	List_1_t10BA05B555C92BD54800DFF82DCAAFC9DE44A077* ___U3CaddedU3Ek__BackingField;
	List_1_t10BA05B555C92BD54800DFF82DCAAFC9DE44A077* ___U3CupdatedU3Ek__BackingField;
	List_1_t10BA05B555C92BD54800DFF82DCAAFC9DE44A077* ___U3CremovedU3Ek__BackingField;
};
struct ARPlanesChangedEventArgs_t8D63E0257BF9942EF8F8C0445F2FD46421017872_marshaled_pinvoke
{
	List_1_t10BA05B555C92BD54800DFF82DCAAFC9DE44A077* ___U3CaddedU3Ek__BackingField;
	List_1_t10BA05B555C92BD54800DFF82DCAAFC9DE44A077* ___U3CupdatedU3Ek__BackingField;
	List_1_t10BA05B555C92BD54800DFF82DCAAFC9DE44A077* ___U3CremovedU3Ek__BackingField;
};
struct ARPlanesChangedEventArgs_t8D63E0257BF9942EF8F8C0445F2FD46421017872_marshaled_com
{
	List_1_t10BA05B555C92BD54800DFF82DCAAFC9DE44A077* ___U3CaddedU3Ek__BackingField;
	List_1_t10BA05B555C92BD54800DFF82DCAAFC9DE44A077* ___U3CupdatedU3Ek__BackingField;
	List_1_t10BA05B555C92BD54800DFF82DCAAFC9DE44A077* ___U3CremovedU3Ek__BackingField;
};
struct ARPointCloudChangedEventArgs_t7DE4CA4D30D369496221061F89CC4BF84E42CC1B 
{
	List_1_tB9CD639EB4FE5EA0A8B417168BB9BD2D29AEE271* ___U3CaddedU3Ek__BackingField;
	List_1_tB9CD639EB4FE5EA0A8B417168BB9BD2D29AEE271* ___U3CupdatedU3Ek__BackingField;
	List_1_tB9CD639EB4FE5EA0A8B417168BB9BD2D29AEE271* ___U3CremovedU3Ek__BackingField;
};
struct ARPointCloudChangedEventArgs_t7DE4CA4D30D369496221061F89CC4BF84E42CC1B_marshaled_pinvoke
{
	List_1_tB9CD639EB4FE5EA0A8B417168BB9BD2D29AEE271* ___U3CaddedU3Ek__BackingField;
	List_1_tB9CD639EB4FE5EA0A8B417168BB9BD2D29AEE271* ___U3CupdatedU3Ek__BackingField;
	List_1_tB9CD639EB4FE5EA0A8B417168BB9BD2D29AEE271* ___U3CremovedU3Ek__BackingField;
};
struct ARPointCloudChangedEventArgs_t7DE4CA4D30D369496221061F89CC4BF84E42CC1B_marshaled_com
{
	List_1_tB9CD639EB4FE5EA0A8B417168BB9BD2D29AEE271* ___U3CaddedU3Ek__BackingField;
	List_1_tB9CD639EB4FE5EA0A8B417168BB9BD2D29AEE271* ___U3CupdatedU3Ek__BackingField;
	List_1_tB9CD639EB4FE5EA0A8B417168BB9BD2D29AEE271* ___U3CremovedU3Ek__BackingField;
};
struct ARTrackedImagesChangedEventArgs_tD7718E40EE25D22E9143DA4F556C44980E18BE2F 
{
	List_1_tFA730335FF46512E4245B2F46892CA318B394FAE* ___U3CaddedU3Ek__BackingField;
	List_1_tFA730335FF46512E4245B2F46892CA318B394FAE* ___U3CupdatedU3Ek__BackingField;
	List_1_tFA730335FF46512E4245B2F46892CA318B394FAE* ___U3CremovedU3Ek__BackingField;
};
struct ARTrackedImagesChangedEventArgs_tD7718E40EE25D22E9143DA4F556C44980E18BE2F_marshaled_pinvoke
{
	List_1_tFA730335FF46512E4245B2F46892CA318B394FAE* ___U3CaddedU3Ek__BackingField;
	List_1_tFA730335FF46512E4245B2F46892CA318B394FAE* ___U3CupdatedU3Ek__BackingField;
	List_1_tFA730335FF46512E4245B2F46892CA318B394FAE* ___U3CremovedU3Ek__BackingField;
};
struct ARTrackedImagesChangedEventArgs_tD7718E40EE25D22E9143DA4F556C44980E18BE2F_marshaled_com
{
	List_1_tFA730335FF46512E4245B2F46892CA318B394FAE* ___U3CaddedU3Ek__BackingField;
	List_1_tFA730335FF46512E4245B2F46892CA318B394FAE* ___U3CupdatedU3Ek__BackingField;
	List_1_tFA730335FF46512E4245B2F46892CA318B394FAE* ___U3CremovedU3Ek__BackingField;
};
struct ARTrackedObjectsChangedEventArgs_t378A63B91AD22EA4BA9C68805C72DDA4B7C01509 
{
	List_1_tF1201F2F725A8AE529D436F38A4771FB6790F3C6* ___U3CaddedU3Ek__BackingField;
	List_1_tF1201F2F725A8AE529D436F38A4771FB6790F3C6* ___U3CupdatedU3Ek__BackingField;
	List_1_tF1201F2F725A8AE529D436F38A4771FB6790F3C6* ___U3CremovedU3Ek__BackingField;
};
struct ARTrackedObjectsChangedEventArgs_t378A63B91AD22EA4BA9C68805C72DDA4B7C01509_marshaled_pinvoke
{
	List_1_tF1201F2F725A8AE529D436F38A4771FB6790F3C6* ___U3CaddedU3Ek__BackingField;
	List_1_tF1201F2F725A8AE529D436F38A4771FB6790F3C6* ___U3CupdatedU3Ek__BackingField;
	List_1_tF1201F2F725A8AE529D436F38A4771FB6790F3C6* ___U3CremovedU3Ek__BackingField;
};
struct ARTrackedObjectsChangedEventArgs_t378A63B91AD22EA4BA9C68805C72DDA4B7C01509_marshaled_com
{
	List_1_tF1201F2F725A8AE529D436F38A4771FB6790F3C6* ___U3CaddedU3Ek__BackingField;
	List_1_tF1201F2F725A8AE529D436F38A4771FB6790F3C6* ___U3CupdatedU3Ek__BackingField;
	List_1_tF1201F2F725A8AE529D436F38A4771FB6790F3C6* ___U3CremovedU3Ek__BackingField;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3 
{
	uint8_t ___m_value;
};
struct Color_tD001788D726C3A7F1379BEED0260B9591F440C1F 
{
	float ___r;
	float ___g;
	float ___b;
	float ___a;
};
struct ControlInput_t1AD434958538C865F55A62C66B9C24DA82AD31FE  : public UnitPort_3_t0F0D7E0DAEC017C7CF07420E3F9BF8EA1BF4CA88
{
	Func_2_tE299C631F0CB2E8E61444D749D446C27349C90B8* ___action;
	Func_2_tFB5F2454526EA057FC21D7028A8B1EADDF2F531F* ___coroutineAction;
};
struct ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9  : public UnitPort_3_t5D4915F350030D5D193DB8ABE809742B4E0A3816
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};
struct EventHook_t1C9E2570B55940C342214BACB5E9EC889A113157 
{
	String_t* ___name;
	RuntimeObject* ___target;
	RuntimeObject* ___tag;
};
struct EventHook_t1C9E2570B55940C342214BACB5E9EC889A113157_marshaled_pinvoke
{
	char* ___name;
	Il2CppIUnknown* ___target;
	Il2CppIUnknown* ___tag;
};
struct EventHook_t1C9E2570B55940C342214BACB5E9EC889A113157_marshaled_com
{
	Il2CppChar* ___name;
	Il2CppIUnknown* ___target;
	Il2CppIUnknown* ___tag;
};
struct GraphStack_tD9049F5FB5F8D9E9CD80764D18067D0F87343E7C  : public GraphPointer_t01C98F06D1A7635CB867665044B7DC1FE03D1C0B
{
};
struct Guid_t 
{
	int32_t ____a;
	int16_t ____b;
	int16_t ____c;
	uint8_t ____d;
	uint8_t ____e;
	uint8_t ____f;
	uint8_t ____g;
	uint8_t ____h;
	uint8_t ____i;
	uint8_t ____j;
	uint8_t ____k;
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 
{
	float ___m00;
	float ___m10;
	float ___m20;
	float ___m30;
	float ___m01;
	float ___m11;
	float ___m21;
	float ___m31;
	float ___m02;
	float ___m12;
	float ___m22;
	float ___m32;
	float ___m03;
	float ___m13;
	float ___m23;
	float ___m33;
};
struct Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 
{
	float ___x;
	float ___y;
	float ___z;
	float ___w;
};
struct SphericalHarmonicsL2_tCBFB646455D2069E738976E5B745C6DF023B6BA2 
{
	float ___shr0;
	float ___shr1;
	float ___shr2;
	float ___shr3;
	float ___shr4;
	float ___shr5;
	float ___shr6;
	float ___shr7;
	float ___shr8;
	float ___shg0;
	float ___shg1;
	float ___shg2;
	float ___shg3;
	float ___shg4;
	float ___shg5;
	float ___shg6;
	float ___shg7;
	float ___shg8;
	float ___shb0;
	float ___shb1;
	float ___shb2;
	float ___shb3;
	float ___shb4;
	float ___shb5;
	float ___shb6;
	float ___shb7;
	float ___shb8;
};
struct TrackableId_t49EAE8AA4B9584E314518723DC22B66496D47AD7 
{
	uint64_t ___m_SubId1;
	uint64_t ___m_SubId2;
};
struct ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392  : public UnitPort_3_t3BDB6AF6AAF9927A0C940F08CFD19E645813CFB0
{
	Type_t* ___U3CtypeU3Ek__BackingField;
	bool ___U3CnullMeansSelfU3Ek__BackingField;
	bool ___U3CallowsNullU3Ek__BackingField;
};
struct ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B  : public UnitPort_3_t3120713700769DD514D6ED9ACFD105D4B583C09E
{
	Func_2_tB98BB93B60A3562C54694B9F37AEBD38DE206A9C* ___getValue;
	Func_2_tCF3029BCCA647C1B54318B688881E1742A230E8C* ___canPredictValue;
	Type_t* ___U3CtypeU3Ek__BackingField;
};
struct Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 
{
	float ___x;
	float ___y;
};
struct Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 
{
	float ___x;
	float ___y;
	float ___z;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D2799_t352C93FCC50B5CED699C9CB47B7281796C108FC6 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D2799_t352C93FCC50B5CED699C9CB47B7281796C108FC6__padding[2799];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D4990_t7BE706357E559B310B67297701C273970742B891 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D4990_t7BE706357E559B310B67297701C273970742B891__padding[4990];
	};
};
#pragma pack(pop, tp)
struct MonoScriptData_t536A062DC987E9D49DDC48415C579F63ADB98E92 
{
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___FilePathsData;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___TypesData;
	int32_t ___TotalTypes;
	int32_t ___TotalFiles;
	bool ___IsEditorOnly;
};
struct MonoScriptData_t536A062DC987E9D49DDC48415C579F63ADB98E92_marshaled_pinvoke
{
	Il2CppSafeArray* ___FilePathsData;
	Il2CppSafeArray* ___TypesData;
	int32_t ___TotalTypes;
	int32_t ___TotalFiles;
	int32_t ___IsEditorOnly;
};
struct MonoScriptData_t536A062DC987E9D49DDC48415C579F63ADB98E92_marshaled_com
{
	Il2CppSafeArray* ___FilePathsData;
	Il2CppSafeArray* ___TypesData;
	int32_t ___TotalTypes;
	int32_t ___TotalFiles;
	int32_t ___IsEditorOnly;
};
struct GraphElement_1_t3220EDD6251B31FC22A192EC12682C1D8B28F16D  : public RuntimeObject
{
	Guid_t ___U3CguidU3Ek__BackingField;
	FlowGraph_tF9E42FECF535CD5BAB10F75672B5BCE285627A45* ___U3CgraphU3Ek__BackingField;
};
struct Nullable_1_tEE83D90B507D40B6C58B5EEF5B9D44D377B44F11 
{
	bool ___hasValue;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___value;
};
struct Nullable_1_t4DE2D2A1D2B73B95E813201AC08E1041435836AA 
{
	bool ___hasValue;
	Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 ___value;
};
struct Nullable_1_t4C9CB95ACA9906A61C759884064F60858BF7E798 
{
	bool ___hasValue;
	SphericalHarmonicsL2_tCBFB646455D2069E738976E5B745C6DF023B6BA2 ___value;
};
struct Nullable_1_t9C51B084784B716FFF4ED4575C63CFD8A71A86FE 
{
	bool ___hasValue;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___value;
};
struct ARSessionState_tAAADC649886D67F7134A0D9A665953D764A115D7 
{
	int32_t ___value__;
};
struct Allocator_t996642592271AAD9EE688F142741D512C07B5824 
{
	int32_t ___value__;
};
struct CameraBackgroundRenderingMode_tDE02C27B25910C5E4D8A8F98213818250A961379 
{
	uint8_t ___value__;
};
struct CameraFacingDirection_t905450A3655BD064DF73761018AB558D4FDA1247 
{
	int32_t ___value__;
};
struct CameraFocusMode_t36A1BEB1F122D191F22FF6AB596CF8240EAE9668 
{
	int32_t ___value__;
};
struct Delegate_t  : public RuntimeObject
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	RuntimeObject* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	bool ___method_is_virtual;
};
struct Delegate_t_marshaled_pinvoke
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct Delegate_t_marshaled_com
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct Exception_t  : public RuntimeObject
{
	String_t* ____className;
	String_t* ____message;
	RuntimeObject* ____data;
	Exception_t* ____innerException;
	String_t* ____helpURL;
	RuntimeObject* ____stackTrace;
	String_t* ____stackTraceString;
	String_t* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	RuntimeObject* ____dynamicMethods;
	int32_t ____HResult;
	String_t* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_pinvoke
{
	char* ____className;
	char* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_pinvoke* ____innerException;
	char* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	char* ____stackTraceString;
	char* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	char* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_com
{
	Il2CppChar* ____className;
	Il2CppChar* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_com* ____innerException;
	Il2CppChar* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	Il2CppChar* ____stackTraceString;
	Il2CppChar* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	Il2CppChar* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct FilterMode_t4AD57F1A3FE272D650E0E688BA044AE872BD2A34 
{
	int32_t ___value__;
};
struct Int32Enum_tCBAC8BA2BFF3A845FA599F303093BBBA374B6F0C 
{
	int32_t ___value__;
};
struct LightEstimation_tF3690F58E54F4FB0A4759BB0A694E887C0052683 
{
	int32_t ___value__;
};
struct LightEstimationMode_t9B424276A66D6FD146A6B15E9C17004869E784A8 
{
	int32_t ___value__;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C  : public RuntimeObject
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_pinvoke
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_com
{
	intptr_t ___m_CachedPtr;
};
struct PlaneDetectionMode_tF0BBEB04D5F881F040E20E6394970EF382273017 
{
	int32_t ___value__;
};
struct Pose_t06BA69EAA6E9FAF60056D519A87D25F54AFE7971 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___position;
	Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ___rotation;
};
struct RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 
{
	intptr_t ___value;
};
struct RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B 
{
	intptr_t ___value;
};
struct TextureDimension_t8D7148B9168256EE1E9AF91378ABA148888CE642 
{
	int32_t ___value__;
};
struct TextureFormat_t87A73E4A3850D3410DC211676FC14B94226C1C1D 
{
	int32_t ___value__;
};
struct TrackingState_t5487241B6888BB15D8448EBBC640835A011DBBC2 
{
	int32_t ___value__;
};
struct XRCameraFrameExifDataColorSpace_t9C2207D83FA98C81BAEFA7F16ECE867D3DF7B3D9 
{
	uint16_t ___value__;
};
struct XRCameraFrameExifDataMeteringMode_t4AB0A7E36A1E4066BF5513511C6442CADCDD30AD 
{
	uint16_t ___value__;
};
struct XRCameraFrameExifDataProperties_tDA208B6E62EA777B3AB86C21BE9FBECEBEFB5C87 
{
	int32_t ___value__;
};
struct ARFoundationFeatureOrder_t0E327C01FC339A2B3C19551803910C1F007EAC01 
{
	int32_t ___value__;
};
struct NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C 
{
	void* ___m_Buffer;
	int32_t ___m_Length;
	int32_t ___m_AllocatorLabel;
};
struct NativeArray_1_t0BB246A2F65C2C705F83BEBE1B62D9543C330B70 
{
	void* ___m_Buffer;
	int32_t ___m_Length;
	int32_t ___m_AllocatorLabel;
};
struct NativeArray_1_t97E2BFD61E13EEF2CDE34A313415FAD03AB993FD 
{
	void* ___m_Buffer;
	int32_t ___m_Length;
	int32_t ___m_AllocatorLabel;
};
struct ARLightEstimationData_tE622C47E05EBA84E05326E72C9D258F0200D5CE7 
{
	Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75 ___U3CaverageColorTemperatureU3Ek__BackingField;
	Nullable_1_tEE83D90B507D40B6C58B5EEF5B9D44D377B44F11 ___U3CcolorCorrectionU3Ek__BackingField;
	Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75 ___U3CmainLightIntensityLumensU3Ek__BackingField;
	Nullable_1_tEE83D90B507D40B6C58B5EEF5B9D44D377B44F11 ___U3CmainLightColorU3Ek__BackingField;
	Nullable_1_t9C51B084784B716FFF4ED4575C63CFD8A71A86FE ___U3CmainLightDirectionU3Ek__BackingField;
	Nullable_1_t4C9CB95ACA9906A61C759884064F60858BF7E798 ___U3CambientSphericalHarmonicsU3Ek__BackingField;
	Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75 ___m_AverageBrightness;
	Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75 ___m_AverageIntensityInLumens;
	Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75 ___m_MainLightBrightness;
};
struct ARLightEstimationData_tE622C47E05EBA84E05326E72C9D258F0200D5CE7_marshaled_pinvoke
{
	Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75 ___U3CaverageColorTemperatureU3Ek__BackingField;
	Nullable_1_tEE83D90B507D40B6C58B5EEF5B9D44D377B44F11 ___U3CcolorCorrectionU3Ek__BackingField;
	Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75 ___U3CmainLightIntensityLumensU3Ek__BackingField;
	Nullable_1_tEE83D90B507D40B6C58B5EEF5B9D44D377B44F11 ___U3CmainLightColorU3Ek__BackingField;
	Nullable_1_t9C51B084784B716FFF4ED4575C63CFD8A71A86FE ___U3CmainLightDirectionU3Ek__BackingField;
	Nullable_1_t4C9CB95ACA9906A61C759884064F60858BF7E798 ___U3CambientSphericalHarmonicsU3Ek__BackingField;
	Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75 ___m_AverageBrightness;
	Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75 ___m_AverageIntensityInLumens;
	Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75 ___m_MainLightBrightness;
};
struct ARLightEstimationData_tE622C47E05EBA84E05326E72C9D258F0200D5CE7_marshaled_com
{
	Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75 ___U3CaverageColorTemperatureU3Ek__BackingField;
	Nullable_1_tEE83D90B507D40B6C58B5EEF5B9D44D377B44F11 ___U3CcolorCorrectionU3Ek__BackingField;
	Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75 ___U3CmainLightIntensityLumensU3Ek__BackingField;
	Nullable_1_tEE83D90B507D40B6C58B5EEF5B9D44D377B44F11 ___U3CmainLightColorU3Ek__BackingField;
	Nullable_1_t9C51B084784B716FFF4ED4575C63CFD8A71A86FE ___U3CmainLightDirectionU3Ek__BackingField;
	Nullable_1_t4C9CB95ACA9906A61C759884064F60858BF7E798 ___U3CambientSphericalHarmonicsU3Ek__BackingField;
	Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75 ___m_AverageBrightness;
	Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75 ___m_AverageIntensityInLumens;
	Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75 ___m_MainLightBrightness;
};
struct ARSessionStateChangedEventArgs_tC31BD123D6B4F6168FB246762E79AB33962AE822 
{
	int32_t ___U3CstateU3Ek__BackingField;
};
struct Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct GameObject_t76FEDD663AB33C991A9C9A23129337651094216F  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct MulticastDelegate_t  : public Delegate_t
{
	DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771* ___delegates;
};
struct MulticastDelegate_t_marshaled_pinvoke : public Delegate_t_marshaled_pinvoke
{
	Delegate_t_marshaled_pinvoke** ___delegates;
};
struct MulticastDelegate_t_marshaled_com : public Delegate_t_marshaled_com
{
	Delegate_t_marshaled_com** ___delegates;
};
struct SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295  : public Exception_t
{
};
struct Type_t  : public MemberInfo_t
{
	RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B ____impl;
};
struct Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191  : public GraphElement_1_t3220EDD6251B31FC22A192EC12682C1D8B28F16D
{
	bool ___U3CisDefinedU3Ek__BackingField;
	RuntimeObject* ___U3CcontrolInputsU3Ek__BackingField;
	RuntimeObject* ___U3CcontrolOutputsU3Ek__BackingField;
	RuntimeObject* ___U3CvalueInputsU3Ek__BackingField;
	RuntimeObject* ___U3CvalueOutputsU3Ek__BackingField;
	RuntimeObject* ___U3CinvalidInputsU3Ek__BackingField;
	RuntimeObject* ___U3CinvalidOutputsU3Ek__BackingField;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___onPortsChanged;
	Dictionary_2_tA348003A3C1CEFB3096E9D2A0BC7F1AC8EC4F710* ___U3CdefaultValuesU3Ek__BackingField;
	RuntimeObject* ___U3CrelationsU3Ek__BackingField;
	bool ___U3CisControlRootU3Ek__BackingField;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___U3CpositionU3Ek__BackingField;
	Exception_t* ___U3CdefinitionExceptionU3Ek__BackingField;
};
struct XRCameraFrameExifData_tCECD90020A07DE4EC65B94AEF60F0E1C5EC0E31E 
{
	intptr_t ___m_NativePtr;
	double ___m_ApertureValue;
	double ___m_BrightnessValue;
	double ___m_ExposureTime;
	double ___m_ShutterSpeedValue;
	float ___m_ExposureBiasValue;
	float ___m_FNumber;
	float ___m_FocalLength;
	int16_t ___m_Flash;
	uint16_t ___m_ColorSpace;
	int16_t ___m_PhotographicSensitivity;
	uint16_t ___m_MeteringMode;
	int32_t ___m_Properties;
};
struct XRFace_t235F951F4AC7CC6A0CA6829CECA05210D64982FB 
{
	TrackableId_t49EAE8AA4B9584E314518723DC22B66496D47AD7 ___m_TrackableId;
	Pose_t06BA69EAA6E9FAF60056D519A87D25F54AFE7971 ___m_Pose;
	int32_t ___m_TrackingState;
	intptr_t ___m_NativePtr;
	Pose_t06BA69EAA6E9FAF60056D519A87D25F54AFE7971 ___m_LeftEyePose;
	Pose_t06BA69EAA6E9FAF60056D519A87D25F54AFE7971 ___m_RightEyePose;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_FixationPoint;
};
struct XRTextureDescriptor_t699023EDE6E2593F61CE969A68B5E56CD04CFA19 
{
	intptr_t ___m_NativeTexture;
	int32_t ___m_Width;
	int32_t ___m_Height;
	int32_t ___m_MipmapCount;
	int32_t ___m_Format;
	int32_t ___m_PropertyNameId;
	int32_t ___m_Depth;
	int32_t ___m_Dimension;
};
struct Action_1_t11340E5174173030E076A75316E18A395082F8A5  : public MulticastDelegate_t
{
};
struct Action_1_tE3E0B091088607877804833E23BFDBBF5594B830  : public MulticastDelegate_t
{
};
struct Action_1_t30DB722576CDAD6BB3F09BD60358BF49AA947548  : public MulticastDelegate_t
{
};
struct Action_1_t0A16CC637B74FE43A20D2784BCA3172E7D13789C  : public MulticastDelegate_t
{
};
struct Action_1_t78DC7DD8CB74A1B21E4419996B784C6E51821247  : public MulticastDelegate_t
{
};
struct Action_1_tE5F1AC110E5F6610E8B460B753F57ACC9B4524E3  : public MulticastDelegate_t
{
};
struct Action_1_t009680BF19017ECA60753C7F605CBD85C56C6560  : public MulticastDelegate_t
{
};
struct Action_1_t053CCF2809F9B0B61EA8298154533C5FACB724F0  : public MulticastDelegate_t
{
};
struct Action_1_t325C602D999E964DB72210E493635E83F8D6CE68  : public MulticastDelegate_t
{
};
struct Action_1_tE7B3D1070B99EFDFA92BD4DBEF95BF881DEB591F  : public MulticastDelegate_t
{
};
struct Action_1_t34AAB95B35DF66496184F8421A8AC5F317B7D8AB  : public MulticastDelegate_t
{
};
struct EventUnit_1_t282F18C855DA005F8D4D93D0475EE6E664F4D19B  : public Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191
{
	bool ___U3CcoroutineU3Ek__BackingField;
	ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___U3CtriggerU3Ek__BackingField;
};
struct EventUnit_1_t069E228EE60F4EA704304B00C24B9A033AD82F5C  : public Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191
{
	bool ___U3CcoroutineU3Ek__BackingField;
	ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___U3CtriggerU3Ek__BackingField;
};
struct EventUnit_1_t573A173418C4998E4D7AABFD88584D6B5C8FBD3B  : public Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191
{
	bool ___U3CcoroutineU3Ek__BackingField;
	ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___U3CtriggerU3Ek__BackingField;
};
struct EventUnit_1_tE2CD7FC7E567410E45AFF31FDD31ACD83A38F60C  : public Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191
{
	bool ___U3CcoroutineU3Ek__BackingField;
	ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___U3CtriggerU3Ek__BackingField;
};
struct EventUnit_1_t95C8F22EA15EC244AA6BBB177BBC61DA8C0D3D8C  : public Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191
{
	bool ___U3CcoroutineU3Ek__BackingField;
	ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___U3CtriggerU3Ek__BackingField;
};
struct EventUnit_1_tFA0A811831250D20F368521BB27A4B414AE2E91B  : public Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191
{
	bool ___U3CcoroutineU3Ek__BackingField;
	ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___U3CtriggerU3Ek__BackingField;
};
struct EventUnit_1_tAD80D0C4FFED37E805222863D96B7EDF5C974B7A  : public Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191
{
	bool ___U3CcoroutineU3Ek__BackingField;
	ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___U3CtriggerU3Ek__BackingField;
};
struct EventUnit_1_t7A199A307AE8162B9C229B62F7CD26502C3D7BB7  : public Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191
{
	bool ___U3CcoroutineU3Ek__BackingField;
	ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___U3CtriggerU3Ek__BackingField;
};
struct EventUnit_1_tAFFBADE3ED61A5389D9F0DBCB7A880A44074F5E4  : public Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191
{
	bool ___U3CcoroutineU3Ek__BackingField;
	ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___U3CtriggerU3Ek__BackingField;
};
struct EventUnit_1_tA2B0E6295E58DB46B38F944529B0DF5E40ABF917  : public Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191
{
	bool ___U3CcoroutineU3Ek__BackingField;
	ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___U3CtriggerU3Ek__BackingField;
};
struct EventUnit_1_t59D2C69DB24A3610C3EFF77E019780AD1F395CFC  : public Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191
{
	bool ___U3CcoroutineU3Ek__BackingField;
	ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___U3CtriggerU3Ek__BackingField;
};
struct EventUnit_1_tD9858C82481E4A429A5E951A194D7D8BE9363DED  : public Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191
{
	bool ___U3CcoroutineU3Ek__BackingField;
	ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___U3CtriggerU3Ek__BackingField;
};
struct Func_2_tE299C631F0CB2E8E61444D749D446C27349C90B8  : public MulticastDelegate_t
{
};
struct GetTrackablesUnit_6_tE3FF81AB62ABB3500474D301EF59B13BF4A1685E  : public Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191
{
	StringBuilder_t* ___m_LogBuilder;
	ControlInput_t1AD434958538C865F55A62C66B9C24DA82AD31FE* ___U3CflowInU3Ek__BackingField;
	ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___U3CflowOutU3Ek__BackingField;
	ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* ___U3CmanagerInU3Ek__BackingField;
	ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* ___U3CtrackablesInU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CtrackablesOutU3Ek__BackingField;
};
struct GetTrackablesUnit_6_tCE90D2E00FA14B5E936843082D488242C7463304  : public Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191
{
	StringBuilder_t* ___m_LogBuilder;
	ControlInput_t1AD434958538C865F55A62C66B9C24DA82AD31FE* ___U3CflowInU3Ek__BackingField;
	ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___U3CflowOutU3Ek__BackingField;
	ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* ___U3CmanagerInU3Ek__BackingField;
	ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* ___U3CtrackablesInU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CtrackablesOutU3Ek__BackingField;
};
struct GetTrackablesUnit_6_tB48912D7C44FC073BE0CCC0B19CCC4A33E066B77  : public Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191
{
	StringBuilder_t* ___m_LogBuilder;
	ControlInput_t1AD434958538C865F55A62C66B9C24DA82AD31FE* ___U3CflowInU3Ek__BackingField;
	ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___U3CflowOutU3Ek__BackingField;
	ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* ___U3CmanagerInU3Ek__BackingField;
	ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* ___U3CtrackablesInU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CtrackablesOutU3Ek__BackingField;
};
struct GetTrackablesUnit_6_t3DE4D256458C27210703957091C3684A86E12198  : public Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191
{
	StringBuilder_t* ___m_LogBuilder;
	ControlInput_t1AD434958538C865F55A62C66B9C24DA82AD31FE* ___U3CflowInU3Ek__BackingField;
	ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___U3CflowOutU3Ek__BackingField;
	ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* ___U3CmanagerInU3Ek__BackingField;
	ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* ___U3CtrackablesInU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CtrackablesOutU3Ek__BackingField;
};
struct GetTrackablesUnit_6_t35B2538FE2BE005439F84D8E3DD2098F607A881B  : public Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191
{
	StringBuilder_t* ___m_LogBuilder;
	ControlInput_t1AD434958538C865F55A62C66B9C24DA82AD31FE* ___U3CflowInU3Ek__BackingField;
	ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___U3CflowOutU3Ek__BackingField;
	ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* ___U3CmanagerInU3Ek__BackingField;
	ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* ___U3CtrackablesInU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CtrackablesOutU3Ek__BackingField;
};
struct GetTrackablesUnit_6_t016BB0C74695D0122C8B6B3599A71F89D32BCF77  : public Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191
{
	StringBuilder_t* ___m_LogBuilder;
	ControlInput_t1AD434958538C865F55A62C66B9C24DA82AD31FE* ___U3CflowInU3Ek__BackingField;
	ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___U3CflowOutU3Ek__BackingField;
	ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* ___U3CmanagerInU3Ek__BackingField;
	ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* ___U3CtrackablesInU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CtrackablesOutU3Ek__BackingField;
};
struct GetTrackablesUnit_6_tBEEA64178AC6F947BE154CE4D41420AED64DC0AE  : public Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191
{
	StringBuilder_t* ___m_LogBuilder;
	ControlInput_t1AD434958538C865F55A62C66B9C24DA82AD31FE* ___U3CflowInU3Ek__BackingField;
	ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___U3CflowOutU3Ek__BackingField;
	ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* ___U3CmanagerInU3Ek__BackingField;
	ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* ___U3CtrackablesInU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CtrackablesOutU3Ek__BackingField;
};
struct GetTrackablesUnit_6_tA1B41E03CB6324ED319781B3AA151D349922B24A  : public Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191
{
	StringBuilder_t* ___m_LogBuilder;
	ControlInput_t1AD434958538C865F55A62C66B9C24DA82AD31FE* ___U3CflowInU3Ek__BackingField;
	ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___U3CflowOutU3Ek__BackingField;
	ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* ___U3CmanagerInU3Ek__BackingField;
	ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* ___U3CtrackablesInU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CtrackablesOutU3Ek__BackingField;
};
struct GetTrackablesUnit_6_t5FAD854E9B61FD3E7409D18C4DED57BC373BACE7  : public Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191
{
	StringBuilder_t* ___m_LogBuilder;
	ControlInput_t1AD434958538C865F55A62C66B9C24DA82AD31FE* ___U3CflowInU3Ek__BackingField;
	ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___U3CflowOutU3Ek__BackingField;
	ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* ___U3CmanagerInU3Ek__BackingField;
	ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* ___U3CtrackablesInU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CtrackablesOutU3Ek__BackingField;
};
struct GetTrackablesUnit_6_t60E470A25938E95AA9CD62B873EA6C36FC34D2FF  : public Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191
{
	StringBuilder_t* ___m_LogBuilder;
	ControlInput_t1AD434958538C865F55A62C66B9C24DA82AD31FE* ___U3CflowInU3Ek__BackingField;
	ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___U3CflowOutU3Ek__BackingField;
	ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* ___U3CmanagerInU3Ek__BackingField;
	ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* ___U3CtrackablesInU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CtrackablesOutU3Ek__BackingField;
};
struct ARCameraFrameEventArgs_t4060608C37C2B7248AFC908876309CC36D53E0EC 
{
	ARLightEstimationData_tE622C47E05EBA84E05326E72C9D258F0200D5CE7 ___U3ClightEstimationU3Ek__BackingField;
	Nullable_1_t365991B3904FDA7642A788423B28692FDC7CDB17 ___U3CtimestampNsU3Ek__BackingField;
	Nullable_1_t4DE2D2A1D2B73B95E813201AC08E1041435836AA ___U3CprojectionMatrixU3Ek__BackingField;
	Nullable_1_t4DE2D2A1D2B73B95E813201AC08E1041435836AA ___U3CdisplayMatrixU3Ek__BackingField;
	List_1_t0F231C3F13EBA1FF9081BD61489D01AA3CBE59D4* ___U3CtexturesU3Ek__BackingField;
	List_1_t05915E9237850A58106982B7FE4BC5DA4E872E73* ___U3CpropertyNameIdsU3Ek__BackingField;
	Nullable_1_t6E154519A812D040E3016229CD7638843A2CC165 ___U3CexposureDurationU3Ek__BackingField;
	Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75 ___U3CexposureOffsetU3Ek__BackingField;
	List_1_tF470A3BE5C1B5B68E1325EF3F109D172E60BD7CD* ___U3CenabledMaterialKeywordsU3Ek__BackingField;
	List_1_tF470A3BE5C1B5B68E1325EF3F109D172E60BD7CD* ___U3CdisabledMaterialKeywordsU3Ek__BackingField;
	Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700* ___U3CcameraGrainTextureU3Ek__BackingField;
	float ___U3CnoiseIntensityU3Ek__BackingField;
	XRCameraFrameExifData_tCECD90020A07DE4EC65B94AEF60F0E1C5EC0E31E ___U3CexifDataU3Ek__BackingField;
};
struct ARCameraFrameEventArgs_t4060608C37C2B7248AFC908876309CC36D53E0EC_marshaled_pinvoke
{
	ARLightEstimationData_tE622C47E05EBA84E05326E72C9D258F0200D5CE7_marshaled_pinvoke ___U3ClightEstimationU3Ek__BackingField;
	Nullable_1_t365991B3904FDA7642A788423B28692FDC7CDB17 ___U3CtimestampNsU3Ek__BackingField;
	Nullable_1_t4DE2D2A1D2B73B95E813201AC08E1041435836AA ___U3CprojectionMatrixU3Ek__BackingField;
	Nullable_1_t4DE2D2A1D2B73B95E813201AC08E1041435836AA ___U3CdisplayMatrixU3Ek__BackingField;
	List_1_t0F231C3F13EBA1FF9081BD61489D01AA3CBE59D4* ___U3CtexturesU3Ek__BackingField;
	List_1_t05915E9237850A58106982B7FE4BC5DA4E872E73* ___U3CpropertyNameIdsU3Ek__BackingField;
	Nullable_1_t6E154519A812D040E3016229CD7638843A2CC165 ___U3CexposureDurationU3Ek__BackingField;
	Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75 ___U3CexposureOffsetU3Ek__BackingField;
	List_1_tF470A3BE5C1B5B68E1325EF3F109D172E60BD7CD* ___U3CenabledMaterialKeywordsU3Ek__BackingField;
	List_1_tF470A3BE5C1B5B68E1325EF3F109D172E60BD7CD* ___U3CdisabledMaterialKeywordsU3Ek__BackingField;
	Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700* ___U3CcameraGrainTextureU3Ek__BackingField;
	float ___U3CnoiseIntensityU3Ek__BackingField;
	XRCameraFrameExifData_tCECD90020A07DE4EC65B94AEF60F0E1C5EC0E31E ___U3CexifDataU3Ek__BackingField;
};
struct ARCameraFrameEventArgs_t4060608C37C2B7248AFC908876309CC36D53E0EC_marshaled_com
{
	ARLightEstimationData_tE622C47E05EBA84E05326E72C9D258F0200D5CE7_marshaled_com ___U3ClightEstimationU3Ek__BackingField;
	Nullable_1_t365991B3904FDA7642A788423B28692FDC7CDB17 ___U3CtimestampNsU3Ek__BackingField;
	Nullable_1_t4DE2D2A1D2B73B95E813201AC08E1041435836AA ___U3CprojectionMatrixU3Ek__BackingField;
	Nullable_1_t4DE2D2A1D2B73B95E813201AC08E1041435836AA ___U3CdisplayMatrixU3Ek__BackingField;
	List_1_t0F231C3F13EBA1FF9081BD61489D01AA3CBE59D4* ___U3CtexturesU3Ek__BackingField;
	List_1_t05915E9237850A58106982B7FE4BC5DA4E872E73* ___U3CpropertyNameIdsU3Ek__BackingField;
	Nullable_1_t6E154519A812D040E3016229CD7638843A2CC165 ___U3CexposureDurationU3Ek__BackingField;
	Nullable_1_t3D746CBB6123D4569FF4DEA60BC4240F32C6FE75 ___U3CexposureOffsetU3Ek__BackingField;
	List_1_tF470A3BE5C1B5B68E1325EF3F109D172E60BD7CD* ___U3CenabledMaterialKeywordsU3Ek__BackingField;
	List_1_tF470A3BE5C1B5B68E1325EF3F109D172E60BD7CD* ___U3CdisabledMaterialKeywordsU3Ek__BackingField;
	Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700* ___U3CcameraGrainTextureU3Ek__BackingField;
	float ___U3CnoiseIntensityU3Ek__BackingField;
	XRCameraFrameExifData_tCECD90020A07DE4EC65B94AEF60F0E1C5EC0E31E ___U3CexifDataU3Ek__BackingField;
};
struct ARTextureInfo_t008DA41DF9555306803303A0353619E5C0917261 
{
	XRTextureDescriptor_t699023EDE6E2593F61CE969A68B5E56CD04CFA19 ___m_Descriptor;
	Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700* ___m_Texture;
};
struct ARTextureInfo_t008DA41DF9555306803303A0353619E5C0917261_marshaled_pinvoke
{
	XRTextureDescriptor_t699023EDE6E2593F61CE969A68B5E56CD04CFA19 ___m_Descriptor;
	Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700* ___m_Texture;
};
struct ARTextureInfo_t008DA41DF9555306803303A0353619E5C0917261_marshaled_com
{
	XRTextureDescriptor_t699023EDE6E2593F61CE969A68B5E56CD04CFA19 ___m_Descriptor;
	Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700* ___m_Texture;
};
struct ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263  : public SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295
{
	String_t* ____paramName;
};
struct Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB  : public SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295
{
};
struct SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8  : public Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191
{
	ControlInput_t1AD434958538C865F55A62C66B9C24DA82AD31FE* ___U3CflowInU3Ek__BackingField;
	ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* ___U3CsessionStateInU3Ek__BackingField;
	ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___U3CnoneU3Ek__BackingField;
	ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___U3CunsupportedU3Ek__BackingField;
	ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___U3CcheckingAvailabilityU3Ek__BackingField;
	ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___U3CneedsInstallU3Ek__BackingField;
	ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___U3CinstallingU3Ek__BackingField;
	ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___U3CreadyU3Ek__BackingField;
	ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___U3CsessionInitializingU3Ek__BackingField;
	ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___U3CsessionTrackingU3Ek__BackingField;
};
struct XRFaceMesh_t6B92499E6ECFF17A9BDCC6E531A79395795E2CD4 
{
	NativeArray_1_t97E2BFD61E13EEF2CDE34A313415FAD03AB993FD ___m_Vertices;
	NativeArray_1_t97E2BFD61E13EEF2CDE34A313415FAD03AB993FD ___m_Normals;
	NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C ___m_Indices;
	NativeArray_1_t0BB246A2F65C2C705F83BEBE1B62D9543C330B70 ___m_UVs;
};
struct Action_1_t54327BAFA15E24E002EFAE48D1AFFC020D368735  : public MulticastDelegate_t
{
};
struct GameObjectEventUnit_1_tB6AFC5E0ED153703A6CE1D8703D0D25E26F9C849  : public EventUnit_1_t282F18C855DA005F8D4D93D0475EE6E664F4D19B
{
	ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* ___U3CtargetU3Ek__BackingField;
};
struct GameObjectEventUnit_1_tA12F0B3D1C478378C11E341089186ED62766AF93  : public EventUnit_1_t069E228EE60F4EA704304B00C24B9A033AD82F5C
{
	ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* ___U3CtargetU3Ek__BackingField;
};
struct GameObjectEventUnit_1_tDEFD0C21681E7E0136494B6DDEA5660EA577C156  : public EventUnit_1_t573A173418C4998E4D7AABFD88584D6B5C8FBD3B
{
	ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* ___U3CtargetU3Ek__BackingField;
};
struct GameObjectEventUnit_1_tD98F9076C76336C1A30A327C151C4BC55CBE45FE  : public EventUnit_1_tE2CD7FC7E567410E45AFF31FDD31ACD83A38F60C
{
	ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* ___U3CtargetU3Ek__BackingField;
};
struct GameObjectEventUnit_1_t063EB218BF6F0191E1F03A814D03E055F9D28E59  : public EventUnit_1_t95C8F22EA15EC244AA6BBB177BBC61DA8C0D3D8C
{
	ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* ___U3CtargetU3Ek__BackingField;
};
struct GameObjectEventUnit_1_t78B78E27AF85CD35BAB92827457A8C2A0721BAB6  : public EventUnit_1_tFA0A811831250D20F368521BB27A4B414AE2E91B
{
	ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* ___U3CtargetU3Ek__BackingField;
};
struct GameObjectEventUnit_1_tA120BADA678284B678A6802440CC08218C9738DA  : public EventUnit_1_tAD80D0C4FFED37E805222863D96B7EDF5C974B7A
{
	ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* ___U3CtargetU3Ek__BackingField;
};
struct GameObjectEventUnit_1_t7CA8CF819390A4D9F3AE951985DBC8833C25B677  : public EventUnit_1_t7A199A307AE8162B9C229B62F7CD26502C3D7BB7
{
	ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* ___U3CtargetU3Ek__BackingField;
};
struct GameObjectEventUnit_1_t3A40F0C72F56365F8071C101519523537BA61372  : public EventUnit_1_tAFFBADE3ED61A5389D9F0DBCB7A880A44074F5E4
{
	ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* ___U3CtargetU3Ek__BackingField;
};
struct GameObjectEventUnit_1_tC7D76058D223CF6B79EE59E069217305CE18A13E  : public EventUnit_1_t59D2C69DB24A3610C3EFF77E019780AD1F395CFC
{
	ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* ___U3CtargetU3Ek__BackingField;
};
struct GameObjectEventUnit_1_t7779539F7C75323975424FAD14786741E0970AAF  : public EventUnit_1_tD9858C82481E4A429A5E951A194D7D8BE9363DED
{
	ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* ___U3CtargetU3Ek__BackingField;
};
struct GlobalEventUnit_1_tFCC2A75C905819911151CA0BD2FEE0E3A24D5FE6  : public EventUnit_1_tA2B0E6295E58DB46B38F944529B0DF5E40ABF917
{
};
struct ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F  : public ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263
{
	RuntimeObject* ____actualValue;
};
struct GetAnchorsUnit_tFD7107C908473E8F1558046830DD41C686447CEE  : public GetTrackablesUnit_6_tE3FF81AB62ABB3500474D301EF59B13BF4A1685E
{
};
struct GetEnvironmentProbesUnit_tD69DF71B014B1CA82754C08D6A7233473D6E6C8C  : public GetTrackablesUnit_6_tCE90D2E00FA14B5E936843082D488242C7463304
{
};
struct GetFacesUnit_t58BA2C20DC7900EE832A781F627CB24B4C73F541  : public GetTrackablesUnit_6_tB48912D7C44FC073BE0CCC0B19CCC4A33E066B77
{
};
struct GetHumanBodiesUnit_t4DB36755728EF8A8E329590C254060A3A1604B65  : public GetTrackablesUnit_6_t3DE4D256458C27210703957091C3684A86E12198
{
};
struct GetParticipantsUnit_t573F6B1CED19A0DD19FA4BDF729BDACCCDBEF062  : public GetTrackablesUnit_6_t35B2538FE2BE005439F84D8E3DD2098F607A881B
{
};
struct GetPlanesUnit_t0D6951EA13B7C5AB100439F6E16959FF7F229D80  : public GetTrackablesUnit_6_t016BB0C74695D0122C8B6B3599A71F89D32BCF77
{
};
struct GetPointCloudsUnit_tE8EBA3C1C2C0250EBA3D742FC6D2EDEAE6100AB3  : public GetTrackablesUnit_6_tBEEA64178AC6F947BE154CE4D41420AED64DC0AE
{
};
struct GetRaycastsUnit_t9B59D89C15171736A9685CADE27468DF2787A886  : public GetTrackablesUnit_6_tA1B41E03CB6324ED319781B3AA151D349922B24A
{
};
struct GetTrackedImagesUnit_tA4BCED5E1DC9EFA11B9BE049594D085A71B44239  : public GetTrackablesUnit_6_t5FAD854E9B61FD3E7409D18C4DED57BC373BACE7
{
};
struct GetTrackedObjectsUnit_t06C3877B12306C2ED474293DD059EEB537795E42  : public GetTrackablesUnit_6_t60E470A25938E95AA9CD62B873EA6C36FC34D2FF
{
};
struct MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71  : public Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA
{
	CancellationTokenSource_tAAE1E0033BCFC233801F8CB4CED5C852B350CB7B* ___m_CancellationTokenSource;
};
struct SubsystemLifecycleManager_3_tAE0BB0092EB47B81AA111C370381E3BA14C88DD3  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	XRAnchorSubsystem_tB8B108EFBD93D4AFD53DCE7BD599009F79D214AF* ___U3CsubsystemU3Ek__BackingField;
};
struct SubsystemLifecycleManager_3_t2810F2EBBCBEAEE830830E20CF311EBAD8EA983B  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	XRCameraSubsystem_t1F15E4BAEF03223F63AB57E49D1B5DE06749D140* ___U3CsubsystemU3Ek__BackingField;
};
struct SubsystemLifecycleManager_3_tFA3968C6C041E64DA6CDC221BF400F2F0EFCB9C4  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	XREnvironmentProbeSubsystem_t1C339A3A0B8601507D2438158D6632271557CC0B* ___U3CsubsystemU3Ek__BackingField;
};
struct SubsystemLifecycleManager_3_tC55963016DDC47B71E344157D85359DBB408C9D1  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	XRFaceSubsystem_t93DD9DDD21C06E5A591BB0094512BC8FD8A890D3* ___U3CsubsystemU3Ek__BackingField;
};
struct SubsystemLifecycleManager_3_tE5D97BF0378EDE42C77E6F18C16B28F56C3E386E  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	XRHumanBodySubsystem_tF7BE6EF91D4B632389B2F818E516F94213281526* ___U3CsubsystemU3Ek__BackingField;
};
struct SubsystemLifecycleManager_3_t17DE984A5507806C3E89F7255F02922D8A7F6FD2  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	XRImageTrackingSubsystem_t5EEEA714F4273F686F836517EE4957AEDFFD7BEB* ___U3CsubsystemU3Ek__BackingField;
};
struct SubsystemLifecycleManager_3_tFFD3CACE2B7A53A40E40FA52D115A65CB0F93F2C  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	XRObjectTrackingSubsystem_t0094478B41F73E26F73943C09ECD134874B68ED5* ___U3CsubsystemU3Ek__BackingField;
};
struct SubsystemLifecycleManager_3_t7133C95527C9A44A49E600C915EDAFE2902942C4  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	XRParticipantSubsystem_tA44C77BC9562FCF6714E6B69322B2CF7F552AF92* ___U3CsubsystemU3Ek__BackingField;
};
struct SubsystemLifecycleManager_3_t7FE0F4D0E7C0B4044705703906B62335E93547A6  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	XRPlaneSubsystem_t6FC18D5B1FF10EE9B4A751977B526F44A16EDB62* ___U3CsubsystemU3Ek__BackingField;
};
struct SubsystemLifecycleManager_3_tA88EBF01DEE96862685C9E6EEE51211282C8F23A  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	XRPointCloudSubsystem_t997D1C035B61DD47C2235712FF0679F716850708* ___U3CsubsystemU3Ek__BackingField;
};
struct TrackablesChangedEventUnit_8_t937DCA5FB2B32B242842AD0C1F2F5EE5F6ADF227  : public GameObjectEventUnit_1_tB6AFC5E0ED153703A6CE1D8703D0D25E26F9C849
{
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CaddedU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CupdatedU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CremovedU3Ek__BackingField;
};
struct TrackablesChangedEventUnit_8_t7E2C88EBC443BD6C1236769AD6E11B6822447A38  : public GameObjectEventUnit_1_tDEFD0C21681E7E0136494B6DDEA5660EA577C156
{
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CaddedU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CupdatedU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CremovedU3Ek__BackingField;
};
struct TrackablesChangedEventUnit_8_t49CC6AB3D8E782C8141D1915452029E22C2213F0  : public GameObjectEventUnit_1_t063EB218BF6F0191E1F03A814D03E055F9D28E59
{
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CaddedU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CupdatedU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CremovedU3Ek__BackingField;
};
struct TrackablesChangedEventUnit_8_tBEDFC346D94F264473B89BF51CB942BCC3D7A3BC  : public GameObjectEventUnit_1_t78B78E27AF85CD35BAB92827457A8C2A0721BAB6
{
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CaddedU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CupdatedU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CremovedU3Ek__BackingField;
};
struct TrackablesChangedEventUnit_8_tD6C8D29C0AC7F5F97EF5642B923DFE86702323F8  : public GameObjectEventUnit_1_tA120BADA678284B678A6802440CC08218C9738DA
{
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CaddedU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CupdatedU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CremovedU3Ek__BackingField;
};
struct TrackablesChangedEventUnit_8_tA9240E148EC702D556AA0394A8591794B81864EE  : public GameObjectEventUnit_1_t7CA8CF819390A4D9F3AE951985DBC8833C25B677
{
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CaddedU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CupdatedU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CremovedU3Ek__BackingField;
};
struct TrackablesChangedEventUnit_8_tEB3E8F04C93B5D5BA458A74D225A0598224B0211  : public GameObjectEventUnit_1_t3A40F0C72F56365F8071C101519523537BA61372
{
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CaddedU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CupdatedU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CremovedU3Ek__BackingField;
};
struct TrackablesChangedEventUnit_8_t9B0C0052BA572E6D5D816B31AE40C31151AB58B2  : public GameObjectEventUnit_1_tC7D76058D223CF6B79EE59E069217305CE18A13E
{
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CaddedU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CupdatedU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CremovedU3Ek__BackingField;
};
struct TrackablesChangedEventUnit_8_t421739B4DF339B26394C3BAFA5BEDF14102691BB  : public GameObjectEventUnit_1_t7779539F7C75323975424FAD14786741E0970AAF
{
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CaddedU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CupdatedU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CremovedU3Ek__BackingField;
};
struct TrackablesChangedEventUnit_8_t80ACB5EC468FCE7380B5D1FFB60D34852FDDECEB  : public GameObjectEventUnit_1_t7CA8CF819390A4D9F3AE951985DBC8833C25B677
{
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CaddedU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CupdatedU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CremovedU3Ek__BackingField;
};
struct TrackablesChangedEventUnit_8_t1733B9D53AC7A75201E66A3C4299F9EEAF6D9AC9  : public GameObjectEventUnit_1_tB6AFC5E0ED153703A6CE1D8703D0D25E26F9C849
{
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CaddedU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CupdatedU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CremovedU3Ek__BackingField;
};
struct TrackablesChangedEventUnit_8_t32C53D3043B80B877FC2C13D865611AE1870707B  : public GameObjectEventUnit_1_tDEFD0C21681E7E0136494B6DDEA5660EA577C156
{
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CaddedU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CupdatedU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CremovedU3Ek__BackingField;
};
struct TrackablesChangedEventUnit_8_t3ED65845C697D5F6365D653761D50033F9DEA043  : public GameObjectEventUnit_1_t063EB218BF6F0191E1F03A814D03E055F9D28E59
{
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CaddedU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CupdatedU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CremovedU3Ek__BackingField;
};
struct TrackablesChangedEventUnit_8_tAD7DEECE198B73643980718B72E5D18B9AE18A29  : public GameObjectEventUnit_1_t78B78E27AF85CD35BAB92827457A8C2A0721BAB6
{
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CaddedU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CupdatedU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CremovedU3Ek__BackingField;
};
struct TrackablesChangedEventUnit_8_tE5B851C314A74199250431CA9EF82851FE17AF1E  : public GameObjectEventUnit_1_tA120BADA678284B678A6802440CC08218C9738DA
{
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CaddedU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CupdatedU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CremovedU3Ek__BackingField;
};
struct TrackablesChangedEventUnit_8_t2B87F30FA4D65ECE396E4A074036A8F6A0F95654  : public GameObjectEventUnit_1_t3A40F0C72F56365F8071C101519523537BA61372
{
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CaddedU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CupdatedU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CremovedU3Ek__BackingField;
};
struct TrackablesChangedEventUnit_8_t9D9E1B534664684D3799239F14EA6394D5EA7102  : public GameObjectEventUnit_1_tC7D76058D223CF6B79EE59E069217305CE18A13E
{
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CaddedU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CupdatedU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CremovedU3Ek__BackingField;
};
struct TrackablesChangedEventUnit_8_t65F4F7BA35861764870385ED28CF9DA4366517DB  : public GameObjectEventUnit_1_t7779539F7C75323975424FAD14786741E0970AAF
{
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CaddedU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CupdatedU3Ek__BackingField;
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CremovedU3Ek__BackingField;
};
struct ARTrackable_t1D98417758C08E98A333CDEC0C2C1688CE10B2E0  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
};
struct CameraFrameReceivedEventUnit_t5C258A06DEEF9DBF3CE6AD4FD0B51313F1DEF858  : public GameObjectEventUnit_1_tA12F0B3D1C478378C11E341089186ED62766AF93
{
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CframeEventArgsU3Ek__BackingField;
};
struct FaceUpdatedEventUnit_tBAF617E9D7CA035FC4D40FDA435F6BC35A39D426  : public GameObjectEventUnit_1_tD98F9076C76336C1A30A327C151C4BC55CBE45FE
{
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CfaceOutU3Ek__BackingField;
};
struct MessageListener_t50FA00662CCB169F065100293E953B3024E0BCAE  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
};
struct SessionStateChangedEventUnit_tB622F7C1B352062C2D5D67A3EA1E1E6DBD910631  : public GlobalEventUnit_1_tFCC2A75C905819911151CA0BD2FEE0E3A24D5FE6
{
	ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___U3CsessionStateOutU3Ek__BackingField;
};
struct ARTrackableManager_5_t58BCDA2A5956989C6A20CC1E41B0F6DBEED545B6  : public SubsystemLifecycleManager_3_tAE0BB0092EB47B81AA111C370381E3BA14C88DD3
{
	XROrigin_t5E534A6D886991AF3C1C64127E19A3A510009CFE* ___U3CoriginU3Ek__BackingField;
	XROrigin_t5E534A6D886991AF3C1C64127E19A3A510009CFE* ___U3CsessionOriginU3Ek__BackingField;
	Dictionary_2_t525DE08A369658A5AE7D624D571ED15216817535* ___m_Trackables;
	Dictionary_2_t525DE08A369658A5AE7D624D571ED15216817535* ___m_PendingAdds;
};
struct ARTrackableManager_5_t5F713392E8DF85A6F649AF339710976EB41DBC05  : public SubsystemLifecycleManager_3_tFA3968C6C041E64DA6CDC221BF400F2F0EFCB9C4
{
	XROrigin_t5E534A6D886991AF3C1C64127E19A3A510009CFE* ___U3CoriginU3Ek__BackingField;
	XROrigin_t5E534A6D886991AF3C1C64127E19A3A510009CFE* ___U3CsessionOriginU3Ek__BackingField;
	Dictionary_2_t7993FC827CBC9BA9CA6B912A2DF915B143970FD4* ___m_Trackables;
	Dictionary_2_t7993FC827CBC9BA9CA6B912A2DF915B143970FD4* ___m_PendingAdds;
};
struct ARTrackableManager_5_t2D42FCFDD2D69C41D2C2951F731A4F489A8605A4  : public SubsystemLifecycleManager_3_tC55963016DDC47B71E344157D85359DBB408C9D1
{
	XROrigin_t5E534A6D886991AF3C1C64127E19A3A510009CFE* ___U3CoriginU3Ek__BackingField;
	XROrigin_t5E534A6D886991AF3C1C64127E19A3A510009CFE* ___U3CsessionOriginU3Ek__BackingField;
	Dictionary_2_t24DD0A560FD522E53E9814CD1A0AD91C3AD5FCCC* ___m_Trackables;
	Dictionary_2_t24DD0A560FD522E53E9814CD1A0AD91C3AD5FCCC* ___m_PendingAdds;
};
struct ARTrackableManager_5_t66B444EEF0E5B63A8B668B882FED811E693A0EC7  : public SubsystemLifecycleManager_3_tE5D97BF0378EDE42C77E6F18C16B28F56C3E386E
{
	XROrigin_t5E534A6D886991AF3C1C64127E19A3A510009CFE* ___U3CoriginU3Ek__BackingField;
	XROrigin_t5E534A6D886991AF3C1C64127E19A3A510009CFE* ___U3CsessionOriginU3Ek__BackingField;
	Dictionary_2_t26E20BE1EB8A93CA83F951C6E2C3E93D20E1EA30* ___m_Trackables;
	Dictionary_2_t26E20BE1EB8A93CA83F951C6E2C3E93D20E1EA30* ___m_PendingAdds;
};
struct ARTrackableManager_5_t1395957883D0FA3B96AA1B31F7D9D15573DA46F5  : public SubsystemLifecycleManager_3_t17DE984A5507806C3E89F7255F02922D8A7F6FD2
{
	XROrigin_t5E534A6D886991AF3C1C64127E19A3A510009CFE* ___U3CoriginU3Ek__BackingField;
	XROrigin_t5E534A6D886991AF3C1C64127E19A3A510009CFE* ___U3CsessionOriginU3Ek__BackingField;
	Dictionary_2_t13B80190D7DD5A74B43A63EC63B97E079A0B2FC2* ___m_Trackables;
	Dictionary_2_t13B80190D7DD5A74B43A63EC63B97E079A0B2FC2* ___m_PendingAdds;
};
struct ARTrackableManager_5_t956DB46580AD764FF0CA6A96A175FDDEC57D74A5  : public SubsystemLifecycleManager_3_tFFD3CACE2B7A53A40E40FA52D115A65CB0F93F2C
{
	XROrigin_t5E534A6D886991AF3C1C64127E19A3A510009CFE* ___U3CoriginU3Ek__BackingField;
	XROrigin_t5E534A6D886991AF3C1C64127E19A3A510009CFE* ___U3CsessionOriginU3Ek__BackingField;
	Dictionary_2_t7DA79BD2AB092CBEF953D24BF7B5C6154A0F04D2* ___m_Trackables;
	Dictionary_2_t7DA79BD2AB092CBEF953D24BF7B5C6154A0F04D2* ___m_PendingAdds;
};
struct ARTrackableManager_5_t2AEA0FB85E08D94EE295557C7AEDBADA317EA986  : public SubsystemLifecycleManager_3_t7133C95527C9A44A49E600C915EDAFE2902942C4
{
	XROrigin_t5E534A6D886991AF3C1C64127E19A3A510009CFE* ___U3CoriginU3Ek__BackingField;
	XROrigin_t5E534A6D886991AF3C1C64127E19A3A510009CFE* ___U3CsessionOriginU3Ek__BackingField;
	Dictionary_2_t30E6C815332BDA64B92237BE387396E9362813BE* ___m_Trackables;
	Dictionary_2_t30E6C815332BDA64B92237BE387396E9362813BE* ___m_PendingAdds;
};
struct ARTrackableManager_5_t1F9F85442D84B92DE0BC4C518768C0154AB69103  : public SubsystemLifecycleManager_3_t7FE0F4D0E7C0B4044705703906B62335E93547A6
{
	XROrigin_t5E534A6D886991AF3C1C64127E19A3A510009CFE* ___U3CoriginU3Ek__BackingField;
	XROrigin_t5E534A6D886991AF3C1C64127E19A3A510009CFE* ___U3CsessionOriginU3Ek__BackingField;
	Dictionary_2_t26A4820BA6F451105138232941B7A9466E583A6C* ___m_Trackables;
	Dictionary_2_t26A4820BA6F451105138232941B7A9466E583A6C* ___m_PendingAdds;
};
struct ARTrackableManager_5_t8ABF1CC7EEE279420A5452FCC971A9B86C71BDBA  : public SubsystemLifecycleManager_3_tA88EBF01DEE96862685C9E6EEE51211282C8F23A
{
	XROrigin_t5E534A6D886991AF3C1C64127E19A3A510009CFE* ___U3CoriginU3Ek__BackingField;
	XROrigin_t5E534A6D886991AF3C1C64127E19A3A510009CFE* ___U3CsessionOriginU3Ek__BackingField;
	Dictionary_2_t9647896A9433E05337C4FDD2755A5C5C81C52F6C* ___m_Trackables;
	Dictionary_2_t9647896A9433E05337C4FDD2755A5C5C81C52F6C* ___m_PendingAdds;
};
struct ARTrackable_2_tADB370031FB959281901DE6F1A8E95A7E25E6740  : public ARTrackable_t1D98417758C08E98A333CDEC0C2C1688CE10B2E0
{
	bool ___m_DestroyOnRemoval;
	bool ___U3CpendingU3Ek__BackingField;
	XRFace_t235F951F4AC7CC6A0CA6829CECA05210D64982FB ___U3CsessionRelativeDataU3Ek__BackingField;
};
struct TrackableManagerListener_1_t368727D76E305857E6524137ED96D00A18546BAC  : public MessageListener_t50FA00662CCB169F065100293E953B3024E0BCAE
{
	ARAnchorManager_tF68C46300BD6F2E83E3360BF9297C3C50F5D6B3E* ___m_Manager;
	StringBuilder_t* ___m_LogBuilder;
};
struct TrackableManagerListener_1_t60BDFBD938C7CBEAEEEC4ED167E4D757936C28E8  : public MessageListener_t50FA00662CCB169F065100293E953B3024E0BCAE
{
	AREnvironmentProbeManager_tE457B73006F0D5136D9F2BDDEB072C1A1DB407F8* ___m_Manager;
	StringBuilder_t* ___m_LogBuilder;
};
struct TrackableManagerListener_1_tAE0B39F766687B7392BB26FC0CEBCAA679BEB365  : public MessageListener_t50FA00662CCB169F065100293E953B3024E0BCAE
{
	ARFaceManager_tE5955187C9D7C9C4C4139F522DA0DEA69EFCFD8F* ___m_Manager;
	StringBuilder_t* ___m_LogBuilder;
};
struct TrackableManagerListener_1_t8EF347E8BB7B8747C247403C9E911620DE36B242  : public MessageListener_t50FA00662CCB169F065100293E953B3024E0BCAE
{
	ARHumanBodyManager_tA5246205B817A3D4A87BFC54FC018FB513867BFB* ___m_Manager;
	StringBuilder_t* ___m_LogBuilder;
};
struct TrackableManagerListener_1_tE4BF78B1F882C85C4BC3496DE722CE244D42E779  : public MessageListener_t50FA00662CCB169F065100293E953B3024E0BCAE
{
	ARParticipantManager_t759CB1A6961D7C7F99E47B8AFFF7BCA4761C99C4* ___m_Manager;
	StringBuilder_t* ___m_LogBuilder;
};
struct TrackableManagerListener_1_t8673B1A6156F98541B1B39B98473DFF37AC0CC90  : public MessageListener_t50FA00662CCB169F065100293E953B3024E0BCAE
{
	ARPlaneManager_t9DA6C512BDCFFBEE8B3DAA7170EB3BFFDE26A928* ___m_Manager;
	StringBuilder_t* ___m_LogBuilder;
};
struct TrackableManagerListener_1_tFCE62DCE9ECC332FFBFBF7949494CC4DF7DA37E9  : public MessageListener_t50FA00662CCB169F065100293E953B3024E0BCAE
{
	ARPointCloudManager_t28D1833BCE1A48657E504109A89A3B75D0AD7D9F* ___m_Manager;
	StringBuilder_t* ___m_LogBuilder;
};
struct TrackableManagerListener_1_t7C3219B15D46C0E0276CF4D8E06F12DC969F5D56  : public MessageListener_t50FA00662CCB169F065100293E953B3024E0BCAE
{
	ARTrackedImageManager_tCBA77F0B8A7172CC6508CA29176EAE1DE5DC4BD4* ___m_Manager;
	StringBuilder_t* ___m_LogBuilder;
};
struct TrackableManagerListener_1_t2D0E0BEBA98DF361C2EB9261ED0310BF3DFF8FD5  : public MessageListener_t50FA00662CCB169F065100293E953B3024E0BCAE
{
	ARTrackedObjectManager_tD667E7CCD95E9D25449BD578F256F728D8619FC7* ___m_Manager;
	StringBuilder_t* ___m_LogBuilder;
};
struct ARCameraManager_t6E957EA922911B8447D086D9FB1F860EC967BD9F  : public SubsystemLifecycleManager_3_t2810F2EBBCBEAEE830830E20CF311EBAD8EA983B
{
	List_1_tBEE41A3C84ED348850866AD76B66E9D9B19767FF* ___m_TextureInfos;
	Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* ___m_Camera;
	bool ___m_PreRenderInvertCullingValue;
	ARTextureInfo_t008DA41DF9555306803303A0353619E5C0917261 ___m_CameraGrainInfo;
	Action_1_t54327BAFA15E24E002EFAE48D1AFFC020D368735* ___frameReceived;
	int32_t ___m_FocusMode;
	int32_t ___m_LightEstimationMode;
	bool ___m_AutoFocus;
	int32_t ___m_LightEstimation;
	int32_t ___m_FacingDirection;
	uint8_t ___m_RenderMode;
};
struct ARCameraManagerListener_t9CDC77A2AAA0062BF970292523A3AD20D76BB3AE  : public MessageListener_t50FA00662CCB169F065100293E953B3024E0BCAE
{
	ARCameraManager_t6E957EA922911B8447D086D9FB1F860EC967BD9F* ___m_Manager;
	StringBuilder_t* ___m_LogBuilder;
};
struct ARFaceListener_tCC12AA9BE708DFBC6BE058D621A5B173162E8A59  : public MessageListener_t50FA00662CCB169F065100293E953B3024E0BCAE
{
	ARFace_t73C068C9C8422F79A858EC41C322D399305B8453* ___m_Face;
};
struct AnchorsChangedEventUnit_t1CDE435C1ABCADB9DEB2117F2E8C2943E274045F  : public TrackablesChangedEventUnit_8_t937DCA5FB2B32B242842AD0C1F2F5EE5F6ADF227
{
};
struct EnvironmentProbesChangedEventUnit_t9F93FC318A1011F92882E76B8284810D2B98ABE3  : public TrackablesChangedEventUnit_8_t7E2C88EBC443BD6C1236769AD6E11B6822447A38
{
};
struct FacesChangedEventUnit_tBA9644CD496CD26CC75569DEC718E5E845635BAC  : public TrackablesChangedEventUnit_8_t49CC6AB3D8E782C8141D1915452029E22C2213F0
{
};
struct HumanBodiesChangedEventUnit_t7E27E223B038B0174E95448F4286BD590A46D54F  : public TrackablesChangedEventUnit_8_tBEDFC346D94F264473B89BF51CB942BCC3D7A3BC
{
};
struct ParticipantsChangedEventUnit_tCB5A32ECD288DCDFDC4AC059E40E7552B424D1F8  : public TrackablesChangedEventUnit_8_tD6C8D29C0AC7F5F97EF5642B923DFE86702323F8
{
};
struct PlanesChangedEventUnit_t2FE36340891DD55C64AD4A68C956533AFDB7D116  : public TrackablesChangedEventUnit_8_tA9240E148EC702D556AA0394A8591794B81864EE
{
};
struct PointCloudsChangedEventUnit_t70FDC749BF87C070761B2EEC7F80045BCBC542E9  : public TrackablesChangedEventUnit_8_tEB3E8F04C93B5D5BA458A74D225A0598224B0211
{
};
struct TrackedImagesChangedEventUnit_tEDE47E3521D6A7321912CFFCB5C10500CD949D29  : public TrackablesChangedEventUnit_8_t9B0C0052BA572E6D5D816B31AE40C31151AB58B2
{
};
struct TrackedObjectsChangedEventUnit_t8EA5435947DF665B0FC12920629B4E6F1A787CF2  : public TrackablesChangedEventUnit_8_t421739B4DF339B26394C3BAFA5BEDF14102691BB
{
};
struct ARAnchorManager_tF68C46300BD6F2E83E3360BF9297C3C50F5D6B3E  : public ARTrackableManager_5_t58BCDA2A5956989C6A20CC1E41B0F6DBEED545B6
{
	GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___m_AnchorPrefab;
	Action_1_t11340E5174173030E076A75316E18A395082F8A5* ___anchorsChanged;
};
struct ARAnchorManagerListener_t566E40F47C375A0FCD517AEAF5732FA1E9FB486A  : public TrackableManagerListener_1_t368727D76E305857E6524137ED96D00A18546BAC
{
};
struct AREnvironmentProbeManager_tE457B73006F0D5136D9F2BDDEB072C1A1DB407F8  : public ARTrackableManager_5_t5F713392E8DF85A6F649AF339710976EB41DBC05
{
	bool ___m_AutomaticPlacement;
	int32_t ___m_EnvironmentTextureFilterMode;
	bool ___m_EnvironmentTextureHDR;
	GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___m_DebugPrefab;
	Action_1_tE3E0B091088607877804833E23BFDBBF5594B830* ___environmentProbesChanged;
};
struct AREnvironmentProbeManagerListener_t919D09DA2D7FF7958381E8B7494E6C5A1D6D1533  : public TrackableManagerListener_1_t60BDFBD938C7CBEAEEEC4ED167E4D757936C28E8
{
};
struct ARFace_t73C068C9C8422F79A858EC41C322D399305B8453  : public ARTrackable_2_tADB370031FB959281901DE6F1A8E95A7E25E6740
{
	Action_1_t30DB722576CDAD6BB3F09BD60358BF49AA947548* ___updated;
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___U3CleftEyeU3Ek__BackingField;
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___U3CrightEyeU3Ek__BackingField;
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___U3CfixationPointU3Ek__BackingField;
	XRFaceMesh_t6B92499E6ECFF17A9BDCC6E531A79395795E2CD4 ___m_FaceMesh;
	bool ___m_Updated;
};
struct ARFaceManager_tE5955187C9D7C9C4C4139F522DA0DEA69EFCFD8F  : public ARTrackableManager_5_t2D42FCFDD2D69C41D2C2951F731A4F489A8605A4
{
	GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___m_FacePrefab;
	int32_t ___m_MaximumFaceCount;
	Action_1_t0A16CC637B74FE43A20D2784BCA3172E7D13789C* ___facesChanged;
};
struct ARFaceManagerListener_t8043177537F4EAE01597D66B7EB8968FBAA5811A  : public TrackableManagerListener_1_tAE0B39F766687B7392BB26FC0CEBCAA679BEB365
{
};
struct ARHumanBodyManager_tA5246205B817A3D4A87BFC54FC018FB513867BFB  : public ARTrackableManager_5_t66B444EEF0E5B63A8B668B882FED811E693A0EC7
{
	bool ___m_Pose2D;
	bool ___m_Pose3D;
	bool ___m_Pose3DScaleEstimation;
	GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___m_HumanBodyPrefab;
	Action_1_t78DC7DD8CB74A1B21E4419996B784C6E51821247* ___humanBodiesChanged;
};
struct ARHumanBodyManagerListener_tD428DC6513EF466039758E70361DF5E6F429589A  : public TrackableManagerListener_1_t8EF347E8BB7B8747C247403C9E911620DE36B242
{
};
struct ARParticipantManager_t759CB1A6961D7C7F99E47B8AFFF7BCA4761C99C4  : public ARTrackableManager_5_t2AEA0FB85E08D94EE295557C7AEDBADA317EA986
{
	GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___m_ParticipantPrefab;
	Action_1_tE5F1AC110E5F6610E8B460B753F57ACC9B4524E3* ___participantsChanged;
};
struct ARParticipantManagerListener_tD1B316B50151D4E2F897C85D37E9EA0CE76DD180  : public TrackableManagerListener_1_tE4BF78B1F882C85C4BC3496DE722CE244D42E779
{
};
struct ARPlaneManager_t9DA6C512BDCFFBEE8B3DAA7170EB3BFFDE26A928  : public ARTrackableManager_5_t1F9F85442D84B92DE0BC4C518768C0154AB69103
{
	GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___m_PlanePrefab;
	int32_t ___m_DetectionMode;
	Action_1_t009680BF19017ECA60753C7F605CBD85C56C6560* ___planesChanged;
};
struct ARPlaneManagerListener_t48EF97E316472A73C564EB0B7A71B067D0BBCE76  : public TrackableManagerListener_1_t8673B1A6156F98541B1B39B98473DFF37AC0CC90
{
};
struct ARPointCloudManager_t28D1833BCE1A48657E504109A89A3B75D0AD7D9F  : public ARTrackableManager_5_t8ABF1CC7EEE279420A5452FCC971A9B86C71BDBA
{
	GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___m_PointCloudPrefab;
	Action_1_t053CCF2809F9B0B61EA8298154533C5FACB724F0* ___pointCloudsChanged;
};
struct ARPointCloudManagerListener_t79362431A1598431A74D9AB30FF9CAC2C7D6FB40  : public TrackableManagerListener_1_tFCE62DCE9ECC332FFBFBF7949494CC4DF7DA37E9
{
};
struct ARTrackedImageManager_tCBA77F0B8A7172CC6508CA29176EAE1DE5DC4BD4  : public ARTrackableManager_5_t1395957883D0FA3B96AA1B31F7D9D15573DA46F5
{
	XRReferenceImageLibrary_t039233790C16E76ED95AABD95D9A35014081EA42* ___m_SerializedLibrary;
	int32_t ___m_MaxNumberOfMovingImages;
	GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___m_TrackedImagePrefab;
	Action_1_tE7B3D1070B99EFDFA92BD4DBEF95BF881DEB591F* ___trackedImagesChanged;
	Dictionary_2_t02017471B47A6BEB88F94FACE526F622A42AEE49* ___m_ReferenceImages;
};
struct ARTrackedImageManagerListener_t41A4A505BF375F0C883703891EB4C03EE271F4E1  : public TrackableManagerListener_1_t7C3219B15D46C0E0276CF4D8E06F12DC969F5D56
{
};
struct ARTrackedObjectManager_tD667E7CCD95E9D25449BD578F256F728D8619FC7  : public ARTrackableManager_5_t956DB46580AD764FF0CA6A96A175FDDEC57D74A5
{
	XRReferenceObjectLibrary_t035F7FE264C24BC98A58CA2F24D047BB99BC8487* ___m_ReferenceLibrary;
	GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___m_TrackedObjectPrefab;
	Action_1_t34AAB95B35DF66496184F8421A8AC5F317B7D8AB* ___trackedObjectsChanged;
	int32_t ___m_ReferenceObjectCount;
	Dictionary_2_t2A895F6D5510191F016166DBE21C821944195F02* ___m_ReferenceObjects;
};
struct ARTrackedObjectManagerListener_t67F1931020F75B64E0AEF23B15F033285A6D3A7A  : public TrackableManagerListener_1_t2D0E0BEBA98DF361C2EB9261ED0310BF3DFF8FD5
{
};
struct List_1_t5C2EBF017989E44F3D1D11AF502E10F6F8D9AB53_StaticFields
{
	ARAnchorU5BU5D_t589CB7678527B30DBFABED0E377BC500C9D3454A* ___s_emptyArray;
};
struct List_1_t6F14EE6179A9A8E042C5D65E902F72E24597029B_StaticFields
{
	AREnvironmentProbeU5BU5D_t172E5E7D75D1738636091C776C8EEC8BAAE3839A* ___s_emptyArray;
};
struct List_1_tA11EBED64210DD13BB4303415D948E6CD964AAF9_StaticFields
{
	ARFaceU5BU5D_t6FC6B0063C2B88AFFF728161A36C9B4402F65711* ___s_emptyArray;
};
struct List_1_t64B1B4ABB789350AE017F3EAF4D3647AF59244C7_StaticFields
{
	ARHumanBodyU5BU5D_t765862C91212D61EA388CB29F8895BC1FDB1EB0D* ___s_emptyArray;
};
struct List_1_t87D9AC2D6B4A4860E01575669DEB0D164D4B33B3_StaticFields
{
	ARParticipantU5BU5D_tE306DE8EAC060B47D21D12030F3E59D78AE4BF9D* ___s_emptyArray;
};
struct List_1_t10BA05B555C92BD54800DFF82DCAAFC9DE44A077_StaticFields
{
	ARPlaneU5BU5D_t8C555584F9BAD4FB2BD4DB6D7933EBBBD61D6FD4* ___s_emptyArray;
};
struct List_1_tB9CD639EB4FE5EA0A8B417168BB9BD2D29AEE271_StaticFields
{
	ARPointCloudU5BU5D_tD64665DDD0344C5780D4BDC332717DA27CEF1CE0* ___s_emptyArray;
};
struct List_1_tFA730335FF46512E4245B2F46892CA318B394FAE_StaticFields
{
	ARTrackedImageU5BU5D_tD227987C766F9B880B8EF0E9ADF5918CDFB72367* ___s_emptyArray;
};
struct List_1_tF1201F2F725A8AE529D436F38A4771FB6790F3C6_StaticFields
{
	ARTrackedObjectU5BU5D_t432458E1432DA3112081C0414632055D92813EDE* ___s_emptyArray;
};
struct U3CPrivateImplementationDetailsU3E_tDDCC949E3B043757E8B2BF5CEA983FB5D64C65A5_StaticFields
{
	__StaticArrayInitTypeSizeU3D4990_t7BE706357E559B310B67297701C273970742B891 ___06B4533828E721283278BAD55C65B14F692386B2BF93E99FAA017FD993F5D9B3;
	__StaticArrayInitTypeSizeU3D2799_t352C93FCC50B5CED699C9CB47B7281796C108FC6 ___4C23045B4C42C8C05E9FC1DE66F40E8CBE11D66FC46CEDF30183BEAF834C5B0B;
};
struct Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582_StaticFields
{
	Func_2_tAF4123F8D69F1A665BBB864E1C97131C05AEBBEB* ___U3CisInspectedBindingU3Ek__BackingField;
};
struct String_t_StaticFields
{
	String_t* ___Empty;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct IntPtr_t_StaticFields
{
	intptr_t ___Zero;
};
struct ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392_StaticFields
{
	HashSet_1_tAE2F12E55878645F5BE7C4D5603228A6FAB429C7* ___typesWithDefaultValues;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_StaticFields
{
	int32_t ___OffsetOfInstanceIDInCPlusPlusObject;
};
struct Type_t_StaticFields
{
	Binder_t91BFCE95A7057FADF4D8A1A342AFE52872246235* ___s_defaultBinder;
	Il2CppChar ___Delimiter;
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___EmptyTypes;
	RuntimeObject* ___Missing;
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterAttribute;
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterName;
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterNameIgnoreCase;
};
struct GetTrackablesUnit_6_tE3FF81AB62ABB3500474D301EF59B13BF4A1685E_StaticFields
{
	bool ___s_IsFirstFrame;
};
struct GetTrackablesUnit_6_tCE90D2E00FA14B5E936843082D488242C7463304_StaticFields
{
	bool ___s_IsFirstFrame;
};
struct GetTrackablesUnit_6_tB48912D7C44FC073BE0CCC0B19CCC4A33E066B77_StaticFields
{
	bool ___s_IsFirstFrame;
};
struct GetTrackablesUnit_6_t3DE4D256458C27210703957091C3684A86E12198_StaticFields
{
	bool ___s_IsFirstFrame;
};
struct GetTrackablesUnit_6_t35B2538FE2BE005439F84D8E3DD2098F607A881B_StaticFields
{
	bool ___s_IsFirstFrame;
};
struct GetTrackablesUnit_6_t016BB0C74695D0122C8B6B3599A71F89D32BCF77_StaticFields
{
	bool ___s_IsFirstFrame;
};
struct GetTrackablesUnit_6_tBEEA64178AC6F947BE154CE4D41420AED64DC0AE_StaticFields
{
	bool ___s_IsFirstFrame;
};
struct GetTrackablesUnit_6_tA1B41E03CB6324ED319781B3AA151D349922B24A_StaticFields
{
	bool ___s_IsFirstFrame;
};
struct GetTrackablesUnit_6_t5FAD854E9B61FD3E7409D18C4DED57BC373BACE7_StaticFields
{
	bool ___s_IsFirstFrame;
};
struct GetTrackablesUnit_6_t60E470A25938E95AA9CD62B873EA6C36FC34D2FF_StaticFields
{
	bool ___s_IsFirstFrame;
};
struct MessageListener_t50FA00662CCB169F065100293E953B3024E0BCAE_StaticFields
{
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ____listenerTypes;
};
struct SessionStateChangedEventUnit_tB622F7C1B352062C2D5D67A3EA1E1E6DBD910631_StaticFields
{
	bool ___s_CallbackIsRegistered;
};
struct ARCameraManager_t6E957EA922911B8447D086D9FB1F860EC967BD9F_StaticFields
{
	List_1_t0F231C3F13EBA1FF9081BD61489D01AA3CBE59D4* ___s_Textures;
	List_1_t05915E9237850A58106982B7FE4BC5DA4E872E73* ___s_PropertyIds;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif
struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031  : public RuntimeArray
{
	ALIGN_FIELD (8) uint8_t m_Items[1];

	inline uint8_t GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline uint8_t* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, uint8_t value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline uint8_t GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline uint8_t* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, uint8_t value)
	{
		m_Items[index] = value;
	}
};


IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_added_mA5EB9599698C839DF1B830628E9AA712D2BAA239_gshared_inline (TrackablesChangedEventUnit_8_t1733B9D53AC7A75201E66A3C4299F9EEAF6D9AC9* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_updated_m0E9C84C6478DC8A8EC77951533068775F7773D47_gshared_inline (TrackablesChangedEventUnit_8_t1733B9D53AC7A75201E66A3C4299F9EEAF6D9AC9* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_removed_mFA20D5CC71D33EF2AD69130EE9B6DFC53C57826E_gshared_inline (TrackablesChangedEventUnit_8_t1733B9D53AC7A75201E66A3C4299F9EEAF6D9AC9* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TrackablesChangedEventUnit_8__ctor_m308C2AAFE4F05EE60FABC654F8DFCE4EEA97C952_gshared (TrackablesChangedEventUnit_8_t1733B9D53AC7A75201E66A3C4299F9EEAF6D9AC9* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GameObjectEventUnit_1_Definition_m993A9F33D4AB3731B9BF811F804CA9BA0E75096E_gshared (GameObjectEventUnit_1_tA12F0B3D1C478378C11E341089186ED62766AF93* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* Unit_ValueOutput_TisARCameraFrameEventArgs_t4060608C37C2B7248AFC908876309CC36D53E0EC_m179CC70EB8D39BBFB29E25C4D83DB538FBF3C6FC_gshared (Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191* __this, String_t* ___0_key, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GameObjectEventUnit_1__ctor_mBA789D715816EDBF30E72C14CE07C03030B2FFD2_gshared (GameObjectEventUnit_1_tA12F0B3D1C478378C11E341089186ED62766AF93* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_added_m227082B23BA194D4F4D78E344F92D69797149EF2_gshared_inline (TrackablesChangedEventUnit_8_t32C53D3043B80B877FC2C13D865611AE1870707B* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_updated_m92262CE776B63637F7622CDECF3F5D1284E380E8_gshared_inline (TrackablesChangedEventUnit_8_t32C53D3043B80B877FC2C13D865611AE1870707B* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_removed_m3681946A08131E0DA77641ED3161001F7EC45ED0_gshared_inline (TrackablesChangedEventUnit_8_t32C53D3043B80B877FC2C13D865611AE1870707B* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TrackablesChangedEventUnit_8__ctor_mEFE6C2A5E0C130EC32B8A8A5F8D16784ED87A2C5_gshared (TrackablesChangedEventUnit_8_t32C53D3043B80B877FC2C13D865611AE1870707B* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_added_m1C11AC657CA855142D543432ABC974CCADE0B3B0_gshared_inline (TrackablesChangedEventUnit_8_t3ED65845C697D5F6365D653761D50033F9DEA043* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_updated_m934BC27B2518C2AF1097361B2834B91346245905_gshared_inline (TrackablesChangedEventUnit_8_t3ED65845C697D5F6365D653761D50033F9DEA043* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_removed_mB2360A7E90332B148E3297E1D32F6031E3E8DEFB_gshared_inline (TrackablesChangedEventUnit_8_t3ED65845C697D5F6365D653761D50033F9DEA043* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TrackablesChangedEventUnit_8__ctor_mEFC05B21043F169E4165A60158BB4EBCE0825918_gshared (TrackablesChangedEventUnit_8_t3ED65845C697D5F6365D653761D50033F9DEA043* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GameObjectEventUnit_1_Definition_mF8BEB75340AEF0E43AD3F4BC254109BD339F4E09_gshared (GameObjectEventUnit_1_tD98F9076C76336C1A30A327C151C4BC55CBE45FE* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* Unit_ValueOutput_TisRuntimeObject_mD512E6E92372FB88500D21640EAA0F24A3468C51_gshared (Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191* __this, String_t* ___0_key, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GameObjectEventUnit_1__ctor_m12AD4CC830381744782178A62EDD22E8BF924894_gshared (GameObjectEventUnit_1_tD98F9076C76336C1A30A327C151C4BC55CBE45FE* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_added_m33DF3083F2E514B2F73286CA73AE45BA5A181D54_gshared_inline (TrackablesChangedEventUnit_8_tAD7DEECE198B73643980718B72E5D18B9AE18A29* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_updated_m92AC4CE3FFB5D2CD7EF4EFEC9FCBA7BD5429FA18_gshared_inline (TrackablesChangedEventUnit_8_tAD7DEECE198B73643980718B72E5D18B9AE18A29* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_removed_m730B768E99F9428C523540CFEAFD4ADC88B3DCBB_gshared_inline (TrackablesChangedEventUnit_8_tAD7DEECE198B73643980718B72E5D18B9AE18A29* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TrackablesChangedEventUnit_8__ctor_mA74332C28A8F0F4BB4E7B9C363DE47DB4F5A960A_gshared (TrackablesChangedEventUnit_8_tAD7DEECE198B73643980718B72E5D18B9AE18A29* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_added_mE06A697E13C4142D6E80237E44FF471D37AA81A8_gshared_inline (TrackablesChangedEventUnit_8_tE5B851C314A74199250431CA9EF82851FE17AF1E* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_updated_mD5811FDA361D62C2B442A29B4271C140BD68FD4D_gshared_inline (TrackablesChangedEventUnit_8_tE5B851C314A74199250431CA9EF82851FE17AF1E* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_removed_mA5A75FD646439C5DF03447808BAC63B942DDE510_gshared_inline (TrackablesChangedEventUnit_8_tE5B851C314A74199250431CA9EF82851FE17AF1E* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TrackablesChangedEventUnit_8__ctor_m912FDF38ED39EC6B4DA92031ED83F57086E5FD51_gshared (TrackablesChangedEventUnit_8_tE5B851C314A74199250431CA9EF82851FE17AF1E* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_added_mBC80FDCEB43A815F5C1E00246B3A0A76BEBF6A55_gshared_inline (TrackablesChangedEventUnit_8_t80ACB5EC468FCE7380B5D1FFB60D34852FDDECEB* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_updated_mE28D5A4C9A9AC30D6587B7C701797F9D89EC4D3C_gshared_inline (TrackablesChangedEventUnit_8_t80ACB5EC468FCE7380B5D1FFB60D34852FDDECEB* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_removed_m72E4C6109F83CDAFC1C03A9E7FA7F9F421F40D2D_gshared_inline (TrackablesChangedEventUnit_8_t80ACB5EC468FCE7380B5D1FFB60D34852FDDECEB* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TrackablesChangedEventUnit_8__ctor_m21F8D2C2250F02424F218D114020A428B8DB2FBD_gshared (TrackablesChangedEventUnit_8_t80ACB5EC468FCE7380B5D1FFB60D34852FDDECEB* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_added_mDFD7A4FA5EE6EE922E003F4F32AF1E97E8532D0B_gshared_inline (TrackablesChangedEventUnit_8_t2B87F30FA4D65ECE396E4A074036A8F6A0F95654* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_updated_mF386F24BC1B82AD93FDFA090C7A6032FD1835BC9_gshared_inline (TrackablesChangedEventUnit_8_t2B87F30FA4D65ECE396E4A074036A8F6A0F95654* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_removed_mD34EBC8EA96EFAA5E5B3BB3A5CD9E63F45C8F46C_gshared_inline (TrackablesChangedEventUnit_8_t2B87F30FA4D65ECE396E4A074036A8F6A0F95654* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TrackablesChangedEventUnit_8__ctor_m7683A2F3073EB853369AA59B04773E2A2D60FE18_gshared (TrackablesChangedEventUnit_8_t2B87F30FA4D65ECE396E4A074036A8F6A0F95654* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EventUnit_1_StartListening_mB6D9B402D7950DAAA250458B8028EAEE5DAD3D10_gshared (EventUnit_1_t0E2A788AF4E4FA2B47C39B7930B50D725B69338D* __this, GraphStack_tD9049F5FB5F8D9E9CD80764D18067D0F87343E7C* ___0_stack, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Action_1__ctor_mC9CFD5CBE62E447D22A4A520EB27144DCD47BBDB_gshared (Action_1_t325C602D999E964DB72210E493635E83F8D6CE68* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EventUnit_1_StopListening_mAC749B19F0503A6FB1237AD485CABA86D5B793A0_gshared (EventUnit_1_t0E2A788AF4E4FA2B47C39B7930B50D725B69338D* __this, GraphStack_tD9049F5FB5F8D9E9CD80764D18067D0F87343E7C* ___0_stack, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EventBus_Trigger_TisInt32Enum_tCBAC8BA2BFF3A845FA599F303093BBBA374B6F0C_m25AF2BE51B827FCEFEC52FEA8C9AD690479DE187_gshared (EventHook_t1C9E2570B55940C342214BACB5E9EC889A113157 ___0_hook, int32_t ___1_args, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EventUnit_1_Definition_mB107EA1B3A9052C1433EF183A8C0D16C0A24E7DD_gshared (EventUnit_1_t0E2A788AF4E4FA2B47C39B7930B50D725B69338D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* Unit_ValueOutput_TisInt32Enum_tCBAC8BA2BFF3A845FA599F303093BBBA374B6F0C_mD272EBBBB2B0A4187CBDF3C90913AF8D050C566E_gshared (Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191* __this, String_t* ___0_key, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GlobalEventUnit_1__ctor_m8C8326D5D9025D0E62A11522CD3915AB44006289_gshared (GlobalEventUnit_1_tFA67B258020D820C41BB840BDBB4B4C3FEA8794B* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_added_m875B59231345FF1E6FBFE1E5EFF593995A2EF71C_gshared_inline (TrackablesChangedEventUnit_8_t9D9E1B534664684D3799239F14EA6394D5EA7102* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_updated_m362E1C8FE77AB6DF95D605D202C2F2D2E51EBDA2_gshared_inline (TrackablesChangedEventUnit_8_t9D9E1B534664684D3799239F14EA6394D5EA7102* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_removed_m81EDFBA98C36006A10B4B9EB3DC52C4E073EE35F_gshared_inline (TrackablesChangedEventUnit_8_t9D9E1B534664684D3799239F14EA6394D5EA7102* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TrackablesChangedEventUnit_8__ctor_m5D8E2CA88711C7F299D1A74DB9861F5178DA7294_gshared (TrackablesChangedEventUnit_8_t9D9E1B534664684D3799239F14EA6394D5EA7102* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_added_m56E2AAE2F7EB5F918BCA351928FA830CFC40B00C_gshared_inline (TrackablesChangedEventUnit_8_t65F4F7BA35861764870385ED28CF9DA4366517DB* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_updated_mB47F00BDB8F28A8C052BF4525824557AA2D0E5F2_gshared_inline (TrackablesChangedEventUnit_8_t65F4F7BA35861764870385ED28CF9DA4366517DB* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_removed_m0F3958FFA505957614CEE2F8AAD2A1BBA5641D5C_gshared_inline (TrackablesChangedEventUnit_8_t65F4F7BA35861764870385ED28CF9DA4366517DB* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TrackablesChangedEventUnit_8__ctor_mCF3C7EDCB1F243B429E2A0F797E31068172458A1_gshared (TrackablesChangedEventUnit_8_t65F4F7BA35861764870385ED28CF9DA4366517DB* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EventBus_Trigger_TisARAnchorsChangedEventArgs_t755E6208C84304CAC448C133B0A8660CD5750A75_mE9B6D9EEA77C3264949C5BA781F1278767B8D982_gshared (String_t* ___0_name, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___1_target, ARAnchorsChangedEventArgs_t755E6208C84304CAC448C133B0A8660CD5750A75 ___2_args, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Action_1__ctor_m27F591D84CB51009686E04C84ED35045DB8DC4B1_gshared (Action_1_t11340E5174173030E076A75316E18A395082F8A5* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TrackableManagerListener_1__ctor_mA005DBA59FBCA1E0EC60AA2B1B1D93527527ABF2_gshared (TrackableManagerListener_1_tF5FD12C1482D0B415F386E1BC32077FEBAA40B8D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* Component_GetComponent_TisRuntimeObject_m7181F81CAEC2CF53F5D2BC79B7425C16E1F80D33_gshared (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* Object_FindObjectOfType_TisRuntimeObject_m02DFBF011F3B59F777A5E521DB2A116DD496E968_gshared (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Action_1__ctor_mDFD48034EFC4DF74EEB44B18C74DD7BAF391814E_gshared (Action_1_t54327BAFA15E24E002EFAE48D1AFFC020D368735* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EventBus_Trigger_TisARCameraFrameEventArgs_t4060608C37C2B7248AFC908876309CC36D53E0EC_m147D0F4E1AEA08F04B42A886DD7FD396ABE2448B_gshared (String_t* ___0_name, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___1_target, ARCameraFrameEventArgs_t4060608C37C2B7248AFC908876309CC36D53E0EC ___2_args, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EventBus_Trigger_TisAREnvironmentProbesChangedEvent_t8757E98E11F3D37F0CFC4589FCA5CD2107F8375C_mF3EF7B9EF8367C19AE598C0BB4C5C819E5E9DDA3_gshared (String_t* ___0_name, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___1_target, AREnvironmentProbesChangedEvent_t8757E98E11F3D37F0CFC4589FCA5CD2107F8375C ___2_args, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Action_1__ctor_mD1DD23E09BBCBCDCB59373D479AA93CADF7E5C48_gshared (Action_1_tE3E0B091088607877804833E23BFDBBF5594B830* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EventBus_Trigger_TisARFaceUpdatedEventArgs_tC20D17255858678A1A8230EA32D7C38287221FF6_m36EBB182855F50B6EA52073A9C5843D3FE22B837_gshared (String_t* ___0_name, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___1_target, ARFaceUpdatedEventArgs_tC20D17255858678A1A8230EA32D7C38287221FF6 ___2_args, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Action_1__ctor_m3A71B4EF8E0C69EED8BBC0BB8AE9856BEA1929D1_gshared (Action_1_t30DB722576CDAD6BB3F09BD60358BF49AA947548* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EventBus_Trigger_TisARFacesChangedEventArgs_tC05D4423CB2096FF5955C3A0A00056EE2B6301AE_mF3D39EAC80939C5697B3EE63307DD1AFB338914D_gshared (String_t* ___0_name, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___1_target, ARFacesChangedEventArgs_tC05D4423CB2096FF5955C3A0A00056EE2B6301AE ___2_args, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Action_1__ctor_m6B29F8F1F0039A82C7A8CE016F79E8D39054E4D4_gshared (Action_1_t0A16CC637B74FE43A20D2784BCA3172E7D13789C* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EventBus_Trigger_TisARHumanBodiesChangedEventArgs_t19088E0D3384A22C5D8A542957705222C834720B_m053372DF7FDC0D251BE56C17199E478393C48C13_gshared (String_t* ___0_name, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___1_target, ARHumanBodiesChangedEventArgs_t19088E0D3384A22C5D8A542957705222C834720B ___2_args, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Action_1__ctor_mCFC45AE4A743ED62D221EC7E1918738EE0C00B60_gshared (Action_1_t78DC7DD8CB74A1B21E4419996B784C6E51821247* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EventBus_Trigger_TisARParticipantsChangedEventArgs_t2EAD9041A074A7ACEFF280662784747A4E85E527_m1AFA7138412723D9A851AE84D192CF53B2FD2CEF_gshared (String_t* ___0_name, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___1_target, ARParticipantsChangedEventArgs_t2EAD9041A074A7ACEFF280662784747A4E85E527 ___2_args, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Action_1__ctor_m0C34C0D8022B4CB26E739D328681C24E65FC460C_gshared (Action_1_tE5F1AC110E5F6610E8B460B753F57ACC9B4524E3* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EventBus_Trigger_TisARPlanesChangedEventArgs_t8D63E0257BF9942EF8F8C0445F2FD46421017872_m95B4A178E0E6B6E98EE3F2CCBA22EF47977C9BCB_gshared (String_t* ___0_name, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___1_target, ARPlanesChangedEventArgs_t8D63E0257BF9942EF8F8C0445F2FD46421017872 ___2_args, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Action_1__ctor_mBEDDD2BF96A8A407FBB833F764447E82E3D50BCF_gshared (Action_1_t009680BF19017ECA60753C7F605CBD85C56C6560* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EventBus_Trigger_TisARPointCloudChangedEventArgs_t7DE4CA4D30D369496221061F89CC4BF84E42CC1B_mE0DDE4D49AE7A3FB982B04E07E2475F9D93977DE_gshared (String_t* ___0_name, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___1_target, ARPointCloudChangedEventArgs_t7DE4CA4D30D369496221061F89CC4BF84E42CC1B ___2_args, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Action_1__ctor_m8C819B4CABAFC405C1DEBA65B1E220746F258AB6_gshared (Action_1_t053CCF2809F9B0B61EA8298154533C5FACB724F0* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EventBus_Trigger_TisARTrackedImagesChangedEventArgs_tD7718E40EE25D22E9143DA4F556C44980E18BE2F_m543E103E3042925BB06EAC45E637282A71E1F853_gshared (String_t* ___0_name, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___1_target, ARTrackedImagesChangedEventArgs_tD7718E40EE25D22E9143DA4F556C44980E18BE2F ___2_args, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Action_1__ctor_m55BF5EC44D8DEC74D367B749B5B29C8F2A9CB957_gshared (Action_1_tE7B3D1070B99EFDFA92BD4DBEF95BF881DEB591F* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EventBus_Trigger_TisARTrackedObjectsChangedEventArgs_t378A63B91AD22EA4BA9C68805C72DDA4B7C01509_m2D44CB18B7DE92A8A28C2336767883E782C70CD8_gshared (String_t* ___0_name, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___1_target, ARTrackedObjectsChangedEventArgs_t378A63B91AD22EA4BA9C68805C72DDA4B7C01509 ___2_args, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Action_1__ctor_mBF52FA156452295382976743FE99EF09B9D21823_gshared (Action_1_t34AAB95B35DF66496184F8421A8AC5F317B7D8AB* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GetTrackablesUnit_6__ctor_m3AE000A7F3697AAB7BE4FF74B3F1B44FB61F723F_gshared (GetTrackablesUnit_6_tF13014734999E8A2395BF31956B595A732B18759* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GetTrackablesUnit_6__ctor_m57BA41B655EE5E6D80FFA1B27E8FAFDB7A9E1970_gshared (GetTrackablesUnit_6_tD4A938F1E77E1BAE7C41C15D337E459E3CB21C85* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GetTrackablesUnit_6__ctor_m0BC517A1466D742CE4C647BD284033CC11A8B376_gshared (GetTrackablesUnit_6_t7CDABC4A4629CF084D0C402FEABCB091691C0EC3* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GetTrackablesUnit_6__ctor_mFE6377E90C71DD4DB1780C5BDE5E5A91667D1FBB_gshared (GetTrackablesUnit_6_t394D5BEFC7488634A560E3C112D1AA5F0B2597B8* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GetTrackablesUnit_6__ctor_m4ACF2829E61A424E7F31E13E41D9D9DF158DAA3B_gshared (GetTrackablesUnit_6_t40700C5453679D51CCE89F1B99C1B4EBCF9F8144* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GetTrackablesUnit_6__ctor_m4A8A07763DB4937CE0573C3506EE437345CBBA0A_gshared (GetTrackablesUnit_6_t53BDD6CA0F569B84218F5FD0AE07A3F5A12B4681* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GetTrackablesUnit_6__ctor_mAE655F774D272F686546A115114DD2543A30E6A0_gshared (GetTrackablesUnit_6_tA252AFCA8D6AD9170BE371DD63B4E1A3D1DC456B* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GetTrackablesUnit_6__ctor_m08D7B92FD847DFB1385D59CCA674A532758E0D26_gshared (GetTrackablesUnit_6_tE93598F0E5891061730B627819A5B94E3EC137C8* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GetTrackablesUnit_6__ctor_m245C91BBB8F29976768E72A2DBD98FA1227FB902_gshared (GetTrackablesUnit_6_t5CCB2F47ECD9360ADBCC2038E60046BB7E753997* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GetTrackablesUnit_6__ctor_mC2A77B203229D56DD6BFEEAE238E1F727E36CA54_gshared (GetTrackablesUnit_6_tEE2BC0E2559CB40B4104C870DF982285700AAA7E* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Func_2__ctor_m7F8A01C0B02BC1D4063F4EB1E817F7A48562A398_gshared (Func_2_tACBF5A1656250800CE861707354491F0611F6624* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* Unit_ValueInput_TisInt32Enum_tCBAC8BA2BFF3A845FA599F303093BBBA374B6F0C_mEE7EB0BD491E883D13BCCD4BD8217D33D3384A48_gshared (Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191* __this, String_t* ___0_key, const RuntimeMethod* method) ;

IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B (RuntimeArray* ___0_array, RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 ___1_fldHandle, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2 (RuntimeObject* __this, const RuntimeMethod* method) ;
inline ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_added_mB590546762CE6A56E4C8B1EB8F24BD6CAA573B17_inline (TrackablesChangedEventUnit_8_t937DCA5FB2B32B242842AD0C1F2F5EE5F6ADF227* __this, const RuntimeMethod* method)
{
	return ((  ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* (*) (TrackablesChangedEventUnit_8_t937DCA5FB2B32B242842AD0C1F2F5EE5F6ADF227*, const RuntimeMethod*))TrackablesChangedEventUnit_8_get_added_mA5EB9599698C839DF1B830628E9AA712D2BAA239_gshared_inline)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_t5C2EBF017989E44F3D1D11AF502E10F6F8D9AB53* ARAnchorsChangedEventArgs_get_added_m5F9CEB4587F9496D0DA28880430B69EFDB550600_inline (ARAnchorsChangedEventArgs_t755E6208C84304CAC448C133B0A8660CD5750A75* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Flow_SetValue_m635BAF02F51D7C027C4F85157B80E2AB2DFB48A7 (Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* __this, RuntimeObject* ___0_port, RuntimeObject* ___1_value, const RuntimeMethod* method) ;
inline ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_updated_m4155AE83417E658B126553D3D5BE9E51691A2561_inline (TrackablesChangedEventUnit_8_t937DCA5FB2B32B242842AD0C1F2F5EE5F6ADF227* __this, const RuntimeMethod* method)
{
	return ((  ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* (*) (TrackablesChangedEventUnit_8_t937DCA5FB2B32B242842AD0C1F2F5EE5F6ADF227*, const RuntimeMethod*))TrackablesChangedEventUnit_8_get_updated_m0E9C84C6478DC8A8EC77951533068775F7773D47_gshared_inline)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_t5C2EBF017989E44F3D1D11AF502E10F6F8D9AB53* ARAnchorsChangedEventArgs_get_updated_m21039F14E472A75F969EE0BA5CBA9007DC12B593_inline (ARAnchorsChangedEventArgs_t755E6208C84304CAC448C133B0A8660CD5750A75* __this, const RuntimeMethod* method) ;
inline ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_removed_m00993034CD9C3DEBE2D7B5D8BCEF7D1F4AC6BB76_inline (TrackablesChangedEventUnit_8_t937DCA5FB2B32B242842AD0C1F2F5EE5F6ADF227* __this, const RuntimeMethod* method)
{
	return ((  ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* (*) (TrackablesChangedEventUnit_8_t937DCA5FB2B32B242842AD0C1F2F5EE5F6ADF227*, const RuntimeMethod*))TrackablesChangedEventUnit_8_get_removed_mFA20D5CC71D33EF2AD69130EE9B6DFC53C57826E_gshared_inline)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_t5C2EBF017989E44F3D1D11AF502E10F6F8D9AB53* ARAnchorsChangedEventArgs_get_removed_mF1E9D61E21153018C87DE99722FF8820C351CFD8_inline (ARAnchorsChangedEventArgs_t755E6208C84304CAC448C133B0A8660CD5750A75* __this, const RuntimeMethod* method) ;
inline void TrackablesChangedEventUnit_8__ctor_mA9A06C3435A47571B7483CD98FC6FC1F3938890F (TrackablesChangedEventUnit_8_t937DCA5FB2B32B242842AD0C1F2F5EE5F6ADF227* __this, const RuntimeMethod* method)
{
	((  void (*) (TrackablesChangedEventUnit_8_t937DCA5FB2B32B242842AD0C1F2F5EE5F6ADF227*, const RuntimeMethod*))TrackablesChangedEventUnit_8__ctor_m308C2AAFE4F05EE60FABC654F8DFCE4EEA97C952_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Type_t* Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57 (RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B ___0_handle, const RuntimeMethod* method) ;
inline void GameObjectEventUnit_1_Definition_m993A9F33D4AB3731B9BF811F804CA9BA0E75096E (GameObjectEventUnit_1_tA12F0B3D1C478378C11E341089186ED62766AF93* __this, const RuntimeMethod* method)
{
	((  void (*) (GameObjectEventUnit_1_tA12F0B3D1C478378C11E341089186ED62766AF93*, const RuntimeMethod*))GameObjectEventUnit_1_Definition_m993A9F33D4AB3731B9BF811F804CA9BA0E75096E_gshared)(__this, method);
}
inline ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* Unit_ValueOutput_TisARCameraFrameEventArgs_t4060608C37C2B7248AFC908876309CC36D53E0EC_m179CC70EB8D39BBFB29E25C4D83DB538FBF3C6FC (Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191* __this, String_t* ___0_key, const RuntimeMethod* method)
{
	return ((  ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* (*) (Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191*, String_t*, const RuntimeMethod*))Unit_ValueOutput_TisARCameraFrameEventArgs_t4060608C37C2B7248AFC908876309CC36D53E0EC_m179CC70EB8D39BBFB29E25C4D83DB538FBF3C6FC_gshared)(__this, ___0_key, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void CameraFrameReceivedEventUnit_set_frameEventArgs_m9D477159407F84279F1EF8E7619B871AA4F542FE_inline (CameraFrameReceivedEventUnit_t5C258A06DEEF9DBF3CE6AD4FD0B51313F1DEF858* __this, ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* CameraFrameReceivedEventUnit_get_frameEventArgs_m2CA34906D7D15DDD541EC38B7862B6CB0AB500E8_inline (CameraFrameReceivedEventUnit_t5C258A06DEEF9DBF3CE6AD4FD0B51313F1DEF858* __this, const RuntimeMethod* method) ;
inline void GameObjectEventUnit_1__ctor_mBA789D715816EDBF30E72C14CE07C03030B2FFD2 (GameObjectEventUnit_1_tA12F0B3D1C478378C11E341089186ED62766AF93* __this, const RuntimeMethod* method)
{
	((  void (*) (GameObjectEventUnit_1_tA12F0B3D1C478378C11E341089186ED62766AF93*, const RuntimeMethod*))GameObjectEventUnit_1__ctor_mBA789D715816EDBF30E72C14CE07C03030B2FFD2_gshared)(__this, method);
}
inline ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_added_mF1D86A14D162BBD5DC42FB80A7C0FA7BC769AF97_inline (TrackablesChangedEventUnit_8_t7E2C88EBC443BD6C1236769AD6E11B6822447A38* __this, const RuntimeMethod* method)
{
	return ((  ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* (*) (TrackablesChangedEventUnit_8_t7E2C88EBC443BD6C1236769AD6E11B6822447A38*, const RuntimeMethod*))TrackablesChangedEventUnit_8_get_added_m227082B23BA194D4F4D78E344F92D69797149EF2_gshared_inline)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_t6F14EE6179A9A8E042C5D65E902F72E24597029B* AREnvironmentProbesChangedEvent_get_added_m5EB8CAE059E5A0F0A84A11866F2343F630B7C997_inline (AREnvironmentProbesChangedEvent_t8757E98E11F3D37F0CFC4589FCA5CD2107F8375C* __this, const RuntimeMethod* method) ;
inline ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_updated_m64D143FBFBC17DC316E6C6F700D961D64B9E0AFE_inline (TrackablesChangedEventUnit_8_t7E2C88EBC443BD6C1236769AD6E11B6822447A38* __this, const RuntimeMethod* method)
{
	return ((  ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* (*) (TrackablesChangedEventUnit_8_t7E2C88EBC443BD6C1236769AD6E11B6822447A38*, const RuntimeMethod*))TrackablesChangedEventUnit_8_get_updated_m92262CE776B63637F7622CDECF3F5D1284E380E8_gshared_inline)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_t6F14EE6179A9A8E042C5D65E902F72E24597029B* AREnvironmentProbesChangedEvent_get_updated_mFFE292486A4D2DB4385C70FAE94EB6048CBB99F3_inline (AREnvironmentProbesChangedEvent_t8757E98E11F3D37F0CFC4589FCA5CD2107F8375C* __this, const RuntimeMethod* method) ;
inline ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_removed_m0AFD1317BBE14E4A88D0D90E190EA056ABD7DE2B_inline (TrackablesChangedEventUnit_8_t7E2C88EBC443BD6C1236769AD6E11B6822447A38* __this, const RuntimeMethod* method)
{
	return ((  ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* (*) (TrackablesChangedEventUnit_8_t7E2C88EBC443BD6C1236769AD6E11B6822447A38*, const RuntimeMethod*))TrackablesChangedEventUnit_8_get_removed_m3681946A08131E0DA77641ED3161001F7EC45ED0_gshared_inline)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_t6F14EE6179A9A8E042C5D65E902F72E24597029B* AREnvironmentProbesChangedEvent_get_removed_m0D5B42BCAD8408B1FC0DF82E24C4EAF1D8ADF40A_inline (AREnvironmentProbesChangedEvent_t8757E98E11F3D37F0CFC4589FCA5CD2107F8375C* __this, const RuntimeMethod* method) ;
inline void TrackablesChangedEventUnit_8__ctor_m4E7FEDDC8BF5DEFA8C29AC317BD812195B30A839 (TrackablesChangedEventUnit_8_t7E2C88EBC443BD6C1236769AD6E11B6822447A38* __this, const RuntimeMethod* method)
{
	((  void (*) (TrackablesChangedEventUnit_8_t7E2C88EBC443BD6C1236769AD6E11B6822447A38*, const RuntimeMethod*))TrackablesChangedEventUnit_8__ctor_mEFE6C2A5E0C130EC32B8A8A5F8D16784ED87A2C5_gshared)(__this, method);
}
inline ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_added_mD3E5CD7DD84402EC4A7054A36FF7678FB8A067F0_inline (TrackablesChangedEventUnit_8_t49CC6AB3D8E782C8141D1915452029E22C2213F0* __this, const RuntimeMethod* method)
{
	return ((  ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* (*) (TrackablesChangedEventUnit_8_t49CC6AB3D8E782C8141D1915452029E22C2213F0*, const RuntimeMethod*))TrackablesChangedEventUnit_8_get_added_m1C11AC657CA855142D543432ABC974CCADE0B3B0_gshared_inline)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_tA11EBED64210DD13BB4303415D948E6CD964AAF9* ARFacesChangedEventArgs_get_added_m8EA1424F8F262955AFD00664B81C15AB54EFA213_inline (ARFacesChangedEventArgs_tC05D4423CB2096FF5955C3A0A00056EE2B6301AE* __this, const RuntimeMethod* method) ;
inline ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_updated_m2B11703016B3C1EB9A5003ACC43AA9507300A5B5_inline (TrackablesChangedEventUnit_8_t49CC6AB3D8E782C8141D1915452029E22C2213F0* __this, const RuntimeMethod* method)
{
	return ((  ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* (*) (TrackablesChangedEventUnit_8_t49CC6AB3D8E782C8141D1915452029E22C2213F0*, const RuntimeMethod*))TrackablesChangedEventUnit_8_get_updated_m934BC27B2518C2AF1097361B2834B91346245905_gshared_inline)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_tA11EBED64210DD13BB4303415D948E6CD964AAF9* ARFacesChangedEventArgs_get_updated_m1D3587AA05E5789F796A71C3C87CD6D11BE0005B_inline (ARFacesChangedEventArgs_tC05D4423CB2096FF5955C3A0A00056EE2B6301AE* __this, const RuntimeMethod* method) ;
inline ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_removed_m1D98456EEB97142B45C4EE15B8443BA8499F97CB_inline (TrackablesChangedEventUnit_8_t49CC6AB3D8E782C8141D1915452029E22C2213F0* __this, const RuntimeMethod* method)
{
	return ((  ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* (*) (TrackablesChangedEventUnit_8_t49CC6AB3D8E782C8141D1915452029E22C2213F0*, const RuntimeMethod*))TrackablesChangedEventUnit_8_get_removed_mB2360A7E90332B148E3297E1D32F6031E3E8DEFB_gshared_inline)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_tA11EBED64210DD13BB4303415D948E6CD964AAF9* ARFacesChangedEventArgs_get_removed_mC31806DE526D3BECAA26C35DAE321F2DE77C335E_inline (ARFacesChangedEventArgs_tC05D4423CB2096FF5955C3A0A00056EE2B6301AE* __this, const RuntimeMethod* method) ;
inline void TrackablesChangedEventUnit_8__ctor_mAA11F3EE9A29DA1A20480633CA21003789A43AC2 (TrackablesChangedEventUnit_8_t49CC6AB3D8E782C8141D1915452029E22C2213F0* __this, const RuntimeMethod* method)
{
	((  void (*) (TrackablesChangedEventUnit_8_t49CC6AB3D8E782C8141D1915452029E22C2213F0*, const RuntimeMethod*))TrackablesChangedEventUnit_8__ctor_mEFC05B21043F169E4165A60158BB4EBCE0825918_gshared)(__this, method);
}
inline void GameObjectEventUnit_1_Definition_mF8BEB75340AEF0E43AD3F4BC254109BD339F4E09 (GameObjectEventUnit_1_tD98F9076C76336C1A30A327C151C4BC55CBE45FE* __this, const RuntimeMethod* method)
{
	((  void (*) (GameObjectEventUnit_1_tD98F9076C76336C1A30A327C151C4BC55CBE45FE*, const RuntimeMethod*))GameObjectEventUnit_1_Definition_mF8BEB75340AEF0E43AD3F4BC254109BD339F4E09_gshared)(__this, method);
}
inline ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* Unit_ValueOutput_TisARFace_t73C068C9C8422F79A858EC41C322D399305B8453_mC94C38A35E16A3EA152B5E1D430FC6D2419FD31E (Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191* __this, String_t* ___0_key, const RuntimeMethod* method)
{
	return ((  ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* (*) (Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191*, String_t*, const RuntimeMethod*))Unit_ValueOutput_TisRuntimeObject_mD512E6E92372FB88500D21640EAA0F24A3468C51_gshared)(__this, ___0_key, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void FaceUpdatedEventUnit_set_faceOut_mA1675257B969683B3E210624EFADE5F0F24A4811_inline (FaceUpdatedEventUnit_tBAF617E9D7CA035FC4D40FDA435F6BC35A39D426* __this, ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* FaceUpdatedEventUnit_get_faceOut_m4C064F7BE0FDC5D28BAB8C72683F60B5399B091D_inline (FaceUpdatedEventUnit_tBAF617E9D7CA035FC4D40FDA435F6BC35A39D426* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ARFace_t73C068C9C8422F79A858EC41C322D399305B8453* ARFaceUpdatedEventArgs_get_face_mFD21EF59457212494C80FE998D1CA8AC0F057170_inline (ARFaceUpdatedEventArgs_tC20D17255858678A1A8230EA32D7C38287221FF6* __this, const RuntimeMethod* method) ;
inline void GameObjectEventUnit_1__ctor_m12AD4CC830381744782178A62EDD22E8BF924894 (GameObjectEventUnit_1_tD98F9076C76336C1A30A327C151C4BC55CBE45FE* __this, const RuntimeMethod* method)
{
	((  void (*) (GameObjectEventUnit_1_tD98F9076C76336C1A30A327C151C4BC55CBE45FE*, const RuntimeMethod*))GameObjectEventUnit_1__ctor_m12AD4CC830381744782178A62EDD22E8BF924894_gshared)(__this, method);
}
inline ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_added_mB2970C7A1E0D78B2D6C4EE5E9DF55E8FB61F0414_inline (TrackablesChangedEventUnit_8_tBEDFC346D94F264473B89BF51CB942BCC3D7A3BC* __this, const RuntimeMethod* method)
{
	return ((  ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* (*) (TrackablesChangedEventUnit_8_tBEDFC346D94F264473B89BF51CB942BCC3D7A3BC*, const RuntimeMethod*))TrackablesChangedEventUnit_8_get_added_m33DF3083F2E514B2F73286CA73AE45BA5A181D54_gshared_inline)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_t64B1B4ABB789350AE017F3EAF4D3647AF59244C7* ARHumanBodiesChangedEventArgs_get_added_m6FD862CC8C8281F9377D754BBAB7F3C616955D4C_inline (ARHumanBodiesChangedEventArgs_t19088E0D3384A22C5D8A542957705222C834720B* __this, const RuntimeMethod* method) ;
inline ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_updated_mD57DCBD8B6B61E09CC5E8AE90F6D728B4B99F008_inline (TrackablesChangedEventUnit_8_tBEDFC346D94F264473B89BF51CB942BCC3D7A3BC* __this, const RuntimeMethod* method)
{
	return ((  ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* (*) (TrackablesChangedEventUnit_8_tBEDFC346D94F264473B89BF51CB942BCC3D7A3BC*, const RuntimeMethod*))TrackablesChangedEventUnit_8_get_updated_m92AC4CE3FFB5D2CD7EF4EFEC9FCBA7BD5429FA18_gshared_inline)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_t64B1B4ABB789350AE017F3EAF4D3647AF59244C7* ARHumanBodiesChangedEventArgs_get_updated_mA2FD128E22BDACD09294F107AA3F2A91CFFAA550_inline (ARHumanBodiesChangedEventArgs_t19088E0D3384A22C5D8A542957705222C834720B* __this, const RuntimeMethod* method) ;
inline ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_removed_m8F35AD75A58329A64455EA693E3AB62EDAA066C9_inline (TrackablesChangedEventUnit_8_tBEDFC346D94F264473B89BF51CB942BCC3D7A3BC* __this, const RuntimeMethod* method)
{
	return ((  ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* (*) (TrackablesChangedEventUnit_8_tBEDFC346D94F264473B89BF51CB942BCC3D7A3BC*, const RuntimeMethod*))TrackablesChangedEventUnit_8_get_removed_m730B768E99F9428C523540CFEAFD4ADC88B3DCBB_gshared_inline)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_t64B1B4ABB789350AE017F3EAF4D3647AF59244C7* ARHumanBodiesChangedEventArgs_get_removed_m613516A989AA48A625C79A3C309D9C1465762BCA_inline (ARHumanBodiesChangedEventArgs_t19088E0D3384A22C5D8A542957705222C834720B* __this, const RuntimeMethod* method) ;
inline void TrackablesChangedEventUnit_8__ctor_m1F8C079F96721DC237C11DA4A5D51E8B6FF363D6 (TrackablesChangedEventUnit_8_tBEDFC346D94F264473B89BF51CB942BCC3D7A3BC* __this, const RuntimeMethod* method)
{
	((  void (*) (TrackablesChangedEventUnit_8_tBEDFC346D94F264473B89BF51CB942BCC3D7A3BC*, const RuntimeMethod*))TrackablesChangedEventUnit_8__ctor_mA74332C28A8F0F4BB4E7B9C363DE47DB4F5A960A_gshared)(__this, method);
}
inline ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_added_mC83DA1B5E5EEB690C2E89C2703CADE9E51A740C7_inline (TrackablesChangedEventUnit_8_tD6C8D29C0AC7F5F97EF5642B923DFE86702323F8* __this, const RuntimeMethod* method)
{
	return ((  ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* (*) (TrackablesChangedEventUnit_8_tD6C8D29C0AC7F5F97EF5642B923DFE86702323F8*, const RuntimeMethod*))TrackablesChangedEventUnit_8_get_added_mE06A697E13C4142D6E80237E44FF471D37AA81A8_gshared_inline)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_t87D9AC2D6B4A4860E01575669DEB0D164D4B33B3* ARParticipantsChangedEventArgs_get_added_m06DA0DBF00575BB5D41A8AFE385ADAB9866BA678_inline (ARParticipantsChangedEventArgs_t2EAD9041A074A7ACEFF280662784747A4E85E527* __this, const RuntimeMethod* method) ;
inline ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_updated_mAE3D67A922C0F1E37CC29792138000F23621BCDD_inline (TrackablesChangedEventUnit_8_tD6C8D29C0AC7F5F97EF5642B923DFE86702323F8* __this, const RuntimeMethod* method)
{
	return ((  ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* (*) (TrackablesChangedEventUnit_8_tD6C8D29C0AC7F5F97EF5642B923DFE86702323F8*, const RuntimeMethod*))TrackablesChangedEventUnit_8_get_updated_mD5811FDA361D62C2B442A29B4271C140BD68FD4D_gshared_inline)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_t87D9AC2D6B4A4860E01575669DEB0D164D4B33B3* ARParticipantsChangedEventArgs_get_updated_mA0EC697655BDFEEB28064579FDD67DD166A2AE65_inline (ARParticipantsChangedEventArgs_t2EAD9041A074A7ACEFF280662784747A4E85E527* __this, const RuntimeMethod* method) ;
inline ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_removed_mA9FEE7FB7606E374551FE70A1B72D8199E237F70_inline (TrackablesChangedEventUnit_8_tD6C8D29C0AC7F5F97EF5642B923DFE86702323F8* __this, const RuntimeMethod* method)
{
	return ((  ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* (*) (TrackablesChangedEventUnit_8_tD6C8D29C0AC7F5F97EF5642B923DFE86702323F8*, const RuntimeMethod*))TrackablesChangedEventUnit_8_get_removed_mA5A75FD646439C5DF03447808BAC63B942DDE510_gshared_inline)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_t87D9AC2D6B4A4860E01575669DEB0D164D4B33B3* ARParticipantsChangedEventArgs_get_removed_m6BBF2ABF2E71DB02A70122927AAD83E3AB35A77F_inline (ARParticipantsChangedEventArgs_t2EAD9041A074A7ACEFF280662784747A4E85E527* __this, const RuntimeMethod* method) ;
inline void TrackablesChangedEventUnit_8__ctor_m924504E4BCE32657BE00FDA2558786DE79FE541C (TrackablesChangedEventUnit_8_tD6C8D29C0AC7F5F97EF5642B923DFE86702323F8* __this, const RuntimeMethod* method)
{
	((  void (*) (TrackablesChangedEventUnit_8_tD6C8D29C0AC7F5F97EF5642B923DFE86702323F8*, const RuntimeMethod*))TrackablesChangedEventUnit_8__ctor_m912FDF38ED39EC6B4DA92031ED83F57086E5FD51_gshared)(__this, method);
}
inline ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_added_m69D43E4E5EE721CF125D2EE3539284002A700B74_inline (TrackablesChangedEventUnit_8_tA9240E148EC702D556AA0394A8591794B81864EE* __this, const RuntimeMethod* method)
{
	return ((  ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* (*) (TrackablesChangedEventUnit_8_tA9240E148EC702D556AA0394A8591794B81864EE*, const RuntimeMethod*))TrackablesChangedEventUnit_8_get_added_mBC80FDCEB43A815F5C1E00246B3A0A76BEBF6A55_gshared_inline)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_t10BA05B555C92BD54800DFF82DCAAFC9DE44A077* ARPlanesChangedEventArgs_get_added_m6E00DD5F0B3261BCBAA8A029924A1F3F4179C747_inline (ARPlanesChangedEventArgs_t8D63E0257BF9942EF8F8C0445F2FD46421017872* __this, const RuntimeMethod* method) ;
inline ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_updated_m990FD05AD7C047671F91787AFD37A2C9E856E2F7_inline (TrackablesChangedEventUnit_8_tA9240E148EC702D556AA0394A8591794B81864EE* __this, const RuntimeMethod* method)
{
	return ((  ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* (*) (TrackablesChangedEventUnit_8_tA9240E148EC702D556AA0394A8591794B81864EE*, const RuntimeMethod*))TrackablesChangedEventUnit_8_get_updated_mE28D5A4C9A9AC30D6587B7C701797F9D89EC4D3C_gshared_inline)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_t10BA05B555C92BD54800DFF82DCAAFC9DE44A077* ARPlanesChangedEventArgs_get_updated_mE979DAD93445C56FF7BEB732528D6CADAF0B6C22_inline (ARPlanesChangedEventArgs_t8D63E0257BF9942EF8F8C0445F2FD46421017872* __this, const RuntimeMethod* method) ;
inline ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_removed_m53656F8942539C9E8684FF4379871CFC068B1A19_inline (TrackablesChangedEventUnit_8_tA9240E148EC702D556AA0394A8591794B81864EE* __this, const RuntimeMethod* method)
{
	return ((  ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* (*) (TrackablesChangedEventUnit_8_tA9240E148EC702D556AA0394A8591794B81864EE*, const RuntimeMethod*))TrackablesChangedEventUnit_8_get_removed_m72E4C6109F83CDAFC1C03A9E7FA7F9F421F40D2D_gshared_inline)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_t10BA05B555C92BD54800DFF82DCAAFC9DE44A077* ARPlanesChangedEventArgs_get_removed_m8CF2AA0278488746F8C55CC6C7DC1870531D63C8_inline (ARPlanesChangedEventArgs_t8D63E0257BF9942EF8F8C0445F2FD46421017872* __this, const RuntimeMethod* method) ;
inline void TrackablesChangedEventUnit_8__ctor_m832218ED199936D78FC2DCAE7AFB8F4A557D210C (TrackablesChangedEventUnit_8_tA9240E148EC702D556AA0394A8591794B81864EE* __this, const RuntimeMethod* method)
{
	((  void (*) (TrackablesChangedEventUnit_8_tA9240E148EC702D556AA0394A8591794B81864EE*, const RuntimeMethod*))TrackablesChangedEventUnit_8__ctor_m21F8D2C2250F02424F218D114020A428B8DB2FBD_gshared)(__this, method);
}
inline ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_added_m7BC749EABE3673D303FAB3CAA1C3746AE97E71A3_inline (TrackablesChangedEventUnit_8_tEB3E8F04C93B5D5BA458A74D225A0598224B0211* __this, const RuntimeMethod* method)
{
	return ((  ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* (*) (TrackablesChangedEventUnit_8_tEB3E8F04C93B5D5BA458A74D225A0598224B0211*, const RuntimeMethod*))TrackablesChangedEventUnit_8_get_added_mDFD7A4FA5EE6EE922E003F4F32AF1E97E8532D0B_gshared_inline)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_tB9CD639EB4FE5EA0A8B417168BB9BD2D29AEE271* ARPointCloudChangedEventArgs_get_added_m74DFD8ED4B3EADB5AC29DD0E748C84C6FA26DDE9_inline (ARPointCloudChangedEventArgs_t7DE4CA4D30D369496221061F89CC4BF84E42CC1B* __this, const RuntimeMethod* method) ;
inline ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_updated_mB0E1D4283CE77BC4F2A15B4BCF2351BB08321517_inline (TrackablesChangedEventUnit_8_tEB3E8F04C93B5D5BA458A74D225A0598224B0211* __this, const RuntimeMethod* method)
{
	return ((  ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* (*) (TrackablesChangedEventUnit_8_tEB3E8F04C93B5D5BA458A74D225A0598224B0211*, const RuntimeMethod*))TrackablesChangedEventUnit_8_get_updated_mF386F24BC1B82AD93FDFA090C7A6032FD1835BC9_gshared_inline)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_tB9CD639EB4FE5EA0A8B417168BB9BD2D29AEE271* ARPointCloudChangedEventArgs_get_updated_mD69E67FE8B06AB44B15F42F1D6EFA91F7E58F7C6_inline (ARPointCloudChangedEventArgs_t7DE4CA4D30D369496221061F89CC4BF84E42CC1B* __this, const RuntimeMethod* method) ;
inline ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_removed_m91B8BEF101F7958D60725FFFA15EF7D255467B59_inline (TrackablesChangedEventUnit_8_tEB3E8F04C93B5D5BA458A74D225A0598224B0211* __this, const RuntimeMethod* method)
{
	return ((  ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* (*) (TrackablesChangedEventUnit_8_tEB3E8F04C93B5D5BA458A74D225A0598224B0211*, const RuntimeMethod*))TrackablesChangedEventUnit_8_get_removed_mD34EBC8EA96EFAA5E5B3BB3A5CD9E63F45C8F46C_gshared_inline)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_tB9CD639EB4FE5EA0A8B417168BB9BD2D29AEE271* ARPointCloudChangedEventArgs_get_removed_m64321D4F0D902494EE19EFF1B258981DE5C712B0_inline (ARPointCloudChangedEventArgs_t7DE4CA4D30D369496221061F89CC4BF84E42CC1B* __this, const RuntimeMethod* method) ;
inline void TrackablesChangedEventUnit_8__ctor_m2BD406EFCF14C551D318E6422DE19A60B50B4B0E (TrackablesChangedEventUnit_8_tEB3E8F04C93B5D5BA458A74D225A0598224B0211* __this, const RuntimeMethod* method)
{
	((  void (*) (TrackablesChangedEventUnit_8_tEB3E8F04C93B5D5BA458A74D225A0598224B0211*, const RuntimeMethod*))TrackablesChangedEventUnit_8__ctor_m7683A2F3073EB853369AA59B04773E2A2D60FE18_gshared)(__this, method);
}
inline void EventUnit_1_StartListening_m988584D9E3245AAED2407FA0A7C0700E827FCAA8 (EventUnit_1_tA2B0E6295E58DB46B38F944529B0DF5E40ABF917* __this, GraphStack_tD9049F5FB5F8D9E9CD80764D18067D0F87343E7C* ___0_stack, const RuntimeMethod* method)
{
	((  void (*) (EventUnit_1_tA2B0E6295E58DB46B38F944529B0DF5E40ABF917*, GraphStack_tD9049F5FB5F8D9E9CD80764D18067D0F87343E7C*, const RuntimeMethod*))EventUnit_1_StartListening_mB6D9B402D7950DAAA250458B8028EAEE5DAD3D10_gshared)(__this, ___0_stack, method);
}
inline void Action_1__ctor_mC9CFD5CBE62E447D22A4A520EB27144DCD47BBDB (Action_1_t325C602D999E964DB72210E493635E83F8D6CE68* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Action_1_t325C602D999E964DB72210E493635E83F8D6CE68*, RuntimeObject*, intptr_t, const RuntimeMethod*))Action_1__ctor_mC9CFD5CBE62E447D22A4A520EB27144DCD47BBDB_gshared)(__this, ___0_object, ___1_method, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARSession_add_stateChanged_mD40004D1EBE89E6CAC651632ABB8EADBE46692FC (Action_1_t325C602D999E964DB72210E493635E83F8D6CE68* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARSession_remove_stateChanged_mDAC5A2DCA86363C495C66FB8CA92F46ABDFE3BE1 (Action_1_t325C602D999E964DB72210E493635E83F8D6CE68* ___0_value, const RuntimeMethod* method) ;
inline void EventUnit_1_StopListening_m3291AC7B5A0B2228C69FF95A85772282389B48A6 (EventUnit_1_tA2B0E6295E58DB46B38F944529B0DF5E40ABF917* __this, GraphStack_tD9049F5FB5F8D9E9CD80764D18067D0F87343E7C* ___0_stack, const RuntimeMethod* method)
{
	((  void (*) (EventUnit_1_tA2B0E6295E58DB46B38F944529B0DF5E40ABF917*, GraphStack_tD9049F5FB5F8D9E9CD80764D18067D0F87343E7C*, const RuntimeMethod*))EventUnit_1_StopListening_mAC749B19F0503A6FB1237AD485CABA86D5B793A0_gshared)(__this, ___0_stack, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR EventHook_t1C9E2570B55940C342214BACB5E9EC889A113157 EventHook_op_Implicit_m93EAC8789A8E846D55B193CB46237A48C54693FA (String_t* ___0_name, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t ARSessionStateChangedEventArgs_get_state_mC0A6FB4AF08C068BFD5D9730F60215BE662A950B_inline (ARSessionStateChangedEventArgs_tC31BD123D6B4F6168FB246762E79AB33962AE822* __this, const RuntimeMethod* method) ;
inline void EventBus_Trigger_TisARSessionState_tAAADC649886D67F7134A0D9A665953D764A115D7_mE75403E46DB2EDDF96E1BF00CAB17E2C12C9BE79 (EventHook_t1C9E2570B55940C342214BACB5E9EC889A113157 ___0_hook, int32_t ___1_args, const RuntimeMethod* method)
{
	((  void (*) (EventHook_t1C9E2570B55940C342214BACB5E9EC889A113157, int32_t, const RuntimeMethod*))EventBus_Trigger_TisInt32Enum_tCBAC8BA2BFF3A845FA599F303093BBBA374B6F0C_m25AF2BE51B827FCEFEC52FEA8C9AD690479DE187_gshared)(___0_hook, ___1_args, method);
}
inline void EventUnit_1_Definition_m4F542628DD546D8F60E22C3A609D7E4D9A980F8D (EventUnit_1_tA2B0E6295E58DB46B38F944529B0DF5E40ABF917* __this, const RuntimeMethod* method)
{
	((  void (*) (EventUnit_1_tA2B0E6295E58DB46B38F944529B0DF5E40ABF917*, const RuntimeMethod*))EventUnit_1_Definition_mB107EA1B3A9052C1433EF183A8C0D16C0A24E7DD_gshared)(__this, method);
}
inline ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* Unit_ValueOutput_TisARSessionState_tAAADC649886D67F7134A0D9A665953D764A115D7_m4BE5C8B6E8E7823F32C58B256BA74C4C5956BF1E (Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191* __this, String_t* ___0_key, const RuntimeMethod* method)
{
	return ((  ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* (*) (Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191*, String_t*, const RuntimeMethod*))Unit_ValueOutput_TisInt32Enum_tCBAC8BA2BFF3A845FA599F303093BBBA374B6F0C_mD272EBBBB2B0A4187CBDF3C90913AF8D050C566E_gshared)(__this, ___0_key, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void SessionStateChangedEventUnit_set_sessionStateOut_mA37AE57139361EFD0003F0FF225B9A917359F04C_inline (SessionStateChangedEventUnit_tB622F7C1B352062C2D5D67A3EA1E1E6DBD910631* __this, ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* SessionStateChangedEventUnit_get_sessionStateOut_m0A6C8851E8BE80C79AEFAE297A6B8A47F68445B9_inline (SessionStateChangedEventUnit_tB622F7C1B352062C2D5D67A3EA1E1E6DBD910631* __this, const RuntimeMethod* method) ;
inline void GlobalEventUnit_1__ctor_m8D2A36FE71F45881F8560975E9F957C36A08539E (GlobalEventUnit_1_tFCC2A75C905819911151CA0BD2FEE0E3A24D5FE6* __this, const RuntimeMethod* method)
{
	((  void (*) (GlobalEventUnit_1_tFCC2A75C905819911151CA0BD2FEE0E3A24D5FE6*, const RuntimeMethod*))GlobalEventUnit_1__ctor_m8C8326D5D9025D0E62A11522CD3915AB44006289_gshared)(__this, method);
}
inline ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_added_m98C7465E257E576752368FE41D6911A596B9BD08_inline (TrackablesChangedEventUnit_8_t9B0C0052BA572E6D5D816B31AE40C31151AB58B2* __this, const RuntimeMethod* method)
{
	return ((  ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* (*) (TrackablesChangedEventUnit_8_t9B0C0052BA572E6D5D816B31AE40C31151AB58B2*, const RuntimeMethod*))TrackablesChangedEventUnit_8_get_added_m875B59231345FF1E6FBFE1E5EFF593995A2EF71C_gshared_inline)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_tFA730335FF46512E4245B2F46892CA318B394FAE* ARTrackedImagesChangedEventArgs_get_added_m2929CC85141D13AF05C1484AFB47E043C6D3EE35_inline (ARTrackedImagesChangedEventArgs_tD7718E40EE25D22E9143DA4F556C44980E18BE2F* __this, const RuntimeMethod* method) ;
inline ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_updated_mDAC4907DBB50BF9C8B7A68F589F2827CB92E054C_inline (TrackablesChangedEventUnit_8_t9B0C0052BA572E6D5D816B31AE40C31151AB58B2* __this, const RuntimeMethod* method)
{
	return ((  ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* (*) (TrackablesChangedEventUnit_8_t9B0C0052BA572E6D5D816B31AE40C31151AB58B2*, const RuntimeMethod*))TrackablesChangedEventUnit_8_get_updated_m362E1C8FE77AB6DF95D605D202C2F2D2E51EBDA2_gshared_inline)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_tFA730335FF46512E4245B2F46892CA318B394FAE* ARTrackedImagesChangedEventArgs_get_updated_m0C896E1C21EF35FF4B31B36563838EC2BA3CDFD1_inline (ARTrackedImagesChangedEventArgs_tD7718E40EE25D22E9143DA4F556C44980E18BE2F* __this, const RuntimeMethod* method) ;
inline ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_removed_m0519BF012BE3FC785FF687EA5655A30E7B566A67_inline (TrackablesChangedEventUnit_8_t9B0C0052BA572E6D5D816B31AE40C31151AB58B2* __this, const RuntimeMethod* method)
{
	return ((  ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* (*) (TrackablesChangedEventUnit_8_t9B0C0052BA572E6D5D816B31AE40C31151AB58B2*, const RuntimeMethod*))TrackablesChangedEventUnit_8_get_removed_m81EDFBA98C36006A10B4B9EB3DC52C4E073EE35F_gshared_inline)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_tFA730335FF46512E4245B2F46892CA318B394FAE* ARTrackedImagesChangedEventArgs_get_removed_m062CBBEF163BEE47A673F7B3BDC0DD1C6EAEA185_inline (ARTrackedImagesChangedEventArgs_tD7718E40EE25D22E9143DA4F556C44980E18BE2F* __this, const RuntimeMethod* method) ;
inline void TrackablesChangedEventUnit_8__ctor_m894F00CFC3DAF8C67CBB694581F43FB8D07DA5DE (TrackablesChangedEventUnit_8_t9B0C0052BA572E6D5D816B31AE40C31151AB58B2* __this, const RuntimeMethod* method)
{
	((  void (*) (TrackablesChangedEventUnit_8_t9B0C0052BA572E6D5D816B31AE40C31151AB58B2*, const RuntimeMethod*))TrackablesChangedEventUnit_8__ctor_m5D8E2CA88711C7F299D1A74DB9861F5178DA7294_gshared)(__this, method);
}
inline ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_added_m12B5AD020EE9860130BC183B984643D5242089D9_inline (TrackablesChangedEventUnit_8_t421739B4DF339B26394C3BAFA5BEDF14102691BB* __this, const RuntimeMethod* method)
{
	return ((  ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* (*) (TrackablesChangedEventUnit_8_t421739B4DF339B26394C3BAFA5BEDF14102691BB*, const RuntimeMethod*))TrackablesChangedEventUnit_8_get_added_m56E2AAE2F7EB5F918BCA351928FA830CFC40B00C_gshared_inline)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_tF1201F2F725A8AE529D436F38A4771FB6790F3C6* ARTrackedObjectsChangedEventArgs_get_added_m71E8E2055C979657273051DE23FCEB938964169A_inline (ARTrackedObjectsChangedEventArgs_t378A63B91AD22EA4BA9C68805C72DDA4B7C01509* __this, const RuntimeMethod* method) ;
inline ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_updated_m2C3941B76B820CCC7161CA63CDCB10D6FBA498F1_inline (TrackablesChangedEventUnit_8_t421739B4DF339B26394C3BAFA5BEDF14102691BB* __this, const RuntimeMethod* method)
{
	return ((  ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* (*) (TrackablesChangedEventUnit_8_t421739B4DF339B26394C3BAFA5BEDF14102691BB*, const RuntimeMethod*))TrackablesChangedEventUnit_8_get_updated_mB47F00BDB8F28A8C052BF4525824557AA2D0E5F2_gshared_inline)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_tF1201F2F725A8AE529D436F38A4771FB6790F3C6* ARTrackedObjectsChangedEventArgs_get_updated_m01050D519E6281EECB48200D952FE1E1FFFE1A60_inline (ARTrackedObjectsChangedEventArgs_t378A63B91AD22EA4BA9C68805C72DDA4B7C01509* __this, const RuntimeMethod* method) ;
inline ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_removed_m9C8736E9632CDA45DADC1A4FB415361148A8A543_inline (TrackablesChangedEventUnit_8_t421739B4DF339B26394C3BAFA5BEDF14102691BB* __this, const RuntimeMethod* method)
{
	return ((  ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* (*) (TrackablesChangedEventUnit_8_t421739B4DF339B26394C3BAFA5BEDF14102691BB*, const RuntimeMethod*))TrackablesChangedEventUnit_8_get_removed_m0F3958FFA505957614CEE2F8AAD2A1BBA5641D5C_gshared_inline)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_tF1201F2F725A8AE529D436F38A4771FB6790F3C6* ARTrackedObjectsChangedEventArgs_get_removed_mDF5FA585D41D4B0D4517D3C5387B254F6EA6DFDA_inline (ARTrackedObjectsChangedEventArgs_t378A63B91AD22EA4BA9C68805C72DDA4B7C01509* __this, const RuntimeMethod* method) ;
inline void TrackablesChangedEventUnit_8__ctor_m6D98CBE8151864AF5BBBDA138AE0D0E8DAAD995C (TrackablesChangedEventUnit_8_t421739B4DF339B26394C3BAFA5BEDF14102691BB* __this, const RuntimeMethod* method)
{
	((  void (*) (TrackablesChangedEventUnit_8_t421739B4DF339B26394C3BAFA5BEDF14102691BB*, const RuntimeMethod*))TrackablesChangedEventUnit_8__ctor_mCF3C7EDCB1F243B429E2A0F797E31068172458A1_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method) ;
inline void EventBus_Trigger_TisARAnchorsChangedEventArgs_t755E6208C84304CAC448C133B0A8660CD5750A75_mE9B6D9EEA77C3264949C5BA781F1278767B8D982 (String_t* ___0_name, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___1_target, ARAnchorsChangedEventArgs_t755E6208C84304CAC448C133B0A8660CD5750A75 ___2_args, const RuntimeMethod* method)
{
	((  void (*) (String_t*, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F*, ARAnchorsChangedEventArgs_t755E6208C84304CAC448C133B0A8660CD5750A75, const RuntimeMethod*))EventBus_Trigger_TisARAnchorsChangedEventArgs_t755E6208C84304CAC448C133B0A8660CD5750A75_mE9B6D9EEA77C3264949C5BA781F1278767B8D982_gshared)(___0_name, ___1_target, ___2_args, method);
}
inline void Action_1__ctor_m27F591D84CB51009686E04C84ED35045DB8DC4B1 (Action_1_t11340E5174173030E076A75316E18A395082F8A5* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Action_1_t11340E5174173030E076A75316E18A395082F8A5*, RuntimeObject*, intptr_t, const RuntimeMethod*))Action_1__ctor_m27F591D84CB51009686E04C84ED35045DB8DC4B1_gshared)(__this, ___0_object, ___1_method, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARAnchorManager_add_anchorsChanged_m9AEAD2307BB09DCC5093D8BAD242AAED777CC5D0 (ARAnchorManager_tF68C46300BD6F2E83E3360BF9297C3C50F5D6B3E* __this, Action_1_t11340E5174173030E076A75316E18A395082F8A5* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARAnchorManager_remove_anchorsChanged_mF59C4EA911963F93B03E01D799007FDD18E7B0D6 (ARAnchorManager_tF68C46300BD6F2E83E3360BF9297C3C50F5D6B3E* __this, Action_1_t11340E5174173030E076A75316E18A395082F8A5* ___0_value, const RuntimeMethod* method) ;
inline void TrackableManagerListener_1__ctor_m9D5A585C61BEE09064B5EB54CC3D5DF04A38A279 (TrackableManagerListener_1_t368727D76E305857E6524137ED96D00A18546BAC* __this, const RuntimeMethod* method)
{
	((  void (*) (TrackableManagerListener_1_t368727D76E305857E6524137ED96D00A18546BAC*, const RuntimeMethod*))TrackableManagerListener_1__ctor_mA005DBA59FBCA1E0EC60AA2B1B1D93527527ABF2_gshared)(__this, method);
}
inline ARCameraManager_t6E957EA922911B8447D086D9FB1F860EC967BD9F* Component_GetComponent_TisARCameraManager_t6E957EA922911B8447D086D9FB1F860EC967BD9F_mD0C05CBA43EFDC472C6C4BB588C366709A88B182 (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method)
{
	return ((  ARCameraManager_t6E957EA922911B8447D086D9FB1F860EC967BD9F* (*) (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3*, const RuntimeMethod*))Component_GetComponent_TisRuntimeObject_m7181F81CAEC2CF53F5D2BC79B7425C16E1F80D33_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605 (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___0_x, Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___1_y, const RuntimeMethod* method) ;
inline ARCameraManager_t6E957EA922911B8447D086D9FB1F860EC967BD9F* Object_FindObjectOfType_TisARCameraManager_t6E957EA922911B8447D086D9FB1F860EC967BD9F_mF55990D2494651CA4DEEE92470F8B341AFF1A089 (const RuntimeMethod* method)
{
	return ((  ARCameraManager_t6E957EA922911B8447D086D9FB1F860EC967BD9F* (*) (const RuntimeMethod*))Object_FindObjectOfType_TisRuntimeObject_m02DFBF011F3B59F777A5E521DB2A116DD496E968_gshared)(method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Type_t* Object_GetType_mE10A8FC1E57F3DF29972CCBC026C2DC3942263B3 (RuntimeObject* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Concat_m9E3155FB84015C823606188F53B47CB44C444991 (String_t* ___0_str0, String_t* ___1_str1, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InvalidOperationException__ctor_mE4CB6F4712AB6D99A2358FBAE2E052B3EE976162 (InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB* __this, String_t* ___0_message, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR StringBuilder_t* StringBuilder_Append_m08904D74E0C78E5F36DCD9C9303BDD07886D9F7D (StringBuilder_t* __this, String_t* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Object_get_name_mAC2F6B897CF1303BA4249B4CB55271AFACBB6392 (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Concat_m8855A6DE10F84DA7F4EC113CADDB59873A25573B (String_t* ___0_str0, String_t* ___1_str1, String_t* ___2_str2, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Concat_m093934F71A9B351911EE46311674ED463B180006 (String_t* ___0_str0, String_t* ___1_str1, String_t* ___2_str2, String_t* ___3_str3, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Debug_LogWarning_m33EF1B897E0C7C6FF538989610BFAFFEF4628CA9 (RuntimeObject* ___0_message, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR StringBuilder_t* StringBuilder_Clear_m2D76F6533574F40A4E3E2DC4E730277CBD0AF8F6 (StringBuilder_t* __this, const RuntimeMethod* method) ;
inline void Action_1__ctor_mDFD48034EFC4DF74EEB44B18C74DD7BAF391814E (Action_1_t54327BAFA15E24E002EFAE48D1AFFC020D368735* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Action_1_t54327BAFA15E24E002EFAE48D1AFFC020D368735*, RuntimeObject*, intptr_t, const RuntimeMethod*))Action_1__ctor_mDFD48034EFC4DF74EEB44B18C74DD7BAF391814E_gshared)(__this, ___0_object, ___1_method, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARCameraManager_add_frameReceived_mB30CD73DB7A25A6E7BC486BA289DBB99CBA3B7A1 (ARCameraManager_t6E957EA922911B8447D086D9FB1F860EC967BD9F* __this, Action_1_t54327BAFA15E24E002EFAE48D1AFFC020D368735* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602 (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___0_x, Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___1_y, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARCameraManager_remove_frameReceived_m8D9C301201B5079323169510656821F0286AA67D (ARCameraManager_t6E957EA922911B8447D086D9FB1F860EC967BD9F* __this, Action_1_t54327BAFA15E24E002EFAE48D1AFFC020D368735* ___0_value, const RuntimeMethod* method) ;
inline void EventBus_Trigger_TisARCameraFrameEventArgs_t4060608C37C2B7248AFC908876309CC36D53E0EC_m147D0F4E1AEA08F04B42A886DD7FD396ABE2448B (String_t* ___0_name, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___1_target, ARCameraFrameEventArgs_t4060608C37C2B7248AFC908876309CC36D53E0EC ___2_args, const RuntimeMethod* method)
{
	((  void (*) (String_t*, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F*, ARCameraFrameEventArgs_t4060608C37C2B7248AFC908876309CC36D53E0EC, const RuntimeMethod*))EventBus_Trigger_TisARCameraFrameEventArgs_t4060608C37C2B7248AFC908876309CC36D53E0EC_m147D0F4E1AEA08F04B42A886DD7FD396ABE2448B_gshared)(___0_name, ___1_target, ___2_args, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void StringBuilder__ctor_m1D99713357DE05DAFA296633639DB55F8C30587D (StringBuilder_t* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MessageListener__ctor_m8DA61A2F8AD16822DAF82D7FBEF6100E0AE5761E (MessageListener_t50FA00662CCB169F065100293E953B3024E0BCAE* __this, const RuntimeMethod* method) ;
inline void EventBus_Trigger_TisAREnvironmentProbesChangedEvent_t8757E98E11F3D37F0CFC4589FCA5CD2107F8375C_mF3EF7B9EF8367C19AE598C0BB4C5C819E5E9DDA3 (String_t* ___0_name, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___1_target, AREnvironmentProbesChangedEvent_t8757E98E11F3D37F0CFC4589FCA5CD2107F8375C ___2_args, const RuntimeMethod* method)
{
	((  void (*) (String_t*, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F*, AREnvironmentProbesChangedEvent_t8757E98E11F3D37F0CFC4589FCA5CD2107F8375C, const RuntimeMethod*))EventBus_Trigger_TisAREnvironmentProbesChangedEvent_t8757E98E11F3D37F0CFC4589FCA5CD2107F8375C_mF3EF7B9EF8367C19AE598C0BB4C5C819E5E9DDA3_gshared)(___0_name, ___1_target, ___2_args, method);
}
inline void Action_1__ctor_mD1DD23E09BBCBCDCB59373D479AA93CADF7E5C48 (Action_1_tE3E0B091088607877804833E23BFDBBF5594B830* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Action_1_tE3E0B091088607877804833E23BFDBBF5594B830*, RuntimeObject*, intptr_t, const RuntimeMethod*))Action_1__ctor_mD1DD23E09BBCBCDCB59373D479AA93CADF7E5C48_gshared)(__this, ___0_object, ___1_method, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AREnvironmentProbeManager_add_environmentProbesChanged_mADB9F6A00161EF9FECCFF0C76FC346E5A6321A96 (AREnvironmentProbeManager_tE457B73006F0D5136D9F2BDDEB072C1A1DB407F8* __this, Action_1_tE3E0B091088607877804833E23BFDBBF5594B830* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AREnvironmentProbeManager_remove_environmentProbesChanged_mABD9405FD4C3272C46B79CA7586901101C243FC7 (AREnvironmentProbeManager_tE457B73006F0D5136D9F2BDDEB072C1A1DB407F8* __this, Action_1_tE3E0B091088607877804833E23BFDBBF5594B830* ___0_value, const RuntimeMethod* method) ;
inline void TrackableManagerListener_1__ctor_mF10CA320B096183174E077D4F6F5A996C3B43363 (TrackableManagerListener_1_t60BDFBD938C7CBEAEEEC4ED167E4D757936C28E8* __this, const RuntimeMethod* method)
{
	((  void (*) (TrackableManagerListener_1_t60BDFBD938C7CBEAEEEC4ED167E4D757936C28E8*, const RuntimeMethod*))TrackableManagerListener_1__ctor_mA005DBA59FBCA1E0EC60AA2B1B1D93527527ABF2_gshared)(__this, method);
}
inline void EventBus_Trigger_TisARFaceUpdatedEventArgs_tC20D17255858678A1A8230EA32D7C38287221FF6_m36EBB182855F50B6EA52073A9C5843D3FE22B837 (String_t* ___0_name, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___1_target, ARFaceUpdatedEventArgs_tC20D17255858678A1A8230EA32D7C38287221FF6 ___2_args, const RuntimeMethod* method)
{
	((  void (*) (String_t*, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F*, ARFaceUpdatedEventArgs_tC20D17255858678A1A8230EA32D7C38287221FF6, const RuntimeMethod*))EventBus_Trigger_TisARFaceUpdatedEventArgs_tC20D17255858678A1A8230EA32D7C38287221FF6_m36EBB182855F50B6EA52073A9C5843D3FE22B837_gshared)(___0_name, ___1_target, ___2_args, method);
}
inline ARFace_t73C068C9C8422F79A858EC41C322D399305B8453* Component_GetComponent_TisARFace_t73C068C9C8422F79A858EC41C322D399305B8453_mDBDD6B87463FFFEC97691FFEDD402BE1F8D1BC07 (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method)
{
	return ((  ARFace_t73C068C9C8422F79A858EC41C322D399305B8453* (*) (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3*, const RuntimeMethod*))Component_GetComponent_TisRuntimeObject_m7181F81CAEC2CF53F5D2BC79B7425C16E1F80D33_gshared)(__this, method);
}
inline void Action_1__ctor_m3A71B4EF8E0C69EED8BBC0BB8AE9856BEA1929D1 (Action_1_t30DB722576CDAD6BB3F09BD60358BF49AA947548* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Action_1_t30DB722576CDAD6BB3F09BD60358BF49AA947548*, RuntimeObject*, intptr_t, const RuntimeMethod*))Action_1__ctor_m3A71B4EF8E0C69EED8BBC0BB8AE9856BEA1929D1_gshared)(__this, ___0_object, ___1_method, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARFace_add_updated_m20975C4A22DDFCC57D15C0205754C3902A699738 (ARFace_t73C068C9C8422F79A858EC41C322D399305B8453* __this, Action_1_t30DB722576CDAD6BB3F09BD60358BF49AA947548* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARFace_remove_updated_m289BD0DA8FE0FF5A2936B6FD21FB4895AFB61499 (ARFace_t73C068C9C8422F79A858EC41C322D399305B8453* __this, Action_1_t30DB722576CDAD6BB3F09BD60358BF49AA947548* ___0_value, const RuntimeMethod* method) ;
inline void EventBus_Trigger_TisARFacesChangedEventArgs_tC05D4423CB2096FF5955C3A0A00056EE2B6301AE_mF3D39EAC80939C5697B3EE63307DD1AFB338914D (String_t* ___0_name, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___1_target, ARFacesChangedEventArgs_tC05D4423CB2096FF5955C3A0A00056EE2B6301AE ___2_args, const RuntimeMethod* method)
{
	((  void (*) (String_t*, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F*, ARFacesChangedEventArgs_tC05D4423CB2096FF5955C3A0A00056EE2B6301AE, const RuntimeMethod*))EventBus_Trigger_TisARFacesChangedEventArgs_tC05D4423CB2096FF5955C3A0A00056EE2B6301AE_mF3D39EAC80939C5697B3EE63307DD1AFB338914D_gshared)(___0_name, ___1_target, ___2_args, method);
}
inline void Action_1__ctor_m6B29F8F1F0039A82C7A8CE016F79E8D39054E4D4 (Action_1_t0A16CC637B74FE43A20D2784BCA3172E7D13789C* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Action_1_t0A16CC637B74FE43A20D2784BCA3172E7D13789C*, RuntimeObject*, intptr_t, const RuntimeMethod*))Action_1__ctor_m6B29F8F1F0039A82C7A8CE016F79E8D39054E4D4_gshared)(__this, ___0_object, ___1_method, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARFaceManager_add_facesChanged_m5EA6C3526D8E44028C6DCF7A6297D7EAE240105E (ARFaceManager_tE5955187C9D7C9C4C4139F522DA0DEA69EFCFD8F* __this, Action_1_t0A16CC637B74FE43A20D2784BCA3172E7D13789C* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARFaceManager_remove_facesChanged_mBFBA84C023749078DC7D801945CF83373B336DCC (ARFaceManager_tE5955187C9D7C9C4C4139F522DA0DEA69EFCFD8F* __this, Action_1_t0A16CC637B74FE43A20D2784BCA3172E7D13789C* ___0_value, const RuntimeMethod* method) ;
inline void TrackableManagerListener_1__ctor_mC84640FEC1BBC643B3517C43EC073EEA79FC8667 (TrackableManagerListener_1_tAE0B39F766687B7392BB26FC0CEBCAA679BEB365* __this, const RuntimeMethod* method)
{
	((  void (*) (TrackableManagerListener_1_tAE0B39F766687B7392BB26FC0CEBCAA679BEB365*, const RuntimeMethod*))TrackableManagerListener_1__ctor_mA005DBA59FBCA1E0EC60AA2B1B1D93527527ABF2_gshared)(__this, method);
}
inline void EventBus_Trigger_TisARHumanBodiesChangedEventArgs_t19088E0D3384A22C5D8A542957705222C834720B_m053372DF7FDC0D251BE56C17199E478393C48C13 (String_t* ___0_name, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___1_target, ARHumanBodiesChangedEventArgs_t19088E0D3384A22C5D8A542957705222C834720B ___2_args, const RuntimeMethod* method)
{
	((  void (*) (String_t*, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F*, ARHumanBodiesChangedEventArgs_t19088E0D3384A22C5D8A542957705222C834720B, const RuntimeMethod*))EventBus_Trigger_TisARHumanBodiesChangedEventArgs_t19088E0D3384A22C5D8A542957705222C834720B_m053372DF7FDC0D251BE56C17199E478393C48C13_gshared)(___0_name, ___1_target, ___2_args, method);
}
inline void Action_1__ctor_mCFC45AE4A743ED62D221EC7E1918738EE0C00B60 (Action_1_t78DC7DD8CB74A1B21E4419996B784C6E51821247* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Action_1_t78DC7DD8CB74A1B21E4419996B784C6E51821247*, RuntimeObject*, intptr_t, const RuntimeMethod*))Action_1__ctor_mCFC45AE4A743ED62D221EC7E1918738EE0C00B60_gshared)(__this, ___0_object, ___1_method, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARHumanBodyManager_add_humanBodiesChanged_mAD886F8E010310FD75E072CDCD0478D40659D318 (ARHumanBodyManager_tA5246205B817A3D4A87BFC54FC018FB513867BFB* __this, Action_1_t78DC7DD8CB74A1B21E4419996B784C6E51821247* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARHumanBodyManager_remove_humanBodiesChanged_m868BF1A9ABD143149D3E4C4F04C6204B5768D7FB (ARHumanBodyManager_tA5246205B817A3D4A87BFC54FC018FB513867BFB* __this, Action_1_t78DC7DD8CB74A1B21E4419996B784C6E51821247* ___0_value, const RuntimeMethod* method) ;
inline void TrackableManagerListener_1__ctor_m2E928AC462404314A3931061A1142F70B3236122 (TrackableManagerListener_1_t8EF347E8BB7B8747C247403C9E911620DE36B242* __this, const RuntimeMethod* method)
{
	((  void (*) (TrackableManagerListener_1_t8EF347E8BB7B8747C247403C9E911620DE36B242*, const RuntimeMethod*))TrackableManagerListener_1__ctor_mA005DBA59FBCA1E0EC60AA2B1B1D93527527ABF2_gshared)(__this, method);
}
inline void EventBus_Trigger_TisARParticipantsChangedEventArgs_t2EAD9041A074A7ACEFF280662784747A4E85E527_m1AFA7138412723D9A851AE84D192CF53B2FD2CEF (String_t* ___0_name, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___1_target, ARParticipantsChangedEventArgs_t2EAD9041A074A7ACEFF280662784747A4E85E527 ___2_args, const RuntimeMethod* method)
{
	((  void (*) (String_t*, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F*, ARParticipantsChangedEventArgs_t2EAD9041A074A7ACEFF280662784747A4E85E527, const RuntimeMethod*))EventBus_Trigger_TisARParticipantsChangedEventArgs_t2EAD9041A074A7ACEFF280662784747A4E85E527_m1AFA7138412723D9A851AE84D192CF53B2FD2CEF_gshared)(___0_name, ___1_target, ___2_args, method);
}
inline void Action_1__ctor_m0C34C0D8022B4CB26E739D328681C24E65FC460C (Action_1_tE5F1AC110E5F6610E8B460B753F57ACC9B4524E3* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Action_1_tE5F1AC110E5F6610E8B460B753F57ACC9B4524E3*, RuntimeObject*, intptr_t, const RuntimeMethod*))Action_1__ctor_m0C34C0D8022B4CB26E739D328681C24E65FC460C_gshared)(__this, ___0_object, ___1_method, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARParticipantManager_add_participantsChanged_m7CB947B046B890EFA75726000DBECA7E86FE8775 (ARParticipantManager_t759CB1A6961D7C7F99E47B8AFFF7BCA4761C99C4* __this, Action_1_tE5F1AC110E5F6610E8B460B753F57ACC9B4524E3* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARParticipantManager_remove_participantsChanged_m8D99AE38D87E378337E82DC2359079A2448D447F (ARParticipantManager_t759CB1A6961D7C7F99E47B8AFFF7BCA4761C99C4* __this, Action_1_tE5F1AC110E5F6610E8B460B753F57ACC9B4524E3* ___0_value, const RuntimeMethod* method) ;
inline void TrackableManagerListener_1__ctor_m07B978E212E0DC99C7A64CE3576F87A983D1676A (TrackableManagerListener_1_tE4BF78B1F882C85C4BC3496DE722CE244D42E779* __this, const RuntimeMethod* method)
{
	((  void (*) (TrackableManagerListener_1_tE4BF78B1F882C85C4BC3496DE722CE244D42E779*, const RuntimeMethod*))TrackableManagerListener_1__ctor_mA005DBA59FBCA1E0EC60AA2B1B1D93527527ABF2_gshared)(__this, method);
}
inline void EventBus_Trigger_TisARPlanesChangedEventArgs_t8D63E0257BF9942EF8F8C0445F2FD46421017872_m95B4A178E0E6B6E98EE3F2CCBA22EF47977C9BCB (String_t* ___0_name, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___1_target, ARPlanesChangedEventArgs_t8D63E0257BF9942EF8F8C0445F2FD46421017872 ___2_args, const RuntimeMethod* method)
{
	((  void (*) (String_t*, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F*, ARPlanesChangedEventArgs_t8D63E0257BF9942EF8F8C0445F2FD46421017872, const RuntimeMethod*))EventBus_Trigger_TisARPlanesChangedEventArgs_t8D63E0257BF9942EF8F8C0445F2FD46421017872_m95B4A178E0E6B6E98EE3F2CCBA22EF47977C9BCB_gshared)(___0_name, ___1_target, ___2_args, method);
}
inline void Action_1__ctor_mBEDDD2BF96A8A407FBB833F764447E82E3D50BCF (Action_1_t009680BF19017ECA60753C7F605CBD85C56C6560* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Action_1_t009680BF19017ECA60753C7F605CBD85C56C6560*, RuntimeObject*, intptr_t, const RuntimeMethod*))Action_1__ctor_mBEDDD2BF96A8A407FBB833F764447E82E3D50BCF_gshared)(__this, ___0_object, ___1_method, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARPlaneManager_add_planesChanged_m295E8F0B09AF78A2D9249F3C07BFD4D4F749F36D (ARPlaneManager_t9DA6C512BDCFFBEE8B3DAA7170EB3BFFDE26A928* __this, Action_1_t009680BF19017ECA60753C7F605CBD85C56C6560* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARPlaneManager_remove_planesChanged_m86DE7059D87299391D2EE97A884E53CCE80B3A5E (ARPlaneManager_t9DA6C512BDCFFBEE8B3DAA7170EB3BFFDE26A928* __this, Action_1_t009680BF19017ECA60753C7F605CBD85C56C6560* ___0_value, const RuntimeMethod* method) ;
inline void TrackableManagerListener_1__ctor_m6D8E3E517736CC5906267C97680E382F66641F03 (TrackableManagerListener_1_t8673B1A6156F98541B1B39B98473DFF37AC0CC90* __this, const RuntimeMethod* method)
{
	((  void (*) (TrackableManagerListener_1_t8673B1A6156F98541B1B39B98473DFF37AC0CC90*, const RuntimeMethod*))TrackableManagerListener_1__ctor_mA005DBA59FBCA1E0EC60AA2B1B1D93527527ABF2_gshared)(__this, method);
}
inline void EventBus_Trigger_TisARPointCloudChangedEventArgs_t7DE4CA4D30D369496221061F89CC4BF84E42CC1B_mE0DDE4D49AE7A3FB982B04E07E2475F9D93977DE (String_t* ___0_name, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___1_target, ARPointCloudChangedEventArgs_t7DE4CA4D30D369496221061F89CC4BF84E42CC1B ___2_args, const RuntimeMethod* method)
{
	((  void (*) (String_t*, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F*, ARPointCloudChangedEventArgs_t7DE4CA4D30D369496221061F89CC4BF84E42CC1B, const RuntimeMethod*))EventBus_Trigger_TisARPointCloudChangedEventArgs_t7DE4CA4D30D369496221061F89CC4BF84E42CC1B_mE0DDE4D49AE7A3FB982B04E07E2475F9D93977DE_gshared)(___0_name, ___1_target, ___2_args, method);
}
inline void Action_1__ctor_m8C819B4CABAFC405C1DEBA65B1E220746F258AB6 (Action_1_t053CCF2809F9B0B61EA8298154533C5FACB724F0* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Action_1_t053CCF2809F9B0B61EA8298154533C5FACB724F0*, RuntimeObject*, intptr_t, const RuntimeMethod*))Action_1__ctor_m8C819B4CABAFC405C1DEBA65B1E220746F258AB6_gshared)(__this, ___0_object, ___1_method, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARPointCloudManager_add_pointCloudsChanged_m108F43CDA88DC192BA5DE3EBD7DFFDE3C59E26B3 (ARPointCloudManager_t28D1833BCE1A48657E504109A89A3B75D0AD7D9F* __this, Action_1_t053CCF2809F9B0B61EA8298154533C5FACB724F0* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARPointCloudManager_remove_pointCloudsChanged_m28A4E43DFFB1A517F932B1A613664782D068C8C0 (ARPointCloudManager_t28D1833BCE1A48657E504109A89A3B75D0AD7D9F* __this, Action_1_t053CCF2809F9B0B61EA8298154533C5FACB724F0* ___0_value, const RuntimeMethod* method) ;
inline void TrackableManagerListener_1__ctor_m3DE0DE01F5CEAED2E7F5AD649EB4E55205725541 (TrackableManagerListener_1_tFCE62DCE9ECC332FFBFBF7949494CC4DF7DA37E9* __this, const RuntimeMethod* method)
{
	((  void (*) (TrackableManagerListener_1_tFCE62DCE9ECC332FFBFBF7949494CC4DF7DA37E9*, const RuntimeMethod*))TrackableManagerListener_1__ctor_mA005DBA59FBCA1E0EC60AA2B1B1D93527527ABF2_gshared)(__this, method);
}
inline void EventBus_Trigger_TisARTrackedImagesChangedEventArgs_tD7718E40EE25D22E9143DA4F556C44980E18BE2F_m543E103E3042925BB06EAC45E637282A71E1F853 (String_t* ___0_name, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___1_target, ARTrackedImagesChangedEventArgs_tD7718E40EE25D22E9143DA4F556C44980E18BE2F ___2_args, const RuntimeMethod* method)
{
	((  void (*) (String_t*, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F*, ARTrackedImagesChangedEventArgs_tD7718E40EE25D22E9143DA4F556C44980E18BE2F, const RuntimeMethod*))EventBus_Trigger_TisARTrackedImagesChangedEventArgs_tD7718E40EE25D22E9143DA4F556C44980E18BE2F_m543E103E3042925BB06EAC45E637282A71E1F853_gshared)(___0_name, ___1_target, ___2_args, method);
}
inline void Action_1__ctor_m55BF5EC44D8DEC74D367B749B5B29C8F2A9CB957 (Action_1_tE7B3D1070B99EFDFA92BD4DBEF95BF881DEB591F* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Action_1_tE7B3D1070B99EFDFA92BD4DBEF95BF881DEB591F*, RuntimeObject*, intptr_t, const RuntimeMethod*))Action_1__ctor_m55BF5EC44D8DEC74D367B749B5B29C8F2A9CB957_gshared)(__this, ___0_object, ___1_method, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARTrackedImageManager_add_trackedImagesChanged_mB190015342B225144729100E0E38745CAFA7F438 (ARTrackedImageManager_tCBA77F0B8A7172CC6508CA29176EAE1DE5DC4BD4* __this, Action_1_tE7B3D1070B99EFDFA92BD4DBEF95BF881DEB591F* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARTrackedImageManager_remove_trackedImagesChanged_mA754FAB732F3D5D803FA3A58A51E6487E75CCF34 (ARTrackedImageManager_tCBA77F0B8A7172CC6508CA29176EAE1DE5DC4BD4* __this, Action_1_tE7B3D1070B99EFDFA92BD4DBEF95BF881DEB591F* ___0_value, const RuntimeMethod* method) ;
inline void TrackableManagerListener_1__ctor_mBEE631CBF51F52534707E2938E971864799D3EDA (TrackableManagerListener_1_t7C3219B15D46C0E0276CF4D8E06F12DC969F5D56* __this, const RuntimeMethod* method)
{
	((  void (*) (TrackableManagerListener_1_t7C3219B15D46C0E0276CF4D8E06F12DC969F5D56*, const RuntimeMethod*))TrackableManagerListener_1__ctor_mA005DBA59FBCA1E0EC60AA2B1B1D93527527ABF2_gshared)(__this, method);
}
inline void EventBus_Trigger_TisARTrackedObjectsChangedEventArgs_t378A63B91AD22EA4BA9C68805C72DDA4B7C01509_m2D44CB18B7DE92A8A28C2336767883E782C70CD8 (String_t* ___0_name, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___1_target, ARTrackedObjectsChangedEventArgs_t378A63B91AD22EA4BA9C68805C72DDA4B7C01509 ___2_args, const RuntimeMethod* method)
{
	((  void (*) (String_t*, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F*, ARTrackedObjectsChangedEventArgs_t378A63B91AD22EA4BA9C68805C72DDA4B7C01509, const RuntimeMethod*))EventBus_Trigger_TisARTrackedObjectsChangedEventArgs_t378A63B91AD22EA4BA9C68805C72DDA4B7C01509_m2D44CB18B7DE92A8A28C2336767883E782C70CD8_gshared)(___0_name, ___1_target, ___2_args, method);
}
inline void Action_1__ctor_mBF52FA156452295382976743FE99EF09B9D21823 (Action_1_t34AAB95B35DF66496184F8421A8AC5F317B7D8AB* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Action_1_t34AAB95B35DF66496184F8421A8AC5F317B7D8AB*, RuntimeObject*, intptr_t, const RuntimeMethod*))Action_1__ctor_mBF52FA156452295382976743FE99EF09B9D21823_gshared)(__this, ___0_object, ___1_method, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARTrackedObjectManager_add_trackedObjectsChanged_m4E28ABA38B522FBE3B8150E1090237954A804FAE (ARTrackedObjectManager_tD667E7CCD95E9D25449BD578F256F728D8619FC7* __this, Action_1_t34AAB95B35DF66496184F8421A8AC5F317B7D8AB* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARTrackedObjectManager_remove_trackedObjectsChanged_m8412618ABDA3802160C112C7C884886D8057F41D (ARTrackedObjectManager_tD667E7CCD95E9D25449BD578F256F728D8619FC7* __this, Action_1_t34AAB95B35DF66496184F8421A8AC5F317B7D8AB* ___0_value, const RuntimeMethod* method) ;
inline void TrackableManagerListener_1__ctor_m35FD529767C9EB77928CBC0BDC28A26273050D7B (TrackableManagerListener_1_t2D0E0BEBA98DF361C2EB9261ED0310BF3DFF8FD5* __this, const RuntimeMethod* method)
{
	((  void (*) (TrackableManagerListener_1_t2D0E0BEBA98DF361C2EB9261ED0310BF3DFF8FD5*, const RuntimeMethod*))TrackableManagerListener_1__ctor_mA005DBA59FBCA1E0EC60AA2B1B1D93527527ABF2_gshared)(__this, method);
}
inline void GetTrackablesUnit_6__ctor_m9BC4C628CE4264EFA7586A6658299BF686ADE2CA (GetTrackablesUnit_6_tE3FF81AB62ABB3500474D301EF59B13BF4A1685E* __this, const RuntimeMethod* method)
{
	((  void (*) (GetTrackablesUnit_6_tE3FF81AB62ABB3500474D301EF59B13BF4A1685E*, const RuntimeMethod*))GetTrackablesUnit_6__ctor_m3AE000A7F3697AAB7BE4FF74B3F1B44FB61F723F_gshared)(__this, method);
}
inline void GetTrackablesUnit_6__ctor_m993C351689850B54D4D415D6555E6963296402E6 (GetTrackablesUnit_6_tCE90D2E00FA14B5E936843082D488242C7463304* __this, const RuntimeMethod* method)
{
	((  void (*) (GetTrackablesUnit_6_tCE90D2E00FA14B5E936843082D488242C7463304*, const RuntimeMethod*))GetTrackablesUnit_6__ctor_m57BA41B655EE5E6D80FFA1B27E8FAFDB7A9E1970_gshared)(__this, method);
}
inline void GetTrackablesUnit_6__ctor_mFE5AAECF852800E5E7743015509249E62860CB62 (GetTrackablesUnit_6_tB48912D7C44FC073BE0CCC0B19CCC4A33E066B77* __this, const RuntimeMethod* method)
{
	((  void (*) (GetTrackablesUnit_6_tB48912D7C44FC073BE0CCC0B19CCC4A33E066B77*, const RuntimeMethod*))GetTrackablesUnit_6__ctor_m0BC517A1466D742CE4C647BD284033CC11A8B376_gshared)(__this, method);
}
inline void GetTrackablesUnit_6__ctor_m80FFBEC9D57E6A6AC206595E20191765D636E72D (GetTrackablesUnit_6_t3DE4D256458C27210703957091C3684A86E12198* __this, const RuntimeMethod* method)
{
	((  void (*) (GetTrackablesUnit_6_t3DE4D256458C27210703957091C3684A86E12198*, const RuntimeMethod*))GetTrackablesUnit_6__ctor_mFE6377E90C71DD4DB1780C5BDE5E5A91667D1FBB_gshared)(__this, method);
}
inline void GetTrackablesUnit_6__ctor_m95F5607653302ED365908C5FD4E47E8F6C046A35 (GetTrackablesUnit_6_t35B2538FE2BE005439F84D8E3DD2098F607A881B* __this, const RuntimeMethod* method)
{
	((  void (*) (GetTrackablesUnit_6_t35B2538FE2BE005439F84D8E3DD2098F607A881B*, const RuntimeMethod*))GetTrackablesUnit_6__ctor_m4ACF2829E61A424E7F31E13E41D9D9DF158DAA3B_gshared)(__this, method);
}
inline void GetTrackablesUnit_6__ctor_m8F8058DE37A8268EABD8551E5CBB014EC6A1FC39 (GetTrackablesUnit_6_t016BB0C74695D0122C8B6B3599A71F89D32BCF77* __this, const RuntimeMethod* method)
{
	((  void (*) (GetTrackablesUnit_6_t016BB0C74695D0122C8B6B3599A71F89D32BCF77*, const RuntimeMethod*))GetTrackablesUnit_6__ctor_m4A8A07763DB4937CE0573C3506EE437345CBBA0A_gshared)(__this, method);
}
inline void GetTrackablesUnit_6__ctor_m4A8A971CAFBC181DC2C00076B9A07CA14F9C2830 (GetTrackablesUnit_6_tBEEA64178AC6F947BE154CE4D41420AED64DC0AE* __this, const RuntimeMethod* method)
{
	((  void (*) (GetTrackablesUnit_6_tBEEA64178AC6F947BE154CE4D41420AED64DC0AE*, const RuntimeMethod*))GetTrackablesUnit_6__ctor_mAE655F774D272F686546A115114DD2543A30E6A0_gshared)(__this, method);
}
inline void GetTrackablesUnit_6__ctor_m42135B0FC7FB4509F6F5630CB0F1A9B235D0C4AF (GetTrackablesUnit_6_tA1B41E03CB6324ED319781B3AA151D349922B24A* __this, const RuntimeMethod* method)
{
	((  void (*) (GetTrackablesUnit_6_tA1B41E03CB6324ED319781B3AA151D349922B24A*, const RuntimeMethod*))GetTrackablesUnit_6__ctor_m08D7B92FD847DFB1385D59CCA674A532758E0D26_gshared)(__this, method);
}
inline void GetTrackablesUnit_6__ctor_mE4E69DCE572AA63C76CCEBB851DE56E31BD02020 (GetTrackablesUnit_6_t5FAD854E9B61FD3E7409D18C4DED57BC373BACE7* __this, const RuntimeMethod* method)
{
	((  void (*) (GetTrackablesUnit_6_t5FAD854E9B61FD3E7409D18C4DED57BC373BACE7*, const RuntimeMethod*))GetTrackablesUnit_6__ctor_m245C91BBB8F29976768E72A2DBD98FA1227FB902_gshared)(__this, method);
}
inline void GetTrackablesUnit_6__ctor_mC724CF523FCF0A0CA43758DC60F0E05E4B30EE32 (GetTrackablesUnit_6_t60E470A25938E95AA9CD62B873EA6C36FC34D2FF* __this, const RuntimeMethod* method)
{
	((  void (*) (GetTrackablesUnit_6_t60E470A25938E95AA9CD62B873EA6C36FC34D2FF*, const RuntimeMethod*))GetTrackablesUnit_6__ctor_mC2A77B203229D56DD6BFEEAE238E1F727E36CA54_gshared)(__this, method);
}
inline void Func_2__ctor_m36A365D852CA6BD70A68A3A26FF683FB3369DDF4 (Func_2_tE299C631F0CB2E8E61444D749D446C27349C90B8* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Func_2_tE299C631F0CB2E8E61444D749D446C27349C90B8*, RuntimeObject*, intptr_t, const RuntimeMethod*))Func_2__ctor_m7F8A01C0B02BC1D4063F4EB1E817F7A48562A398_gshared)(__this, ___0_object, ___1_method, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ControlInput_t1AD434958538C865F55A62C66B9C24DA82AD31FE* Unit_ControlInput_mF730DABA4FE60423D39B8EADE5DA72F51E6166A5 (Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191* __this, String_t* ___0_key, Func_2_tE299C631F0CB2E8E61444D749D446C27349C90B8* ___1_action, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void SessionStateSwitchUnit_set_flowIn_mD8F159479E3884269A7D2057D7F3E8C52B2923D5_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, ControlInput_t1AD434958538C865F55A62C66B9C24DA82AD31FE* ___0_value, const RuntimeMethod* method) ;
inline ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* Unit_ValueInput_TisARSessionState_tAAADC649886D67F7134A0D9A665953D764A115D7_mBD32544DF033F6AA05190E690F3F77D3420E6CF4 (Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191* __this, String_t* ___0_key, const RuntimeMethod* method)
{
	return ((  ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* (*) (Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191*, String_t*, const RuntimeMethod*))Unit_ValueInput_TisInt32Enum_tCBAC8BA2BFF3A845FA599F303093BBBA374B6F0C_mEE7EB0BD491E883D13BCCD4BD8217D33D3384A48_gshared)(__this, ___0_key, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void SessionStateSwitchUnit_set_sessionStateIn_m39EB7D50DFEE0306E0C4F690E06D8B6D246B1AFB_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* Unit_ControlOutput_mCD76724DFB6AAE10C099E9984A39A5E0FC2824FE (Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191* __this, String_t* ___0_key, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void SessionStateSwitchUnit_set_none_mE058B6C1A72B0BD0DC6BAEA629EC6C8A55BD273C_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void SessionStateSwitchUnit_set_unsupported_m683621DAFF0725F51FAB5D0FB9CB63F5731E53A1_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void SessionStateSwitchUnit_set_checkingAvailability_m0D10A7BFD1348E9694B381482E90F5974E3CF246_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void SessionStateSwitchUnit_set_needsInstall_m6C03848DA29361417CBE17AAC6EF641AE4946A95_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void SessionStateSwitchUnit_set_installing_m91BC0E0D467AB4CDE4A9E87F43ABA98874D7BAF4_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void SessionStateSwitchUnit_set_ready_m4CF3C56F9F24CDCFE9678F22BBAA988BF7AA6FD3_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void SessionStateSwitchUnit_set_sessionInitializing_mB3A2FF70C33664AFF792B183A2E39252DDFDACB2_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void SessionStateSwitchUnit_set_sessionTracking_mA270750450E8D31E271811C785AE730FB61AACF0_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* SessionStateSwitchUnit_get_sessionStateIn_m4E966BABF18BE40C7178158088CC8DEA31893939_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ControlInput_t1AD434958538C865F55A62C66B9C24DA82AD31FE* SessionStateSwitchUnit_get_flowIn_m667548B27056B2F6F27B63DFCF4412BBAE6637A5_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Unit_Requirement_m9F929AD842C0E254FA9AD48E9200C63B43BFE64A (Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191* __this, ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* ___0_source, ControlInput_t1AD434958538C865F55A62C66B9C24DA82AD31FE* ___1_destination, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* SessionStateSwitchUnit_get_none_mCDA812C5D868B12D04C020ED27ED0F886EE0963E_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Unit_Succession_m202BB700D861D65B85102AD7B3119AD1B2FF2158 (Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191* __this, ControlInput_t1AD434958538C865F55A62C66B9C24DA82AD31FE* ___0_source, ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___1_destination, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* SessionStateSwitchUnit_get_unsupported_m911986D3ADF69617D9E6DFFB367926BBA3639439_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* SessionStateSwitchUnit_get_checkingAvailability_m3EF1098F57D30B08B8C7587F9D94B64A4E650ED9_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* SessionStateSwitchUnit_get_needsInstall_mDBE283471C7F3B8C8A1D6962DAFECBC81CC7071E_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* SessionStateSwitchUnit_get_installing_mAB1C0C05D8CAD09DE7EA9177806621D1263E120F_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* SessionStateSwitchUnit_get_ready_m5FDBB155FE257620F68167B4A7A1DEEB597491D2_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* SessionStateSwitchUnit_get_sessionInitializing_mDAB4A059DA69D91C4A7E3ED9B207A31F64040746_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* SessionStateSwitchUnit_get_sessionTracking_m46B500F7B3F7276E269EC893E52EE71478554365_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* Flow_GetValue_m4396BFB8BF013A1CF992A4E36D47E864D4180718 (Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* __this, ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* ___0_input, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ArgumentException__ctor_m026938A67AF9D36BB7ED27F80425D7194B514465 (ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263* __this, String_t* ___0_message, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ArgumentOutOfRangeException__ctor_mB596C51BFA864B65C2CED275458FAE90F7CD29C9 (ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Unit__ctor_m7C07A3A00D7777361F4B86B45F40073D1A65D8B2 (Unit_tEFDDF0DAD2C72D6F68E357D3530E23599F7D8191* __this, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MonoScriptData_t536A062DC987E9D49DDC48415C579F63ADB98E92 UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m5CF7F52D86C5B13E9D6018E08EBFC9FEE68FFD12 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3E_tDDCC949E3B043757E8B2BF5CEA983FB5D64C65A5____06B4533828E721283278BAD55C65B14F692386B2BF93E99FAA017FD993F5D9B3_FieldInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3E_tDDCC949E3B043757E8B2BF5CEA983FB5D64C65A5____4C23045B4C42C8C05E9FC1DE66F40E8CBE11D66FC46CEDF30183BEAF834C5B0B_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	MonoScriptData_t536A062DC987E9D49DDC48415C579F63ADB98E92 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		il2cpp_codegen_initobj((&V_0), sizeof(MonoScriptData_t536A062DC987E9D49DDC48415C579F63ADB98E92));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_0 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)((int32_t)4990));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_1 = L_0;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_2 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3E_tDDCC949E3B043757E8B2BF5CEA983FB5D64C65A5____06B4533828E721283278BAD55C65B14F692386B2BF93E99FAA017FD993F5D9B3_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_1, L_2, NULL);
		(&V_0)->___FilePathsData = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&(&V_0)->___FilePathsData), (void*)L_1);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_3 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)((int32_t)2799));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_4 = L_3;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_5 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3E_tDDCC949E3B043757E8B2BF5CEA983FB5D64C65A5____4C23045B4C42C8C05E9FC1DE66F40E8CBE11D66FC46CEDF30183BEAF834C5B0B_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_4, L_5, NULL);
		(&V_0)->___TypesData = L_4;
		Il2CppCodeGenWriteBarrier((void**)(&(&V_0)->___TypesData), (void*)L_4);
		(&V_0)->___TotalFiles = ((int32_t)38);
		(&V_0)->___TotalTypes = ((int32_t)39);
		(&V_0)->___IsEditorOnly = (bool)0;
		MonoScriptData_t536A062DC987E9D49DDC48415C579F63ADB98E92 L_6 = V_0;
		return L_6;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mC1B4DF333E4618654773D73CC5CCF6106BC4FE2C (UnitySourceGeneratedAssemblyMonoScriptTypes_v1_t8D822E9B7C7E0474CF4A1283070CD2A45E6A5C71* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C void MonoScriptData_t536A062DC987E9D49DDC48415C579F63ADB98E92_marshal_pinvoke(const MonoScriptData_t536A062DC987E9D49DDC48415C579F63ADB98E92& unmarshaled, MonoScriptData_t536A062DC987E9D49DDC48415C579F63ADB98E92_marshaled_pinvoke& marshaled)
{
	marshaled.___FilePathsData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___FilePathsData);
	marshaled.___TypesData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___TypesData);
	marshaled.___TotalTypes = unmarshaled.___TotalTypes;
	marshaled.___TotalFiles = unmarshaled.___TotalFiles;
	marshaled.___IsEditorOnly = static_cast<int32_t>(unmarshaled.___IsEditorOnly);
}
IL2CPP_EXTERN_C void MonoScriptData_t536A062DC987E9D49DDC48415C579F63ADB98E92_marshal_pinvoke_back(const MonoScriptData_t536A062DC987E9D49DDC48415C579F63ADB98E92_marshaled_pinvoke& marshaled, MonoScriptData_t536A062DC987E9D49DDC48415C579F63ADB98E92& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	unmarshaled.___FilePathsData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___FilePathsData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData));
	unmarshaled.___TypesData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___TypesData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData));
	int32_t unmarshaledTotalTypes_temp_2 = 0;
	unmarshaledTotalTypes_temp_2 = marshaled.___TotalTypes;
	unmarshaled.___TotalTypes = unmarshaledTotalTypes_temp_2;
	int32_t unmarshaledTotalFiles_temp_3 = 0;
	unmarshaledTotalFiles_temp_3 = marshaled.___TotalFiles;
	unmarshaled.___TotalFiles = unmarshaledTotalFiles_temp_3;
	bool unmarshaledIsEditorOnly_temp_4 = false;
	unmarshaledIsEditorOnly_temp_4 = static_cast<bool>(marshaled.___IsEditorOnly);
	unmarshaled.___IsEditorOnly = unmarshaledIsEditorOnly_temp_4;
}
IL2CPP_EXTERN_C void MonoScriptData_t536A062DC987E9D49DDC48415C579F63ADB98E92_marshal_pinvoke_cleanup(MonoScriptData_t536A062DC987E9D49DDC48415C579F63ADB98E92_marshaled_pinvoke& marshaled)
{
	il2cpp_codegen_com_destroy_safe_array(marshaled.___FilePathsData);
	marshaled.___FilePathsData = NULL;
	il2cpp_codegen_com_destroy_safe_array(marshaled.___TypesData);
	marshaled.___TypesData = NULL;
}
IL2CPP_EXTERN_C void MonoScriptData_t536A062DC987E9D49DDC48415C579F63ADB98E92_marshal_com(const MonoScriptData_t536A062DC987E9D49DDC48415C579F63ADB98E92& unmarshaled, MonoScriptData_t536A062DC987E9D49DDC48415C579F63ADB98E92_marshaled_com& marshaled)
{
	marshaled.___FilePathsData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___FilePathsData);
	marshaled.___TypesData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___TypesData);
	marshaled.___TotalTypes = unmarshaled.___TotalTypes;
	marshaled.___TotalFiles = unmarshaled.___TotalFiles;
	marshaled.___IsEditorOnly = static_cast<int32_t>(unmarshaled.___IsEditorOnly);
}
IL2CPP_EXTERN_C void MonoScriptData_t536A062DC987E9D49DDC48415C579F63ADB98E92_marshal_com_back(const MonoScriptData_t536A062DC987E9D49DDC48415C579F63ADB98E92_marshaled_com& marshaled, MonoScriptData_t536A062DC987E9D49DDC48415C579F63ADB98E92& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	unmarshaled.___FilePathsData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___FilePathsData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData));
	unmarshaled.___TypesData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___TypesData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData));
	int32_t unmarshaledTotalTypes_temp_2 = 0;
	unmarshaledTotalTypes_temp_2 = marshaled.___TotalTypes;
	unmarshaled.___TotalTypes = unmarshaledTotalTypes_temp_2;
	int32_t unmarshaledTotalFiles_temp_3 = 0;
	unmarshaledTotalFiles_temp_3 = marshaled.___TotalFiles;
	unmarshaled.___TotalFiles = unmarshaledTotalFiles_temp_3;
	bool unmarshaledIsEditorOnly_temp_4 = false;
	unmarshaledIsEditorOnly_temp_4 = static_cast<bool>(marshaled.___IsEditorOnly);
	unmarshaled.___IsEditorOnly = unmarshaledIsEditorOnly_temp_4;
}
IL2CPP_EXTERN_C void MonoScriptData_t536A062DC987E9D49DDC48415C579F63ADB98E92_marshal_com_cleanup(MonoScriptData_t536A062DC987E9D49DDC48415C579F63ADB98E92_marshaled_com& marshaled)
{
	il2cpp_codegen_com_destroy_safe_array(marshaled.___FilePathsData);
	marshaled.___FilePathsData = NULL;
	il2cpp_codegen_com_destroy_safe_array(marshaled.___TypesData);
	marshaled.___TypesData = NULL;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* AnchorsChangedEventUnit_get_hookName_mD996B5E207C2889FE47E181D78FEF149398426A9 (AnchorsChangedEventUnit_t1CDE435C1ABCADB9DEB2117F2E8C2943E274045F* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralBC7D1F049771153528B6E1B4730B5DD939FF7F47);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteralBC7D1F049771153528B6E1B4730B5DD939FF7F47;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AnchorsChangedEventUnit_AssignArguments_m899A2B6F52D8EB9D919CF8FC138FF8DF052AF403 (AnchorsChangedEventUnit_t1CDE435C1ABCADB9DEB2117F2E8C2943E274045F* __this, Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* ___0_flow, ARAnchorsChangedEventArgs_t755E6208C84304CAC448C133B0A8660CD5750A75 ___1_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackablesChangedEventUnit_8_get_added_mB590546762CE6A56E4C8B1EB8F24BD6CAA573B17_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackablesChangedEventUnit_8_get_removed_m00993034CD9C3DEBE2D7B5D8BCEF7D1F4AC6BB76_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackablesChangedEventUnit_8_get_updated_m4155AE83417E658B126553D3D5BE9E51691A2561_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* L_0 = ___0_flow;
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_1;
		L_1 = TrackablesChangedEventUnit_8_get_added_mB590546762CE6A56E4C8B1EB8F24BD6CAA573B17_inline(__this, TrackablesChangedEventUnit_8_get_added_mB590546762CE6A56E4C8B1EB8F24BD6CAA573B17_RuntimeMethod_var);
		List_1_t5C2EBF017989E44F3D1D11AF502E10F6F8D9AB53* L_2;
		L_2 = ARAnchorsChangedEventArgs_get_added_m5F9CEB4587F9496D0DA28880430B69EFDB550600_inline((&___1_args), NULL);
		NullCheck(L_0);
		Flow_SetValue_m635BAF02F51D7C027C4F85157B80E2AB2DFB48A7(L_0, L_1, L_2, NULL);
		Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* L_3 = ___0_flow;
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_4;
		L_4 = TrackablesChangedEventUnit_8_get_updated_m4155AE83417E658B126553D3D5BE9E51691A2561_inline(__this, TrackablesChangedEventUnit_8_get_updated_m4155AE83417E658B126553D3D5BE9E51691A2561_RuntimeMethod_var);
		List_1_t5C2EBF017989E44F3D1D11AF502E10F6F8D9AB53* L_5;
		L_5 = ARAnchorsChangedEventArgs_get_updated_m21039F14E472A75F969EE0BA5CBA9007DC12B593_inline((&___1_args), NULL);
		NullCheck(L_3);
		Flow_SetValue_m635BAF02F51D7C027C4F85157B80E2AB2DFB48A7(L_3, L_4, L_5, NULL);
		Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* L_6 = ___0_flow;
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_7;
		L_7 = TrackablesChangedEventUnit_8_get_removed_m00993034CD9C3DEBE2D7B5D8BCEF7D1F4AC6BB76_inline(__this, TrackablesChangedEventUnit_8_get_removed_m00993034CD9C3DEBE2D7B5D8BCEF7D1F4AC6BB76_RuntimeMethod_var);
		List_1_t5C2EBF017989E44F3D1D11AF502E10F6F8D9AB53* L_8;
		L_8 = ARAnchorsChangedEventArgs_get_removed_mF1E9D61E21153018C87DE99722FF8820C351CFD8_inline((&___1_args), NULL);
		NullCheck(L_6);
		Flow_SetValue_m635BAF02F51D7C027C4F85157B80E2AB2DFB48A7(L_6, L_7, L_8, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AnchorsChangedEventUnit__ctor_mB3BF0403E6002D1FDA58EFB7F2921E8C07215414 (AnchorsChangedEventUnit_t1CDE435C1ABCADB9DEB2117F2E8C2943E274045F* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackablesChangedEventUnit_8__ctor_mA9A06C3435A47571B7483CD98FC6FC1F3938890F_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		TrackablesChangedEventUnit_8__ctor_mA9A06C3435A47571B7483CD98FC6FC1F3938890F(__this, TrackablesChangedEventUnit_8__ctor_mA9A06C3435A47571B7483CD98FC6FC1F3938890F_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* CameraFrameReceivedEventUnit_get_frameEventArgs_m2CA34906D7D15DDD541EC38B7862B6CB0AB500E8 (CameraFrameReceivedEventUnit_t5C258A06DEEF9DBF3CE6AD4FD0B51313F1DEF858* __this, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = __this->___U3CframeEventArgsU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CameraFrameReceivedEventUnit_set_frameEventArgs_m9D477159407F84279F1EF8E7619B871AA4F542FE (CameraFrameReceivedEventUnit_t5C258A06DEEF9DBF3CE6AD4FD0B51313F1DEF858* __this, ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___0_value, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = ___0_value;
		__this->___U3CframeEventArgsU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CframeEventArgsU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* CameraFrameReceivedEventUnit_get_hookName_m622599602E2C6D5C5B718B2B08DDAA3563C704C3 (CameraFrameReceivedEventUnit_t5C258A06DEEF9DBF3CE6AD4FD0B51313F1DEF858* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralF78A6EBA0E7E34457C9FFEC3652864FEDD473081);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteralF78A6EBA0E7E34457C9FFEC3652864FEDD473081;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Type_t* CameraFrameReceivedEventUnit_get_MessageListenerType_mDE66642906108E26851B7CC208C8BCE367B24AEA (CameraFrameReceivedEventUnit_t5C258A06DEEF9DBF3CE6AD4FD0B51313F1DEF858* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ARCameraManagerListener_t9CDC77A2AAA0062BF970292523A3AD20D76BB3AE_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Type_t_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_0 = { reinterpret_cast<intptr_t> (ARCameraManagerListener_t9CDC77A2AAA0062BF970292523A3AD20D76BB3AE_0_0_0_var) };
		il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
		Type_t* L_1;
		L_1 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CameraFrameReceivedEventUnit_Definition_m3BA0B54D3F778C2FF3AAA37693C69E54A5836DE2 (CameraFrameReceivedEventUnit_t5C258A06DEEF9DBF3CE6AD4FD0B51313F1DEF858* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GameObjectEventUnit_1_Definition_m993A9F33D4AB3731B9BF811F804CA9BA0E75096E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Unit_ValueOutput_TisARCameraFrameEventArgs_t4060608C37C2B7248AFC908876309CC36D53E0EC_m179CC70EB8D39BBFB29E25C4D83DB538FBF3C6FC_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralA8CFAE0A1486E64AE5F73A446DEF27C6B7E9B54F);
		s_Il2CppMethodInitialized = true;
	}
	{
		GameObjectEventUnit_1_Definition_m993A9F33D4AB3731B9BF811F804CA9BA0E75096E(__this, GameObjectEventUnit_1_Definition_m993A9F33D4AB3731B9BF811F804CA9BA0E75096E_RuntimeMethod_var);
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0;
		L_0 = Unit_ValueOutput_TisARCameraFrameEventArgs_t4060608C37C2B7248AFC908876309CC36D53E0EC_m179CC70EB8D39BBFB29E25C4D83DB538FBF3C6FC(__this, _stringLiteralA8CFAE0A1486E64AE5F73A446DEF27C6B7E9B54F, Unit_ValueOutput_TisARCameraFrameEventArgs_t4060608C37C2B7248AFC908876309CC36D53E0EC_m179CC70EB8D39BBFB29E25C4D83DB538FBF3C6FC_RuntimeMethod_var);
		CameraFrameReceivedEventUnit_set_frameEventArgs_m9D477159407F84279F1EF8E7619B871AA4F542FE_inline(__this, L_0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CameraFrameReceivedEventUnit_AssignArguments_m3632CC04DC5E4651074CDF8353FF93DFA5E62330 (CameraFrameReceivedEventUnit_t5C258A06DEEF9DBF3CE6AD4FD0B51313F1DEF858* __this, Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* ___0_flow, ARCameraFrameEventArgs_t4060608C37C2B7248AFC908876309CC36D53E0EC ___1_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ARCameraFrameEventArgs_t4060608C37C2B7248AFC908876309CC36D53E0EC_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* L_0 = ___0_flow;
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_1;
		L_1 = CameraFrameReceivedEventUnit_get_frameEventArgs_m2CA34906D7D15DDD541EC38B7862B6CB0AB500E8_inline(__this, NULL);
		ARCameraFrameEventArgs_t4060608C37C2B7248AFC908876309CC36D53E0EC L_2 = ___1_args;
		ARCameraFrameEventArgs_t4060608C37C2B7248AFC908876309CC36D53E0EC L_3 = L_2;
		RuntimeObject* L_4 = Box(ARCameraFrameEventArgs_t4060608C37C2B7248AFC908876309CC36D53E0EC_il2cpp_TypeInfo_var, &L_3);
		NullCheck(L_0);
		Flow_SetValue_m635BAF02F51D7C027C4F85157B80E2AB2DFB48A7(L_0, L_1, L_4, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CameraFrameReceivedEventUnit__ctor_m49D54CB2470E3CB3D522BD5ACA7E13FF47E6CBF6 (CameraFrameReceivedEventUnit_t5C258A06DEEF9DBF3CE6AD4FD0B51313F1DEF858* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GameObjectEventUnit_1__ctor_mBA789D715816EDBF30E72C14CE07C03030B2FFD2_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		GameObjectEventUnit_1__ctor_mBA789D715816EDBF30E72C14CE07C03030B2FFD2(__this, GameObjectEventUnit_1__ctor_mBA789D715816EDBF30E72C14CE07C03030B2FFD2_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* EnvironmentProbesChangedEventUnit_get_hookName_m42A4B87F47C4FC4364CA15F859298E8FAF076116 (EnvironmentProbesChangedEventUnit_t9F93FC318A1011F92882E76B8284810D2B98ABE3* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral0EAF6D0EBB42EC97177547351019DB3DC3130C07);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteral0EAF6D0EBB42EC97177547351019DB3DC3130C07;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnvironmentProbesChangedEventUnit_AssignArguments_m199E25B95E3DC8D3A4E2A3205959366BA8B6B7C6 (EnvironmentProbesChangedEventUnit_t9F93FC318A1011F92882E76B8284810D2B98ABE3* __this, Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* ___0_flow, AREnvironmentProbesChangedEvent_t8757E98E11F3D37F0CFC4589FCA5CD2107F8375C ___1_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackablesChangedEventUnit_8_get_added_mF1D86A14D162BBD5DC42FB80A7C0FA7BC769AF97_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackablesChangedEventUnit_8_get_removed_m0AFD1317BBE14E4A88D0D90E190EA056ABD7DE2B_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackablesChangedEventUnit_8_get_updated_m64D143FBFBC17DC316E6C6F700D961D64B9E0AFE_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* L_0 = ___0_flow;
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_1;
		L_1 = TrackablesChangedEventUnit_8_get_added_mF1D86A14D162BBD5DC42FB80A7C0FA7BC769AF97_inline(__this, TrackablesChangedEventUnit_8_get_added_mF1D86A14D162BBD5DC42FB80A7C0FA7BC769AF97_RuntimeMethod_var);
		List_1_t6F14EE6179A9A8E042C5D65E902F72E24597029B* L_2;
		L_2 = AREnvironmentProbesChangedEvent_get_added_m5EB8CAE059E5A0F0A84A11866F2343F630B7C997_inline((&___1_args), NULL);
		NullCheck(L_0);
		Flow_SetValue_m635BAF02F51D7C027C4F85157B80E2AB2DFB48A7(L_0, L_1, L_2, NULL);
		Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* L_3 = ___0_flow;
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_4;
		L_4 = TrackablesChangedEventUnit_8_get_updated_m64D143FBFBC17DC316E6C6F700D961D64B9E0AFE_inline(__this, TrackablesChangedEventUnit_8_get_updated_m64D143FBFBC17DC316E6C6F700D961D64B9E0AFE_RuntimeMethod_var);
		List_1_t6F14EE6179A9A8E042C5D65E902F72E24597029B* L_5;
		L_5 = AREnvironmentProbesChangedEvent_get_updated_mFFE292486A4D2DB4385C70FAE94EB6048CBB99F3_inline((&___1_args), NULL);
		NullCheck(L_3);
		Flow_SetValue_m635BAF02F51D7C027C4F85157B80E2AB2DFB48A7(L_3, L_4, L_5, NULL);
		Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* L_6 = ___0_flow;
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_7;
		L_7 = TrackablesChangedEventUnit_8_get_removed_m0AFD1317BBE14E4A88D0D90E190EA056ABD7DE2B_inline(__this, TrackablesChangedEventUnit_8_get_removed_m0AFD1317BBE14E4A88D0D90E190EA056ABD7DE2B_RuntimeMethod_var);
		List_1_t6F14EE6179A9A8E042C5D65E902F72E24597029B* L_8;
		L_8 = AREnvironmentProbesChangedEvent_get_removed_m0D5B42BCAD8408B1FC0DF82E24C4EAF1D8ADF40A_inline((&___1_args), NULL);
		NullCheck(L_6);
		Flow_SetValue_m635BAF02F51D7C027C4F85157B80E2AB2DFB48A7(L_6, L_7, L_8, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnvironmentProbesChangedEventUnit__ctor_m618569E9356D0FAC231385EF74091BD7EF889BE6 (EnvironmentProbesChangedEventUnit_t9F93FC318A1011F92882E76B8284810D2B98ABE3* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackablesChangedEventUnit_8__ctor_m4E7FEDDC8BF5DEFA8C29AC317BD812195B30A839_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		TrackablesChangedEventUnit_8__ctor_m4E7FEDDC8BF5DEFA8C29AC317BD812195B30A839(__this, TrackablesChangedEventUnit_8__ctor_m4E7FEDDC8BF5DEFA8C29AC317BD812195B30A839_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* FacesChangedEventUnit_get_hookName_m25C307FFC3651366618356692EBB21454D2802D8 (FacesChangedEventUnit_tBA9644CD496CD26CC75569DEC718E5E845635BAC* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral7DC363E2C257BFAA3463F6A31F8F519A7D6E18F3);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteral7DC363E2C257BFAA3463F6A31F8F519A7D6E18F3;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FacesChangedEventUnit_AssignArguments_mBD69A01EEA5552FF75E62DA4DE628CD7B0C3FB81 (FacesChangedEventUnit_tBA9644CD496CD26CC75569DEC718E5E845635BAC* __this, Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* ___0_flow, ARFacesChangedEventArgs_tC05D4423CB2096FF5955C3A0A00056EE2B6301AE ___1_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackablesChangedEventUnit_8_get_added_mD3E5CD7DD84402EC4A7054A36FF7678FB8A067F0_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackablesChangedEventUnit_8_get_removed_m1D98456EEB97142B45C4EE15B8443BA8499F97CB_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackablesChangedEventUnit_8_get_updated_m2B11703016B3C1EB9A5003ACC43AA9507300A5B5_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* L_0 = ___0_flow;
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_1;
		L_1 = TrackablesChangedEventUnit_8_get_added_mD3E5CD7DD84402EC4A7054A36FF7678FB8A067F0_inline(__this, TrackablesChangedEventUnit_8_get_added_mD3E5CD7DD84402EC4A7054A36FF7678FB8A067F0_RuntimeMethod_var);
		List_1_tA11EBED64210DD13BB4303415D948E6CD964AAF9* L_2;
		L_2 = ARFacesChangedEventArgs_get_added_m8EA1424F8F262955AFD00664B81C15AB54EFA213_inline((&___1_args), NULL);
		NullCheck(L_0);
		Flow_SetValue_m635BAF02F51D7C027C4F85157B80E2AB2DFB48A7(L_0, L_1, L_2, NULL);
		Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* L_3 = ___0_flow;
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_4;
		L_4 = TrackablesChangedEventUnit_8_get_updated_m2B11703016B3C1EB9A5003ACC43AA9507300A5B5_inline(__this, TrackablesChangedEventUnit_8_get_updated_m2B11703016B3C1EB9A5003ACC43AA9507300A5B5_RuntimeMethod_var);
		List_1_tA11EBED64210DD13BB4303415D948E6CD964AAF9* L_5;
		L_5 = ARFacesChangedEventArgs_get_updated_m1D3587AA05E5789F796A71C3C87CD6D11BE0005B_inline((&___1_args), NULL);
		NullCheck(L_3);
		Flow_SetValue_m635BAF02F51D7C027C4F85157B80E2AB2DFB48A7(L_3, L_4, L_5, NULL);
		Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* L_6 = ___0_flow;
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_7;
		L_7 = TrackablesChangedEventUnit_8_get_removed_m1D98456EEB97142B45C4EE15B8443BA8499F97CB_inline(__this, TrackablesChangedEventUnit_8_get_removed_m1D98456EEB97142B45C4EE15B8443BA8499F97CB_RuntimeMethod_var);
		List_1_tA11EBED64210DD13BB4303415D948E6CD964AAF9* L_8;
		L_8 = ARFacesChangedEventArgs_get_removed_mC31806DE526D3BECAA26C35DAE321F2DE77C335E_inline((&___1_args), NULL);
		NullCheck(L_6);
		Flow_SetValue_m635BAF02F51D7C027C4F85157B80E2AB2DFB48A7(L_6, L_7, L_8, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FacesChangedEventUnit__ctor_m33A7C04F6D09F5980669FA57262F17B9AB16E722 (FacesChangedEventUnit_tBA9644CD496CD26CC75569DEC718E5E845635BAC* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackablesChangedEventUnit_8__ctor_mAA11F3EE9A29DA1A20480633CA21003789A43AC2_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		TrackablesChangedEventUnit_8__ctor_mAA11F3EE9A29DA1A20480633CA21003789A43AC2(__this, TrackablesChangedEventUnit_8__ctor_mAA11F3EE9A29DA1A20480633CA21003789A43AC2_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* FaceUpdatedEventUnit_get_faceOut_m4C064F7BE0FDC5D28BAB8C72683F60B5399B091D (FaceUpdatedEventUnit_tBAF617E9D7CA035FC4D40FDA435F6BC35A39D426* __this, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = __this->___U3CfaceOutU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FaceUpdatedEventUnit_set_faceOut_mA1675257B969683B3E210624EFADE5F0F24A4811 (FaceUpdatedEventUnit_tBAF617E9D7CA035FC4D40FDA435F6BC35A39D426* __this, ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___0_value, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = ___0_value;
		__this->___U3CfaceOutU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CfaceOutU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* FaceUpdatedEventUnit_get_hookName_mC91F19ECC90B14DFE7A12AFC261B7904E875F79E (FaceUpdatedEventUnit_tBAF617E9D7CA035FC4D40FDA435F6BC35A39D426* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralDDE2D0A68406372F193061A2914ACA00C88A93D3);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteralDDE2D0A68406372F193061A2914ACA00C88A93D3;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Type_t* FaceUpdatedEventUnit_get_MessageListenerType_m9569E3DA99C317D7979CA8E3DDF3AA80D717601E (FaceUpdatedEventUnit_tBAF617E9D7CA035FC4D40FDA435F6BC35A39D426* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ARFaceListener_tCC12AA9BE708DFBC6BE058D621A5B173162E8A59_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Type_t_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_0 = { reinterpret_cast<intptr_t> (ARFaceListener_tCC12AA9BE708DFBC6BE058D621A5B173162E8A59_0_0_0_var) };
		il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
		Type_t* L_1;
		L_1 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FaceUpdatedEventUnit_Definition_mF72B08663AB57D86B075994731416C5B4B787138 (FaceUpdatedEventUnit_tBAF617E9D7CA035FC4D40FDA435F6BC35A39D426* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GameObjectEventUnit_1_Definition_mF8BEB75340AEF0E43AD3F4BC254109BD339F4E09_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Unit_ValueOutput_TisARFace_t73C068C9C8422F79A858EC41C322D399305B8453_mC94C38A35E16A3EA152B5E1D430FC6D2419FD31E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral32EE50C3CB7212B57D1B34DC4D1E5D299F118C16);
		s_Il2CppMethodInitialized = true;
	}
	{
		GameObjectEventUnit_1_Definition_mF8BEB75340AEF0E43AD3F4BC254109BD339F4E09(__this, GameObjectEventUnit_1_Definition_mF8BEB75340AEF0E43AD3F4BC254109BD339F4E09_RuntimeMethod_var);
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0;
		L_0 = Unit_ValueOutput_TisARFace_t73C068C9C8422F79A858EC41C322D399305B8453_mC94C38A35E16A3EA152B5E1D430FC6D2419FD31E(__this, _stringLiteral32EE50C3CB7212B57D1B34DC4D1E5D299F118C16, Unit_ValueOutput_TisARFace_t73C068C9C8422F79A858EC41C322D399305B8453_mC94C38A35E16A3EA152B5E1D430FC6D2419FD31E_RuntimeMethod_var);
		FaceUpdatedEventUnit_set_faceOut_mA1675257B969683B3E210624EFADE5F0F24A4811_inline(__this, L_0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FaceUpdatedEventUnit_AssignArguments_m3090FCDC05B47F2147891071BE013E615994E067 (FaceUpdatedEventUnit_tBAF617E9D7CA035FC4D40FDA435F6BC35A39D426* __this, Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* ___0_flow, ARFaceUpdatedEventArgs_tC20D17255858678A1A8230EA32D7C38287221FF6 ___1_args, const RuntimeMethod* method) 
{
	{
		Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* L_0 = ___0_flow;
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_1;
		L_1 = FaceUpdatedEventUnit_get_faceOut_m4C064F7BE0FDC5D28BAB8C72683F60B5399B091D_inline(__this, NULL);
		ARFace_t73C068C9C8422F79A858EC41C322D399305B8453* L_2;
		L_2 = ARFaceUpdatedEventArgs_get_face_mFD21EF59457212494C80FE998D1CA8AC0F057170_inline((&___1_args), NULL);
		NullCheck(L_0);
		Flow_SetValue_m635BAF02F51D7C027C4F85157B80E2AB2DFB48A7(L_0, L_1, L_2, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FaceUpdatedEventUnit__ctor_m3B3B46D3477D89D51EE72D0A636B5D982AC94E93 (FaceUpdatedEventUnit_tBAF617E9D7CA035FC4D40FDA435F6BC35A39D426* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GameObjectEventUnit_1__ctor_m12AD4CC830381744782178A62EDD22E8BF924894_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		GameObjectEventUnit_1__ctor_m12AD4CC830381744782178A62EDD22E8BF924894(__this, GameObjectEventUnit_1__ctor_m12AD4CC830381744782178A62EDD22E8BF924894_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* HumanBodiesChangedEventUnit_get_hookName_m67E5122D4D2D723303916F0C72BCA472CAF92768 (HumanBodiesChangedEventUnit_t7E27E223B038B0174E95448F4286BD590A46D54F* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral472B198FF9B13CC3CEB5817E644011EF36F83EF9);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteral472B198FF9B13CC3CEB5817E644011EF36F83EF9;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void HumanBodiesChangedEventUnit_AssignArguments_mDCA3EF5F7512E95706B68E4DD9F8DB9D253FD1B4 (HumanBodiesChangedEventUnit_t7E27E223B038B0174E95448F4286BD590A46D54F* __this, Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* ___0_flow, ARHumanBodiesChangedEventArgs_t19088E0D3384A22C5D8A542957705222C834720B ___1_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackablesChangedEventUnit_8_get_added_mB2970C7A1E0D78B2D6C4EE5E9DF55E8FB61F0414_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackablesChangedEventUnit_8_get_removed_m8F35AD75A58329A64455EA693E3AB62EDAA066C9_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackablesChangedEventUnit_8_get_updated_mD57DCBD8B6B61E09CC5E8AE90F6D728B4B99F008_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* L_0 = ___0_flow;
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_1;
		L_1 = TrackablesChangedEventUnit_8_get_added_mB2970C7A1E0D78B2D6C4EE5E9DF55E8FB61F0414_inline(__this, TrackablesChangedEventUnit_8_get_added_mB2970C7A1E0D78B2D6C4EE5E9DF55E8FB61F0414_RuntimeMethod_var);
		List_1_t64B1B4ABB789350AE017F3EAF4D3647AF59244C7* L_2;
		L_2 = ARHumanBodiesChangedEventArgs_get_added_m6FD862CC8C8281F9377D754BBAB7F3C616955D4C_inline((&___1_args), NULL);
		NullCheck(L_0);
		Flow_SetValue_m635BAF02F51D7C027C4F85157B80E2AB2DFB48A7(L_0, L_1, L_2, NULL);
		Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* L_3 = ___0_flow;
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_4;
		L_4 = TrackablesChangedEventUnit_8_get_updated_mD57DCBD8B6B61E09CC5E8AE90F6D728B4B99F008_inline(__this, TrackablesChangedEventUnit_8_get_updated_mD57DCBD8B6B61E09CC5E8AE90F6D728B4B99F008_RuntimeMethod_var);
		List_1_t64B1B4ABB789350AE017F3EAF4D3647AF59244C7* L_5;
		L_5 = ARHumanBodiesChangedEventArgs_get_updated_mA2FD128E22BDACD09294F107AA3F2A91CFFAA550_inline((&___1_args), NULL);
		NullCheck(L_3);
		Flow_SetValue_m635BAF02F51D7C027C4F85157B80E2AB2DFB48A7(L_3, L_4, L_5, NULL);
		Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* L_6 = ___0_flow;
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_7;
		L_7 = TrackablesChangedEventUnit_8_get_removed_m8F35AD75A58329A64455EA693E3AB62EDAA066C9_inline(__this, TrackablesChangedEventUnit_8_get_removed_m8F35AD75A58329A64455EA693E3AB62EDAA066C9_RuntimeMethod_var);
		List_1_t64B1B4ABB789350AE017F3EAF4D3647AF59244C7* L_8;
		L_8 = ARHumanBodiesChangedEventArgs_get_removed_m613516A989AA48A625C79A3C309D9C1465762BCA_inline((&___1_args), NULL);
		NullCheck(L_6);
		Flow_SetValue_m635BAF02F51D7C027C4F85157B80E2AB2DFB48A7(L_6, L_7, L_8, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void HumanBodiesChangedEventUnit__ctor_m67A7804169D1D1C464DE511BB251D2D1852C3C03 (HumanBodiesChangedEventUnit_t7E27E223B038B0174E95448F4286BD590A46D54F* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackablesChangedEventUnit_8__ctor_m1F8C079F96721DC237C11DA4A5D51E8B6FF363D6_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		TrackablesChangedEventUnit_8__ctor_m1F8C079F96721DC237C11DA4A5D51E8B6FF363D6(__this, TrackablesChangedEventUnit_8__ctor_m1F8C079F96721DC237C11DA4A5D51E8B6FF363D6_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* ParticipantsChangedEventUnit_get_hookName_mA4CDFD4FD4E062B9967AED9A88C976959565C5A6 (ParticipantsChangedEventUnit_tCB5A32ECD288DCDFDC4AC059E40E7552B424D1F8* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral873B4196DB10C0F07B11703CABABB6C8664BDBF4);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteral873B4196DB10C0F07B11703CABABB6C8664BDBF4;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ParticipantsChangedEventUnit_AssignArguments_m39BEEE8BBB7557918AC369100EEBED8441FC3948 (ParticipantsChangedEventUnit_tCB5A32ECD288DCDFDC4AC059E40E7552B424D1F8* __this, Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* ___0_flow, ARParticipantsChangedEventArgs_t2EAD9041A074A7ACEFF280662784747A4E85E527 ___1_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackablesChangedEventUnit_8_get_added_mC83DA1B5E5EEB690C2E89C2703CADE9E51A740C7_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackablesChangedEventUnit_8_get_removed_mA9FEE7FB7606E374551FE70A1B72D8199E237F70_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackablesChangedEventUnit_8_get_updated_mAE3D67A922C0F1E37CC29792138000F23621BCDD_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* L_0 = ___0_flow;
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_1;
		L_1 = TrackablesChangedEventUnit_8_get_added_mC83DA1B5E5EEB690C2E89C2703CADE9E51A740C7_inline(__this, TrackablesChangedEventUnit_8_get_added_mC83DA1B5E5EEB690C2E89C2703CADE9E51A740C7_RuntimeMethod_var);
		List_1_t87D9AC2D6B4A4860E01575669DEB0D164D4B33B3* L_2;
		L_2 = ARParticipantsChangedEventArgs_get_added_m06DA0DBF00575BB5D41A8AFE385ADAB9866BA678_inline((&___1_args), NULL);
		NullCheck(L_0);
		Flow_SetValue_m635BAF02F51D7C027C4F85157B80E2AB2DFB48A7(L_0, L_1, L_2, NULL);
		Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* L_3 = ___0_flow;
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_4;
		L_4 = TrackablesChangedEventUnit_8_get_updated_mAE3D67A922C0F1E37CC29792138000F23621BCDD_inline(__this, TrackablesChangedEventUnit_8_get_updated_mAE3D67A922C0F1E37CC29792138000F23621BCDD_RuntimeMethod_var);
		List_1_t87D9AC2D6B4A4860E01575669DEB0D164D4B33B3* L_5;
		L_5 = ARParticipantsChangedEventArgs_get_updated_mA0EC697655BDFEEB28064579FDD67DD166A2AE65_inline((&___1_args), NULL);
		NullCheck(L_3);
		Flow_SetValue_m635BAF02F51D7C027C4F85157B80E2AB2DFB48A7(L_3, L_4, L_5, NULL);
		Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* L_6 = ___0_flow;
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_7;
		L_7 = TrackablesChangedEventUnit_8_get_removed_mA9FEE7FB7606E374551FE70A1B72D8199E237F70_inline(__this, TrackablesChangedEventUnit_8_get_removed_mA9FEE7FB7606E374551FE70A1B72D8199E237F70_RuntimeMethod_var);
		List_1_t87D9AC2D6B4A4860E01575669DEB0D164D4B33B3* L_8;
		L_8 = ARParticipantsChangedEventArgs_get_removed_m6BBF2ABF2E71DB02A70122927AAD83E3AB35A77F_inline((&___1_args), NULL);
		NullCheck(L_6);
		Flow_SetValue_m635BAF02F51D7C027C4F85157B80E2AB2DFB48A7(L_6, L_7, L_8, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ParticipantsChangedEventUnit__ctor_m3ACA8D1E560935861985B00AA5BEADC1852420C6 (ParticipantsChangedEventUnit_tCB5A32ECD288DCDFDC4AC059E40E7552B424D1F8* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackablesChangedEventUnit_8__ctor_m924504E4BCE32657BE00FDA2558786DE79FE541C_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		TrackablesChangedEventUnit_8__ctor_m924504E4BCE32657BE00FDA2558786DE79FE541C(__this, TrackablesChangedEventUnit_8__ctor_m924504E4BCE32657BE00FDA2558786DE79FE541C_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* PlanesChangedEventUnit_get_hookName_m48C3C8CD9DE2A31ADDD018D9BC38E58D4A861CDA (PlanesChangedEventUnit_t2FE36340891DD55C64AD4A68C956533AFDB7D116* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral1E79F5F1B507D82BF4BE0B3BC9BE5CCC5A0A1C69);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteral1E79F5F1B507D82BF4BE0B3BC9BE5CCC5A0A1C69;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlanesChangedEventUnit_AssignArguments_m8920B6D4EFA5405677DD7E120F03079EA92C479D (PlanesChangedEventUnit_t2FE36340891DD55C64AD4A68C956533AFDB7D116* __this, Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* ___0_flow, ARPlanesChangedEventArgs_t8D63E0257BF9942EF8F8C0445F2FD46421017872 ___1_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackablesChangedEventUnit_8_get_added_m69D43E4E5EE721CF125D2EE3539284002A700B74_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackablesChangedEventUnit_8_get_removed_m53656F8942539C9E8684FF4379871CFC068B1A19_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackablesChangedEventUnit_8_get_updated_m990FD05AD7C047671F91787AFD37A2C9E856E2F7_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* L_0 = ___0_flow;
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_1;
		L_1 = TrackablesChangedEventUnit_8_get_added_m69D43E4E5EE721CF125D2EE3539284002A700B74_inline(__this, TrackablesChangedEventUnit_8_get_added_m69D43E4E5EE721CF125D2EE3539284002A700B74_RuntimeMethod_var);
		List_1_t10BA05B555C92BD54800DFF82DCAAFC9DE44A077* L_2;
		L_2 = ARPlanesChangedEventArgs_get_added_m6E00DD5F0B3261BCBAA8A029924A1F3F4179C747_inline((&___1_args), NULL);
		NullCheck(L_0);
		Flow_SetValue_m635BAF02F51D7C027C4F85157B80E2AB2DFB48A7(L_0, L_1, L_2, NULL);
		Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* L_3 = ___0_flow;
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_4;
		L_4 = TrackablesChangedEventUnit_8_get_updated_m990FD05AD7C047671F91787AFD37A2C9E856E2F7_inline(__this, TrackablesChangedEventUnit_8_get_updated_m990FD05AD7C047671F91787AFD37A2C9E856E2F7_RuntimeMethod_var);
		List_1_t10BA05B555C92BD54800DFF82DCAAFC9DE44A077* L_5;
		L_5 = ARPlanesChangedEventArgs_get_updated_mE979DAD93445C56FF7BEB732528D6CADAF0B6C22_inline((&___1_args), NULL);
		NullCheck(L_3);
		Flow_SetValue_m635BAF02F51D7C027C4F85157B80E2AB2DFB48A7(L_3, L_4, L_5, NULL);
		Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* L_6 = ___0_flow;
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_7;
		L_7 = TrackablesChangedEventUnit_8_get_removed_m53656F8942539C9E8684FF4379871CFC068B1A19_inline(__this, TrackablesChangedEventUnit_8_get_removed_m53656F8942539C9E8684FF4379871CFC068B1A19_RuntimeMethod_var);
		List_1_t10BA05B555C92BD54800DFF82DCAAFC9DE44A077* L_8;
		L_8 = ARPlanesChangedEventArgs_get_removed_m8CF2AA0278488746F8C55CC6C7DC1870531D63C8_inline((&___1_args), NULL);
		NullCheck(L_6);
		Flow_SetValue_m635BAF02F51D7C027C4F85157B80E2AB2DFB48A7(L_6, L_7, L_8, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlanesChangedEventUnit__ctor_mE065B63E0CE8FEE5F3FAF6751A9FA5081694E9A6 (PlanesChangedEventUnit_t2FE36340891DD55C64AD4A68C956533AFDB7D116* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackablesChangedEventUnit_8__ctor_m832218ED199936D78FC2DCAE7AFB8F4A557D210C_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		TrackablesChangedEventUnit_8__ctor_m832218ED199936D78FC2DCAE7AFB8F4A557D210C(__this, TrackablesChangedEventUnit_8__ctor_m832218ED199936D78FC2DCAE7AFB8F4A557D210C_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* PointCloudsChangedEventUnit_get_hookName_m361A47DC7CE10FBC6D4817EB3C49317C335F2DFE (PointCloudsChangedEventUnit_t70FDC749BF87C070761B2EEC7F80045BCBC542E9* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral3366B219987D5EE59C199D05D8DDF5CACB099288);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteral3366B219987D5EE59C199D05D8DDF5CACB099288;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PointCloudsChangedEventUnit_AssignArguments_mAA9FA32A721AA48E7643E8EAFE99C859AE4A0E22 (PointCloudsChangedEventUnit_t70FDC749BF87C070761B2EEC7F80045BCBC542E9* __this, Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* ___0_flow, ARPointCloudChangedEventArgs_t7DE4CA4D30D369496221061F89CC4BF84E42CC1B ___1_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackablesChangedEventUnit_8_get_added_m7BC749EABE3673D303FAB3CAA1C3746AE97E71A3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackablesChangedEventUnit_8_get_removed_m91B8BEF101F7958D60725FFFA15EF7D255467B59_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackablesChangedEventUnit_8_get_updated_mB0E1D4283CE77BC4F2A15B4BCF2351BB08321517_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* L_0 = ___0_flow;
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_1;
		L_1 = TrackablesChangedEventUnit_8_get_added_m7BC749EABE3673D303FAB3CAA1C3746AE97E71A3_inline(__this, TrackablesChangedEventUnit_8_get_added_m7BC749EABE3673D303FAB3CAA1C3746AE97E71A3_RuntimeMethod_var);
		List_1_tB9CD639EB4FE5EA0A8B417168BB9BD2D29AEE271* L_2;
		L_2 = ARPointCloudChangedEventArgs_get_added_m74DFD8ED4B3EADB5AC29DD0E748C84C6FA26DDE9_inline((&___1_args), NULL);
		NullCheck(L_0);
		Flow_SetValue_m635BAF02F51D7C027C4F85157B80E2AB2DFB48A7(L_0, L_1, L_2, NULL);
		Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* L_3 = ___0_flow;
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_4;
		L_4 = TrackablesChangedEventUnit_8_get_updated_mB0E1D4283CE77BC4F2A15B4BCF2351BB08321517_inline(__this, TrackablesChangedEventUnit_8_get_updated_mB0E1D4283CE77BC4F2A15B4BCF2351BB08321517_RuntimeMethod_var);
		List_1_tB9CD639EB4FE5EA0A8B417168BB9BD2D29AEE271* L_5;
		L_5 = ARPointCloudChangedEventArgs_get_updated_mD69E67FE8B06AB44B15F42F1D6EFA91F7E58F7C6_inline((&___1_args), NULL);
		NullCheck(L_3);
		Flow_SetValue_m635BAF02F51D7C027C4F85157B80E2AB2DFB48A7(L_3, L_4, L_5, NULL);
		Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* L_6 = ___0_flow;
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_7;
		L_7 = TrackablesChangedEventUnit_8_get_removed_m91B8BEF101F7958D60725FFFA15EF7D255467B59_inline(__this, TrackablesChangedEventUnit_8_get_removed_m91B8BEF101F7958D60725FFFA15EF7D255467B59_RuntimeMethod_var);
		List_1_tB9CD639EB4FE5EA0A8B417168BB9BD2D29AEE271* L_8;
		L_8 = ARPointCloudChangedEventArgs_get_removed_m64321D4F0D902494EE19EFF1B258981DE5C712B0_inline((&___1_args), NULL);
		NullCheck(L_6);
		Flow_SetValue_m635BAF02F51D7C027C4F85157B80E2AB2DFB48A7(L_6, L_7, L_8, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PointCloudsChangedEventUnit__ctor_m1FE03A5858ADDCF6CA0A326677C183993B91C121 (PointCloudsChangedEventUnit_t70FDC749BF87C070761B2EEC7F80045BCBC542E9* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackablesChangedEventUnit_8__ctor_m2BD406EFCF14C551D318E6422DE19A60B50B4B0E_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		TrackablesChangedEventUnit_8__ctor_m2BD406EFCF14C551D318E6422DE19A60B50B4B0E(__this, TrackablesChangedEventUnit_8__ctor_m2BD406EFCF14C551D318E6422DE19A60B50B4B0E_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* SessionStateChangedEventUnit_get_sessionStateOut_m0A6C8851E8BE80C79AEFAE297A6B8A47F68445B9 (SessionStateChangedEventUnit_tB622F7C1B352062C2D5D67A3EA1E1E6DBD910631* __this, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = __this->___U3CsessionStateOutU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SessionStateChangedEventUnit_set_sessionStateOut_mA37AE57139361EFD0003F0FF225B9A917359F04C (SessionStateChangedEventUnit_tB622F7C1B352062C2D5D67A3EA1E1E6DBD910631* __this, ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___0_value, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = ___0_value;
		__this->___U3CsessionStateOutU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CsessionStateOutU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* SessionStateChangedEventUnit_get_hookName_mAEFA9468C32ABAA500B9EA29659785F58AAED656 (SessionStateChangedEventUnit_tB622F7C1B352062C2D5D67A3EA1E1E6DBD910631* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralE6FB67187B5B59D4A24904A7962B4BDD32C3A56E);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteralE6FB67187B5B59D4A24904A7962B4BDD32C3A56E;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SessionStateChangedEventUnit_StartListening_mCD5FDDF66624175226E58047C04636B183F6E640 (SessionStateChangedEventUnit_tB622F7C1B352062C2D5D67A3EA1E1E6DBD910631* __this, GraphStack_tD9049F5FB5F8D9E9CD80764D18067D0F87343E7C* ___0_stack, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t325C602D999E964DB72210E493635E83F8D6CE68_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventUnit_1_StartListening_m988584D9E3245AAED2407FA0A7C0700E827FCAA8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SessionStateChangedEventUnit_HandleStateChange_m59A2A9D8AE5794385BC8A62BFA35EEF1183F4770_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SessionStateChangedEventUnit_tB622F7C1B352062C2D5D67A3EA1E1E6DBD910631_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		GraphStack_tD9049F5FB5F8D9E9CD80764D18067D0F87343E7C* L_0 = ___0_stack;
		EventUnit_1_StartListening_m988584D9E3245AAED2407FA0A7C0700E827FCAA8(__this, L_0, EventUnit_1_StartListening_m988584D9E3245AAED2407FA0A7C0700E827FCAA8_RuntimeMethod_var);
		bool L_1 = ((SessionStateChangedEventUnit_tB622F7C1B352062C2D5D67A3EA1E1E6DBD910631_StaticFields*)il2cpp_codegen_static_fields_for(SessionStateChangedEventUnit_tB622F7C1B352062C2D5D67A3EA1E1E6DBD910631_il2cpp_TypeInfo_var))->___s_CallbackIsRegistered;
		if (!L_1)
		{
			goto IL_000f;
		}
	}
	{
		return;
	}

IL_000f:
	{
		Action_1_t325C602D999E964DB72210E493635E83F8D6CE68* L_2 = (Action_1_t325C602D999E964DB72210E493635E83F8D6CE68*)il2cpp_codegen_object_new(Action_1_t325C602D999E964DB72210E493635E83F8D6CE68_il2cpp_TypeInfo_var);
		Action_1__ctor_mC9CFD5CBE62E447D22A4A520EB27144DCD47BBDB(L_2, NULL, (intptr_t)((void*)SessionStateChangedEventUnit_HandleStateChange_m59A2A9D8AE5794385BC8A62BFA35EEF1183F4770_RuntimeMethod_var), NULL);
		ARSession_add_stateChanged_mD40004D1EBE89E6CAC651632ABB8EADBE46692FC(L_2, NULL);
		((SessionStateChangedEventUnit_tB622F7C1B352062C2D5D67A3EA1E1E6DBD910631_StaticFields*)il2cpp_codegen_static_fields_for(SessionStateChangedEventUnit_tB622F7C1B352062C2D5D67A3EA1E1E6DBD910631_il2cpp_TypeInfo_var))->___s_CallbackIsRegistered = (bool)1;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SessionStateChangedEventUnit_StopListening_mF5443BFC057623A8AB81E12C257FACB4771EBCB8 (SessionStateChangedEventUnit_tB622F7C1B352062C2D5D67A3EA1E1E6DBD910631* __this, GraphStack_tD9049F5FB5F8D9E9CD80764D18067D0F87343E7C* ___0_stack, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t325C602D999E964DB72210E493635E83F8D6CE68_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventUnit_1_StopListening_m3291AC7B5A0B2228C69FF95A85772282389B48A6_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SessionStateChangedEventUnit_HandleStateChange_m59A2A9D8AE5794385BC8A62BFA35EEF1183F4770_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SessionStateChangedEventUnit_tB622F7C1B352062C2D5D67A3EA1E1E6DBD910631_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_1_t325C602D999E964DB72210E493635E83F8D6CE68* L_0 = (Action_1_t325C602D999E964DB72210E493635E83F8D6CE68*)il2cpp_codegen_object_new(Action_1_t325C602D999E964DB72210E493635E83F8D6CE68_il2cpp_TypeInfo_var);
		Action_1__ctor_mC9CFD5CBE62E447D22A4A520EB27144DCD47BBDB(L_0, NULL, (intptr_t)((void*)SessionStateChangedEventUnit_HandleStateChange_m59A2A9D8AE5794385BC8A62BFA35EEF1183F4770_RuntimeMethod_var), NULL);
		ARSession_remove_stateChanged_mDAC5A2DCA86363C495C66FB8CA92F46ABDFE3BE1(L_0, NULL);
		((SessionStateChangedEventUnit_tB622F7C1B352062C2D5D67A3EA1E1E6DBD910631_StaticFields*)il2cpp_codegen_static_fields_for(SessionStateChangedEventUnit_tB622F7C1B352062C2D5D67A3EA1E1E6DBD910631_il2cpp_TypeInfo_var))->___s_CallbackIsRegistered = (bool)0;
		GraphStack_tD9049F5FB5F8D9E9CD80764D18067D0F87343E7C* L_1 = ___0_stack;
		EventUnit_1_StopListening_m3291AC7B5A0B2228C69FF95A85772282389B48A6(__this, L_1, EventUnit_1_StopListening_m3291AC7B5A0B2228C69FF95A85772282389B48A6_RuntimeMethod_var);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SessionStateChangedEventUnit_HandleStateChange_m59A2A9D8AE5794385BC8A62BFA35EEF1183F4770 (ARSessionStateChangedEventArgs_tC31BD123D6B4F6168FB246762E79AB33962AE822 ___0_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventBus_Trigger_TisARSessionState_tAAADC649886D67F7134A0D9A665953D764A115D7_mE75403E46DB2EDDF96E1BF00CAB17E2C12C9BE79_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventBus_t6457FCB216134244ECE28921407E893BC1D38056_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralE6FB67187B5B59D4A24904A7962B4BDD32C3A56E);
		s_Il2CppMethodInitialized = true;
	}
	{
		EventHook_t1C9E2570B55940C342214BACB5E9EC889A113157 L_0;
		L_0 = EventHook_op_Implicit_m93EAC8789A8E846D55B193CB46237A48C54693FA(_stringLiteralE6FB67187B5B59D4A24904A7962B4BDD32C3A56E, NULL);
		int32_t L_1;
		L_1 = ARSessionStateChangedEventArgs_get_state_mC0A6FB4AF08C068BFD5D9730F60215BE662A950B_inline((&___0_args), NULL);
		il2cpp_codegen_runtime_class_init_inline(EventBus_t6457FCB216134244ECE28921407E893BC1D38056_il2cpp_TypeInfo_var);
		EventBus_Trigger_TisARSessionState_tAAADC649886D67F7134A0D9A665953D764A115D7_mE75403E46DB2EDDF96E1BF00CAB17E2C12C9BE79(L_0, L_1, EventBus_Trigger_TisARSessionState_tAAADC649886D67F7134A0D9A665953D764A115D7_mE75403E46DB2EDDF96E1BF00CAB17E2C12C9BE79_RuntimeMethod_var);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SessionStateChangedEventUnit_Definition_m13B3E0C78A229D7390013899679766F1F3D04A6A (SessionStateChangedEventUnit_tB622F7C1B352062C2D5D67A3EA1E1E6DBD910631* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventUnit_1_Definition_m4F542628DD546D8F60E22C3A609D7E4D9A980F8D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Unit_ValueOutput_TisARSessionState_tAAADC649886D67F7134A0D9A665953D764A115D7_m4BE5C8B6E8E7823F32C58B256BA74C4C5956BF1E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral22841DF93BF106671B9C411695F73A63FA8C4A0A);
		s_Il2CppMethodInitialized = true;
	}
	{
		EventUnit_1_Definition_m4F542628DD546D8F60E22C3A609D7E4D9A980F8D(__this, EventUnit_1_Definition_m4F542628DD546D8F60E22C3A609D7E4D9A980F8D_RuntimeMethod_var);
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0;
		L_0 = Unit_ValueOutput_TisARSessionState_tAAADC649886D67F7134A0D9A665953D764A115D7_m4BE5C8B6E8E7823F32C58B256BA74C4C5956BF1E(__this, _stringLiteral22841DF93BF106671B9C411695F73A63FA8C4A0A, Unit_ValueOutput_TisARSessionState_tAAADC649886D67F7134A0D9A665953D764A115D7_m4BE5C8B6E8E7823F32C58B256BA74C4C5956BF1E_RuntimeMethod_var);
		SessionStateChangedEventUnit_set_sessionStateOut_mA37AE57139361EFD0003F0FF225B9A917359F04C_inline(__this, L_0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SessionStateChangedEventUnit_AssignArguments_m112E323BB24EABC6CF7D933169F9A5FEA88F3B56 (SessionStateChangedEventUnit_tB622F7C1B352062C2D5D67A3EA1E1E6DBD910631* __this, Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* ___0_flow, int32_t ___1_state, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ARSessionState_tAAADC649886D67F7134A0D9A665953D764A115D7_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* L_0 = ___0_flow;
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_1;
		L_1 = SessionStateChangedEventUnit_get_sessionStateOut_m0A6C8851E8BE80C79AEFAE297A6B8A47F68445B9_inline(__this, NULL);
		int32_t L_2 = ___1_state;
		int32_t L_3 = L_2;
		RuntimeObject* L_4 = Box(ARSessionState_tAAADC649886D67F7134A0D9A665953D764A115D7_il2cpp_TypeInfo_var, &L_3);
		NullCheck(L_0);
		Flow_SetValue_m635BAF02F51D7C027C4F85157B80E2AB2DFB48A7(L_0, L_1, L_4, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SessionStateChangedEventUnit__ctor_m88A0475A1E868228B52D4994A322018EAA9BC80B (SessionStateChangedEventUnit_tB622F7C1B352062C2D5D67A3EA1E1E6DBD910631* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GlobalEventUnit_1__ctor_m8D2A36FE71F45881F8560975E9F957C36A08539E_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		GlobalEventUnit_1__ctor_m8D2A36FE71F45881F8560975E9F957C36A08539E(__this, GlobalEventUnit_1__ctor_m8D2A36FE71F45881F8560975E9F957C36A08539E_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* TrackedImagesChangedEventUnit_get_hookName_m71D20EC82716ADA07A7A4E12FF86561556111BEC (TrackedImagesChangedEventUnit_tEDE47E3521D6A7321912CFFCB5C10500CD949D29* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral241F175A1A7F5FA16826BDB6ABABA5CAF688AF00);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteral241F175A1A7F5FA16826BDB6ABABA5CAF688AF00;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TrackedImagesChangedEventUnit_AssignArguments_m99F331B5C10B48D05E6243C433A29FF8C2945EF7 (TrackedImagesChangedEventUnit_tEDE47E3521D6A7321912CFFCB5C10500CD949D29* __this, Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* ___0_flow, ARTrackedImagesChangedEventArgs_tD7718E40EE25D22E9143DA4F556C44980E18BE2F ___1_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackablesChangedEventUnit_8_get_added_m98C7465E257E576752368FE41D6911A596B9BD08_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackablesChangedEventUnit_8_get_removed_m0519BF012BE3FC785FF687EA5655A30E7B566A67_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackablesChangedEventUnit_8_get_updated_mDAC4907DBB50BF9C8B7A68F589F2827CB92E054C_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* L_0 = ___0_flow;
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_1;
		L_1 = TrackablesChangedEventUnit_8_get_added_m98C7465E257E576752368FE41D6911A596B9BD08_inline(__this, TrackablesChangedEventUnit_8_get_added_m98C7465E257E576752368FE41D6911A596B9BD08_RuntimeMethod_var);
		List_1_tFA730335FF46512E4245B2F46892CA318B394FAE* L_2;
		L_2 = ARTrackedImagesChangedEventArgs_get_added_m2929CC85141D13AF05C1484AFB47E043C6D3EE35_inline((&___1_args), NULL);
		NullCheck(L_0);
		Flow_SetValue_m635BAF02F51D7C027C4F85157B80E2AB2DFB48A7(L_0, L_1, L_2, NULL);
		Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* L_3 = ___0_flow;
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_4;
		L_4 = TrackablesChangedEventUnit_8_get_updated_mDAC4907DBB50BF9C8B7A68F589F2827CB92E054C_inline(__this, TrackablesChangedEventUnit_8_get_updated_mDAC4907DBB50BF9C8B7A68F589F2827CB92E054C_RuntimeMethod_var);
		List_1_tFA730335FF46512E4245B2F46892CA318B394FAE* L_5;
		L_5 = ARTrackedImagesChangedEventArgs_get_updated_m0C896E1C21EF35FF4B31B36563838EC2BA3CDFD1_inline((&___1_args), NULL);
		NullCheck(L_3);
		Flow_SetValue_m635BAF02F51D7C027C4F85157B80E2AB2DFB48A7(L_3, L_4, L_5, NULL);
		Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* L_6 = ___0_flow;
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_7;
		L_7 = TrackablesChangedEventUnit_8_get_removed_m0519BF012BE3FC785FF687EA5655A30E7B566A67_inline(__this, TrackablesChangedEventUnit_8_get_removed_m0519BF012BE3FC785FF687EA5655A30E7B566A67_RuntimeMethod_var);
		List_1_tFA730335FF46512E4245B2F46892CA318B394FAE* L_8;
		L_8 = ARTrackedImagesChangedEventArgs_get_removed_m062CBBEF163BEE47A673F7B3BDC0DD1C6EAEA185_inline((&___1_args), NULL);
		NullCheck(L_6);
		Flow_SetValue_m635BAF02F51D7C027C4F85157B80E2AB2DFB48A7(L_6, L_7, L_8, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TrackedImagesChangedEventUnit__ctor_mD97F8007E5D7D9FB4765DE62295E0646A63C85EF (TrackedImagesChangedEventUnit_tEDE47E3521D6A7321912CFFCB5C10500CD949D29* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackablesChangedEventUnit_8__ctor_m894F00CFC3DAF8C67CBB694581F43FB8D07DA5DE_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		TrackablesChangedEventUnit_8__ctor_m894F00CFC3DAF8C67CBB694581F43FB8D07DA5DE(__this, TrackablesChangedEventUnit_8__ctor_m894F00CFC3DAF8C67CBB694581F43FB8D07DA5DE_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* TrackedObjectsChangedEventUnit_get_hookName_m4CE8FAAAF5FD10E9B4C8B99D9576083C0F11DE6E (TrackedObjectsChangedEventUnit_t8EA5435947DF665B0FC12920629B4E6F1A787CF2* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralE44178F8CBCB7518AC61AD2FF65D32E8798D89A3);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteralE44178F8CBCB7518AC61AD2FF65D32E8798D89A3;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TrackedObjectsChangedEventUnit_AssignArguments_m5D3602536AD4FF039FDAC2A7F9CD3AFBC50F5ED0 (TrackedObjectsChangedEventUnit_t8EA5435947DF665B0FC12920629B4E6F1A787CF2* __this, Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* ___0_flow, ARTrackedObjectsChangedEventArgs_t378A63B91AD22EA4BA9C68805C72DDA4B7C01509 ___1_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackablesChangedEventUnit_8_get_added_m12B5AD020EE9860130BC183B984643D5242089D9_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackablesChangedEventUnit_8_get_removed_m9C8736E9632CDA45DADC1A4FB415361148A8A543_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackablesChangedEventUnit_8_get_updated_m2C3941B76B820CCC7161CA63CDCB10D6FBA498F1_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* L_0 = ___0_flow;
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_1;
		L_1 = TrackablesChangedEventUnit_8_get_added_m12B5AD020EE9860130BC183B984643D5242089D9_inline(__this, TrackablesChangedEventUnit_8_get_added_m12B5AD020EE9860130BC183B984643D5242089D9_RuntimeMethod_var);
		List_1_tF1201F2F725A8AE529D436F38A4771FB6790F3C6* L_2;
		L_2 = ARTrackedObjectsChangedEventArgs_get_added_m71E8E2055C979657273051DE23FCEB938964169A_inline((&___1_args), NULL);
		NullCheck(L_0);
		Flow_SetValue_m635BAF02F51D7C027C4F85157B80E2AB2DFB48A7(L_0, L_1, L_2, NULL);
		Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* L_3 = ___0_flow;
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_4;
		L_4 = TrackablesChangedEventUnit_8_get_updated_m2C3941B76B820CCC7161CA63CDCB10D6FBA498F1_inline(__this, TrackablesChangedEventUnit_8_get_updated_m2C3941B76B820CCC7161CA63CDCB10D6FBA498F1_RuntimeMethod_var);
		List_1_tF1201F2F725A8AE529D436F38A4771FB6790F3C6* L_5;
		L_5 = ARTrackedObjectsChangedEventArgs_get_updated_m01050D519E6281EECB48200D952FE1E1FFFE1A60_inline((&___1_args), NULL);
		NullCheck(L_3);
		Flow_SetValue_m635BAF02F51D7C027C4F85157B80E2AB2DFB48A7(L_3, L_4, L_5, NULL);
		Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* L_6 = ___0_flow;
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_7;
		L_7 = TrackablesChangedEventUnit_8_get_removed_m9C8736E9632CDA45DADC1A4FB415361148A8A543_inline(__this, TrackablesChangedEventUnit_8_get_removed_m9C8736E9632CDA45DADC1A4FB415361148A8A543_RuntimeMethod_var);
		List_1_tF1201F2F725A8AE529D436F38A4771FB6790F3C6* L_8;
		L_8 = ARTrackedObjectsChangedEventArgs_get_removed_mDF5FA585D41D4B0D4517D3C5387B254F6EA6DFDA_inline((&___1_args), NULL);
		NullCheck(L_6);
		Flow_SetValue_m635BAF02F51D7C027C4F85157B80E2AB2DFB48A7(L_6, L_7, L_8, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TrackedObjectsChangedEventUnit__ctor_mDBAAA2924F415BB174398367F96C61794310E52C (TrackedObjectsChangedEventUnit_t8EA5435947DF665B0FC12920629B4E6F1A787CF2* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackablesChangedEventUnit_8__ctor_m6D98CBE8151864AF5BBBDA138AE0D0E8DAAD995C_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		TrackablesChangedEventUnit_8__ctor_m6D98CBE8151864AF5BBBDA138AE0D0E8DAAD995C(__this, TrackablesChangedEventUnit_8__ctor_m6D98CBE8151864AF5BBBDA138AE0D0E8DAAD995C_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARAnchorManagerListener_OnTrackablesChanged_m4334EFEB7FF640C2029A8567B7F33FEA2F6012BF (ARAnchorManagerListener_t566E40F47C375A0FCD517AEAF5732FA1E9FB486A* __this, ARAnchorsChangedEventArgs_t755E6208C84304CAC448C133B0A8660CD5750A75 ___0_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventBus_Trigger_TisARAnchorsChangedEventArgs_t755E6208C84304CAC448C133B0A8660CD5750A75_mE9B6D9EEA77C3264949C5BA781F1278767B8D982_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventBus_t6457FCB216134244ECE28921407E893BC1D38056_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralBC7D1F049771153528B6E1B4730B5DD939FF7F47);
		s_Il2CppMethodInitialized = true;
	}
	{
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_0;
		L_0 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(__this, NULL);
		ARAnchorsChangedEventArgs_t755E6208C84304CAC448C133B0A8660CD5750A75 L_1 = ___0_args;
		il2cpp_codegen_runtime_class_init_inline(EventBus_t6457FCB216134244ECE28921407E893BC1D38056_il2cpp_TypeInfo_var);
		EventBus_Trigger_TisARAnchorsChangedEventArgs_t755E6208C84304CAC448C133B0A8660CD5750A75_mE9B6D9EEA77C3264949C5BA781F1278767B8D982(_stringLiteralBC7D1F049771153528B6E1B4730B5DD939FF7F47, L_0, L_1, EventBus_Trigger_TisARAnchorsChangedEventArgs_t755E6208C84304CAC448C133B0A8660CD5750A75_mE9B6D9EEA77C3264949C5BA781F1278767B8D982_RuntimeMethod_var);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARAnchorManagerListener_RegisterTrackablesChangedDelegate_mB98A79FE77937C7B254E99A4DB9280987DF96A3C (ARAnchorManagerListener_t566E40F47C375A0FCD517AEAF5732FA1E9FB486A* __this, ARAnchorManager_tF68C46300BD6F2E83E3360BF9297C3C50F5D6B3E* ___0_manager, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ARAnchorManagerListener_OnTrackablesChanged_m4334EFEB7FF640C2029A8567B7F33FEA2F6012BF_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t11340E5174173030E076A75316E18A395082F8A5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ARAnchorManager_tF68C46300BD6F2E83E3360BF9297C3C50F5D6B3E* L_0 = ___0_manager;
		Action_1_t11340E5174173030E076A75316E18A395082F8A5* L_1 = (Action_1_t11340E5174173030E076A75316E18A395082F8A5*)il2cpp_codegen_object_new(Action_1_t11340E5174173030E076A75316E18A395082F8A5_il2cpp_TypeInfo_var);
		Action_1__ctor_m27F591D84CB51009686E04C84ED35045DB8DC4B1(L_1, __this, (intptr_t)((void*)ARAnchorManagerListener_OnTrackablesChanged_m4334EFEB7FF640C2029A8567B7F33FEA2F6012BF_RuntimeMethod_var), NULL);
		NullCheck(L_0);
		ARAnchorManager_add_anchorsChanged_m9AEAD2307BB09DCC5093D8BAD242AAED777CC5D0(L_0, L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARAnchorManagerListener_UnregisterTrackablesChangedDelegate_mD523B3EE5EBC73350C2121DC8CDC326FAB330B3C (ARAnchorManagerListener_t566E40F47C375A0FCD517AEAF5732FA1E9FB486A* __this, ARAnchorManager_tF68C46300BD6F2E83E3360BF9297C3C50F5D6B3E* ___0_manager, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ARAnchorManagerListener_OnTrackablesChanged_m4334EFEB7FF640C2029A8567B7F33FEA2F6012BF_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t11340E5174173030E076A75316E18A395082F8A5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ARAnchorManager_tF68C46300BD6F2E83E3360BF9297C3C50F5D6B3E* L_0 = ___0_manager;
		Action_1_t11340E5174173030E076A75316E18A395082F8A5* L_1 = (Action_1_t11340E5174173030E076A75316E18A395082F8A5*)il2cpp_codegen_object_new(Action_1_t11340E5174173030E076A75316E18A395082F8A5_il2cpp_TypeInfo_var);
		Action_1__ctor_m27F591D84CB51009686E04C84ED35045DB8DC4B1(L_1, __this, (intptr_t)((void*)ARAnchorManagerListener_OnTrackablesChanged_m4334EFEB7FF640C2029A8567B7F33FEA2F6012BF_RuntimeMethod_var), NULL);
		NullCheck(L_0);
		ARAnchorManager_remove_anchorsChanged_mF59C4EA911963F93B03E01D799007FDD18E7B0D6(L_0, L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARAnchorManagerListener__ctor_m3446119DC76812D6B10825B9E3DF3F54F59F210B (ARAnchorManagerListener_t566E40F47C375A0FCD517AEAF5732FA1E9FB486A* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackableManagerListener_1__ctor_m9D5A585C61BEE09064B5EB54CC3D5DF04A38A279_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		TrackableManagerListener_1__ctor_m9D5A585C61BEE09064B5EB54CC3D5DF04A38A279(__this, TrackableManagerListener_1__ctor_m9D5A585C61BEE09064B5EB54CC3D5DF04A38A279_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARCameraManagerListener_OnEnable_m3FCCA525327452EC89BEB0B6192E785675354BBD (ARCameraManagerListener_t9CDC77A2AAA0062BF970292523A3AD20D76BB3AE* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ARCameraManagerListener_OnFrameReceived_mC4DF9EC830D8147C5E4604BBE32DD5D289C7E901_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t54327BAFA15E24E002EFAE48D1AFFC020D368735_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponent_TisARCameraManager_t6E957EA922911B8447D086D9FB1F860EC967BD9F_mD0C05CBA43EFDC472C6C4BB588C366709A88B182_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_FindObjectOfType_TisARCameraManager_t6E957EA922911B8447D086D9FB1F860EC967BD9F_mF55990D2494651CA4DEEE92470F8B341AFF1A089_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral0C2C7A9E7436DA4E0013C1BB4B0A6143CE0F38ED);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral105B4D37F778AEF4041E8AB742473DC80494D94B);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral2E09EF05EED3B12F6A56A89C2BC22D0E77DC5C9E);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral6C60DEBFC7F1B80F535AA96A50E69DC5071AE93D);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral88C714A35938E8B6F0BAFDF2B7AD9DBCB055A63D);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralE9AD4157A19CF89606B880798B83375D3DB7E6C1);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralF3E84B722399601AD7E281754E917478AA9AD48D);
		s_Il2CppMethodInitialized = true;
	}
	{
		ARCameraManager_t6E957EA922911B8447D086D9FB1F860EC967BD9F* L_0;
		L_0 = Component_GetComponent_TisARCameraManager_t6E957EA922911B8447D086D9FB1F860EC967BD9F_mD0C05CBA43EFDC472C6C4BB588C366709A88B182(__this, Component_GetComponent_TisARCameraManager_t6E957EA922911B8447D086D9FB1F860EC967BD9F_mD0C05CBA43EFDC472C6C4BB588C366709A88B182_RuntimeMethod_var);
		__this->___m_Manager = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___m_Manager), (void*)L_0);
		ARCameraManager_t6E957EA922911B8447D086D9FB1F860EC967BD9F* L_1 = __this->___m_Manager;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_2;
		L_2 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_1, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_2)
		{
			goto IL_011c;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		ARCameraManager_t6E957EA922911B8447D086D9FB1F860EC967BD9F* L_3;
		L_3 = Object_FindObjectOfType_TisARCameraManager_t6E957EA922911B8447D086D9FB1F860EC967BD9F_mF55990D2494651CA4DEEE92470F8B341AFF1A089(Object_FindObjectOfType_TisARCameraManager_t6E957EA922911B8447D086D9FB1F860EC967BD9F_mF55990D2494651CA4DEEE92470F8B341AFF1A089_RuntimeMethod_var);
		__this->___m_Manager = L_3;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___m_Manager), (void*)L_3);
		ARCameraManager_t6E957EA922911B8447D086D9FB1F860EC967BD9F* L_4 = __this->___m_Manager;
		bool L_5;
		L_5 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_4, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_5)
		{
			goto IL_0051;
		}
	}
	{
		Type_t* L_6;
		L_6 = Object_GetType_mE10A8FC1E57F3DF29972CCBC026C2DC3942263B3(__this, NULL);
		NullCheck(L_6);
		String_t* L_7;
		L_7 = VirtualFuncInvoker0< String_t* >::Invoke(7, L_6);
		String_t* L_8;
		L_8 = String_Concat_m9E3155FB84015C823606188F53B47CB44C444991(L_7, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral296249B54F612A320F18BA11A9DB197EFE4BB84A)), NULL);
		InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB* L_9 = (InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB_il2cpp_TypeInfo_var)));
		InvalidOperationException__ctor_mE4CB6F4712AB6D99A2358FBAE2E052B3EE976162(L_9, L_8, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_9, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ARCameraManagerListener_OnEnable_m3FCCA525327452EC89BEB0B6192E785675354BBD_RuntimeMethod_var)));
	}

IL_0051:
	{
		StringBuilder_t* L_10 = __this->___m_LogBuilder;
		Type_t* L_11;
		L_11 = Object_GetType_mE10A8FC1E57F3DF29972CCBC026C2DC3942263B3(__this, NULL);
		NullCheck(L_11);
		String_t* L_12;
		L_12 = VirtualFuncInvoker0< String_t* >::Invoke(7, L_11);
		String_t* L_13;
		L_13 = String_Concat_m9E3155FB84015C823606188F53B47CB44C444991(L_12, _stringLiteralE9AD4157A19CF89606B880798B83375D3DB7E6C1, NULL);
		NullCheck(L_10);
		StringBuilder_t* L_14;
		L_14 = StringBuilder_Append_m08904D74E0C78E5F36DCD9C9303BDD07886D9F7D(L_10, L_13, NULL);
		StringBuilder_t* L_15 = __this->___m_LogBuilder;
		String_t* L_16;
		L_16 = Object_get_name_mAC2F6B897CF1303BA4249B4CB55271AFACBB6392(__this, NULL);
		String_t* L_17;
		L_17 = String_Concat_m8855A6DE10F84DA7F4EC113CADDB59873A25573B(_stringLiteral6C60DEBFC7F1B80F535AA96A50E69DC5071AE93D, L_16, _stringLiteralF3E84B722399601AD7E281754E917478AA9AD48D, NULL);
		NullCheck(L_15);
		StringBuilder_t* L_18;
		L_18 = StringBuilder_Append_m08904D74E0C78E5F36DCD9C9303BDD07886D9F7D(L_15, L_17, NULL);
		StringBuilder_t* L_19 = __this->___m_LogBuilder;
		ARCameraManager_t6E957EA922911B8447D086D9FB1F860EC967BD9F* L_20 = __this->___m_Manager;
		NullCheck(L_20);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_21;
		L_21 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(L_20, NULL);
		NullCheck(L_21);
		String_t* L_22;
		L_22 = Object_get_name_mAC2F6B897CF1303BA4249B4CB55271AFACBB6392(L_21, NULL);
		String_t* L_23;
		L_23 = String_Concat_m8855A6DE10F84DA7F4EC113CADDB59873A25573B(_stringLiteral2E09EF05EED3B12F6A56A89C2BC22D0E77DC5C9E, L_22, _stringLiteralF3E84B722399601AD7E281754E917478AA9AD48D, NULL);
		NullCheck(L_19);
		StringBuilder_t* L_24;
		L_24 = StringBuilder_Append_m08904D74E0C78E5F36DCD9C9303BDD07886D9F7D(L_19, L_23, NULL);
		StringBuilder_t* L_25 = __this->___m_LogBuilder;
		ARCameraManager_t6E957EA922911B8447D086D9FB1F860EC967BD9F* L_26 = __this->___m_Manager;
		NullCheck(L_26);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_27;
		L_27 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(L_26, NULL);
		NullCheck(L_27);
		String_t* L_28;
		L_28 = Object_get_name_mAC2F6B897CF1303BA4249B4CB55271AFACBB6392(L_27, NULL);
		String_t* L_29;
		L_29 = Object_get_name_mAC2F6B897CF1303BA4249B4CB55271AFACBB6392(__this, NULL);
		String_t* L_30;
		L_30 = String_Concat_m093934F71A9B351911EE46311674ED463B180006(_stringLiteral105B4D37F778AEF4041E8AB742473DC80494D94B, L_28, _stringLiteral0C2C7A9E7436DA4E0013C1BB4B0A6143CE0F38ED, L_29, NULL);
		NullCheck(L_25);
		StringBuilder_t* L_31;
		L_31 = StringBuilder_Append_m08904D74E0C78E5F36DCD9C9303BDD07886D9F7D(L_25, L_30, NULL);
		StringBuilder_t* L_32 = __this->___m_LogBuilder;
		NullCheck(L_32);
		StringBuilder_t* L_33;
		L_33 = StringBuilder_Append_m08904D74E0C78E5F36DCD9C9303BDD07886D9F7D(L_32, _stringLiteral88C714A35938E8B6F0BAFDF2B7AD9DBCB055A63D, NULL);
		StringBuilder_t* L_34 = __this->___m_LogBuilder;
		NullCheck(L_34);
		String_t* L_35;
		L_35 = VirtualFuncInvoker0< String_t* >::Invoke(3, L_34);
		il2cpp_codegen_runtime_class_init_inline(Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		Debug_LogWarning_m33EF1B897E0C7C6FF538989610BFAFFEF4628CA9(L_35, NULL);
		StringBuilder_t* L_36 = __this->___m_LogBuilder;
		NullCheck(L_36);
		StringBuilder_t* L_37;
		L_37 = StringBuilder_Clear_m2D76F6533574F40A4E3E2DC4E730277CBD0AF8F6(L_36, NULL);
	}

IL_011c:
	{
		ARCameraManager_t6E957EA922911B8447D086D9FB1F860EC967BD9F* L_38 = __this->___m_Manager;
		Action_1_t54327BAFA15E24E002EFAE48D1AFFC020D368735* L_39 = (Action_1_t54327BAFA15E24E002EFAE48D1AFFC020D368735*)il2cpp_codegen_object_new(Action_1_t54327BAFA15E24E002EFAE48D1AFFC020D368735_il2cpp_TypeInfo_var);
		Action_1__ctor_mDFD48034EFC4DF74EEB44B18C74DD7BAF391814E(L_39, __this, (intptr_t)((void*)ARCameraManagerListener_OnFrameReceived_mC4DF9EC830D8147C5E4604BBE32DD5D289C7E901_RuntimeMethod_var), NULL);
		NullCheck(L_38);
		ARCameraManager_add_frameReceived_mB30CD73DB7A25A6E7BC486BA289DBB99CBA3B7A1(L_38, L_39, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARCameraManagerListener_OnDisable_mE4E3D8277DFBA681900BD3B3F9F2335C2DC7BA27 (ARCameraManagerListener_t9CDC77A2AAA0062BF970292523A3AD20D76BB3AE* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ARCameraManagerListener_OnFrameReceived_mC4DF9EC830D8147C5E4604BBE32DD5D289C7E901_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t54327BAFA15E24E002EFAE48D1AFFC020D368735_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ARCameraManager_t6E957EA922911B8447D086D9FB1F860EC967BD9F* L_0 = __this->___m_Manager;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_0, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_1)
		{
			goto IL_0025;
		}
	}
	{
		ARCameraManager_t6E957EA922911B8447D086D9FB1F860EC967BD9F* L_2 = __this->___m_Manager;
		Action_1_t54327BAFA15E24E002EFAE48D1AFFC020D368735* L_3 = (Action_1_t54327BAFA15E24E002EFAE48D1AFFC020D368735*)il2cpp_codegen_object_new(Action_1_t54327BAFA15E24E002EFAE48D1AFFC020D368735_il2cpp_TypeInfo_var);
		Action_1__ctor_mDFD48034EFC4DF74EEB44B18C74DD7BAF391814E(L_3, __this, (intptr_t)((void*)ARCameraManagerListener_OnFrameReceived_mC4DF9EC830D8147C5E4604BBE32DD5D289C7E901_RuntimeMethod_var), NULL);
		NullCheck(L_2);
		ARCameraManager_remove_frameReceived_m8D9C301201B5079323169510656821F0286AA67D(L_2, L_3, NULL);
	}

IL_0025:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARCameraManagerListener_OnFrameReceived_mC4DF9EC830D8147C5E4604BBE32DD5D289C7E901 (ARCameraManagerListener_t9CDC77A2AAA0062BF970292523A3AD20D76BB3AE* __this, ARCameraFrameEventArgs_t4060608C37C2B7248AFC908876309CC36D53E0EC ___0_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventBus_Trigger_TisARCameraFrameEventArgs_t4060608C37C2B7248AFC908876309CC36D53E0EC_m147D0F4E1AEA08F04B42A886DD7FD396ABE2448B_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventBus_t6457FCB216134244ECE28921407E893BC1D38056_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralF78A6EBA0E7E34457C9FFEC3652864FEDD473081);
		s_Il2CppMethodInitialized = true;
	}
	{
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_0;
		L_0 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(__this, NULL);
		ARCameraFrameEventArgs_t4060608C37C2B7248AFC908876309CC36D53E0EC L_1 = ___0_args;
		il2cpp_codegen_runtime_class_init_inline(EventBus_t6457FCB216134244ECE28921407E893BC1D38056_il2cpp_TypeInfo_var);
		EventBus_Trigger_TisARCameraFrameEventArgs_t4060608C37C2B7248AFC908876309CC36D53E0EC_m147D0F4E1AEA08F04B42A886DD7FD396ABE2448B(_stringLiteralF78A6EBA0E7E34457C9FFEC3652864FEDD473081, L_0, L_1, EventBus_Trigger_TisARCameraFrameEventArgs_t4060608C37C2B7248AFC908876309CC36D53E0EC_m147D0F4E1AEA08F04B42A886DD7FD396ABE2448B_RuntimeMethod_var);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARCameraManagerListener__ctor_mAC77E9D72A839372BB3B36491951FBC01AF001E8 (ARCameraManagerListener_t9CDC77A2AAA0062BF970292523A3AD20D76BB3AE* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&StringBuilder_t_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		StringBuilder_t* L_0 = (StringBuilder_t*)il2cpp_codegen_object_new(StringBuilder_t_il2cpp_TypeInfo_var);
		StringBuilder__ctor_m1D99713357DE05DAFA296633639DB55F8C30587D(L_0, NULL);
		__this->___m_LogBuilder = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___m_LogBuilder), (void*)L_0);
		MessageListener__ctor_m8DA61A2F8AD16822DAF82D7FBEF6100E0AE5761E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AREnvironmentProbeManagerListener_OnTrackablesChanged_mFFE922C02832EEF6AC8F645762C471B3BEF8B93D (AREnvironmentProbeManagerListener_t919D09DA2D7FF7958381E8B7494E6C5A1D6D1533* __this, AREnvironmentProbesChangedEvent_t8757E98E11F3D37F0CFC4589FCA5CD2107F8375C ___0_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventBus_Trigger_TisAREnvironmentProbesChangedEvent_t8757E98E11F3D37F0CFC4589FCA5CD2107F8375C_mF3EF7B9EF8367C19AE598C0BB4C5C819E5E9DDA3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventBus_t6457FCB216134244ECE28921407E893BC1D38056_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral0EAF6D0EBB42EC97177547351019DB3DC3130C07);
		s_Il2CppMethodInitialized = true;
	}
	{
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_0;
		L_0 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(__this, NULL);
		AREnvironmentProbesChangedEvent_t8757E98E11F3D37F0CFC4589FCA5CD2107F8375C L_1 = ___0_args;
		il2cpp_codegen_runtime_class_init_inline(EventBus_t6457FCB216134244ECE28921407E893BC1D38056_il2cpp_TypeInfo_var);
		EventBus_Trigger_TisAREnvironmentProbesChangedEvent_t8757E98E11F3D37F0CFC4589FCA5CD2107F8375C_mF3EF7B9EF8367C19AE598C0BB4C5C819E5E9DDA3(_stringLiteral0EAF6D0EBB42EC97177547351019DB3DC3130C07, L_0, L_1, EventBus_Trigger_TisAREnvironmentProbesChangedEvent_t8757E98E11F3D37F0CFC4589FCA5CD2107F8375C_mF3EF7B9EF8367C19AE598C0BB4C5C819E5E9DDA3_RuntimeMethod_var);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AREnvironmentProbeManagerListener_RegisterTrackablesChangedDelegate_m872353C32A0321432B32E69E1BC8FF679B046962 (AREnvironmentProbeManagerListener_t919D09DA2D7FF7958381E8B7494E6C5A1D6D1533* __this, AREnvironmentProbeManager_tE457B73006F0D5136D9F2BDDEB072C1A1DB407F8* ___0_manager, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AREnvironmentProbeManagerListener_OnTrackablesChanged_mFFE922C02832EEF6AC8F645762C471B3BEF8B93D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_tE3E0B091088607877804833E23BFDBBF5594B830_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		AREnvironmentProbeManager_tE457B73006F0D5136D9F2BDDEB072C1A1DB407F8* L_0 = ___0_manager;
		Action_1_tE3E0B091088607877804833E23BFDBBF5594B830* L_1 = (Action_1_tE3E0B091088607877804833E23BFDBBF5594B830*)il2cpp_codegen_object_new(Action_1_tE3E0B091088607877804833E23BFDBBF5594B830_il2cpp_TypeInfo_var);
		Action_1__ctor_mD1DD23E09BBCBCDCB59373D479AA93CADF7E5C48(L_1, __this, (intptr_t)((void*)AREnvironmentProbeManagerListener_OnTrackablesChanged_mFFE922C02832EEF6AC8F645762C471B3BEF8B93D_RuntimeMethod_var), NULL);
		NullCheck(L_0);
		AREnvironmentProbeManager_add_environmentProbesChanged_mADB9F6A00161EF9FECCFF0C76FC346E5A6321A96(L_0, L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AREnvironmentProbeManagerListener_UnregisterTrackablesChangedDelegate_m7D1DB80EA456F3BEA9A0D7A4184467B8925478E3 (AREnvironmentProbeManagerListener_t919D09DA2D7FF7958381E8B7494E6C5A1D6D1533* __this, AREnvironmentProbeManager_tE457B73006F0D5136D9F2BDDEB072C1A1DB407F8* ___0_manager, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AREnvironmentProbeManagerListener_OnTrackablesChanged_mFFE922C02832EEF6AC8F645762C471B3BEF8B93D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_tE3E0B091088607877804833E23BFDBBF5594B830_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		AREnvironmentProbeManager_tE457B73006F0D5136D9F2BDDEB072C1A1DB407F8* L_0 = ___0_manager;
		Action_1_tE3E0B091088607877804833E23BFDBBF5594B830* L_1 = (Action_1_tE3E0B091088607877804833E23BFDBBF5594B830*)il2cpp_codegen_object_new(Action_1_tE3E0B091088607877804833E23BFDBBF5594B830_il2cpp_TypeInfo_var);
		Action_1__ctor_mD1DD23E09BBCBCDCB59373D479AA93CADF7E5C48(L_1, __this, (intptr_t)((void*)AREnvironmentProbeManagerListener_OnTrackablesChanged_mFFE922C02832EEF6AC8F645762C471B3BEF8B93D_RuntimeMethod_var), NULL);
		NullCheck(L_0);
		AREnvironmentProbeManager_remove_environmentProbesChanged_mABD9405FD4C3272C46B79CA7586901101C243FC7(L_0, L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AREnvironmentProbeManagerListener__ctor_mDDB6426B47643A97BB37711BA5C7031FDDDD9171 (AREnvironmentProbeManagerListener_t919D09DA2D7FF7958381E8B7494E6C5A1D6D1533* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackableManagerListener_1__ctor_mF10CA320B096183174E077D4F6F5A996C3B43363_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		TrackableManagerListener_1__ctor_mF10CA320B096183174E077D4F6F5A996C3B43363(__this, TrackableManagerListener_1__ctor_mF10CA320B096183174E077D4F6F5A996C3B43363_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARFaceListener_OnFaceUpdated_mEB4873976DAAC601F816A95E0E36C93CC6C43D51 (ARFaceListener_tCC12AA9BE708DFBC6BE058D621A5B173162E8A59* __this, ARFaceUpdatedEventArgs_tC20D17255858678A1A8230EA32D7C38287221FF6 ___0_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventBus_Trigger_TisARFaceUpdatedEventArgs_tC20D17255858678A1A8230EA32D7C38287221FF6_m36EBB182855F50B6EA52073A9C5843D3FE22B837_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventBus_t6457FCB216134244ECE28921407E893BC1D38056_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralDDE2D0A68406372F193061A2914ACA00C88A93D3);
		s_Il2CppMethodInitialized = true;
	}
	{
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_0;
		L_0 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(__this, NULL);
		ARFaceUpdatedEventArgs_tC20D17255858678A1A8230EA32D7C38287221FF6 L_1 = ___0_args;
		il2cpp_codegen_runtime_class_init_inline(EventBus_t6457FCB216134244ECE28921407E893BC1D38056_il2cpp_TypeInfo_var);
		EventBus_Trigger_TisARFaceUpdatedEventArgs_tC20D17255858678A1A8230EA32D7C38287221FF6_m36EBB182855F50B6EA52073A9C5843D3FE22B837(_stringLiteralDDE2D0A68406372F193061A2914ACA00C88A93D3, L_0, L_1, EventBus_Trigger_TisARFaceUpdatedEventArgs_tC20D17255858678A1A8230EA32D7C38287221FF6_m36EBB182855F50B6EA52073A9C5843D3FE22B837_RuntimeMethod_var);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARFaceListener_OnEnable_mFB7CEACFC165E077636A31D574F41CAEFB2737A1 (ARFaceListener_tCC12AA9BE708DFBC6BE058D621A5B173162E8A59* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ARFaceListener_OnFaceUpdated_mEB4873976DAAC601F816A95E0E36C93CC6C43D51_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t30DB722576CDAD6BB3F09BD60358BF49AA947548_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponent_TisARFace_t73C068C9C8422F79A858EC41C322D399305B8453_mDBDD6B87463FFFEC97691FFEDD402BE1F8D1BC07_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ARFace_t73C068C9C8422F79A858EC41C322D399305B8453* L_0;
		L_0 = Component_GetComponent_TisARFace_t73C068C9C8422F79A858EC41C322D399305B8453_mDBDD6B87463FFFEC97691FFEDD402BE1F8D1BC07(__this, Component_GetComponent_TisARFace_t73C068C9C8422F79A858EC41C322D399305B8453_mDBDD6B87463FFFEC97691FFEDD402BE1F8D1BC07_RuntimeMethod_var);
		__this->___m_Face = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___m_Face), (void*)L_0);
		ARFace_t73C068C9C8422F79A858EC41C322D399305B8453* L_1 = __this->___m_Face;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_2;
		L_2 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_1, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_2)
		{
			goto IL_0035;
		}
	}
	{
		String_t* L_3;
		L_3 = Object_get_name_mAC2F6B897CF1303BA4249B4CB55271AFACBB6392(__this, NULL);
		String_t* L_4;
		L_4 = String_Concat_m8855A6DE10F84DA7F4EC113CADDB59873A25573B(((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral74A7350AADB2E77B2FEC504172F6A7570C2061B4)), L_3, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralB724CA1414F1B6A0513232FC2A7137B8A9657B4D)), NULL);
		InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB* L_5 = (InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB_il2cpp_TypeInfo_var)));
		InvalidOperationException__ctor_mE4CB6F4712AB6D99A2358FBAE2E052B3EE976162(L_5, L_4, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_5, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ARFaceListener_OnEnable_mFB7CEACFC165E077636A31D574F41CAEFB2737A1_RuntimeMethod_var)));
	}

IL_0035:
	{
		ARFace_t73C068C9C8422F79A858EC41C322D399305B8453* L_6 = __this->___m_Face;
		Action_1_t30DB722576CDAD6BB3F09BD60358BF49AA947548* L_7 = (Action_1_t30DB722576CDAD6BB3F09BD60358BF49AA947548*)il2cpp_codegen_object_new(Action_1_t30DB722576CDAD6BB3F09BD60358BF49AA947548_il2cpp_TypeInfo_var);
		Action_1__ctor_m3A71B4EF8E0C69EED8BBC0BB8AE9856BEA1929D1(L_7, __this, (intptr_t)((void*)ARFaceListener_OnFaceUpdated_mEB4873976DAAC601F816A95E0E36C93CC6C43D51_RuntimeMethod_var), NULL);
		NullCheck(L_6);
		ARFace_add_updated_m20975C4A22DDFCC57D15C0205754C3902A699738(L_6, L_7, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARFaceListener_OnDisable_m97C2530EC7E803706F1E64CFCCEBAE565D439525 (ARFaceListener_tCC12AA9BE708DFBC6BE058D621A5B173162E8A59* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ARFaceListener_OnFaceUpdated_mEB4873976DAAC601F816A95E0E36C93CC6C43D51_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t30DB722576CDAD6BB3F09BD60358BF49AA947548_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ARFace_t73C068C9C8422F79A858EC41C322D399305B8453* L_0 = __this->___m_Face;
		Action_1_t30DB722576CDAD6BB3F09BD60358BF49AA947548* L_1 = (Action_1_t30DB722576CDAD6BB3F09BD60358BF49AA947548*)il2cpp_codegen_object_new(Action_1_t30DB722576CDAD6BB3F09BD60358BF49AA947548_il2cpp_TypeInfo_var);
		Action_1__ctor_m3A71B4EF8E0C69EED8BBC0BB8AE9856BEA1929D1(L_1, __this, (intptr_t)((void*)ARFaceListener_OnFaceUpdated_mEB4873976DAAC601F816A95E0E36C93CC6C43D51_RuntimeMethod_var), NULL);
		NullCheck(L_0);
		ARFace_remove_updated_m289BD0DA8FE0FF5A2936B6FD21FB4895AFB61499(L_0, L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARFaceListener__ctor_mF6F04669987369D3F349D4E6817E9E84A1C6EC12 (ARFaceListener_tCC12AA9BE708DFBC6BE058D621A5B173162E8A59* __this, const RuntimeMethod* method) 
{
	{
		MessageListener__ctor_m8DA61A2F8AD16822DAF82D7FBEF6100E0AE5761E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARFaceManagerListener_OnTrackablesChanged_m5F42EC3570B5D34BAE69E833677980E334D97E84 (ARFaceManagerListener_t8043177537F4EAE01597D66B7EB8968FBAA5811A* __this, ARFacesChangedEventArgs_tC05D4423CB2096FF5955C3A0A00056EE2B6301AE ___0_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventBus_Trigger_TisARFacesChangedEventArgs_tC05D4423CB2096FF5955C3A0A00056EE2B6301AE_mF3D39EAC80939C5697B3EE63307DD1AFB338914D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventBus_t6457FCB216134244ECE28921407E893BC1D38056_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral7DC363E2C257BFAA3463F6A31F8F519A7D6E18F3);
		s_Il2CppMethodInitialized = true;
	}
	{
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_0;
		L_0 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(__this, NULL);
		ARFacesChangedEventArgs_tC05D4423CB2096FF5955C3A0A00056EE2B6301AE L_1 = ___0_args;
		il2cpp_codegen_runtime_class_init_inline(EventBus_t6457FCB216134244ECE28921407E893BC1D38056_il2cpp_TypeInfo_var);
		EventBus_Trigger_TisARFacesChangedEventArgs_tC05D4423CB2096FF5955C3A0A00056EE2B6301AE_mF3D39EAC80939C5697B3EE63307DD1AFB338914D(_stringLiteral7DC363E2C257BFAA3463F6A31F8F519A7D6E18F3, L_0, L_1, EventBus_Trigger_TisARFacesChangedEventArgs_tC05D4423CB2096FF5955C3A0A00056EE2B6301AE_mF3D39EAC80939C5697B3EE63307DD1AFB338914D_RuntimeMethod_var);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARFaceManagerListener_RegisterTrackablesChangedDelegate_m1C8CED37ADBCCDE1125F632BCC683C6DF4210E47 (ARFaceManagerListener_t8043177537F4EAE01597D66B7EB8968FBAA5811A* __this, ARFaceManager_tE5955187C9D7C9C4C4139F522DA0DEA69EFCFD8F* ___0_manager, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ARFaceManagerListener_OnTrackablesChanged_m5F42EC3570B5D34BAE69E833677980E334D97E84_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t0A16CC637B74FE43A20D2784BCA3172E7D13789C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ARFaceManager_tE5955187C9D7C9C4C4139F522DA0DEA69EFCFD8F* L_0 = ___0_manager;
		Action_1_t0A16CC637B74FE43A20D2784BCA3172E7D13789C* L_1 = (Action_1_t0A16CC637B74FE43A20D2784BCA3172E7D13789C*)il2cpp_codegen_object_new(Action_1_t0A16CC637B74FE43A20D2784BCA3172E7D13789C_il2cpp_TypeInfo_var);
		Action_1__ctor_m6B29F8F1F0039A82C7A8CE016F79E8D39054E4D4(L_1, __this, (intptr_t)((void*)ARFaceManagerListener_OnTrackablesChanged_m5F42EC3570B5D34BAE69E833677980E334D97E84_RuntimeMethod_var), NULL);
		NullCheck(L_0);
		ARFaceManager_add_facesChanged_m5EA6C3526D8E44028C6DCF7A6297D7EAE240105E(L_0, L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARFaceManagerListener_UnregisterTrackablesChangedDelegate_m98D01C89E58172CBA368330001F3805AB37D2014 (ARFaceManagerListener_t8043177537F4EAE01597D66B7EB8968FBAA5811A* __this, ARFaceManager_tE5955187C9D7C9C4C4139F522DA0DEA69EFCFD8F* ___0_manager, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ARFaceManagerListener_OnTrackablesChanged_m5F42EC3570B5D34BAE69E833677980E334D97E84_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t0A16CC637B74FE43A20D2784BCA3172E7D13789C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ARFaceManager_tE5955187C9D7C9C4C4139F522DA0DEA69EFCFD8F* L_0 = ___0_manager;
		Action_1_t0A16CC637B74FE43A20D2784BCA3172E7D13789C* L_1 = (Action_1_t0A16CC637B74FE43A20D2784BCA3172E7D13789C*)il2cpp_codegen_object_new(Action_1_t0A16CC637B74FE43A20D2784BCA3172E7D13789C_il2cpp_TypeInfo_var);
		Action_1__ctor_m6B29F8F1F0039A82C7A8CE016F79E8D39054E4D4(L_1, __this, (intptr_t)((void*)ARFaceManagerListener_OnTrackablesChanged_m5F42EC3570B5D34BAE69E833677980E334D97E84_RuntimeMethod_var), NULL);
		NullCheck(L_0);
		ARFaceManager_remove_facesChanged_mBFBA84C023749078DC7D801945CF83373B336DCC(L_0, L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARFaceManagerListener__ctor_m5FAC77D48D54B77D1AF6767F9CC1A9FD8C47579D (ARFaceManagerListener_t8043177537F4EAE01597D66B7EB8968FBAA5811A* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackableManagerListener_1__ctor_mC84640FEC1BBC643B3517C43EC073EEA79FC8667_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		TrackableManagerListener_1__ctor_mC84640FEC1BBC643B3517C43EC073EEA79FC8667(__this, TrackableManagerListener_1__ctor_mC84640FEC1BBC643B3517C43EC073EEA79FC8667_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARHumanBodyManagerListener_OnTrackablesChanged_mC8B24E64F660D8C4F29C2567E233795C7905E8D8 (ARHumanBodyManagerListener_tD428DC6513EF466039758E70361DF5E6F429589A* __this, ARHumanBodiesChangedEventArgs_t19088E0D3384A22C5D8A542957705222C834720B ___0_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventBus_Trigger_TisARHumanBodiesChangedEventArgs_t19088E0D3384A22C5D8A542957705222C834720B_m053372DF7FDC0D251BE56C17199E478393C48C13_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventBus_t6457FCB216134244ECE28921407E893BC1D38056_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral472B198FF9B13CC3CEB5817E644011EF36F83EF9);
		s_Il2CppMethodInitialized = true;
	}
	{
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_0;
		L_0 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(__this, NULL);
		ARHumanBodiesChangedEventArgs_t19088E0D3384A22C5D8A542957705222C834720B L_1 = ___0_args;
		il2cpp_codegen_runtime_class_init_inline(EventBus_t6457FCB216134244ECE28921407E893BC1D38056_il2cpp_TypeInfo_var);
		EventBus_Trigger_TisARHumanBodiesChangedEventArgs_t19088E0D3384A22C5D8A542957705222C834720B_m053372DF7FDC0D251BE56C17199E478393C48C13(_stringLiteral472B198FF9B13CC3CEB5817E644011EF36F83EF9, L_0, L_1, EventBus_Trigger_TisARHumanBodiesChangedEventArgs_t19088E0D3384A22C5D8A542957705222C834720B_m053372DF7FDC0D251BE56C17199E478393C48C13_RuntimeMethod_var);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARHumanBodyManagerListener_RegisterTrackablesChangedDelegate_mC4B68B78F1C8A14D91BB47766F0D9CFBC3E76665 (ARHumanBodyManagerListener_tD428DC6513EF466039758E70361DF5E6F429589A* __this, ARHumanBodyManager_tA5246205B817A3D4A87BFC54FC018FB513867BFB* ___0_manager, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ARHumanBodyManagerListener_OnTrackablesChanged_mC8B24E64F660D8C4F29C2567E233795C7905E8D8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t78DC7DD8CB74A1B21E4419996B784C6E51821247_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ARHumanBodyManager_tA5246205B817A3D4A87BFC54FC018FB513867BFB* L_0 = ___0_manager;
		Action_1_t78DC7DD8CB74A1B21E4419996B784C6E51821247* L_1 = (Action_1_t78DC7DD8CB74A1B21E4419996B784C6E51821247*)il2cpp_codegen_object_new(Action_1_t78DC7DD8CB74A1B21E4419996B784C6E51821247_il2cpp_TypeInfo_var);
		Action_1__ctor_mCFC45AE4A743ED62D221EC7E1918738EE0C00B60(L_1, __this, (intptr_t)((void*)ARHumanBodyManagerListener_OnTrackablesChanged_mC8B24E64F660D8C4F29C2567E233795C7905E8D8_RuntimeMethod_var), NULL);
		NullCheck(L_0);
		ARHumanBodyManager_add_humanBodiesChanged_mAD886F8E010310FD75E072CDCD0478D40659D318(L_0, L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARHumanBodyManagerListener_UnregisterTrackablesChangedDelegate_mF252B1724CB1EE3FB8C778EA76B178D1189D9A6F (ARHumanBodyManagerListener_tD428DC6513EF466039758E70361DF5E6F429589A* __this, ARHumanBodyManager_tA5246205B817A3D4A87BFC54FC018FB513867BFB* ___0_manager, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ARHumanBodyManagerListener_OnTrackablesChanged_mC8B24E64F660D8C4F29C2567E233795C7905E8D8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t78DC7DD8CB74A1B21E4419996B784C6E51821247_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ARHumanBodyManager_tA5246205B817A3D4A87BFC54FC018FB513867BFB* L_0 = ___0_manager;
		Action_1_t78DC7DD8CB74A1B21E4419996B784C6E51821247* L_1 = (Action_1_t78DC7DD8CB74A1B21E4419996B784C6E51821247*)il2cpp_codegen_object_new(Action_1_t78DC7DD8CB74A1B21E4419996B784C6E51821247_il2cpp_TypeInfo_var);
		Action_1__ctor_mCFC45AE4A743ED62D221EC7E1918738EE0C00B60(L_1, __this, (intptr_t)((void*)ARHumanBodyManagerListener_OnTrackablesChanged_mC8B24E64F660D8C4F29C2567E233795C7905E8D8_RuntimeMethod_var), NULL);
		NullCheck(L_0);
		ARHumanBodyManager_remove_humanBodiesChanged_m868BF1A9ABD143149D3E4C4F04C6204B5768D7FB(L_0, L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARHumanBodyManagerListener__ctor_m1C30D9B89578DFD325B5670B6864480FEA735595 (ARHumanBodyManagerListener_tD428DC6513EF466039758E70361DF5E6F429589A* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackableManagerListener_1__ctor_m2E928AC462404314A3931061A1142F70B3236122_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		TrackableManagerListener_1__ctor_m2E928AC462404314A3931061A1142F70B3236122(__this, TrackableManagerListener_1__ctor_m2E928AC462404314A3931061A1142F70B3236122_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARParticipantManagerListener_OnTrackablesChanged_mFBD305D70AFFBB9ADDE54456B499CEE28B7586F0 (ARParticipantManagerListener_tD1B316B50151D4E2F897C85D37E9EA0CE76DD180* __this, ARParticipantsChangedEventArgs_t2EAD9041A074A7ACEFF280662784747A4E85E527 ___0_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventBus_Trigger_TisARParticipantsChangedEventArgs_t2EAD9041A074A7ACEFF280662784747A4E85E527_m1AFA7138412723D9A851AE84D192CF53B2FD2CEF_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventBus_t6457FCB216134244ECE28921407E893BC1D38056_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral873B4196DB10C0F07B11703CABABB6C8664BDBF4);
		s_Il2CppMethodInitialized = true;
	}
	{
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_0;
		L_0 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(__this, NULL);
		ARParticipantsChangedEventArgs_t2EAD9041A074A7ACEFF280662784747A4E85E527 L_1 = ___0_args;
		il2cpp_codegen_runtime_class_init_inline(EventBus_t6457FCB216134244ECE28921407E893BC1D38056_il2cpp_TypeInfo_var);
		EventBus_Trigger_TisARParticipantsChangedEventArgs_t2EAD9041A074A7ACEFF280662784747A4E85E527_m1AFA7138412723D9A851AE84D192CF53B2FD2CEF(_stringLiteral873B4196DB10C0F07B11703CABABB6C8664BDBF4, L_0, L_1, EventBus_Trigger_TisARParticipantsChangedEventArgs_t2EAD9041A074A7ACEFF280662784747A4E85E527_m1AFA7138412723D9A851AE84D192CF53B2FD2CEF_RuntimeMethod_var);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARParticipantManagerListener_RegisterTrackablesChangedDelegate_m4299A76DBAD136E3E3008B0E13A5C59986C70F25 (ARParticipantManagerListener_tD1B316B50151D4E2F897C85D37E9EA0CE76DD180* __this, ARParticipantManager_t759CB1A6961D7C7F99E47B8AFFF7BCA4761C99C4* ___0_manager, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ARParticipantManagerListener_OnTrackablesChanged_mFBD305D70AFFBB9ADDE54456B499CEE28B7586F0_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_tE5F1AC110E5F6610E8B460B753F57ACC9B4524E3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ARParticipantManager_t759CB1A6961D7C7F99E47B8AFFF7BCA4761C99C4* L_0 = ___0_manager;
		Action_1_tE5F1AC110E5F6610E8B460B753F57ACC9B4524E3* L_1 = (Action_1_tE5F1AC110E5F6610E8B460B753F57ACC9B4524E3*)il2cpp_codegen_object_new(Action_1_tE5F1AC110E5F6610E8B460B753F57ACC9B4524E3_il2cpp_TypeInfo_var);
		Action_1__ctor_m0C34C0D8022B4CB26E739D328681C24E65FC460C(L_1, __this, (intptr_t)((void*)ARParticipantManagerListener_OnTrackablesChanged_mFBD305D70AFFBB9ADDE54456B499CEE28B7586F0_RuntimeMethod_var), NULL);
		NullCheck(L_0);
		ARParticipantManager_add_participantsChanged_m7CB947B046B890EFA75726000DBECA7E86FE8775(L_0, L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARParticipantManagerListener_UnregisterTrackablesChangedDelegate_mCF7B6C59B0BE204CF23E8AD061B1FB5B8A91FA4A (ARParticipantManagerListener_tD1B316B50151D4E2F897C85D37E9EA0CE76DD180* __this, ARParticipantManager_t759CB1A6961D7C7F99E47B8AFFF7BCA4761C99C4* ___0_manager, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ARParticipantManagerListener_OnTrackablesChanged_mFBD305D70AFFBB9ADDE54456B499CEE28B7586F0_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_tE5F1AC110E5F6610E8B460B753F57ACC9B4524E3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ARParticipantManager_t759CB1A6961D7C7F99E47B8AFFF7BCA4761C99C4* L_0 = ___0_manager;
		Action_1_tE5F1AC110E5F6610E8B460B753F57ACC9B4524E3* L_1 = (Action_1_tE5F1AC110E5F6610E8B460B753F57ACC9B4524E3*)il2cpp_codegen_object_new(Action_1_tE5F1AC110E5F6610E8B460B753F57ACC9B4524E3_il2cpp_TypeInfo_var);
		Action_1__ctor_m0C34C0D8022B4CB26E739D328681C24E65FC460C(L_1, __this, (intptr_t)((void*)ARParticipantManagerListener_OnTrackablesChanged_mFBD305D70AFFBB9ADDE54456B499CEE28B7586F0_RuntimeMethod_var), NULL);
		NullCheck(L_0);
		ARParticipantManager_remove_participantsChanged_m8D99AE38D87E378337E82DC2359079A2448D447F(L_0, L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARParticipantManagerListener__ctor_m49FD1669DE44469CC5E5588DFB5673EB92D35D66 (ARParticipantManagerListener_tD1B316B50151D4E2F897C85D37E9EA0CE76DD180* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackableManagerListener_1__ctor_m07B978E212E0DC99C7A64CE3576F87A983D1676A_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		TrackableManagerListener_1__ctor_m07B978E212E0DC99C7A64CE3576F87A983D1676A(__this, TrackableManagerListener_1__ctor_m07B978E212E0DC99C7A64CE3576F87A983D1676A_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARPlaneManagerListener_OnTrackablesChanged_mB2C63B0FEA0C321CE64E042C4CC659F3858F7406 (ARPlaneManagerListener_t48EF97E316472A73C564EB0B7A71B067D0BBCE76* __this, ARPlanesChangedEventArgs_t8D63E0257BF9942EF8F8C0445F2FD46421017872 ___0_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventBus_Trigger_TisARPlanesChangedEventArgs_t8D63E0257BF9942EF8F8C0445F2FD46421017872_m95B4A178E0E6B6E98EE3F2CCBA22EF47977C9BCB_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventBus_t6457FCB216134244ECE28921407E893BC1D38056_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral1E79F5F1B507D82BF4BE0B3BC9BE5CCC5A0A1C69);
		s_Il2CppMethodInitialized = true;
	}
	{
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_0;
		L_0 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(__this, NULL);
		ARPlanesChangedEventArgs_t8D63E0257BF9942EF8F8C0445F2FD46421017872 L_1 = ___0_args;
		il2cpp_codegen_runtime_class_init_inline(EventBus_t6457FCB216134244ECE28921407E893BC1D38056_il2cpp_TypeInfo_var);
		EventBus_Trigger_TisARPlanesChangedEventArgs_t8D63E0257BF9942EF8F8C0445F2FD46421017872_m95B4A178E0E6B6E98EE3F2CCBA22EF47977C9BCB(_stringLiteral1E79F5F1B507D82BF4BE0B3BC9BE5CCC5A0A1C69, L_0, L_1, EventBus_Trigger_TisARPlanesChangedEventArgs_t8D63E0257BF9942EF8F8C0445F2FD46421017872_m95B4A178E0E6B6E98EE3F2CCBA22EF47977C9BCB_RuntimeMethod_var);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARPlaneManagerListener_RegisterTrackablesChangedDelegate_m41E2E4D926F04FA4902C694C68832508349C47AF (ARPlaneManagerListener_t48EF97E316472A73C564EB0B7A71B067D0BBCE76* __this, ARPlaneManager_t9DA6C512BDCFFBEE8B3DAA7170EB3BFFDE26A928* ___0_manager, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ARPlaneManagerListener_OnTrackablesChanged_mB2C63B0FEA0C321CE64E042C4CC659F3858F7406_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t009680BF19017ECA60753C7F605CBD85C56C6560_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ARPlaneManager_t9DA6C512BDCFFBEE8B3DAA7170EB3BFFDE26A928* L_0 = ___0_manager;
		Action_1_t009680BF19017ECA60753C7F605CBD85C56C6560* L_1 = (Action_1_t009680BF19017ECA60753C7F605CBD85C56C6560*)il2cpp_codegen_object_new(Action_1_t009680BF19017ECA60753C7F605CBD85C56C6560_il2cpp_TypeInfo_var);
		Action_1__ctor_mBEDDD2BF96A8A407FBB833F764447E82E3D50BCF(L_1, __this, (intptr_t)((void*)ARPlaneManagerListener_OnTrackablesChanged_mB2C63B0FEA0C321CE64E042C4CC659F3858F7406_RuntimeMethod_var), NULL);
		NullCheck(L_0);
		ARPlaneManager_add_planesChanged_m295E8F0B09AF78A2D9249F3C07BFD4D4F749F36D(L_0, L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARPlaneManagerListener_UnregisterTrackablesChangedDelegate_m8E80A7CE9E87E8FCB787B341E5F1B34D8AFC8B0A (ARPlaneManagerListener_t48EF97E316472A73C564EB0B7A71B067D0BBCE76* __this, ARPlaneManager_t9DA6C512BDCFFBEE8B3DAA7170EB3BFFDE26A928* ___0_manager, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ARPlaneManagerListener_OnTrackablesChanged_mB2C63B0FEA0C321CE64E042C4CC659F3858F7406_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t009680BF19017ECA60753C7F605CBD85C56C6560_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ARPlaneManager_t9DA6C512BDCFFBEE8B3DAA7170EB3BFFDE26A928* L_0 = ___0_manager;
		Action_1_t009680BF19017ECA60753C7F605CBD85C56C6560* L_1 = (Action_1_t009680BF19017ECA60753C7F605CBD85C56C6560*)il2cpp_codegen_object_new(Action_1_t009680BF19017ECA60753C7F605CBD85C56C6560_il2cpp_TypeInfo_var);
		Action_1__ctor_mBEDDD2BF96A8A407FBB833F764447E82E3D50BCF(L_1, __this, (intptr_t)((void*)ARPlaneManagerListener_OnTrackablesChanged_mB2C63B0FEA0C321CE64E042C4CC659F3858F7406_RuntimeMethod_var), NULL);
		NullCheck(L_0);
		ARPlaneManager_remove_planesChanged_m86DE7059D87299391D2EE97A884E53CCE80B3A5E(L_0, L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARPlaneManagerListener__ctor_mC0BA10E19292774E8F44C7BAB23725C169A0F5BD (ARPlaneManagerListener_t48EF97E316472A73C564EB0B7A71B067D0BBCE76* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackableManagerListener_1__ctor_m6D8E3E517736CC5906267C97680E382F66641F03_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		TrackableManagerListener_1__ctor_m6D8E3E517736CC5906267C97680E382F66641F03(__this, TrackableManagerListener_1__ctor_m6D8E3E517736CC5906267C97680E382F66641F03_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARPointCloudManagerListener_OnTrackablesChanged_m37F4B0894E58642522DD88A5B70B2D6425D171F2 (ARPointCloudManagerListener_t79362431A1598431A74D9AB30FF9CAC2C7D6FB40* __this, ARPointCloudChangedEventArgs_t7DE4CA4D30D369496221061F89CC4BF84E42CC1B ___0_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventBus_Trigger_TisARPointCloudChangedEventArgs_t7DE4CA4D30D369496221061F89CC4BF84E42CC1B_mE0DDE4D49AE7A3FB982B04E07E2475F9D93977DE_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventBus_t6457FCB216134244ECE28921407E893BC1D38056_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral3366B219987D5EE59C199D05D8DDF5CACB099288);
		s_Il2CppMethodInitialized = true;
	}
	{
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_0;
		L_0 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(__this, NULL);
		ARPointCloudChangedEventArgs_t7DE4CA4D30D369496221061F89CC4BF84E42CC1B L_1 = ___0_args;
		il2cpp_codegen_runtime_class_init_inline(EventBus_t6457FCB216134244ECE28921407E893BC1D38056_il2cpp_TypeInfo_var);
		EventBus_Trigger_TisARPointCloudChangedEventArgs_t7DE4CA4D30D369496221061F89CC4BF84E42CC1B_mE0DDE4D49AE7A3FB982B04E07E2475F9D93977DE(_stringLiteral3366B219987D5EE59C199D05D8DDF5CACB099288, L_0, L_1, EventBus_Trigger_TisARPointCloudChangedEventArgs_t7DE4CA4D30D369496221061F89CC4BF84E42CC1B_mE0DDE4D49AE7A3FB982B04E07E2475F9D93977DE_RuntimeMethod_var);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARPointCloudManagerListener_RegisterTrackablesChangedDelegate_m8FDD10C3610B31F4C6755847ABFF706E74EEA786 (ARPointCloudManagerListener_t79362431A1598431A74D9AB30FF9CAC2C7D6FB40* __this, ARPointCloudManager_t28D1833BCE1A48657E504109A89A3B75D0AD7D9F* ___0_manager, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ARPointCloudManagerListener_OnTrackablesChanged_m37F4B0894E58642522DD88A5B70B2D6425D171F2_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t053CCF2809F9B0B61EA8298154533C5FACB724F0_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ARPointCloudManager_t28D1833BCE1A48657E504109A89A3B75D0AD7D9F* L_0 = ___0_manager;
		Action_1_t053CCF2809F9B0B61EA8298154533C5FACB724F0* L_1 = (Action_1_t053CCF2809F9B0B61EA8298154533C5FACB724F0*)il2cpp_codegen_object_new(Action_1_t053CCF2809F9B0B61EA8298154533C5FACB724F0_il2cpp_TypeInfo_var);
		Action_1__ctor_m8C819B4CABAFC405C1DEBA65B1E220746F258AB6(L_1, __this, (intptr_t)((void*)ARPointCloudManagerListener_OnTrackablesChanged_m37F4B0894E58642522DD88A5B70B2D6425D171F2_RuntimeMethod_var), NULL);
		NullCheck(L_0);
		ARPointCloudManager_add_pointCloudsChanged_m108F43CDA88DC192BA5DE3EBD7DFFDE3C59E26B3(L_0, L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARPointCloudManagerListener_UnregisterTrackablesChangedDelegate_m211D23F127AE987796DD35083C3BBDB15ED63928 (ARPointCloudManagerListener_t79362431A1598431A74D9AB30FF9CAC2C7D6FB40* __this, ARPointCloudManager_t28D1833BCE1A48657E504109A89A3B75D0AD7D9F* ___0_manager, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ARPointCloudManagerListener_OnTrackablesChanged_m37F4B0894E58642522DD88A5B70B2D6425D171F2_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t053CCF2809F9B0B61EA8298154533C5FACB724F0_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ARPointCloudManager_t28D1833BCE1A48657E504109A89A3B75D0AD7D9F* L_0 = ___0_manager;
		Action_1_t053CCF2809F9B0B61EA8298154533C5FACB724F0* L_1 = (Action_1_t053CCF2809F9B0B61EA8298154533C5FACB724F0*)il2cpp_codegen_object_new(Action_1_t053CCF2809F9B0B61EA8298154533C5FACB724F0_il2cpp_TypeInfo_var);
		Action_1__ctor_m8C819B4CABAFC405C1DEBA65B1E220746F258AB6(L_1, __this, (intptr_t)((void*)ARPointCloudManagerListener_OnTrackablesChanged_m37F4B0894E58642522DD88A5B70B2D6425D171F2_RuntimeMethod_var), NULL);
		NullCheck(L_0);
		ARPointCloudManager_remove_pointCloudsChanged_m28A4E43DFFB1A517F932B1A613664782D068C8C0(L_0, L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARPointCloudManagerListener__ctor_mAC662B594114A7F330A910B4DF50C7D489B0C0D8 (ARPointCloudManagerListener_t79362431A1598431A74D9AB30FF9CAC2C7D6FB40* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackableManagerListener_1__ctor_m3DE0DE01F5CEAED2E7F5AD649EB4E55205725541_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		TrackableManagerListener_1__ctor_m3DE0DE01F5CEAED2E7F5AD649EB4E55205725541(__this, TrackableManagerListener_1__ctor_m3DE0DE01F5CEAED2E7F5AD649EB4E55205725541_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARTrackedImageManagerListener_OnTrackablesChanged_mB7342F42759F03B7495C3863A66339D21A50B229 (ARTrackedImageManagerListener_t41A4A505BF375F0C883703891EB4C03EE271F4E1* __this, ARTrackedImagesChangedEventArgs_tD7718E40EE25D22E9143DA4F556C44980E18BE2F ___0_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventBus_Trigger_TisARTrackedImagesChangedEventArgs_tD7718E40EE25D22E9143DA4F556C44980E18BE2F_m543E103E3042925BB06EAC45E637282A71E1F853_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventBus_t6457FCB216134244ECE28921407E893BC1D38056_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral241F175A1A7F5FA16826BDB6ABABA5CAF688AF00);
		s_Il2CppMethodInitialized = true;
	}
	{
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_0;
		L_0 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(__this, NULL);
		ARTrackedImagesChangedEventArgs_tD7718E40EE25D22E9143DA4F556C44980E18BE2F L_1 = ___0_args;
		il2cpp_codegen_runtime_class_init_inline(EventBus_t6457FCB216134244ECE28921407E893BC1D38056_il2cpp_TypeInfo_var);
		EventBus_Trigger_TisARTrackedImagesChangedEventArgs_tD7718E40EE25D22E9143DA4F556C44980E18BE2F_m543E103E3042925BB06EAC45E637282A71E1F853(_stringLiteral241F175A1A7F5FA16826BDB6ABABA5CAF688AF00, L_0, L_1, EventBus_Trigger_TisARTrackedImagesChangedEventArgs_tD7718E40EE25D22E9143DA4F556C44980E18BE2F_m543E103E3042925BB06EAC45E637282A71E1F853_RuntimeMethod_var);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARTrackedImageManagerListener_RegisterTrackablesChangedDelegate_m012C380AFF5DD189E5AD3C059C3783B6AE369DF2 (ARTrackedImageManagerListener_t41A4A505BF375F0C883703891EB4C03EE271F4E1* __this, ARTrackedImageManager_tCBA77F0B8A7172CC6508CA29176EAE1DE5DC4BD4* ___0_manager, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ARTrackedImageManagerListener_OnTrackablesChanged_mB7342F42759F03B7495C3863A66339D21A50B229_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_tE7B3D1070B99EFDFA92BD4DBEF95BF881DEB591F_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ARTrackedImageManager_tCBA77F0B8A7172CC6508CA29176EAE1DE5DC4BD4* L_0 = ___0_manager;
		Action_1_tE7B3D1070B99EFDFA92BD4DBEF95BF881DEB591F* L_1 = (Action_1_tE7B3D1070B99EFDFA92BD4DBEF95BF881DEB591F*)il2cpp_codegen_object_new(Action_1_tE7B3D1070B99EFDFA92BD4DBEF95BF881DEB591F_il2cpp_TypeInfo_var);
		Action_1__ctor_m55BF5EC44D8DEC74D367B749B5B29C8F2A9CB957(L_1, __this, (intptr_t)((void*)ARTrackedImageManagerListener_OnTrackablesChanged_mB7342F42759F03B7495C3863A66339D21A50B229_RuntimeMethod_var), NULL);
		NullCheck(L_0);
		ARTrackedImageManager_add_trackedImagesChanged_mB190015342B225144729100E0E38745CAFA7F438(L_0, L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARTrackedImageManagerListener_UnregisterTrackablesChangedDelegate_m47ABAAD0DD04A2519A59F2F0F98C01BDB4D8DD98 (ARTrackedImageManagerListener_t41A4A505BF375F0C883703891EB4C03EE271F4E1* __this, ARTrackedImageManager_tCBA77F0B8A7172CC6508CA29176EAE1DE5DC4BD4* ___0_manager, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ARTrackedImageManagerListener_OnTrackablesChanged_mB7342F42759F03B7495C3863A66339D21A50B229_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_tE7B3D1070B99EFDFA92BD4DBEF95BF881DEB591F_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ARTrackedImageManager_tCBA77F0B8A7172CC6508CA29176EAE1DE5DC4BD4* L_0 = ___0_manager;
		Action_1_tE7B3D1070B99EFDFA92BD4DBEF95BF881DEB591F* L_1 = (Action_1_tE7B3D1070B99EFDFA92BD4DBEF95BF881DEB591F*)il2cpp_codegen_object_new(Action_1_tE7B3D1070B99EFDFA92BD4DBEF95BF881DEB591F_il2cpp_TypeInfo_var);
		Action_1__ctor_m55BF5EC44D8DEC74D367B749B5B29C8F2A9CB957(L_1, __this, (intptr_t)((void*)ARTrackedImageManagerListener_OnTrackablesChanged_mB7342F42759F03B7495C3863A66339D21A50B229_RuntimeMethod_var), NULL);
		NullCheck(L_0);
		ARTrackedImageManager_remove_trackedImagesChanged_mA754FAB732F3D5D803FA3A58A51E6487E75CCF34(L_0, L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARTrackedImageManagerListener__ctor_m80CAF9FEAA38FDC8ADF8789E1DEE5F51E899D556 (ARTrackedImageManagerListener_t41A4A505BF375F0C883703891EB4C03EE271F4E1* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackableManagerListener_1__ctor_mBEE631CBF51F52534707E2938E971864799D3EDA_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		TrackableManagerListener_1__ctor_mBEE631CBF51F52534707E2938E971864799D3EDA(__this, TrackableManagerListener_1__ctor_mBEE631CBF51F52534707E2938E971864799D3EDA_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARTrackedObjectManagerListener_OnTrackablesChanged_m63FB84E742A86FFC4D163A0B17B8989491A51EE6 (ARTrackedObjectManagerListener_t67F1931020F75B64E0AEF23B15F033285A6D3A7A* __this, ARTrackedObjectsChangedEventArgs_t378A63B91AD22EA4BA9C68805C72DDA4B7C01509 ___0_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventBus_Trigger_TisARTrackedObjectsChangedEventArgs_t378A63B91AD22EA4BA9C68805C72DDA4B7C01509_m2D44CB18B7DE92A8A28C2336767883E782C70CD8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventBus_t6457FCB216134244ECE28921407E893BC1D38056_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralE44178F8CBCB7518AC61AD2FF65D32E8798D89A3);
		s_Il2CppMethodInitialized = true;
	}
	{
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_0;
		L_0 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(__this, NULL);
		ARTrackedObjectsChangedEventArgs_t378A63B91AD22EA4BA9C68805C72DDA4B7C01509 L_1 = ___0_args;
		il2cpp_codegen_runtime_class_init_inline(EventBus_t6457FCB216134244ECE28921407E893BC1D38056_il2cpp_TypeInfo_var);
		EventBus_Trigger_TisARTrackedObjectsChangedEventArgs_t378A63B91AD22EA4BA9C68805C72DDA4B7C01509_m2D44CB18B7DE92A8A28C2336767883E782C70CD8(_stringLiteralE44178F8CBCB7518AC61AD2FF65D32E8798D89A3, L_0, L_1, EventBus_Trigger_TisARTrackedObjectsChangedEventArgs_t378A63B91AD22EA4BA9C68805C72DDA4B7C01509_m2D44CB18B7DE92A8A28C2336767883E782C70CD8_RuntimeMethod_var);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARTrackedObjectManagerListener_RegisterTrackablesChangedDelegate_m23EA19D74EBB109B2B74AF9DB9D8597FEA16A0E7 (ARTrackedObjectManagerListener_t67F1931020F75B64E0AEF23B15F033285A6D3A7A* __this, ARTrackedObjectManager_tD667E7CCD95E9D25449BD578F256F728D8619FC7* ___0_manager, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ARTrackedObjectManagerListener_OnTrackablesChanged_m63FB84E742A86FFC4D163A0B17B8989491A51EE6_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t34AAB95B35DF66496184F8421A8AC5F317B7D8AB_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ARTrackedObjectManager_tD667E7CCD95E9D25449BD578F256F728D8619FC7* L_0 = ___0_manager;
		Action_1_t34AAB95B35DF66496184F8421A8AC5F317B7D8AB* L_1 = (Action_1_t34AAB95B35DF66496184F8421A8AC5F317B7D8AB*)il2cpp_codegen_object_new(Action_1_t34AAB95B35DF66496184F8421A8AC5F317B7D8AB_il2cpp_TypeInfo_var);
		Action_1__ctor_mBF52FA156452295382976743FE99EF09B9D21823(L_1, __this, (intptr_t)((void*)ARTrackedObjectManagerListener_OnTrackablesChanged_m63FB84E742A86FFC4D163A0B17B8989491A51EE6_RuntimeMethod_var), NULL);
		NullCheck(L_0);
		ARTrackedObjectManager_add_trackedObjectsChanged_m4E28ABA38B522FBE3B8150E1090237954A804FAE(L_0, L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARTrackedObjectManagerListener_UnregisterTrackablesChangedDelegate_m95CD86B38632CF9A9E1C50D027E3727F90ED7EF3 (ARTrackedObjectManagerListener_t67F1931020F75B64E0AEF23B15F033285A6D3A7A* __this, ARTrackedObjectManager_tD667E7CCD95E9D25449BD578F256F728D8619FC7* ___0_manager, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ARTrackedObjectManagerListener_OnTrackablesChanged_m63FB84E742A86FFC4D163A0B17B8989491A51EE6_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t34AAB95B35DF66496184F8421A8AC5F317B7D8AB_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ARTrackedObjectManager_tD667E7CCD95E9D25449BD578F256F728D8619FC7* L_0 = ___0_manager;
		Action_1_t34AAB95B35DF66496184F8421A8AC5F317B7D8AB* L_1 = (Action_1_t34AAB95B35DF66496184F8421A8AC5F317B7D8AB*)il2cpp_codegen_object_new(Action_1_t34AAB95B35DF66496184F8421A8AC5F317B7D8AB_il2cpp_TypeInfo_var);
		Action_1__ctor_mBF52FA156452295382976743FE99EF09B9D21823(L_1, __this, (intptr_t)((void*)ARTrackedObjectManagerListener_OnTrackablesChanged_m63FB84E742A86FFC4D163A0B17B8989491A51EE6_RuntimeMethod_var), NULL);
		NullCheck(L_0);
		ARTrackedObjectManager_remove_trackedObjectsChanged_m8412618ABDA3802160C112C7C884886D8057F41D(L_0, L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ARTrackedObjectManagerListener__ctor_mF863E16AC8DDCE13BD309673BD24A4B10EDD5AA7 (ARTrackedObjectManagerListener_t67F1931020F75B64E0AEF23B15F033285A6D3A7A* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TrackableManagerListener_1__ctor_m35FD529767C9EB77928CBC0BDC28A26273050D7B_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		TrackableManagerListener_1__ctor_m35FD529767C9EB77928CBC0BDC28A26273050D7B(__this, TrackableManagerListener_1__ctor_m35FD529767C9EB77928CBC0BDC28A26273050D7B_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GetAnchorsUnit__ctor_mE0D0675CC37CA1DA87C83FC0BA153B0E0462B4D2 (GetAnchorsUnit_tFD7107C908473E8F1558046830DD41C686447CEE* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GetTrackablesUnit_6__ctor_m9BC4C628CE4264EFA7586A6658299BF686ADE2CA_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GetTrackablesUnit_6_tE3FF81AB62ABB3500474D301EF59B13BF4A1685E_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(GetTrackablesUnit_6_tE3FF81AB62ABB3500474D301EF59B13BF4A1685E_il2cpp_TypeInfo_var);
		GetTrackablesUnit_6__ctor_m9BC4C628CE4264EFA7586A6658299BF686ADE2CA(__this, GetTrackablesUnit_6__ctor_m9BC4C628CE4264EFA7586A6658299BF686ADE2CA_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GetEnvironmentProbesUnit__ctor_m99D12CCEB247C2FA0DF000C258C9D001B1596C63 (GetEnvironmentProbesUnit_tD69DF71B014B1CA82754C08D6A7233473D6E6C8C* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GetTrackablesUnit_6__ctor_m993C351689850B54D4D415D6555E6963296402E6_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GetTrackablesUnit_6_tCE90D2E00FA14B5E936843082D488242C7463304_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(GetTrackablesUnit_6_tCE90D2E00FA14B5E936843082D488242C7463304_il2cpp_TypeInfo_var);
		GetTrackablesUnit_6__ctor_m993C351689850B54D4D415D6555E6963296402E6(__this, GetTrackablesUnit_6__ctor_m993C351689850B54D4D415D6555E6963296402E6_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GetFacesUnit__ctor_mAED50CDA1039DE45158B1F90E10AD3B9C2270121 (GetFacesUnit_t58BA2C20DC7900EE832A781F627CB24B4C73F541* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GetTrackablesUnit_6__ctor_mFE5AAECF852800E5E7743015509249E62860CB62_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GetTrackablesUnit_6_tB48912D7C44FC073BE0CCC0B19CCC4A33E066B77_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(GetTrackablesUnit_6_tB48912D7C44FC073BE0CCC0B19CCC4A33E066B77_il2cpp_TypeInfo_var);
		GetTrackablesUnit_6__ctor_mFE5AAECF852800E5E7743015509249E62860CB62(__this, GetTrackablesUnit_6__ctor_mFE5AAECF852800E5E7743015509249E62860CB62_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GetHumanBodiesUnit__ctor_m91BE6AF7CFBE7B218BB781F65F17E88224DA4CFF (GetHumanBodiesUnit_t4DB36755728EF8A8E329590C254060A3A1604B65* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GetTrackablesUnit_6__ctor_m80FFBEC9D57E6A6AC206595E20191765D636E72D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GetTrackablesUnit_6_t3DE4D256458C27210703957091C3684A86E12198_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(GetTrackablesUnit_6_t3DE4D256458C27210703957091C3684A86E12198_il2cpp_TypeInfo_var);
		GetTrackablesUnit_6__ctor_m80FFBEC9D57E6A6AC206595E20191765D636E72D(__this, GetTrackablesUnit_6__ctor_m80FFBEC9D57E6A6AC206595E20191765D636E72D_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GetParticipantsUnit__ctor_m0D8567DF8B8285D04B1A590EEF7E70346015A416 (GetParticipantsUnit_t573F6B1CED19A0DD19FA4BDF729BDACCCDBEF062* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GetTrackablesUnit_6__ctor_m95F5607653302ED365908C5FD4E47E8F6C046A35_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GetTrackablesUnit_6_t35B2538FE2BE005439F84D8E3DD2098F607A881B_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(GetTrackablesUnit_6_t35B2538FE2BE005439F84D8E3DD2098F607A881B_il2cpp_TypeInfo_var);
		GetTrackablesUnit_6__ctor_m95F5607653302ED365908C5FD4E47E8F6C046A35(__this, GetTrackablesUnit_6__ctor_m95F5607653302ED365908C5FD4E47E8F6C046A35_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GetPlanesUnit__ctor_mD0526AD4E7F72FEEACEB9502815B5FA3CC989966 (GetPlanesUnit_t0D6951EA13B7C5AB100439F6E16959FF7F229D80* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GetTrackablesUnit_6__ctor_m8F8058DE37A8268EABD8551E5CBB014EC6A1FC39_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GetTrackablesUnit_6_t016BB0C74695D0122C8B6B3599A71F89D32BCF77_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(GetTrackablesUnit_6_t016BB0C74695D0122C8B6B3599A71F89D32BCF77_il2cpp_TypeInfo_var);
		GetTrackablesUnit_6__ctor_m8F8058DE37A8268EABD8551E5CBB014EC6A1FC39(__this, GetTrackablesUnit_6__ctor_m8F8058DE37A8268EABD8551E5CBB014EC6A1FC39_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GetPointCloudsUnit__ctor_mCCE4B499918A3416C73072C13E9F7FCBC37B5261 (GetPointCloudsUnit_tE8EBA3C1C2C0250EBA3D742FC6D2EDEAE6100AB3* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GetTrackablesUnit_6__ctor_m4A8A971CAFBC181DC2C00076B9A07CA14F9C2830_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GetTrackablesUnit_6_tBEEA64178AC6F947BE154CE4D41420AED64DC0AE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(GetTrackablesUnit_6_tBEEA64178AC6F947BE154CE4D41420AED64DC0AE_il2cpp_TypeInfo_var);
		GetTrackablesUnit_6__ctor_m4A8A971CAFBC181DC2C00076B9A07CA14F9C2830(__this, GetTrackablesUnit_6__ctor_m4A8A971CAFBC181DC2C00076B9A07CA14F9C2830_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GetRaycastsUnit__ctor_mE784813F0C8D8392C75C397DED11DC88B52CF77E (GetRaycastsUnit_t9B59D89C15171736A9685CADE27468DF2787A886* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GetTrackablesUnit_6__ctor_m42135B0FC7FB4509F6F5630CB0F1A9B235D0C4AF_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GetTrackablesUnit_6_tA1B41E03CB6324ED319781B3AA151D349922B24A_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(GetTrackablesUnit_6_tA1B41E03CB6324ED319781B3AA151D349922B24A_il2cpp_TypeInfo_var);
		GetTrackablesUnit_6__ctor_m42135B0FC7FB4509F6F5630CB0F1A9B235D0C4AF(__this, GetTrackablesUnit_6__ctor_m42135B0FC7FB4509F6F5630CB0F1A9B235D0C4AF_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GetTrackedImagesUnit__ctor_m2007F922FF32CD8AB54CB65F3FE4A6B30934A808 (GetTrackedImagesUnit_tA4BCED5E1DC9EFA11B9BE049594D085A71B44239* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GetTrackablesUnit_6__ctor_mE4E69DCE572AA63C76CCEBB851DE56E31BD02020_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GetTrackablesUnit_6_t5FAD854E9B61FD3E7409D18C4DED57BC373BACE7_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(GetTrackablesUnit_6_t5FAD854E9B61FD3E7409D18C4DED57BC373BACE7_il2cpp_TypeInfo_var);
		GetTrackablesUnit_6__ctor_mE4E69DCE572AA63C76CCEBB851DE56E31BD02020(__this, GetTrackablesUnit_6__ctor_mE4E69DCE572AA63C76CCEBB851DE56E31BD02020_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GetTrackedObjectsUnit__ctor_m7178E4143A9C53B511F82A696C70672E0C88160A (GetTrackedObjectsUnit_t06C3877B12306C2ED474293DD059EEB537795E42* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GetTrackablesUnit_6__ctor_mC724CF523FCF0A0CA43758DC60F0E05E4B30EE32_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GetTrackablesUnit_6_t60E470A25938E95AA9CD62B873EA6C36FC34D2FF_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(GetTrackablesUnit_6_t60E470A25938E95AA9CD62B873EA6C36FC34D2FF_il2cpp_TypeInfo_var);
		GetTrackablesUnit_6__ctor_mC724CF523FCF0A0CA43758DC60F0E05E4B30EE32(__this, GetTrackablesUnit_6__ctor_mC724CF523FCF0A0CA43758DC60F0E05E4B30EE32_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ControlInput_t1AD434958538C865F55A62C66B9C24DA82AD31FE* SessionStateSwitchUnit_get_flowIn_m667548B27056B2F6F27B63DFCF4412BBAE6637A5 (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, const RuntimeMethod* method) 
{
	{
		ControlInput_t1AD434958538C865F55A62C66B9C24DA82AD31FE* L_0 = __this->___U3CflowInU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SessionStateSwitchUnit_set_flowIn_mD8F159479E3884269A7D2057D7F3E8C52B2923D5 (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, ControlInput_t1AD434958538C865F55A62C66B9C24DA82AD31FE* ___0_value, const RuntimeMethod* method) 
{
	{
		ControlInput_t1AD434958538C865F55A62C66B9C24DA82AD31FE* L_0 = ___0_value;
		__this->___U3CflowInU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CflowInU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* SessionStateSwitchUnit_get_sessionStateIn_m4E966BABF18BE40C7178158088CC8DEA31893939 (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, const RuntimeMethod* method) 
{
	{
		ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* L_0 = __this->___U3CsessionStateInU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SessionStateSwitchUnit_set_sessionStateIn_m39EB7D50DFEE0306E0C4F690E06D8B6D246B1AFB (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* ___0_value, const RuntimeMethod* method) 
{
	{
		ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* L_0 = ___0_value;
		__this->___U3CsessionStateInU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CsessionStateInU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* SessionStateSwitchUnit_get_none_mCDA812C5D868B12D04C020ED27ED0F886EE0963E (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, const RuntimeMethod* method) 
{
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_0 = __this->___U3CnoneU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SessionStateSwitchUnit_set_none_mE058B6C1A72B0BD0DC6BAEA629EC6C8A55BD273C (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___0_value, const RuntimeMethod* method) 
{
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_0 = ___0_value;
		__this->___U3CnoneU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CnoneU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* SessionStateSwitchUnit_get_unsupported_m911986D3ADF69617D9E6DFFB367926BBA3639439 (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, const RuntimeMethod* method) 
{
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_0 = __this->___U3CunsupportedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SessionStateSwitchUnit_set_unsupported_m683621DAFF0725F51FAB5D0FB9CB63F5731E53A1 (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___0_value, const RuntimeMethod* method) 
{
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_0 = ___0_value;
		__this->___U3CunsupportedU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CunsupportedU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* SessionStateSwitchUnit_get_checkingAvailability_m3EF1098F57D30B08B8C7587F9D94B64A4E650ED9 (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, const RuntimeMethod* method) 
{
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_0 = __this->___U3CcheckingAvailabilityU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SessionStateSwitchUnit_set_checkingAvailability_m0D10A7BFD1348E9694B381482E90F5974E3CF246 (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___0_value, const RuntimeMethod* method) 
{
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_0 = ___0_value;
		__this->___U3CcheckingAvailabilityU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CcheckingAvailabilityU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* SessionStateSwitchUnit_get_needsInstall_mDBE283471C7F3B8C8A1D6962DAFECBC81CC7071E (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, const RuntimeMethod* method) 
{
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_0 = __this->___U3CneedsInstallU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SessionStateSwitchUnit_set_needsInstall_m6C03848DA29361417CBE17AAC6EF641AE4946A95 (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___0_value, const RuntimeMethod* method) 
{
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_0 = ___0_value;
		__this->___U3CneedsInstallU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CneedsInstallU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* SessionStateSwitchUnit_get_installing_mAB1C0C05D8CAD09DE7EA9177806621D1263E120F (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, const RuntimeMethod* method) 
{
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_0 = __this->___U3CinstallingU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SessionStateSwitchUnit_set_installing_m91BC0E0D467AB4CDE4A9E87F43ABA98874D7BAF4 (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___0_value, const RuntimeMethod* method) 
{
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_0 = ___0_value;
		__this->___U3CinstallingU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CinstallingU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* SessionStateSwitchUnit_get_ready_m5FDBB155FE257620F68167B4A7A1DEEB597491D2 (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, const RuntimeMethod* method) 
{
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_0 = __this->___U3CreadyU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SessionStateSwitchUnit_set_ready_m4CF3C56F9F24CDCFE9678F22BBAA988BF7AA6FD3 (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___0_value, const RuntimeMethod* method) 
{
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_0 = ___0_value;
		__this->___U3CreadyU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CreadyU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* SessionStateSwitchUnit_get_sessionInitializing_mDAB4A059DA69D91C4A7E3ED9B207A31F64040746 (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, const RuntimeMethod* method) 
{
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_0 = __this->___U3CsessionInitializingU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SessionStateSwitchUnit_set_sessionInitializing_mB3A2FF70C33664AFF792B183A2E39252DDFDACB2 (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___0_value, const RuntimeMethod* method) 
{
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_0 = ___0_value;
		__this->___U3CsessionInitializingU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CsessionInitializingU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* SessionStateSwitchUnit_get_sessionTracking_m46B500F7B3F7276E269EC893E52EE71478554365 (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, const RuntimeMethod* method) 
{
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_0 = __this->___U3CsessionTrackingU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SessionStateSwitchUnit_set_sessionTracking_mA270750450E8D31E271811C785AE730FB61AACF0 (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___0_value, const RuntimeMethod* method) 
{
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_0 = ___0_value;
		__this->___U3CsessionTrackingU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CsessionTrackingU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SessionStateSwitchUnit_Definition_m51005D8BC9A2A714696B3D883AB3391DEFB9477D (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Func_2_tE299C631F0CB2E8E61444D749D446C27349C90B8_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SessionStateSwitchUnit_ProcessFlow_m57E488DEC7E1E2F26E074FB49BB7F19DA905C410_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Unit_ValueInput_TisARSessionState_tAAADC649886D67F7134A0D9A665953D764A115D7_mBD32544DF033F6AA05190E690F3F77D3420E6CF4_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral0AEA2C46AAEB233C6D2DF0275EF92FB2E56BAFE9);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral1B610611D3BE45C25E5133155F9D83AF5F3832ED);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral2CFD1985F0B0EF58F786512B364283578445FAC5);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral37D6BA2FAF7F2FCCC8DD58518A3048E66432C10B);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral43452BBEA0638F454FF0D37B4FD3C2DEBA3C06FF);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral699D09C7B53816C9B0927B8BD5FA1AE027F1C3C1);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral7B82EC15E841121B1695710D448FAF4971871672);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral8A8FE283442D993E502B69A3252F69EFFAE40786);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralA0AC1B410D80766301FC82938739173E5BCB79A1);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralA783A5CEF05EBCA80DF7AA6E558B8F8514ED24EC);
		s_Il2CppMethodInitialized = true;
	}
	{
		Func_2_tE299C631F0CB2E8E61444D749D446C27349C90B8* L_0 = (Func_2_tE299C631F0CB2E8E61444D749D446C27349C90B8*)il2cpp_codegen_object_new(Func_2_tE299C631F0CB2E8E61444D749D446C27349C90B8_il2cpp_TypeInfo_var);
		Func_2__ctor_m36A365D852CA6BD70A68A3A26FF683FB3369DDF4(L_0, __this, (intptr_t)((void*)SessionStateSwitchUnit_ProcessFlow_m57E488DEC7E1E2F26E074FB49BB7F19DA905C410_RuntimeMethod_var), NULL);
		ControlInput_t1AD434958538C865F55A62C66B9C24DA82AD31FE* L_1;
		L_1 = Unit_ControlInput_mF730DABA4FE60423D39B8EADE5DA72F51E6166A5(__this, _stringLiteral8A8FE283442D993E502B69A3252F69EFFAE40786, L_0, NULL);
		SessionStateSwitchUnit_set_flowIn_mD8F159479E3884269A7D2057D7F3E8C52B2923D5_inline(__this, L_1, NULL);
		ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* L_2;
		L_2 = Unit_ValueInput_TisARSessionState_tAAADC649886D67F7134A0D9A665953D764A115D7_mBD32544DF033F6AA05190E690F3F77D3420E6CF4(__this, _stringLiteral37D6BA2FAF7F2FCCC8DD58518A3048E66432C10B, Unit_ValueInput_TisARSessionState_tAAADC649886D67F7134A0D9A665953D764A115D7_mBD32544DF033F6AA05190E690F3F77D3420E6CF4_RuntimeMethod_var);
		SessionStateSwitchUnit_set_sessionStateIn_m39EB7D50DFEE0306E0C4F690E06D8B6D246B1AFB_inline(__this, L_2, NULL);
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_3;
		L_3 = Unit_ControlOutput_mCD76724DFB6AAE10C099E9984A39A5E0FC2824FE(__this, _stringLiteral0AEA2C46AAEB233C6D2DF0275EF92FB2E56BAFE9, NULL);
		SessionStateSwitchUnit_set_none_mE058B6C1A72B0BD0DC6BAEA629EC6C8A55BD273C_inline(__this, L_3, NULL);
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_4;
		L_4 = Unit_ControlOutput_mCD76724DFB6AAE10C099E9984A39A5E0FC2824FE(__this, _stringLiteral43452BBEA0638F454FF0D37B4FD3C2DEBA3C06FF, NULL);
		SessionStateSwitchUnit_set_unsupported_m683621DAFF0725F51FAB5D0FB9CB63F5731E53A1_inline(__this, L_4, NULL);
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_5;
		L_5 = Unit_ControlOutput_mCD76724DFB6AAE10C099E9984A39A5E0FC2824FE(__this, _stringLiteralA783A5CEF05EBCA80DF7AA6E558B8F8514ED24EC, NULL);
		SessionStateSwitchUnit_set_checkingAvailability_m0D10A7BFD1348E9694B381482E90F5974E3CF246_inline(__this, L_5, NULL);
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_6;
		L_6 = Unit_ControlOutput_mCD76724DFB6AAE10C099E9984A39A5E0FC2824FE(__this, _stringLiteral7B82EC15E841121B1695710D448FAF4971871672, NULL);
		SessionStateSwitchUnit_set_needsInstall_m6C03848DA29361417CBE17AAC6EF641AE4946A95_inline(__this, L_6, NULL);
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_7;
		L_7 = Unit_ControlOutput_mCD76724DFB6AAE10C099E9984A39A5E0FC2824FE(__this, _stringLiteralA0AC1B410D80766301FC82938739173E5BCB79A1, NULL);
		SessionStateSwitchUnit_set_installing_m91BC0E0D467AB4CDE4A9E87F43ABA98874D7BAF4_inline(__this, L_7, NULL);
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_8;
		L_8 = Unit_ControlOutput_mCD76724DFB6AAE10C099E9984A39A5E0FC2824FE(__this, _stringLiteral1B610611D3BE45C25E5133155F9D83AF5F3832ED, NULL);
		SessionStateSwitchUnit_set_ready_m4CF3C56F9F24CDCFE9678F22BBAA988BF7AA6FD3_inline(__this, L_8, NULL);
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_9;
		L_9 = Unit_ControlOutput_mCD76724DFB6AAE10C099E9984A39A5E0FC2824FE(__this, _stringLiteral2CFD1985F0B0EF58F786512B364283578445FAC5, NULL);
		SessionStateSwitchUnit_set_sessionInitializing_mB3A2FF70C33664AFF792B183A2E39252DDFDACB2_inline(__this, L_9, NULL);
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_10;
		L_10 = Unit_ControlOutput_mCD76724DFB6AAE10C099E9984A39A5E0FC2824FE(__this, _stringLiteral699D09C7B53816C9B0927B8BD5FA1AE027F1C3C1, NULL);
		SessionStateSwitchUnit_set_sessionTracking_mA270750450E8D31E271811C785AE730FB61AACF0_inline(__this, L_10, NULL);
		ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* L_11;
		L_11 = SessionStateSwitchUnit_get_sessionStateIn_m4E966BABF18BE40C7178158088CC8DEA31893939_inline(__this, NULL);
		ControlInput_t1AD434958538C865F55A62C66B9C24DA82AD31FE* L_12;
		L_12 = SessionStateSwitchUnit_get_flowIn_m667548B27056B2F6F27B63DFCF4412BBAE6637A5_inline(__this, NULL);
		Unit_Requirement_m9F929AD842C0E254FA9AD48E9200C63B43BFE64A(__this, L_11, L_12, NULL);
		ControlInput_t1AD434958538C865F55A62C66B9C24DA82AD31FE* L_13;
		L_13 = SessionStateSwitchUnit_get_flowIn_m667548B27056B2F6F27B63DFCF4412BBAE6637A5_inline(__this, NULL);
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_14;
		L_14 = SessionStateSwitchUnit_get_none_mCDA812C5D868B12D04C020ED27ED0F886EE0963E_inline(__this, NULL);
		Unit_Succession_m202BB700D861D65B85102AD7B3119AD1B2FF2158(__this, L_13, L_14, NULL);
		ControlInput_t1AD434958538C865F55A62C66B9C24DA82AD31FE* L_15;
		L_15 = SessionStateSwitchUnit_get_flowIn_m667548B27056B2F6F27B63DFCF4412BBAE6637A5_inline(__this, NULL);
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_16;
		L_16 = SessionStateSwitchUnit_get_unsupported_m911986D3ADF69617D9E6DFFB367926BBA3639439_inline(__this, NULL);
		Unit_Succession_m202BB700D861D65B85102AD7B3119AD1B2FF2158(__this, L_15, L_16, NULL);
		ControlInput_t1AD434958538C865F55A62C66B9C24DA82AD31FE* L_17;
		L_17 = SessionStateSwitchUnit_get_flowIn_m667548B27056B2F6F27B63DFCF4412BBAE6637A5_inline(__this, NULL);
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_18;
		L_18 = SessionStateSwitchUnit_get_checkingAvailability_m3EF1098F57D30B08B8C7587F9D94B64A4E650ED9_inline(__this, NULL);
		Unit_Succession_m202BB700D861D65B85102AD7B3119AD1B2FF2158(__this, L_17, L_18, NULL);
		ControlInput_t1AD434958538C865F55A62C66B9C24DA82AD31FE* L_19;
		L_19 = SessionStateSwitchUnit_get_flowIn_m667548B27056B2F6F27B63DFCF4412BBAE6637A5_inline(__this, NULL);
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_20;
		L_20 = SessionStateSwitchUnit_get_needsInstall_mDBE283471C7F3B8C8A1D6962DAFECBC81CC7071E_inline(__this, NULL);
		Unit_Succession_m202BB700D861D65B85102AD7B3119AD1B2FF2158(__this, L_19, L_20, NULL);
		ControlInput_t1AD434958538C865F55A62C66B9C24DA82AD31FE* L_21;
		L_21 = SessionStateSwitchUnit_get_flowIn_m667548B27056B2F6F27B63DFCF4412BBAE6637A5_inline(__this, NULL);
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_22;
		L_22 = SessionStateSwitchUnit_get_installing_mAB1C0C05D8CAD09DE7EA9177806621D1263E120F_inline(__this, NULL);
		Unit_Succession_m202BB700D861D65B85102AD7B3119AD1B2FF2158(__this, L_21, L_22, NULL);
		ControlInput_t1AD434958538C865F55A62C66B9C24DA82AD31FE* L_23;
		L_23 = SessionStateSwitchUnit_get_flowIn_m667548B27056B2F6F27B63DFCF4412BBAE6637A5_inline(__this, NULL);
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_24;
		L_24 = SessionStateSwitchUnit_get_ready_m5FDBB155FE257620F68167B4A7A1DEEB597491D2_inline(__this, NULL);
		Unit_Succession_m202BB700D861D65B85102AD7B3119AD1B2FF2158(__this, L_23, L_24, NULL);
		ControlInput_t1AD434958538C865F55A62C66B9C24DA82AD31FE* L_25;
		L_25 = SessionStateSwitchUnit_get_flowIn_m667548B27056B2F6F27B63DFCF4412BBAE6637A5_inline(__this, NULL);
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_26;
		L_26 = SessionStateSwitchUnit_get_sessionInitializing_mDAB4A059DA69D91C4A7E3ED9B207A31F64040746_inline(__this, NULL);
		Unit_Succession_m202BB700D861D65B85102AD7B3119AD1B2FF2158(__this, L_25, L_26, NULL);
		ControlInput_t1AD434958538C865F55A62C66B9C24DA82AD31FE* L_27;
		L_27 = SessionStateSwitchUnit_get_flowIn_m667548B27056B2F6F27B63DFCF4412BBAE6637A5_inline(__this, NULL);
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_28;
		L_28 = SessionStateSwitchUnit_get_sessionTracking_m46B500F7B3F7276E269EC893E52EE71478554365_inline(__this, NULL);
		Unit_Succession_m202BB700D861D65B85102AD7B3119AD1B2FF2158(__this, L_27, L_28, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* SessionStateSwitchUnit_ProcessFlow_m57E488DEC7E1E2F26E074FB49BB7F19DA905C410 (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* ___0_flow, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ARSessionState_tAAADC649886D67F7134A0D9A665953D764A115D7_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	RuntimeObject* V_1 = NULL;
	ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* V_2 = NULL;
	{
		Flow_tC370BDFF173D53E274F883ED6ADAFE29B049C582* L_0 = ___0_flow;
		ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* L_1;
		L_1 = SessionStateSwitchUnit_get_sessionStateIn_m4E966BABF18BE40C7178158088CC8DEA31893939_inline(__this, NULL);
		NullCheck(L_0);
		RuntimeObject* L_2;
		L_2 = Flow_GetValue_m4396BFB8BF013A1CF992A4E36D47E864D4180718(L_0, L_1, NULL);
		V_1 = L_2;
		RuntimeObject* L_3 = V_1;
		if (!((RuntimeObject*)IsInstSealed((RuntimeObject*)L_3, ARSessionState_tAAADC649886D67F7134A0D9A665953D764A115D7_il2cpp_TypeInfo_var)))
		{
			goto IL_001e;
		}
	}
	{
		RuntimeObject* L_4 = V_1;
		V_0 = ((*(int32_t*)((int32_t*)(int32_t*)UnBox(L_4, ARSessionState_tAAADC649886D67F7134A0D9A665953D764A115D7_il2cpp_TypeInfo_var))));
		goto IL_0029;
	}

IL_001e:
	{
		ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263* L_5 = (ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263_il2cpp_TypeInfo_var)));
		ArgumentException__ctor_m026938A67AF9D36BB7ED27F80425D7194B514465(L_5, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral6E449B10A312A589CEC8243A12BBA89445ED50DE)), NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_5, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&SessionStateSwitchUnit_ProcessFlow_m57E488DEC7E1E2F26E074FB49BB7F19DA905C410_RuntimeMethod_var)));
	}

IL_0029:
	{
		int32_t L_6 = V_0;
		switch (L_6)
		{
			case 0:
			{
				goto IL_0051;
			}
			case 1:
			{
				goto IL_005a;
			}
			case 2:
			{
				goto IL_0063;
			}
			case 3:
			{
				goto IL_006c;
			}
			case 4:
			{
				goto IL_0075;
			}
			case 5:
			{
				goto IL_007e;
			}
			case 6:
			{
				goto IL_0087;
			}
			case 7:
			{
				goto IL_0090;
			}
		}
	}
	{
		goto IL_0099;
	}

IL_0051:
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_7;
		L_7 = SessionStateSwitchUnit_get_none_mCDA812C5D868B12D04C020ED27ED0F886EE0963E_inline(__this, NULL);
		V_2 = L_7;
		goto IL_009f;
	}

IL_005a:
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_8;
		L_8 = SessionStateSwitchUnit_get_unsupported_m911986D3ADF69617D9E6DFFB367926BBA3639439_inline(__this, NULL);
		V_2 = L_8;
		goto IL_009f;
	}

IL_0063:
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_9;
		L_9 = SessionStateSwitchUnit_get_checkingAvailability_m3EF1098F57D30B08B8C7587F9D94B64A4E650ED9_inline(__this, NULL);
		V_2 = L_9;
		goto IL_009f;
	}

IL_006c:
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_10;
		L_10 = SessionStateSwitchUnit_get_needsInstall_mDBE283471C7F3B8C8A1D6962DAFECBC81CC7071E_inline(__this, NULL);
		V_2 = L_10;
		goto IL_009f;
	}

IL_0075:
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_11;
		L_11 = SessionStateSwitchUnit_get_installing_mAB1C0C05D8CAD09DE7EA9177806621D1263E120F_inline(__this, NULL);
		V_2 = L_11;
		goto IL_009f;
	}

IL_007e:
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_12;
		L_12 = SessionStateSwitchUnit_get_ready_m5FDBB155FE257620F68167B4A7A1DEEB597491D2_inline(__this, NULL);
		V_2 = L_12;
		goto IL_009f;
	}

IL_0087:
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_13;
		L_13 = SessionStateSwitchUnit_get_sessionInitializing_mDAB4A059DA69D91C4A7E3ED9B207A31F64040746_inline(__this, NULL);
		V_2 = L_13;
		goto IL_009f;
	}

IL_0090:
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_14;
		L_14 = SessionStateSwitchUnit_get_sessionTracking_m46B500F7B3F7276E269EC893E52EE71478554365_inline(__this, NULL);
		V_2 = L_14;
		goto IL_009f;
	}

IL_0099:
	{
		ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F* L_15 = (ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F_il2cpp_TypeInfo_var)));
		ArgumentOutOfRangeException__ctor_mB596C51BFA864B65C2CED275458FAE90F7CD29C9(L_15, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_15, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&SessionStateSwitchUnit_ProcessFlow_m57E488DEC7E1E2F26E074FB49BB7F19DA905C410_RuntimeMethod_var)));
	}

IL_009f:
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_16 = V_2;
		return L_16;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SessionStateSwitchUnit__ctor_m93E299F5D4B3285000E037D77A034C454C564F68 (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, const RuntimeMethod* method) 
{
	{
		Unit__ctor_m7C07A3A00D7777361F4B86B45F40073D1A65D8B2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_t5C2EBF017989E44F3D1D11AF502E10F6F8D9AB53* ARAnchorsChangedEventArgs_get_added_m5F9CEB4587F9496D0DA28880430B69EFDB550600_inline (ARAnchorsChangedEventArgs_t755E6208C84304CAC448C133B0A8660CD5750A75* __this, const RuntimeMethod* method) 
{
	{
		List_1_t5C2EBF017989E44F3D1D11AF502E10F6F8D9AB53* L_0 = __this->___U3CaddedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_t5C2EBF017989E44F3D1D11AF502E10F6F8D9AB53* ARAnchorsChangedEventArgs_get_updated_m21039F14E472A75F969EE0BA5CBA9007DC12B593_inline (ARAnchorsChangedEventArgs_t755E6208C84304CAC448C133B0A8660CD5750A75* __this, const RuntimeMethod* method) 
{
	{
		List_1_t5C2EBF017989E44F3D1D11AF502E10F6F8D9AB53* L_0 = __this->___U3CupdatedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_t5C2EBF017989E44F3D1D11AF502E10F6F8D9AB53* ARAnchorsChangedEventArgs_get_removed_mF1E9D61E21153018C87DE99722FF8820C351CFD8_inline (ARAnchorsChangedEventArgs_t755E6208C84304CAC448C133B0A8660CD5750A75* __this, const RuntimeMethod* method) 
{
	{
		List_1_t5C2EBF017989E44F3D1D11AF502E10F6F8D9AB53* L_0 = __this->___U3CremovedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void CameraFrameReceivedEventUnit_set_frameEventArgs_m9D477159407F84279F1EF8E7619B871AA4F542FE_inline (CameraFrameReceivedEventUnit_t5C258A06DEEF9DBF3CE6AD4FD0B51313F1DEF858* __this, ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___0_value, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = ___0_value;
		__this->___U3CframeEventArgsU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CframeEventArgsU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* CameraFrameReceivedEventUnit_get_frameEventArgs_m2CA34906D7D15DDD541EC38B7862B6CB0AB500E8_inline (CameraFrameReceivedEventUnit_t5C258A06DEEF9DBF3CE6AD4FD0B51313F1DEF858* __this, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = __this->___U3CframeEventArgsU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_t6F14EE6179A9A8E042C5D65E902F72E24597029B* AREnvironmentProbesChangedEvent_get_added_m5EB8CAE059E5A0F0A84A11866F2343F630B7C997_inline (AREnvironmentProbesChangedEvent_t8757E98E11F3D37F0CFC4589FCA5CD2107F8375C* __this, const RuntimeMethod* method) 
{
	{
		List_1_t6F14EE6179A9A8E042C5D65E902F72E24597029B* L_0 = __this->___U3CaddedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_t6F14EE6179A9A8E042C5D65E902F72E24597029B* AREnvironmentProbesChangedEvent_get_updated_mFFE292486A4D2DB4385C70FAE94EB6048CBB99F3_inline (AREnvironmentProbesChangedEvent_t8757E98E11F3D37F0CFC4589FCA5CD2107F8375C* __this, const RuntimeMethod* method) 
{
	{
		List_1_t6F14EE6179A9A8E042C5D65E902F72E24597029B* L_0 = __this->___U3CupdatedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_t6F14EE6179A9A8E042C5D65E902F72E24597029B* AREnvironmentProbesChangedEvent_get_removed_m0D5B42BCAD8408B1FC0DF82E24C4EAF1D8ADF40A_inline (AREnvironmentProbesChangedEvent_t8757E98E11F3D37F0CFC4589FCA5CD2107F8375C* __this, const RuntimeMethod* method) 
{
	{
		List_1_t6F14EE6179A9A8E042C5D65E902F72E24597029B* L_0 = __this->___U3CremovedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_tA11EBED64210DD13BB4303415D948E6CD964AAF9* ARFacesChangedEventArgs_get_added_m8EA1424F8F262955AFD00664B81C15AB54EFA213_inline (ARFacesChangedEventArgs_tC05D4423CB2096FF5955C3A0A00056EE2B6301AE* __this, const RuntimeMethod* method) 
{
	{
		List_1_tA11EBED64210DD13BB4303415D948E6CD964AAF9* L_0 = __this->___U3CaddedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_tA11EBED64210DD13BB4303415D948E6CD964AAF9* ARFacesChangedEventArgs_get_updated_m1D3587AA05E5789F796A71C3C87CD6D11BE0005B_inline (ARFacesChangedEventArgs_tC05D4423CB2096FF5955C3A0A00056EE2B6301AE* __this, const RuntimeMethod* method) 
{
	{
		List_1_tA11EBED64210DD13BB4303415D948E6CD964AAF9* L_0 = __this->___U3CupdatedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_tA11EBED64210DD13BB4303415D948E6CD964AAF9* ARFacesChangedEventArgs_get_removed_mC31806DE526D3BECAA26C35DAE321F2DE77C335E_inline (ARFacesChangedEventArgs_tC05D4423CB2096FF5955C3A0A00056EE2B6301AE* __this, const RuntimeMethod* method) 
{
	{
		List_1_tA11EBED64210DD13BB4303415D948E6CD964AAF9* L_0 = __this->___U3CremovedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void FaceUpdatedEventUnit_set_faceOut_mA1675257B969683B3E210624EFADE5F0F24A4811_inline (FaceUpdatedEventUnit_tBAF617E9D7CA035FC4D40FDA435F6BC35A39D426* __this, ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___0_value, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = ___0_value;
		__this->___U3CfaceOutU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CfaceOutU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* FaceUpdatedEventUnit_get_faceOut_m4C064F7BE0FDC5D28BAB8C72683F60B5399B091D_inline (FaceUpdatedEventUnit_tBAF617E9D7CA035FC4D40FDA435F6BC35A39D426* __this, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = __this->___U3CfaceOutU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ARFace_t73C068C9C8422F79A858EC41C322D399305B8453* ARFaceUpdatedEventArgs_get_face_mFD21EF59457212494C80FE998D1CA8AC0F057170_inline (ARFaceUpdatedEventArgs_tC20D17255858678A1A8230EA32D7C38287221FF6* __this, const RuntimeMethod* method) 
{
	{
		ARFace_t73C068C9C8422F79A858EC41C322D399305B8453* L_0 = __this->___U3CfaceU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_t64B1B4ABB789350AE017F3EAF4D3647AF59244C7* ARHumanBodiesChangedEventArgs_get_added_m6FD862CC8C8281F9377D754BBAB7F3C616955D4C_inline (ARHumanBodiesChangedEventArgs_t19088E0D3384A22C5D8A542957705222C834720B* __this, const RuntimeMethod* method) 
{
	{
		List_1_t64B1B4ABB789350AE017F3EAF4D3647AF59244C7* L_0 = __this->___U3CaddedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_t64B1B4ABB789350AE017F3EAF4D3647AF59244C7* ARHumanBodiesChangedEventArgs_get_updated_mA2FD128E22BDACD09294F107AA3F2A91CFFAA550_inline (ARHumanBodiesChangedEventArgs_t19088E0D3384A22C5D8A542957705222C834720B* __this, const RuntimeMethod* method) 
{
	{
		List_1_t64B1B4ABB789350AE017F3EAF4D3647AF59244C7* L_0 = __this->___U3CupdatedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_t64B1B4ABB789350AE017F3EAF4D3647AF59244C7* ARHumanBodiesChangedEventArgs_get_removed_m613516A989AA48A625C79A3C309D9C1465762BCA_inline (ARHumanBodiesChangedEventArgs_t19088E0D3384A22C5D8A542957705222C834720B* __this, const RuntimeMethod* method) 
{
	{
		List_1_t64B1B4ABB789350AE017F3EAF4D3647AF59244C7* L_0 = __this->___U3CremovedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_t87D9AC2D6B4A4860E01575669DEB0D164D4B33B3* ARParticipantsChangedEventArgs_get_added_m06DA0DBF00575BB5D41A8AFE385ADAB9866BA678_inline (ARParticipantsChangedEventArgs_t2EAD9041A074A7ACEFF280662784747A4E85E527* __this, const RuntimeMethod* method) 
{
	{
		List_1_t87D9AC2D6B4A4860E01575669DEB0D164D4B33B3* L_0 = __this->___U3CaddedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_t87D9AC2D6B4A4860E01575669DEB0D164D4B33B3* ARParticipantsChangedEventArgs_get_updated_mA0EC697655BDFEEB28064579FDD67DD166A2AE65_inline (ARParticipantsChangedEventArgs_t2EAD9041A074A7ACEFF280662784747A4E85E527* __this, const RuntimeMethod* method) 
{
	{
		List_1_t87D9AC2D6B4A4860E01575669DEB0D164D4B33B3* L_0 = __this->___U3CupdatedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_t87D9AC2D6B4A4860E01575669DEB0D164D4B33B3* ARParticipantsChangedEventArgs_get_removed_m6BBF2ABF2E71DB02A70122927AAD83E3AB35A77F_inline (ARParticipantsChangedEventArgs_t2EAD9041A074A7ACEFF280662784747A4E85E527* __this, const RuntimeMethod* method) 
{
	{
		List_1_t87D9AC2D6B4A4860E01575669DEB0D164D4B33B3* L_0 = __this->___U3CremovedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_t10BA05B555C92BD54800DFF82DCAAFC9DE44A077* ARPlanesChangedEventArgs_get_added_m6E00DD5F0B3261BCBAA8A029924A1F3F4179C747_inline (ARPlanesChangedEventArgs_t8D63E0257BF9942EF8F8C0445F2FD46421017872* __this, const RuntimeMethod* method) 
{
	{
		List_1_t10BA05B555C92BD54800DFF82DCAAFC9DE44A077* L_0 = __this->___U3CaddedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_t10BA05B555C92BD54800DFF82DCAAFC9DE44A077* ARPlanesChangedEventArgs_get_updated_mE979DAD93445C56FF7BEB732528D6CADAF0B6C22_inline (ARPlanesChangedEventArgs_t8D63E0257BF9942EF8F8C0445F2FD46421017872* __this, const RuntimeMethod* method) 
{
	{
		List_1_t10BA05B555C92BD54800DFF82DCAAFC9DE44A077* L_0 = __this->___U3CupdatedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_t10BA05B555C92BD54800DFF82DCAAFC9DE44A077* ARPlanesChangedEventArgs_get_removed_m8CF2AA0278488746F8C55CC6C7DC1870531D63C8_inline (ARPlanesChangedEventArgs_t8D63E0257BF9942EF8F8C0445F2FD46421017872* __this, const RuntimeMethod* method) 
{
	{
		List_1_t10BA05B555C92BD54800DFF82DCAAFC9DE44A077* L_0 = __this->___U3CremovedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_tB9CD639EB4FE5EA0A8B417168BB9BD2D29AEE271* ARPointCloudChangedEventArgs_get_added_m74DFD8ED4B3EADB5AC29DD0E748C84C6FA26DDE9_inline (ARPointCloudChangedEventArgs_t7DE4CA4D30D369496221061F89CC4BF84E42CC1B* __this, const RuntimeMethod* method) 
{
	{
		List_1_tB9CD639EB4FE5EA0A8B417168BB9BD2D29AEE271* L_0 = __this->___U3CaddedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_tB9CD639EB4FE5EA0A8B417168BB9BD2D29AEE271* ARPointCloudChangedEventArgs_get_updated_mD69E67FE8B06AB44B15F42F1D6EFA91F7E58F7C6_inline (ARPointCloudChangedEventArgs_t7DE4CA4D30D369496221061F89CC4BF84E42CC1B* __this, const RuntimeMethod* method) 
{
	{
		List_1_tB9CD639EB4FE5EA0A8B417168BB9BD2D29AEE271* L_0 = __this->___U3CupdatedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_tB9CD639EB4FE5EA0A8B417168BB9BD2D29AEE271* ARPointCloudChangedEventArgs_get_removed_m64321D4F0D902494EE19EFF1B258981DE5C712B0_inline (ARPointCloudChangedEventArgs_t7DE4CA4D30D369496221061F89CC4BF84E42CC1B* __this, const RuntimeMethod* method) 
{
	{
		List_1_tB9CD639EB4FE5EA0A8B417168BB9BD2D29AEE271* L_0 = __this->___U3CremovedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t ARSessionStateChangedEventArgs_get_state_mC0A6FB4AF08C068BFD5D9730F60215BE662A950B_inline (ARSessionStateChangedEventArgs_tC31BD123D6B4F6168FB246762E79AB33962AE822* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___U3CstateU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void SessionStateChangedEventUnit_set_sessionStateOut_mA37AE57139361EFD0003F0FF225B9A917359F04C_inline (SessionStateChangedEventUnit_tB622F7C1B352062C2D5D67A3EA1E1E6DBD910631* __this, ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* ___0_value, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = ___0_value;
		__this->___U3CsessionStateOutU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CsessionStateOutU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* SessionStateChangedEventUnit_get_sessionStateOut_m0A6C8851E8BE80C79AEFAE297A6B8A47F68445B9_inline (SessionStateChangedEventUnit_tB622F7C1B352062C2D5D67A3EA1E1E6DBD910631* __this, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = __this->___U3CsessionStateOutU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_tFA730335FF46512E4245B2F46892CA318B394FAE* ARTrackedImagesChangedEventArgs_get_added_m2929CC85141D13AF05C1484AFB47E043C6D3EE35_inline (ARTrackedImagesChangedEventArgs_tD7718E40EE25D22E9143DA4F556C44980E18BE2F* __this, const RuntimeMethod* method) 
{
	{
		List_1_tFA730335FF46512E4245B2F46892CA318B394FAE* L_0 = __this->___U3CaddedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_tFA730335FF46512E4245B2F46892CA318B394FAE* ARTrackedImagesChangedEventArgs_get_updated_m0C896E1C21EF35FF4B31B36563838EC2BA3CDFD1_inline (ARTrackedImagesChangedEventArgs_tD7718E40EE25D22E9143DA4F556C44980E18BE2F* __this, const RuntimeMethod* method) 
{
	{
		List_1_tFA730335FF46512E4245B2F46892CA318B394FAE* L_0 = __this->___U3CupdatedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_tFA730335FF46512E4245B2F46892CA318B394FAE* ARTrackedImagesChangedEventArgs_get_removed_m062CBBEF163BEE47A673F7B3BDC0DD1C6EAEA185_inline (ARTrackedImagesChangedEventArgs_tD7718E40EE25D22E9143DA4F556C44980E18BE2F* __this, const RuntimeMethod* method) 
{
	{
		List_1_tFA730335FF46512E4245B2F46892CA318B394FAE* L_0 = __this->___U3CremovedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_tF1201F2F725A8AE529D436F38A4771FB6790F3C6* ARTrackedObjectsChangedEventArgs_get_added_m71E8E2055C979657273051DE23FCEB938964169A_inline (ARTrackedObjectsChangedEventArgs_t378A63B91AD22EA4BA9C68805C72DDA4B7C01509* __this, const RuntimeMethod* method) 
{
	{
		List_1_tF1201F2F725A8AE529D436F38A4771FB6790F3C6* L_0 = __this->___U3CaddedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_tF1201F2F725A8AE529D436F38A4771FB6790F3C6* ARTrackedObjectsChangedEventArgs_get_updated_m01050D519E6281EECB48200D952FE1E1FFFE1A60_inline (ARTrackedObjectsChangedEventArgs_t378A63B91AD22EA4BA9C68805C72DDA4B7C01509* __this, const RuntimeMethod* method) 
{
	{
		List_1_tF1201F2F725A8AE529D436F38A4771FB6790F3C6* L_0 = __this->___U3CupdatedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_tF1201F2F725A8AE529D436F38A4771FB6790F3C6* ARTrackedObjectsChangedEventArgs_get_removed_mDF5FA585D41D4B0D4517D3C5387B254F6EA6DFDA_inline (ARTrackedObjectsChangedEventArgs_t378A63B91AD22EA4BA9C68805C72DDA4B7C01509* __this, const RuntimeMethod* method) 
{
	{
		List_1_tF1201F2F725A8AE529D436F38A4771FB6790F3C6* L_0 = __this->___U3CremovedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void SessionStateSwitchUnit_set_flowIn_mD8F159479E3884269A7D2057D7F3E8C52B2923D5_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, ControlInput_t1AD434958538C865F55A62C66B9C24DA82AD31FE* ___0_value, const RuntimeMethod* method) 
{
	{
		ControlInput_t1AD434958538C865F55A62C66B9C24DA82AD31FE* L_0 = ___0_value;
		__this->___U3CflowInU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CflowInU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void SessionStateSwitchUnit_set_sessionStateIn_m39EB7D50DFEE0306E0C4F690E06D8B6D246B1AFB_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* ___0_value, const RuntimeMethod* method) 
{
	{
		ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* L_0 = ___0_value;
		__this->___U3CsessionStateInU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CsessionStateInU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void SessionStateSwitchUnit_set_none_mE058B6C1A72B0BD0DC6BAEA629EC6C8A55BD273C_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___0_value, const RuntimeMethod* method) 
{
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_0 = ___0_value;
		__this->___U3CnoneU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CnoneU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void SessionStateSwitchUnit_set_unsupported_m683621DAFF0725F51FAB5D0FB9CB63F5731E53A1_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___0_value, const RuntimeMethod* method) 
{
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_0 = ___0_value;
		__this->___U3CunsupportedU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CunsupportedU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void SessionStateSwitchUnit_set_checkingAvailability_m0D10A7BFD1348E9694B381482E90F5974E3CF246_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___0_value, const RuntimeMethod* method) 
{
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_0 = ___0_value;
		__this->___U3CcheckingAvailabilityU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CcheckingAvailabilityU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void SessionStateSwitchUnit_set_needsInstall_m6C03848DA29361417CBE17AAC6EF641AE4946A95_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___0_value, const RuntimeMethod* method) 
{
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_0 = ___0_value;
		__this->___U3CneedsInstallU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CneedsInstallU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void SessionStateSwitchUnit_set_installing_m91BC0E0D467AB4CDE4A9E87F43ABA98874D7BAF4_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___0_value, const RuntimeMethod* method) 
{
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_0 = ___0_value;
		__this->___U3CinstallingU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CinstallingU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void SessionStateSwitchUnit_set_ready_m4CF3C56F9F24CDCFE9678F22BBAA988BF7AA6FD3_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___0_value, const RuntimeMethod* method) 
{
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_0 = ___0_value;
		__this->___U3CreadyU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CreadyU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void SessionStateSwitchUnit_set_sessionInitializing_mB3A2FF70C33664AFF792B183A2E39252DDFDACB2_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___0_value, const RuntimeMethod* method) 
{
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_0 = ___0_value;
		__this->___U3CsessionInitializingU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CsessionInitializingU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void SessionStateSwitchUnit_set_sessionTracking_mA270750450E8D31E271811C785AE730FB61AACF0_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* ___0_value, const RuntimeMethod* method) 
{
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_0 = ___0_value;
		__this->___U3CsessionTrackingU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CsessionTrackingU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* SessionStateSwitchUnit_get_sessionStateIn_m4E966BABF18BE40C7178158088CC8DEA31893939_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, const RuntimeMethod* method) 
{
	{
		ValueInput_t2F9A2AA29F683A1C27C273BF8AABA4C2D9851392* L_0 = __this->___U3CsessionStateInU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ControlInput_t1AD434958538C865F55A62C66B9C24DA82AD31FE* SessionStateSwitchUnit_get_flowIn_m667548B27056B2F6F27B63DFCF4412BBAE6637A5_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, const RuntimeMethod* method) 
{
	{
		ControlInput_t1AD434958538C865F55A62C66B9C24DA82AD31FE* L_0 = __this->___U3CflowInU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* SessionStateSwitchUnit_get_none_mCDA812C5D868B12D04C020ED27ED0F886EE0963E_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, const RuntimeMethod* method) 
{
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_0 = __this->___U3CnoneU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* SessionStateSwitchUnit_get_unsupported_m911986D3ADF69617D9E6DFFB367926BBA3639439_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, const RuntimeMethod* method) 
{
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_0 = __this->___U3CunsupportedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* SessionStateSwitchUnit_get_checkingAvailability_m3EF1098F57D30B08B8C7587F9D94B64A4E650ED9_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, const RuntimeMethod* method) 
{
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_0 = __this->___U3CcheckingAvailabilityU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* SessionStateSwitchUnit_get_needsInstall_mDBE283471C7F3B8C8A1D6962DAFECBC81CC7071E_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, const RuntimeMethod* method) 
{
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_0 = __this->___U3CneedsInstallU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* SessionStateSwitchUnit_get_installing_mAB1C0C05D8CAD09DE7EA9177806621D1263E120F_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, const RuntimeMethod* method) 
{
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_0 = __this->___U3CinstallingU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* SessionStateSwitchUnit_get_ready_m5FDBB155FE257620F68167B4A7A1DEEB597491D2_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, const RuntimeMethod* method) 
{
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_0 = __this->___U3CreadyU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* SessionStateSwitchUnit_get_sessionInitializing_mDAB4A059DA69D91C4A7E3ED9B207A31F64040746_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, const RuntimeMethod* method) 
{
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_0 = __this->___U3CsessionInitializingU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* SessionStateSwitchUnit_get_sessionTracking_m46B500F7B3F7276E269EC893E52EE71478554365_inline (SessionStateSwitchUnit_t20FE0233ACF3B7C4EE2306768E7176E70AC6C1C8* __this, const RuntimeMethod* method) 
{
	{
		ControlOutput_t478B71A736EFBCE24B532F0279A9E701C9CDD8B9* L_0 = __this->___U3CsessionTrackingU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_added_mA5EB9599698C839DF1B830628E9AA712D2BAA239_gshared_inline (TrackablesChangedEventUnit_8_t1733B9D53AC7A75201E66A3C4299F9EEAF6D9AC9* __this, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = __this->___U3CaddedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_updated_m0E9C84C6478DC8A8EC77951533068775F7773D47_gshared_inline (TrackablesChangedEventUnit_8_t1733B9D53AC7A75201E66A3C4299F9EEAF6D9AC9* __this, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = __this->___U3CupdatedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_removed_mFA20D5CC71D33EF2AD69130EE9B6DFC53C57826E_gshared_inline (TrackablesChangedEventUnit_8_t1733B9D53AC7A75201E66A3C4299F9EEAF6D9AC9* __this, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = __this->___U3CremovedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_added_m227082B23BA194D4F4D78E344F92D69797149EF2_gshared_inline (TrackablesChangedEventUnit_8_t32C53D3043B80B877FC2C13D865611AE1870707B* __this, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = __this->___U3CaddedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_updated_m92262CE776B63637F7622CDECF3F5D1284E380E8_gshared_inline (TrackablesChangedEventUnit_8_t32C53D3043B80B877FC2C13D865611AE1870707B* __this, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = __this->___U3CupdatedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_removed_m3681946A08131E0DA77641ED3161001F7EC45ED0_gshared_inline (TrackablesChangedEventUnit_8_t32C53D3043B80B877FC2C13D865611AE1870707B* __this, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = __this->___U3CremovedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_added_m1C11AC657CA855142D543432ABC974CCADE0B3B0_gshared_inline (TrackablesChangedEventUnit_8_t3ED65845C697D5F6365D653761D50033F9DEA043* __this, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = __this->___U3CaddedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_updated_m934BC27B2518C2AF1097361B2834B91346245905_gshared_inline (TrackablesChangedEventUnit_8_t3ED65845C697D5F6365D653761D50033F9DEA043* __this, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = __this->___U3CupdatedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_removed_mB2360A7E90332B148E3297E1D32F6031E3E8DEFB_gshared_inline (TrackablesChangedEventUnit_8_t3ED65845C697D5F6365D653761D50033F9DEA043* __this, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = __this->___U3CremovedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_added_m33DF3083F2E514B2F73286CA73AE45BA5A181D54_gshared_inline (TrackablesChangedEventUnit_8_tAD7DEECE198B73643980718B72E5D18B9AE18A29* __this, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = __this->___U3CaddedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_updated_m92AC4CE3FFB5D2CD7EF4EFEC9FCBA7BD5429FA18_gshared_inline (TrackablesChangedEventUnit_8_tAD7DEECE198B73643980718B72E5D18B9AE18A29* __this, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = __this->___U3CupdatedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_removed_m730B768E99F9428C523540CFEAFD4ADC88B3DCBB_gshared_inline (TrackablesChangedEventUnit_8_tAD7DEECE198B73643980718B72E5D18B9AE18A29* __this, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = __this->___U3CremovedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_added_mE06A697E13C4142D6E80237E44FF471D37AA81A8_gshared_inline (TrackablesChangedEventUnit_8_tE5B851C314A74199250431CA9EF82851FE17AF1E* __this, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = __this->___U3CaddedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_updated_mD5811FDA361D62C2B442A29B4271C140BD68FD4D_gshared_inline (TrackablesChangedEventUnit_8_tE5B851C314A74199250431CA9EF82851FE17AF1E* __this, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = __this->___U3CupdatedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_removed_mA5A75FD646439C5DF03447808BAC63B942DDE510_gshared_inline (TrackablesChangedEventUnit_8_tE5B851C314A74199250431CA9EF82851FE17AF1E* __this, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = __this->___U3CremovedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_added_mBC80FDCEB43A815F5C1E00246B3A0A76BEBF6A55_gshared_inline (TrackablesChangedEventUnit_8_t80ACB5EC468FCE7380B5D1FFB60D34852FDDECEB* __this, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = __this->___U3CaddedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_updated_mE28D5A4C9A9AC30D6587B7C701797F9D89EC4D3C_gshared_inline (TrackablesChangedEventUnit_8_t80ACB5EC468FCE7380B5D1FFB60D34852FDDECEB* __this, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = __this->___U3CupdatedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_removed_m72E4C6109F83CDAFC1C03A9E7FA7F9F421F40D2D_gshared_inline (TrackablesChangedEventUnit_8_t80ACB5EC468FCE7380B5D1FFB60D34852FDDECEB* __this, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = __this->___U3CremovedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_added_mDFD7A4FA5EE6EE922E003F4F32AF1E97E8532D0B_gshared_inline (TrackablesChangedEventUnit_8_t2B87F30FA4D65ECE396E4A074036A8F6A0F95654* __this, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = __this->___U3CaddedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_updated_mF386F24BC1B82AD93FDFA090C7A6032FD1835BC9_gshared_inline (TrackablesChangedEventUnit_8_t2B87F30FA4D65ECE396E4A074036A8F6A0F95654* __this, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = __this->___U3CupdatedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_removed_mD34EBC8EA96EFAA5E5B3BB3A5CD9E63F45C8F46C_gshared_inline (TrackablesChangedEventUnit_8_t2B87F30FA4D65ECE396E4A074036A8F6A0F95654* __this, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = __this->___U3CremovedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_added_m875B59231345FF1E6FBFE1E5EFF593995A2EF71C_gshared_inline (TrackablesChangedEventUnit_8_t9D9E1B534664684D3799239F14EA6394D5EA7102* __this, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = __this->___U3CaddedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_updated_m362E1C8FE77AB6DF95D605D202C2F2D2E51EBDA2_gshared_inline (TrackablesChangedEventUnit_8_t9D9E1B534664684D3799239F14EA6394D5EA7102* __this, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = __this->___U3CupdatedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_removed_m81EDFBA98C36006A10B4B9EB3DC52C4E073EE35F_gshared_inline (TrackablesChangedEventUnit_8_t9D9E1B534664684D3799239F14EA6394D5EA7102* __this, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = __this->___U3CremovedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_added_m56E2AAE2F7EB5F918BCA351928FA830CFC40B00C_gshared_inline (TrackablesChangedEventUnit_8_t65F4F7BA35861764870385ED28CF9DA4366517DB* __this, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = __this->___U3CaddedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_updated_mB47F00BDB8F28A8C052BF4525824557AA2D0E5F2_gshared_inline (TrackablesChangedEventUnit_8_t65F4F7BA35861764870385ED28CF9DA4366517DB* __this, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = __this->___U3CupdatedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* TrackablesChangedEventUnit_8_get_removed_m0F3958FFA505957614CEE2F8AAD2A1BBA5641D5C_gshared_inline (TrackablesChangedEventUnit_8_t65F4F7BA35861764870385ED28CF9DA4366517DB* __this, const RuntimeMethod* method) 
{
	{
		ValueOutput_tADDC9DBF6515A1572E876C3881B5F110DC5EBE1B* L_0 = __this->___U3CremovedU3Ek__BackingField;
		return L_0;
	}
}
