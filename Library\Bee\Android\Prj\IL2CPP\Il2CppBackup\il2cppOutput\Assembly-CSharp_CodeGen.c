﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"


extern const RuntimeMethod* EasyWebCamBase_EasyWebCamStarted_m51612CECBB0E65406A385FAEC9459359BCA2EA67_RuntimeMethod_var;
extern const RuntimeMethod* EasyWebCamBase_EasyWebCamStoped_mA95AEC189C7F85009572FFEE5153B99E0D53AC1C_RuntimeMethod_var;
extern const RuntimeMethod* EasyWebCamBase_EasyWebCamUpdate_m9AB6AA793FDD7CDFBFFC802290BF145C37B23A05_RuntimeMethod_var;



extern void AnimController_OnEnable_m496E64AAC29A813F2AF9A6773EFE4A9D7A0D50E7 (void);
extern void AnimController_OnDisable_m82ED81C15AABF38626BF43945E1A1E61D7A84345 (void);
extern void AnimController_Start_m9D6164B6CE37DECBD114FDE08915ADFAE46B5EB4 (void);
extern void AnimController_StartKinAnim_m6092D6256423D40C252FDA98F688922754D2DCB6 (void);
extern void AnimController_DelayedStartCoroutine_m93F7514F0DE5605FD73984041F4D3BF931FCE06D (void);
extern void AnimController_CallFunctionsSequentially_m95C3344D56A4482FF7247CF75861FDED8CF92540 (void);
extern void AnimController_CallFunctionWithDelay_m62C8C5E1BB0340B1A4144937BCEC25F76425B430 (void);
extern void AnimController_TurnOnShockWave_m5CED8C3A09F55BC1A2353381AB99725856F62280 (void);
extern void AnimController_TurnOffGrenalDer1_m490D7E7BE39F6E0F7531857E685CBD2F51C73ED0 (void);
extern void AnimController_TurnOffGrenalDer2_mE8F23051CD33826C60CAEAE70DBF70E49BE059E2 (void);
extern void AnimController_TurnOffKidneysCube22_m9093E9763903D0718F571D038445B515BBB782FC (void);
extern void AnimController_TurnOffKidneyArteria_m00765D407DC2D6F9967474EC40F5BDD4620DDB60 (void);
extern void AnimController_TurnOffKidneyVena_m6AF7729DEF9898500438BF58AD7EF7C1FDE172FC (void);
extern void AnimController_TurnOffkidneysCompleteLeftFront_mE4FA6FEE3A9BCE46480DED2DEE5EEA9E34158B31 (void);
extern void AnimController__ctor_m243F834C40EE4C5E658EAB4A6EEC37C010547D19 (void);
extern void U3CCallFunctionWithDelayU3Ed__21__ctor_m784A4E67CC5A5047E7A96C0777548FC39C38CF37 (void);
extern void U3CCallFunctionWithDelayU3Ed__21_System_IDisposable_Dispose_mF2F56111B02E65D63815E6601F83517B7805CCBE (void);
extern void U3CCallFunctionWithDelayU3Ed__21_MoveNext_m5CC541D5C29F01AFA4C3E90E7B7425EA41A36E29 (void);
extern void U3CCallFunctionWithDelayU3Ed__21_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m4E470F93FF3AB2239EF515B58036C3CBD479B8AD (void);
extern void U3CCallFunctionWithDelayU3Ed__21_System_Collections_IEnumerator_Reset_m3863163F26D591B9A90584F67FAEE45F90ED3BD0 (void);
extern void U3CCallFunctionWithDelayU3Ed__21_System_Collections_IEnumerator_get_Current_m3D5110010B75079B53E9F594D32BC64B2D96D2B8 (void);
extern void U3CCallFunctionsSequentiallyU3Ed__20__ctor_mECA9A8828AC9904EFB2B12D960648A9705CC422B (void);
extern void U3CCallFunctionsSequentiallyU3Ed__20_System_IDisposable_Dispose_m636E14C098F57E298168125C5F72C777B40F7C34 (void);
extern void U3CCallFunctionsSequentiallyU3Ed__20_MoveNext_m2E306BB008189576F988C98CBF1528CFF1CC8EE4 (void);
extern void U3CCallFunctionsSequentiallyU3Ed__20_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m20BA6E58506006CCA75166AFFC6BB6046DDE130D (void);
extern void U3CCallFunctionsSequentiallyU3Ed__20_System_Collections_IEnumerator_Reset_m4C86B6BCB5CADB0D45C20DE5FCD23117544C30BC (void);
extern void U3CCallFunctionsSequentiallyU3Ed__20_System_Collections_IEnumerator_get_Current_m1F20A403D0A9F18EE9779AAF0488C260A5B22AD7 (void);
extern void ARCursor_Start_mF861EF07BBBDEA10481F7F4AC557740AE0B182B1 (void);
extern void ARCursor_Update_m156A232086B1784C1125D3A9F1141FEE78055443 (void);
extern void ARCursor_UpdateCursor_mF10A97A53652C882FD9FFEE7DD963F3990861CDA (void);
extern void ARCursor__ctor_m137F0AE7E4CBAB2E1D72B1ED04AEC5E1315980EB (void);
extern void AudioPlayer_Start_m1CD4C540B92CF134746EF50084C5729768B93CD8 (void);
extern void AudioPlayer_PlaySound_mF515D08985D1C0C7D284EECE40FC2B9E3CDFD279 (void);
extern void AudioPlayer_PlaySound_mB6036E595DE3D133353BB037F6D4FCE0B4CF41D9 (void);
extern void AudioPlayer_PlaySoundAtPosition_mFF76BE97DA63E8BED0CC779CEEBBE6867BDBF837 (void);
extern void AudioPlayer__ctor_m3F81E1F07C97F2ECE2A837B3FB918ECBBCA14B17 (void);
extern void FaceCamera_Start_m36C189451F380B7FA03494B1466276854CF49FBD (void);
extern void FaceCamera_Update_m295E828F20DC5F8CADC64934C989C94F27592CA3 (void);
extern void FaceCamera__ctor_mC5D44A53DFAE40B1103AEBB2A513D3CE453AE25A (void);
extern void MaterialAlphaAnimator_AnimateMaterialAlpha_mCED510BA73F43C0247384F649D9DA7B9CDE2E9C2 (void);
extern void MaterialAlphaAnimator_AnimateMaterialAlphaRoutine_mDDC67BF3B8CCF9800ACDC215692C5315E0C236DB (void);
extern void MaterialAlphaAnimator__ctor_m57AC72D6D777DBF6FCFFA713B693221FF45D4C0C (void);
extern void U3CAnimateMaterialAlphaRoutineU3Ed__1__ctor_m171F781AC97D8A4F5B6307873B0C0D2DDC5BF8C1 (void);
extern void U3CAnimateMaterialAlphaRoutineU3Ed__1_System_IDisposable_Dispose_mD18190D85743AD88FDD78A3471B64F171E468395 (void);
extern void U3CAnimateMaterialAlphaRoutineU3Ed__1_MoveNext_mB6B5D16CC0CE4155F7E0C9B5FB65B2D223854AF8 (void);
extern void U3CAnimateMaterialAlphaRoutineU3Ed__1_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m8A7BBA5B45C765CF3370FE9D07924CF62D05E4FF (void);
extern void U3CAnimateMaterialAlphaRoutineU3Ed__1_System_Collections_IEnumerator_Reset_mB26CA06F43A43395E476FA2D9DC011F91524D63F (void);
extern void U3CAnimateMaterialAlphaRoutineU3Ed__1_System_Collections_IEnumerator_get_Current_mCB775FBE0CA8F490D129ACF1CCFE4CCFBB6534FA (void);
extern void NeverSleepCode_Start_mEDB1BAB920DE0F6C104E4286207423FF9D499A92 (void);
extern void NeverSleepCode__ctor_m10C4F406259693E7AA8673945881401EE4E1B44E (void);
extern void ObjectAnimator_Start_m1848FD6B2C1E5EF858B9501DF165FEF6907FAAF3 (void);
extern void ObjectAnimator_StartAnimation_mF6FA57605E8FC7D1E61217D47B8BE56E977AC493 (void);
extern void ObjectAnimator_ReturnToStart_m979D3EB5378646FE50074E6329AD254C0F97C55D (void);
extern void ObjectAnimator_AnimateObject_mB98749B8DC526EFA39D10855840639B7CF662933 (void);
extern void ObjectAnimator_SkipToTarget_mB547F59970965A127C30DDB4E547BEFC63D16596 (void);
extern void ObjectAnimator_SkipToStart_m4D40EDB9E772936ADA8108789B1FF91F106272FD (void);
extern void ObjectAnimator_SetAnimationDistance_m7088E82696D4A36517DEEA22FA3AAEE29E17A7B0 (void);
extern void ObjectAnimator_SetAnimationDuration_m56F7608C7E1DA453C9F5C47079D01E29074DCD43 (void);
extern void ObjectAnimator__ctor_m9D8357561F77936D80D6465A4B918CBB2D161428 (void);
extern void U3CAnimateObjectU3Ed__12__ctor_m1C7FA9BBAA3B55C5B861CFB9D8059F49682C37F2 (void);
extern void U3CAnimateObjectU3Ed__12_System_IDisposable_Dispose_mF41081A9EF20441B6BDF12E498320FBA1DAEE0B9 (void);
extern void U3CAnimateObjectU3Ed__12_MoveNext_m823D876FF2CDC4CFDBC5E722AB10EDE77C933819 (void);
extern void U3CAnimateObjectU3Ed__12_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m2D598265D61AAA0E589B25365EA9523456BC27A1 (void);
extern void U3CAnimateObjectU3Ed__12_System_Collections_IEnumerator_Reset_mF5DA63BFA4A213643AF389F3B5CDBE55D5F49405 (void);
extern void U3CAnimateObjectU3Ed__12_System_Collections_IEnumerator_get_Current_m4218B2E5D2DD4D3832390B8F88D25D5ED71D585D (void);
extern void PressStartContoller_Awake_m4268AF40B5F271949AE25EAC55A77CD1C356BD47 (void);
extern void PressStartContoller_ButtonPressToStart_mD992EB65B23CDA85AC9EFA5CE60C15FC82B78E46 (void);
extern void PressStartContoller_Update_mB125E658B7953A1684123945A18E680C57F6F420 (void);
extern void PressStartContoller__ctor_m1F4DB263ACAB82839CC84F00ACD4CC6588BE070D (void);
extern void PressStartContoller__cctor_mAFC01F54EBA39B6203BC742058D1788D32E603F8 (void);
extern void PressStartContollerNew_Start_m4CABA0658129063B33D3564806591399D619A201 (void);
extern void PressStartContollerNew_Update_m99E8036055D80A18CD4843D265BAE97CACBD6D3F (void);
extern void PressStartContollerNew_PressPlayButton_m7BE6F5E64155F9DAD7ED89AA1C3C38AFB34AA009 (void);
extern void PressStartContollerNew_PressPauseButton_m57AA11DCD019CBD2A8BDFFA91DF46B6836883869 (void);
extern void PressStartContollerNew_PressReplayButton_m9824F028F5C3F54DCDB5BE094DD8C50F65F33D42 (void);
extern void PressStartContollerNew__ctor_mDD9239304F4D95B57CA059BDEEF3F3436F18C69F (void);
extern void PressStartContollerNew__cctor_m5B03A431AF21D522A4EFDD060ADC4EC54E4D9303 (void);
extern void QRCodeContoller_ResetScanner_m27297DD9FD7A26B35FF6CBA976B7234B5588E73B (void);
extern void QRCodeContoller_QrCallback_mBB7D5597715E3843179ABA6631F43943AAAA447D (void);
extern void QRCodeContoller_GetRequest_m928B51BCE2D98C096E74744EE24357DA5A720810 (void);
extern void QRCodeContoller__ctor_mF29138FC93834033AAC0F2CF58B0EE5CA51EFFCD (void);
extern void U3CGetRequestU3Ed__5__ctor_m3FF28BAADDEBA107974B07F4CC0816C9AE88292B (void);
extern void U3CGetRequestU3Ed__5_System_IDisposable_Dispose_m638A821E41082E680B1070E01B504BCBAD9657D9 (void);
extern void U3CGetRequestU3Ed__5_MoveNext_m6AD918BEEC6E70B8C214DD4CCDE72577B55A2ADF (void);
extern void U3CGetRequestU3Ed__5_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mFF8CB067EED56D8AF7D0832800B5E4A9321C4011 (void);
extern void U3CGetRequestU3Ed__5_System_Collections_IEnumerator_Reset_m81540B8B2BF4CA1C57693412ECA43841FD2699F4 (void);
extern void U3CGetRequestU3Ed__5_System_Collections_IEnumerator_get_Current_m97CF7DC2ADAEE6BE07661A4EBFD798C2EB144170 (void);
extern void TimelimeControllerNew_OnEnable_m624E18A261527976FCAAA758D364211574A582E1 (void);
extern void TimelimeControllerNew_OnDisable_m0EDBBCD1DC2752CE4F6062DD7EC94A95594566AA (void);
extern void TimelimeControllerNew_StartTimeline_m4A25D1C3193F5614BCB8B9BAF8DECBA312913E26 (void);
extern void TimelimeControllerNew_ReplayTimeline_m04B9B216A30C1FB0C7E90AA8FE5A05D8981EB873 (void);
extern void TimelimeControllerNew__ctor_m6379FA842B86A766AFB89812F4B509515C34C421 (void);
extern void CameraSetting_RequestCameraPermissions_m103B16EC4C29D9FC1AC552D7F7A253438F5A6E0F (void);
extern void CameraSetting__ctor_m74657D44311DA4D0B0EB7E7219E1C904A54513A2 (void);
extern void CameraPermissionHelper_Awake_m72008FDB1DD2ED54E80738F0A17FB350E1FB4370 (void);
extern void CameraPermissionHelper__ctor_m962448E8715123ED6C27E9FDE6B0FF0F148CCE6C (void);
extern void U3CU3Ec__DisplayClass0_0__ctor_mD680524965EEF36617ABCC1C24112C4C56CFC0BA (void);
extern void U3CU3Ec__DisplayClass0_0_U3CRequestCameraPermissionsU3Eg__RequestAndroidU7C0_m482ECF6DA3A4ED9782EAC124A376E1B01EED8971 (void);
extern void U3CU3CRequestCameraPermissionsU3Eg__RequestAndroidU7C0U3Ed__ctor_m3C619D0DE60FFEE0FB7BB38F6098754D9D3D3117 (void);
extern void U3CU3CRequestCameraPermissionsU3Eg__RequestAndroidU7C0U3Ed_System_IDisposable_Dispose_m07620BAC53AA86D4AB0EB254E066343007B77AFF (void);
extern void U3CU3CRequestCameraPermissionsU3Eg__RequestAndroidU7C0U3Ed_MoveNext_mD4875593F241A01F832E8DB80140346AB9506EE4 (void);
extern void U3CU3CRequestCameraPermissionsU3Eg__RequestAndroidU7C0U3Ed_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m601CFBD3514CAED84FBF71B7DD1A7F8617A12C41 (void);
extern void U3CU3CRequestCameraPermissionsU3Eg__RequestAndroidU7C0U3Ed_System_Collections_IEnumerator_Reset_mCE3C3548F4A692DCDD70ABAEC4964840AB43CBBE (void);
extern void U3CU3CRequestCameraPermissionsU3Eg__RequestAndroidU7C0U3Ed_System_Collections_IEnumerator_get_Current_mD978663B6FC8070F5EF09B0B9912CAA1EBAD6427 (void);
extern void U3CU3Ec__DisplayClass0_1__ctor_m24BA8B5C8DBE557B204CFFCE1C8819B4F6324828 (void);
extern void U3CU3Ec__DisplayClass0_1_U3CRequestCameraPermissionsU3Eb__1_m1E138A090711CC25CC99ACDD10FB5C41C4B14327 (void);
extern void EasyWebCamUnity_get_WebCamPreview_m13F1DED348CABAB5D5E3BBB3E27127AB1F7E2ED6 (void);
extern void EasyWebCamUnity_get_previewWidth_m26C5BF432B65C8DC2E0DD7BE3675352B029F13F8 (void);
extern void EasyWebCamUnity_set_previewWidth_mB7445C9047FBBBDAF5FF627AEEF572DD561EB43A (void);
extern void EasyWebCamUnity_get_previewHeight_m8DB70FD1618B24FC6A66C5F3EE701D47E836A79C (void);
extern void EasyWebCamUnity_set_previewHeight_m25F7B125F15B44F1D03DDC0BB55E8EC99E51728A (void);
extern void EasyWebCamUnity_get_IsPlaying_mF39AEE4B229B984A10321E43ACD8DF0B8851C813 (void);
extern void EasyWebCamUnity__ctor_m1FD0F33DF61A3E8B9F62F0CABFA01C322BDED735 (void);
extern void EasyWebCamUnity_getEnterFrame_mCAF395651B48E6231BEB7ACFC453B51EEA2C5818 (void);
extern void EasyWebCamUnity_Init_m5276653A00CDED191DF285C5294A1976ADD79931 (void);
extern void EasyWebCamUnity_isCameraUsable_m8EDE7AC1536E57E01146DE1E8009BC6A55993E9B (void);
extern void EasyWebCamUnity_OnPause_m80C620B0AC3369BA33C508BC6EA272EFDB6B831F (void);
extern void EasyWebCamUnity_Play_m1B0A91AB06E1AD0D6557DC1C6DB052AB53A6D34D (void);
extern void EasyWebCamUnity_waitimeToPlay_mBDC86DD2FC713D8CFCB08305E6C132ABF19166CD (void);
extern void EasyWebCamUnity_Stop_m941EF8F176E673F0C7E34A6AAA226BCFA382C996 (void);
extern void EasyWebCamUnity_Release_mECFBD944B4182434CCBF272CBC432F7FB5FC5387 (void);
extern void EasyWebCamUnity_setFlashMode_m8F35EBDE71C2FB825BEACCC879F06757753C22B3 (void);
extern void EasyWebCamUnity_setFocusMode_m3C87DD8FAF687E977D2CFD23931DDD12158FD4A6 (void);
extern void EasyWebCamUnity_setOrientation_m47DF951DCCBB622CDE0F18A7AFDA9DBAA21635C9 (void);
extern void EasyWebCamUnity_setPreviewResolution_mA0C24C872D3B49E5FA17F77ADA82403C72109E55 (void);
extern void EasyWebCamUnity_initResolution_m183777E116C2818A55520050B19CC47F03396AD6 (void);
extern void EasyWebCamUnity_requestIosCamera_m57A23087E1D79C23373B5404EB2A7731E6766DA4 (void);
extern void EasyWebCamUnity_waitCameraInit_mCFB85D31F1F31455FE0AED98F16AC26192C1352E (void);
extern void EasyWebCamUnity_waitToolActive_m60287E2CA02BC7FDD988039EDE20146D80AD90FC (void);
extern void EasyWebCamUnity_setTorchMode_m60EFC4EF22838C52D75A0B6DD143C0A2F8E58049 (void);
extern void EasyWebCamUnity_SwitchCamera_m597BF291870DB67EDE752732980C84813A99FDEA (void);
extern void EasyWebCamUnity_TakePhoto_mD4D7F36CE9C2960CF1BA6DE3A37BAD9F3F183AC5 (void);
extern void EasyWebCamUnity_tapFocus_mCFDAB250237AFBF118C2D9F1D1B355FC4224195F (void);
extern void EasyWebCamUnity_UpdateImage_mDD33710FCAFD2FD4CF17D9E398140ECC98545D5A (void);
extern void EasyWebCamUnity_convertUntiyTexture_m57707603B25E5D3C48C6FAD4371AE0D93B82ED3E (void);
extern void EasyWebCamUnity_GetDeviceName_m1FC04A150C3E9524A8F2BAAB2C0719ADA587EB72 (void);
extern void CameraPermissionHelper__ctor_m760C3A5942F4E913A1945D10D4D0FFFB402D3EC4 (void);
extern void IosCameraPermissionHelper__ctor_m9C3F4F8BF254F72D9B1715A78329CE11EBAD8AC7 (void);
extern void ToolActiveCameraPermissionHelper__ctor_m61723129917ECA717EFE3302D1A087B7AD457A88 (void);
extern void U3CU3Ec__DisplayClass37_0__ctor_mFC317B5FEBE9C829EB0F338B9D66234EEDBF25D6 (void);
extern void U3CU3Ec__DisplayClass37_0_U3CrequestIosCameraU3Eg__callIosCameraU7C0_mC035B3B515580CB3038560944C9FA739DB54211C (void);
extern void U3CU3CrequestIosCameraU3Eg__callIosCameraU7C0U3Ed__ctor_m11551C4F41BE2BA7E8D239BE6E713A2D17BDB5B1 (void);
extern void U3CU3CrequestIosCameraU3Eg__callIosCameraU7C0U3Ed_System_IDisposable_Dispose_mE1AFBF54092A3C11273AB684ED1736A7E34795DB (void);
extern void U3CU3CrequestIosCameraU3Eg__callIosCameraU7C0U3Ed_MoveNext_m49D82476C12176B99FE7D75C824A482EBB12E938 (void);
extern void U3CU3CrequestIosCameraU3Eg__callIosCameraU7C0U3Ed_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m58539BBD4C2303525BD52C11E5ED6E8823D5A1CF (void);
extern void U3CU3CrequestIosCameraU3Eg__callIosCameraU7C0U3Ed_System_Collections_IEnumerator_Reset_mD869C4AA341B5336883DFD0237B51BDBCA8C9316 (void);
extern void U3CU3CrequestIosCameraU3Eg__callIosCameraU7C0U3Ed_System_Collections_IEnumerator_get_Current_mDEECFB98924B5FD289BD9B974C27632C38D6D9DD (void);
extern void U3CU3Ec__DisplayClass38_0__ctor_m85A25A54D8578FBD0B44F0409C398EA6D33762E7 (void);
extern void U3CU3Ec__DisplayClass38_0_U3CwaitCameraInitU3Eg__RequestInitU7C0_m6BEF386C3B8600A1A16D8F57D8F5F312D1D7FEBC (void);
extern void U3CU3CwaitCameraInitU3Eg__RequestInitU7C0U3Ed__ctor_m0954B715D03E6FDDB87DD4C197192589CE8F09AD (void);
extern void U3CU3CwaitCameraInitU3Eg__RequestInitU7C0U3Ed_System_IDisposable_Dispose_mB5F77BC65D299136B4841012E7B8452CC67B4CE0 (void);
extern void U3CU3CwaitCameraInitU3Eg__RequestInitU7C0U3Ed_MoveNext_m50F6EB5738A3BA6F56A4F2FBD633404C31EE7116 (void);
extern void U3CU3CwaitCameraInitU3Eg__RequestInitU7C0U3Ed_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m5803C1727DFE3F8E66E0DE44E98F18CA71E4A5CA (void);
extern void U3CU3CwaitCameraInitU3Eg__RequestInitU7C0U3Ed_System_Collections_IEnumerator_Reset_mA427E86E67970C56C00F66EBFDAC211128AA5949 (void);
extern void U3CU3CwaitCameraInitU3Eg__RequestInitU7C0U3Ed_System_Collections_IEnumerator_get_Current_m38CBB850CEF2B8ECCDC59D89941B83356A78B8F5 (void);
extern void U3CU3Ec__DisplayClass39_0__ctor_m3A22CD6FA5B9DEA1DCE8C33877DCE5B041CB2F52 (void);
extern void U3CU3Ec__DisplayClass39_0_U3CwaitToolActiveU3Eg__waitActiveU7C0_m7F1791568F1D927DDEF4940481D41D99EC060373 (void);
extern void U3CU3CwaitToolActiveU3Eg__waitActiveU7C0U3Ed__ctor_m9764EFC7A1A021AA93D4B4FAD9BF401BF075C40C (void);
extern void U3CU3CwaitToolActiveU3Eg__waitActiveU7C0U3Ed_System_IDisposable_Dispose_m8FE45986A87DAE3C3AFD2762F686E3FFB6606510 (void);
extern void U3CU3CwaitToolActiveU3Eg__waitActiveU7C0U3Ed_MoveNext_m4DF8B21956F9D40A285DE4A9459C08BF78B7A7C5 (void);
extern void U3CU3CwaitToolActiveU3Eg__waitActiveU7C0U3Ed_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mA569F35EE555EC9DC7BFDD47EFA461A246CBA2DA (void);
extern void U3CU3CwaitToolActiveU3Eg__waitActiveU7C0U3Ed_System_Collections_IEnumerator_Reset_mAF6F5844BA64D42B832986C14BC6676EE80DF0E9 (void);
extern void U3CU3CwaitToolActiveU3Eg__waitActiveU7C0U3Ed_System_Collections_IEnumerator_get_Current_mC26C184E437C6230C0F743A0788DE83260319394 (void);
extern void U3CPlayU3Ed__28_MoveNext_m0B80CA253A3DD436B7F7519800733916C991113F (void);
extern void U3CPlayU3Ed__28_SetStateMachine_mF04521BD01F999AC71E018A63B9C7EEB354623DD (void);
extern void U3CinitResolutionU3Ed__36_MoveNext_mDB77DF9D195C5E3B23DBAA601508FB216B54072A (void);
extern void U3CinitResolutionU3Ed__36_SetStateMachine_mBE7C03BAFECFA209A1DB9EBDC29870BFBABED513 (void);
extern void U3CwaitimeToPlayU3Ed__29__ctor_m72F1CA318C153605A46EC0DF3B6B4EBCC08ECCF4 (void);
extern void U3CwaitimeToPlayU3Ed__29_System_IDisposable_Dispose_mBDCF537F0CE8BEB3D31767B1D2309A4978037D9C (void);
extern void U3CwaitimeToPlayU3Ed__29_MoveNext_m6B199BF0F08E5176934216D1D78F75C4F94C7930 (void);
extern void U3CwaitimeToPlayU3Ed__29_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m6342318FCF9EE579CF127FB2429CB74723EF3397 (void);
extern void U3CwaitimeToPlayU3Ed__29_System_Collections_IEnumerator_Reset_mE88C04C2AEA447FA5B493319356F2DC3ED89B2BB (void);
extern void U3CwaitimeToPlayU3Ed__29_System_Collections_IEnumerator_get_Current_m1EA6F3A2D5F004FCA51F878D91958A31EDC0F53E (void);
extern void NativePlugin_Init_m5BA932D39DB178DBC215562C1E43A3B22B514C38 (void);
extern void NativePlugin_Close_mB8D29DC463D5ACFCBB902E5BB995BE20CE59E9C1 (void);
extern void NativePlugin_ewcUpdateTextureGL_m907FF8A22C526424FC39CF7B575B49D33E3D35BF (void);
extern void NativePlugin_ewcUpdateTexture32_m61C3117BB56062A9AC78C7C042B99EB60F70FBF5 (void);
extern void NativePlugin_ewcUpdateTexture_m420491ADF2041ACB2CB16B0CCD7035195DE94D7C (void);
extern void NativePlugin_updateParam_m4F62F44F7BC5AB694BCF6F8304BADD1FF01E7867 (void);
extern void NativePlugin_addFloat_mA67E53AE0F3099D592F1DC56CE4AE13A1D3DF71F (void);
extern void NativePlugin_getHeight_m5B494DA802C7DA3A7AF00BBBACEF2C72255CB871 (void);
extern void NativePlugin_getWidth_mEFE9102B993FA618D3FA2F760653E09814D05F74 (void);
extern void NativePlugin_getTestSize_mA03C7CA35638C0C63A23FCA659D9F0A9AEDC58FA (void);
extern void NativePlugin_StartRunning_m01C5930D371BE92011D4996E6F176287A8235B01 (void);
extern void NativePlugin_StopRunning_m7AF7DB9AF648702576D6D6A6C5EA2B57A9C9ACCB (void);
extern void NativePlugin_RegisterCallbacks_m7C6D0C3FA87200BDD16B2BA3345F86863804B572 (void);
extern void NativePlugin_setUnityResolution_mA4D1699F7EE16EAC930DE91D8933A820F9360203 (void);
extern void NativePlugin_setUnityCameraOrientation_mF79085CCCE74BE57A7A79630C6D21043082C7BEA (void);
extern void NativePlugin_convertUnityPixels32_m92FCC044C174F10723ED5820122F94247EE41B6B (void);
extern void NativePlugin_stopUnityCamera_m8E6030C2D7F628F6DC412BF77A66CA945E277FC6 (void);
extern void NativePlugin_Release_mFA8CEA67A4AA61C08B58011411951AADA6B30D1D (void);
extern void NativePlugin__cctor_m252159BFE4CF594FDD16F063B5D1F7438815FB99 (void);
extern void NativePluginStatic_ewcUpdateTexture32_m5DC97EFD59232F2A8E888CE6826AD4976094DBA0 (void);
extern void NativePluginStatic_aruRequestCamera_m9B789E7E7037AE6E81CD9D6A8E20439266C8C20D (void);
extern void NativePluginStatic_Init_m26C834A1DE751657CCDF2A18196CDB1999A13677 (void);
extern void NativePluginStatic_Play_mA506AAFCD07B428F9927C1C320216E379F8C1622 (void);
extern void NativePluginStatic_Stop_m325D8E42A9D075DE5C893883D5B690FD994C35BF (void);
extern void NativePluginStatic_setFocusMode_mABF1DF5FB60303733F73D2A445C2688FDAA7DA08 (void);
extern void NativePluginStatic_setTorchMode_m1C452AA7EF4F6B527F523D19BC0767326CBB8941 (void);
extern void NativePluginStatic_SwitchCamera_m1BF96584D64E5C06CFFDD7CA5B757A2562CFD1FC (void);
extern void NativePluginStatic_UpdateTexture32_m8293EADDF1C723A8A1A813BBD11C5378B9F594F1 (void);
extern void NativePluginStatic_UpdateTextureByte_m870D23D224EC552FF41C3CCA1DBB8461E152A85E (void);
extern void NativePluginStatic_RegisterCallbacks_m42C4EAEF08C379E8418435AEA726263D46A56703 (void);
extern void NativePluginStatic_setPreviewResolution_m77CDCE0569ABE436E0108F98DF71CCCDBFE7D1D2 (void);
extern void NativePluginStatic_SaveImageToAlbum_m395EE1527A879C1B7AB97C6FD3B4217FBB589210 (void);
extern void NativePluginStatic_setOrientation_m9088BBEDE5687402A84A7171198589EFE55F73BE (void);
extern void NativePluginStatic_Release_m17E52F849D86EEE4C8EAF8C3953E3706835C06D4 (void);
extern void NativePluginStatic_setUnityResolution_m04249DE36E2A8A651C50E0ABA04F89A20EB287A3 (void);
extern void NativePluginStatic_setUnityCameraOrientation_mA8A3B4202B49F20B470669B3CDC9D2216F067E03 (void);
extern void NativePluginStatic_convertUnityPixels32_mE4A30E4FC5174FC9050653BE0888D6D27BA6DBD1 (void);
extern void NativePluginStatic_stopUnityCamera_m482A67A00C707A714262696E38E37E457433FE93 (void);
extern void RenderListenerUtility_add_onQuit_m464E77371EFEA6184D5EFA412C115D14C796FBA8 (void);
extern void RenderListenerUtility_remove_onQuit_m5F8E8E54AE32FD2C9D893A057F1CC152DC274D66 (void);
extern void RenderListenerUtility_add_onPause_mBA6DFC43BB5CE73E06D1E5E36C3C77D424D22089 (void);
extern void RenderListenerUtility_remove_onPause_mA7401A45922C7E69F7FE40CCCCEED0DEEF155F57 (void);
extern void RenderListenerUtility__cctor_m63E81ABF01E2A83E1B65B99D36952B17A52135BF (void);
extern void RenderListenerUtility_Start_m79F0CE629E84076E021851D4F84FCEA956510C66 (void);
extern void RenderListenerUtility_Update_m0ED82D25BB903039B84DDD1696B0558576F187F4 (void);
extern void RenderListenerUtility_OnApplicationPause_m3A605CCE2D963BCEB31EE1F64261C7D7C228911F (void);
extern void RenderListenerUtility_OnApplicationQuit_m07BE1E2ACF54F3F8072C839635084A31C1CAB7DC (void);
extern void RenderListenerUtility__ctor_m4B1626D9FA3CF13205CE1DFDA7F104D3B69AA1F9 (void);
extern void Loom_get_Current_m238E0A9AC87A306C23404C6CA2CD3CA3D8D13048 (void);
extern void Loom_Awake_m40186EE40F861381C341DC32CF193FC8679B05B2 (void);
extern void Loom_QueueOnMainThread_mF632665AE41C63CFF07F6338413FCB5386CF3403 (void);
extern void Loom_QueueOnMainThread_mBCEE6625332F8EFD80AE3221FFD2B96B40325833 (void);
extern void Loom_QueueOnMainThread_m50E3B3F0A313EDE10A869E9EE16837DA409E1801 (void);
extern void Loom_QueueOnMainThread_m9F8D85A88F4D2D3D6A8729F49497A2DBE8B23C09 (void);
extern void Loom_RunAsync_mC0333F442F462BA65BE2AB7407F529499E187B97 (void);
extern void Loom_RunAction_mBA937F2AAA721C6BEB299167ED6F261B559D1754 (void);
extern void Loom_Update_m805DA1F6C27748606264FDD84101FAB918325FA0 (void);
extern void Loom__ctor_mFD03E0F1369DFFCC1B28FC39B1B682B876B97456 (void);
extern void DelayedQueueItem__ctor_m22FD1B4DD9EDA74F09C84DB85014138003AAA773 (void);
extern void U3CU3Ec__cctor_m6365AA1ACF32BC66440774B19B200200835C06FD (void);
extern void U3CU3Ec__ctor_mC47F7F2A3395C5C7CCB9CFB44A0E02BD66E55AB1 (void);
extern void U3CU3Ec_U3CUpdateU3Eb__15_0_mF1A976460CD5EF62CFC27C6EB279DD0AB882E25D (void);
extern void U3CU3Ec__DisplayClass7_0__ctor_mB9D81DBAE2F6F187A93F6A9E292AD1124F3BB68A (void);
extern void U3CU3Ec__DisplayClass7_0_U3CQueueOnMainThreadU3Eb__0_m30EC8AD3A8800A494FD662462252A30903867E28 (void);
extern void DeviceCamera_get_preview_m7B7433A2AE04F39001F8EA6F54F268C26F0A3500 (void);
extern void DeviceCamera__ctor_m304B7626F7502A1B5E2B42A62400A9D24BD4C445 (void);
extern void DeviceCamera_getRotationAngle_m3C173A51079D62507D2DAB12A073F455ABD69C26 (void);
extern void DeviceCamera_Play_m8A303CCEFB910A0EC5DD670783B9271063D521B3 (void);
extern void DeviceCamera_Stop_m35FC1DAA2158B865D30FCD943F51A35B3F769583 (void);
extern void DeviceCamera_getSize_m1BC49350DB7AD445CAD06BA9CC88CA16904A09D7 (void);
extern void DeviceCamera_Width_m38FCA6EC23B1ACDC23338B9AC6C6A4146EE4A852 (void);
extern void DeviceCamera_Height_m3DD64154EDD67F60157D2EDB897D3851C68D5B9F (void);
extern void DeviceCamera_isPlaying_mC9CB2258D2F5D3A9CFF1F6736237D5078FFC3C3F (void);
extern void DeviceCamera_GetPixels_mDDA66602FA1FE591320FB1950AC8DF1DA928B02A (void);
extern void DeviceCamera_GetPixels_m366A1FA2C0500D5EE9B239EF8CBBDFFAAD466690 (void);
extern void DeviceCamera_GetPixels32_m619AC5F25C45AC50E1C103A61035E2E21A5126C0 (void);
extern void DeviceCameraController_get_dWebCam_m760F573A9E1EB308F4398532C500D8C9CCE95926 (void);
extern void DeviceCameraController_get_isPlaying_mE7E2DBD207256D0866F97281F714A4B9E170AE47 (void);
extern void DeviceCameraController_Start_m443ED0CD59DB1F7614707F317D7B51B7C7411110 (void);
extern void DeviceCameraController_PreviewStart_m6F15C594C442C8F9F8651DBDE36A6CCE0A747534 (void);
extern void DeviceCameraController_Update_mC447422DFE111E58FD791671C5F40391435DE9CC (void);
extern void DeviceCameraController_StartWork_m951BD91729AD54D2711D53275B7081363AD2BFF3 (void);
extern void DeviceCameraController_StopWork_mFD7BDECFBF491FBA62FF45DC5E22F8BDFB80FB66 (void);
extern void DeviceCameraController_tapFocus_mC18BAE291562D00A65DDFD0577665D19DC78F0A9 (void);
extern void DeviceCameraController_swithCamera_mB913AEF1B3B9DA937CDE7122F831EDC58AA6A2DE (void);
extern void DeviceCameraController_toggleTorch_m30D4B688936A55EC9DA48E6ACE5BCFF04BDA1F1F (void);
extern void DeviceCameraController__ctor_m734A15A78C3C52DC345528AFDB05FA5F6CCC7B24 (void);
extern void GalleryController_get_GalleryPath_mE7420336DBC55812CC3DBB1CE44F0638627F3253 (void);
extern void GalleryController_Start_mA52C274B9B58557C88E70F4DA45C0E0A7537F1E8 (void);
extern void GalleryController_SetGalleryPath_m8264E6FC8D77D07259D81FA802A1BFF9ADBE6E9D (void);
extern void GalleryController_receiveGalleryPath_mE4FDAA38A4AD8AD0D5C1FF0AC93B3D117467C794 (void);
extern void GalleryController_Refresh_m39D3D61820D69CBE823E61579AEFD28E13B3FC81 (void);
extern void GalleryController_SaveImageToGallery_m61D5B4854CBCE889A13D9F97F750FAB12A2BBB49 (void);
extern void GalleryController__ctor_mA114FB72316D3FF57D603AB8F8F4392AF960D72F (void);
extern void QRCodeDecodeController_Start_mC12766A257434043D9451D94044AFD2981EC5EDE (void);
extern void QRCodeDecodeController_Update_m532A336057E1C603262A26660D878E10CA3B97BB (void);
extern void QRCodeDecodeController_Reset_mD7E3AE158E0920E746F2257F3B5283BF5FBF2211 (void);
extern void QRCodeDecodeController_StartWork_mEFB96626661AD7ED7DF1DD170880F8870C70C99A (void);
extern void QRCodeDecodeController_StopWork_m116DC19CE575D07EC096BE67808E65DE56D5FA7B (void);
extern void QRCodeDecodeController_DecodeByStaticPic_m21EBFBA4F0EBF0AC28A65AF8F227ED16B463D534 (void);
extern void QRCodeDecodeController__ctor_m428BD41CDD91B91C5725D9BF821ABF04DF6B06A0 (void);
extern void QRCodeDecodeController_U3CUpdateU3Eb__17_0_m14BD64815EB33E73062EB281C2942682133B7CC0 (void);
extern void UnityEventString__ctor_mD1028D3CF6EBCF023DC5FD697FEFFD5204C0CC8F (void);
extern void QRCodeEncodeController_Start_m930B4E5A2A566C759E4E2A6AC1DA1C826260A256 (void);
extern void QRCodeEncodeController_Update_m1F2C372D7AC5BBF09D21A0BFF092C7E26001CFCB (void);
extern void QRCodeEncodeController_Encode_m91E1747387EA55BF78F1D8B2B13C5531333E2E8E (void);
extern void QRCodeEncodeController_RotateMatrixByClockwise_m82E5DBDBBEEDCCA2DA09CC9A97241081A77EDDC4 (void);
extern void QRCodeEncodeController_RotateMatrixByAnticlockwise_m8CD537ED508315859CDE8D59CF9E5C168D03D988 (void);
extern void QRCodeEncodeController_isContainDigit_mDEEC171256D79D70FAA97F63E488D990D57371BC (void);
extern void QRCodeEncodeController_IsNumAndEnCh_mAEE909841812612733D13D381C134B158A4A270A (void);
extern void QRCodeEncodeController_isContainChar_m95DD94911642993CC01E17455EA4A9735C77D3C2 (void);
extern void QRCodeEncodeController_bAllDigit_mA1BBB3E3EDA7565E5E1A66F6E762270BA55F6F80 (void);
extern void QRCodeEncodeController_AddLogoToQRCode_m0BFB253651F29A6DC771AFAEBDAE09F90B127203 (void);
extern void QRCodeEncodeController__ctor_m5200609EC80999F192C8F4F2261D3AB2AF0012B0 (void);
extern void UnityEventTexture2D__ctor_m8C2F0968F8A990154B6C0BAC24EA0CF0976FA0AE (void);
extern void QRDecodeTest_Start_m37E114609E5330581C576A3B01F4B5DC23DC8B58 (void);
extern void QRDecodeTest_Update_mEF7AF0BAB9DD1448EBF3074C7E9F9320BFBD5CC1 (void);
extern void QRDecodeTest_qrScanFinished_mE24B09A074FB3D3E8D70E5278A3D98543AF82308 (void);
extern void QRDecodeTest_Reset_m1EC8A6F3D239AE2CAAF053372A2CE439827BA227 (void);
extern void QRDecodeTest_Play_mF83F609B3351856B6D3D0A0784937C93BFE50988 (void);
extern void QRDecodeTest_Stop_m948323E237EC32E96818B99DD64D56E8A715AB06 (void);
extern void QRDecodeTest_GotoNextScene_m2878E319634507C099D47CAA08B63CC762C80819 (void);
extern void QRDecodeTest__ctor_m50400EF2070B4019124FF33227EA35955EC12E01 (void);
extern void QREncodeTest_Start_m787659C2CB9CB37AE5438133A3B33952631AA2DA (void);
extern void QREncodeTest_Update_m5FF8D66E55A59F5EE4C90D1E2E76DF0825F6B720 (void);
extern void QREncodeTest_qrEncodeFinished_mF367B757C50DF236422D734E1310E3FAF63C614A (void);
extern void QREncodeTest_setCodeType_m3B19477466F32960015E0B4F8B9AD6EB02D976CE (void);
extern void QREncodeTest_Encode_mCD28EDD1045DE0CC5127975063078480409D4200 (void);
extern void QREncodeTest_ClearCode_m5666F37D7D9BF7A2ACA3F403C1EEA2B07A539868 (void);
extern void QREncodeTest_SaveCode_mEF4032A351EAE0B8E41602E89248D906123E6F14 (void);
extern void QREncodeTest_GotoNextScene_m456AE362721FCE64321BE2E21E98B909F6884794 (void);
extern void QREncodeTest__ctor_m27903FEDDE86DAA760A9A404787DAE0C46D314E1 (void);
extern void TextureScale_Point_m2AE3872E9E41EDA1C80F3621DB02CDDDC37A16E9 (void);
extern void TextureScale_Bilinear_m46D3FAF17B036C221322852995D13D65B93AD9A7 (void);
extern void TextureScale_ThreadedScale_mCAFF7C32104EB62501C535FC53DAE5C9C3A11BB1 (void);
extern void TextureScale_BilinearScale_m22923828DE2338530B8F46A6364FAA89BAB2C92F (void);
extern void TextureScale_PointScale_mFFA5765213B531DA43626AC5822CA48F72EC07D1 (void);
extern void TextureScale_ColorLerpUnclamped_m1E513C5098D6689AC1C66818609B160416F01512 (void);
extern void TextureScale__ctor_m58F73B7E18FAEC7656C4EFBBCEFA2D5E06297906 (void);
extern void ThreadData__ctor_m871190A9851FF9A4E07BBBAD8F2F6CB87CD185B2 (void);
extern void Utility_CheckIsUrlFormat_m53211E48EF5C7428F166768B4EC4E9FB9CA7A19D (void);
extern void Utility_CheckIsFormat_mE7967AC10F87F3971146F9FCC99A469C8EC423AD (void);
extern void Utility__ctor_mDE968B2BF99D305E4E010E70EBD998699B0C7410 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mBEB95BEB954BB63E9710BBC7AD5E78C4CB0A0033 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mE70FB23ACC1EA12ABC948AA22C2E78B2D0AA39B1 (void);
extern void MyClass__ctor_m542849D89902B6DCA2385356B7959E0CA6ED1B23 (void);
extern void EasyWebCamAndroid_get_previewWidth_m67C62820B6C21DCF51EFB3D625D4C5D91DA32DC0 (void);
extern void EasyWebCamAndroid_set_previewWidth_m4A52CE1B9037CA9CBBBCDEBDBAD77C2DA3137811 (void);
extern void EasyWebCamAndroid_get_previewHeight_m7CBEF0DD54F4F5FA153F5A6983C74DCF84B80018 (void);
extern void EasyWebCamAndroid_set_previewHeight_m837E5A38699A47E1B28B5656EAF9D124892A4E90 (void);
extern void EasyWebCamAndroid_get_IsPlaying_m4C222B17AE449EC443124D824E345C4A0FD83DAF (void);
extern void EasyWebCamAndroid_get_WebCamPreview_m8F0E613D72ABCBB58DDDE04010D3CA4462F1BD3F (void);
extern void EasyWebCamAndroid__ctor_mB6A51CFBA08AF508CA2A609E4E68ECFFBB5F751F (void);
extern void EasyWebCamAndroid_setPreviewResolution_mF1D98788A252AEA8AD0E3F8F8249D90022C231AC (void);
extern void EasyWebCamAndroid_Play_m43C21D985F8ADCDB300045058B74AB98D5E774F0 (void);
extern void EasyWebCamAndroid_Init_m9DB299FD520B1B0C9B5861C3F8B3C7EF03C97FEB (void);
extern void EasyWebCamAndroid_Stop_m6B5FA52F15512938D973C86E500987F4F479FAA4 (void);
extern void EasyWebCamAndroid_setFocusMode_m6AB3D2DC5CA56A9BB12D76735AB320F8046D8746 (void);
extern void EasyWebCamAndroid_setFlashMode_m32F573CFDAA962A28C8420401E8065F183991AA9 (void);
extern void EasyWebCamAndroid_setTorchMode_m919D1128803EF4020FABB907A113A9DBE884389C (void);
extern void EasyWebCamAndroid_tapFocus_m80779DC55FB9EB6603DEE148FE9B5F272F34A613 (void);
extern void EasyWebCamAndroid_TakePhoto_mF0E8CE7EB720AA81EF5B5B1B2BAAA891D7AA7AED (void);
extern void EasyWebCamAndroid_OnPause_m6E6B7FDECEC4E090B852950F26B7F548366B72B1 (void);
extern void EasyWebCamAndroid_Release_m99B08F27EDD51EBF72470BC0523DF5B18F4CD27F (void);
extern void EasyWebCamAndroid_SwitchCamera_mE6394DFE3868460004D93265BFDC13E2858A35FD (void);
extern void EasyWebCamAndroid_UpdateImage_m5605488B338EB15DEBFCFA838C9C22E21C83044F (void);
extern void EasyWebCamAndroid_ewcUpdateTexture32_m4E6C61E075D899586F215AF3DE90811C9E2788BF (void);
extern void EasyWebCamAndroid_setOrientation_mE14941ACC5C769B48BAC67B94E21D9D48AEEB376 (void);
extern void EasyWebCamAndroid_getEnterFrame_m63BEA992C5E3B4DCDE301FDFE6C344B3B2B436BF (void);
extern void EasyWebCamAndroid_isCameraUsable_mA700A1F002865151E9B226E28B3B32EFF0E87F53 (void);
extern void EasyWebCamBase_add_OnEasyWebCamStart_m999B968E383190B4F677B967F8A21F7F9B19E0F7 (void);
extern void EasyWebCamBase_remove_OnEasyWebCamStart_mAFE85654AEB3264401C7FFAC4A883A6F8E33573A (void);
extern void EasyWebCamBase_add_OnEasyWebCamUpdate_m9C5EEF5DF21BAD3DDAF659ED189B09EDE6B9B67E (void);
extern void EasyWebCamBase_remove_OnEasyWebCamUpdate_m830439E1BB980928DB7702234418C9934549D5CF (void);
extern void EasyWebCamBase_add_OnEasyWebCamStoped_mFB81AD08067BBE462103C7A0BD2689ACB8DF0062 (void);
extern void EasyWebCamBase_remove_OnEasyWebCamStoped_mC18F2B2186260BDE522019C8A945DFA4E113F973 (void);
extern void EasyWebCamBase_EasyWebCamStarted_m51612CECBB0E65406A385FAEC9459359BCA2EA67 (void);
extern void EasyWebCamBase_EasyWebCamUpdate_m9AB6AA793FDD7CDFBFFC802290BF145C37B23A05 (void);
extern void EasyWebCamBase_EasyWebCamStoped_mA95AEC189C7F85009572FFEE5153B99E0D53AC1C (void);
extern void EasyWebCamiOS_get_previewWidth_m16D2DA56E74FFFCCF221612833448A9DD26F2C33 (void);
extern void EasyWebCamiOS_set_previewWidth_m94D2FFC65AFF256A733CA411B3AA7E95B8DE0A36 (void);
extern void EasyWebCamiOS_get_previewHeight_mE04AB0CEB55A350C7C79EF882C319462365EBDC4 (void);
extern void EasyWebCamiOS_set_previewHeight_mE876506E701867D34B42644617B218185685906D (void);
extern void EasyWebCamiOS_get_IsPlaying_m7A3CAED25126FF6DBD2676D1832993BA698864E6 (void);
extern void EasyWebCamiOS_get_WebCamPreview_mBCC797FD82092E0687EB78819394AA552E937E1A (void);
extern void EasyWebCamiOS__ctor_m4EEEF348EA03A8A06BAE1900790ADBAC917135DB (void);
extern void EasyWebCamiOS_setPreviewResolution_m467A294DCD5F1AED00E86E642371F369075CB245 (void);
extern void EasyWebCamiOS_Play_m0693AC7CE4A90F9498F1EAC4EB1D35F2CBB16800 (void);
extern void EasyWebCamiOS_Init_m031E5ECED8E71A97B1BA43660F802B1CB1BCCFA6 (void);
extern void EasyWebCamiOS_Stop_m948FD800CCE9DDA068FE85D0B86ADC4A05A1255D (void);
extern void EasyWebCamiOS_setFocusMode_m1CF08AD4B7A68BC8F2E78CA78AF88F166150E664 (void);
extern void EasyWebCamiOS_setFlashMode_mF3C4DC54B2119405474E667463EAF58886436941 (void);
extern void EasyWebCamiOS_setTorchMode_m787533F19D09F2DC3D9FE13CC3CDF9DD478AC88C (void);
extern void EasyWebCamiOS_TakePhoto_mC48F3A5851A1D9988BAB3C9008B2C0F0B5A5C52B (void);
extern void EasyWebCamiOS_OnPause_m02D846C8A34451C6F2755303066DCCD6A9E10291 (void);
extern void EasyWebCamiOS_Release_m41DD88D0476FB8162E0B94909B089E35950342A3 (void);
extern void EasyWebCamiOS_UpdateImage_m37E1E8C177E0E897D074DA4F354F6386FC678CEE (void);
extern void EasyWebCamiOS_getEnterFrame_m88BD3146E9D8CE22450ACE3CC0E6C3B371457816 (void);
extern void EasyWebCamiOS_isCameraUsable_mDDE0916FE2988B3EA450D754E8A2FAF45B1EADBA (void);
extern void EasyWebCamiOS_SwitchCamera_mC39F072CE8C0533E96ECFACE78B2C22726249772 (void);
extern void EasyWebCamiOS_tapFocus_m68CC0AF5956D65AB915E509F3CBA5492EB4E2905 (void);
extern void EasyWebCamiOS_setOrientation_mE70C30D5F311FA57A77FC855A8E268E14FA8AD3E (void);
extern void Utilities_Dimensions_m1FC035A9F95036E33CAE8D4E0F1051EDD73C35EA (void);
extern void EasyWebCam_add_OnEasyWebCamStart_mE0558AD4819C898744CD038251C5BC79479ACB04 (void);
extern void EasyWebCam_remove_OnEasyWebCamStart_m4BB2719FD978DF0E3D35D79AD78C8C86CFBBE287 (void);
extern void EasyWebCam_add_OnEasyWebCamUpdate_mBC4AD648B5D235D86E8763ACAF6A54347CAA240C (void);
extern void EasyWebCam_remove_OnEasyWebCamUpdate_m81AE31219CA5E76E8FBBF7B7D90595112B45E585 (void);
extern void EasyWebCam_add_OnEasyWebCamStoped_m60770CD567276528F542E1DE2587A9CB1983B03A (void);
extern void EasyWebCam_remove_OnEasyWebCamStoped_m04F0C61EA4BD52E6C8AF1BFB71F1F02AC2E237EB (void);
extern void EasyWebCam_get_WebCamPreview_mD65335B49D6617EB0380583107EFC7BB3AA8AE38 (void);
extern void EasyWebCam__ctor_m17FAFCDFD01A9F2E45B693170356387EFD8914FE (void);
extern void EasyWebCam_Awake_mB1422156816C4350D72226D3B776329CD053A446 (void);
extern void EasyWebCam_Start_m1A528370C66FF74D4D95ADBFC5FA44124DD469C2 (void);
extern void EasyWebCam_Update_mD7B2E6E07C13F5591651BD593F4AC82A54003345 (void);
extern void EasyWebCam_Play_m42DCDB8BB061F87D8DE224027379EC3CC8AA9676 (void);
extern void EasyWebCam_Stop_mAA236F0FEB84FFF6363AB8F9E3531FD8A3D2B594 (void);
extern void EasyWebCam_setPreviewResolution_mDDD8C930AA899CD7B319B2E07836DEFAC83509F6 (void);
extern void EasyWebCam_TakePhoto_mEB82A9E5BDE3F7BA65F5D2508E36106EF415067B (void);
extern void EasyWebCam_setFocusMode_m013C22F037D9902D83931D902C3968DF3A60D41F (void);
extern void EasyWebCam_tapFocus_m5CCBA23FD9EB55AEF8E0D5DEF5D8938784CE342D (void);
extern void EasyWebCam_setFlashMode_m0B3B24946DB44CBD59CDDD4CA998D16B2591CCC5 (void);
extern void EasyWebCam_setTorchMode_mB3608C390572D32F2AD828FF8BA7A52C2807C31F (void);
extern void EasyWebCam_SwitchCamera_m829A7AA3602C6D43DAB9870A043458D79CA900F5 (void);
extern void EasyWebCam_Width_m9B8115BB90E64918DACB079140CD7524A4656FDF (void);
extern void EasyWebCam_Height_m6709BA9C72C614B7009DFBFA7E65466E07B82E0D (void);
extern void EasyWebCam_getFrame_m051D742726BC5B9DDD6F66A44296BA87651251B8 (void);
extern void EasyWebCam_OnPause_m5ABF2156FFC125DDBA77CBC92A6B639C7812157E (void);
extern void EasyWebCam_OnRelease_mF3EC81A3A0C14B1C7FFF5E9223777577E3B76340 (void);
extern void EasyWebCam_Release_mE01DEC7089287322F6313FD9252E01C0278F169D (void);
extern void EasyWebCam_RequestEasyWebCamInterface_mDF24831BF3D6974684BA7A10A4C93BDAD0156DFC (void);
extern void EasyWebCam_isDoubleClick_m79FE2264D207FE6D5350B390ED0C3C45055AC323 (void);
extern void EasyWebCam_EasyWebCamStarted_mF86470DC83480135FC078902AD5B0DB32D83211B (void);
extern void EasyWebCam_EasyWebCamUpdate_mAD7552D5C00FD7A727B2D3D1AA4AD1C647969697 (void);
extern void EasyWebCam_EasyWebCamStoped_mC7965D4AC5E972AFD8783EE470133E81899DD51D (void);
extern void EasyWebCam_OnApplicationPause_mE7BFB3A43050FF419EDDC8D536DE949556F61D4E (void);
extern void EasyWebCam_OnApplicationQuit_mD38274104F2C47FE1C67F7A679FA77CB0C7BFFE0 (void);
extern void EasyWebCam_OnDestroy_m5843214EEFCBB1F3D3976F708F7DC9A9DCF99D17 (void);
extern void EasyWebCam__cctor_m62E0AA2AFEE00B878CA4891CCB79CAEE9DE4A545 (void);
extern void EasyWebCamCheckHelper_Awake_m6D57717D1604AA0792AC7B4C60F7B82F84005BBA (void);
extern void EasyWebCamCheckHelper__ctor_m83741A847A8CEDE4BD339559CBDCDA761EC607B7 (void);
extern void U3CU3Ec__cctor_m4984EC36298A5B11910927200AFB2CA6FF547E50 (void);
extern void U3CU3Ec__ctor_mD8F5C32DB6A0D8BE046ABAFBC9C5647F164DDAEB (void);
extern void U3CU3Ec_U3CRequestEasyWebCamInterfaceU3Eb__38_1_mA51AF69822B9810C1EC4DAE7FBAEC038D81995B4 (void);
extern void U3CU3Ec__DisplayClass38_0__ctor_m602D4E866B2924C00FEDE17FD0F5508D523B1587 (void);
extern void U3CU3Ec__DisplayClass38_0_U3CRequestEasyWebCamInterfaceU3Eg__RequestEasyWebCamActiveU7C0_m9180334E6065A8FF8FECD20398334628611D3055 (void);
extern void U3CU3CRequestEasyWebCamInterfaceU3Eg__RequestEasyWebCamActiveU7C0U3Ed__ctor_m1DE251DBD4EB3F21C283F04A3A94A3F792B82B35 (void);
extern void U3CU3CRequestEasyWebCamInterfaceU3Eg__RequestEasyWebCamActiveU7C0U3Ed_System_IDisposable_Dispose_m20058E11E8EC49C2BB330CEBCD89CA9B52B0837E (void);
extern void U3CU3CRequestEasyWebCamInterfaceU3Eg__RequestEasyWebCamActiveU7C0U3Ed_MoveNext_mB21AB243AA28C7BD4F9FB63AF573D1F1E113F83D (void);
extern void U3CU3CRequestEasyWebCamInterfaceU3Eg__RequestEasyWebCamActiveU7C0U3Ed_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mD1A7A364224BB0DC9A9FDB69332A158E6B444427 (void);
extern void U3CU3CRequestEasyWebCamInterfaceU3Eg__RequestEasyWebCamActiveU7C0U3Ed_System_Collections_IEnumerator_Reset_mB7ABB114FF6493B6CF6726776F0468DFF87ADC5C (void);
extern void U3CU3CRequestEasyWebCamInterfaceU3Eg__RequestEasyWebCamActiveU7C0U3Ed_System_Collections_IEnumerator_get_Current_m3F3AAD21C4826702DE5B9AA6CA886DC3B1F56AFF (void);
extern void U3CAwakeU3Ed__20_MoveNext_mC37ADC8C7359494CD3536931E4A388DF84662E95 (void);
extern void U3CAwakeU3Ed__20_SetStateMachine_m52D92CECABC76F7C860B13A7392FEB181F4CF22F (void);
extern void U3CPlayU3Ed__23_MoveNext_mB51DE6F7B174831FFF628567D2AA190836AF578B (void);
extern void U3CPlayU3Ed__23_SetStateMachine_m62C3CA53B818A01BDEB85FDAA51770E7E013A7C2 (void);
extern void EasyWebCamStartedDelegate__ctor_m7CF2FF48DD1583C9A796C4B51FF0DFB84FDE1EE6 (void);
extern void EasyWebCamStartedDelegate_Invoke_mE3549CBA4A29B721884A06585AB39C569A8F48CD (void);
extern void EasyWebCamStartedDelegate_BeginInvoke_mD095B69520DA61E2BA8CF82CE96A98258B18D5F8 (void);
extern void EasyWebCamStartedDelegate_EndInvoke_mEB3C4E38783FBA09BE2E5F47632BBF143B45A7EA (void);
extern void EasyWebCamStopedDelegate__ctor_m66A0E2F9446B6D09E9F697FD23714FED66995430 (void);
extern void EasyWebCamStopedDelegate_Invoke_mC26F20362D6236CF7762148063553C22CA2DCC3E (void);
extern void EasyWebCamStopedDelegate_BeginInvoke_m99C1A8F289F76FD9588450D863A79ADA31793A85 (void);
extern void EasyWebCamStopedDelegate_EndInvoke_mDDC0AED6CD02537F6A454A5D8C9E2D4BEC29137D (void);
extern void EasyWebCamUpdateDelegate__ctor_mE8216630017A009A44ED27AF264601C699D6B03B (void);
extern void EasyWebCamUpdateDelegate_Invoke_m059942CAEDD2ABB85076002C8824DF0FAE5F802B (void);
extern void EasyWebCamUpdateDelegate_BeginInvoke_m73008AB6AEBFFD724DC0DFD71AD063E49CF8DFF0 (void);
extern void EasyWebCamUpdateDelegate_EndInvoke_m81DF24BE194BA607E1B7A8F14A53201A0190AED8 (void);
extern void AnimationPlayer_Awake_m176ED1A61C4B8D6DC9A859F9DAED4234F1961523 (void);
extern void AnimationPlayer_PlayAnimation_m5EACC3194501CC65ADEC4A7CE9ACFF79A3A90556 (void);
extern void AnimationPlayer_PlayAnimationWithCallback_m1F7B9E3CA11C3FE503526B25AE58E398000ED4A1 (void);
extern void AnimationPlayer_AnimationExists_mE4CECAF81230628BD76F98A4D910102D46F6097D (void);
extern void AnimationPlayer_SetTrigger_mA68F5742A4583DEA64A3C98079D5EA105618FD5A (void);
extern void AnimationPlayer_SetBool_m23C6CDC5A6EE1EEF54D54D30C151ABCF2C95A69B (void);
extern void AnimationPlayer_SetFloat_m70EE7E3254013006AFAF0EB640B074EB8E061EB5 (void);
extern void AnimationPlayer_SetInteger_m7835C4E3562681A69A80970879EAB251D2737EB7 (void);
extern void AnimationPlayer_WaitForAnimationComplete_m5AFD69BD0CE5D696A9A3A3F2CC78287B95D3485F (void);
extern void AnimationPlayer__ctor_mA3875A2CA1374D1AF010C2435ACA93E909829137 (void);
extern void U3CWaitForAnimationCompleteU3Ed__9__ctor_m1CE65015917B1FFC2D92F09BBC7B3B608041B802 (void);
extern void U3CWaitForAnimationCompleteU3Ed__9_System_IDisposable_Dispose_mA4413AAF7399D83F3829F8EB87771677940F448A (void);
extern void U3CWaitForAnimationCompleteU3Ed__9_MoveNext_mC999DDA0C87748CA2F445942C11EA7130C0528C7 (void);
extern void U3CWaitForAnimationCompleteU3Ed__9_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mC0A306C9D9DC247BD27F7F0293F98DC2C71C21C7 (void);
extern void U3CWaitForAnimationCompleteU3Ed__9_System_Collections_IEnumerator_Reset_mF036C035A5C1BEF23F3F66A9F7AEE587BCA770CF (void);
extern void U3CWaitForAnimationCompleteU3Ed__9_System_Collections_IEnumerator_get_Current_mB4864375E61BAFEB1A48EA6A40F7A89C879182BE (void);
static Il2CppMethodPointer s_methodPointers[478] = 
{
	AnimController_OnEnable_m496E64AAC29A813F2AF9A6773EFE4A9D7A0D50E7,
	AnimController_OnDisable_m82ED81C15AABF38626BF43945E1A1E61D7A84345,
	AnimController_Start_m9D6164B6CE37DECBD114FDE08915ADFAE46B5EB4,
	AnimController_StartKinAnim_m6092D6256423D40C252FDA98F688922754D2DCB6,
	AnimController_DelayedStartCoroutine_m93F7514F0DE5605FD73984041F4D3BF931FCE06D,
	AnimController_CallFunctionsSequentially_m95C3344D56A4482FF7247CF75861FDED8CF92540,
	AnimController_CallFunctionWithDelay_m62C8C5E1BB0340B1A4144937BCEC25F76425B430,
	AnimController_TurnOnShockWave_m5CED8C3A09F55BC1A2353381AB99725856F62280,
	AnimController_TurnOffGrenalDer1_m490D7E7BE39F6E0F7531857E685CBD2F51C73ED0,
	AnimController_TurnOffGrenalDer2_mE8F23051CD33826C60CAEAE70DBF70E49BE059E2,
	AnimController_TurnOffKidneysCube22_m9093E9763903D0718F571D038445B515BBB782FC,
	AnimController_TurnOffKidneyArteria_m00765D407DC2D6F9967474EC40F5BDD4620DDB60,
	AnimController_TurnOffKidneyVena_m6AF7729DEF9898500438BF58AD7EF7C1FDE172FC,
	AnimController_TurnOffkidneysCompleteLeftFront_mE4FA6FEE3A9BCE46480DED2DEE5EEA9E34158B31,
	AnimController__ctor_m243F834C40EE4C5E658EAB4A6EEC37C010547D19,
	U3CCallFunctionWithDelayU3Ed__21__ctor_m784A4E67CC5A5047E7A96C0777548FC39C38CF37,
	U3CCallFunctionWithDelayU3Ed__21_System_IDisposable_Dispose_mF2F56111B02E65D63815E6601F83517B7805CCBE,
	U3CCallFunctionWithDelayU3Ed__21_MoveNext_m5CC541D5C29F01AFA4C3E90E7B7425EA41A36E29,
	U3CCallFunctionWithDelayU3Ed__21_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m4E470F93FF3AB2239EF515B58036C3CBD479B8AD,
	U3CCallFunctionWithDelayU3Ed__21_System_Collections_IEnumerator_Reset_m3863163F26D591B9A90584F67FAEE45F90ED3BD0,
	U3CCallFunctionWithDelayU3Ed__21_System_Collections_IEnumerator_get_Current_m3D5110010B75079B53E9F594D32BC64B2D96D2B8,
	U3CCallFunctionsSequentiallyU3Ed__20__ctor_mECA9A8828AC9904EFB2B12D960648A9705CC422B,
	U3CCallFunctionsSequentiallyU3Ed__20_System_IDisposable_Dispose_m636E14C098F57E298168125C5F72C777B40F7C34,
	U3CCallFunctionsSequentiallyU3Ed__20_MoveNext_m2E306BB008189576F988C98CBF1528CFF1CC8EE4,
	U3CCallFunctionsSequentiallyU3Ed__20_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m20BA6E58506006CCA75166AFFC6BB6046DDE130D,
	U3CCallFunctionsSequentiallyU3Ed__20_System_Collections_IEnumerator_Reset_m4C86B6BCB5CADB0D45C20DE5FCD23117544C30BC,
	U3CCallFunctionsSequentiallyU3Ed__20_System_Collections_IEnumerator_get_Current_m1F20A403D0A9F18EE9779AAF0488C260A5B22AD7,
	ARCursor_Start_mF861EF07BBBDEA10481F7F4AC557740AE0B182B1,
	ARCursor_Update_m156A232086B1784C1125D3A9F1141FEE78055443,
	ARCursor_UpdateCursor_mF10A97A53652C882FD9FFEE7DD963F3990861CDA,
	ARCursor__ctor_m137F0AE7E4CBAB2E1D72B1ED04AEC5E1315980EB,
	AudioPlayer_Start_m1CD4C540B92CF134746EF50084C5729768B93CD8,
	AudioPlayer_PlaySound_mF515D08985D1C0C7D284EECE40FC2B9E3CDFD279,
	AudioPlayer_PlaySound_mB6036E595DE3D133353BB037F6D4FCE0B4CF41D9,
	AudioPlayer_PlaySoundAtPosition_mFF76BE97DA63E8BED0CC779CEEBBE6867BDBF837,
	AudioPlayer__ctor_m3F81E1F07C97F2ECE2A837B3FB918ECBBCA14B17,
	FaceCamera_Start_m36C189451F380B7FA03494B1466276854CF49FBD,
	FaceCamera_Update_m295E828F20DC5F8CADC64934C989C94F27592CA3,
	FaceCamera__ctor_mC5D44A53DFAE40B1103AEBB2A513D3CE453AE25A,
	MaterialAlphaAnimator_AnimateMaterialAlpha_mCED510BA73F43C0247384F649D9DA7B9CDE2E9C2,
	MaterialAlphaAnimator_AnimateMaterialAlphaRoutine_mDDC67BF3B8CCF9800ACDC215692C5315E0C236DB,
	MaterialAlphaAnimator__ctor_m57AC72D6D777DBF6FCFFA713B693221FF45D4C0C,
	U3CAnimateMaterialAlphaRoutineU3Ed__1__ctor_m171F781AC97D8A4F5B6307873B0C0D2DDC5BF8C1,
	U3CAnimateMaterialAlphaRoutineU3Ed__1_System_IDisposable_Dispose_mD18190D85743AD88FDD78A3471B64F171E468395,
	U3CAnimateMaterialAlphaRoutineU3Ed__1_MoveNext_mB6B5D16CC0CE4155F7E0C9B5FB65B2D223854AF8,
	U3CAnimateMaterialAlphaRoutineU3Ed__1_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m8A7BBA5B45C765CF3370FE9D07924CF62D05E4FF,
	U3CAnimateMaterialAlphaRoutineU3Ed__1_System_Collections_IEnumerator_Reset_mB26CA06F43A43395E476FA2D9DC011F91524D63F,
	U3CAnimateMaterialAlphaRoutineU3Ed__1_System_Collections_IEnumerator_get_Current_mCB775FBE0CA8F490D129ACF1CCFE4CCFBB6534FA,
	NeverSleepCode_Start_mEDB1BAB920DE0F6C104E4286207423FF9D499A92,
	NeverSleepCode__ctor_m10C4F406259693E7AA8673945881401EE4E1B44E,
	ObjectAnimator_Start_m1848FD6B2C1E5EF858B9501DF165FEF6907FAAF3,
	ObjectAnimator_StartAnimation_mF6FA57605E8FC7D1E61217D47B8BE56E977AC493,
	ObjectAnimator_ReturnToStart_m979D3EB5378646FE50074E6329AD254C0F97C55D,
	ObjectAnimator_AnimateObject_mB98749B8DC526EFA39D10855840639B7CF662933,
	ObjectAnimator_SkipToTarget_mB547F59970965A127C30DDB4E547BEFC63D16596,
	ObjectAnimator_SkipToStart_m4D40EDB9E772936ADA8108789B1FF91F106272FD,
	ObjectAnimator_SetAnimationDistance_m7088E82696D4A36517DEEA22FA3AAEE29E17A7B0,
	ObjectAnimator_SetAnimationDuration_m56F7608C7E1DA453C9F5C47079D01E29074DCD43,
	ObjectAnimator__ctor_m9D8357561F77936D80D6465A4B918CBB2D161428,
	U3CAnimateObjectU3Ed__12__ctor_m1C7FA9BBAA3B55C5B861CFB9D8059F49682C37F2,
	U3CAnimateObjectU3Ed__12_System_IDisposable_Dispose_mF41081A9EF20441B6BDF12E498320FBA1DAEE0B9,
	U3CAnimateObjectU3Ed__12_MoveNext_m823D876FF2CDC4CFDBC5E722AB10EDE77C933819,
	U3CAnimateObjectU3Ed__12_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m2D598265D61AAA0E589B25365EA9523456BC27A1,
	U3CAnimateObjectU3Ed__12_System_Collections_IEnumerator_Reset_mF5DA63BFA4A213643AF389F3B5CDBE55D5F49405,
	U3CAnimateObjectU3Ed__12_System_Collections_IEnumerator_get_Current_m4218B2E5D2DD4D3832390B8F88D25D5ED71D585D,
	PressStartContoller_Awake_m4268AF40B5F271949AE25EAC55A77CD1C356BD47,
	PressStartContoller_ButtonPressToStart_mD992EB65B23CDA85AC9EFA5CE60C15FC82B78E46,
	PressStartContoller_Update_mB125E658B7953A1684123945A18E680C57F6F420,
	PressStartContoller__ctor_m1F4DB263ACAB82839CC84F00ACD4CC6588BE070D,
	PressStartContoller__cctor_mAFC01F54EBA39B6203BC742058D1788D32E603F8,
	PressStartContollerNew_Start_m4CABA0658129063B33D3564806591399D619A201,
	PressStartContollerNew_Update_m99E8036055D80A18CD4843D265BAE97CACBD6D3F,
	PressStartContollerNew_PressPlayButton_m7BE6F5E64155F9DAD7ED89AA1C3C38AFB34AA009,
	PressStartContollerNew_PressPauseButton_m57AA11DCD019CBD2A8BDFFA91DF46B6836883869,
	PressStartContollerNew_PressReplayButton_m9824F028F5C3F54DCDB5BE094DD8C50F65F33D42,
	PressStartContollerNew__ctor_mDD9239304F4D95B57CA059BDEEF3F3436F18C69F,
	PressStartContollerNew__cctor_m5B03A431AF21D522A4EFDD060ADC4EC54E4D9303,
	QRCodeContoller_ResetScanner_m27297DD9FD7A26B35FF6CBA976B7234B5588E73B,
	QRCodeContoller_QrCallback_mBB7D5597715E3843179ABA6631F43943AAAA447D,
	QRCodeContoller_GetRequest_m928B51BCE2D98C096E74744EE24357DA5A720810,
	QRCodeContoller__ctor_mF29138FC93834033AAC0F2CF58B0EE5CA51EFFCD,
	U3CGetRequestU3Ed__5__ctor_m3FF28BAADDEBA107974B07F4CC0816C9AE88292B,
	U3CGetRequestU3Ed__5_System_IDisposable_Dispose_m638A821E41082E680B1070E01B504BCBAD9657D9,
	U3CGetRequestU3Ed__5_MoveNext_m6AD918BEEC6E70B8C214DD4CCDE72577B55A2ADF,
	U3CGetRequestU3Ed__5_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mFF8CB067EED56D8AF7D0832800B5E4A9321C4011,
	U3CGetRequestU3Ed__5_System_Collections_IEnumerator_Reset_m81540B8B2BF4CA1C57693412ECA43841FD2699F4,
	U3CGetRequestU3Ed__5_System_Collections_IEnumerator_get_Current_m97CF7DC2ADAEE6BE07661A4EBFD798C2EB144170,
	TimelimeControllerNew_OnEnable_m624E18A261527976FCAAA758D364211574A582E1,
	TimelimeControllerNew_OnDisable_m0EDBBCD1DC2752CE4F6062DD7EC94A95594566AA,
	TimelimeControllerNew_StartTimeline_m4A25D1C3193F5614BCB8B9BAF8DECBA312913E26,
	TimelimeControllerNew_ReplayTimeline_m04B9B216A30C1FB0C7E90AA8FE5A05D8981EB873,
	TimelimeControllerNew__ctor_m6379FA842B86A766AFB89812F4B509515C34C421,
	CameraSetting_RequestCameraPermissions_m103B16EC4C29D9FC1AC552D7F7A253438F5A6E0F,
	CameraSetting__ctor_m74657D44311DA4D0B0EB7E7219E1C904A54513A2,
	CameraPermissionHelper_Awake_m72008FDB1DD2ED54E80738F0A17FB350E1FB4370,
	CameraPermissionHelper__ctor_m962448E8715123ED6C27E9FDE6B0FF0F148CCE6C,
	U3CU3Ec__DisplayClass0_0__ctor_mD680524965EEF36617ABCC1C24112C4C56CFC0BA,
	U3CU3Ec__DisplayClass0_0_U3CRequestCameraPermissionsU3Eg__RequestAndroidU7C0_m482ECF6DA3A4ED9782EAC124A376E1B01EED8971,
	U3CU3CRequestCameraPermissionsU3Eg__RequestAndroidU7C0U3Ed__ctor_m3C619D0DE60FFEE0FB7BB38F6098754D9D3D3117,
	U3CU3CRequestCameraPermissionsU3Eg__RequestAndroidU7C0U3Ed_System_IDisposable_Dispose_m07620BAC53AA86D4AB0EB254E066343007B77AFF,
	U3CU3CRequestCameraPermissionsU3Eg__RequestAndroidU7C0U3Ed_MoveNext_mD4875593F241A01F832E8DB80140346AB9506EE4,
	U3CU3CRequestCameraPermissionsU3Eg__RequestAndroidU7C0U3Ed_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m601CFBD3514CAED84FBF71B7DD1A7F8617A12C41,
	U3CU3CRequestCameraPermissionsU3Eg__RequestAndroidU7C0U3Ed_System_Collections_IEnumerator_Reset_mCE3C3548F4A692DCDD70ABAEC4964840AB43CBBE,
	U3CU3CRequestCameraPermissionsU3Eg__RequestAndroidU7C0U3Ed_System_Collections_IEnumerator_get_Current_mD978663B6FC8070F5EF09B0B9912CAA1EBAD6427,
	U3CU3Ec__DisplayClass0_1__ctor_m24BA8B5C8DBE557B204CFFCE1C8819B4F6324828,
	U3CU3Ec__DisplayClass0_1_U3CRequestCameraPermissionsU3Eb__1_m1E138A090711CC25CC99ACDD10FB5C41C4B14327,
	EasyWebCamUnity_get_WebCamPreview_m13F1DED348CABAB5D5E3BBB3E27127AB1F7E2ED6,
	EasyWebCamUnity_get_previewWidth_m26C5BF432B65C8DC2E0DD7BE3675352B029F13F8,
	EasyWebCamUnity_set_previewWidth_mB7445C9047FBBBDAF5FF627AEEF572DD561EB43A,
	EasyWebCamUnity_get_previewHeight_m8DB70FD1618B24FC6A66C5F3EE701D47E836A79C,
	EasyWebCamUnity_set_previewHeight_m25F7B125F15B44F1D03DDC0BB55E8EC99E51728A,
	EasyWebCamUnity_get_IsPlaying_mF39AEE4B229B984A10321E43ACD8DF0B8851C813,
	EasyWebCamUnity__ctor_m1FD0F33DF61A3E8B9F62F0CABFA01C322BDED735,
	EasyWebCamUnity_getEnterFrame_mCAF395651B48E6231BEB7ACFC453B51EEA2C5818,
	EasyWebCamUnity_Init_m5276653A00CDED191DF285C5294A1976ADD79931,
	EasyWebCamUnity_isCameraUsable_m8EDE7AC1536E57E01146DE1E8009BC6A55993E9B,
	EasyWebCamUnity_OnPause_m80C620B0AC3369BA33C508BC6EA272EFDB6B831F,
	EasyWebCamUnity_Play_m1B0A91AB06E1AD0D6557DC1C6DB052AB53A6D34D,
	EasyWebCamUnity_waitimeToPlay_mBDC86DD2FC713D8CFCB08305E6C132ABF19166CD,
	EasyWebCamUnity_Stop_m941EF8F176E673F0C7E34A6AAA226BCFA382C996,
	EasyWebCamUnity_Release_mECFBD944B4182434CCBF272CBC432F7FB5FC5387,
	EasyWebCamUnity_setFlashMode_m8F35EBDE71C2FB825BEACCC879F06757753C22B3,
	EasyWebCamUnity_setFocusMode_m3C87DD8FAF687E977D2CFD23931DDD12158FD4A6,
	EasyWebCamUnity_setOrientation_m47DF951DCCBB622CDE0F18A7AFDA9DBAA21635C9,
	EasyWebCamUnity_setPreviewResolution_mA0C24C872D3B49E5FA17F77ADA82403C72109E55,
	EasyWebCamUnity_initResolution_m183777E116C2818A55520050B19CC47F03396AD6,
	EasyWebCamUnity_requestIosCamera_m57A23087E1D79C23373B5404EB2A7731E6766DA4,
	EasyWebCamUnity_waitCameraInit_mCFB85D31F1F31455FE0AED98F16AC26192C1352E,
	EasyWebCamUnity_waitToolActive_m60287E2CA02BC7FDD988039EDE20146D80AD90FC,
	EasyWebCamUnity_setTorchMode_m60EFC4EF22838C52D75A0B6DD143C0A2F8E58049,
	EasyWebCamUnity_SwitchCamera_m597BF291870DB67EDE752732980C84813A99FDEA,
	EasyWebCamUnity_TakePhoto_mD4D7F36CE9C2960CF1BA6DE3A37BAD9F3F183AC5,
	EasyWebCamUnity_tapFocus_mCFDAB250237AFBF118C2D9F1D1B355FC4224195F,
	EasyWebCamUnity_UpdateImage_mDD33710FCAFD2FD4CF17D9E398140ECC98545D5A,
	EasyWebCamUnity_convertUntiyTexture_m57707603B25E5D3C48C6FAD4371AE0D93B82ED3E,
	EasyWebCamUnity_GetDeviceName_m1FC04A150C3E9524A8F2BAAB2C0719ADA587EB72,
	CameraPermissionHelper__ctor_m760C3A5942F4E913A1945D10D4D0FFFB402D3EC4,
	IosCameraPermissionHelper__ctor_m9C3F4F8BF254F72D9B1715A78329CE11EBAD8AC7,
	ToolActiveCameraPermissionHelper__ctor_m61723129917ECA717EFE3302D1A087B7AD457A88,
	U3CU3Ec__DisplayClass37_0__ctor_mFC317B5FEBE9C829EB0F338B9D66234EEDBF25D6,
	U3CU3Ec__DisplayClass37_0_U3CrequestIosCameraU3Eg__callIosCameraU7C0_mC035B3B515580CB3038560944C9FA739DB54211C,
	U3CU3CrequestIosCameraU3Eg__callIosCameraU7C0U3Ed__ctor_m11551C4F41BE2BA7E8D239BE6E713A2D17BDB5B1,
	U3CU3CrequestIosCameraU3Eg__callIosCameraU7C0U3Ed_System_IDisposable_Dispose_mE1AFBF54092A3C11273AB684ED1736A7E34795DB,
	U3CU3CrequestIosCameraU3Eg__callIosCameraU7C0U3Ed_MoveNext_m49D82476C12176B99FE7D75C824A482EBB12E938,
	U3CU3CrequestIosCameraU3Eg__callIosCameraU7C0U3Ed_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m58539BBD4C2303525BD52C11E5ED6E8823D5A1CF,
	U3CU3CrequestIosCameraU3Eg__callIosCameraU7C0U3Ed_System_Collections_IEnumerator_Reset_mD869C4AA341B5336883DFD0237B51BDBCA8C9316,
	U3CU3CrequestIosCameraU3Eg__callIosCameraU7C0U3Ed_System_Collections_IEnumerator_get_Current_mDEECFB98924B5FD289BD9B974C27632C38D6D9DD,
	U3CU3Ec__DisplayClass38_0__ctor_m85A25A54D8578FBD0B44F0409C398EA6D33762E7,
	U3CU3Ec__DisplayClass38_0_U3CwaitCameraInitU3Eg__RequestInitU7C0_m6BEF386C3B8600A1A16D8F57D8F5F312D1D7FEBC,
	U3CU3CwaitCameraInitU3Eg__RequestInitU7C0U3Ed__ctor_m0954B715D03E6FDDB87DD4C197192589CE8F09AD,
	U3CU3CwaitCameraInitU3Eg__RequestInitU7C0U3Ed_System_IDisposable_Dispose_mB5F77BC65D299136B4841012E7B8452CC67B4CE0,
	U3CU3CwaitCameraInitU3Eg__RequestInitU7C0U3Ed_MoveNext_m50F6EB5738A3BA6F56A4F2FBD633404C31EE7116,
	U3CU3CwaitCameraInitU3Eg__RequestInitU7C0U3Ed_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m5803C1727DFE3F8E66E0DE44E98F18CA71E4A5CA,
	U3CU3CwaitCameraInitU3Eg__RequestInitU7C0U3Ed_System_Collections_IEnumerator_Reset_mA427E86E67970C56C00F66EBFDAC211128AA5949,
	U3CU3CwaitCameraInitU3Eg__RequestInitU7C0U3Ed_System_Collections_IEnumerator_get_Current_m38CBB850CEF2B8ECCDC59D89941B83356A78B8F5,
	U3CU3Ec__DisplayClass39_0__ctor_m3A22CD6FA5B9DEA1DCE8C33877DCE5B041CB2F52,
	U3CU3Ec__DisplayClass39_0_U3CwaitToolActiveU3Eg__waitActiveU7C0_m7F1791568F1D927DDEF4940481D41D99EC060373,
	U3CU3CwaitToolActiveU3Eg__waitActiveU7C0U3Ed__ctor_m9764EFC7A1A021AA93D4B4FAD9BF401BF075C40C,
	U3CU3CwaitToolActiveU3Eg__waitActiveU7C0U3Ed_System_IDisposable_Dispose_m8FE45986A87DAE3C3AFD2762F686E3FFB6606510,
	U3CU3CwaitToolActiveU3Eg__waitActiveU7C0U3Ed_MoveNext_m4DF8B21956F9D40A285DE4A9459C08BF78B7A7C5,
	U3CU3CwaitToolActiveU3Eg__waitActiveU7C0U3Ed_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mA569F35EE555EC9DC7BFDD47EFA461A246CBA2DA,
	U3CU3CwaitToolActiveU3Eg__waitActiveU7C0U3Ed_System_Collections_IEnumerator_Reset_mAF6F5844BA64D42B832986C14BC6676EE80DF0E9,
	U3CU3CwaitToolActiveU3Eg__waitActiveU7C0U3Ed_System_Collections_IEnumerator_get_Current_mC26C184E437C6230C0F743A0788DE83260319394,
	U3CPlayU3Ed__28_MoveNext_m0B80CA253A3DD436B7F7519800733916C991113F,
	U3CPlayU3Ed__28_SetStateMachine_mF04521BD01F999AC71E018A63B9C7EEB354623DD,
	U3CinitResolutionU3Ed__36_MoveNext_mDB77DF9D195C5E3B23DBAA601508FB216B54072A,
	U3CinitResolutionU3Ed__36_SetStateMachine_mBE7C03BAFECFA209A1DB9EBDC29870BFBABED513,
	U3CwaitimeToPlayU3Ed__29__ctor_m72F1CA318C153605A46EC0DF3B6B4EBCC08ECCF4,
	U3CwaitimeToPlayU3Ed__29_System_IDisposable_Dispose_mBDCF537F0CE8BEB3D31767B1D2309A4978037D9C,
	U3CwaitimeToPlayU3Ed__29_MoveNext_m6B199BF0F08E5176934216D1D78F75C4F94C7930,
	U3CwaitimeToPlayU3Ed__29_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m6342318FCF9EE579CF127FB2429CB74723EF3397,
	U3CwaitimeToPlayU3Ed__29_System_Collections_IEnumerator_Reset_mE88C04C2AEA447FA5B493319356F2DC3ED89B2BB,
	U3CwaitimeToPlayU3Ed__29_System_Collections_IEnumerator_get_Current_m1EA6F3A2D5F004FCA51F878D91958A31EDC0F53E,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NativePlugin_Init_m5BA932D39DB178DBC215562C1E43A3B22B514C38,
	NativePlugin_Close_mB8D29DC463D5ACFCBB902E5BB995BE20CE59E9C1,
	NativePlugin_ewcUpdateTextureGL_m907FF8A22C526424FC39CF7B575B49D33E3D35BF,
	NativePlugin_ewcUpdateTexture32_m61C3117BB56062A9AC78C7C042B99EB60F70FBF5,
	NativePlugin_ewcUpdateTexture_m420491ADF2041ACB2CB16B0CCD7035195DE94D7C,
	NativePlugin_updateParam_m4F62F44F7BC5AB694BCF6F8304BADD1FF01E7867,
	NativePlugin_addFloat_mA67E53AE0F3099D592F1DC56CE4AE13A1D3DF71F,
	NativePlugin_getHeight_m5B494DA802C7DA3A7AF00BBBACEF2C72255CB871,
	NativePlugin_getWidth_mEFE9102B993FA618D3FA2F760653E09814D05F74,
	NativePlugin_getTestSize_mA03C7CA35638C0C63A23FCA659D9F0A9AEDC58FA,
	NativePlugin_StartRunning_m01C5930D371BE92011D4996E6F176287A8235B01,
	NativePlugin_StopRunning_m7AF7DB9AF648702576D6D6A6C5EA2B57A9C9ACCB,
	NativePlugin_RegisterCallbacks_m7C6D0C3FA87200BDD16B2BA3345F86863804B572,
	NativePlugin_setUnityResolution_mA4D1699F7EE16EAC930DE91D8933A820F9360203,
	NativePlugin_setUnityCameraOrientation_mF79085CCCE74BE57A7A79630C6D21043082C7BEA,
	NativePlugin_convertUnityPixels32_m92FCC044C174F10723ED5820122F94247EE41B6B,
	NativePlugin_stopUnityCamera_m8E6030C2D7F628F6DC412BF77A66CA945E277FC6,
	NativePlugin_Release_mFA8CEA67A4AA61C08B58011411951AADA6B30D1D,
	NativePlugin__cctor_m252159BFE4CF594FDD16F063B5D1F7438815FB99,
	NativePluginStatic_ewcUpdateTexture32_m5DC97EFD59232F2A8E888CE6826AD4976094DBA0,
	NativePluginStatic_aruRequestCamera_m9B789E7E7037AE6E81CD9D6A8E20439266C8C20D,
	NativePluginStatic_Init_m26C834A1DE751657CCDF2A18196CDB1999A13677,
	NativePluginStatic_Play_mA506AAFCD07B428F9927C1C320216E379F8C1622,
	NativePluginStatic_Stop_m325D8E42A9D075DE5C893883D5B690FD994C35BF,
	NativePluginStatic_setFocusMode_mABF1DF5FB60303733F73D2A445C2688FDAA7DA08,
	NativePluginStatic_setTorchMode_m1C452AA7EF4F6B527F523D19BC0767326CBB8941,
	NativePluginStatic_SwitchCamera_m1BF96584D64E5C06CFFDD7CA5B757A2562CFD1FC,
	NativePluginStatic_UpdateTexture32_m8293EADDF1C723A8A1A813BBD11C5378B9F594F1,
	NativePluginStatic_UpdateTextureByte_m870D23D224EC552FF41C3CCA1DBB8461E152A85E,
	NativePluginStatic_RegisterCallbacks_m42C4EAEF08C379E8418435AEA726263D46A56703,
	NativePluginStatic_setPreviewResolution_m77CDCE0569ABE436E0108F98DF71CCCDBFE7D1D2,
	NativePluginStatic_SaveImageToAlbum_m395EE1527A879C1B7AB97C6FD3B4217FBB589210,
	NativePluginStatic_setOrientation_m9088BBEDE5687402A84A7171198589EFE55F73BE,
	NativePluginStatic_Release_m17E52F849D86EEE4C8EAF8C3953E3706835C06D4,
	NativePluginStatic_setUnityResolution_m04249DE36E2A8A651C50E0ABA04F89A20EB287A3,
	NativePluginStatic_setUnityCameraOrientation_mA8A3B4202B49F20B470669B3CDC9D2216F067E03,
	NativePluginStatic_convertUnityPixels32_mE4A30E4FC5174FC9050653BE0888D6D27BA6DBD1,
	NativePluginStatic_stopUnityCamera_m482A67A00C707A714262696E38E37E457433FE93,
	RenderListenerUtility_add_onQuit_m464E77371EFEA6184D5EFA412C115D14C796FBA8,
	RenderListenerUtility_remove_onQuit_m5F8E8E54AE32FD2C9D893A057F1CC152DC274D66,
	RenderListenerUtility_add_onPause_mBA6DFC43BB5CE73E06D1E5E36C3C77D424D22089,
	RenderListenerUtility_remove_onPause_mA7401A45922C7E69F7FE40CCCCEED0DEEF155F57,
	RenderListenerUtility__cctor_m63E81ABF01E2A83E1B65B99D36952B17A52135BF,
	RenderListenerUtility_Start_m79F0CE629E84076E021851D4F84FCEA956510C66,
	RenderListenerUtility_Update_m0ED82D25BB903039B84DDD1696B0558576F187F4,
	RenderListenerUtility_OnApplicationPause_m3A605CCE2D963BCEB31EE1F64261C7D7C228911F,
	RenderListenerUtility_OnApplicationQuit_m07BE1E2ACF54F3F8072C839635084A31C1CAB7DC,
	RenderListenerUtility__ctor_m4B1626D9FA3CF13205CE1DFDA7F104D3B69AA1F9,
	Loom_get_Current_m238E0A9AC87A306C23404C6CA2CD3CA3D8D13048,
	Loom_Awake_m40186EE40F861381C341DC32CF193FC8679B05B2,
	Loom_QueueOnMainThread_mF632665AE41C63CFF07F6338413FCB5386CF3403,
	Loom_QueueOnMainThread_mBCEE6625332F8EFD80AE3221FFD2B96B40325833,
	Loom_QueueOnMainThread_m50E3B3F0A313EDE10A869E9EE16837DA409E1801,
	Loom_QueueOnMainThread_m9F8D85A88F4D2D3D6A8729F49497A2DBE8B23C09,
	Loom_RunAsync_mC0333F442F462BA65BE2AB7407F529499E187B97,
	Loom_RunAction_mBA937F2AAA721C6BEB299167ED6F261B559D1754,
	Loom_Update_m805DA1F6C27748606264FDD84101FAB918325FA0,
	Loom__ctor_mFD03E0F1369DFFCC1B28FC39B1B682B876B97456,
	DelayedQueueItem__ctor_m22FD1B4DD9EDA74F09C84DB85014138003AAA773,
	U3CU3Ec__cctor_m6365AA1ACF32BC66440774B19B200200835C06FD,
	U3CU3Ec__ctor_mC47F7F2A3395C5C7CCB9CFB44A0E02BD66E55AB1,
	U3CU3Ec_U3CUpdateU3Eb__15_0_mF1A976460CD5EF62CFC27C6EB279DD0AB882E25D,
	U3CU3Ec__DisplayClass7_0__ctor_mB9D81DBAE2F6F187A93F6A9E292AD1124F3BB68A,
	U3CU3Ec__DisplayClass7_0_U3CQueueOnMainThreadU3Eb__0_m30EC8AD3A8800A494FD662462252A30903867E28,
	DeviceCamera_get_preview_m7B7433A2AE04F39001F8EA6F54F268C26F0A3500,
	DeviceCamera__ctor_m304B7626F7502A1B5E2B42A62400A9D24BD4C445,
	DeviceCamera_getRotationAngle_m3C173A51079D62507D2DAB12A073F455ABD69C26,
	DeviceCamera_Play_m8A303CCEFB910A0EC5DD670783B9271063D521B3,
	DeviceCamera_Stop_m35FC1DAA2158B865D30FCD943F51A35B3F769583,
	DeviceCamera_getSize_m1BC49350DB7AD445CAD06BA9CC88CA16904A09D7,
	DeviceCamera_Width_m38FCA6EC23B1ACDC23338B9AC6C6A4146EE4A852,
	DeviceCamera_Height_m3DD64154EDD67F60157D2EDB897D3851C68D5B9F,
	DeviceCamera_isPlaying_mC9CB2258D2F5D3A9CFF1F6736237D5078FFC3C3F,
	DeviceCamera_GetPixels_mDDA66602FA1FE591320FB1950AC8DF1DA928B02A,
	DeviceCamera_GetPixels_m366A1FA2C0500D5EE9B239EF8CBBDFFAAD466690,
	DeviceCamera_GetPixels32_m619AC5F25C45AC50E1C103A61035E2E21A5126C0,
	DeviceCameraController_get_dWebCam_m760F573A9E1EB308F4398532C500D8C9CCE95926,
	DeviceCameraController_get_isPlaying_mE7E2DBD207256D0866F97281F714A4B9E170AE47,
	DeviceCameraController_Start_m443ED0CD59DB1F7614707F317D7B51B7C7411110,
	DeviceCameraController_PreviewStart_m6F15C594C442C8F9F8651DBDE36A6CCE0A747534,
	DeviceCameraController_Update_mC447422DFE111E58FD791671C5F40391435DE9CC,
	DeviceCameraController_StartWork_m951BD91729AD54D2711D53275B7081363AD2BFF3,
	DeviceCameraController_StopWork_mFD7BDECFBF491FBA62FF45DC5E22F8BDFB80FB66,
	DeviceCameraController_tapFocus_mC18BAE291562D00A65DDFD0577665D19DC78F0A9,
	DeviceCameraController_swithCamera_mB913AEF1B3B9DA937CDE7122F831EDC58AA6A2DE,
	DeviceCameraController_toggleTorch_m30D4B688936A55EC9DA48E6ACE5BCFF04BDA1F1F,
	DeviceCameraController__ctor_m734A15A78C3C52DC345528AFDB05FA5F6CCC7B24,
	GalleryController_get_GalleryPath_mE7420336DBC55812CC3DBB1CE44F0638627F3253,
	GalleryController_Start_mA52C274B9B58557C88E70F4DA45C0E0A7537F1E8,
	GalleryController_SetGalleryPath_m8264E6FC8D77D07259D81FA802A1BFF9ADBE6E9D,
	GalleryController_receiveGalleryPath_mE4FDAA38A4AD8AD0D5C1FF0AC93B3D117467C794,
	GalleryController_Refresh_m39D3D61820D69CBE823E61579AEFD28E13B3FC81,
	GalleryController_SaveImageToGallery_m61D5B4854CBCE889A13D9F97F750FAB12A2BBB49,
	GalleryController__ctor_mA114FB72316D3FF57D603AB8F8F4392AF960D72F,
	QRCodeDecodeController_Start_mC12766A257434043D9451D94044AFD2981EC5EDE,
	QRCodeDecodeController_Update_m532A336057E1C603262A26660D878E10CA3B97BB,
	QRCodeDecodeController_Reset_mD7E3AE158E0920E746F2257F3B5283BF5FBF2211,
	QRCodeDecodeController_StartWork_mEFB96626661AD7ED7DF1DD170880F8870C70C99A,
	QRCodeDecodeController_StopWork_m116DC19CE575D07EC096BE67808E65DE56D5FA7B,
	QRCodeDecodeController_DecodeByStaticPic_m21EBFBA4F0EBF0AC28A65AF8F227ED16B463D534,
	QRCodeDecodeController__ctor_m428BD41CDD91B91C5725D9BF821ABF04DF6B06A0,
	QRCodeDecodeController_U3CUpdateU3Eb__17_0_m14BD64815EB33E73062EB281C2942682133B7CC0,
	UnityEventString__ctor_mD1028D3CF6EBCF023DC5FD697FEFFD5204C0CC8F,
	QRCodeEncodeController_Start_m930B4E5A2A566C759E4E2A6AC1DA1C826260A256,
	QRCodeEncodeController_Update_m1F2C372D7AC5BBF09D21A0BFF092C7E26001CFCB,
	QRCodeEncodeController_Encode_m91E1747387EA55BF78F1D8B2B13C5531333E2E8E,
	QRCodeEncodeController_RotateMatrixByClockwise_m82E5DBDBBEEDCCA2DA09CC9A97241081A77EDDC4,
	QRCodeEncodeController_RotateMatrixByAnticlockwise_m8CD537ED508315859CDE8D59CF9E5C168D03D988,
	QRCodeEncodeController_isContainDigit_mDEEC171256D79D70FAA97F63E488D990D57371BC,
	QRCodeEncodeController_IsNumAndEnCh_mAEE909841812612733D13D381C134B158A4A270A,
	QRCodeEncodeController_isContainChar_m95DD94911642993CC01E17455EA4A9735C77D3C2,
	QRCodeEncodeController_bAllDigit_mA1BBB3E3EDA7565E5E1A66F6E762270BA55F6F80,
	QRCodeEncodeController_AddLogoToQRCode_m0BFB253651F29A6DC771AFAEBDAE09F90B127203,
	QRCodeEncodeController__ctor_m5200609EC80999F192C8F4F2261D3AB2AF0012B0,
	UnityEventTexture2D__ctor_m8C2F0968F8A990154B6C0BAC24EA0CF0976FA0AE,
	QRDecodeTest_Start_m37E114609E5330581C576A3B01F4B5DC23DC8B58,
	QRDecodeTest_Update_mEF7AF0BAB9DD1448EBF3074C7E9F9320BFBD5CC1,
	QRDecodeTest_qrScanFinished_mE24B09A074FB3D3E8D70E5278A3D98543AF82308,
	QRDecodeTest_Reset_m1EC8A6F3D239AE2CAAF053372A2CE439827BA227,
	QRDecodeTest_Play_mF83F609B3351856B6D3D0A0784937C93BFE50988,
	QRDecodeTest_Stop_m948323E237EC32E96818B99DD64D56E8A715AB06,
	QRDecodeTest_GotoNextScene_m2878E319634507C099D47CAA08B63CC762C80819,
	QRDecodeTest__ctor_m50400EF2070B4019124FF33227EA35955EC12E01,
	QREncodeTest_Start_m787659C2CB9CB37AE5438133A3B33952631AA2DA,
	QREncodeTest_Update_m5FF8D66E55A59F5EE4C90D1E2E76DF0825F6B720,
	QREncodeTest_qrEncodeFinished_mF367B757C50DF236422D734E1310E3FAF63C614A,
	QREncodeTest_setCodeType_m3B19477466F32960015E0B4F8B9AD6EB02D976CE,
	QREncodeTest_Encode_mCD28EDD1045DE0CC5127975063078480409D4200,
	QREncodeTest_ClearCode_m5666F37D7D9BF7A2ACA3F403C1EEA2B07A539868,
	QREncodeTest_SaveCode_mEF4032A351EAE0B8E41602E89248D906123E6F14,
	QREncodeTest_GotoNextScene_m456AE362721FCE64321BE2E21E98B909F6884794,
	QREncodeTest__ctor_m27903FEDDE86DAA760A9A404787DAE0C46D314E1,
	TextureScale_Point_m2AE3872E9E41EDA1C80F3621DB02CDDDC37A16E9,
	TextureScale_Bilinear_m46D3FAF17B036C221322852995D13D65B93AD9A7,
	TextureScale_ThreadedScale_mCAFF7C32104EB62501C535FC53DAE5C9C3A11BB1,
	TextureScale_BilinearScale_m22923828DE2338530B8F46A6364FAA89BAB2C92F,
	TextureScale_PointScale_mFFA5765213B531DA43626AC5822CA48F72EC07D1,
	TextureScale_ColorLerpUnclamped_m1E513C5098D6689AC1C66818609B160416F01512,
	TextureScale__ctor_m58F73B7E18FAEC7656C4EFBBCEFA2D5E06297906,
	ThreadData__ctor_m871190A9851FF9A4E07BBBAD8F2F6CB87CD185B2,
	Utility_CheckIsUrlFormat_m53211E48EF5C7428F166768B4EC4E9FB9CA7A19D,
	Utility_CheckIsFormat_mE7967AC10F87F3971146F9FCC99A469C8EC423AD,
	Utility__ctor_mDE968B2BF99D305E4E010E70EBD998699B0C7410,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mBEB95BEB954BB63E9710BBC7AD5E78C4CB0A0033,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mE70FB23ACC1EA12ABC948AA22C2E78B2D0AA39B1,
	MyClass__ctor_m542849D89902B6DCA2385356B7959E0CA6ED1B23,
	EasyWebCamAndroid_get_previewWidth_m67C62820B6C21DCF51EFB3D625D4C5D91DA32DC0,
	EasyWebCamAndroid_set_previewWidth_m4A52CE1B9037CA9CBBBCDEBDBAD77C2DA3137811,
	EasyWebCamAndroid_get_previewHeight_m7CBEF0DD54F4F5FA153F5A6983C74DCF84B80018,
	EasyWebCamAndroid_set_previewHeight_m837E5A38699A47E1B28B5656EAF9D124892A4E90,
	EasyWebCamAndroid_get_IsPlaying_m4C222B17AE449EC443124D824E345C4A0FD83DAF,
	EasyWebCamAndroid_get_WebCamPreview_m8F0E613D72ABCBB58DDDE04010D3CA4462F1BD3F,
	EasyWebCamAndroid__ctor_mB6A51CFBA08AF508CA2A609E4E68ECFFBB5F751F,
	EasyWebCamAndroid_setPreviewResolution_mF1D98788A252AEA8AD0E3F8F8249D90022C231AC,
	EasyWebCamAndroid_Play_m43C21D985F8ADCDB300045058B74AB98D5E774F0,
	EasyWebCamAndroid_Init_m9DB299FD520B1B0C9B5861C3F8B3C7EF03C97FEB,
	EasyWebCamAndroid_Stop_m6B5FA52F15512938D973C86E500987F4F479FAA4,
	EasyWebCamAndroid_setFocusMode_m6AB3D2DC5CA56A9BB12D76735AB320F8046D8746,
	EasyWebCamAndroid_setFlashMode_m32F573CFDAA962A28C8420401E8065F183991AA9,
	EasyWebCamAndroid_setTorchMode_m919D1128803EF4020FABB907A113A9DBE884389C,
	EasyWebCamAndroid_tapFocus_m80779DC55FB9EB6603DEE148FE9B5F272F34A613,
	EasyWebCamAndroid_TakePhoto_mF0E8CE7EB720AA81EF5B5B1B2BAAA891D7AA7AED,
	EasyWebCamAndroid_OnPause_m6E6B7FDECEC4E090B852950F26B7F548366B72B1,
	EasyWebCamAndroid_Release_m99B08F27EDD51EBF72470BC0523DF5B18F4CD27F,
	EasyWebCamAndroid_SwitchCamera_mE6394DFE3868460004D93265BFDC13E2858A35FD,
	EasyWebCamAndroid_UpdateImage_m5605488B338EB15DEBFCFA838C9C22E21C83044F,
	EasyWebCamAndroid_ewcUpdateTexture32_m4E6C61E075D899586F215AF3DE90811C9E2788BF,
	EasyWebCamAndroid_setOrientation_mE14941ACC5C769B48BAC67B94E21D9D48AEEB376,
	EasyWebCamAndroid_getEnterFrame_m63BEA992C5E3B4DCDE301FDFE6C344B3B2B436BF,
	EasyWebCamAndroid_isCameraUsable_mA700A1F002865151E9B226E28B3B32EFF0E87F53,
	EasyWebCamBase_add_OnEasyWebCamStart_m999B968E383190B4F677B967F8A21F7F9B19E0F7,
	EasyWebCamBase_remove_OnEasyWebCamStart_mAFE85654AEB3264401C7FFAC4A883A6F8E33573A,
	EasyWebCamBase_add_OnEasyWebCamUpdate_m9C5EEF5DF21BAD3DDAF659ED189B09EDE6B9B67E,
	EasyWebCamBase_remove_OnEasyWebCamUpdate_m830439E1BB980928DB7702234418C9934549D5CF,
	EasyWebCamBase_add_OnEasyWebCamStoped_mFB81AD08067BBE462103C7A0BD2689ACB8DF0062,
	EasyWebCamBase_remove_OnEasyWebCamStoped_mC18F2B2186260BDE522019C8A945DFA4E113F973,
	EasyWebCamBase_EasyWebCamStarted_m51612CECBB0E65406A385FAEC9459359BCA2EA67,
	EasyWebCamBase_EasyWebCamUpdate_m9AB6AA793FDD7CDFBFFC802290BF145C37B23A05,
	EasyWebCamBase_EasyWebCamStoped_mA95AEC189C7F85009572FFEE5153B99E0D53AC1C,
	EasyWebCamiOS_get_previewWidth_m16D2DA56E74FFFCCF221612833448A9DD26F2C33,
	EasyWebCamiOS_set_previewWidth_m94D2FFC65AFF256A733CA411B3AA7E95B8DE0A36,
	EasyWebCamiOS_get_previewHeight_mE04AB0CEB55A350C7C79EF882C319462365EBDC4,
	EasyWebCamiOS_set_previewHeight_mE876506E701867D34B42644617B218185685906D,
	EasyWebCamiOS_get_IsPlaying_m7A3CAED25126FF6DBD2676D1832993BA698864E6,
	EasyWebCamiOS_get_WebCamPreview_mBCC797FD82092E0687EB78819394AA552E937E1A,
	EasyWebCamiOS__ctor_m4EEEF348EA03A8A06BAE1900790ADBAC917135DB,
	EasyWebCamiOS_setPreviewResolution_m467A294DCD5F1AED00E86E642371F369075CB245,
	EasyWebCamiOS_Play_m0693AC7CE4A90F9498F1EAC4EB1D35F2CBB16800,
	EasyWebCamiOS_Init_m031E5ECED8E71A97B1BA43660F802B1CB1BCCFA6,
	EasyWebCamiOS_Stop_m948FD800CCE9DDA068FE85D0B86ADC4A05A1255D,
	EasyWebCamiOS_setFocusMode_m1CF08AD4B7A68BC8F2E78CA78AF88F166150E664,
	EasyWebCamiOS_setFlashMode_mF3C4DC54B2119405474E667463EAF58886436941,
	EasyWebCamiOS_setTorchMode_m787533F19D09F2DC3D9FE13CC3CDF9DD478AC88C,
	EasyWebCamiOS_TakePhoto_mC48F3A5851A1D9988BAB3C9008B2C0F0B5A5C52B,
	EasyWebCamiOS_OnPause_m02D846C8A34451C6F2755303066DCCD6A9E10291,
	EasyWebCamiOS_Release_m41DD88D0476FB8162E0B94909B089E35950342A3,
	EasyWebCamiOS_UpdateImage_m37E1E8C177E0E897D074DA4F354F6386FC678CEE,
	EasyWebCamiOS_getEnterFrame_m88BD3146E9D8CE22450ACE3CC0E6C3B371457816,
	EasyWebCamiOS_isCameraUsable_mDDE0916FE2988B3EA450D754E8A2FAF45B1EADBA,
	EasyWebCamiOS_SwitchCamera_mC39F072CE8C0533E96ECFACE78B2C22726249772,
	EasyWebCamiOS_tapFocus_m68CC0AF5956D65AB915E509F3CBA5492EB4E2905,
	EasyWebCamiOS_setOrientation_mE70C30D5F311FA57A77FC855A8E268E14FA8AD3E,
	Utilities_Dimensions_m1FC035A9F95036E33CAE8D4E0F1051EDD73C35EA,
	EasyWebCam_add_OnEasyWebCamStart_mE0558AD4819C898744CD038251C5BC79479ACB04,
	EasyWebCam_remove_OnEasyWebCamStart_m4BB2719FD978DF0E3D35D79AD78C8C86CFBBE287,
	EasyWebCam_add_OnEasyWebCamUpdate_mBC4AD648B5D235D86E8763ACAF6A54347CAA240C,
	EasyWebCam_remove_OnEasyWebCamUpdate_m81AE31219CA5E76E8FBBF7B7D90595112B45E585,
	EasyWebCam_add_OnEasyWebCamStoped_m60770CD567276528F542E1DE2587A9CB1983B03A,
	EasyWebCam_remove_OnEasyWebCamStoped_m04F0C61EA4BD52E6C8AF1BFB71F1F02AC2E237EB,
	EasyWebCam_get_WebCamPreview_mD65335B49D6617EB0380583107EFC7BB3AA8AE38,
	EasyWebCam__ctor_m17FAFCDFD01A9F2E45B693170356387EFD8914FE,
	EasyWebCam_Awake_mB1422156816C4350D72226D3B776329CD053A446,
	EasyWebCam_Start_m1A528370C66FF74D4D95ADBFC5FA44124DD469C2,
	EasyWebCam_Update_mD7B2E6E07C13F5591651BD593F4AC82A54003345,
	EasyWebCam_Play_m42DCDB8BB061F87D8DE224027379EC3CC8AA9676,
	EasyWebCam_Stop_mAA236F0FEB84FFF6363AB8F9E3531FD8A3D2B594,
	EasyWebCam_setPreviewResolution_mDDD8C930AA899CD7B319B2E07836DEFAC83509F6,
	EasyWebCam_TakePhoto_mEB82A9E5BDE3F7BA65F5D2508E36106EF415067B,
	EasyWebCam_setFocusMode_m013C22F037D9902D83931D902C3968DF3A60D41F,
	EasyWebCam_tapFocus_m5CCBA23FD9EB55AEF8E0D5DEF5D8938784CE342D,
	EasyWebCam_setFlashMode_m0B3B24946DB44CBD59CDDD4CA998D16B2591CCC5,
	EasyWebCam_setTorchMode_mB3608C390572D32F2AD828FF8BA7A52C2807C31F,
	EasyWebCam_SwitchCamera_m829A7AA3602C6D43DAB9870A043458D79CA900F5,
	EasyWebCam_Width_m9B8115BB90E64918DACB079140CD7524A4656FDF,
	EasyWebCam_Height_m6709BA9C72C614B7009DFBFA7E65466E07B82E0D,
	EasyWebCam_getFrame_m051D742726BC5B9DDD6F66A44296BA87651251B8,
	EasyWebCam_OnPause_m5ABF2156FFC125DDBA77CBC92A6B639C7812157E,
	EasyWebCam_OnRelease_mF3EC81A3A0C14B1C7FFF5E9223777577E3B76340,
	EasyWebCam_Release_mE01DEC7089287322F6313FD9252E01C0278F169D,
	EasyWebCam_RequestEasyWebCamInterface_mDF24831BF3D6974684BA7A10A4C93BDAD0156DFC,
	EasyWebCam_isDoubleClick_m79FE2264D207FE6D5350B390ED0C3C45055AC323,
	EasyWebCam_EasyWebCamStarted_mF86470DC83480135FC078902AD5B0DB32D83211B,
	EasyWebCam_EasyWebCamUpdate_mAD7552D5C00FD7A727B2D3D1AA4AD1C647969697,
	EasyWebCam_EasyWebCamStoped_mC7965D4AC5E972AFD8783EE470133E81899DD51D,
	EasyWebCam_OnApplicationPause_mE7BFB3A43050FF419EDDC8D536DE949556F61D4E,
	EasyWebCam_OnApplicationQuit_mD38274104F2C47FE1C67F7A679FA77CB0C7BFFE0,
	EasyWebCam_OnDestroy_m5843214EEFCBB1F3D3976F708F7DC9A9DCF99D17,
	EasyWebCam__cctor_m62E0AA2AFEE00B878CA4891CCB79CAEE9DE4A545,
	EasyWebCamCheckHelper_Awake_m6D57717D1604AA0792AC7B4C60F7B82F84005BBA,
	EasyWebCamCheckHelper__ctor_m83741A847A8CEDE4BD339559CBDCDA761EC607B7,
	U3CU3Ec__cctor_m4984EC36298A5B11910927200AFB2CA6FF547E50,
	U3CU3Ec__ctor_mD8F5C32DB6A0D8BE046ABAFBC9C5647F164DDAEB,
	U3CU3Ec_U3CRequestEasyWebCamInterfaceU3Eb__38_1_mA51AF69822B9810C1EC4DAE7FBAEC038D81995B4,
	U3CU3Ec__DisplayClass38_0__ctor_m602D4E866B2924C00FEDE17FD0F5508D523B1587,
	U3CU3Ec__DisplayClass38_0_U3CRequestEasyWebCamInterfaceU3Eg__RequestEasyWebCamActiveU7C0_m9180334E6065A8FF8FECD20398334628611D3055,
	U3CU3CRequestEasyWebCamInterfaceU3Eg__RequestEasyWebCamActiveU7C0U3Ed__ctor_m1DE251DBD4EB3F21C283F04A3A94A3F792B82B35,
	U3CU3CRequestEasyWebCamInterfaceU3Eg__RequestEasyWebCamActiveU7C0U3Ed_System_IDisposable_Dispose_m20058E11E8EC49C2BB330CEBCD89CA9B52B0837E,
	U3CU3CRequestEasyWebCamInterfaceU3Eg__RequestEasyWebCamActiveU7C0U3Ed_MoveNext_mB21AB243AA28C7BD4F9FB63AF573D1F1E113F83D,
	U3CU3CRequestEasyWebCamInterfaceU3Eg__RequestEasyWebCamActiveU7C0U3Ed_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mD1A7A364224BB0DC9A9FDB69332A158E6B444427,
	U3CU3CRequestEasyWebCamInterfaceU3Eg__RequestEasyWebCamActiveU7C0U3Ed_System_Collections_IEnumerator_Reset_mB7ABB114FF6493B6CF6726776F0468DFF87ADC5C,
	U3CU3CRequestEasyWebCamInterfaceU3Eg__RequestEasyWebCamActiveU7C0U3Ed_System_Collections_IEnumerator_get_Current_m3F3AAD21C4826702DE5B9AA6CA886DC3B1F56AFF,
	U3CAwakeU3Ed__20_MoveNext_mC37ADC8C7359494CD3536931E4A388DF84662E95,
	U3CAwakeU3Ed__20_SetStateMachine_m52D92CECABC76F7C860B13A7392FEB181F4CF22F,
	U3CPlayU3Ed__23_MoveNext_mB51DE6F7B174831FFF628567D2AA190836AF578B,
	U3CPlayU3Ed__23_SetStateMachine_m62C3CA53B818A01BDEB85FDAA51770E7E013A7C2,
	EasyWebCamStartedDelegate__ctor_m7CF2FF48DD1583C9A796C4B51FF0DFB84FDE1EE6,
	EasyWebCamStartedDelegate_Invoke_mE3549CBA4A29B721884A06585AB39C569A8F48CD,
	EasyWebCamStartedDelegate_BeginInvoke_mD095B69520DA61E2BA8CF82CE96A98258B18D5F8,
	EasyWebCamStartedDelegate_EndInvoke_mEB3C4E38783FBA09BE2E5F47632BBF143B45A7EA,
	EasyWebCamStopedDelegate__ctor_m66A0E2F9446B6D09E9F697FD23714FED66995430,
	EasyWebCamStopedDelegate_Invoke_mC26F20362D6236CF7762148063553C22CA2DCC3E,
	EasyWebCamStopedDelegate_BeginInvoke_m99C1A8F289F76FD9588450D863A79ADA31793A85,
	EasyWebCamStopedDelegate_EndInvoke_mDDC0AED6CD02537F6A454A5D8C9E2D4BEC29137D,
	EasyWebCamUpdateDelegate__ctor_mE8216630017A009A44ED27AF264601C699D6B03B,
	EasyWebCamUpdateDelegate_Invoke_m059942CAEDD2ABB85076002C8824DF0FAE5F802B,
	EasyWebCamUpdateDelegate_BeginInvoke_m73008AB6AEBFFD724DC0DFD71AD063E49CF8DFF0,
	EasyWebCamUpdateDelegate_EndInvoke_m81DF24BE194BA607E1B7A8F14A53201A0190AED8,
	AnimationPlayer_Awake_m176ED1A61C4B8D6DC9A859F9DAED4234F1961523,
	AnimationPlayer_PlayAnimation_m5EACC3194501CC65ADEC4A7CE9ACFF79A3A90556,
	AnimationPlayer_PlayAnimationWithCallback_m1F7B9E3CA11C3FE503526B25AE58E398000ED4A1,
	AnimationPlayer_AnimationExists_mE4CECAF81230628BD76F98A4D910102D46F6097D,
	AnimationPlayer_SetTrigger_mA68F5742A4583DEA64A3C98079D5EA105618FD5A,
	AnimationPlayer_SetBool_m23C6CDC5A6EE1EEF54D54D30C151ABCF2C95A69B,
	AnimationPlayer_SetFloat_m70EE7E3254013006AFAF0EB640B074EB8E061EB5,
	AnimationPlayer_SetInteger_m7835C4E3562681A69A80970879EAB251D2737EB7,
	AnimationPlayer_WaitForAnimationComplete_m5AFD69BD0CE5D696A9A3A3F2CC78287B95D3485F,
	AnimationPlayer__ctor_mA3875A2CA1374D1AF010C2435ACA93E909829137,
	U3CWaitForAnimationCompleteU3Ed__9__ctor_m1CE65015917B1FFC2D92F09BBC7B3B608041B802,
	U3CWaitForAnimationCompleteU3Ed__9_System_IDisposable_Dispose_mA4413AAF7399D83F3829F8EB87771677940F448A,
	U3CWaitForAnimationCompleteU3Ed__9_MoveNext_mC999DDA0C87748CA2F445942C11EA7130C0528C7,
	U3CWaitForAnimationCompleteU3Ed__9_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mC0A306C9D9DC247BD27F7F0293F98DC2C71C21C7,
	U3CWaitForAnimationCompleteU3Ed__9_System_Collections_IEnumerator_Reset_mF036C035A5C1BEF23F3F66A9F7AEE587BCA770CF,
	U3CWaitForAnimationCompleteU3Ed__9_System_Collections_IEnumerator_get_Current_mB4864375E61BAFEB1A48EA6A40F7A89C879182BE,
};
extern void U3CPlayU3Ed__28_MoveNext_m0B80CA253A3DD436B7F7519800733916C991113F_AdjustorThunk (void);
extern void U3CPlayU3Ed__28_SetStateMachine_mF04521BD01F999AC71E018A63B9C7EEB354623DD_AdjustorThunk (void);
extern void U3CinitResolutionU3Ed__36_MoveNext_mDB77DF9D195C5E3B23DBAA601508FB216B54072A_AdjustorThunk (void);
extern void U3CinitResolutionU3Ed__36_SetStateMachine_mBE7C03BAFECFA209A1DB9EBDC29870BFBABED513_AdjustorThunk (void);
extern void U3CAwakeU3Ed__20_MoveNext_mC37ADC8C7359494CD3536931E4A388DF84662E95_AdjustorThunk (void);
extern void U3CAwakeU3Ed__20_SetStateMachine_m52D92CECABC76F7C860B13A7392FEB181F4CF22F_AdjustorThunk (void);
extern void U3CPlayU3Ed__23_MoveNext_mB51DE6F7B174831FFF628567D2AA190836AF578B_AdjustorThunk (void);
extern void U3CPlayU3Ed__23_SetStateMachine_m62C3CA53B818A01BDEB85FDAA51770E7E013A7C2_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[8] = 
{
	{ 0x060000A4, U3CPlayU3Ed__28_MoveNext_m0B80CA253A3DD436B7F7519800733916C991113F_AdjustorThunk },
	{ 0x060000A5, U3CPlayU3Ed__28_SetStateMachine_mF04521BD01F999AC71E018A63B9C7EEB354623DD_AdjustorThunk },
	{ 0x060000A6, U3CinitResolutionU3Ed__36_MoveNext_mDB77DF9D195C5E3B23DBAA601508FB216B54072A_AdjustorThunk },
	{ 0x060000A7, U3CinitResolutionU3Ed__36_SetStateMachine_mBE7C03BAFECFA209A1DB9EBDC29870BFBABED513_AdjustorThunk },
	{ 0x060001BF, U3CAwakeU3Ed__20_MoveNext_mC37ADC8C7359494CD3536931E4A388DF84662E95_AdjustorThunk },
	{ 0x060001C0, U3CAwakeU3Ed__20_SetStateMachine_m52D92CECABC76F7C860B13A7392FEB181F4CF22F_AdjustorThunk },
	{ 0x060001C1, U3CPlayU3Ed__23_MoveNext_mB51DE6F7B174831FFF628567D2AA190836AF578B_AdjustorThunk },
	{ 0x060001C2, U3CPlayU3Ed__23_SetStateMachine_m62C3CA53B818A01BDEB85FDAA51770E7E013A7C2_AdjustorThunk },
};
static const int32_t s_InvokerIndices[478] = 
{
	7776,
	7776,
	7776,
	7776,
	7776,
	7656,
	1428,
	7776,
	7776,
	7776,
	7776,
	7776,
	7776,
	7776,
	7776,
	6176,
	7776,
	7540,
	7656,
	7776,
	7656,
	6176,
	7776,
	7540,
	7656,
	7776,
	7656,
	7776,
	7776,
	7776,
	7776,
	7776,
	7776,
	6213,
	3449,
	7776,
	7776,
	7776,
	7776,
	8442,
	483,
	7776,
	6176,
	7776,
	7540,
	7656,
	7776,
	7656,
	7776,
	7776,
	7776,
	7776,
	7776,
	2663,
	7776,
	7776,
	6309,
	6253,
	7776,
	6176,
	7776,
	7540,
	7656,
	7776,
	7656,
	7776,
	7776,
	7776,
	7776,
	11802,
	7776,
	7776,
	7776,
	7776,
	7776,
	7776,
	11802,
	7776,
	6213,
	5461,
	7776,
	6176,
	7776,
	7540,
	7656,
	7776,
	7656,
	7776,
	7776,
	7776,
	7776,
	7776,
	11754,
	7776,
	7776,
	7776,
	7776,
	5461,
	6176,
	7776,
	7540,
	7656,
	7776,
	7656,
	7776,
	7540,
	7656,
	7618,
	6176,
	7618,
	6176,
	7540,
	6176,
	7618,
	7776,
	7540,
	6094,
	7776,
	7656,
	7776,
	7776,
	6176,
	6176,
	6176,
	3139,
	7776,
	7656,
	7656,
	7656,
	6094,
	6176,
	7776,
	7776,
	7776,
	6213,
	4412,
	7776,
	7776,
	7776,
	7776,
	7656,
	6176,
	7776,
	7540,
	7656,
	7776,
	7656,
	7776,
	7656,
	6176,
	7776,
	7540,
	7656,
	7776,
	7656,
	7776,
	7656,
	6176,
	7776,
	7540,
	7656,
	7776,
	7656,
	7776,
	6213,
	7776,
	6213,
	6176,
	7776,
	7540,
	7656,
	7776,
	7656,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	11802,
	11802,
	11209,
	11095,
	11086,
	9854,
	10571,
	11746,
	11746,
	11746,
	11724,
	11724,
	9332,
	10758,
	11574,
	11576,
	11802,
	11802,
	11802,
	11213,
	11802,
	11746,
	11802,
	11802,
	11574,
	11574,
	11574,
	11211,
	10347,
	9332,
	10368,
	11578,
	11574,
	11802,
	10758,
	11574,
	11576,
	11802,
	11578,
	11578,
	11578,
	11578,
	11802,
	7776,
	7776,
	6094,
	7776,
	7776,
	11754,
	7776,
	9939,
	10792,
	10797,
	11578,
	11578,
	11578,
	7776,
	7776,
	7776,
	11802,
	7776,
	4450,
	7776,
	4450,
	7656,
	6094,
	7618,
	7776,
	7776,
	7765,
	7618,
	7618,
	7540,
	7656,
	954,
	7656,
	7656,
	7540,
	7776,
	7776,
	7776,
	7776,
	7776,
	7776,
	7776,
	7776,
	7776,
	7656,
	7776,
	7776,
	6213,
	6213,
	11578,
	7776,
	7776,
	7776,
	7776,
	7776,
	7776,
	11351,
	7776,
	7776,
	7776,
	7776,
	7776,
	5164,
	10477,
	10477,
	4450,
	4450,
	4450,
	4450,
	7776,
	7776,
	7776,
	7776,
	7776,
	6213,
	7776,
	7776,
	7776,
	6213,
	7776,
	7776,
	7776,
	6213,
	6176,
	7776,
	7776,
	7776,
	6213,
	7776,
	9903,
	9903,
	9143,
	11578,
	11578,
	9360,
	7776,
	3139,
	11096,
	10162,
	7776,
	11823,
	7776,
	7776,
	7618,
	6176,
	7618,
	6176,
	7540,
	7656,
	7776,
	3139,
	7776,
	7776,
	7776,
	6176,
	6176,
	6094,
	7776,
	7776,
	6094,
	7776,
	6176,
	7776,
	11096,
	6176,
	7618,
	7540,
	11578,
	11578,
	11578,
	11578,
	11578,
	11578,
	11802,
	11802,
	11802,
	7618,
	6176,
	7618,
	6176,
	7540,
	7656,
	7776,
	3139,
	7776,
	7776,
	7776,
	6176,
	6176,
	6094,
	7776,
	6094,
	7776,
	7776,
	7618,
	7540,
	6176,
	7776,
	6176,
	9845,
	11578,
	11578,
	11578,
	11578,
	11578,
	11578,
	11754,
	7776,
	7776,
	7776,
	7776,
	11802,
	11802,
	11564,
	11802,
	11574,
	11802,
	11574,
	11574,
	11574,
	11746,
	11746,
	11746,
	6094,
	7776,
	11802,
	11754,
	7540,
	11802,
	11802,
	11802,
	6094,
	7776,
	7776,
	11802,
	7776,
	7776,
	11802,
	7776,
	7540,
	7776,
	5461,
	6176,
	7776,
	7540,
	7656,
	7776,
	7656,
	7776,
	6213,
	7776,
	6213,
	3432,
	7776,
	2592,
	6213,
	3432,
	7776,
	2592,
	6213,
	3432,
	7776,
	2592,
	6213,
	7776,
	1301,
	1164,
	4450,
	6213,
	3418,
	3440,
	3430,
	1419,
	7776,
	6176,
	7776,
	7540,
	7656,
	7776,
	7656,
};
static const Il2CppTokenIndexMethodTuple s_reversePInvokeIndices[3] = 
{
	{ 0x06000174, 2,  (void**)&EasyWebCamBase_EasyWebCamStarted_m51612CECBB0E65406A385FAEC9459359BCA2EA67_RuntimeMethod_var, 0 },
	{ 0x06000175, 4,  (void**)&EasyWebCamBase_EasyWebCamUpdate_m9AB6AA793FDD7CDFBFFC802290BF145C37B23A05_RuntimeMethod_var, 0 },
	{ 0x06000176, 3,  (void**)&EasyWebCamBase_EasyWebCamStoped_mA95AEC189C7F85009572FFEE5153B99E0D53AC1C_RuntimeMethod_var, 0 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_AssemblyU2DCSharp_CodeGenModule;
const Il2CppCodeGenModule g_AssemblyU2DCSharp_CodeGenModule = 
{
	"Assembly-CSharp.dll",
	478,
	s_methodPointers,
	8,
	s_adjustorThunks,
	s_InvokerIndices,
	3,
	s_reversePInvokeIndices,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
