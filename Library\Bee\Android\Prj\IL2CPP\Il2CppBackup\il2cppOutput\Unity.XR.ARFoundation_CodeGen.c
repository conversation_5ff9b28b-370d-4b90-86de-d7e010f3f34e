﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"


extern const RuntimeMethod* ARCameraBackground_BeforeBackgroundRenderHandler_m3B215B54CFB6F6F00A724D9A6BE66808593ABFCD_RuntimeMethod_var;



extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m970916F4D526DD5A8B56258C1832C2DCD8AED6D3 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mD111E020C143C9F5D8E50CB513DC66985FD0D305 (void);
extern void ARAnchor_get_nativePtr_mBE82BAA63BB9C836B5AAECBC8B61F855B352196F (void);
extern void ARAnchor_get_sessionId_m6D16A20B42835AC11CC7B3942D92A67887F9D012 (void);
extern void ARAnchor_OnEnable_mFE3DADEC4D7DD29783458A6D1C5DABA2487C4EE2 (void);
extern void ARAnchor_Update_m9147EC2BE20D9441A4375CB366B43CF71AC964D5 (void);
extern void ARAnchor_OnDisable_m5F3C9AE17070DAEBA3822B2DB1A6530264822BF0 (void);
extern void ARAnchor__ctor_m768D346AAF28D2CBCFBDB433C06E38BE96F1E8A6 (void);
extern void ARAnchorManager_get_anchorPrefab_mAC3DD1BAC5D9EF69A877978562C4C196DE9B9456 (void);
extern void ARAnchorManager_set_anchorPrefab_m9802A824D21CA4FC05CDDD422C7821A409F6C0FB (void);
extern void ARAnchorManager_add_anchorsChanged_m9AEAD2307BB09DCC5093D8BAD242AAED777CC5D0 (void);
extern void ARAnchorManager_remove_anchorsChanged_mF59C4EA911963F93B03E01D799007FDD18E7B0D6 (void);
extern void ARAnchorManager_AddAnchor_mF86AC736D4568019E5BF0DA99720697F8970C559 (void);
extern void ARAnchorManager_TryAddAnchor_mA413E51FC549593EBA992D3D8E3C7E48751075B1 (void);
extern void ARAnchorManager_AttachAnchor_m8FA6B772401F90B38A06F43B8EA1B02EA82FFA75 (void);
extern void ARAnchorManager_RemoveAnchor_m25BAC7FE434D25904F4D9E41EBF65A65E96D479F (void);
extern void ARAnchorManager_TryRemoveAnchor_m408B170DFF5E17E6BA8AA33BCC7EAE4B46A3E51B (void);
extern void ARAnchorManager_GetAnchor_m41112A0FE871C0097F9DA6610CA86417CF01A232 (void);
extern void ARAnchorManager_GetPrefab_m0F97F1601711964828AC1394B51BDC6B4F05E24D (void);
extern void ARAnchorManager_get_gameObjectName_m30353DBEEBD961E03E816F947884C44CA30E338B (void);
extern void ARAnchorManager_OnTrackablesChanged_m2FEB6C24F7C2B439DA785DA4745F6D2FBCE684EE (void);
extern void ARAnchorManager__ctor_m15271DAC2CC3EBCF8EB107B0B53632F905E8F9B9 (void);
extern void ARAnchorsChangedEventArgs_get_added_m5F9CEB4587F9496D0DA28880430B69EFDB550600 (void);
extern void ARAnchorsChangedEventArgs_set_added_mEE4383EBEAFBE249A99CEAF1F0EA6FDB964BAFEB (void);
extern void ARAnchorsChangedEventArgs_get_updated_m21039F14E472A75F969EE0BA5CBA9007DC12B593 (void);
extern void ARAnchorsChangedEventArgs_set_updated_mDA509077951F3AE2F2DC7300937CA5C626BABEF4 (void);
extern void ARAnchorsChangedEventArgs_get_removed_mF1E9D61E21153018C87DE99722FF8820C351CFD8 (void);
extern void ARAnchorsChangedEventArgs_set_removed_m18D9FF865947D6363BA0989B783B2CC746A7B867 (void);
extern void ARAnchorsChangedEventArgs__ctor_m67B444466F34F943C140F8F7EC4959DC1FFDDB8A (void);
extern void ARAnchorsChangedEventArgs_GetHashCode_m278A649D0606054A9B7E7C8AFF08CC99C2685024 (void);
extern void ARAnchorsChangedEventArgs_Equals_mC2C108ADF4C9783BD7470E6573196978E61FD49B (void);
extern void ARAnchorsChangedEventArgs_ToString_mA0C838C2F9AB2B51089A47F4CF0E7049F8A86BAC (void);
extern void ARAnchorsChangedEventArgs_Equals_m67E2B32F8DEAB6F7C31BF818E1F42E799B3A0DAF (void);
extern void ARAnchorsChangedEventArgs_op_Equality_m8CA56256C5101ADA8708F263BE39C40700D10106 (void);
extern void ARAnchorsChangedEventArgs_op_Inequality_m2DBE5E61A355C2A128939783FDA4FAA94F3136F0 (void);
extern void ARBackgroundRendererFeature__ctor_m3F83EF33358F752AFD6C177AD06A593064AFAAA7 (void);
extern void ARCameraBackground_get_camera_m95EA27983775174A10F59937EB1EEDAEDB091E53 (void);
extern void ARCameraBackground_get_cameraManager_m38A0B34E4DEF2B6DAB9B93B7F081846A1F503031 (void);
extern void ARCameraBackground_get_occlusionManager_m0002011570FE986A7505CCBA8A98A27B30301355 (void);
extern void ARCameraBackground_get_material_mD444C30747216E7E7B5FF0F714EE0DF57CC93EC8 (void);
extern void ARCameraBackground_get_useCustomMaterial_m237B3CDC574BE3A8DFB9D1B3EF6855CB07B6D6DA (void);
extern void ARCameraBackground_set_useCustomMaterial_m0A3ABCEF2FD08A0C25BE125B59A3D9D8D5569FA7 (void);
extern void ARCameraBackground_get_customMaterial_mA156A934FC64330835BCB9A040C51BDFC49B8C0D (void);
extern void ARCameraBackground_set_customMaterial_m64CDD14D4793D1DBC890DBE232CDB43CFDED1578 (void);
extern void ARCameraBackground_get_backgroundRenderingEnabled_m25B0181C7A2321F2CE97D52C04CA73EA50A768E2 (void);
extern void ARCameraBackground_get_defaultMaterial_m492949B4FA22A0DBF97EBE0D35EC52F84F06360D (void);
extern void ARCameraBackground_BeforeBackgroundRenderHandler_m3B215B54CFB6F6F00A724D9A6BE66808593ABFCD (void);
extern void ARCameraBackground_get_shouldInvertCulling_m2B97B37FD914B74390EC5D9DB92AF823B1A6204A (void);
extern void ARCameraBackground_get_currentRenderingMode_m356E6A88138C933DCACEF92FA37CC5D06BDB1231 (void);
extern void ARCameraBackground_Awake_m75B40D62F040140E5280778DE567862B57F325B7 (void);
extern void ARCameraBackground_OnEnable_m2BD485D597238EB65F61DB7291C421689B67E08E (void);
extern void ARCameraBackground_OnDisable_m31BF14F0E5ECF28913E1D1CC25AE72F4C8EDAC11 (void);
extern void ARCameraBackground_EnableBackgroundRendering_mF0E94430015503FCB4AE4DA7B94C3419CB65AFE7 (void);
extern void ARCameraBackground_DisableBackgroundRendering_m843F0468353D4AFFA7A99AA9148C7757999E865B (void);
extern void ARCameraBackground_DisableBackgroundClearFlags_m2F73DE07971DB0356E0916E00E82C92D96B933D0 (void);
extern void ARCameraBackground_RestoreBackgroundClearFlags_m9E651BB4CDA1F3B9A2E0E572BD9D81045795556E (void);
extern void ARCameraBackground_get_legacyCameraEvents_m965181793B5235D43C7D9C2A1088B1A483C3E90F (void);
extern void ARCameraBackground_ConfigureLegacyCommandBuffer_mE834A7EC1EE8AF3CE85C3E115D44497A4E49BF1C (void);
extern void ARCameraBackground_EnableLegacyRenderPipelineBackgroundRendering_mE3248B2AF0F2730A475CEB096C3418317A9FE829 (void);
extern void ARCameraBackground_DisableLegacyRenderPipelineBackgroundRendering_m9610B7084D5C9BD40829C570D2BACAEAFDAE2527 (void);
extern void ARCameraBackground_AddCommandBufferToCameraEvent_m547997D77AA9F4C1CE620B87553303949B404236 (void);
extern void ARCameraBackground_RemoveCommandBufferFromCameraEvents_m6524CE593D7CE338F442C08BAD5AB6870B14B4FD (void);
extern void ARCameraBackground_AddBeforeBackgroundRenderHandler_m4B566E1944111065C7D262FB4A3ABC5C659949D6 (void);
extern void ARCameraBackground_OnCameraFrameReceived_m8671B0000ED84A20852586D60F6FC62A6BE95075 (void);
extern void ARCameraBackground_SetCameraDepthTextureMode_m156A534B45A960344344336B8D9D68D5B42EE585 (void);
extern void ARCameraBackground_OnOcclusionFrameReceived_m5AAA250023997466F22A824558B74F2EEBB04FDC (void);
extern void ARCameraBackground_SetMaterialKeywords_mEE55C7A06BA08ECF1D1F53EC3D79CC12213DDAF3 (void);
extern void ARCameraBackground__ctor_m7CA5CCD065DA2988AAAC2ADCE38E77047875E7AE (void);
extern void ARCameraBackground__cctor_mC0E45652907684579936B9466751DD3E6D79F310 (void);
extern void ARCameraBackgroundRenderingUtils_get_fullScreenFarClipMesh_mD59358E22DAF5A95AD82AE57963DCD8ACF0C5FF6 (void);
extern void ARCameraBackgroundRenderingUtils_get_fullScreenNearClipMesh_mA5C2D90A8F4B4EA4F0DC0C42DE164677D2A22A85 (void);
extern void ARCameraBackgroundRenderingUtils_BuildFullscreenMesh_m8B8D33BFE7ECFF88296FBB6B8A34CF1E04D8AB6A (void);
extern void ARCameraBackgroundRenderingUtils_get_beforeOpaquesOrthoProjection_m8FEF7D4C53DB2F7E8E91FC6C2546063E359B48D0 (void);
extern void ARCameraBackgroundRenderingUtils_get_afterOpaquesOrthoProjection_mDD479079699B86A699A0E121B50EB6F82C413A01 (void);
extern void ARCameraBackgroundRenderingUtils__cctor_m574A99D445239DDFAB3AD309403714F0F8AD0B2D (void);
extern void ARCameraFrameEventArgs_get_lightEstimation_mDAD20A000D180FB372E8214B37A8A4EB4F0F312C (void);
extern void ARCameraFrameEventArgs_set_lightEstimation_mE04691D14C2CD985D757C11AA126AE60BC6DBB51 (void);
extern void ARCameraFrameEventArgs_get_timestampNs_m3B9E21BF0B36F035E105ED2B155983E03B21F4E9 (void);
extern void ARCameraFrameEventArgs_set_timestampNs_mF3DE3C39790E92B6F2190BEE8DA69568C8A47F20 (void);
extern void ARCameraFrameEventArgs_get_projectionMatrix_mA09F2170ACA84AEC8E9407DE70B9CD1D2E443182 (void);
extern void ARCameraFrameEventArgs_set_projectionMatrix_m7F7C0173EAC5E149E6A4A6FA9FA85E8C071DF517 (void);
extern void ARCameraFrameEventArgs_get_displayMatrix_m3354FFAEF054F0A8F4D19BFD506468A06F3AD65F (void);
extern void ARCameraFrameEventArgs_set_displayMatrix_m4DB301FFAEAC7277FD816737053A2688B9CDE902 (void);
extern void ARCameraFrameEventArgs_get_textures_m96823876351BD11C26D78BAA01936FF751182547 (void);
extern void ARCameraFrameEventArgs_set_textures_mB9F3D579FB8DA3BDF3F4B85D1E598AE19959BAC7 (void);
extern void ARCameraFrameEventArgs_get_propertyNameIds_m9B76BC52DB8349A38D5514528C63A36494283798 (void);
extern void ARCameraFrameEventArgs_set_propertyNameIds_m7F4470E1C46E52045645D6C93923F6DB7A92C6B5 (void);
extern void ARCameraFrameEventArgs_get_exposureDuration_mC4D1A5D0266949EDB98A74E66564E421A9B2A2F8 (void);
extern void ARCameraFrameEventArgs_set_exposureDuration_m48C0FBF3D428A1507BB522D23B2A383B265F4B1B (void);
extern void ARCameraFrameEventArgs_get_exposureOffset_mFDCFBB779CF302DF2159000D328826E7880FDD0F (void);
extern void ARCameraFrameEventArgs_set_exposureOffset_m82EEA2BFFE750731718B4B39FBCA59BC0B4CD2EE (void);
extern void ARCameraFrameEventArgs_get_enabledMaterialKeywords_m76A2E83147560A72FF3834BF0FB7C1B3FEED6240 (void);
extern void ARCameraFrameEventArgs_set_enabledMaterialKeywords_mE8FD88DCAEFA52B0B6E05DA72F61AA7727D88E9D (void);
extern void ARCameraFrameEventArgs_get_disabledMaterialKeywords_m254ACC80879CD8B270566B38CCD6ED99D26255A8 (void);
extern void ARCameraFrameEventArgs_set_disabledMaterialKeywords_m95D9308AC9AA63B459F87C44AD1B378043C08BDA (void);
extern void ARCameraFrameEventArgs_get_cameraGrainTexture_m0CEACD5C125C07A7D6E73695808202940300B633 (void);
extern void ARCameraFrameEventArgs_set_cameraGrainTexture_m2BBDCEB456D16E1AD14D74874CD3F274806F1D61 (void);
extern void ARCameraFrameEventArgs_get_noiseIntensity_m441B0EFBF898D8A18AB1E102E41CF1BB33072591 (void);
extern void ARCameraFrameEventArgs_set_noiseIntensity_m8AA71321684DA61B4D83D0C2F91C85AA9A2FEE7E (void);
extern void ARCameraFrameEventArgs_get_exifData_mDB0728F64EE619CB4174644B6D58598DAE8EC2F5 (void);
extern void ARCameraFrameEventArgs_set_exifData_m1FB5103D69EEE478654B39783FCD7D662F557E55 (void);
extern void ARCameraFrameEventArgs_TryGetExifData_m6AD832BCB4867C3D42D03A0CB6B291BAE259B3E0 (void);
extern void ARCameraFrameEventArgs_GetHashCode_m13E6D8C9B47ACA833C0D2F23C838883FEF4A464F (void);
extern void ARCameraFrameEventArgs_Equals_m896C0CD5FC48A354B4AE1CA23F7219FD812873CF (void);
extern void ARCameraFrameEventArgs_ToString_m31AA489F1C81FA10709305176CE7EE2676F28CD8 (void);
extern void ARCameraFrameEventArgs_Equals_mE86A86B648BFB94A2CB70BE472F974C550AB6CC6 (void);
extern void ARCameraFrameEventArgs_op_Equality_m332C80B6B4C598563AB455FB199A8C60FDF178F5 (void);
extern void ARCameraFrameEventArgs_op_Inequality_m86840F89781D14FDBC398171DFC6C6CB19CE3D12 (void);
extern void ARCameraManager_add_frameReceived_mB30CD73DB7A25A6E7BC486BA289DBB99CBA3B7A1 (void);
extern void ARCameraManager_remove_frameReceived_m8D9C301201B5079323169510656821F0286AA67D (void);
extern void ARCameraManager_get_autoFocusRequested_mCE8CB1883F0FB30F5DF9A0F2B1F6601CA264A26D (void);
extern void ARCameraManager_set_autoFocusRequested_m1E9D7EAC0CB61ABBD9A1240BCBB1A77CB22E219B (void);
extern void ARCameraManager_get_focusMode_m15289C64A6AABBE562681C70A0A743DC01C2F209 (void);
extern void ARCameraManager_set_focusMode_m65C339A2D43423E5A0A3FB954A9CB5AE4993A9C7 (void);
extern void ARCameraManager_get_autoFocusEnabled_m88A78675BF5EECB9BA3DE2EC0308D38987D1497F (void);
extern void ARCameraManager_get_requestedLightEstimation_m1ABD158E3BE20A7F2B311071D44E8A796E209BAB (void);
extern void ARCameraManager_set_requestedLightEstimation_m6571108C98F7622CCA555F91668359B1A2F61D4B (void);
extern void ARCameraManager_get_currentLightEstimation_mBD80DD338D8DF62515FABD05820CA3ECEEAF6B28 (void);
extern void ARCameraManager_get_lightEstimationMode_m61EC23A65B4C4679DEC893F573EB13EE189053BD (void);
extern void ARCameraManager_set_lightEstimationMode_m1A54F00ADF92ECCAD88F59F019CBB7B9F9C87A5E (void);
extern void ARCameraManager_get_requestedFacingDirection_mDD9519E3F839B1ACC242388DF0757AD18069A19F (void);
extern void ARCameraManager_set_requestedFacingDirection_mFF42F5C8B185257403AFF6B963E5C1F47020FF5B (void);
extern void ARCameraManager_get_currentFacingDirection_m79981A1F52B2C8B74495608E9A7F4179BAAAD6F4 (void);
extern void ARCameraManager_get_requestedBackgroundRenderingMode_m80BFC959A25B1680BF2DB13E147AA839A7623602 (void);
extern void ARCameraManager_set_requestedBackgroundRenderingMode_m8AA1C64E3948DE390BF13BA0066EA60448E8B64F (void);
extern void ARCameraManager_get_currentRenderingMode_m33B99B6C897ECD63093F8D6201ABB65FE3690321 (void);
extern void ARCameraManager_get_permissionGranted_m6C34AC20847F992C4213E08AB86DC5B261DD7CE9 (void);
extern void ARCameraManager_get_cameraMaterial_m6EC2895B604AE3D79F8176A8D9C97B531E937437 (void);
extern void ARCameraManager_OnBeforeSerialize_m64A21146DEE3C6AE9C9774BE2E6F43824C85C31D (void);
extern void ARCameraManager_OnAfterDeserialize_m538D9B456947A0C4CE0C3A08518B9B3F133BD00A (void);
extern void ARCameraManager_TryGetIntrinsics_m9D3306A91112F7E3C42A3D382290A6C94BADA8D3 (void);
extern void ARCameraManager_GetConfigurations_mC8E7B130EA3DDF94810B8939F0BBC38466A74316 (void);
extern void ARCameraManager_get_currentConfiguration_m1E20D407F73102BA55EC6CDC4C6AAC6C6C716509 (void);
extern void ARCameraManager_set_currentConfiguration_m300F3F31B734E15C56E136F1E2285E873647823F (void);
extern void ARCameraManager_TryGetLatestImage_m0CECFD8E8B65B22AE4712EACF68BB4EEA31E3C11 (void);
extern void ARCameraManager_TryAcquireLatestCpuImage_m79247340777701FCF1E9CF274D1FC6EAFE0AA874 (void);
extern void ARCameraManager_Awake_m7010013BE02DF0DEFBADA398237FCF758EEBB7E5 (void);
extern void ARCameraManager_OnBeforeStart_m0094D8B406D9C69AE6BBCAEB985F23267CD9CB01 (void);
extern void ARCameraManager_OnDisable_m97049FA6F9676AEA13B003C5641B33506342BBAD (void);
extern void ARCameraManager_Update_mCEC545D77C80228A07B589408426D03420535C17 (void);
extern void ARCameraManager_UpdateTexturesInfos_mA64F17DB0F88464B42F59EC91AAFBC95DAC866F0 (void);
extern void ARCameraManager_InvokeFrameReceivedEvent_m56E1A41175297197EAD1DC4841536B198B56C3C9 (void);
extern void ARCameraManager__ctor_mCD28CAF4C17EE19A33240ED4E81EEC23ACCC1893 (void);
extern void ARCameraManager__cctor_m2CCB79CEFC22E72D5A03A5AC34F39368B710977D (void);
extern void ARDebugMenu_get_originAxisPrefab_m259DE0627CD28F49F45B1C5C536A63E1835C4A74 (void);
extern void ARDebugMenu_set_originAxisPrefab_mC8DD66E33441AAE7DC3B7F7C8291A79B342D441B (void);
extern void ARDebugMenu_get_pointCloudParticleSystem_m76EDC2F6676A4EBC45AF9CF9781439444C79AB8E (void);
extern void ARDebugMenu_set_pointCloudParticleSystem_m9F7E45C7F201092F183514A88EDF564CAD4CEBDD (void);
extern void ARDebugMenu_get_lineRendererPrefab_m08646C973FDF4EE351D9BFD60C39FBBD1E8A6439 (void);
extern void ARDebugMenu_set_lineRendererPrefab_m18A7C54D96B9631231B1DD275678B593D20C146E (void);
extern void ARDebugMenu_get_anchorPrefab_m431D27D360C714D9A2C940DCBCF4718C47F27B1A (void);
extern void ARDebugMenu_set_anchorPrefab_mDDF915B31935FA7CFB94DB4E13F77BD69F9D9285 (void);
extern void ARDebugMenu_get_displayInfoMenuButton_mBB1499ED6DE0512F632AFEB3D818041F2AC1427B (void);
extern void ARDebugMenu_set_displayInfoMenuButton_m994E2B8A4D37BE37687F415D37BB4E357237D7D7 (void);
extern void ARDebugMenu_get_displayConfigurationsMenuButton_m1E85917F57D98007D6A205B002D2850215017100 (void);
extern void ARDebugMenu_set_displayConfigurationsMenuButton_m56AEDA2F2FBD818AB4AC40D6F34AC63C2D182E70 (void);
extern void ARDebugMenu_get_displayCameraConfigurationsMenuButton_mC5636623C7652FB95B512F49A53A06B2245684F6 (void);
extern void ARDebugMenu_set_displayCameraConfigurationsMenuButton_m8DB18DEF3AA93F356D4BE698D3703D2C6019C52F (void);
extern void ARDebugMenu_get_displayDebugOptionsMenuButton_mF8DB109B25768B71B1C0ABDAFDDD27D5E7D80534 (void);
extern void ARDebugMenu_set_displayDebugOptionsMenuButton_m665DD91C50996EAB204822C703747CE912680B4C (void);
extern void ARDebugMenu_get_infoMenu_m41FA06B25A2CF5ACE16CC55E4ACDFABD63A7A5E1 (void);
extern void ARDebugMenu_set_infoMenu_mD595F868A1210903F57A4A53B6A0E4D34991AA2D (void);
extern void ARDebugMenu_get_cameraConfigurationMenu_m97F899567FDB81CFD10F8EB85E111BE31F711E1F (void);
extern void ARDebugMenu_set_cameraConfigurationMenu_m842BBF87BFF187B117A8965013011FD79229D713 (void);
extern void ARDebugMenu_get_configurationMenu_mE2D40BE4B00FF96BE322F15CD912F7571617B957 (void);
extern void ARDebugMenu_set_configurationMenu_m014807B22B8DA18F74352A266B1873C7D0557974 (void);
extern void ARDebugMenu_get_configurationMenuRoot_m4CAF9556F715C65C3F92DB632F327683FD5EA913 (void);
extern void ARDebugMenu_set_configurationMenuRoot_mB72E6BDE0E9512AED47C67A79D772A4C60950E89 (void);
extern void ARDebugMenu_get_debugOptionsMenu_m3592E84BE276DE8A601DEE1E723F0596501A086B (void);
extern void ARDebugMenu_set_debugOptionsMenu_m2A751D87B3BA360751344E1F42C61CF67A39B8B2 (void);
extern void ARDebugMenu_get_debugOptionsToastMenu_m7716D3A31359726ECE1355D8DD0F607E613BF878 (void);
extern void ARDebugMenu_set_debugOptionsToastMenu_mE1B73042FE4178A67B946FA9B079DC10AC63287A (void);
extern void ARDebugMenu_get_showOriginButton_m8CE0DDF01855C3A92D6DE16BFF03CEA9ED1E240C (void);
extern void ARDebugMenu_set_showOriginButton_mA51FFEAF00AB7480DBE17B09A6F0E5D8445A7384 (void);
extern void ARDebugMenu_get_showPlanesButton_mF52159758B0A0174D1C78B740FA1F64E50430813 (void);
extern void ARDebugMenu_set_showPlanesButton_m0444CA1E9E0CF97A1A7415EBBAA8FDAAF5B29CF4 (void);
extern void ARDebugMenu_get_showAnchorsButton_mA3049069F350D9AB6899B41AA68C63C2B9653E3E (void);
extern void ARDebugMenu_set_showAnchorsButton_mFD811EAC32C501BCE9808AFB76B0EBC1EA803BBC (void);
extern void ARDebugMenu_get_showPointCloudsButton_m2CEFD047FF9783ED12076E0E96F41AA4A4EEBBE3 (void);
extern void ARDebugMenu_set_showPointCloudsButton_m2663A91862C86AD4A1C294A52C0EE533477707D9 (void);
extern void ARDebugMenu_get_fpsLabel_m5F30A720414ACF312AE8CB545663709F8E97AD64 (void);
extern void ARDebugMenu_set_fpsLabel_m4F81FE78D8E1D826132BF47F30237C35E3482C1C (void);
extern void ARDebugMenu_get_trackingModeLabel_m5EE6A7089B40E825D10316271A633BB90D8F29B9 (void);
extern void ARDebugMenu_set_trackingModeLabel_m0B1EB6964F99A0B71D43C7C59FB5CD0C274E1066 (void);
extern void ARDebugMenu_get_checkMarkTexture_m20E9C63E88D62E612A020762967D777CE5620400 (void);
extern void ARDebugMenu_set_checkMarkTexture_mBB2BB41DD309F0D7158F56C726E85F86AD1305BF (void);
extern void ARDebugMenu_get_toolbar_mA3886383D3240DE3DBE281CB016EBCDA335D7F65 (void);
extern void ARDebugMenu_set_toolbar_mAC228540FE0B8A52173592AEF03716AD564F0BB6 (void);
extern void ARDebugMenu_get_menuFont_m27FED893786F6DDEB04D792A7FCB05EC978B9CC1 (void);
extern void ARDebugMenu_set_menuFont_m6FBF97973C3D6D8B23A253BDFC639ABF35BAF1A0 (void);
extern void ARDebugMenu_get_cameraResolutionLabel_mCB4D0203D8DB11406EDCE1E0513C8946CD921551 (void);
extern void ARDebugMenu_set_cameraResolutionLabel_mC1A6F0519A7F52AF2A0A3AADBB10CE87D488B6C6 (void);
extern void ARDebugMenu_get_cameraFrameRateLabel_m9FF008BF41AF441B37982468F39B643006DDA0E9 (void);
extern void ARDebugMenu_set_cameraFrameRateLabel_m9DC6A44C13719EFFD78C2F9B6A2E0B86793D2356 (void);
extern void ARDebugMenu_get_cameraDepthSensorLabel_mEA7B077A789E1C98FA6A3AADB5F45CA70C12A394 (void);
extern void ARDebugMenu_set_cameraDepthSensorLabel_m1C52F367CDF7C8ED15FFDE93DD4FF8E5C7AE647E (void);
extern void ARDebugMenu_get_cameraConfigurationDropdown_m4F7018130C23AE99CCDA8020125CCAB94A409634 (void);
extern void ARDebugMenu_set_cameraConfigurationDropdown_mC40D11896E7B00F3678FCAE8B8E0BF96095B2A1D (void);
extern void ARDebugMenu_Start_mF7993FB02817243DD5CDCCDEB4AFC7C466D51B60 (void);
extern void ARDebugMenu_CheckMenuConfigured_m6014B307D602CA4A889B4E9FAEBC311A7D111886 (void);
extern void ARDebugMenu_OnEnable_mD58FD60A713AFD5E08BF2B826A119FC25DC52742 (void);
extern void ARDebugMenu_OnDisable_m3A6F9167E65992B233870D99BB4C8E9FF5394EB8 (void);
extern void ARDebugMenu_Update_m8D04A8338E3AA4315BCD6C722601757308297717 (void);
extern void ARDebugMenu_LateUpdate_m65A57ECDD231E99F55C5C34404BCD69F3ADB24E9 (void);
extern void ARDebugMenu_InitMenu_m47EA4DA919CBFDDFFAC22FE0BCD1AA6CA8A1C51D (void);
extern void ARDebugMenu_ConfigureMenuPosition_m21B976B1BF4847FC4A66E984096061F54D284A0C (void);
extern void ARDebugMenu_ConfigureButtons_m7473E9227B9A55D8F11C1C62A4681496B59C6356 (void);
extern void ARDebugMenu_DeregisterUIListeners_m80D1D95E645F4DEC248CF0C5EC72358F402DA799 (void);
extern void ARDebugMenu_ShowMenu_m5D24D6FC775B03E1505447E71F76418613DEF86E (void);
extern void ARDebugMenu_ToggleOriginVisibility_mC8E861AB27C07B861578DDCB3AD4EFEAAD3AAF8E (void);
extern void ARDebugMenu_TogglePlanesVisibility_mCDC25C7BAF2A64E4C16F0782D171D9E869245471 (void);
extern void ARDebugMenu_TogglePointCloudVisibility_mD728FD954EA73722156906A95BB2C046CA52DF18 (void);
extern void ARDebugMenu_ToggleAnchorsVisibility_m904AAA5D760DF956B3AD4F1A63BE7D3B188FD5DF (void);
extern void ARDebugMenu_FadeToastDialog_m3BA93AEA5FBE8BA61227918E8F5E1DC9962C33A2 (void);
extern void ARDebugMenu_SetupConfigurationMenu_m42CC629EBEB5074AF9B5FB626E02450F4651D6DA (void);
extern void ARDebugMenu_DisableToolbarButton_m17FE4E45DCF2FD9B6450943DAC7EA55924FCA688 (void);
extern void ARDebugMenu_CreateConfigurationGraph_m3819A23B1869B2150E2559E480A8BE3993E3F0E1 (void);
extern void ARDebugMenu_GetSessionSubsystem_m05ECCFEAE7AB5D1436035C208AA260366B8726E3 (void);
extern void ARDebugMenu_CreateBackgroundColumn_m5D2CA28F243A010419A18C756750947D37A4065F (void);
extern void ARDebugMenu_HighlightCurrentConfiguration_mAF71E95E416877F2EEB5C449742D1B072F3C6A50 (void);
extern void ARDebugMenu_FollowCamera_m8EA92021B40CA00DB5E3B1087675802A8AA43983 (void);
extern void ARDebugMenu_OnPlaneChanged_m657A60AC2F55B12E70F081EE27EA967DDCDCF687 (void);
extern void ARDebugMenu_OnAnchorChanged_m4BC324913AA1E0779649049CFF0E678660253C60 (void);
extern void ARDebugMenu_OnPointCloudChanged_m0D596AED1110701F82EBC436B9FCB8A6AE6E6329 (void);
extern void ARDebugMenu_GetOrCreateLineRenderer_mC0082E9019FBAEA5E0C53EB9DF18DE6BA7CEB478 (void);
extern void ARDebugMenu_GetOrCreateAnchorPrefab_m158EEB290F5A40F5614F4DC84A672FCB530A2F77 (void);
extern void ARDebugMenu_CreateOrUpdatePoints_m7BBB4FA4740C38A050013C786BDC8D91545E0715 (void);
extern void ARDebugMenu_RemovePoints_m714640EED976E552D14349D3FF5F15A3FDD728CB (void);
extern void ARDebugMenu_RenderPoints_mF255E5A894987ECA0F5A48B46838584FBD79DD65 (void);
extern void ARDebugMenu_SetParticlePosition_m4A99F3FE98FB7C254063A5095D3ACC14AAA24E4E (void);
extern void ARDebugMenu_UpdateLine_m98E9D5E72E6A0183493082B7B84419115DF572BB (void);
extern void ARDebugMenu_OnCameraDropdownValueChanged_mF183F21ACD6890CBF25E95B6DF0441E3C91A142C (void);
extern void ARDebugMenu_PopulateCameraDropdown_m371BE799DA6E2E68EF63564261E27A75836A5798 (void);
extern void ARDebugMenu__ctor_m7AE42EF12487BAD4AAFED8CE0728B4FF4592B800 (void);
extern void ARDebugMenu_U3CInitMenuU3Eb__138_0_m4B60004B0693047863F9551F27CA3F3ABB3A6A7C (void);
extern void ARDebugMenu_U3CInitMenuU3Eb__138_1_m28C8DA346CF3493D93305D15362A814B758D8BD4 (void);
extern void ARDebugMenu_U3CInitMenuU3Eb__138_2_m1B6BBAD3EDAD0EFA520A9687A0430954ADC88D59 (void);
extern void ARDebugMenu_U3CInitMenuU3Eb__138_3_m14B2B2F0FD61F5E5F49968D93E63B9AC3175001E (void);
extern void ARDebugMenu_U3CInitMenuU3Eb__138_4_m46F6411A585219CB316CB61A5C107345008BAE4C (void);
extern void ARDebugMenu_U3CConfigureButtonsU3Eb__140_0_mDCA8551319A3D86DD6F88CEE0CC9111616149B9F (void);
extern void ARDebugMenu_U3CConfigureButtonsU3Eb__140_1_m057C71CC0608C9F6F03FDC05D1AB5E442ED1A749 (void);
extern void ARDebugMenu_U3CConfigureButtonsU3Eb__140_2_mD8401BDF55C056700424458D5019BE4A5FB524A1 (void);
extern void U3CU3Ec__DisplayClass140_0__ctor_m3ED6432B0598D40453EFC23919F27D385D43C199 (void);
extern void U3CU3Ec__DisplayClass140_0_U3CConfigureButtonsU3Eb__3_mD2F9CED59BA60A1983C54526646978ABEE053A38 (void);
extern void U3CFadeToastDialogU3Ed__147__ctor_mFF262D31E0755AE69B3302AC02AA2C7A8A944712 (void);
extern void U3CFadeToastDialogU3Ed__147_System_IDisposable_Dispose_mF753B23F4C4BDCA2F4C673A571193934ED87C974 (void);
extern void U3CFadeToastDialogU3Ed__147_MoveNext_m9DDA364B94B070B8689DF212B3F0A1D4A8C47727 (void);
extern void U3CFadeToastDialogU3Ed__147_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m008D9762519F52D0101C25BAF7D1F210ACF83B0B (void);
extern void U3CFadeToastDialogU3Ed__147_System_Collections_IEnumerator_Reset_mE225D44E7D14E46E41D4009FF990FF560733009C (void);
extern void U3CFadeToastDialogU3Ed__147_System_Collections_IEnumerator_get_Current_mE8BFCA8E4FAB6D3627AED49525898B7D7D24AD93 (void);
extern void AREnvironmentProbe_get_environmentTextureFilterMode_m92C6377EA4F61A593CA82BE8028FE707011AD2C1 (void);
extern void AREnvironmentProbe_set_environmentTextureFilterMode_m06B80A36CCCFA8CBD770FA638CBD5B6833A16B40 (void);
extern void AREnvironmentProbe_get_placementType_mDC47CC6DF864F84A227E7EAEFF90976E71DAAEE8 (void);
extern void AREnvironmentProbe_set_placementType_m88E37666B552F5D26A658CC799041589690B1AC0 (void);
extern void AREnvironmentProbe_get_size_m7F165EE19D2AD44A25EC017CC5D319B150C2F942 (void);
extern void AREnvironmentProbe_get_extents_mA795818D4A5D96B96FBCBC16B811B520F21741AB (void);
extern void AREnvironmentProbe_get_nativePtr_m469302AC396B6CB4F337E7D4708CAAF5C19CB5D9 (void);
extern void AREnvironmentProbe_get_textureDescriptor_m83F1A3C0BE6CC6F7577258B0079B7187B376CBDE (void);
extern void AREnvironmentProbe_Awake_mE25DD39C415F594D863D6790DC0D88FAED586DB1 (void);
extern void AREnvironmentProbe_GetTrackablesParent_mBFBC7FBA4FEC0D68C6B4D132FEFB0B9FE077D880 (void);
extern void AREnvironmentProbe_OnAfterSetSessionRelativeData_m029574C26A74DDC5EE20636BB01034D4A50D583C (void);
extern void AREnvironmentProbe_UpdateEnvironmentTexture_mDDDAB7FC75929880637A253DE1B0688F614374A7 (void);
extern void AREnvironmentProbe_ToString_m48F93ED6D32DE124123C0DFE83D7FEE55AA3546D (void);
extern void AREnvironmentProbe_OnEnable_m49E68299AFE6FF15CED372D6187A07D68CE84D0B (void);
extern void AREnvironmentProbe_Update_mDADAFB19F0B5A3A73DEB83F897F474B36F6BF99D (void);
extern void AREnvironmentProbe_OnDisable_mE50C0A9638259ED5870038F1A3A6AB8F1AD7AE73 (void);
extern void AREnvironmentProbe__ctor_mB58D3CE9CCF895B0E1C829B643C996E3AEF5699E (void);
extern void AREnvironmentProbeManager_get_automaticPlacement_m83BBBB2F7F9A8FECE8B44F73502C873474BA8026 (void);
extern void AREnvironmentProbeManager_set_automaticPlacement_mE2059D886D13DA19FAADE1AC7D5EACB9902EE5C1 (void);
extern void AREnvironmentProbeManager_get_supportsAutomaticPlacement_m7D382800D518CD11997DD971312055F79A3C1CF6 (void);
extern void AREnvironmentProbeManager_get_automaticPlacementRequested_m543115D74E87157FDB6F43876D5C22A512E252B9 (void);
extern void AREnvironmentProbeManager_set_automaticPlacementRequested_m432779702A1ACE667BA0B697F902E33121EB71DF (void);
extern void AREnvironmentProbeManager_get_automaticPlacementEnabled_m3EB4F6061CC0994BEA30C653BD5A25187EA47EF5 (void);
extern void AREnvironmentProbeManager_get_environmentTextureFilterMode_mA1F5EB09E8881D4187B81F1A29B1753FF0AB7846 (void);
extern void AREnvironmentProbeManager_set_environmentTextureFilterMode_m76E7C9A7C13E3C849AD8701C363C6F4902BFE51C (void);
extern void AREnvironmentProbeManager_get_environmentTextureHDR_m06EABE61A6739A6134B0D3499CDB46E60C25124F (void);
extern void AREnvironmentProbeManager_set_environmentTextureHDR_m0C05AD9E24BCB7229787138410FFAD6874584960 (void);
extern void AREnvironmentProbeManager_get_supportsEnvironmentTextureHDR_mD9F3A566A8B93C40DB8C4D83C379548FAE40280C (void);
extern void AREnvironmentProbeManager_get_environmentTextureHDRRequested_mEA9DE2473292137FB9661EB986675F943876CF10 (void);
extern void AREnvironmentProbeManager_set_environmentTextureHDRRequested_mB18115A2A309F3E0A037F310EFC91327639FFC29 (void);
extern void AREnvironmentProbeManager_get_environmentTextureHDREnabled_m12AEFA87AF6EA74DDAA357735978115B9E2C6579 (void);
extern void AREnvironmentProbeManager_get_debugPrefab_mD6F19E1518C49C7080F3267140CAABFD86E95908 (void);
extern void AREnvironmentProbeManager_set_debugPrefab_mAD3B4C8AAB07D3E6ACD5C73FD8AD08E6B4471896 (void);
extern void AREnvironmentProbeManager_add_environmentProbesChanged_mADB9F6A00161EF9FECCFF0C76FC346E5A6321A96 (void);
extern void AREnvironmentProbeManager_remove_environmentProbesChanged_mABD9405FD4C3272C46B79CA7586901101C243FC7 (void);
extern void AREnvironmentProbeManager_GetEnvironmentProbe_m376D4F69F5F74C54A05A37E846331FE91C0C39E2 (void);
extern void AREnvironmentProbeManager_AddEnvironmentProbe_m9732DBDE74395B58377BCFD261234CDC1CCD2AB9 (void);
extern void AREnvironmentProbeManager_TryAddEnvironmentProbe_mFCA56125452C0EC5C749666FCE931F3508F70FFF (void);
extern void AREnvironmentProbeManager_RemoveEnvironmentProbe_m018D89EAFFECEFF2AFBE3F779B56C1FDEBCC6A35 (void);
extern void AREnvironmentProbeManager_TryRemoveEnvironmentProbe_m0C4DC46BAB27B6E8D998EE6145AF3646C6CA3BC9 (void);
extern void AREnvironmentProbeManager_get_gameObjectName_mBA5365658D8161D25501B5021F430C6A46419B85 (void);
extern void AREnvironmentProbeManager_GetPrefab_m0A7F459227BA94846FD236E9EBF76C46A8561C37 (void);
extern void AREnvironmentProbeManager_OnBeforeStart_m4A900DAA58DC7AFE0267719DD5EA83E65B5D2F49 (void);
extern void AREnvironmentProbeManager_OnDestroy_m6DE8F413FC37199204D4FD23DA48878DBE583F23 (void);
extern void AREnvironmentProbeManager_OnTrackablesChanged_m7B80003F63B937537625F138BC429CDDCBA92ED7 (void);
extern void AREnvironmentProbeManager_OnCreateTrackable_m0609B6E9F1E2D5B40EF204FB97B90AC6B3099A56 (void);
extern void AREnvironmentProbeManager_SetAutomaticPlacementStateOnSubsystem_m652A140E012CB9B945EDCAFD56E95BEA312C67DA (void);
extern void AREnvironmentProbeManager_SetEnvironmentTextureHDRStateOnSubsystem_m6EDC7E710689568ADE736B19D124A44A124AB3DC (void);
extern void AREnvironmentProbeManager__ctor_mD60AA1F4A8CD9BA3575D8485B37E86994CF97C07 (void);
extern void AREnvironmentProbesChangedEvent_get_added_m5EB8CAE059E5A0F0A84A11866F2343F630B7C997 (void);
extern void AREnvironmentProbesChangedEvent_set_added_mD254DC5C2E0B0AC8D0F604829177E92AAA36DB98 (void);
extern void AREnvironmentProbesChangedEvent_get_updated_mFFE292486A4D2DB4385C70FAE94EB6048CBB99F3 (void);
extern void AREnvironmentProbesChangedEvent_set_updated_m8346F2C232C68965428428BFC1D42EF188390D27 (void);
extern void AREnvironmentProbesChangedEvent_get_removed_m0D5B42BCAD8408B1FC0DF82E24C4EAF1D8ADF40A (void);
extern void AREnvironmentProbesChangedEvent_set_removed_m1C122637075CAC8EE8D30678068F20C10F9805B8 (void);
extern void AREnvironmentProbesChangedEvent__ctor_mA834200E5CC7AEB6D93ED5B987F19D24A9C82944 (void);
extern void AREnvironmentProbesChangedEvent_GetHashCode_m1F82F2279F41834A46A159D45D9FA4C37ABDEFF6 (void);
extern void AREnvironmentProbesChangedEvent_Equals_m5DC12B1B83E64882261F789A0651ADA40D58D7C3 (void);
extern void AREnvironmentProbesChangedEvent_ToString_m66B3C7111A9054FAB69BABF82CF0789974FBA048 (void);
extern void AREnvironmentProbesChangedEvent_Equals_mAF37E53C6392E0457299A76EE7CC5D805BDF4D64 (void);
extern void AREnvironmentProbesChangedEvent_op_Equality_mFF839F4FC56E55C561CF427006B2CACB38EE588B (void);
extern void AREnvironmentProbesChangedEvent_op_Inequality_m674DEA826D1570F1075F8EECA67BB2A1C9B356F0 (void);
extern void ARFace_add_updated_m20975C4A22DDFCC57D15C0205754C3902A699738 (void);
extern void ARFace_remove_updated_m289BD0DA8FE0FF5A2936B6FD21FB4895AFB61499 (void);
extern void ARFace_get_vertices_m5248E2AF1A5377DF265F89D06A0DAC2DBA27504C (void);
extern void ARFace_get_normals_m915D6684CDD31EEF5868D000AB58A106CA8AD725 (void);
extern void ARFace_get_indices_m99D2797D6B0BEEB096FBDFB0429FA42B6E4BC4F2 (void);
extern void ARFace_get_uvs_m2A17FCE4279C523F03B08C1A33F4F32E3FFA644E (void);
extern void ARFace_get_nativePtr_mF951F5F0845138234A1A8E46B1C038638FE01C21 (void);
extern void ARFace_get_leftEye_mF83D665DDA85EA92A4032B5AD3F73D5F7CA94349 (void);
extern void ARFace_set_leftEye_m5AF65F3CC81D824E048B71D04D03B1AEA7E94826 (void);
extern void ARFace_get_rightEye_m7388FC86889DD13F557A9BE697CAD20B160DB29E (void);
extern void ARFace_set_rightEye_m91F356CE6B8E1AB5F44B92EEE0AAB006FB6C0BFD (void);
extern void ARFace_get_fixationPoint_m6CFEF4FF10D226A45525EC86F2AD14DE1722D0D8 (void);
extern void ARFace_set_fixationPoint_m33F919437C4FA404DD86068AD29987190A413224 (void);
extern void ARFace_Update_m0DE4EC42F26E5CEE6E665DB842DEED87D5432219 (void);
extern void ARFace_OnDestroy_mABE9113FB97FC55BACF6E4962E067BCADE91B264 (void);
extern void ARFace_UpdateMesh_m1F3D6373D09116FDA91270BB7832AA022A7EC524 (void);
extern void ARFace_UpdateEyes_m3C724563A1E7043E38D1B896B28D239C69DC3FB6 (void);
extern void ARFace_UpdateTransformFromPose_m942F3434C90AF500AB693E69FD8966E15F1EAFCA (void);
extern void ARFace__ctor_m3DC462489C771F46E11BA8D18446B2751A5E1687 (void);
extern void ARFace_U3CUpdateEyesU3Eg__CreateGameObjectU7C29_0_m3299BEC27F75349075882DDACF55A2B7FBA7709C (void);
extern void ARFaceManager_get_facePrefab_mB97FB7247DA2EAFCC642D71C1826216B1C2F4380 (void);
extern void ARFaceManager_set_facePrefab_m817BF61BC85C89439E7740C986274D3967351427 (void);
extern void ARFaceManager_get_requestedMaximumFaceCount_m155E3DDDFDBAF3F52FF2276B23F27A8BF39F7D83 (void);
extern void ARFaceManager_set_requestedMaximumFaceCount_mD5CA3BD35E3AAD9423A3CC88921D56083E727278 (void);
extern void ARFaceManager_get_maximumFaceCount_m6854F8AB7381DFA7C5E4534B7EB3CC48E1162AF2 (void);
extern void ARFaceManager_set_maximumFaceCount_m5B877FE343E0BD0348A9E60BA9083F24B221FD60 (void);
extern void ARFaceManager_get_currentMaximumFaceCount_m1CFF61ED04271FC2E4890B6F8AD9A345E3D643DC (void);
extern void ARFaceManager_get_supportedFaceCount_m2F343026AE979F36529B713A9B6A44F2FE3B57C2 (void);
extern void ARFaceManager_add_facesChanged_m5EA6C3526D8E44028C6DCF7A6297D7EAE240105E (void);
extern void ARFaceManager_remove_facesChanged_mBFBA84C023749078DC7D801945CF83373B336DCC (void);
extern void ARFaceManager_TryGetFace_m607F9345904F06EB26F0220BE3AED75A14FBFD5A (void);
extern void ARFaceManager_OnBeforeStart_mD1C6B8B194CB252561CEA3866795CA15E29669FB (void);
extern void ARFaceManager_OnAfterSetSessionRelativeData_mB7213CC0AFD22EC8393C01C2F7C234B916238A95 (void);
extern void ARFaceManager_OnTrackablesChanged_m50B1275196ECF6049BC1F8FBA6C628C727F35B7D (void);
extern void ARFaceManager_GetPrefab_m2E5C6E8D3DD0338DE887ABD3EA61A08C5499B682 (void);
extern void ARFaceManager_get_gameObjectName_m1A433377D66F0B7C217D958166CEDB65813C37FF (void);
extern void ARFaceManager__ctor_mC4AB2DA06A27538F600B5F7BA56D2FA3FB574185 (void);
extern void ARFaceMeshVisualizer_get_mesh_m8DFF3C9F6FA8233C67918831AC6640C902CC0317 (void);
extern void ARFaceMeshVisualizer_set_mesh_m042183A8A5919F50C53C0CE5ACB0B38D977BD288 (void);
extern void ARFaceMeshVisualizer_SetVisible_m5FEE4245326A5DA13A9C9C6FAE59E5B791751D37 (void);
extern void ARFaceMeshVisualizer_SetMeshTopology_m0E862DE0D7AC2FB6A4649770204784CC6B8812CC (void);
extern void ARFaceMeshVisualizer_UpdateVisibility_mF3E693708B1CDA925E2458D1B9B3C12C3AFE4416 (void);
extern void ARFaceMeshVisualizer_OnUpdated_m85A34C40F5075FC4326C8BE6369EDA4A337CC06F (void);
extern void ARFaceMeshVisualizer_OnSessionStateChanged_mBEBEE52B39FBE5D1FD96B68948A0B9D537A9C943 (void);
extern void ARFaceMeshVisualizer_Awake_m02EFC7A7F287BC2B880E3C060E6D05050089C596 (void);
extern void ARFaceMeshVisualizer_OnEnable_mAD29EDBD3930D23869B9C3DDE68AB1BF934E3E76 (void);
extern void ARFaceMeshVisualizer_OnDisable_mED42B3DEEA4C700C7D3603519EE86456CFDDC2D8 (void);
extern void ARFaceMeshVisualizer__ctor_m8057677E2EF2E2B719EB33091E89C55288D3BDD0 (void);
extern void ARFacesChangedEventArgs_get_added_m8EA1424F8F262955AFD00664B81C15AB54EFA213 (void);
extern void ARFacesChangedEventArgs_set_added_mF3FF69CFE03334FC9582BFF06D5124961F955D43 (void);
extern void ARFacesChangedEventArgs_get_updated_m1D3587AA05E5789F796A71C3C87CD6D11BE0005B (void);
extern void ARFacesChangedEventArgs_set_updated_mAD5B3FD1E0A6058BD6328793BA0505E82B476469 (void);
extern void ARFacesChangedEventArgs_get_removed_mC31806DE526D3BECAA26C35DAE321F2DE77C335E (void);
extern void ARFacesChangedEventArgs_set_removed_m0A6A558713C45E20F0040C50763F01B1E061E387 (void);
extern void ARFacesChangedEventArgs__ctor_m4C7990AA3703F850C5B4D0CCCCEABBC0B58DB940 (void);
extern void ARFacesChangedEventArgs_GetHashCode_m67DD1456E147A37E35FF2483BC7053D59B549CF6 (void);
extern void ARFacesChangedEventArgs_Equals_m08B2E52D4FD329CAB89DC14BEF84E55274E2EAEB (void);
extern void ARFacesChangedEventArgs_ToString_m0773324E2DC3392C12286F68495590B2F1E7C057 (void);
extern void ARFacesChangedEventArgs_Equals_m32B401EBB3F1CFE364F4F2C5B7746F8D48B1DDDE (void);
extern void ARFacesChangedEventArgs_op_Equality_m851D0A36D19E2133F6E62BA9201F99844C986C3F (void);
extern void ARFacesChangedEventArgs_op_Inequality_m5AAB9E433F4D506C3FDF9BAEAA67F53515DEC13B (void);
extern void ARFaceUpdatedEventArgs_get_face_mFD21EF59457212494C80FE998D1CA8AC0F057170 (void);
extern void ARFaceUpdatedEventArgs_set_face_m0E59FF874A145B994A77250CEA675FBF8AD3019C (void);
extern void ARFaceUpdatedEventArgs__ctor_m3D4294A61EBEAF3158E5C90C57FB112020A58987 (void);
extern void ARFaceUpdatedEventArgs_GetHashCode_mCF1AF45853FADD6B4F52B43038647782B888B700 (void);
extern void ARFaceUpdatedEventArgs_Equals_m24F398E98F52EBA0DF689C392CF58291742028F9 (void);
extern void ARFaceUpdatedEventArgs_Equals_m50565F99C9CC4FF2F3122EF78D2C4DE1881E1BDC (void);
extern void ARFaceUpdatedEventArgs_op_Equality_mD62113C65CC5A0EFD17110D588D6804E35D28462 (void);
extern void ARFaceUpdatedEventArgs_op_Inequality_m2018D0A528AF15ADC1C3903D75231E3BE8A60A70 (void);
extern void ARHumanBodiesChangedEventArgs_get_added_m6FD862CC8C8281F9377D754BBAB7F3C616955D4C (void);
extern void ARHumanBodiesChangedEventArgs_set_added_mAC9FB746BD1745CB5BEDE7C2220302035CD63F77 (void);
extern void ARHumanBodiesChangedEventArgs_get_updated_mA2FD128E22BDACD09294F107AA3F2A91CFFAA550 (void);
extern void ARHumanBodiesChangedEventArgs_set_updated_mB71FDE70AD3CFC1096386A5E4260A9FFBB420C06 (void);
extern void ARHumanBodiesChangedEventArgs_get_removed_m613516A989AA48A625C79A3C309D9C1465762BCA (void);
extern void ARHumanBodiesChangedEventArgs_set_removed_m97AC3D82086A3691484D3B587A534B9D6DC385B1 (void);
extern void ARHumanBodiesChangedEventArgs__ctor_mA364D7E41518A8059F90E1EFC71FDD9F5F52B7F7 (void);
extern void ARHumanBodiesChangedEventArgs_GetHashCode_m7C9E78E03AE212095DDA1B44594F64475937253F (void);
extern void ARHumanBodiesChangedEventArgs_Equals_m0D61EF468E803F4B69514B78E7E47852B9AC39AF (void);
extern void ARHumanBodiesChangedEventArgs_Equals_m2E24676DA642BE3E29F0347454BC4CB993DCF8B4 (void);
extern void ARHumanBodiesChangedEventArgs_op_Equality_m84ECC2119180D5EEAA9BB83D63331B2F2500977F (void);
extern void ARHumanBodiesChangedEventArgs_op_Inequality_m43F5321EA5B1ED1B37378BEBEED99F9EFEB6015B (void);
extern void ARHumanBodiesChangedEventArgs_ToString_m4685EF40034A2625C670EA5813E73ED415B35431 (void);
extern void ARHumanBody_get_pose_mDF3829483679DED8DB271A45534BDE7ADB9B61C9 (void);
extern void ARHumanBody_get_estimatedHeightScaleFactor_mD8412089622125E0B045C41F9716854E3D6ADA49 (void);
extern void ARHumanBody_get_joints_m7681747651EA8107E8E9050C0E698034CCBD543B (void);
extern void ARHumanBody_UpdateSkeleton_m7BF0D10DF09F8165B5185A1D725F57B661FEE96F (void);
extern void ARHumanBody_ToString_m0026ED473E3DB7C646549F9D0BF0BEB30D3DE6E0 (void);
extern void ARHumanBody_ToString_m73369EE25D8CBE66B8C0C76F0380AF2A8850FB32 (void);
extern void ARHumanBody_Dispose_mB0423B3C4B8C64755A68F7395FE89B1ECF15F02C (void);
extern void ARHumanBody_OnDestroy_mC1DCF2852CDFBBAAD9784FBB9C80DD1EEF834114 (void);
extern void ARHumanBody__ctor_mC8D82750C6787761053A74AF2B8336CF52BF524A (void);
extern void ARHumanBodyManager_get_humanBodyPose2DEstimationEnabled_mE6DAFF825902AF1AAEF1FD53A240DBDC7E264351 (void);
extern void ARHumanBodyManager_set_humanBodyPose2DEstimationEnabled_m96F65E0BBDF38B385CAEC55358EBA5F26EA33CA3 (void);
extern void ARHumanBodyManager_get_pose2DEnabled_m796EF39EFA542FCC94B69B673C0CC7CD4C5D24E2 (void);
extern void ARHumanBodyManager_get_pose2DRequested_mF884C3A8B90D0C91A9024D93C02B88D8662612C8 (void);
extern void ARHumanBodyManager_set_pose2DRequested_m917E0D4D8DABC80FDD25693D6BC3B5B8D25331FC (void);
extern void ARHumanBodyManager_get_humanBodyPose3DEstimationEnabled_m477D538E2CDBFFEA0655203A1B432B1872D9FE09 (void);
extern void ARHumanBodyManager_set_humanBodyPose3DEstimationEnabled_mDD2989905490687FC7204836F896B9D82C0F9725 (void);
extern void ARHumanBodyManager_get_pose3DRequested_mFF8FBBCD9E4B48F3629520ED9DF07D9287700D7A (void);
extern void ARHumanBodyManager_set_pose3DRequested_mCB5DF13189EE08137FC75C2F547A3B7890509966 (void);
extern void ARHumanBodyManager_get_pose3DEnabled_m8C252BB1DDAA5E394E929FADAD5498B1D879D6F9 (void);
extern void ARHumanBodyManager_get_humanBodyPose3DScaleEstimationEnabled_m3623EB1138FB66E9C8A7883AF7280895A8D8AFBB (void);
extern void ARHumanBodyManager_set_humanBodyPose3DScaleEstimationEnabled_m917F83543C3569FD54FE7822F8CA1E061F9AFD60 (void);
extern void ARHumanBodyManager_get_pose3DScaleEstimationRequested_m5DB10D060C50FBC5BF2B073F4240A861A5375B28 (void);
extern void ARHumanBodyManager_set_pose3DScaleEstimationRequested_mBFC17BBB6B0EF302FF71B0E5EA3D1BDA60878C42 (void);
extern void ARHumanBodyManager_get_pose3DScaleEstimationEnabled_m1DB11320F84D1197947D7D49FE30E0E3187DC592 (void);
extern void ARHumanBodyManager_get_humanBodyPrefab_m93F253FF1CADEC53B9F14790A2DA799D731EA3A4 (void);
extern void ARHumanBodyManager_set_humanBodyPrefab_mE2381B0F57B0A4FB150B843C898536BFF19133FE (void);
extern void ARHumanBodyManager_get_gameObjectName_mBB3531C12ACE38C39F0B0EAEFB5ADCFC9BB0B679 (void);
extern void ARHumanBodyManager_add_humanBodiesChanged_mAD886F8E010310FD75E072CDCD0478D40659D318 (void);
extern void ARHumanBodyManager_remove_humanBodiesChanged_m868BF1A9ABD143149D3E4C4F04C6204B5768D7FB (void);
extern void ARHumanBodyManager_GetPrefab_m3239C4290C47DB76E9DF2A5BA5C4E5698C3CCBBC (void);
extern void ARHumanBodyManager_GetHumanBody_m66B477EC806934DD056B8785517AA05639136BA7 (void);
extern void ARHumanBodyManager_GetHumanBodyPose2DJoints_m96E12B6F15FDE28C5E74218CD7CDDA07A8875600 (void);
extern void ARHumanBodyManager_OnBeforeStart_mBAE43118C586308FBB93E8F0EDE382A1FC664106 (void);
extern void ARHumanBodyManager_OnDestroy_m97C60BE4C48A125058C79DA1E6295FFA55F625DB (void);
extern void ARHumanBodyManager_OnAfterSetSessionRelativeData_mF437E9691AA1BE8EA449C0C839DE2EC966E64653 (void);
extern void ARHumanBodyManager_OnTrackablesChanged_m5D027087D8927AAA2DA4941F7DD81BAC1A2920E8 (void);
extern void ARHumanBodyManager__ctor_m17FEEBB075373C95121F2B2DB9B1EA7332B6524F (void);
extern void ARInputManager_get_subsystem_m457880CC51D7BE9C9D4DE12F2473E7EAFB4C650C (void);
extern void ARInputManager_set_subsystem_mE16FA39F0E25600FBFEB5C8E58032E62479E62DD (void);
extern void ARInputManager_OnEnable_m3ABE47A517D7E51D5BFA6EE269B113CFE3AA6F36 (void);
extern void ARInputManager_OnDisable_m32F5415FBE10F4222A9F667F6689D1CCA972E666 (void);
extern void ARInputManager_OnDestroy_m283BFC5A64D52B3308325D2C028D67588CCE30D8 (void);
extern void ARInputManager_GetActiveSubsystemInstance_m9982C5A517B58D82911D73A81A7EE8476BDB49D2 (void);
extern void ARInputManager__ctor_mDA0C2F2134CD0FFCF1A34152EAB3BF5FF2AE4A6C (void);
extern void ARInputManager__cctor_mE577C81B472781AF1A6A155EDD48B7380AFE7F56 (void);
extern void ARLightEstimationData_get_averageBrightness_mCDE95FB42D807C168E187942BD9DDAB65439AE19 (void);
extern void ARLightEstimationData_set_averageBrightness_mC057F484D1EA8E3760977FE33297539B665D95CE (void);
extern void ARLightEstimationData_get_averageColorTemperature_m688BB1F18E15D7058FDEFC012451A72CD6D193DC (void);
extern void ARLightEstimationData_set_averageColorTemperature_m2B62048E88C65A904843D4A46BDAC8F764519B79 (void);
extern void ARLightEstimationData_get_colorCorrection_mF7D9D83F249587E6A1E18D845C0D521C907DF496 (void);
extern void ARLightEstimationData_set_colorCorrection_mD28E53EA20EE633541E1F9AFBB853A10AF3361BA (void);
extern void ARLightEstimationData_get_averageIntensityInLumens_m3D74CD050CDA61A97595AF8854E97C3239F159D1 (void);
extern void ARLightEstimationData_set_averageIntensityInLumens_m79B3A12B384470DCCCCEE390DE5ECCC0E1CCCE5E (void);
extern void ARLightEstimationData_get_mainLightIntensityLumens_m618D7C8A4FA8FFC3AB370DFC00596DDFA592288F (void);
extern void ARLightEstimationData_set_mainLightIntensityLumens_m4B7F856E88E7F8247B61EFEA6231720A23340B42 (void);
extern void ARLightEstimationData_get_averageMainLightBrightness_mAA37F537DB4589CB01464B6A7E9AA8AF2C6E693F (void);
extern void ARLightEstimationData_set_averageMainLightBrightness_mCE3727F3F32B8EB41503E852CFBCC20898B50D93 (void);
extern void ARLightEstimationData_get_mainLightColor_m7A17F97AC5EBEE6B3E3B093AB73F8FB904C79C3E (void);
extern void ARLightEstimationData_set_mainLightColor_m80FE4C8781175687581668E3708A899C15F278FE (void);
extern void ARLightEstimationData_get_mainLightDirection_m49A1B8B5DB38EAE356A22C1AD0A4FD671C6C1427 (void);
extern void ARLightEstimationData_set_mainLightDirection_m701A092104413621AC8320050422D4A0C94BE57C (void);
extern void ARLightEstimationData_get_ambientSphericalHarmonics_m34C8C4BB5EFDD7E2DAEDF983F11031ABFCA16C41 (void);
extern void ARLightEstimationData_set_ambientSphericalHarmonics_m4DF86D65B2095ADB18B9504CC23518600DDA2D9B (void);
extern void ARLightEstimationData_GetHashCode_m3F60EB276A24D0B1D3D7B74F0C9CBE044CC49595 (void);
extern void ARLightEstimationData_Equals_mD33829136230F81948FE58D2EB7A32E53973AD02 (void);
extern void ARLightEstimationData_ToString_m57D70F82DD05F504BB4DC8937BAE5E9B0CE686FA (void);
extern void ARLightEstimationData_Equals_m91813FC80F85190AC70FCE2827D4844F15A1BB65 (void);
extern void ARLightEstimationData_op_Equality_m146595607978F22D689834E0042DC3C91A1D7C00 (void);
extern void ARLightEstimationData_op_Inequality_m4CC745CDD06C42BF6A2F7BCE9549906B22B1AEFB (void);
extern void ARLightEstimationData_ConvertBrightnessToLumens_m09279642CD4DFF335987CE9212C0F1E82F4CA4A2 (void);
extern void ARLightEstimationData_ConvertLumensToBrightness_mDEC789CC752A2A51FBBB1C8B2C27CB6F0C615D35 (void);
extern void ARMeshesChangedEventArgs_get_added_m0D451C7DDD6CE9B7697CF3678D2F1DE440170E6B (void);
extern void ARMeshesChangedEventArgs_set_added_m6A461DFF63F7E28D7A4B02AD47629D95BBAC20B6 (void);
extern void ARMeshesChangedEventArgs_get_updated_m06E6CE944759C31B7500D0A4EF8D07D262A0626F (void);
extern void ARMeshesChangedEventArgs_set_updated_m60115D846577E418E88D4D273AD1A2ECE1D1BAAE (void);
extern void ARMeshesChangedEventArgs_get_removed_mB982A0629BEA14EC069263AD7BAB6C43CB187674 (void);
extern void ARMeshesChangedEventArgs_set_removed_mA6932589C03B43D48CB7DC1F7AA627BD85F6E396 (void);
extern void ARMeshesChangedEventArgs__ctor_m3265D79A775738B64ADFBA5B52C89DCC77B26F2B (void);
extern void ARMeshesChangedEventArgs_GetHashCode_m43CF0D8A78557CE5A2E097DF4DED87531AE07F5E (void);
extern void ARMeshesChangedEventArgs_Equals_m077E07D6856F294A374A0EFE0E38DCB958ADAAFC (void);
extern void ARMeshesChangedEventArgs_ToString_m9249BC2AF8169516627B851472F3DAFA6F067879 (void);
extern void ARMeshesChangedEventArgs_Equals_mAC06257880DA02E04E099EDE83361618D0175EEA (void);
extern void ARMeshesChangedEventArgs_op_Equality_m295B2381EBA5F89DA12BA00DBB18C7D3A6BE7E8F (void);
extern void ARMeshesChangedEventArgs_op_Inequality_m27A98E0C6192BB6CE379BC02F262DFD045075218 (void);
extern void ARMeshManager_get_meshPrefab_m4E46FECEC68D97E48BAF9E1813C374FEED7C1D28 (void);
extern void ARMeshManager_set_meshPrefab_m3ACB204D3D6382FEB944222ABFADE221BA4128D6 (void);
extern void ARMeshManager_get_density_m25870253F89E5B3D942393A4AB7F2ACAB1494156 (void);
extern void ARMeshManager_set_density_mEC198B79FA6CB2BC7637848DE8C99902BAF0E611 (void);
extern void ARMeshManager_get_normals_m0992EE7C313CD0FC0CE5BA4F4B2B6664809A0893 (void);
extern void ARMeshManager_set_normals_mC51DF99F87CC0D1248BDEC5F7469070C56C4968A (void);
extern void ARMeshManager_get_tangents_m06F3881148DEFE8AC0836785E8770F4863BBCA63 (void);
extern void ARMeshManager_set_tangents_m9B026C93E7E948C79E1ADBC0976AE461C03A38EA (void);
extern void ARMeshManager_get_textureCoordinates_m8FBD7696106413BC9753204A099BF6479D924485 (void);
extern void ARMeshManager_set_textureCoordinates_mC1A9E2D2AF4BB3D5D0D59D15F626DD40731EDADA (void);
extern void ARMeshManager_get_colors_m9D877332BA40A17504F0DD279AAF66A50FE819AB (void);
extern void ARMeshManager_set_colors_m719499B8B8C8B4F707935737AE3A19D7BF025D9F (void);
extern void ARMeshManager_get_concurrentQueueSize_m30020A42624987FD3413EA2FDB4F3921634245BE (void);
extern void ARMeshManager_set_concurrentQueueSize_mB0214487805F504CD8C857ACEDD036674A6B0296 (void);
extern void ARMeshManager_add_meshesChanged_m61D881D3F3A1AD033E8D86F4016181A8945C54CF (void);
extern void ARMeshManager_remove_meshesChanged_m5F287FD4EBF77D84842BAB9B16AC385A55F6B2EB (void);
extern void ARMeshManager_get_subsystem_m75B33162C77E20C5BA6A073A596141A1E0CA05E9 (void);
extern void ARMeshManager_get_meshes_mE4409AFCBEFF5A5BAEE4E4F3093C4D1129D5BB0B (void);
extern void ARMeshManager_DestroyAllMeshes_m1184F2FC6AEACED0111439C5F280C2441C736BEF (void);
extern void ARMeshManager_GetXROrigin_mD884C170E809D1604629268DBDF03B018A87E8DB (void);
extern void ARMeshManager_SetBoundingVolume_mAAD59BF3626DB86F65B0EE49281F1E2BE39665EC (void);
extern void ARMeshManager_OnEnable_m6A3BA5F1FEC026E8F23E05A5EED8FA02AC2338BB (void);
extern void ARMeshManager_GetActiveSubsystemInstance_m2E3C5792174F92F8ED27509326C34117590CFE8B (void);
extern void ARMeshManager_OnDrawGizmosSelected_m14402AA8D71260A40872F587B23E0F06B2FAF3FB (void);
extern void ARMeshManager_Update_mF1E202ED19E5674811221EE0A387AAE0800090D4 (void);
extern void ARMeshManager_Generate_m973A5CBB0E9DFA436954640C44B3FD4F11EDB25C (void);
extern void ARMeshManager_OnMeshGenerated_mD390561CF0C2B19DC733D4F8E8DF54D6D5E1FA6A (void);
extern void ARMeshManager_GetOrUpdateMeshTransform_m245D988398C52CBD4316D062BFD7FE08EF71A3CC (void);
extern void ARMeshManager_SetMeshTransform_m7433A3AEDE9769BB39E1F61E4981F8D9EFF2956C (void);
extern void ARMeshManager_UpdateMeshInfos_m599C18B6478E18F60220FC9656443FC502AB2A1E (void);
extern void ARMeshManager_OnDisable_m90ABCBF834BEB7F2C8E75B3DA4ABBAD7245B0695 (void);
extern void ARMeshManager_OnDestroy_m9480FE5BD7281E4C704B843F48B1353B2F2859FB (void);
extern void ARMeshManager_GetOrCreateMeshFilter_mD7A54DC382A78367262C8A74881C8D8BFBE09AA6 (void);
extern void ARMeshManager_GetTrackableId_m479D30109452143F4680A8FA4DDD25E05ED374FD (void);
extern void ARMeshManager_GetLegacyMeshId_mB432B84C3E2284CD2413939841B86DF87CA50E22 (void);
extern void ARMeshManager_Awake_mEDF1A889F6F28DC25592058BE4ED23DA7045A365 (void);
extern void ARMeshManager__ctor_mDBECCA216201478248BB2687C428CA89F04D38D3 (void);
extern void ARMeshManager__cctor_m7422CBD18B7A260C0BD7A8AF1932D9016EA66D52 (void);
extern void TrackableIdComparer_Compare_m5CF79B428F76D82D2D84715130B3BF8DFEBE6BFE (void);
extern void TrackableIdComparer__ctor_m50920B0DF39D8572FA5F22B2C22D6777C838C1F3 (void);
extern void AROcclusionFrameEventArgs_get_textures_m71DA887B2AF42DDB9500E82DA7A896B0199C1F74 (void);
extern void AROcclusionFrameEventArgs_set_textures_m5BB4042AD796E509530E064073A6AE707FC7DE99 (void);
extern void AROcclusionFrameEventArgs_get_propertyNameIds_m44F7274F021BE5B6E973ED4F59E13328656C5CD0 (void);
extern void AROcclusionFrameEventArgs_set_propertyNameIds_m597B2EF5AA77522294E0A6B0EE5058BECCF92B99 (void);
extern void AROcclusionFrameEventArgs_get_enabledMaterialKeywords_mD4B62FFB1EAFA72FC1EC69A533AE6B726078264E (void);
extern void AROcclusionFrameEventArgs_set_enabledMaterialKeywords_m1F3B100DB81295D6D99FB40477E9B71A597714B1 (void);
extern void AROcclusionFrameEventArgs_get_disabledMaterialKeywords_m4348A651EED14C4FE7A9DD5E941ED496FBBFDA8A (void);
extern void AROcclusionFrameEventArgs_set_disabledMaterialKeywords_m1857AC4F635D5122AA0C14D4DD48F6E62506EF46 (void);
extern void AROcclusionFrameEventArgs_GetHashCode_m7115792D710F0C7C0FB985857E741C600F170EF5 (void);
extern void AROcclusionFrameEventArgs_Equals_m569A3536BE31ECC807124FB95DE73677BB762C52 (void);
extern void AROcclusionFrameEventArgs_Equals_mF6121B9B23AF4C030B23B2CCD73F70597C47FE02 (void);
extern void AROcclusionFrameEventArgs_op_Equality_m8366637D6E50819E63713F766E31159D136CE3C8 (void);
extern void AROcclusionFrameEventArgs_op_Inequality_m6437EE200543587A29D368ECEF36FFABC91BB645 (void);
extern void AROcclusionManager_add_frameReceived_m3477ACEB4A153BFA23B040C82101CCBECD8738E7 (void);
extern void AROcclusionManager_remove_frameReceived_m253009A31AAE711BED3A3480C36270DBA85043C9 (void);
extern void AROcclusionManager_get_humanSegmentationStencilMode_m6E99ED17943FF4CE8B78B6F08148846F0A36D7CA (void);
extern void AROcclusionManager_set_humanSegmentationStencilMode_mD2D2761E279E8189A31F235BC1D5B2F632AE33F8 (void);
extern void AROcclusionManager_get_requestedHumanStencilMode_m683B227E011266D1D7DB77E236F2ABF555F93A0E (void);
extern void AROcclusionManager_set_requestedHumanStencilMode_m73D9A0E9621AE3C80F659059F3DB6A23EE0B64F1 (void);
extern void AROcclusionManager_get_currentHumanStencilMode_m57E30562049653C86AF90C19B4B53596535ED805 (void);
extern void AROcclusionManager_get_humanSegmentationDepthMode_m840E71ECDFEF90B7546D107172F20CE2DE0BB878 (void);
extern void AROcclusionManager_set_humanSegmentationDepthMode_m12706BC43248A17F89EE6D43045746E5DF67D54A (void);
extern void AROcclusionManager_get_requestedHumanDepthMode_mADCCAF2B841C3D36FE1E9222924C9D283AFCEB28 (void);
extern void AROcclusionManager_set_requestedHumanDepthMode_mB83BA91C222F7C576C09C79D63FB7C8DDD6D1642 (void);
extern void AROcclusionManager_get_currentHumanDepthMode_mDB6A4FB40D47960B249F215311D1F9121652A525 (void);
extern void AROcclusionManager_get_requestedEnvironmentDepthMode_mE0BBD4AB6B70A9F75EC19206AB9B3F690CE05C8B (void);
extern void AROcclusionManager_set_requestedEnvironmentDepthMode_mDF1DA23F3A3B7C70F3802B0A7FC58A47D272D45F (void);
extern void AROcclusionManager_get_currentEnvironmentDepthMode_m56A466D29413F131D2B41A2E30B19E51A4D66207 (void);
extern void AROcclusionManager_get_environmentDepthTemporalSmoothingRequested_m0E0FBB92E6C94EC19A6FF3D9A80524A38B7CDAE7 (void);
extern void AROcclusionManager_set_environmentDepthTemporalSmoothingRequested_m3E5C48FEB03F83D916D060B8F6940006EC2FB621 (void);
extern void AROcclusionManager_get_environmentDepthTemporalSmoothingEnabled_mD066573D62D0F9328737D9767E3680D577DEA525 (void);
extern void AROcclusionManager_get_requestedOcclusionPreferenceMode_m5E9247118E3647775717609C06D2EBF0DCDFA4F3 (void);
extern void AROcclusionManager_set_requestedOcclusionPreferenceMode_m0D70CC75D6DC39BEF3F79E7CFCAE47A5BBC7CDAF (void);
extern void AROcclusionManager_get_currentOcclusionPreferenceMode_m56F4592CEBBCC1CE1831C71D277765A4F4D95676 (void);
extern void AROcclusionManager_get_humanStencilTexture_m4F81CA6ABBFE6326872B0545E826766F58ADC86F (void);
extern void AROcclusionManager_TryAcquireHumanStencilCpuImage_mD58759667706F23FE88E457AB47603456E99B68D (void);
extern void AROcclusionManager_get_humanDepthTexture_mF9B84E503E3B394AAE1544B07BD3BEB1FE3EFEDB (void);
extern void AROcclusionManager_TryAcquireEnvironmentDepthConfidenceCpuImage_m03C58FBD4DC0D364CF72219723E76073DFBE30CC (void);
extern void AROcclusionManager_get_environmentDepthConfidenceTexture_m58B59DBF5E0ADED43B1810FF81F7CDE53F6AB768 (void);
extern void AROcclusionManager_TryAcquireHumanDepthCpuImage_m477918E28097A23044EFE097741B1D2B2FA7E99D (void);
extern void AROcclusionManager_get_environmentDepthTexture_m8D961A8DB1023D5B4340A2020C51D75A21C5D454 (void);
extern void AROcclusionManager_TryAcquireEnvironmentDepthCpuImage_mB57569CD3F57DC88454AA01E2A735C183F0F0958 (void);
extern void AROcclusionManager_TryAcquireRawEnvironmentDepthCpuImage_m988381DD34936EE0FAA7F1177358C97AAE6AB6E4 (void);
extern void AROcclusionManager_TryAcquireSmoothedEnvironmentDepthCpuImage_mB1BFFCB6A0FEF5AE4298224D448F9C6F7A551EDF (void);
extern void AROcclusionManager_OnBeforeStart_m913D2B7D1CEE94816C124C7A16E66597A7997CB1 (void);
extern void AROcclusionManager_OnDisable_mD945E4ADB6073F286D5BDECD78CD13B064378B2E (void);
extern void AROcclusionManager_Update_m97CCC8D1EFFA544B65D1E0F02C2DB099F267934F (void);
extern void AROcclusionManager_ResetTextureInfos_m863803DE85E0715F6CE0474BFF58BAE27A3E4C53 (void);
extern void AROcclusionManager_UpdateTexturesInfos_m86684113F8D996C1B75A3FF1B81DB0BAD08A6E1F (void);
extern void AROcclusionManager_InvokeFrameReceived_m95D3C9962774B0221B8448A55D14404BDE87A7C3 (void);
extern void AROcclusionManager__ctor_mEA736ACC071C2B0266F1F004BAED38D250958324 (void);
extern void ARParticipant_get_nativePtr_mCA91843BF20512073D04C0F85133A21EBD5BEE87 (void);
extern void ARParticipant_get_sessionId_m6BA7B78F940CD77E0E4E7F979C46DF26D8E7CDE8 (void);
extern void ARParticipant__ctor_m543621608CB954414B30C2FA289E0F92F6D89CA2 (void);
extern void ARParticipantManager_get_participantPrefab_m33C930AA127180879E5E0E7FF2A0E9DC7D7CC1C6 (void);
extern void ARParticipantManager_set_participantPrefab_mC2A5D19A592EC6942A59C751C03503AF1363DF45 (void);
extern void ARParticipantManager_add_participantsChanged_m7CB947B046B890EFA75726000DBECA7E86FE8775 (void);
extern void ARParticipantManager_remove_participantsChanged_m8D99AE38D87E378337E82DC2359079A2448D447F (void);
extern void ARParticipantManager_GetParticipant_m31EDC2D3AA0826E3863536C3BDBBB56234E5AA33 (void);
extern void ARParticipantManager_GetPrefab_mD5D1DBF7DD43563F97DCCCFAF2ACB01970FF9FB2 (void);
extern void ARParticipantManager_OnTrackablesChanged_mB07256F2590691B4302FAC7D625FEE1103831600 (void);
extern void ARParticipantManager_get_gameObjectName_mB1EBFFD4D5FD2A96DFA1F96CCF3241B36FF1A883 (void);
extern void ARParticipantManager__ctor_mCD87C3FD6845E88B8E23CD2813C75E868D22F6A2 (void);
extern void ARParticipantsChangedEventArgs_get_added_m06DA0DBF00575BB5D41A8AFE385ADAB9866BA678 (void);
extern void ARParticipantsChangedEventArgs_set_added_m166787C372D8632EF3F075B63E1E58DF3665DE4E (void);
extern void ARParticipantsChangedEventArgs_get_updated_mA0EC697655BDFEEB28064579FDD67DD166A2AE65 (void);
extern void ARParticipantsChangedEventArgs_set_updated_m082B2C0864682385CDDD7C89DCF8C135A1E2CBAD (void);
extern void ARParticipantsChangedEventArgs_get_removed_m6BBF2ABF2E71DB02A70122927AAD83E3AB35A77F (void);
extern void ARParticipantsChangedEventArgs_set_removed_mD046A770079A3D9635A832DC2A7AA4997FBCCA60 (void);
extern void ARParticipantsChangedEventArgs__ctor_m3D9C5565D3F7AB1077A197397CCAF28E0DF360D4 (void);
extern void ARParticipantsChangedEventArgs_GetHashCode_m155622653A93F1E5021158B3B506D5D04460D0C5 (void);
extern void ARParticipantsChangedEventArgs_Equals_mF5F4985298C2A01B6DA6119757ED5964BA7A1C44 (void);
extern void ARParticipantsChangedEventArgs_GetCount_mAAA0EAA40FE5210B3072EC5C8CAA435267B89FA8 (void);
extern void ARParticipantsChangedEventArgs_ToString_m6AE6DC3EE171F559B60C06C734EEF2CD39546821 (void);
extern void ARParticipantsChangedEventArgs_Equals_mD086A71DE5FE4C60061792F37BC11F17D14871C3 (void);
extern void ARParticipantsChangedEventArgs_op_Equality_mF465D7F7C9ECEBED9B9969296A80D70BF8A12F5E (void);
extern void ARParticipantsChangedEventArgs_op_Inequality_m53BC1E056B9C431ABCEEE0BF582AE0F50A37BBA3 (void);
extern void ARPlane_get_vertexChangedThreshold_m2D434A45D29E764DA9E36E48C8837F3598A971BF (void);
extern void ARPlane_set_vertexChangedThreshold_m5DAEB406D9A02A3DB5D5D91C0FE217785051A53C (void);
extern void ARPlane_add_boundaryChanged_m6A9CA2ADF4742C004EF7BCFD48A7C820320616AA (void);
extern void ARPlane_remove_boundaryChanged_m6F71EEF2CD3C71AF069C5FE6CD993906BD8A9A10 (void);
extern void ARPlane_get_normal_m12A4F62B15333D04011C0DBD6377FA1E9B3C60B7 (void);
extern void ARPlane_get_subsumedBy_m18A95DC877D1F172CC50522A716D45DA21790027 (void);
extern void ARPlane_set_subsumedBy_mA9EDF95F501F0B757836FA2A6C4FF2723749279C (void);
extern void ARPlane_get_alignment_mBEF1BCDE0A964ADAE4E5F477BD4C961CA8849EC1 (void);
extern void ARPlane_get_classification_mDE82B3032BBD7BD6ACAFEBEBAE439B4044BDA085 (void);
extern void ARPlane_get_centerInPlaneSpace_mB920536734D86017855A46CCBCB51C4FC8C4876A (void);
extern void ARPlane_get_center_mB33D672F5673C29616BC46AC56AF1276D5A627CC (void);
extern void ARPlane_get_extents_m62F945D694458164CB2DFA6D53C8ED085B3900C9 (void);
extern void ARPlane_get_size_m4F874C7EC0884482FB962790594D2ACF5A8874A6 (void);
extern void ARPlane_get_infinitePlane_m5BE7FB076247945DF209B8CBEAD2CC62433D6AC5 (void);
extern void ARPlane_get_nativePtr_m0227A663EF6E1CC15D6A5BE2BF3D5EC21BCFFA25 (void);
extern void ARPlane_get_boundary_m7C8D2C076E5FE6F1A8568B9C7575402073D5A4DC (void);
extern void ARPlane_UpdateBoundary_m0F54D0A3D284E8051ADB089B1D720BA8543EC616 (void);
extern void ARPlane_OnValidate_mECE1137B9D2F0680DBBFAE4E1F6FCE7D805BF847 (void);
extern void ARPlane_OnDestroy_m1B928C0ECC64C9C95467D0F9C99317A3EEE8F701 (void);
extern void ARPlane_DisposeNativeContainers_mCACDEA77F5356585B085FACB161B3FE9B6648D32 (void);
extern void ARPlane_CheckForBoundaryChanges_m84BBF242B8FB7A7B5C8493389E0B887C11BBBB2E (void);
extern void ARPlane_CopyBoundaryAndSetChangedFlag_m4FB01C9388B0A20761F61436BC3A88D1AEA2506A (void);
extern void ARPlane_Update_mB6D7BDC2CD9F2B2DB73B6F8A32981C1F855856DA (void);
extern void ARPlane__ctor_m75D393C13FDB895D2B66FDA3F6DAF9A49F770DC1 (void);
extern void ARPlaneBoundaryChangedEventArgs_get_plane_m4A8050E854AD2A386891D06B8695A83B59C73FDE (void);
extern void ARPlaneBoundaryChangedEventArgs_set_plane_m297AB56CF4E77AF4AE5C4983BD69F576F5487AD7 (void);
extern void ARPlaneBoundaryChangedEventArgs__ctor_mD7B4EC2D5BB290541E54078930F949A5F9E34F1B (void);
extern void ARPlaneBoundaryChangedEventArgs_GetHashCode_m1D4324C6E2918575F7DA49CD1FC9EBB96C3C39BC (void);
extern void ARPlaneBoundaryChangedEventArgs_Equals_m0A539DC1276324BD525A49B913A9532301192DD1 (void);
extern void ARPlaneBoundaryChangedEventArgs_ToString_m9098FE81C630F76EE51EF044C24673846C930040 (void);
extern void ARPlaneBoundaryChangedEventArgs_Equals_m7AA9243101F5680770335ADAD9C11BE43235B861 (void);
extern void ARPlaneBoundaryChangedEventArgs_op_Equality_m2280262D8DE3A47C72B3C707EEFF38375D5F40BA (void);
extern void ARPlaneBoundaryChangedEventArgs_op_Inequality_mD182C2A05E4BBCD00BBF21B0DCBCF15DEC869292 (void);
extern void ARPlaneManager_get_planePrefab_mD1AE647924733F69F5446087BF5BFD4EEB4EF2C2 (void);
extern void ARPlaneManager_set_planePrefab_mC934904F4FA889CF92277D56CA268B3EB1149FB2 (void);
extern void ARPlaneManager_get_detectionMode_m58EC905BD8D12740899BC9A6D4A7968FE482AB09 (void);
extern void ARPlaneManager_set_detectionMode_m525E1EFE03451CEEDE3E434179E098D76E00CE4E (void);
extern void ARPlaneManager_get_requestedDetectionMode_m48D3EFD9C77C930F7A059E8C0E37410DE0C5A142 (void);
extern void ARPlaneManager_set_requestedDetectionMode_mEBF1F2ADD9AED8B433AF590235FB23C734DF1D5E (void);
extern void ARPlaneManager_get_currentDetectionMode_m5B222DE66EDDF55C552262CD7C741DD6D3C8B839 (void);
extern void ARPlaneManager_add_planesChanged_m295E8F0B09AF78A2D9249F3C07BFD4D4F749F36D (void);
extern void ARPlaneManager_remove_planesChanged_m86DE7059D87299391D2EE97A884E53CCE80B3A5E (void);
extern void ARPlaneManager_GetPlane_m49FC33F6BC0DCFE67C4FC5F5CEE99CC8A7036FAE (void);
extern void ARPlaneManager_Raycast_m567B9C820E9E0456FD5FE878F3533CB605DDB615 (void);
extern void ARPlaneManager_GetCrossDirection_m0898AE13C39AC18D3C52882D401FF17C64430136 (void);
extern void ARPlaneManager_WindingNumber_m7291CC7CEF88D30F49D9C99AF9B6F6BF4EBDC871 (void);
extern void ARPlaneManager_GetPrefab_m52C742FF457449D290C3B7D7035741B686CD5851 (void);
extern void ARPlaneManager_OnBeforeStart_m21596786467B87EA7620F6B997F8A44556BC23FD (void);
extern void ARPlaneManager_OnAfterSetSessionRelativeData_m8F6BCA07C252256594155FAACE3CFC025D266B19 (void);
extern void ARPlaneManager_OnTrackablesChanged_m5B10E7EE8C5F9B3DA686A9A182EB28BB6DC8D825 (void);
extern void ARPlaneManager_get_gameObjectName_mA3E24259EB0C4B773E1887B5D0645A683C0BC082 (void);
extern void ARPlaneManager_OnEnable_m5A60291499C3B5AF2789552E4CD2E670BD4B8EE0 (void);
extern void ARPlaneManager_OnDisable_mD340A55FC5F8D2B4EBBE201F0EFA79E042BA371B (void);
extern void ARPlaneManager__ctor_mAFCAB69B1267C0EEB09BF4687FDC045D0C6C4E31 (void);
extern void ARPlaneMeshGenerators_GenerateMesh_mB7990122259CAEF8FD537C5A32B360313F857D1B (void);
extern void ARPlaneMeshGenerators_GenerateUvs_m6C406C633DAEC01385CBA4CDB20250B6BB596CC2 (void);
extern void ARPlaneMeshGenerators_GenerateIndices_m8048F590E06AA353732AF95DB066F00984383E86 (void);
extern void ARPlaneMeshGenerators__cctor_m9F065134C253EC77C8C039D8BF2A1D9BB2B50288 (void);
extern void ARPlaneMeshVisualizer_get_mesh_m6C1A730CECE8AE0A0549ED615AB3B3D66E4A0FD8 (void);
extern void ARPlaneMeshVisualizer_set_mesh_m8CD0379CEA0D69FE611772065773F289F7DC72AC (void);
extern void ARPlaneMeshVisualizer_get_trackingStateVisibilityThreshold_m4B12258F93FA5CC0D872C2D40F4099C9955E17B4 (void);
extern void ARPlaneMeshVisualizer_set_trackingStateVisibilityThreshold_mF605945507B06EF28C32B4959F200DB1D1EBC092 (void);
extern void ARPlaneMeshVisualizer_get_hideSubsumed_mCCD37D9204A25314CCB1265428F31CE60756531B (void);
extern void ARPlaneMeshVisualizer_set_hideSubsumed_m23832E8A5F46EF97E1C842F42EAD70E736A7E9D7 (void);
extern void ARPlaneMeshVisualizer_OnBoundaryChanged_m18E75B8204676B24B7CA97F972B52D113D2DD393 (void);
extern void ARPlaneMeshVisualizer_DisableComponents_mDC0DB459E5F92C72961B248B15288812AD3AAD60 (void);
extern void ARPlaneMeshVisualizer_SetVisible_mDEB7B36C8DBCC2D49CF8384CDB6776B10AB693C6 (void);
extern void ARPlaneMeshVisualizer_UpdateVisibility_m3BDC44D40DCB36A855EC1DA3FAE5CCC7B08EAF37 (void);
extern void ARPlaneMeshVisualizer_Awake_mBC4D682166B1A469F75E1DAB7B73FC81186C9380 (void);
extern void ARPlaneMeshVisualizer_OnEnable_m022D7A992E4827FF0D1C7C7ADCB5A10BE757E9ED (void);
extern void ARPlaneMeshVisualizer_OnDisable_mE85F1015B64CB38CE1AB27FAF63272B4059D0536 (void);
extern void ARPlaneMeshVisualizer_Update_mB7049601E8BE4168E9873CD75316100B3AF05798 (void);
extern void ARPlaneMeshVisualizer__ctor_m634D93D340A27CD62EEDBD1DC21FC97E942501FB (void);
extern void ARPlanesChangedEventArgs_get_added_m6E00DD5F0B3261BCBAA8A029924A1F3F4179C747 (void);
extern void ARPlanesChangedEventArgs_set_added_m86405667F94B610F057F9685D32DFE4D1D58AFB8 (void);
extern void ARPlanesChangedEventArgs_get_updated_mE979DAD93445C56FF7BEB732528D6CADAF0B6C22 (void);
extern void ARPlanesChangedEventArgs_set_updated_m99AB6C62358CF84214D0ABC7C55246FF04FEF4FD (void);
extern void ARPlanesChangedEventArgs_get_removed_m8CF2AA0278488746F8C55CC6C7DC1870531D63C8 (void);
extern void ARPlanesChangedEventArgs_set_removed_mADCA5E7123CC12A874A9B95D189BA621163B7B74 (void);
extern void ARPlanesChangedEventArgs__ctor_m20AE62576EED835E5930101056E72092B75CA2F3 (void);
extern void ARPlanesChangedEventArgs_GetHashCode_m85C48FB255A115C4E1C8BE91457D6D070C14010E (void);
extern void ARPlanesChangedEventArgs_Equals_m7BFD63AF9035113B2BEBC0ACC86E6A55FA2E9397 (void);
extern void ARPlanesChangedEventArgs_ToString_m4A51F333B5B52A6857AD19DADD92B5F437FCDFE1 (void);
extern void ARPlanesChangedEventArgs_Equals_m8AA6CA2EBB7D2C3DC8C279D77A5894AA0CCB53EB (void);
extern void ARPlanesChangedEventArgs_op_Equality_mBB2E0E0C5555011BD1A653E42C0ABB91020F4E91 (void);
extern void ARPlanesChangedEventArgs_op_Inequality_mA616877FE51980F3ACC0B27963D940BFB66B016C (void);
extern void ARPointCloud_add_updated_mF2E2E46A88A53C750389AE65A20758F39EB760DA (void);
extern void ARPointCloud_remove_updated_m9EB9732BBCD9E806E70F40757BE9C7AD6E591C07 (void);
extern void ARPointCloud_get_positions_mD1B6C96855B80F0CCF6B32F97CBA75110E6EBEB5 (void);
extern void ARPointCloud_get_identifiers_mBC363588E42905575E0855C6A70375098E30D799 (void);
extern void ARPointCloud_get_confidenceValues_mF46E6073F5430E6EFC497B4CF2F226F3C9FAD4B5 (void);
extern void ARPointCloud_Update_m03BEBE6A9E17ED2DB0DD4DE93C5A7430178515E4 (void);
extern void ARPointCloud_OnDestroy_m9B851E60E78847B6D649DE62897B360BF210FBA9 (void);
extern void ARPointCloud_DisposeNativeContainers_m917FC6487433B7814A851678BD06E7C21F922958 (void);
extern void ARPointCloud_UpdateData_m30068C4F76A33C6B5CFDF829ABD01D45549E9231 (void);
extern void ARPointCloud__ctor_mB065DD0D95C26B28A045ED6B5A7A48BE73F78316 (void);
extern void ARPointCloudChangedEventArgs_get_added_m74DFD8ED4B3EADB5AC29DD0E748C84C6FA26DDE9 (void);
extern void ARPointCloudChangedEventArgs_set_added_m3FE2F4143F780814D136E17E361046408D50C63D (void);
extern void ARPointCloudChangedEventArgs_get_updated_mD69E67FE8B06AB44B15F42F1D6EFA91F7E58F7C6 (void);
extern void ARPointCloudChangedEventArgs_set_updated_mC936361996F351B6BF791EB3658C8F62C2210CBF (void);
extern void ARPointCloudChangedEventArgs_get_removed_m64321D4F0D902494EE19EFF1B258981DE5C712B0 (void);
extern void ARPointCloudChangedEventArgs_set_removed_m99F84576C91F6538E21C4B33C81F9947A3A095B9 (void);
extern void ARPointCloudChangedEventArgs__ctor_m4D0FC76C4C018FCBE287313DC150D23E46295648 (void);
extern void ARPointCloudChangedEventArgs_GetHashCode_mF40A348475B5813A13FFE2C8FF2BA433F7CC9EDA (void);
extern void ARPointCloudChangedEventArgs_Equals_m05CA0C188628022AD80F0C83A4BF9D88EC93DF85 (void);
extern void ARPointCloudChangedEventArgs_ToString_m9AB26A548169C9B39BA06A1DBAFDD29C280880DB (void);
extern void ARPointCloudChangedEventArgs_Equals_m663B71243F5F1D3A01EF2CCA59420E82ACD796C7 (void);
extern void ARPointCloudChangedEventArgs_op_Equality_mB548C7D04AE85309E877342443317131F064CBB4 (void);
extern void ARPointCloudChangedEventArgs_op_Inequality_mDD961D67FC89A1ADAC3F4A1A988127211FDB5146 (void);
extern void ARPointCloudManager_get_pointCloudPrefab_m4B3EE259F857DFD335088EE2A42D7339FF13D4DA (void);
extern void ARPointCloudManager_set_pointCloudPrefab_m7DE2BABC80A83D6744D67AB17B09167817A6AB7C (void);
extern void ARPointCloudManager_add_pointCloudsChanged_m108F43CDA88DC192BA5DE3EBD7DFFDE3C59E26B3 (void);
extern void ARPointCloudManager_remove_pointCloudsChanged_m28A4E43DFFB1A517F932B1A613664782D068C8C0 (void);
extern void ARPointCloudManager_OnEnable_mB337EABD7FE2F636826A1E8268C10CD69DE14B61 (void);
extern void ARPointCloudManager_OnDisable_mE638749742E3295F193EB46E41DA848AD67B0C80 (void);
extern void ARPointCloudManager_GetPrefab_m8192FDD40FBDF2D09B15117CBEAA698AC56B2C92 (void);
extern void ARPointCloudManager_get_gameObjectName_m30AD2147A29A3AD991932F7C9AD658DCEE39D068 (void);
extern void ARPointCloudManager_OnAfterSetSessionRelativeData_mE604F78EF54380998C7FF4A3477315FABC103356 (void);
extern void ARPointCloudManager_OnTrackablesChanged_mF512581E2055A773EC41BD3E115F1AF654705AE0 (void);
extern void ARPointCloudManager_Raycast_m75E11BD6179ECDF207D43F4ACD01103E00607761 (void);
extern void ARPointCloudManager__ctor_m28345518F8E9DAD9A9C6A5F6A51933A58125B8DF (void);
extern void PointCloudRaycastJob_Execute_mCC84BA675585A86C765936270AF8EFD99C3D5C13 (void);
extern void PointCloudRaycastCollectResultsJob_Execute_m1AA868B2EEF8C3AAE8838F00B42860FC1F740A78 (void);
extern void ARPointCloudMeshVisualizer_get_mesh_m49F56BF31D05DA5E221F73B5A0F0EAAFECE85B85 (void);
extern void ARPointCloudMeshVisualizer_set_mesh_m29FA764125D5D67B7DF1A8278958F2AA0D139A09 (void);
extern void ARPointCloudMeshVisualizer_OnPointCloudChanged_mEDB040DA37A02524FE4CAC45004C36EABD9AF790 (void);
extern void ARPointCloudMeshVisualizer_Awake_m887B0ECC6880ED00AF6093E04DCFF7046E6B1E92 (void);
extern void ARPointCloudMeshVisualizer_OnEnable_m1667D34F8C3014FF3ACF0815AF12482D1BFA69F1 (void);
extern void ARPointCloudMeshVisualizer_OnDisable_m42CA252D8A4E7A81E8454302944C4A202A9C996C (void);
extern void ARPointCloudMeshVisualizer_Update_mFB9885E77AA22AB49BB15BD72E8A0A86044A4103 (void);
extern void ARPointCloudMeshVisualizer_UpdateVisibility_m4AF7339C94A64DA159E400960751AD90364F7979 (void);
extern void ARPointCloudMeshVisualizer_SetVisible_mD3B63AF0179A18FC52F01BADF128EDCC8CBE5FAF (void);
extern void ARPointCloudMeshVisualizer__ctor_mDDF94641701508D63F93896AAA59BA0CEA964D35 (void);
extern void ARPointCloudMeshVisualizer__cctor_mA0C71E819658469A7997880BAEAA367AA65D42E0 (void);
extern void ARPointCloudParticleVisualizer_OnPointCloudChanged_mE982B0C5A100942FCB6CEA7C93AEDE12A9A8782A (void);
extern void ARPointCloudParticleVisualizer_Awake_m99B022575BEFB8A857E4873E5012C420189717EC (void);
extern void ARPointCloudParticleVisualizer_OnEnable_m8223E2AF28CCFA50300FEB01AFC95962EB42E270 (void);
extern void ARPointCloudParticleVisualizer_OnDisable_mBF710CB602BE4ADAC0D0FC66C5924B0F5D10DDF3 (void);
extern void ARPointCloudParticleVisualizer_Update_m051339455E1DF98BCC4F65DBCD0F1FBA9A714DFF (void);
extern void ARPointCloudParticleVisualizer_UpdateVisibility_m279926EC98D35245CFF722594DBEB1E187832D2E (void);
extern void ARPointCloudParticleVisualizer_SetVisible_mE7481C4B47E42B6F451F93E907944A74E66767F0 (void);
extern void ARPointCloudParticleVisualizer__ctor_mADBF1702FDFB7F9C45A780334B58491C9F6801F0 (void);
extern void ARPointCloudParticleVisualizer__cctor_mE479457EB84C3C569ABBF019B3BF69EEC318274D (void);
extern void ARPointCloudUpdatedEventArgs_GetHashCode_m53F08CA39C23B713F1918D21E6AFD78731EACC6F (void);
extern void ARPointCloudUpdatedEventArgs_Equals_m79BA8AC909F4E622B9396930F80B1A900DEDD217 (void);
extern void ARPointCloudUpdatedEventArgs_Equals_mD3C036D4490380AE4099AAF2F4D9BA32519159F4 (void);
extern void ARPointCloudUpdatedEventArgs_op_Equality_m68F17CFE1A16FAB1F9ACEBBCED0346AF6B7285C2 (void);
extern void ARPointCloudUpdatedEventArgs_op_Inequality_mADACED9475D0DACDBBE54486D047FA9E6C70B4E2 (void);
extern void ARPoseDriver_OnEnable_mE2094A035372476C10C8E739950B18F22A519FC4 (void);
extern void ARPoseDriver_OnDisable_mCCBD832DB28EB6D60E37A5EBAE110CE52E495BA5 (void);
extern void ARPoseDriver_Update_mEACE85D68C1B35540A124FC7644557120409074E (void);
extern void ARPoseDriver_OnBeforeRender_m75FE5362273B5459BF4CB331AF3EF84E302B27EC (void);
extern void ARPoseDriver_PerformUpdate_m528ED9B930ECC6ADBB65A5FD71F11B454AB77719 (void);
extern void ARPoseDriver_OnInputDeviceConnected_m43A1F50F3D4A44EDD91363814A5667DA6FABCC0A (void);
extern void ARPoseDriver_CheckConnectedDevice_m801AABF1ED6B1E1B49B71E7BEFD96DDA30FBBA45 (void);
extern void ARPoseDriver_GetPoseData_m9F3D183BA2004C84B984BBAD6E2DA3A6B062E409 (void);
extern void ARPoseDriver__ctor_mAFA3405F3863ECD38E016F7B805F8F89FC0A4144 (void);
extern void ARRaycast_get_nativePtr_mB2DE425D459403A80B76D0845713C8992FFD16F3 (void);
extern void ARRaycast_get_distance_mA8272EC82C2829D0D8D0EBFCA2BAB74584D45B62 (void);
extern void ARRaycast_get_plane_mF6C8E3D2C90D67D00ECB4541DDC17980EC0584CF (void);
extern void ARRaycast_set_plane_m5BF5973F88BF91EC407275321D1F42CEE1CD3C3F (void);
extern void ARRaycast_add_updated_m73570A6579B28D1FD16C5BE50B28F73562565E73 (void);
extern void ARRaycast_remove_updated_mB733AA38B898F08AE50F1962A83360271E8BAD44 (void);
extern void ARRaycast_OnAfterSetSessionRelativeData_mB076F8EC7A02CD9F7B8EB26BB24E54CB89C09D44 (void);
extern void ARRaycast_Update_m3A086526146B5AF819020677D567D9668C2777A3 (void);
extern void ARRaycast__ctor_m6159FDBC04CE424C0474FA868523D302D861BF0A (void);
extern void ARRaycastHit__ctor_m0C23F16B12F6D0F71C0B00D48D8BC3271BF7F39D (void);
extern void ARRaycastHit__ctor_m954400C9EFC7F5B5A227276ED8EE2FEF32E6BC48 (void);
extern void ARRaycastHit_get_distance_mB761B6EA13AA35393AB92EDD3A82D61659DE3126 (void);
extern void ARRaycastHit_get_hitType_m4ACAC8C59DED2EEF01C165D15136A15EBBA996F0 (void);
extern void ARRaycastHit_get_pose_m84C13E71E21FE12CBA9AAD98DC28B1E414C9EFFD (void);
extern void ARRaycastHit_get_trackableId_m4E510F2C326AFF23086203E4241C8F9B293616C3 (void);
extern void ARRaycastHit_get_sessionRelativePose_mD06C35AA1BE0F142669BACB95F30A059A65D3DF7 (void);
extern void ARRaycastHit_get_sessionRelativeDistance_mFD19959FCA30322A0BF427FB0D9C3BD9D4464047 (void);
extern void ARRaycastHit_get_trackable_mF8D64EB03AFF2E1D5FC9B88255D2A84130B43D09 (void);
extern void ARRaycastHit_GetHashCode_mE1E5375DD029685234B765B1D0C653B048BB8D5E (void);
extern void ARRaycastHit_Equals_m35C3DD720E50D890CA94E70DB77A01DAE0A0A1FB (void);
extern void ARRaycastHit_Equals_m91ECCB5154E5B4B18018C00A7E71129D682DD3B1 (void);
extern void ARRaycastHit_op_Equality_m2C051D9D19EF97A840B88FD6C0D560AE71F506D2 (void);
extern void ARRaycastHit_op_Inequality_m0E08E39A204BADC5AEEB2BDEA5F980B7AD74E03B (void);
extern void ARRaycastHit_CompareTo_m839A11B878030471B287EDA2250807A5A4F3F4D8 (void);
extern void ARRaycastManager_get_gameObjectName_m317034F0A7D9F78204DDDFD2D974AB67E428A142 (void);
extern void ARRaycastManager_get_raycastPrefab_mB08FF79C4DAA2074B10A5A8C25F3AED1ADF458E8 (void);
extern void ARRaycastManager_set_raycastPrefab_mD1B935A438CB84F1DA1608B7BA577FCAD9AC30C8 (void);
extern void ARRaycastManager_Raycast_mF56A1E2D7CBB61131E4D844ADA9D0CC1F6B53EAC (void);
extern void ARRaycastManager_Raycast_m6D403ADB6840FC93F5C3E4C765BE1517180ED2EB (void);
extern void ARRaycastManager_AddRaycast_mEC7CA45433E91FE30A39EF1BC593501DCC29C862 (void);
extern void ARRaycastManager_RemoveRaycast_m6287F0E52B0F3358EE21AC7A354F543841AC7802 (void);
extern void ARRaycastManager_GetPrefab_m0C8F561D8BC973FD5B1A671810353380A01459C3 (void);
extern void ARRaycastManager_RegisterRaycaster_mED8A37992AA3FA14D98B49783B670E54B363935D (void);
extern void ARRaycastManager_UnregisterRaycaster_mBF732586B4DE3197DD8BE0B337194396AFF54894 (void);
extern void ARRaycastManager_OnAfterStart_mD7FAA6AA9C4917661E9B3AEB32526B29744C59E8 (void);
extern void ARRaycastManager_ScreenPointToSessionSpaceRay_m16A93EFB3AD1C851615B0C29D30EDA31E1B9E285 (void);
extern void ARRaycastManager_RaycastViewportAsRay_mCCCF650D528D5CB9463CE07A77C4153B85171200 (void);
extern void ARRaycastManager_RaycastViewport_mD4B1E431F070DB723B706B4AF93C4870E14AE5A3 (void);
extern void ARRaycastManager_RaycastRay_m0DD4601EA39C8F69CF01DBE246CF5A988585C984 (void);
extern void ARRaycastManager_RaycastHitComparer_m8199F00C8E80441FF36E6CCDFC6BC429162D1FE9 (void);
extern void ARRaycastManager_RaycastFallback_m2AEB1BB26F1E99A489F35019BCA8A472A0974D70 (void);
extern void ARRaycastManager_TransformAndDisposeNativeHitResults_mD8A9311ADA233E3A3DBBB6E8102CEE2AC082E915 (void);
extern void ARRaycastManager_OnAfterSetSessionRelativeData_m5F5050F6CAFF19E1CDB0C145694A97F5DD7A4266 (void);
extern void ARRaycastManager__ctor_m9091C8E7281C43EDF580F8C0A0B085C4F42778C4 (void);
extern void ARRaycastManager__cctor_m8A6F66979C15A4CF1EA73660F4DDB1DC883BABBE (void);
extern void ARRaycastUpdatedEventArgs_get_raycast_m3B8C525D8EA47F31AA2492A9797652A9C2BAE8E7 (void);
extern void ARRaycastUpdatedEventArgs_set_raycast_m54ED1343FBDD93ED1A45B0A08CCEA53489FEB1C9 (void);
extern void ARRaycastUpdatedEventArgs_Equals_mDFA0333A1E3C1F0F909561C790846DE76DD78943 (void);
extern void ARRaycastUpdatedEventArgs_Equals_mBC0B147BFAB731AEF33493D1E68B67176F869981 (void);
extern void ARRaycastUpdatedEventArgs_GetHashCode_m676A512737D735B0B6B1AAB43095845823F3B49E (void);
extern void ARRaycastUpdatedEventArgs_op_Equality_m336643ED3F8C8671C84875ABF6A542FBE2AC5E82 (void);
extern void ARRaycastUpdatedEventArgs_op_Inequality_mFEA98064550BC3CFE097BDECCF7B45C5F7C5F841 (void);
extern void ARSession_get_attemptUpdate_m0EAA239D17F9E81EA7B91E7F9264F25D3A21053A (void);
extern void ARSession_set_attemptUpdate_m96B3F3270D4234FFCF1FF75BE8483DA14954A50D (void);
extern void ARSession_get_matchFrameRate_mE9DD185413E4221C338E62F62A1D9FF11A1C0C4A (void);
extern void ARSession_set_matchFrameRate_mB4A8508F8B7BC6277E29897398EA978E79AC52F3 (void);
extern void ARSession_get_matchFrameRateEnabled_mA00102E4B432C2EFC208581324F1E4C321A53A23 (void);
extern void ARSession_get_matchFrameRateRequested_mE71E1A95553A3DD0CBCC3D231EF8B8839B3020E1 (void);
extern void ARSession_set_matchFrameRateRequested_m2D869903BF0203BC1996BC381C29876D36A1DB7C (void);
extern void ARSession_get_requestedTrackingMode_m6E568E072F4C49CCF9CF5E26F20C6818275E7D52 (void);
extern void ARSession_set_requestedTrackingMode_mD234A3A6321102EB25D95EFD9C6CCD96D282418A (void);
extern void ARSession_get_currentTrackingMode_m0705EF50CB1914A74B455D1D8B80E82B5E49DF54 (void);
extern void ARSession_get_frameRate_mF8050E4AF339A55F79BFDCB519C290396AA1A138 (void);
extern void ARSession_add_stateChanged_mD40004D1EBE89E6CAC651632ABB8EADBE46692FC (void);
extern void ARSession_remove_stateChanged_mDAC5A2DCA86363C495C66FB8CA92F46ABDFE3BE1 (void);
extern void ARSession_get_state_m6A22EE6441E58AC66E1FE9A7359D06C90C1A2842 (void);
extern void ARSession_set_state_m8C16FB66D3910F9079CD01CE9C98D2D6D46D7CD5 (void);
extern void ARSession_get_notTrackingReason_m18987B9B3B06BF4136C13E8F83A338BB332F2BF8 (void);
extern void ARSession_Reset_mE905496B8400CB5BF1EB50A01C675771FCCF9A91 (void);
extern void ARSession_SetMatchFrameRateRequested_m6430151F0DD7474FD48BA18B33AA889BD92A630A (void);
extern void ARSession_WarnIfMultipleARSessions_m42E940191AC74642FE7E16117AA44A606BB8094A (void);
extern void ARSession_GetSubsystem_m31E4A8324A31B4844B6AA43797FE2C53A2BA3BEB (void);
extern void ARSession_CheckAvailability_m7090FD96E8580EB3E6CAB97B8344BF845B254D0A (void);
extern void ARSession_Install_m9C4DCECEA000E16F4992376B7E40DA0C95B58166 (void);
extern void ARSession_OnEnable_m15300A28354F0EBC321A2005F09A3B8C69739E19 (void);
extern void ARSession_Initialize_m9A38049E4C026CEA25415EC025407D2378F02AA1 (void);
extern void ARSession_StartSubsystem_mABE004B0E0CCB1E71F7A5B58D2DC8A71C2094E2D (void);
extern void ARSession_Awake_m39D818E017C1D4DA1CE60591740CB59AA3326FF5 (void);
extern void ARSession_Update_m655A9FF7829D10D92F3C495FC996BD456E7D91BE (void);
extern void ARSession_OnApplicationPause_m35CDAAD00BFDE112C443803A5A7436783D62E225 (void);
extern void ARSession_OnDisable_m9E4AF208C0FE89B50C41F7A7713540A41EE5080E (void);
extern void ARSession_OnDestroy_mBB3CEB034D35227A6E76F2D20D80639898F2D9FE (void);
extern void ARSession_UpdateNotTrackingReason_m8F96B5947DFFA5D308DBA3E9D8CBFDB3ADABD1B2 (void);
extern void ARSession__ctor_mF86E51E302EF8623E0E7465D295579D42ABA21FB (void);
extern void U3CCheckAvailabilityU3Ed__36__ctor_m735863EED119A94ECAEA97AA6163A7888BAAE407 (void);
extern void U3CCheckAvailabilityU3Ed__36_System_IDisposable_Dispose_mF0BC9EA254A4F2262928F6B7DED3DBAF2C124958 (void);
extern void U3CCheckAvailabilityU3Ed__36_MoveNext_mF640DE420523AFC133818CAFBD896C139E041218 (void);
extern void U3CCheckAvailabilityU3Ed__36_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mC6179916F8366120728FBC4A319199B5A33EA81F (void);
extern void U3CCheckAvailabilityU3Ed__36_System_Collections_IEnumerator_Reset_m8826529D457D216C4F0D74058D9C41EB77713367 (void);
extern void U3CCheckAvailabilityU3Ed__36_System_Collections_IEnumerator_get_Current_m89D3BE7534710C9D3AAA093A4EC2BA45FFA4BE8E (void);
extern void U3CInitializeU3Ed__39__ctor_mD9B6C69399849460F6186CA514C8C0C1E3E4A31F (void);
extern void U3CInitializeU3Ed__39_System_IDisposable_Dispose_mAE3424C27A371EE887CF8C7268B5961CCFC99127 (void);
extern void U3CInitializeU3Ed__39_MoveNext_mEC7D94B4F821E5F112EE36C6D43C26E86E28F1C4 (void);
extern void U3CInitializeU3Ed__39_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m4EC9894037C15834354448A7CBA6F50EA660DA3E (void);
extern void U3CInitializeU3Ed__39_System_Collections_IEnumerator_Reset_m8FC03158D4A30390C52890CE1CDA62916CF29114 (void);
extern void U3CInitializeU3Ed__39_System_Collections_IEnumerator_get_Current_m7F70E4E5D3B7329FD234B87E00BC439DA70E4774 (void);
extern void U3CInstallU3Ed__37__ctor_mD9BA7D04DE9A0604D515EEEF6969A8833E6B69C0 (void);
extern void U3CInstallU3Ed__37_System_IDisposable_Dispose_mE976D780D3543F4D7E8131386463899493885C85 (void);
extern void U3CInstallU3Ed__37_MoveNext_m10883753BFAA6E6C97897C8C1009C7ED51270B16 (void);
extern void U3CInstallU3Ed__37_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mD718DE0B0B711C9E9CD7FA45F56C4756DECA26DF (void);
extern void U3CInstallU3Ed__37_System_Collections_IEnumerator_Reset_m8DF442701056725A9816E886F188D86C31E29879 (void);
extern void U3CInstallU3Ed__37_System_Collections_IEnumerator_get_Current_mC897BF06DE02727E24E1CA2BE07C0304EDBA8EE6 (void);
extern void ARSessionOrigin_get_camera_m5D908BC3C882C5BA0E21A07E4BB5093AF0AD5830 (void);
extern void ARSessionOrigin_set_camera_m67B450A0B2E571CE6B9785DE656ED32B41906239 (void);
extern void ARSessionOrigin_get_trackablesParent_mF42C34E0B09B58DEE52CF3CC13563BE541C14F9F (void);
extern void ARSessionOrigin__ctor_m35D4BAAA2E6EEB818983F5AB4FEDEF27CCEDDDFB (void);
extern void ARSessionStateChangedEventArgs_get_state_mC0A6FB4AF08C068BFD5D9730F60215BE662A950B (void);
extern void ARSessionStateChangedEventArgs_set_state_mBEF6AA8214783D771EDF618B3E863D6DBC1FE8A7 (void);
extern void ARSessionStateChangedEventArgs__ctor_m1C0C6E6FD7F83932D0780826124568FCC85C888A (void);
extern void ARSessionStateChangedEventArgs_GetHashCode_m58967DAA40E3E7088B23CB434E04B3BA745E3076 (void);
extern void ARSessionStateChangedEventArgs_Equals_mDB8C22D91659F1BEA3417585A531A31661D56FD2 (void);
extern void ARSessionStateChangedEventArgs_ToString_m1F84E487E601B1FB57C5E6F051262AA9264E66D5 (void);
extern void ARSessionStateChangedEventArgs_Equals_mD7EC44B01E9572499F4888DED9885761FCBA47BF (void);
extern void ARSessionStateChangedEventArgs_op_Equality_m136CF1C9C70E06432E3A359FE26B9CC50A3666AE (void);
extern void ARSessionStateChangedEventArgs_op_Inequality_mE85DFE523DFB69288E4FD9FC6A70CFA137502321 (void);
extern void ARTextureInfo_get_descriptor_m42C04EE9D6B420EFFC6002A044CA8B2FB6E27FE2 (void);
extern void ARTextureInfo_get_texture_m9A17329F68E159CC5C924956D4258BD26990C40A (void);
extern void ARTextureInfo__ctor_mE3D6856CEA559DD33C81F5E993451608F7515AC1 (void);
extern void ARTextureInfo_Reset_m94C8E8348B77C4A7055ABFC20FA8E59632293A59 (void);
extern void ARTextureInfo_DestroyTexture_m4A1489A030CA1E43E61CB7F3413AC97B5F5180F9 (void);
extern void ARTextureInfo_GetUpdatedTextureInfo_m88B8391EEE756B9D4DD68A2C8B7CDB5468C319F0 (void);
extern void ARTextureInfo_CreateTexture_m6A095D54141ADC1B50174B6FE9CCBECFD1B4713C (void);
extern void ARTextureInfo_IsSupported_m3DF7C6656B31ED56F557D0BD4DE78E40304E6B09 (void);
extern void ARTextureInfo_Dispose_mF92A6E0C4D08205EDC7CF817BC866A9AB179632F (void);
extern void ARTextureInfo_GetHashCode_m85504109E923E3CA1494548DD2C3261C1976E97A (void);
extern void ARTextureInfo_Equals_mFB1B9B103E90F76AE0EB4E2DED235BCF3DCBF034 (void);
extern void ARTextureInfo_Equals_m020839E8607BDF4DEFEDBB125778BEFE105F563D (void);
extern void ARTextureInfo_op_Equality_m0EA32FFFF37B03E66CCA2D2E6DFBB8B9A62006AE (void);
extern void ARTextureInfo_op_Inequality_m4A2C24587F6484E0CBD99F3D91C7D0BD5657104B (void);
extern void ARTrackable__ctor_m8074A81414BDB3B30F9863C80B10EF90C99FFD8B (void);
extern void ARTrackedImage_get_extents_m3201758E786A2532D340FE93EEF73B6AA2D055DB (void);
extern void ARTrackedImage_get_size_mAC20A0ECB99C7502138BA134686286B3C126D6F9 (void);
extern void ARTrackedImage_get_nativePtr_mE99C5B4BF589443B6C342B22C99AC02D7DED3E10 (void);
extern void ARTrackedImage_get_referenceImage_m7F8C3733154BE3242F82D2C9A7987A8AAC1F3E6B (void);
extern void ARTrackedImage_set_referenceImage_m1EA6089A5257113AC8141362107AA8CEA58EF38B (void);
extern void ARTrackedImage__ctor_mE6C1F2FA91A2B6F58665058A2550EFC07C7CCDA8 (void);
extern void ARTrackedImageManager_get_referenceLibrary_mD811689B94E007CD8F708D91155C8DC575E948DD (void);
extern void ARTrackedImageManager_set_referenceLibrary_m826772B820572A41FE7B566E0F6E0C25024D0E5E (void);
extern void ARTrackedImageManager_CreateRuntimeLibrary_m53343F2B04F48AE642735069B9348B0EAF009D1E (void);
extern void ARTrackedImageManager_get_maxNumberOfMovingImages_m54630EFA22CCADFA120FD908E0714E7297AEB215 (void);
extern void ARTrackedImageManager_set_maxNumberOfMovingImages_m4D867796B5E6C507243D2C43680BDDFF5AEC0CD8 (void);
extern void ARTrackedImageManager_get_supportsMovingImages_m9940C251AF671CDAB6517357F5A5C05563478203 (void);
extern void ARTrackedImageManager_get_requestedMaxNumberOfMovingImages_m1FC9793915B2DE5361B432DC12F137210C832BF0 (void);
extern void ARTrackedImageManager_set_requestedMaxNumberOfMovingImages_mD253ACF680396E6BDBEE194358093D84D7D3218A (void);
extern void ARTrackedImageManager_get_currentMaxNumberOfMovingImages_mB131CA9A3525418C91945D1CC075D00C59D70F1C (void);
extern void ARTrackedImageManager_get_trackedImagePrefab_mC698D56D9B539242437FA40F1DDC6E4FE959DE2A (void);
extern void ARTrackedImageManager_set_trackedImagePrefab_mAAE136140F485320C96A4E30F244CEED525395A9 (void);
extern void ARTrackedImageManager_GetPrefab_mE7358EBC36EAFF6F7CECA248BAB004DDEA8B1DA9 (void);
extern void ARTrackedImageManager_add_trackedImagesChanged_mB190015342B225144729100E0E38745CAFA7F438 (void);
extern void ARTrackedImageManager_remove_trackedImagesChanged_mA754FAB732F3D5D803FA3A58A51E6487E75CCF34 (void);
extern void ARTrackedImageManager_get_gameObjectName_m0E49CC1FC7F70A3355F7E78BB7A5253A0740C991 (void);
extern void ARTrackedImageManager_OnBeforeStart_mB9877EAD9A54B82012E04BA3BAFA72F0843F906C (void);
extern void ARTrackedImageManager_FindReferenceImage_m7B01D81D360921A7DCDFEAA4DDD7710146371ABD (void);
extern void ARTrackedImageManager_OnAfterSetSessionRelativeData_m4B0BEBB19A6073F8CFF940C974643B3241539FE5 (void);
extern void ARTrackedImageManager_OnTrackablesChanged_mCD020C973B99D2842BDCC4723B49DAC2BAD34EF0 (void);
extern void ARTrackedImageManager_UpdateReferenceImages_mFD7E9622A308CF5AB7005736FF1425FD9EDA6EFB (void);
extern void ARTrackedImageManager__ctor_m1F381872541A32AA65A40BDC401754F8C817DB4A (void);
extern void ARTrackedImagesChangedEventArgs_get_added_m2929CC85141D13AF05C1484AFB47E043C6D3EE35 (void);
extern void ARTrackedImagesChangedEventArgs_set_added_m4E298214A1144403A84E0AE63837F2941528F18F (void);
extern void ARTrackedImagesChangedEventArgs_get_updated_m0C896E1C21EF35FF4B31B36563838EC2BA3CDFD1 (void);
extern void ARTrackedImagesChangedEventArgs_set_updated_mBFE8B2D25F10827CC95CA76E4C52D98EFC1131BE (void);
extern void ARTrackedImagesChangedEventArgs_get_removed_m062CBBEF163BEE47A673F7B3BDC0DD1C6EAEA185 (void);
extern void ARTrackedImagesChangedEventArgs_set_removed_mC7F5D25B8E4AD4CBBE6A43D50FAE0456BE3F928B (void);
extern void ARTrackedImagesChangedEventArgs__ctor_m22F5D20572E4D17270B7CFBA7F0EA9445DAEE8C3 (void);
extern void ARTrackedImagesChangedEventArgs_GetHashCode_mEFBDE822EC1AA6B0E388B86ACE4043D66E23A742 (void);
extern void ARTrackedImagesChangedEventArgs_Equals_m728C2C2B10BA8C4A625C3004C83730589C86C542 (void);
extern void ARTrackedImagesChangedEventArgs_ToString_m949FF3EE96422AE2776C8FFBBD7CA342E56AE365 (void);
extern void ARTrackedImagesChangedEventArgs_Equals_m25F0396D7AA82B5E488BEA4129EE00F355EA3919 (void);
extern void ARTrackedImagesChangedEventArgs_op_Equality_m5D20224CEBE5751DEE808D4037B91FC92BD6FB04 (void);
extern void ARTrackedImagesChangedEventArgs_op_Inequality_mF9739F81EDDDCFB2FC3FFE197272FBD53A15F1EE (void);
extern void ARTrackedObject_get_nativePtr_m15342BE99E069A5B047D184FE30ED47CF7C3A614 (void);
extern void ARTrackedObject_get_referenceObject_mCD7D3716A01017BE3A0DEF15B8B4D11360679D44 (void);
extern void ARTrackedObject_set_referenceObject_m44D01DB41972F1860CAAF329F2E3564170577D6B (void);
extern void ARTrackedObject__ctor_mCFCC68813EF818A1084A35A8F69E553ACEF45D88 (void);
extern void ARTrackedObjectManager_get_referenceLibrary_mCAF4DB685114A5401A283897A4CD0C00C7A67762 (void);
extern void ARTrackedObjectManager_set_referenceLibrary_mDD520435B7F2BECDB8F2FD77CF5D11FAF51B8BD6 (void);
extern void ARTrackedObjectManager_get_trackedObjectPrefab_m08AA42CA1FC81DC3584E38092F30001F509457C8 (void);
extern void ARTrackedObjectManager_set_trackedObjectPrefab_m9A5F19F8F5B48C6967E56B45DCF39F3E9334E503 (void);
extern void ARTrackedObjectManager_GetPrefab_mED6DCAD42BF177A96B0FDA9F2796BF7ABDA7F5BC (void);
extern void ARTrackedObjectManager_add_trackedObjectsChanged_m4E28ABA38B522FBE3B8150E1090237954A804FAE (void);
extern void ARTrackedObjectManager_remove_trackedObjectsChanged_m8412618ABDA3802160C112C7C884886D8057F41D (void);
extern void ARTrackedObjectManager_get_gameObjectName_m3B538A2C8762E66D92FC16DE8F04CC3ECBD059EF (void);
extern void ARTrackedObjectManager_OnBeforeStart_m9D10F03CFBD1456E388E140041F2FF8A950D8DEE (void);
extern void ARTrackedObjectManager_OnAfterSetSessionRelativeData_m5FB25CD1E84A12863FCD5A29560B5EEDA34C881C (void);
extern void ARTrackedObjectManager_OnTrackablesChanged_m618F742518B359861B7DADCFA727F11FD0F03846 (void);
extern void ARTrackedObjectManager_UpdateReferenceObjects_mE6BEC5C294F6E2D966572EBF0687E81261AE3551 (void);
extern void ARTrackedObjectManager__ctor_m37B637D2161B6EDDD2BFB8FAC4660CF3B2334073 (void);
extern void ARTrackedObjectsChangedEventArgs_get_added_m71E8E2055C979657273051DE23FCEB938964169A (void);
extern void ARTrackedObjectsChangedEventArgs_set_added_m6DE32B4C3E8C17166AC5DB4C181DE56842BD0922 (void);
extern void ARTrackedObjectsChangedEventArgs_get_updated_m01050D519E6281EECB48200D952FE1E1FFFE1A60 (void);
extern void ARTrackedObjectsChangedEventArgs_set_updated_mF2A1424732988AB85FF48E63741896BF61E94B46 (void);
extern void ARTrackedObjectsChangedEventArgs_get_removed_mDF5FA585D41D4B0D4517D3C5387B254F6EA6DFDA (void);
extern void ARTrackedObjectsChangedEventArgs_set_removed_m58FAB3CA4AFD4D9B8D0BA1FD150C8FBF06D30F4E (void);
extern void ARTrackedObjectsChangedEventArgs__ctor_mED9F9F2F2307AC78C3580327A6501F5E7ED5060A (void);
extern void ARTrackedObjectsChangedEventArgs_GetHashCode_mC0D8DBBE030D0F00D1D0662C2C100FADF4EC429A (void);
extern void ARTrackedObjectsChangedEventArgs_Equals_mB6CEB7818A761BB4B5009A280C058522176E7641 (void);
extern void ARTrackedObjectsChangedEventArgs_ToString_m1E23EE412E0E27AE4186B56B83F4EF1F12FAD5A7 (void);
extern void ARTrackedObjectsChangedEventArgs_Equals_m822193D19946082B0F5D17078ABD169B4A002EDE (void);
extern void ARTrackedObjectsChangedEventArgs_op_Equality_m45210A3221BD9BDDF26391B7B0652E13FD2EBDF1 (void);
extern void ARTrackedObjectsChangedEventArgs_op_Inequality_m5D1EFCF44E645D67AB54A770E89E3C4A5D541028 (void);
extern void DebugAssert_That_m8F3B14DDB5DC184A9E5E5E239499CC6F2F7D1FF7 (void);
extern void Message_WithMessage_m6D980DF1AD1190D26E45B88C3600AF4226FDD63C (void);
extern void DebugWarn_WhenFalse_mADECDEF90132C7730D282B7F39723E3C1223C760 (void);
extern void Message_WithMessage_m7331F24D7470335EC2EEF8FBA56A8D570ACC8FD3 (void);
extern void CameraBackgroundRenderingModeUtilities_ToXRSupportedCameraBackgroundRenderingMode_m419843F229D4A977DECD405B5A91138BB6C3D8CF (void);
extern void CameraBackgroundRenderingModeUtilities_ToBackgroundRenderingMode_mF6F4B035C02786ACBD23668EB7756DED6B70FB19 (void);
extern void CameraModeExtensions_ToCameraFacingDirection_mE8BDA09B71E726FF7E363A71628B7AF7CD44E715 (void);
extern void CameraModeExtensions_ToFeature_m43AE9A005CAA4089C520C0A622F1006759B38C6C (void);
extern void DebugSlider_OnPointerDown_mB6259788D06531A4A5B2BDA16C97DE0510AC0E72 (void);
extern void DebugSlider__ctor_m53A63C1A397D2EA62B829FAD9A74B78FE5C0EF7D (void);
extern void HashCodeUtil_Combine_m5CA91217A96CB1506F0A9C83ECC4ED11F4EDF39C (void);
extern void HashCodeUtil_ReferenceHash_mFDEB5485EAA482CF3FFD30D45BFFAF9D15CACED3 (void);
extern void HashCodeUtil_Combine_m1FCE36175602D1544BF3DC515483B7B17A9C3356 (void);
extern void HashCodeUtil_Combine_m4C8CAD427BA28472BA6AA92D62A2D03C5778D4AC (void);
extern void HashCodeUtil_Combine_mE51AF1366174B0F2C05662D0D010E5A9506C57EB (void);
extern void HashCodeUtil_Combine_mD6780CAC0596A183974DFEF13F0D3876F23FED45 (void);
extern void HashCodeUtil_Combine_mA931E66011BC522343259C0B46EA40748E93EABE (void);
extern void HashCodeUtil_Combine_m9268BF0EF88EB1BF2333A239165C7949F6BF74FB (void);
extern void HelpURLAttribute__ctor_mD7EFDC5AF7F7319C616C07A83679302B335BF142 (void);
extern void LightEstimationExtensions_ToFeature_m0D9030625B25390EADA9BA2A0ABB9E1FD135A0B2 (void);
extern void LightEstimationExtensions_ToLightEstimation_m99E4653A9A25AF86F23C159949952A9B607C61A5 (void);
extern void LightEstimationExtensions_ToLightEstimation_m23C075B15237696ED895A71F5DD990F28FD0571E (void);
extern void LightEstimationExtensions_ToLightEstimationMode_m73F6397FE2A4534E42DE104592CAD83B178831C5 (void);
extern void LoaderUtility_GetActiveLoader_mBAEB6B82BB71ED27FB5041D90BE39D0EEFE61603 (void);
extern void LoaderUtility_Initialize_m0AB24E553DEC4425F9E1EB328FC7F20FFA34AAE8 (void);
extern void LoaderUtility_Deinitialize_m567824AD533051BA6006043EAB1F94AC76F4847C (void);
extern void MeshInfoComparer_Compare_m1A84A2B187F6AF909D794B1F6E2084B91CB6BA84 (void);
extern void MeshInfoComparer__ctor_m3CA4B312F3F01D87ED687B402A1B2A6E99E21054 (void);
extern void MeshQueue_EnqueueUnique_m0A135283E10EC5C7EB0BABD7F828838309F76DCF (void);
extern void MeshQueue_get_count_mD34731FE94DA122722DBDAD7EED0EE24A3C23A5D (void);
extern void MeshQueue_TryDequeue_m4BEA797ABC13CC2A5BC105CBE82492EE213057BB (void);
extern void MeshQueue_Remove_mDF7E634D6EED310E75A70E9DACB7FDC968AE0837 (void);
extern void MeshQueue_InsertNew_m9FCE96265CD46761953063618A3AC85B54DFCC9F (void);
extern void MeshQueue_UpdateExisting_m0BF022171FB9EC99619552D30C85BA95B8301E60 (void);
extern void MeshQueue_Clear_mB28172C35E681281DC31ADBED3023D10DB288057 (void);
extern void MeshQueue__ctor_m5D8C8E3CC97C1A6590AA3BF954829CE44F5407C5 (void);
extern void MutableRuntimeReferenceImageLibraryExtensions_ScheduleAddImageJob_mF333C41068A0270A13A2DAAB5956B42514F6A86A (void);
extern void MutableRuntimeReferenceImageLibraryExtensions_ScheduleAddImageWithValidationJob_m7FBA37C8669AAED35DB626632C60D68085CF6FED (void);
extern void DeallocateJob_Execute_mECC71B15D80D3951E0D0B4B0B7B28A50DBC81D6F (void);
extern void PlaneDetectionModeMaskAttribute__ctor_m716EA3E15D951963D85172A4A6B0DACA68FE1D7A (void);
extern void PoseExtensions_InverseTransformPosition_mD503BE16CB7C5E4F2DC50E90C3BDD22BC871A6A1 (void);
extern void PoseExtensions_InverseTransformDirection_mE426CC955182D3C53273AB1DB1E706D432D33160 (void);
extern void PoseExtensions_InverseTransformPositions_m3C842AB25E8C84CD1A322562F76680CC99B352D1 (void);
extern void ToolButton_get_buttonHighlights_mC1F0475431AEEC1E84199C35A735DAF0568772FA (void);
extern void ToolButton_set_buttonHighlights_m7A153CD59D08BB7368394D34ABD1F5A2F035BB3D (void);
extern void ToolButton_Start_m3166B8812FBAF5A30470ED482A8E2FE5928F0583 (void);
extern void ToolButton_HighlightButton_m6909154958F4992A8643F61958D685D311CDEF67 (void);
extern void ToolButton__ctor_m32DBB315DF184CBC20B4A4F6DC87E9F4A2C29A41 (void);
extern void TrackingModeExtensions_ToFeature_m683FFDDC9DDC2DA86001B1CCF0C12D8961639041 (void);
extern void TrackingModeExtensions_ToTrackingMode_m4896FC7865A6619FB20F1028CF585126F4B3D6A2 (void);
extern void TransformExtensions_TransformRay_m34136D03EFA2C12718B4F8EC879C293E405D7AB5 (void);
extern void TransformExtensions_InverseTransformRay_m3417C7D5AFC5CC8B2ADC0F6DCA4BFF3C7E539B3C (void);
extern void TransformExtensions_TransformPose_m657A2F269CFE7ABD9E77029577861EA464E2364C (void);
extern void TransformExtensions_InverseTransformPose_m3705FF36F67498108EBA26FA116491ED4ECBB1DE (void);
extern void TransformExtensions_TransformPointList_m7C213FB0F243A84C8ADA345E7D0600CCBC1A066E (void);
extern void TransformExtensions_InverseTransformPointList_m62F86C3FA0F4DCB23357FBCB9E6254A72F502A78 (void);
extern void TransformExtensions_SetLayerRecursively_m8A1487644AED6A3BE7873728671CB6EE9304EF8C (void);
static Il2CppMethodPointer s_methodPointers[1085] = 
{
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m970916F4D526DD5A8B56258C1832C2DCD8AED6D3,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mD111E020C143C9F5D8E50CB513DC66985FD0D305,
	ARAnchor_get_nativePtr_mBE82BAA63BB9C836B5AAECBC8B61F855B352196F,
	ARAnchor_get_sessionId_m6D16A20B42835AC11CC7B3942D92A67887F9D012,
	ARAnchor_OnEnable_mFE3DADEC4D7DD29783458A6D1C5DABA2487C4EE2,
	ARAnchor_Update_m9147EC2BE20D9441A4375CB366B43CF71AC964D5,
	ARAnchor_OnDisable_m5F3C9AE17070DAEBA3822B2DB1A6530264822BF0,
	ARAnchor__ctor_m768D346AAF28D2CBCFBDB433C06E38BE96F1E8A6,
	ARAnchorManager_get_anchorPrefab_mAC3DD1BAC5D9EF69A877978562C4C196DE9B9456,
	ARAnchorManager_set_anchorPrefab_m9802A824D21CA4FC05CDDD422C7821A409F6C0FB,
	ARAnchorManager_add_anchorsChanged_m9AEAD2307BB09DCC5093D8BAD242AAED777CC5D0,
	ARAnchorManager_remove_anchorsChanged_mF59C4EA911963F93B03E01D799007FDD18E7B0D6,
	ARAnchorManager_AddAnchor_mF86AC736D4568019E5BF0DA99720697F8970C559,
	ARAnchorManager_TryAddAnchor_mA413E51FC549593EBA992D3D8E3C7E48751075B1,
	ARAnchorManager_AttachAnchor_m8FA6B772401F90B38A06F43B8EA1B02EA82FFA75,
	ARAnchorManager_RemoveAnchor_m25BAC7FE434D25904F4D9E41EBF65A65E96D479F,
	ARAnchorManager_TryRemoveAnchor_m408B170DFF5E17E6BA8AA33BCC7EAE4B46A3E51B,
	ARAnchorManager_GetAnchor_m41112A0FE871C0097F9DA6610CA86417CF01A232,
	ARAnchorManager_GetPrefab_m0F97F1601711964828AC1394B51BDC6B4F05E24D,
	ARAnchorManager_get_gameObjectName_m30353DBEEBD961E03E816F947884C44CA30E338B,
	ARAnchorManager_OnTrackablesChanged_m2FEB6C24F7C2B439DA785DA4745F6D2FBCE684EE,
	ARAnchorManager__ctor_m15271DAC2CC3EBCF8EB107B0B53632F905E8F9B9,
	ARAnchorsChangedEventArgs_get_added_m5F9CEB4587F9496D0DA28880430B69EFDB550600,
	ARAnchorsChangedEventArgs_set_added_mEE4383EBEAFBE249A99CEAF1F0EA6FDB964BAFEB,
	ARAnchorsChangedEventArgs_get_updated_m21039F14E472A75F969EE0BA5CBA9007DC12B593,
	ARAnchorsChangedEventArgs_set_updated_mDA509077951F3AE2F2DC7300937CA5C626BABEF4,
	ARAnchorsChangedEventArgs_get_removed_mF1E9D61E21153018C87DE99722FF8820C351CFD8,
	ARAnchorsChangedEventArgs_set_removed_m18D9FF865947D6363BA0989B783B2CC746A7B867,
	ARAnchorsChangedEventArgs__ctor_m67B444466F34F943C140F8F7EC4959DC1FFDDB8A,
	ARAnchorsChangedEventArgs_GetHashCode_m278A649D0606054A9B7E7C8AFF08CC99C2685024,
	ARAnchorsChangedEventArgs_Equals_mC2C108ADF4C9783BD7470E6573196978E61FD49B,
	ARAnchorsChangedEventArgs_ToString_mA0C838C2F9AB2B51089A47F4CF0E7049F8A86BAC,
	ARAnchorsChangedEventArgs_Equals_m67E2B32F8DEAB6F7C31BF818E1F42E799B3A0DAF,
	ARAnchorsChangedEventArgs_op_Equality_m8CA56256C5101ADA8708F263BE39C40700D10106,
	ARAnchorsChangedEventArgs_op_Inequality_m2DBE5E61A355C2A128939783FDA4FAA94F3136F0,
	ARBackgroundRendererFeature__ctor_m3F83EF33358F752AFD6C177AD06A593064AFAAA7,
	ARCameraBackground_get_camera_m95EA27983775174A10F59937EB1EEDAEDB091E53,
	ARCameraBackground_get_cameraManager_m38A0B34E4DEF2B6DAB9B93B7F081846A1F503031,
	ARCameraBackground_get_occlusionManager_m0002011570FE986A7505CCBA8A98A27B30301355,
	ARCameraBackground_get_material_mD444C30747216E7E7B5FF0F714EE0DF57CC93EC8,
	ARCameraBackground_get_useCustomMaterial_m237B3CDC574BE3A8DFB9D1B3EF6855CB07B6D6DA,
	ARCameraBackground_set_useCustomMaterial_m0A3ABCEF2FD08A0C25BE125B59A3D9D8D5569FA7,
	ARCameraBackground_get_customMaterial_mA156A934FC64330835BCB9A040C51BDFC49B8C0D,
	ARCameraBackground_set_customMaterial_m64CDD14D4793D1DBC890DBE232CDB43CFDED1578,
	ARCameraBackground_get_backgroundRenderingEnabled_m25B0181C7A2321F2CE97D52C04CA73EA50A768E2,
	ARCameraBackground_get_defaultMaterial_m492949B4FA22A0DBF97EBE0D35EC52F84F06360D,
	ARCameraBackground_BeforeBackgroundRenderHandler_m3B215B54CFB6F6F00A724D9A6BE66808593ABFCD,
	ARCameraBackground_get_shouldInvertCulling_m2B97B37FD914B74390EC5D9DB92AF823B1A6204A,
	ARCameraBackground_get_currentRenderingMode_m356E6A88138C933DCACEF92FA37CC5D06BDB1231,
	ARCameraBackground_Awake_m75B40D62F040140E5280778DE567862B57F325B7,
	ARCameraBackground_OnEnable_m2BD485D597238EB65F61DB7291C421689B67E08E,
	ARCameraBackground_OnDisable_m31BF14F0E5ECF28913E1D1CC25AE72F4C8EDAC11,
	ARCameraBackground_EnableBackgroundRendering_mF0E94430015503FCB4AE4DA7B94C3419CB65AFE7,
	ARCameraBackground_DisableBackgroundRendering_m843F0468353D4AFFA7A99AA9148C7757999E865B,
	ARCameraBackground_DisableBackgroundClearFlags_m2F73DE07971DB0356E0916E00E82C92D96B933D0,
	ARCameraBackground_RestoreBackgroundClearFlags_m9E651BB4CDA1F3B9A2E0E572BD9D81045795556E,
	ARCameraBackground_get_legacyCameraEvents_m965181793B5235D43C7D9C2A1088B1A483C3E90F,
	ARCameraBackground_ConfigureLegacyCommandBuffer_mE834A7EC1EE8AF3CE85C3E115D44497A4E49BF1C,
	ARCameraBackground_EnableLegacyRenderPipelineBackgroundRendering_mE3248B2AF0F2730A475CEB096C3418317A9FE829,
	ARCameraBackground_DisableLegacyRenderPipelineBackgroundRendering_m9610B7084D5C9BD40829C570D2BACAEAFDAE2527,
	ARCameraBackground_AddCommandBufferToCameraEvent_m547997D77AA9F4C1CE620B87553303949B404236,
	ARCameraBackground_RemoveCommandBufferFromCameraEvents_m6524CE593D7CE338F442C08BAD5AB6870B14B4FD,
	ARCameraBackground_AddBeforeBackgroundRenderHandler_m4B566E1944111065C7D262FB4A3ABC5C659949D6,
	ARCameraBackground_OnCameraFrameReceived_m8671B0000ED84A20852586D60F6FC62A6BE95075,
	ARCameraBackground_SetCameraDepthTextureMode_m156A534B45A960344344336B8D9D68D5B42EE585,
	ARCameraBackground_OnOcclusionFrameReceived_m5AAA250023997466F22A824558B74F2EEBB04FDC,
	ARCameraBackground_SetMaterialKeywords_mEE55C7A06BA08ECF1D1F53EC3D79CC12213DDAF3,
	ARCameraBackground__ctor_m7CA5CCD065DA2988AAAC2ADCE38E77047875E7AE,
	ARCameraBackground__cctor_mC0E45652907684579936B9466751DD3E6D79F310,
	ARCameraBackgroundRenderingUtils_get_fullScreenFarClipMesh_mD59358E22DAF5A95AD82AE57963DCD8ACF0C5FF6,
	ARCameraBackgroundRenderingUtils_get_fullScreenNearClipMesh_mA5C2D90A8F4B4EA4F0DC0C42DE164677D2A22A85,
	ARCameraBackgroundRenderingUtils_BuildFullscreenMesh_m8B8D33BFE7ECFF88296FBB6B8A34CF1E04D8AB6A,
	ARCameraBackgroundRenderingUtils_get_beforeOpaquesOrthoProjection_m8FEF7D4C53DB2F7E8E91FC6C2546063E359B48D0,
	ARCameraBackgroundRenderingUtils_get_afterOpaquesOrthoProjection_mDD479079699B86A699A0E121B50EB6F82C413A01,
	ARCameraBackgroundRenderingUtils__cctor_m574A99D445239DDFAB3AD309403714F0F8AD0B2D,
	ARCameraFrameEventArgs_get_lightEstimation_mDAD20A000D180FB372E8214B37A8A4EB4F0F312C,
	ARCameraFrameEventArgs_set_lightEstimation_mE04691D14C2CD985D757C11AA126AE60BC6DBB51,
	ARCameraFrameEventArgs_get_timestampNs_m3B9E21BF0B36F035E105ED2B155983E03B21F4E9,
	ARCameraFrameEventArgs_set_timestampNs_mF3DE3C39790E92B6F2190BEE8DA69568C8A47F20,
	ARCameraFrameEventArgs_get_projectionMatrix_mA09F2170ACA84AEC8E9407DE70B9CD1D2E443182,
	ARCameraFrameEventArgs_set_projectionMatrix_m7F7C0173EAC5E149E6A4A6FA9FA85E8C071DF517,
	ARCameraFrameEventArgs_get_displayMatrix_m3354FFAEF054F0A8F4D19BFD506468A06F3AD65F,
	ARCameraFrameEventArgs_set_displayMatrix_m4DB301FFAEAC7277FD816737053A2688B9CDE902,
	ARCameraFrameEventArgs_get_textures_m96823876351BD11C26D78BAA01936FF751182547,
	ARCameraFrameEventArgs_set_textures_mB9F3D579FB8DA3BDF3F4B85D1E598AE19959BAC7,
	ARCameraFrameEventArgs_get_propertyNameIds_m9B76BC52DB8349A38D5514528C63A36494283798,
	ARCameraFrameEventArgs_set_propertyNameIds_m7F4470E1C46E52045645D6C93923F6DB7A92C6B5,
	ARCameraFrameEventArgs_get_exposureDuration_mC4D1A5D0266949EDB98A74E66564E421A9B2A2F8,
	ARCameraFrameEventArgs_set_exposureDuration_m48C0FBF3D428A1507BB522D23B2A383B265F4B1B,
	ARCameraFrameEventArgs_get_exposureOffset_mFDCFBB779CF302DF2159000D328826E7880FDD0F,
	ARCameraFrameEventArgs_set_exposureOffset_m82EEA2BFFE750731718B4B39FBCA59BC0B4CD2EE,
	ARCameraFrameEventArgs_get_enabledMaterialKeywords_m76A2E83147560A72FF3834BF0FB7C1B3FEED6240,
	ARCameraFrameEventArgs_set_enabledMaterialKeywords_mE8FD88DCAEFA52B0B6E05DA72F61AA7727D88E9D,
	ARCameraFrameEventArgs_get_disabledMaterialKeywords_m254ACC80879CD8B270566B38CCD6ED99D26255A8,
	ARCameraFrameEventArgs_set_disabledMaterialKeywords_m95D9308AC9AA63B459F87C44AD1B378043C08BDA,
	ARCameraFrameEventArgs_get_cameraGrainTexture_m0CEACD5C125C07A7D6E73695808202940300B633,
	ARCameraFrameEventArgs_set_cameraGrainTexture_m2BBDCEB456D16E1AD14D74874CD3F274806F1D61,
	ARCameraFrameEventArgs_get_noiseIntensity_m441B0EFBF898D8A18AB1E102E41CF1BB33072591,
	ARCameraFrameEventArgs_set_noiseIntensity_m8AA71321684DA61B4D83D0C2F91C85AA9A2FEE7E,
	ARCameraFrameEventArgs_get_exifData_mDB0728F64EE619CB4174644B6D58598DAE8EC2F5,
	ARCameraFrameEventArgs_set_exifData_m1FB5103D69EEE478654B39783FCD7D662F557E55,
	ARCameraFrameEventArgs_TryGetExifData_m6AD832BCB4867C3D42D03A0CB6B291BAE259B3E0,
	ARCameraFrameEventArgs_GetHashCode_m13E6D8C9B47ACA833C0D2F23C838883FEF4A464F,
	ARCameraFrameEventArgs_Equals_m896C0CD5FC48A354B4AE1CA23F7219FD812873CF,
	ARCameraFrameEventArgs_ToString_m31AA489F1C81FA10709305176CE7EE2676F28CD8,
	ARCameraFrameEventArgs_Equals_mE86A86B648BFB94A2CB70BE472F974C550AB6CC6,
	ARCameraFrameEventArgs_op_Equality_m332C80B6B4C598563AB455FB199A8C60FDF178F5,
	ARCameraFrameEventArgs_op_Inequality_m86840F89781D14FDBC398171DFC6C6CB19CE3D12,
	ARCameraManager_add_frameReceived_mB30CD73DB7A25A6E7BC486BA289DBB99CBA3B7A1,
	ARCameraManager_remove_frameReceived_m8D9C301201B5079323169510656821F0286AA67D,
	ARCameraManager_get_autoFocusRequested_mCE8CB1883F0FB30F5DF9A0F2B1F6601CA264A26D,
	ARCameraManager_set_autoFocusRequested_m1E9D7EAC0CB61ABBD9A1240BCBB1A77CB22E219B,
	ARCameraManager_get_focusMode_m15289C64A6AABBE562681C70A0A743DC01C2F209,
	ARCameraManager_set_focusMode_m65C339A2D43423E5A0A3FB954A9CB5AE4993A9C7,
	ARCameraManager_get_autoFocusEnabled_m88A78675BF5EECB9BA3DE2EC0308D38987D1497F,
	ARCameraManager_get_requestedLightEstimation_m1ABD158E3BE20A7F2B311071D44E8A796E209BAB,
	ARCameraManager_set_requestedLightEstimation_m6571108C98F7622CCA555F91668359B1A2F61D4B,
	ARCameraManager_get_currentLightEstimation_mBD80DD338D8DF62515FABD05820CA3ECEEAF6B28,
	ARCameraManager_get_lightEstimationMode_m61EC23A65B4C4679DEC893F573EB13EE189053BD,
	ARCameraManager_set_lightEstimationMode_m1A54F00ADF92ECCAD88F59F019CBB7B9F9C87A5E,
	ARCameraManager_get_requestedFacingDirection_mDD9519E3F839B1ACC242388DF0757AD18069A19F,
	ARCameraManager_set_requestedFacingDirection_mFF42F5C8B185257403AFF6B963E5C1F47020FF5B,
	ARCameraManager_get_currentFacingDirection_m79981A1F52B2C8B74495608E9A7F4179BAAAD6F4,
	ARCameraManager_get_requestedBackgroundRenderingMode_m80BFC959A25B1680BF2DB13E147AA839A7623602,
	ARCameraManager_set_requestedBackgroundRenderingMode_m8AA1C64E3948DE390BF13BA0066EA60448E8B64F,
	ARCameraManager_get_currentRenderingMode_m33B99B6C897ECD63093F8D6201ABB65FE3690321,
	ARCameraManager_get_permissionGranted_m6C34AC20847F992C4213E08AB86DC5B261DD7CE9,
	ARCameraManager_get_cameraMaterial_m6EC2895B604AE3D79F8176A8D9C97B531E937437,
	ARCameraManager_OnBeforeSerialize_m64A21146DEE3C6AE9C9774BE2E6F43824C85C31D,
	ARCameraManager_OnAfterDeserialize_m538D9B456947A0C4CE0C3A08518B9B3F133BD00A,
	ARCameraManager_TryGetIntrinsics_m9D3306A91112F7E3C42A3D382290A6C94BADA8D3,
	ARCameraManager_GetConfigurations_mC8E7B130EA3DDF94810B8939F0BBC38466A74316,
	ARCameraManager_get_currentConfiguration_m1E20D407F73102BA55EC6CDC4C6AAC6C6C716509,
	ARCameraManager_set_currentConfiguration_m300F3F31B734E15C56E136F1E2285E873647823F,
	ARCameraManager_TryGetLatestImage_m0CECFD8E8B65B22AE4712EACF68BB4EEA31E3C11,
	ARCameraManager_TryAcquireLatestCpuImage_m79247340777701FCF1E9CF274D1FC6EAFE0AA874,
	ARCameraManager_Awake_m7010013BE02DF0DEFBADA398237FCF758EEBB7E5,
	ARCameraManager_OnBeforeStart_m0094D8B406D9C69AE6BBCAEB985F23267CD9CB01,
	ARCameraManager_OnDisable_m97049FA6F9676AEA13B003C5641B33506342BBAD,
	ARCameraManager_Update_mCEC545D77C80228A07B589408426D03420535C17,
	ARCameraManager_UpdateTexturesInfos_mA64F17DB0F88464B42F59EC91AAFBC95DAC866F0,
	ARCameraManager_InvokeFrameReceivedEvent_m56E1A41175297197EAD1DC4841536B198B56C3C9,
	ARCameraManager__ctor_mCD28CAF4C17EE19A33240ED4E81EEC23ACCC1893,
	ARCameraManager__cctor_m2CCB79CEFC22E72D5A03A5AC34F39368B710977D,
	ARDebugMenu_get_originAxisPrefab_m259DE0627CD28F49F45B1C5C536A63E1835C4A74,
	ARDebugMenu_set_originAxisPrefab_mC8DD66E33441AAE7DC3B7F7C8291A79B342D441B,
	ARDebugMenu_get_pointCloudParticleSystem_m76EDC2F6676A4EBC45AF9CF9781439444C79AB8E,
	ARDebugMenu_set_pointCloudParticleSystem_m9F7E45C7F201092F183514A88EDF564CAD4CEBDD,
	ARDebugMenu_get_lineRendererPrefab_m08646C973FDF4EE351D9BFD60C39FBBD1E8A6439,
	ARDebugMenu_set_lineRendererPrefab_m18A7C54D96B9631231B1DD275678B593D20C146E,
	ARDebugMenu_get_anchorPrefab_m431D27D360C714D9A2C940DCBCF4718C47F27B1A,
	ARDebugMenu_set_anchorPrefab_mDDF915B31935FA7CFB94DB4E13F77BD69F9D9285,
	ARDebugMenu_get_displayInfoMenuButton_mBB1499ED6DE0512F632AFEB3D818041F2AC1427B,
	ARDebugMenu_set_displayInfoMenuButton_m994E2B8A4D37BE37687F415D37BB4E357237D7D7,
	ARDebugMenu_get_displayConfigurationsMenuButton_m1E85917F57D98007D6A205B002D2850215017100,
	ARDebugMenu_set_displayConfigurationsMenuButton_m56AEDA2F2FBD818AB4AC40D6F34AC63C2D182E70,
	ARDebugMenu_get_displayCameraConfigurationsMenuButton_mC5636623C7652FB95B512F49A53A06B2245684F6,
	ARDebugMenu_set_displayCameraConfigurationsMenuButton_m8DB18DEF3AA93F356D4BE698D3703D2C6019C52F,
	ARDebugMenu_get_displayDebugOptionsMenuButton_mF8DB109B25768B71B1C0ABDAFDDD27D5E7D80534,
	ARDebugMenu_set_displayDebugOptionsMenuButton_m665DD91C50996EAB204822C703747CE912680B4C,
	ARDebugMenu_get_infoMenu_m41FA06B25A2CF5ACE16CC55E4ACDFABD63A7A5E1,
	ARDebugMenu_set_infoMenu_mD595F868A1210903F57A4A53B6A0E4D34991AA2D,
	ARDebugMenu_get_cameraConfigurationMenu_m97F899567FDB81CFD10F8EB85E111BE31F711E1F,
	ARDebugMenu_set_cameraConfigurationMenu_m842BBF87BFF187B117A8965013011FD79229D713,
	ARDebugMenu_get_configurationMenu_mE2D40BE4B00FF96BE322F15CD912F7571617B957,
	ARDebugMenu_set_configurationMenu_m014807B22B8DA18F74352A266B1873C7D0557974,
	ARDebugMenu_get_configurationMenuRoot_m4CAF9556F715C65C3F92DB632F327683FD5EA913,
	ARDebugMenu_set_configurationMenuRoot_mB72E6BDE0E9512AED47C67A79D772A4C60950E89,
	ARDebugMenu_get_debugOptionsMenu_m3592E84BE276DE8A601DEE1E723F0596501A086B,
	ARDebugMenu_set_debugOptionsMenu_m2A751D87B3BA360751344E1F42C61CF67A39B8B2,
	ARDebugMenu_get_debugOptionsToastMenu_m7716D3A31359726ECE1355D8DD0F607E613BF878,
	ARDebugMenu_set_debugOptionsToastMenu_mE1B73042FE4178A67B946FA9B079DC10AC63287A,
	ARDebugMenu_get_showOriginButton_m8CE0DDF01855C3A92D6DE16BFF03CEA9ED1E240C,
	ARDebugMenu_set_showOriginButton_mA51FFEAF00AB7480DBE17B09A6F0E5D8445A7384,
	ARDebugMenu_get_showPlanesButton_mF52159758B0A0174D1C78B740FA1F64E50430813,
	ARDebugMenu_set_showPlanesButton_m0444CA1E9E0CF97A1A7415EBBAA8FDAAF5B29CF4,
	ARDebugMenu_get_showAnchorsButton_mA3049069F350D9AB6899B41AA68C63C2B9653E3E,
	ARDebugMenu_set_showAnchorsButton_mFD811EAC32C501BCE9808AFB76B0EBC1EA803BBC,
	ARDebugMenu_get_showPointCloudsButton_m2CEFD047FF9783ED12076E0E96F41AA4A4EEBBE3,
	ARDebugMenu_set_showPointCloudsButton_m2663A91862C86AD4A1C294A52C0EE533477707D9,
	ARDebugMenu_get_fpsLabel_m5F30A720414ACF312AE8CB545663709F8E97AD64,
	ARDebugMenu_set_fpsLabel_m4F81FE78D8E1D826132BF47F30237C35E3482C1C,
	ARDebugMenu_get_trackingModeLabel_m5EE6A7089B40E825D10316271A633BB90D8F29B9,
	ARDebugMenu_set_trackingModeLabel_m0B1EB6964F99A0B71D43C7C59FB5CD0C274E1066,
	ARDebugMenu_get_checkMarkTexture_m20E9C63E88D62E612A020762967D777CE5620400,
	ARDebugMenu_set_checkMarkTexture_mBB2BB41DD309F0D7158F56C726E85F86AD1305BF,
	ARDebugMenu_get_toolbar_mA3886383D3240DE3DBE281CB016EBCDA335D7F65,
	ARDebugMenu_set_toolbar_mAC228540FE0B8A52173592AEF03716AD564F0BB6,
	ARDebugMenu_get_menuFont_m27FED893786F6DDEB04D792A7FCB05EC978B9CC1,
	ARDebugMenu_set_menuFont_m6FBF97973C3D6D8B23A253BDFC639ABF35BAF1A0,
	ARDebugMenu_get_cameraResolutionLabel_mCB4D0203D8DB11406EDCE1E0513C8946CD921551,
	ARDebugMenu_set_cameraResolutionLabel_mC1A6F0519A7F52AF2A0A3AADBB10CE87D488B6C6,
	ARDebugMenu_get_cameraFrameRateLabel_m9FF008BF41AF441B37982468F39B643006DDA0E9,
	ARDebugMenu_set_cameraFrameRateLabel_m9DC6A44C13719EFFD78C2F9B6A2E0B86793D2356,
	ARDebugMenu_get_cameraDepthSensorLabel_mEA7B077A789E1C98FA6A3AADB5F45CA70C12A394,
	ARDebugMenu_set_cameraDepthSensorLabel_m1C52F367CDF7C8ED15FFDE93DD4FF8E5C7AE647E,
	ARDebugMenu_get_cameraConfigurationDropdown_m4F7018130C23AE99CCDA8020125CCAB94A409634,
	ARDebugMenu_set_cameraConfigurationDropdown_mC40D11896E7B00F3678FCAE8B8E0BF96095B2A1D,
	ARDebugMenu_Start_mF7993FB02817243DD5CDCCDEB4AFC7C466D51B60,
	ARDebugMenu_CheckMenuConfigured_m6014B307D602CA4A889B4E9FAEBC311A7D111886,
	ARDebugMenu_OnEnable_mD58FD60A713AFD5E08BF2B826A119FC25DC52742,
	ARDebugMenu_OnDisable_m3A6F9167E65992B233870D99BB4C8E9FF5394EB8,
	ARDebugMenu_Update_m8D04A8338E3AA4315BCD6C722601757308297717,
	ARDebugMenu_LateUpdate_m65A57ECDD231E99F55C5C34404BCD69F3ADB24E9,
	ARDebugMenu_InitMenu_m47EA4DA919CBFDDFFAC22FE0BCD1AA6CA8A1C51D,
	ARDebugMenu_ConfigureMenuPosition_m21B976B1BF4847FC4A66E984096061F54D284A0C,
	ARDebugMenu_ConfigureButtons_m7473E9227B9A55D8F11C1C62A4681496B59C6356,
	ARDebugMenu_DeregisterUIListeners_m80D1D95E645F4DEC248CF0C5EC72358F402DA799,
	ARDebugMenu_ShowMenu_m5D24D6FC775B03E1505447E71F76418613DEF86E,
	ARDebugMenu_ToggleOriginVisibility_mC8E861AB27C07B861578DDCB3AD4EFEAAD3AAF8E,
	ARDebugMenu_TogglePlanesVisibility_mCDC25C7BAF2A64E4C16F0782D171D9E869245471,
	ARDebugMenu_TogglePointCloudVisibility_mD728FD954EA73722156906A95BB2C046CA52DF18,
	ARDebugMenu_ToggleAnchorsVisibility_m904AAA5D760DF956B3AD4F1A63BE7D3B188FD5DF,
	ARDebugMenu_FadeToastDialog_m3BA93AEA5FBE8BA61227918E8F5E1DC9962C33A2,
	ARDebugMenu_SetupConfigurationMenu_m42CC629EBEB5074AF9B5FB626E02450F4651D6DA,
	ARDebugMenu_DisableToolbarButton_m17FE4E45DCF2FD9B6450943DAC7EA55924FCA688,
	ARDebugMenu_CreateConfigurationGraph_m3819A23B1869B2150E2559E480A8BE3993E3F0E1,
	ARDebugMenu_GetSessionSubsystem_m05ECCFEAE7AB5D1436035C208AA260366B8726E3,
	ARDebugMenu_CreateBackgroundColumn_m5D2CA28F243A010419A18C756750947D37A4065F,
	ARDebugMenu_HighlightCurrentConfiguration_mAF71E95E416877F2EEB5C449742D1B072F3C6A50,
	ARDebugMenu_FollowCamera_m8EA92021B40CA00DB5E3B1087675802A8AA43983,
	ARDebugMenu_OnPlaneChanged_m657A60AC2F55B12E70F081EE27EA967DDCDCF687,
	ARDebugMenu_OnAnchorChanged_m4BC324913AA1E0779649049CFF0E678660253C60,
	ARDebugMenu_OnPointCloudChanged_m0D596AED1110701F82EBC436B9FCB8A6AE6E6329,
	ARDebugMenu_GetOrCreateLineRenderer_mC0082E9019FBAEA5E0C53EB9DF18DE6BA7CEB478,
	ARDebugMenu_GetOrCreateAnchorPrefab_m158EEB290F5A40F5614F4DC84A672FCB530A2F77,
	ARDebugMenu_CreateOrUpdatePoints_m7BBB4FA4740C38A050013C786BDC8D91545E0715,
	ARDebugMenu_RemovePoints_m714640EED976E552D14349D3FF5F15A3FDD728CB,
	ARDebugMenu_RenderPoints_mF255E5A894987ECA0F5A48B46838584FBD79DD65,
	ARDebugMenu_SetParticlePosition_m4A99F3FE98FB7C254063A5095D3ACC14AAA24E4E,
	ARDebugMenu_UpdateLine_m98E9D5E72E6A0183493082B7B84419115DF572BB,
	ARDebugMenu_OnCameraDropdownValueChanged_mF183F21ACD6890CBF25E95B6DF0441E3C91A142C,
	ARDebugMenu_PopulateCameraDropdown_m371BE799DA6E2E68EF63564261E27A75836A5798,
	ARDebugMenu__ctor_m7AE42EF12487BAD4AAFED8CE0728B4FF4592B800,
	ARDebugMenu_U3CInitMenuU3Eb__138_0_m4B60004B0693047863F9551F27CA3F3ABB3A6A7C,
	ARDebugMenu_U3CInitMenuU3Eb__138_1_m28C8DA346CF3493D93305D15362A814B758D8BD4,
	ARDebugMenu_U3CInitMenuU3Eb__138_2_m1B6BBAD3EDAD0EFA520A9687A0430954ADC88D59,
	ARDebugMenu_U3CInitMenuU3Eb__138_3_m14B2B2F0FD61F5E5F49968D93E63B9AC3175001E,
	ARDebugMenu_U3CInitMenuU3Eb__138_4_m46F6411A585219CB316CB61A5C107345008BAE4C,
	ARDebugMenu_U3CConfigureButtonsU3Eb__140_0_mDCA8551319A3D86DD6F88CEE0CC9111616149B9F,
	ARDebugMenu_U3CConfigureButtonsU3Eb__140_1_m057C71CC0608C9F6F03FDC05D1AB5E442ED1A749,
	ARDebugMenu_U3CConfigureButtonsU3Eb__140_2_mD8401BDF55C056700424458D5019BE4A5FB524A1,
	U3CU3Ec__DisplayClass140_0__ctor_m3ED6432B0598D40453EFC23919F27D385D43C199,
	U3CU3Ec__DisplayClass140_0_U3CConfigureButtonsU3Eb__3_mD2F9CED59BA60A1983C54526646978ABEE053A38,
	U3CFadeToastDialogU3Ed__147__ctor_mFF262D31E0755AE69B3302AC02AA2C7A8A944712,
	U3CFadeToastDialogU3Ed__147_System_IDisposable_Dispose_mF753B23F4C4BDCA2F4C673A571193934ED87C974,
	U3CFadeToastDialogU3Ed__147_MoveNext_m9DDA364B94B070B8689DF212B3F0A1D4A8C47727,
	U3CFadeToastDialogU3Ed__147_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m008D9762519F52D0101C25BAF7D1F210ACF83B0B,
	U3CFadeToastDialogU3Ed__147_System_Collections_IEnumerator_Reset_mE225D44E7D14E46E41D4009FF990FF560733009C,
	U3CFadeToastDialogU3Ed__147_System_Collections_IEnumerator_get_Current_mE8BFCA8E4FAB6D3627AED49525898B7D7D24AD93,
	AREnvironmentProbe_get_environmentTextureFilterMode_m92C6377EA4F61A593CA82BE8028FE707011AD2C1,
	AREnvironmentProbe_set_environmentTextureFilterMode_m06B80A36CCCFA8CBD770FA638CBD5B6833A16B40,
	AREnvironmentProbe_get_placementType_mDC47CC6DF864F84A227E7EAEFF90976E71DAAEE8,
	AREnvironmentProbe_set_placementType_m88E37666B552F5D26A658CC799041589690B1AC0,
	AREnvironmentProbe_get_size_m7F165EE19D2AD44A25EC017CC5D319B150C2F942,
	AREnvironmentProbe_get_extents_mA795818D4A5D96B96FBCBC16B811B520F21741AB,
	AREnvironmentProbe_get_nativePtr_m469302AC396B6CB4F337E7D4708CAAF5C19CB5D9,
	AREnvironmentProbe_get_textureDescriptor_m83F1A3C0BE6CC6F7577258B0079B7187B376CBDE,
	AREnvironmentProbe_Awake_mE25DD39C415F594D863D6790DC0D88FAED586DB1,
	AREnvironmentProbe_GetTrackablesParent_mBFBC7FBA4FEC0D68C6B4D132FEFB0B9FE077D880,
	AREnvironmentProbe_OnAfterSetSessionRelativeData_m029574C26A74DDC5EE20636BB01034D4A50D583C,
	AREnvironmentProbe_UpdateEnvironmentTexture_mDDDAB7FC75929880637A253DE1B0688F614374A7,
	AREnvironmentProbe_ToString_m48F93ED6D32DE124123C0DFE83D7FEE55AA3546D,
	AREnvironmentProbe_OnEnable_m49E68299AFE6FF15CED372D6187A07D68CE84D0B,
	AREnvironmentProbe_Update_mDADAFB19F0B5A3A73DEB83F897F474B36F6BF99D,
	AREnvironmentProbe_OnDisable_mE50C0A9638259ED5870038F1A3A6AB8F1AD7AE73,
	AREnvironmentProbe__ctor_mB58D3CE9CCF895B0E1C829B643C996E3AEF5699E,
	AREnvironmentProbeManager_get_automaticPlacement_m83BBBB2F7F9A8FECE8B44F73502C873474BA8026,
	AREnvironmentProbeManager_set_automaticPlacement_mE2059D886D13DA19FAADE1AC7D5EACB9902EE5C1,
	AREnvironmentProbeManager_get_supportsAutomaticPlacement_m7D382800D518CD11997DD971312055F79A3C1CF6,
	AREnvironmentProbeManager_get_automaticPlacementRequested_m543115D74E87157FDB6F43876D5C22A512E252B9,
	AREnvironmentProbeManager_set_automaticPlacementRequested_m432779702A1ACE667BA0B697F902E33121EB71DF,
	AREnvironmentProbeManager_get_automaticPlacementEnabled_m3EB4F6061CC0994BEA30C653BD5A25187EA47EF5,
	AREnvironmentProbeManager_get_environmentTextureFilterMode_mA1F5EB09E8881D4187B81F1A29B1753FF0AB7846,
	AREnvironmentProbeManager_set_environmentTextureFilterMode_m76E7C9A7C13E3C849AD8701C363C6F4902BFE51C,
	AREnvironmentProbeManager_get_environmentTextureHDR_m06EABE61A6739A6134B0D3499CDB46E60C25124F,
	AREnvironmentProbeManager_set_environmentTextureHDR_m0C05AD9E24BCB7229787138410FFAD6874584960,
	AREnvironmentProbeManager_get_supportsEnvironmentTextureHDR_mD9F3A566A8B93C40DB8C4D83C379548FAE40280C,
	AREnvironmentProbeManager_get_environmentTextureHDRRequested_mEA9DE2473292137FB9661EB986675F943876CF10,
	AREnvironmentProbeManager_set_environmentTextureHDRRequested_mB18115A2A309F3E0A037F310EFC91327639FFC29,
	AREnvironmentProbeManager_get_environmentTextureHDREnabled_m12AEFA87AF6EA74DDAA357735978115B9E2C6579,
	AREnvironmentProbeManager_get_debugPrefab_mD6F19E1518C49C7080F3267140CAABFD86E95908,
	AREnvironmentProbeManager_set_debugPrefab_mAD3B4C8AAB07D3E6ACD5C73FD8AD08E6B4471896,
	AREnvironmentProbeManager_add_environmentProbesChanged_mADB9F6A00161EF9FECCFF0C76FC346E5A6321A96,
	AREnvironmentProbeManager_remove_environmentProbesChanged_mABD9405FD4C3272C46B79CA7586901101C243FC7,
	AREnvironmentProbeManager_GetEnvironmentProbe_m376D4F69F5F74C54A05A37E846331FE91C0C39E2,
	AREnvironmentProbeManager_AddEnvironmentProbe_m9732DBDE74395B58377BCFD261234CDC1CCD2AB9,
	AREnvironmentProbeManager_TryAddEnvironmentProbe_mFCA56125452C0EC5C749666FCE931F3508F70FFF,
	AREnvironmentProbeManager_RemoveEnvironmentProbe_m018D89EAFFECEFF2AFBE3F779B56C1FDEBCC6A35,
	AREnvironmentProbeManager_TryRemoveEnvironmentProbe_m0C4DC46BAB27B6E8D998EE6145AF3646C6CA3BC9,
	AREnvironmentProbeManager_get_gameObjectName_mBA5365658D8161D25501B5021F430C6A46419B85,
	AREnvironmentProbeManager_GetPrefab_m0A7F459227BA94846FD236E9EBF76C46A8561C37,
	AREnvironmentProbeManager_OnBeforeStart_m4A900DAA58DC7AFE0267719DD5EA83E65B5D2F49,
	AREnvironmentProbeManager_OnDestroy_m6DE8F413FC37199204D4FD23DA48878DBE583F23,
	AREnvironmentProbeManager_OnTrackablesChanged_m7B80003F63B937537625F138BC429CDDCBA92ED7,
	AREnvironmentProbeManager_OnCreateTrackable_m0609B6E9F1E2D5B40EF204FB97B90AC6B3099A56,
	AREnvironmentProbeManager_SetAutomaticPlacementStateOnSubsystem_m652A140E012CB9B945EDCAFD56E95BEA312C67DA,
	AREnvironmentProbeManager_SetEnvironmentTextureHDRStateOnSubsystem_m6EDC7E710689568ADE736B19D124A44A124AB3DC,
	AREnvironmentProbeManager__ctor_mD60AA1F4A8CD9BA3575D8485B37E86994CF97C07,
	AREnvironmentProbesChangedEvent_get_added_m5EB8CAE059E5A0F0A84A11866F2343F630B7C997,
	AREnvironmentProbesChangedEvent_set_added_mD254DC5C2E0B0AC8D0F604829177E92AAA36DB98,
	AREnvironmentProbesChangedEvent_get_updated_mFFE292486A4D2DB4385C70FAE94EB6048CBB99F3,
	AREnvironmentProbesChangedEvent_set_updated_m8346F2C232C68965428428BFC1D42EF188390D27,
	AREnvironmentProbesChangedEvent_get_removed_m0D5B42BCAD8408B1FC0DF82E24C4EAF1D8ADF40A,
	AREnvironmentProbesChangedEvent_set_removed_m1C122637075CAC8EE8D30678068F20C10F9805B8,
	AREnvironmentProbesChangedEvent__ctor_mA834200E5CC7AEB6D93ED5B987F19D24A9C82944,
	AREnvironmentProbesChangedEvent_GetHashCode_m1F82F2279F41834A46A159D45D9FA4C37ABDEFF6,
	AREnvironmentProbesChangedEvent_Equals_m5DC12B1B83E64882261F789A0651ADA40D58D7C3,
	AREnvironmentProbesChangedEvent_ToString_m66B3C7111A9054FAB69BABF82CF0789974FBA048,
	AREnvironmentProbesChangedEvent_Equals_mAF37E53C6392E0457299A76EE7CC5D805BDF4D64,
	AREnvironmentProbesChangedEvent_op_Equality_mFF839F4FC56E55C561CF427006B2CACB38EE588B,
	AREnvironmentProbesChangedEvent_op_Inequality_m674DEA826D1570F1075F8EECA67BB2A1C9B356F0,
	ARFace_add_updated_m20975C4A22DDFCC57D15C0205754C3902A699738,
	ARFace_remove_updated_m289BD0DA8FE0FF5A2936B6FD21FB4895AFB61499,
	ARFace_get_vertices_m5248E2AF1A5377DF265F89D06A0DAC2DBA27504C,
	ARFace_get_normals_m915D6684CDD31EEF5868D000AB58A106CA8AD725,
	ARFace_get_indices_m99D2797D6B0BEEB096FBDFB0429FA42B6E4BC4F2,
	ARFace_get_uvs_m2A17FCE4279C523F03B08C1A33F4F32E3FFA644E,
	ARFace_get_nativePtr_mF951F5F0845138234A1A8E46B1C038638FE01C21,
	ARFace_get_leftEye_mF83D665DDA85EA92A4032B5AD3F73D5F7CA94349,
	ARFace_set_leftEye_m5AF65F3CC81D824E048B71D04D03B1AEA7E94826,
	ARFace_get_rightEye_m7388FC86889DD13F557A9BE697CAD20B160DB29E,
	ARFace_set_rightEye_m91F356CE6B8E1AB5F44B92EEE0AAB006FB6C0BFD,
	ARFace_get_fixationPoint_m6CFEF4FF10D226A45525EC86F2AD14DE1722D0D8,
	ARFace_set_fixationPoint_m33F919437C4FA404DD86068AD29987190A413224,
	ARFace_Update_m0DE4EC42F26E5CEE6E665DB842DEED87D5432219,
	ARFace_OnDestroy_mABE9113FB97FC55BACF6E4962E067BCADE91B264,
	NULL,
	ARFace_UpdateMesh_m1F3D6373D09116FDA91270BB7832AA022A7EC524,
	ARFace_UpdateEyes_m3C724563A1E7043E38D1B896B28D239C69DC3FB6,
	ARFace_UpdateTransformFromPose_m942F3434C90AF500AB693E69FD8966E15F1EAFCA,
	ARFace__ctor_m3DC462489C771F46E11BA8D18446B2751A5E1687,
	ARFace_U3CUpdateEyesU3Eg__CreateGameObjectU7C29_0_m3299BEC27F75349075882DDACF55A2B7FBA7709C,
	ARFaceManager_get_facePrefab_mB97FB7247DA2EAFCC642D71C1826216B1C2F4380,
	ARFaceManager_set_facePrefab_m817BF61BC85C89439E7740C986274D3967351427,
	ARFaceManager_get_requestedMaximumFaceCount_m155E3DDDFDBAF3F52FF2276B23F27A8BF39F7D83,
	ARFaceManager_set_requestedMaximumFaceCount_mD5CA3BD35E3AAD9423A3CC88921D56083E727278,
	ARFaceManager_get_maximumFaceCount_m6854F8AB7381DFA7C5E4534B7EB3CC48E1162AF2,
	ARFaceManager_set_maximumFaceCount_m5B877FE343E0BD0348A9E60BA9083F24B221FD60,
	ARFaceManager_get_currentMaximumFaceCount_m1CFF61ED04271FC2E4890B6F8AD9A345E3D643DC,
	ARFaceManager_get_supportedFaceCount_m2F343026AE979F36529B713A9B6A44F2FE3B57C2,
	ARFaceManager_add_facesChanged_m5EA6C3526D8E44028C6DCF7A6297D7EAE240105E,
	ARFaceManager_remove_facesChanged_mBFBA84C023749078DC7D801945CF83373B336DCC,
	ARFaceManager_TryGetFace_m607F9345904F06EB26F0220BE3AED75A14FBFD5A,
	ARFaceManager_OnBeforeStart_mD1C6B8B194CB252561CEA3866795CA15E29669FB,
	ARFaceManager_OnAfterSetSessionRelativeData_mB7213CC0AFD22EC8393C01C2F7C234B916238A95,
	ARFaceManager_OnTrackablesChanged_m50B1275196ECF6049BC1F8FBA6C628C727F35B7D,
	ARFaceManager_GetPrefab_m2E5C6E8D3DD0338DE887ABD3EA61A08C5499B682,
	ARFaceManager_get_gameObjectName_m1A433377D66F0B7C217D958166CEDB65813C37FF,
	ARFaceManager__ctor_mC4AB2DA06A27538F600B5F7BA56D2FA3FB574185,
	ARFaceMeshVisualizer_get_mesh_m8DFF3C9F6FA8233C67918831AC6640C902CC0317,
	ARFaceMeshVisualizer_set_mesh_m042183A8A5919F50C53C0CE5ACB0B38D977BD288,
	ARFaceMeshVisualizer_SetVisible_m5FEE4245326A5DA13A9C9C6FAE59E5B791751D37,
	ARFaceMeshVisualizer_SetMeshTopology_m0E862DE0D7AC2FB6A4649770204784CC6B8812CC,
	ARFaceMeshVisualizer_UpdateVisibility_mF3E693708B1CDA925E2458D1B9B3C12C3AFE4416,
	ARFaceMeshVisualizer_OnUpdated_m85A34C40F5075FC4326C8BE6369EDA4A337CC06F,
	ARFaceMeshVisualizer_OnSessionStateChanged_mBEBEE52B39FBE5D1FD96B68948A0B9D537A9C943,
	ARFaceMeshVisualizer_Awake_m02EFC7A7F287BC2B880E3C060E6D05050089C596,
	ARFaceMeshVisualizer_OnEnable_mAD29EDBD3930D23869B9C3DDE68AB1BF934E3E76,
	ARFaceMeshVisualizer_OnDisable_mED42B3DEEA4C700C7D3603519EE86456CFDDC2D8,
	ARFaceMeshVisualizer__ctor_m8057677E2EF2E2B719EB33091E89C55288D3BDD0,
	ARFacesChangedEventArgs_get_added_m8EA1424F8F262955AFD00664B81C15AB54EFA213,
	ARFacesChangedEventArgs_set_added_mF3FF69CFE03334FC9582BFF06D5124961F955D43,
	ARFacesChangedEventArgs_get_updated_m1D3587AA05E5789F796A71C3C87CD6D11BE0005B,
	ARFacesChangedEventArgs_set_updated_mAD5B3FD1E0A6058BD6328793BA0505E82B476469,
	ARFacesChangedEventArgs_get_removed_mC31806DE526D3BECAA26C35DAE321F2DE77C335E,
	ARFacesChangedEventArgs_set_removed_m0A6A558713C45E20F0040C50763F01B1E061E387,
	ARFacesChangedEventArgs__ctor_m4C7990AA3703F850C5B4D0CCCCEABBC0B58DB940,
	ARFacesChangedEventArgs_GetHashCode_m67DD1456E147A37E35FF2483BC7053D59B549CF6,
	ARFacesChangedEventArgs_Equals_m08B2E52D4FD329CAB89DC14BEF84E55274E2EAEB,
	ARFacesChangedEventArgs_ToString_m0773324E2DC3392C12286F68495590B2F1E7C057,
	ARFacesChangedEventArgs_Equals_m32B401EBB3F1CFE364F4F2C5B7746F8D48B1DDDE,
	ARFacesChangedEventArgs_op_Equality_m851D0A36D19E2133F6E62BA9201F99844C986C3F,
	ARFacesChangedEventArgs_op_Inequality_m5AAB9E433F4D506C3FDF9BAEAA67F53515DEC13B,
	ARFaceUpdatedEventArgs_get_face_mFD21EF59457212494C80FE998D1CA8AC0F057170,
	ARFaceUpdatedEventArgs_set_face_m0E59FF874A145B994A77250CEA675FBF8AD3019C,
	ARFaceUpdatedEventArgs__ctor_m3D4294A61EBEAF3158E5C90C57FB112020A58987,
	ARFaceUpdatedEventArgs_GetHashCode_mCF1AF45853FADD6B4F52B43038647782B888B700,
	ARFaceUpdatedEventArgs_Equals_m24F398E98F52EBA0DF689C392CF58291742028F9,
	ARFaceUpdatedEventArgs_Equals_m50565F99C9CC4FF2F3122EF78D2C4DE1881E1BDC,
	ARFaceUpdatedEventArgs_op_Equality_mD62113C65CC5A0EFD17110D588D6804E35D28462,
	ARFaceUpdatedEventArgs_op_Inequality_m2018D0A528AF15ADC1C3903D75231E3BE8A60A70,
	ARHumanBodiesChangedEventArgs_get_added_m6FD862CC8C8281F9377D754BBAB7F3C616955D4C,
	ARHumanBodiesChangedEventArgs_set_added_mAC9FB746BD1745CB5BEDE7C2220302035CD63F77,
	ARHumanBodiesChangedEventArgs_get_updated_mA2FD128E22BDACD09294F107AA3F2A91CFFAA550,
	ARHumanBodiesChangedEventArgs_set_updated_mB71FDE70AD3CFC1096386A5E4260A9FFBB420C06,
	ARHumanBodiesChangedEventArgs_get_removed_m613516A989AA48A625C79A3C309D9C1465762BCA,
	ARHumanBodiesChangedEventArgs_set_removed_m97AC3D82086A3691484D3B587A534B9D6DC385B1,
	ARHumanBodiesChangedEventArgs__ctor_mA364D7E41518A8059F90E1EFC71FDD9F5F52B7F7,
	ARHumanBodiesChangedEventArgs_GetHashCode_m7C9E78E03AE212095DDA1B44594F64475937253F,
	ARHumanBodiesChangedEventArgs_Equals_m0D61EF468E803F4B69514B78E7E47852B9AC39AF,
	ARHumanBodiesChangedEventArgs_Equals_m2E24676DA642BE3E29F0347454BC4CB993DCF8B4,
	ARHumanBodiesChangedEventArgs_op_Equality_m84ECC2119180D5EEAA9BB83D63331B2F2500977F,
	ARHumanBodiesChangedEventArgs_op_Inequality_m43F5321EA5B1ED1B37378BEBEED99F9EFEB6015B,
	ARHumanBodiesChangedEventArgs_ToString_m4685EF40034A2625C670EA5813E73ED415B35431,
	ARHumanBody_get_pose_mDF3829483679DED8DB271A45534BDE7ADB9B61C9,
	ARHumanBody_get_estimatedHeightScaleFactor_mD8412089622125E0B045C41F9716854E3D6ADA49,
	ARHumanBody_get_joints_m7681747651EA8107E8E9050C0E698034CCBD543B,
	ARHumanBody_UpdateSkeleton_m7BF0D10DF09F8165B5185A1D725F57B661FEE96F,
	ARHumanBody_ToString_m0026ED473E3DB7C646549F9D0BF0BEB30D3DE6E0,
	ARHumanBody_ToString_m73369EE25D8CBE66B8C0C76F0380AF2A8850FB32,
	ARHumanBody_Dispose_mB0423B3C4B8C64755A68F7395FE89B1ECF15F02C,
	ARHumanBody_OnDestroy_mC1DCF2852CDFBBAAD9784FBB9C80DD1EEF834114,
	ARHumanBody__ctor_mC8D82750C6787761053A74AF2B8336CF52BF524A,
	ARHumanBodyManager_get_humanBodyPose2DEstimationEnabled_mE6DAFF825902AF1AAEF1FD53A240DBDC7E264351,
	ARHumanBodyManager_set_humanBodyPose2DEstimationEnabled_m96F65E0BBDF38B385CAEC55358EBA5F26EA33CA3,
	ARHumanBodyManager_get_pose2DEnabled_m796EF39EFA542FCC94B69B673C0CC7CD4C5D24E2,
	ARHumanBodyManager_get_pose2DRequested_mF884C3A8B90D0C91A9024D93C02B88D8662612C8,
	ARHumanBodyManager_set_pose2DRequested_m917E0D4D8DABC80FDD25693D6BC3B5B8D25331FC,
	ARHumanBodyManager_get_humanBodyPose3DEstimationEnabled_m477D538E2CDBFFEA0655203A1B432B1872D9FE09,
	ARHumanBodyManager_set_humanBodyPose3DEstimationEnabled_mDD2989905490687FC7204836F896B9D82C0F9725,
	ARHumanBodyManager_get_pose3DRequested_mFF8FBBCD9E4B48F3629520ED9DF07D9287700D7A,
	ARHumanBodyManager_set_pose3DRequested_mCB5DF13189EE08137FC75C2F547A3B7890509966,
	ARHumanBodyManager_get_pose3DEnabled_m8C252BB1DDAA5E394E929FADAD5498B1D879D6F9,
	ARHumanBodyManager_get_humanBodyPose3DScaleEstimationEnabled_m3623EB1138FB66E9C8A7883AF7280895A8D8AFBB,
	ARHumanBodyManager_set_humanBodyPose3DScaleEstimationEnabled_m917F83543C3569FD54FE7822F8CA1E061F9AFD60,
	ARHumanBodyManager_get_pose3DScaleEstimationRequested_m5DB10D060C50FBC5BF2B073F4240A861A5375B28,
	ARHumanBodyManager_set_pose3DScaleEstimationRequested_mBFC17BBB6B0EF302FF71B0E5EA3D1BDA60878C42,
	ARHumanBodyManager_get_pose3DScaleEstimationEnabled_m1DB11320F84D1197947D7D49FE30E0E3187DC592,
	ARHumanBodyManager_get_humanBodyPrefab_m93F253FF1CADEC53B9F14790A2DA799D731EA3A4,
	ARHumanBodyManager_set_humanBodyPrefab_mE2381B0F57B0A4FB150B843C898536BFF19133FE,
	ARHumanBodyManager_get_gameObjectName_mBB3531C12ACE38C39F0B0EAEFB5ADCFC9BB0B679,
	ARHumanBodyManager_add_humanBodiesChanged_mAD886F8E010310FD75E072CDCD0478D40659D318,
	ARHumanBodyManager_remove_humanBodiesChanged_m868BF1A9ABD143149D3E4C4F04C6204B5768D7FB,
	ARHumanBodyManager_GetPrefab_m3239C4290C47DB76E9DF2A5BA5C4E5698C3CCBBC,
	ARHumanBodyManager_GetHumanBody_m66B477EC806934DD056B8785517AA05639136BA7,
	ARHumanBodyManager_GetHumanBodyPose2DJoints_m96E12B6F15FDE28C5E74218CD7CDDA07A8875600,
	ARHumanBodyManager_OnBeforeStart_mBAE43118C586308FBB93E8F0EDE382A1FC664106,
	ARHumanBodyManager_OnDestroy_m97C60BE4C48A125058C79DA1E6295FFA55F625DB,
	ARHumanBodyManager_OnAfterSetSessionRelativeData_mF437E9691AA1BE8EA449C0C839DE2EC966E64653,
	ARHumanBodyManager_OnTrackablesChanged_m5D027087D8927AAA2DA4941F7DD81BAC1A2920E8,
	ARHumanBodyManager__ctor_m17FEEBB075373C95121F2B2DB9B1EA7332B6524F,
	ARInputManager_get_subsystem_m457880CC51D7BE9C9D4DE12F2473E7EAFB4C650C,
	ARInputManager_set_subsystem_mE16FA39F0E25600FBFEB5C8E58032E62479E62DD,
	ARInputManager_OnEnable_m3ABE47A517D7E51D5BFA6EE269B113CFE3AA6F36,
	ARInputManager_OnDisable_m32F5415FBE10F4222A9F667F6689D1CCA972E666,
	ARInputManager_OnDestroy_m283BFC5A64D52B3308325D2C028D67588CCE30D8,
	ARInputManager_GetActiveSubsystemInstance_m9982C5A517B58D82911D73A81A7EE8476BDB49D2,
	ARInputManager__ctor_mDA0C2F2134CD0FFCF1A34152EAB3BF5FF2AE4A6C,
	ARInputManager__cctor_mE577C81B472781AF1A6A155EDD48B7380AFE7F56,
	ARLightEstimationData_get_averageBrightness_mCDE95FB42D807C168E187942BD9DDAB65439AE19,
	ARLightEstimationData_set_averageBrightness_mC057F484D1EA8E3760977FE33297539B665D95CE,
	ARLightEstimationData_get_averageColorTemperature_m688BB1F18E15D7058FDEFC012451A72CD6D193DC,
	ARLightEstimationData_set_averageColorTemperature_m2B62048E88C65A904843D4A46BDAC8F764519B79,
	ARLightEstimationData_get_colorCorrection_mF7D9D83F249587E6A1E18D845C0D521C907DF496,
	ARLightEstimationData_set_colorCorrection_mD28E53EA20EE633541E1F9AFBB853A10AF3361BA,
	ARLightEstimationData_get_averageIntensityInLumens_m3D74CD050CDA61A97595AF8854E97C3239F159D1,
	ARLightEstimationData_set_averageIntensityInLumens_m79B3A12B384470DCCCCEE390DE5ECCC0E1CCCE5E,
	ARLightEstimationData_get_mainLightIntensityLumens_m618D7C8A4FA8FFC3AB370DFC00596DDFA592288F,
	ARLightEstimationData_set_mainLightIntensityLumens_m4B7F856E88E7F8247B61EFEA6231720A23340B42,
	ARLightEstimationData_get_averageMainLightBrightness_mAA37F537DB4589CB01464B6A7E9AA8AF2C6E693F,
	ARLightEstimationData_set_averageMainLightBrightness_mCE3727F3F32B8EB41503E852CFBCC20898B50D93,
	ARLightEstimationData_get_mainLightColor_m7A17F97AC5EBEE6B3E3B093AB73F8FB904C79C3E,
	ARLightEstimationData_set_mainLightColor_m80FE4C8781175687581668E3708A899C15F278FE,
	ARLightEstimationData_get_mainLightDirection_m49A1B8B5DB38EAE356A22C1AD0A4FD671C6C1427,
	ARLightEstimationData_set_mainLightDirection_m701A092104413621AC8320050422D4A0C94BE57C,
	ARLightEstimationData_get_ambientSphericalHarmonics_m34C8C4BB5EFDD7E2DAEDF983F11031ABFCA16C41,
	ARLightEstimationData_set_ambientSphericalHarmonics_m4DF86D65B2095ADB18B9504CC23518600DDA2D9B,
	ARLightEstimationData_GetHashCode_m3F60EB276A24D0B1D3D7B74F0C9CBE044CC49595,
	ARLightEstimationData_Equals_mD33829136230F81948FE58D2EB7A32E53973AD02,
	ARLightEstimationData_ToString_m57D70F82DD05F504BB4DC8937BAE5E9B0CE686FA,
	ARLightEstimationData_Equals_m91813FC80F85190AC70FCE2827D4844F15A1BB65,
	ARLightEstimationData_op_Equality_m146595607978F22D689834E0042DC3C91A1D7C00,
	ARLightEstimationData_op_Inequality_m4CC745CDD06C42BF6A2F7BCE9549906B22B1AEFB,
	ARLightEstimationData_ConvertBrightnessToLumens_m09279642CD4DFF335987CE9212C0F1E82F4CA4A2,
	ARLightEstimationData_ConvertLumensToBrightness_mDEC789CC752A2A51FBBB1C8B2C27CB6F0C615D35,
	ARMeshesChangedEventArgs_get_added_m0D451C7DDD6CE9B7697CF3678D2F1DE440170E6B,
	ARMeshesChangedEventArgs_set_added_m6A461DFF63F7E28D7A4B02AD47629D95BBAC20B6,
	ARMeshesChangedEventArgs_get_updated_m06E6CE944759C31B7500D0A4EF8D07D262A0626F,
	ARMeshesChangedEventArgs_set_updated_m60115D846577E418E88D4D273AD1A2ECE1D1BAAE,
	ARMeshesChangedEventArgs_get_removed_mB982A0629BEA14EC069263AD7BAB6C43CB187674,
	ARMeshesChangedEventArgs_set_removed_mA6932589C03B43D48CB7DC1F7AA627BD85F6E396,
	ARMeshesChangedEventArgs__ctor_m3265D79A775738B64ADFBA5B52C89DCC77B26F2B,
	ARMeshesChangedEventArgs_GetHashCode_m43CF0D8A78557CE5A2E097DF4DED87531AE07F5E,
	ARMeshesChangedEventArgs_Equals_m077E07D6856F294A374A0EFE0E38DCB958ADAAFC,
	ARMeshesChangedEventArgs_ToString_m9249BC2AF8169516627B851472F3DAFA6F067879,
	ARMeshesChangedEventArgs_Equals_mAC06257880DA02E04E099EDE83361618D0175EEA,
	ARMeshesChangedEventArgs_op_Equality_m295B2381EBA5F89DA12BA00DBB18C7D3A6BE7E8F,
	ARMeshesChangedEventArgs_op_Inequality_m27A98E0C6192BB6CE379BC02F262DFD045075218,
	ARMeshManager_get_meshPrefab_m4E46FECEC68D97E48BAF9E1813C374FEED7C1D28,
	ARMeshManager_set_meshPrefab_m3ACB204D3D6382FEB944222ABFADE221BA4128D6,
	ARMeshManager_get_density_m25870253F89E5B3D942393A4AB7F2ACAB1494156,
	ARMeshManager_set_density_mEC198B79FA6CB2BC7637848DE8C99902BAF0E611,
	ARMeshManager_get_normals_m0992EE7C313CD0FC0CE5BA4F4B2B6664809A0893,
	ARMeshManager_set_normals_mC51DF99F87CC0D1248BDEC5F7469070C56C4968A,
	ARMeshManager_get_tangents_m06F3881148DEFE8AC0836785E8770F4863BBCA63,
	ARMeshManager_set_tangents_m9B026C93E7E948C79E1ADBC0976AE461C03A38EA,
	ARMeshManager_get_textureCoordinates_m8FBD7696106413BC9753204A099BF6479D924485,
	ARMeshManager_set_textureCoordinates_mC1A9E2D2AF4BB3D5D0D59D15F626DD40731EDADA,
	ARMeshManager_get_colors_m9D877332BA40A17504F0DD279AAF66A50FE819AB,
	ARMeshManager_set_colors_m719499B8B8C8B4F707935737AE3A19D7BF025D9F,
	ARMeshManager_get_concurrentQueueSize_m30020A42624987FD3413EA2FDB4F3921634245BE,
	ARMeshManager_set_concurrentQueueSize_mB0214487805F504CD8C857ACEDD036674A6B0296,
	ARMeshManager_add_meshesChanged_m61D881D3F3A1AD033E8D86F4016181A8945C54CF,
	ARMeshManager_remove_meshesChanged_m5F287FD4EBF77D84842BAB9B16AC385A55F6B2EB,
	ARMeshManager_get_subsystem_m75B33162C77E20C5BA6A073A596141A1E0CA05E9,
	ARMeshManager_get_meshes_mE4409AFCBEFF5A5BAEE4E4F3093C4D1129D5BB0B,
	ARMeshManager_DestroyAllMeshes_m1184F2FC6AEACED0111439C5F280C2441C736BEF,
	NULL,
	ARMeshManager_GetXROrigin_mD884C170E809D1604629268DBDF03B018A87E8DB,
	ARMeshManager_SetBoundingVolume_mAAD59BF3626DB86F65B0EE49281F1E2BE39665EC,
	ARMeshManager_OnEnable_m6A3BA5F1FEC026E8F23E05A5EED8FA02AC2338BB,
	ARMeshManager_GetActiveSubsystemInstance_m2E3C5792174F92F8ED27509326C34117590CFE8B,
	ARMeshManager_OnDrawGizmosSelected_m14402AA8D71260A40872F587B23E0F06B2FAF3FB,
	ARMeshManager_Update_mF1E202ED19E5674811221EE0A387AAE0800090D4,
	ARMeshManager_Generate_m973A5CBB0E9DFA436954640C44B3FD4F11EDB25C,
	ARMeshManager_OnMeshGenerated_mD390561CF0C2B19DC733D4F8E8DF54D6D5E1FA6A,
	ARMeshManager_GetOrUpdateMeshTransform_m245D988398C52CBD4316D062BFD7FE08EF71A3CC,
	ARMeshManager_SetMeshTransform_m7433A3AEDE9769BB39E1F61E4981F8D9EFF2956C,
	ARMeshManager_UpdateMeshInfos_m599C18B6478E18F60220FC9656443FC502AB2A1E,
	ARMeshManager_OnDisable_m90ABCBF834BEB7F2C8E75B3DA4ABBAD7245B0695,
	ARMeshManager_OnDestroy_m9480FE5BD7281E4C704B843F48B1353B2F2859FB,
	ARMeshManager_GetOrCreateMeshFilter_mD7A54DC382A78367262C8A74881C8D8BFBE09AA6,
	ARMeshManager_GetTrackableId_m479D30109452143F4680A8FA4DDD25E05ED374FD,
	ARMeshManager_GetLegacyMeshId_mB432B84C3E2284CD2413939841B86DF87CA50E22,
	ARMeshManager_Awake_mEDF1A889F6F28DC25592058BE4ED23DA7045A365,
	ARMeshManager__ctor_mDBECCA216201478248BB2687C428CA89F04D38D3,
	ARMeshManager__cctor_m7422CBD18B7A260C0BD7A8AF1932D9016EA66D52,
	TrackableIdComparer_Compare_m5CF79B428F76D82D2D84715130B3BF8DFEBE6BFE,
	TrackableIdComparer__ctor_m50920B0DF39D8572FA5F22B2C22D6777C838C1F3,
	AROcclusionFrameEventArgs_get_textures_m71DA887B2AF42DDB9500E82DA7A896B0199C1F74,
	AROcclusionFrameEventArgs_set_textures_m5BB4042AD796E509530E064073A6AE707FC7DE99,
	AROcclusionFrameEventArgs_get_propertyNameIds_m44F7274F021BE5B6E973ED4F59E13328656C5CD0,
	AROcclusionFrameEventArgs_set_propertyNameIds_m597B2EF5AA77522294E0A6B0EE5058BECCF92B99,
	AROcclusionFrameEventArgs_get_enabledMaterialKeywords_mD4B62FFB1EAFA72FC1EC69A533AE6B726078264E,
	AROcclusionFrameEventArgs_set_enabledMaterialKeywords_m1F3B100DB81295D6D99FB40477E9B71A597714B1,
	AROcclusionFrameEventArgs_get_disabledMaterialKeywords_m4348A651EED14C4FE7A9DD5E941ED496FBBFDA8A,
	AROcclusionFrameEventArgs_set_disabledMaterialKeywords_m1857AC4F635D5122AA0C14D4DD48F6E62506EF46,
	AROcclusionFrameEventArgs_GetHashCode_m7115792D710F0C7C0FB985857E741C600F170EF5,
	AROcclusionFrameEventArgs_Equals_m569A3536BE31ECC807124FB95DE73677BB762C52,
	AROcclusionFrameEventArgs_Equals_mF6121B9B23AF4C030B23B2CCD73F70597C47FE02,
	AROcclusionFrameEventArgs_op_Equality_m8366637D6E50819E63713F766E31159D136CE3C8,
	AROcclusionFrameEventArgs_op_Inequality_m6437EE200543587A29D368ECEF36FFABC91BB645,
	AROcclusionManager_add_frameReceived_m3477ACEB4A153BFA23B040C82101CCBECD8738E7,
	AROcclusionManager_remove_frameReceived_m253009A31AAE711BED3A3480C36270DBA85043C9,
	AROcclusionManager_get_humanSegmentationStencilMode_m6E99ED17943FF4CE8B78B6F08148846F0A36D7CA,
	AROcclusionManager_set_humanSegmentationStencilMode_mD2D2761E279E8189A31F235BC1D5B2F632AE33F8,
	AROcclusionManager_get_requestedHumanStencilMode_m683B227E011266D1D7DB77E236F2ABF555F93A0E,
	AROcclusionManager_set_requestedHumanStencilMode_m73D9A0E9621AE3C80F659059F3DB6A23EE0B64F1,
	AROcclusionManager_get_currentHumanStencilMode_m57E30562049653C86AF90C19B4B53596535ED805,
	AROcclusionManager_get_humanSegmentationDepthMode_m840E71ECDFEF90B7546D107172F20CE2DE0BB878,
	AROcclusionManager_set_humanSegmentationDepthMode_m12706BC43248A17F89EE6D43045746E5DF67D54A,
	AROcclusionManager_get_requestedHumanDepthMode_mADCCAF2B841C3D36FE1E9222924C9D283AFCEB28,
	AROcclusionManager_set_requestedHumanDepthMode_mB83BA91C222F7C576C09C79D63FB7C8DDD6D1642,
	AROcclusionManager_get_currentHumanDepthMode_mDB6A4FB40D47960B249F215311D1F9121652A525,
	AROcclusionManager_get_requestedEnvironmentDepthMode_mE0BBD4AB6B70A9F75EC19206AB9B3F690CE05C8B,
	AROcclusionManager_set_requestedEnvironmentDepthMode_mDF1DA23F3A3B7C70F3802B0A7FC58A47D272D45F,
	AROcclusionManager_get_currentEnvironmentDepthMode_m56A466D29413F131D2B41A2E30B19E51A4D66207,
	AROcclusionManager_get_environmentDepthTemporalSmoothingRequested_m0E0FBB92E6C94EC19A6FF3D9A80524A38B7CDAE7,
	AROcclusionManager_set_environmentDepthTemporalSmoothingRequested_m3E5C48FEB03F83D916D060B8F6940006EC2FB621,
	AROcclusionManager_get_environmentDepthTemporalSmoothingEnabled_mD066573D62D0F9328737D9767E3680D577DEA525,
	AROcclusionManager_get_requestedOcclusionPreferenceMode_m5E9247118E3647775717609C06D2EBF0DCDFA4F3,
	AROcclusionManager_set_requestedOcclusionPreferenceMode_m0D70CC75D6DC39BEF3F79E7CFCAE47A5BBC7CDAF,
	AROcclusionManager_get_currentOcclusionPreferenceMode_m56F4592CEBBCC1CE1831C71D277765A4F4D95676,
	AROcclusionManager_get_humanStencilTexture_m4F81CA6ABBFE6326872B0545E826766F58ADC86F,
	AROcclusionManager_TryAcquireHumanStencilCpuImage_mD58759667706F23FE88E457AB47603456E99B68D,
	AROcclusionManager_get_humanDepthTexture_mF9B84E503E3B394AAE1544B07BD3BEB1FE3EFEDB,
	AROcclusionManager_TryAcquireEnvironmentDepthConfidenceCpuImage_m03C58FBD4DC0D364CF72219723E76073DFBE30CC,
	AROcclusionManager_get_environmentDepthConfidenceTexture_m58B59DBF5E0ADED43B1810FF81F7CDE53F6AB768,
	AROcclusionManager_TryAcquireHumanDepthCpuImage_m477918E28097A23044EFE097741B1D2B2FA7E99D,
	AROcclusionManager_get_environmentDepthTexture_m8D961A8DB1023D5B4340A2020C51D75A21C5D454,
	AROcclusionManager_TryAcquireEnvironmentDepthCpuImage_mB57569CD3F57DC88454AA01E2A735C183F0F0958,
	AROcclusionManager_TryAcquireRawEnvironmentDepthCpuImage_m988381DD34936EE0FAA7F1177358C97AAE6AB6E4,
	AROcclusionManager_TryAcquireSmoothedEnvironmentDepthCpuImage_mB1BFFCB6A0FEF5AE4298224D448F9C6F7A551EDF,
	AROcclusionManager_OnBeforeStart_m913D2B7D1CEE94816C124C7A16E66597A7997CB1,
	AROcclusionManager_OnDisable_mD945E4ADB6073F286D5BDECD78CD13B064378B2E,
	AROcclusionManager_Update_m97CCC8D1EFFA544B65D1E0F02C2DB099F267934F,
	AROcclusionManager_ResetTextureInfos_m863803DE85E0715F6CE0474BFF58BAE27A3E4C53,
	AROcclusionManager_UpdateTexturesInfos_m86684113F8D996C1B75A3FF1B81DB0BAD08A6E1F,
	AROcclusionManager_InvokeFrameReceived_m95D3C9962774B0221B8448A55D14404BDE87A7C3,
	AROcclusionManager__ctor_mEA736ACC071C2B0266F1F004BAED38D250958324,
	ARParticipant_get_nativePtr_mCA91843BF20512073D04C0F85133A21EBD5BEE87,
	ARParticipant_get_sessionId_m6BA7B78F940CD77E0E4E7F979C46DF26D8E7CDE8,
	ARParticipant__ctor_m543621608CB954414B30C2FA289E0F92F6D89CA2,
	ARParticipantManager_get_participantPrefab_m33C930AA127180879E5E0E7FF2A0E9DC7D7CC1C6,
	ARParticipantManager_set_participantPrefab_mC2A5D19A592EC6942A59C751C03503AF1363DF45,
	ARParticipantManager_add_participantsChanged_m7CB947B046B890EFA75726000DBECA7E86FE8775,
	ARParticipantManager_remove_participantsChanged_m8D99AE38D87E378337E82DC2359079A2448D447F,
	ARParticipantManager_GetParticipant_m31EDC2D3AA0826E3863536C3BDBBB56234E5AA33,
	ARParticipantManager_GetPrefab_mD5D1DBF7DD43563F97DCCCFAF2ACB01970FF9FB2,
	ARParticipantManager_OnTrackablesChanged_mB07256F2590691B4302FAC7D625FEE1103831600,
	ARParticipantManager_get_gameObjectName_mB1EBFFD4D5FD2A96DFA1F96CCF3241B36FF1A883,
	ARParticipantManager__ctor_mCD87C3FD6845E88B8E23CD2813C75E868D22F6A2,
	ARParticipantsChangedEventArgs_get_added_m06DA0DBF00575BB5D41A8AFE385ADAB9866BA678,
	ARParticipantsChangedEventArgs_set_added_m166787C372D8632EF3F075B63E1E58DF3665DE4E,
	ARParticipantsChangedEventArgs_get_updated_mA0EC697655BDFEEB28064579FDD67DD166A2AE65,
	ARParticipantsChangedEventArgs_set_updated_m082B2C0864682385CDDD7C89DCF8C135A1E2CBAD,
	ARParticipantsChangedEventArgs_get_removed_m6BBF2ABF2E71DB02A70122927AAD83E3AB35A77F,
	ARParticipantsChangedEventArgs_set_removed_mD046A770079A3D9635A832DC2A7AA4997FBCCA60,
	ARParticipantsChangedEventArgs__ctor_m3D9C5565D3F7AB1077A197397CCAF28E0DF360D4,
	ARParticipantsChangedEventArgs_GetHashCode_m155622653A93F1E5021158B3B506D5D04460D0C5,
	ARParticipantsChangedEventArgs_Equals_mF5F4985298C2A01B6DA6119757ED5964BA7A1C44,
	ARParticipantsChangedEventArgs_GetCount_mAAA0EAA40FE5210B3072EC5C8CAA435267B89FA8,
	ARParticipantsChangedEventArgs_ToString_m6AE6DC3EE171F559B60C06C734EEF2CD39546821,
	ARParticipantsChangedEventArgs_Equals_mD086A71DE5FE4C60061792F37BC11F17D14871C3,
	ARParticipantsChangedEventArgs_op_Equality_mF465D7F7C9ECEBED9B9969296A80D70BF8A12F5E,
	ARParticipantsChangedEventArgs_op_Inequality_m53BC1E056B9C431ABCEEE0BF582AE0F50A37BBA3,
	ARPlane_get_vertexChangedThreshold_m2D434A45D29E764DA9E36E48C8837F3598A971BF,
	ARPlane_set_vertexChangedThreshold_m5DAEB406D9A02A3DB5D5D91C0FE217785051A53C,
	ARPlane_add_boundaryChanged_m6A9CA2ADF4742C004EF7BCFD48A7C820320616AA,
	ARPlane_remove_boundaryChanged_m6F71EEF2CD3C71AF069C5FE6CD993906BD8A9A10,
	ARPlane_get_normal_m12A4F62B15333D04011C0DBD6377FA1E9B3C60B7,
	ARPlane_get_subsumedBy_m18A95DC877D1F172CC50522A716D45DA21790027,
	ARPlane_set_subsumedBy_mA9EDF95F501F0B757836FA2A6C4FF2723749279C,
	ARPlane_get_alignment_mBEF1BCDE0A964ADAE4E5F477BD4C961CA8849EC1,
	ARPlane_get_classification_mDE82B3032BBD7BD6ACAFEBEBAE439B4044BDA085,
	ARPlane_get_centerInPlaneSpace_mB920536734D86017855A46CCBCB51C4FC8C4876A,
	ARPlane_get_center_mB33D672F5673C29616BC46AC56AF1276D5A627CC,
	ARPlane_get_extents_m62F945D694458164CB2DFA6D53C8ED085B3900C9,
	ARPlane_get_size_m4F874C7EC0884482FB962790594D2ACF5A8874A6,
	ARPlane_get_infinitePlane_m5BE7FB076247945DF209B8CBEAD2CC62433D6AC5,
	ARPlane_get_nativePtr_m0227A663EF6E1CC15D6A5BE2BF3D5EC21BCFFA25,
	ARPlane_get_boundary_m7C8D2C076E5FE6F1A8568B9C7575402073D5A4DC,
	ARPlane_UpdateBoundary_m0F54D0A3D284E8051ADB089B1D720BA8543EC616,
	ARPlane_OnValidate_mECE1137B9D2F0680DBBFAE4E1F6FCE7D805BF847,
	ARPlane_OnDestroy_m1B928C0ECC64C9C95467D0F9C99317A3EEE8F701,
	ARPlane_DisposeNativeContainers_mCACDEA77F5356585B085FACB161B3FE9B6648D32,
	ARPlane_CheckForBoundaryChanges_m84BBF242B8FB7A7B5C8493389E0B887C11BBBB2E,
	ARPlane_CopyBoundaryAndSetChangedFlag_m4FB01C9388B0A20761F61436BC3A88D1AEA2506A,
	ARPlane_Update_mB6D7BDC2CD9F2B2DB73B6F8A32981C1F855856DA,
	ARPlane__ctor_m75D393C13FDB895D2B66FDA3F6DAF9A49F770DC1,
	ARPlaneBoundaryChangedEventArgs_get_plane_m4A8050E854AD2A386891D06B8695A83B59C73FDE,
	ARPlaneBoundaryChangedEventArgs_set_plane_m297AB56CF4E77AF4AE5C4983BD69F576F5487AD7,
	ARPlaneBoundaryChangedEventArgs__ctor_mD7B4EC2D5BB290541E54078930F949A5F9E34F1B,
	ARPlaneBoundaryChangedEventArgs_GetHashCode_m1D4324C6E2918575F7DA49CD1FC9EBB96C3C39BC,
	ARPlaneBoundaryChangedEventArgs_Equals_m0A539DC1276324BD525A49B913A9532301192DD1,
	ARPlaneBoundaryChangedEventArgs_ToString_m9098FE81C630F76EE51EF044C24673846C930040,
	ARPlaneBoundaryChangedEventArgs_Equals_m7AA9243101F5680770335ADAD9C11BE43235B861,
	ARPlaneBoundaryChangedEventArgs_op_Equality_m2280262D8DE3A47C72B3C707EEFF38375D5F40BA,
	ARPlaneBoundaryChangedEventArgs_op_Inequality_mD182C2A05E4BBCD00BBF21B0DCBCF15DEC869292,
	ARPlaneManager_get_planePrefab_mD1AE647924733F69F5446087BF5BFD4EEB4EF2C2,
	ARPlaneManager_set_planePrefab_mC934904F4FA889CF92277D56CA268B3EB1149FB2,
	ARPlaneManager_get_detectionMode_m58EC905BD8D12740899BC9A6D4A7968FE482AB09,
	ARPlaneManager_set_detectionMode_m525E1EFE03451CEEDE3E434179E098D76E00CE4E,
	ARPlaneManager_get_requestedDetectionMode_m48D3EFD9C77C930F7A059E8C0E37410DE0C5A142,
	ARPlaneManager_set_requestedDetectionMode_mEBF1F2ADD9AED8B433AF590235FB23C734DF1D5E,
	ARPlaneManager_get_currentDetectionMode_m5B222DE66EDDF55C552262CD7C741DD6D3C8B839,
	ARPlaneManager_add_planesChanged_m295E8F0B09AF78A2D9249F3C07BFD4D4F749F36D,
	ARPlaneManager_remove_planesChanged_m86DE7059D87299391D2EE97A884E53CCE80B3A5E,
	ARPlaneManager_GetPlane_m49FC33F6BC0DCFE67C4FC5F5CEE99CC8A7036FAE,
	ARPlaneManager_Raycast_m567B9C820E9E0456FD5FE878F3533CB605DDB615,
	ARPlaneManager_GetCrossDirection_m0898AE13C39AC18D3C52882D401FF17C64430136,
	ARPlaneManager_WindingNumber_m7291CC7CEF88D30F49D9C99AF9B6F6BF4EBDC871,
	ARPlaneManager_GetPrefab_m52C742FF457449D290C3B7D7035741B686CD5851,
	ARPlaneManager_OnBeforeStart_m21596786467B87EA7620F6B997F8A44556BC23FD,
	ARPlaneManager_OnAfterSetSessionRelativeData_m8F6BCA07C252256594155FAACE3CFC025D266B19,
	ARPlaneManager_OnTrackablesChanged_m5B10E7EE8C5F9B3DA686A9A182EB28BB6DC8D825,
	ARPlaneManager_get_gameObjectName_mA3E24259EB0C4B773E1887B5D0645A683C0BC082,
	ARPlaneManager_OnEnable_m5A60291499C3B5AF2789552E4CD2E670BD4B8EE0,
	ARPlaneManager_OnDisable_mD340A55FC5F8D2B4EBBE201F0EFA79E042BA371B,
	ARPlaneManager__ctor_mAFCAB69B1267C0EEB09BF4687FDC045D0C6C4E31,
	ARPlaneMeshGenerators_GenerateMesh_mB7990122259CAEF8FD537C5A32B360313F857D1B,
	ARPlaneMeshGenerators_GenerateUvs_m6C406C633DAEC01385CBA4CDB20250B6BB596CC2,
	ARPlaneMeshGenerators_GenerateIndices_m8048F590E06AA353732AF95DB066F00984383E86,
	ARPlaneMeshGenerators__cctor_m9F065134C253EC77C8C039D8BF2A1D9BB2B50288,
	ARPlaneMeshVisualizer_get_mesh_m6C1A730CECE8AE0A0549ED615AB3B3D66E4A0FD8,
	ARPlaneMeshVisualizer_set_mesh_m8CD0379CEA0D69FE611772065773F289F7DC72AC,
	ARPlaneMeshVisualizer_get_trackingStateVisibilityThreshold_m4B12258F93FA5CC0D872C2D40F4099C9955E17B4,
	ARPlaneMeshVisualizer_set_trackingStateVisibilityThreshold_mF605945507B06EF28C32B4959F200DB1D1EBC092,
	ARPlaneMeshVisualizer_get_hideSubsumed_mCCD37D9204A25314CCB1265428F31CE60756531B,
	ARPlaneMeshVisualizer_set_hideSubsumed_m23832E8A5F46EF97E1C842F42EAD70E736A7E9D7,
	ARPlaneMeshVisualizer_OnBoundaryChanged_m18E75B8204676B24B7CA97F972B52D113D2DD393,
	ARPlaneMeshVisualizer_DisableComponents_mDC0DB459E5F92C72961B248B15288812AD3AAD60,
	NULL,
	ARPlaneMeshVisualizer_SetVisible_mDEB7B36C8DBCC2D49CF8384CDB6776B10AB693C6,
	ARPlaneMeshVisualizer_UpdateVisibility_m3BDC44D40DCB36A855EC1DA3FAE5CCC7B08EAF37,
	ARPlaneMeshVisualizer_Awake_mBC4D682166B1A469F75E1DAB7B73FC81186C9380,
	ARPlaneMeshVisualizer_OnEnable_m022D7A992E4827FF0D1C7C7ADCB5A10BE757E9ED,
	ARPlaneMeshVisualizer_OnDisable_mE85F1015B64CB38CE1AB27FAF63272B4059D0536,
	ARPlaneMeshVisualizer_Update_mB7049601E8BE4168E9873CD75316100B3AF05798,
	ARPlaneMeshVisualizer__ctor_m634D93D340A27CD62EEDBD1DC21FC97E942501FB,
	ARPlanesChangedEventArgs_get_added_m6E00DD5F0B3261BCBAA8A029924A1F3F4179C747,
	ARPlanesChangedEventArgs_set_added_m86405667F94B610F057F9685D32DFE4D1D58AFB8,
	ARPlanesChangedEventArgs_get_updated_mE979DAD93445C56FF7BEB732528D6CADAF0B6C22,
	ARPlanesChangedEventArgs_set_updated_m99AB6C62358CF84214D0ABC7C55246FF04FEF4FD,
	ARPlanesChangedEventArgs_get_removed_m8CF2AA0278488746F8C55CC6C7DC1870531D63C8,
	ARPlanesChangedEventArgs_set_removed_mADCA5E7123CC12A874A9B95D189BA621163B7B74,
	ARPlanesChangedEventArgs__ctor_m20AE62576EED835E5930101056E72092B75CA2F3,
	ARPlanesChangedEventArgs_GetHashCode_m85C48FB255A115C4E1C8BE91457D6D070C14010E,
	ARPlanesChangedEventArgs_Equals_m7BFD63AF9035113B2BEBC0ACC86E6A55FA2E9397,
	ARPlanesChangedEventArgs_ToString_m4A51F333B5B52A6857AD19DADD92B5F437FCDFE1,
	ARPlanesChangedEventArgs_Equals_m8AA6CA2EBB7D2C3DC8C279D77A5894AA0CCB53EB,
	ARPlanesChangedEventArgs_op_Equality_mBB2E0E0C5555011BD1A653E42C0ABB91020F4E91,
	ARPlanesChangedEventArgs_op_Inequality_mA616877FE51980F3ACC0B27963D940BFB66B016C,
	ARPointCloud_add_updated_mF2E2E46A88A53C750389AE65A20758F39EB760DA,
	ARPointCloud_remove_updated_m9EB9732BBCD9E806E70F40757BE9C7AD6E591C07,
	ARPointCloud_get_positions_mD1B6C96855B80F0CCF6B32F97CBA75110E6EBEB5,
	ARPointCloud_get_identifiers_mBC363588E42905575E0855C6A70375098E30D799,
	ARPointCloud_get_confidenceValues_mF46E6073F5430E6EFC497B4CF2F226F3C9FAD4B5,
	ARPointCloud_Update_m03BEBE6A9E17ED2DB0DD4DE93C5A7430178515E4,
	ARPointCloud_OnDestroy_m9B851E60E78847B6D649DE62897B360BF210FBA9,
	ARPointCloud_DisposeNativeContainers_m917FC6487433B7814A851678BD06E7C21F922958,
	ARPointCloud_UpdateData_m30068C4F76A33C6B5CFDF829ABD01D45549E9231,
	ARPointCloud__ctor_mB065DD0D95C26B28A045ED6B5A7A48BE73F78316,
	ARPointCloudChangedEventArgs_get_added_m74DFD8ED4B3EADB5AC29DD0E748C84C6FA26DDE9,
	ARPointCloudChangedEventArgs_set_added_m3FE2F4143F780814D136E17E361046408D50C63D,
	ARPointCloudChangedEventArgs_get_updated_mD69E67FE8B06AB44B15F42F1D6EFA91F7E58F7C6,
	ARPointCloudChangedEventArgs_set_updated_mC936361996F351B6BF791EB3658C8F62C2210CBF,
	ARPointCloudChangedEventArgs_get_removed_m64321D4F0D902494EE19EFF1B258981DE5C712B0,
	ARPointCloudChangedEventArgs_set_removed_m99F84576C91F6538E21C4B33C81F9947A3A095B9,
	ARPointCloudChangedEventArgs__ctor_m4D0FC76C4C018FCBE287313DC150D23E46295648,
	ARPointCloudChangedEventArgs_GetHashCode_mF40A348475B5813A13FFE2C8FF2BA433F7CC9EDA,
	ARPointCloudChangedEventArgs_Equals_m05CA0C188628022AD80F0C83A4BF9D88EC93DF85,
	ARPointCloudChangedEventArgs_ToString_m9AB26A548169C9B39BA06A1DBAFDD29C280880DB,
	ARPointCloudChangedEventArgs_Equals_m663B71243F5F1D3A01EF2CCA59420E82ACD796C7,
	ARPointCloudChangedEventArgs_op_Equality_mB548C7D04AE85309E877342443317131F064CBB4,
	ARPointCloudChangedEventArgs_op_Inequality_mDD961D67FC89A1ADAC3F4A1A988127211FDB5146,
	ARPointCloudManager_get_pointCloudPrefab_m4B3EE259F857DFD335088EE2A42D7339FF13D4DA,
	ARPointCloudManager_set_pointCloudPrefab_m7DE2BABC80A83D6744D67AB17B09167817A6AB7C,
	ARPointCloudManager_add_pointCloudsChanged_m108F43CDA88DC192BA5DE3EBD7DFFDE3C59E26B3,
	ARPointCloudManager_remove_pointCloudsChanged_m28A4E43DFFB1A517F932B1A613664782D068C8C0,
	ARPointCloudManager_OnEnable_mB337EABD7FE2F636826A1E8268C10CD69DE14B61,
	ARPointCloudManager_OnDisable_mE638749742E3295F193EB46E41DA848AD67B0C80,
	ARPointCloudManager_GetPrefab_m8192FDD40FBDF2D09B15117CBEAA698AC56B2C92,
	ARPointCloudManager_get_gameObjectName_m30AD2147A29A3AD991932F7C9AD658DCEE39D068,
	ARPointCloudManager_OnAfterSetSessionRelativeData_mE604F78EF54380998C7FF4A3477315FABC103356,
	ARPointCloudManager_OnTrackablesChanged_mF512581E2055A773EC41BD3E115F1AF654705AE0,
	ARPointCloudManager_Raycast_m75E11BD6179ECDF207D43F4ACD01103E00607761,
	NULL,
	ARPointCloudManager__ctor_m28345518F8E9DAD9A9C6A5F6A51933A58125B8DF,
	PointCloudRaycastJob_Execute_mCC84BA675585A86C765936270AF8EFD99C3D5C13,
	PointCloudRaycastCollectResultsJob_Execute_m1AA868B2EEF8C3AAE8838F00B42860FC1F740A78,
	ARPointCloudMeshVisualizer_get_mesh_m49F56BF31D05DA5E221F73B5A0F0EAAFECE85B85,
	ARPointCloudMeshVisualizer_set_mesh_m29FA764125D5D67B7DF1A8278958F2AA0D139A09,
	ARPointCloudMeshVisualizer_OnPointCloudChanged_mEDB040DA37A02524FE4CAC45004C36EABD9AF790,
	ARPointCloudMeshVisualizer_Awake_m887B0ECC6880ED00AF6093E04DCFF7046E6B1E92,
	ARPointCloudMeshVisualizer_OnEnable_m1667D34F8C3014FF3ACF0815AF12482D1BFA69F1,
	ARPointCloudMeshVisualizer_OnDisable_m42CA252D8A4E7A81E8454302944C4A202A9C996C,
	ARPointCloudMeshVisualizer_Update_mFB9885E77AA22AB49BB15BD72E8A0A86044A4103,
	ARPointCloudMeshVisualizer_UpdateVisibility_m4AF7339C94A64DA159E400960751AD90364F7979,
	ARPointCloudMeshVisualizer_SetVisible_mD3B63AF0179A18FC52F01BADF128EDCC8CBE5FAF,
	ARPointCloudMeshVisualizer__ctor_mDDF94641701508D63F93896AAA59BA0CEA964D35,
	ARPointCloudMeshVisualizer__cctor_mA0C71E819658469A7997880BAEAA367AA65D42E0,
	ARPointCloudParticleVisualizer_OnPointCloudChanged_mE982B0C5A100942FCB6CEA7C93AEDE12A9A8782A,
	ARPointCloudParticleVisualizer_Awake_m99B022575BEFB8A857E4873E5012C420189717EC,
	ARPointCloudParticleVisualizer_OnEnable_m8223E2AF28CCFA50300FEB01AFC95962EB42E270,
	ARPointCloudParticleVisualizer_OnDisable_mBF710CB602BE4ADAC0D0FC66C5924B0F5D10DDF3,
	ARPointCloudParticleVisualizer_Update_m051339455E1DF98BCC4F65DBCD0F1FBA9A714DFF,
	ARPointCloudParticleVisualizer_UpdateVisibility_m279926EC98D35245CFF722594DBEB1E187832D2E,
	ARPointCloudParticleVisualizer_SetVisible_mE7481C4B47E42B6F451F93E907944A74E66767F0,
	ARPointCloudParticleVisualizer__ctor_mADBF1702FDFB7F9C45A780334B58491C9F6801F0,
	ARPointCloudParticleVisualizer__cctor_mE479457EB84C3C569ABBF019B3BF69EEC318274D,
	ARPointCloudUpdatedEventArgs_GetHashCode_m53F08CA39C23B713F1918D21E6AFD78731EACC6F,
	ARPointCloudUpdatedEventArgs_Equals_m79BA8AC909F4E622B9396930F80B1A900DEDD217,
	ARPointCloudUpdatedEventArgs_Equals_mD3C036D4490380AE4099AAF2F4D9BA32519159F4,
	ARPointCloudUpdatedEventArgs_op_Equality_m68F17CFE1A16FAB1F9ACEBBCED0346AF6B7285C2,
	ARPointCloudUpdatedEventArgs_op_Inequality_mADACED9475D0DACDBBE54486D047FA9E6C70B4E2,
	ARPoseDriver_OnEnable_mE2094A035372476C10C8E739950B18F22A519FC4,
	ARPoseDriver_OnDisable_mCCBD832DB28EB6D60E37A5EBAE110CE52E495BA5,
	ARPoseDriver_Update_mEACE85D68C1B35540A124FC7644557120409074E,
	ARPoseDriver_OnBeforeRender_m75FE5362273B5459BF4CB331AF3EF84E302B27EC,
	ARPoseDriver_PerformUpdate_m528ED9B930ECC6ADBB65A5FD71F11B454AB77719,
	ARPoseDriver_OnInputDeviceConnected_m43A1F50F3D4A44EDD91363814A5667DA6FABCC0A,
	ARPoseDriver_CheckConnectedDevice_m801AABF1ED6B1E1B49B71E7BEFD96DDA30FBBA45,
	ARPoseDriver_GetPoseData_m9F3D183BA2004C84B984BBAD6E2DA3A6B062E409,
	ARPoseDriver__ctor_mAFA3405F3863ECD38E016F7B805F8F89FC0A4144,
	ARRaycast_get_nativePtr_mB2DE425D459403A80B76D0845713C8992FFD16F3,
	ARRaycast_get_distance_mA8272EC82C2829D0D8D0EBFCA2BAB74584D45B62,
	ARRaycast_get_plane_mF6C8E3D2C90D67D00ECB4541DDC17980EC0584CF,
	ARRaycast_set_plane_m5BF5973F88BF91EC407275321D1F42CEE1CD3C3F,
	ARRaycast_add_updated_m73570A6579B28D1FD16C5BE50B28F73562565E73,
	ARRaycast_remove_updated_mB733AA38B898F08AE50F1962A83360271E8BAD44,
	ARRaycast_OnAfterSetSessionRelativeData_mB076F8EC7A02CD9F7B8EB26BB24E54CB89C09D44,
	ARRaycast_Update_m3A086526146B5AF819020677D567D9668C2777A3,
	ARRaycast__ctor_m6159FDBC04CE424C0474FA868523D302D861BF0A,
	ARRaycastHit__ctor_m0C23F16B12F6D0F71C0B00D48D8BC3271BF7F39D,
	ARRaycastHit__ctor_m954400C9EFC7F5B5A227276ED8EE2FEF32E6BC48,
	ARRaycastHit_get_distance_mB761B6EA13AA35393AB92EDD3A82D61659DE3126,
	ARRaycastHit_get_hitType_m4ACAC8C59DED2EEF01C165D15136A15EBBA996F0,
	ARRaycastHit_get_pose_m84C13E71E21FE12CBA9AAD98DC28B1E414C9EFFD,
	ARRaycastHit_get_trackableId_m4E510F2C326AFF23086203E4241C8F9B293616C3,
	ARRaycastHit_get_sessionRelativePose_mD06C35AA1BE0F142669BACB95F30A059A65D3DF7,
	ARRaycastHit_get_sessionRelativeDistance_mFD19959FCA30322A0BF427FB0D9C3BD9D4464047,
	ARRaycastHit_get_trackable_mF8D64EB03AFF2E1D5FC9B88255D2A84130B43D09,
	ARRaycastHit_GetHashCode_mE1E5375DD029685234B765B1D0C653B048BB8D5E,
	ARRaycastHit_Equals_m35C3DD720E50D890CA94E70DB77A01DAE0A0A1FB,
	ARRaycastHit_Equals_m91ECCB5154E5B4B18018C00A7E71129D682DD3B1,
	ARRaycastHit_op_Equality_m2C051D9D19EF97A840B88FD6C0D560AE71F506D2,
	ARRaycastHit_op_Inequality_m0E08E39A204BADC5AEEB2BDEA5F980B7AD74E03B,
	ARRaycastHit_CompareTo_m839A11B878030471B287EDA2250807A5A4F3F4D8,
	ARRaycastManager_get_gameObjectName_m317034F0A7D9F78204DDDFD2D974AB67E428A142,
	ARRaycastManager_get_raycastPrefab_mB08FF79C4DAA2074B10A5A8C25F3AED1ADF458E8,
	ARRaycastManager_set_raycastPrefab_mD1B935A438CB84F1DA1608B7BA577FCAD9AC30C8,
	ARRaycastManager_Raycast_mF56A1E2D7CBB61131E4D844ADA9D0CC1F6B53EAC,
	ARRaycastManager_Raycast_m6D403ADB6840FC93F5C3E4C765BE1517180ED2EB,
	ARRaycastManager_AddRaycast_mEC7CA45433E91FE30A39EF1BC593501DCC29C862,
	ARRaycastManager_RemoveRaycast_m6287F0E52B0F3358EE21AC7A354F543841AC7802,
	ARRaycastManager_GetPrefab_m0C8F561D8BC973FD5B1A671810353380A01459C3,
	ARRaycastManager_RegisterRaycaster_mED8A37992AA3FA14D98B49783B670E54B363935D,
	ARRaycastManager_UnregisterRaycaster_mBF732586B4DE3197DD8BE0B337194396AFF54894,
	ARRaycastManager_OnAfterStart_mD7FAA6AA9C4917661E9B3AEB32526B29744C59E8,
	ARRaycastManager_ScreenPointToSessionSpaceRay_m16A93EFB3AD1C851615B0C29D30EDA31E1B9E285,
	ARRaycastManager_RaycastViewportAsRay_mCCCF650D528D5CB9463CE07A77C4153B85171200,
	ARRaycastManager_RaycastViewport_mD4B1E431F070DB723B706B4AF93C4870E14AE5A3,
	ARRaycastManager_RaycastRay_m0DD4601EA39C8F69CF01DBE246CF5A988585C984,
	ARRaycastManager_RaycastHitComparer_m8199F00C8E80441FF36E6CCDFC6BC429162D1FE9,
	ARRaycastManager_RaycastFallback_m2AEB1BB26F1E99A489F35019BCA8A472A0974D70,
	ARRaycastManager_TransformAndDisposeNativeHitResults_mD8A9311ADA233E3A3DBBB6E8102CEE2AC082E915,
	ARRaycastManager_OnAfterSetSessionRelativeData_m5F5050F6CAFF19E1CDB0C145694A97F5DD7A4266,
	ARRaycastManager__ctor_m9091C8E7281C43EDF580F8C0A0B085C4F42778C4,
	ARRaycastManager__cctor_m8A6F66979C15A4CF1EA73660F4DDB1DC883BABBE,
	ARRaycastUpdatedEventArgs_get_raycast_m3B8C525D8EA47F31AA2492A9797652A9C2BAE8E7,
	ARRaycastUpdatedEventArgs_set_raycast_m54ED1343FBDD93ED1A45B0A08CCEA53489FEB1C9,
	ARRaycastUpdatedEventArgs_Equals_mDFA0333A1E3C1F0F909561C790846DE76DD78943,
	ARRaycastUpdatedEventArgs_Equals_mBC0B147BFAB731AEF33493D1E68B67176F869981,
	ARRaycastUpdatedEventArgs_GetHashCode_m676A512737D735B0B6B1AAB43095845823F3B49E,
	ARRaycastUpdatedEventArgs_op_Equality_m336643ED3F8C8671C84875ABF6A542FBE2AC5E82,
	ARRaycastUpdatedEventArgs_op_Inequality_mFEA98064550BC3CFE097BDECCF7B45C5F7C5F841,
	ARSession_get_attemptUpdate_m0EAA239D17F9E81EA7B91E7F9264F25D3A21053A,
	ARSession_set_attemptUpdate_m96B3F3270D4234FFCF1FF75BE8483DA14954A50D,
	ARSession_get_matchFrameRate_mE9DD185413E4221C338E62F62A1D9FF11A1C0C4A,
	ARSession_set_matchFrameRate_mB4A8508F8B7BC6277E29897398EA978E79AC52F3,
	ARSession_get_matchFrameRateEnabled_mA00102E4B432C2EFC208581324F1E4C321A53A23,
	ARSession_get_matchFrameRateRequested_mE71E1A95553A3DD0CBCC3D231EF8B8839B3020E1,
	ARSession_set_matchFrameRateRequested_m2D869903BF0203BC1996BC381C29876D36A1DB7C,
	ARSession_get_requestedTrackingMode_m6E568E072F4C49CCF9CF5E26F20C6818275E7D52,
	ARSession_set_requestedTrackingMode_mD234A3A6321102EB25D95EFD9C6CCD96D282418A,
	ARSession_get_currentTrackingMode_m0705EF50CB1914A74B455D1D8B80E82B5E49DF54,
	ARSession_get_frameRate_mF8050E4AF339A55F79BFDCB519C290396AA1A138,
	ARSession_add_stateChanged_mD40004D1EBE89E6CAC651632ABB8EADBE46692FC,
	ARSession_remove_stateChanged_mDAC5A2DCA86363C495C66FB8CA92F46ABDFE3BE1,
	ARSession_get_state_m6A22EE6441E58AC66E1FE9A7359D06C90C1A2842,
	ARSession_set_state_m8C16FB66D3910F9079CD01CE9C98D2D6D46D7CD5,
	ARSession_get_notTrackingReason_m18987B9B3B06BF4136C13E8F83A338BB332F2BF8,
	ARSession_Reset_mE905496B8400CB5BF1EB50A01C675771FCCF9A91,
	ARSession_SetMatchFrameRateRequested_m6430151F0DD7474FD48BA18B33AA889BD92A630A,
	ARSession_WarnIfMultipleARSessions_m42E940191AC74642FE7E16117AA44A606BB8094A,
	ARSession_GetSubsystem_m31E4A8324A31B4844B6AA43797FE2C53A2BA3BEB,
	ARSession_CheckAvailability_m7090FD96E8580EB3E6CAB97B8344BF845B254D0A,
	ARSession_Install_m9C4DCECEA000E16F4992376B7E40DA0C95B58166,
	ARSession_OnEnable_m15300A28354F0EBC321A2005F09A3B8C69739E19,
	ARSession_Initialize_m9A38049E4C026CEA25415EC025407D2378F02AA1,
	ARSession_StartSubsystem_mABE004B0E0CCB1E71F7A5B58D2DC8A71C2094E2D,
	ARSession_Awake_m39D818E017C1D4DA1CE60591740CB59AA3326FF5,
	ARSession_Update_m655A9FF7829D10D92F3C495FC996BD456E7D91BE,
	ARSession_OnApplicationPause_m35CDAAD00BFDE112C443803A5A7436783D62E225,
	ARSession_OnDisable_m9E4AF208C0FE89B50C41F7A7713540A41EE5080E,
	ARSession_OnDestroy_mBB3CEB034D35227A6E76F2D20D80639898F2D9FE,
	ARSession_UpdateNotTrackingReason_m8F96B5947DFFA5D308DBA3E9D8CBFDB3ADABD1B2,
	ARSession__ctor_mF86E51E302EF8623E0E7465D295579D42ABA21FB,
	U3CCheckAvailabilityU3Ed__36__ctor_m735863EED119A94ECAEA97AA6163A7888BAAE407,
	U3CCheckAvailabilityU3Ed__36_System_IDisposable_Dispose_mF0BC9EA254A4F2262928F6B7DED3DBAF2C124958,
	U3CCheckAvailabilityU3Ed__36_MoveNext_mF640DE420523AFC133818CAFBD896C139E041218,
	U3CCheckAvailabilityU3Ed__36_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mC6179916F8366120728FBC4A319199B5A33EA81F,
	U3CCheckAvailabilityU3Ed__36_System_Collections_IEnumerator_Reset_m8826529D457D216C4F0D74058D9C41EB77713367,
	U3CCheckAvailabilityU3Ed__36_System_Collections_IEnumerator_get_Current_m89D3BE7534710C9D3AAA093A4EC2BA45FFA4BE8E,
	U3CInitializeU3Ed__39__ctor_mD9B6C69399849460F6186CA514C8C0C1E3E4A31F,
	U3CInitializeU3Ed__39_System_IDisposable_Dispose_mAE3424C27A371EE887CF8C7268B5961CCFC99127,
	U3CInitializeU3Ed__39_MoveNext_mEC7D94B4F821E5F112EE36C6D43C26E86E28F1C4,
	U3CInitializeU3Ed__39_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m4EC9894037C15834354448A7CBA6F50EA660DA3E,
	U3CInitializeU3Ed__39_System_Collections_IEnumerator_Reset_m8FC03158D4A30390C52890CE1CDA62916CF29114,
	U3CInitializeU3Ed__39_System_Collections_IEnumerator_get_Current_m7F70E4E5D3B7329FD234B87E00BC439DA70E4774,
	U3CInstallU3Ed__37__ctor_mD9BA7D04DE9A0604D515EEEF6969A8833E6B69C0,
	U3CInstallU3Ed__37_System_IDisposable_Dispose_mE976D780D3543F4D7E8131386463899493885C85,
	U3CInstallU3Ed__37_MoveNext_m10883753BFAA6E6C97897C8C1009C7ED51270B16,
	U3CInstallU3Ed__37_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mD718DE0B0B711C9E9CD7FA45F56C4756DECA26DF,
	U3CInstallU3Ed__37_System_Collections_IEnumerator_Reset_m8DF442701056725A9816E886F188D86C31E29879,
	U3CInstallU3Ed__37_System_Collections_IEnumerator_get_Current_mC897BF06DE02727E24E1CA2BE07C0304EDBA8EE6,
	ARSessionOrigin_get_camera_m5D908BC3C882C5BA0E21A07E4BB5093AF0AD5830,
	ARSessionOrigin_set_camera_m67B450A0B2E571CE6B9785DE656ED32B41906239,
	ARSessionOrigin_get_trackablesParent_mF42C34E0B09B58DEE52CF3CC13563BE541C14F9F,
	ARSessionOrigin__ctor_m35D4BAAA2E6EEB818983F5AB4FEDEF27CCEDDDFB,
	ARSessionStateChangedEventArgs_get_state_mC0A6FB4AF08C068BFD5D9730F60215BE662A950B,
	ARSessionStateChangedEventArgs_set_state_mBEF6AA8214783D771EDF618B3E863D6DBC1FE8A7,
	ARSessionStateChangedEventArgs__ctor_m1C0C6E6FD7F83932D0780826124568FCC85C888A,
	ARSessionStateChangedEventArgs_GetHashCode_m58967DAA40E3E7088B23CB434E04B3BA745E3076,
	ARSessionStateChangedEventArgs_Equals_mDB8C22D91659F1BEA3417585A531A31661D56FD2,
	ARSessionStateChangedEventArgs_ToString_m1F84E487E601B1FB57C5E6F051262AA9264E66D5,
	ARSessionStateChangedEventArgs_Equals_mD7EC44B01E9572499F4888DED9885761FCBA47BF,
	ARSessionStateChangedEventArgs_op_Equality_m136CF1C9C70E06432E3A359FE26B9CC50A3666AE,
	ARSessionStateChangedEventArgs_op_Inequality_mE85DFE523DFB69288E4FD9FC6A70CFA137502321,
	ARTextureInfo_get_descriptor_m42C04EE9D6B420EFFC6002A044CA8B2FB6E27FE2,
	ARTextureInfo_get_texture_m9A17329F68E159CC5C924956D4258BD26990C40A,
	ARTextureInfo__ctor_mE3D6856CEA559DD33C81F5E993451608F7515AC1,
	ARTextureInfo_Reset_m94C8E8348B77C4A7055ABFC20FA8E59632293A59,
	ARTextureInfo_DestroyTexture_m4A1489A030CA1E43E61CB7F3413AC97B5F5180F9,
	ARTextureInfo_GetUpdatedTextureInfo_m88B8391EEE756B9D4DD68A2C8B7CDB5468C319F0,
	ARTextureInfo_CreateTexture_m6A095D54141ADC1B50174B6FE9CCBECFD1B4713C,
	ARTextureInfo_IsSupported_m3DF7C6656B31ED56F557D0BD4DE78E40304E6B09,
	ARTextureInfo_Dispose_mF92A6E0C4D08205EDC7CF817BC866A9AB179632F,
	ARTextureInfo_GetHashCode_m85504109E923E3CA1494548DD2C3261C1976E97A,
	ARTextureInfo_Equals_mFB1B9B103E90F76AE0EB4E2DED235BCF3DCBF034,
	ARTextureInfo_Equals_m020839E8607BDF4DEFEDBB125778BEFE105F563D,
	ARTextureInfo_op_Equality_m0EA32FFFF37B03E66CCA2D2E6DFBB8B9A62006AE,
	ARTextureInfo_op_Inequality_m4A2C24587F6484E0CBD99F3D91C7D0BD5657104B,
	ARTrackable__ctor_m8074A81414BDB3B30F9863C80B10EF90C99FFD8B,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	ARTrackedImage_get_extents_m3201758E786A2532D340FE93EEF73B6AA2D055DB,
	ARTrackedImage_get_size_mAC20A0ECB99C7502138BA134686286B3C126D6F9,
	ARTrackedImage_get_nativePtr_mE99C5B4BF589443B6C342B22C99AC02D7DED3E10,
	ARTrackedImage_get_referenceImage_m7F8C3733154BE3242F82D2C9A7987A8AAC1F3E6B,
	ARTrackedImage_set_referenceImage_m1EA6089A5257113AC8141362107AA8CEA58EF38B,
	ARTrackedImage__ctor_mE6C1F2FA91A2B6F58665058A2550EFC07C7CCDA8,
	ARTrackedImageManager_get_referenceLibrary_mD811689B94E007CD8F708D91155C8DC575E948DD,
	ARTrackedImageManager_set_referenceLibrary_m826772B820572A41FE7B566E0F6E0C25024D0E5E,
	ARTrackedImageManager_CreateRuntimeLibrary_m53343F2B04F48AE642735069B9348B0EAF009D1E,
	ARTrackedImageManager_get_maxNumberOfMovingImages_m54630EFA22CCADFA120FD908E0714E7297AEB215,
	ARTrackedImageManager_set_maxNumberOfMovingImages_m4D867796B5E6C507243D2C43680BDDFF5AEC0CD8,
	ARTrackedImageManager_get_supportsMovingImages_m9940C251AF671CDAB6517357F5A5C05563478203,
	ARTrackedImageManager_get_requestedMaxNumberOfMovingImages_m1FC9793915B2DE5361B432DC12F137210C832BF0,
	ARTrackedImageManager_set_requestedMaxNumberOfMovingImages_mD253ACF680396E6BDBEE194358093D84D7D3218A,
	ARTrackedImageManager_get_currentMaxNumberOfMovingImages_mB131CA9A3525418C91945D1CC075D00C59D70F1C,
	ARTrackedImageManager_get_trackedImagePrefab_mC698D56D9B539242437FA40F1DDC6E4FE959DE2A,
	ARTrackedImageManager_set_trackedImagePrefab_mAAE136140F485320C96A4E30F244CEED525395A9,
	ARTrackedImageManager_GetPrefab_mE7358EBC36EAFF6F7CECA248BAB004DDEA8B1DA9,
	ARTrackedImageManager_add_trackedImagesChanged_mB190015342B225144729100E0E38745CAFA7F438,
	ARTrackedImageManager_remove_trackedImagesChanged_mA754FAB732F3D5D803FA3A58A51E6487E75CCF34,
	ARTrackedImageManager_get_gameObjectName_m0E49CC1FC7F70A3355F7E78BB7A5253A0740C991,
	ARTrackedImageManager_OnBeforeStart_mB9877EAD9A54B82012E04BA3BAFA72F0843F906C,
	ARTrackedImageManager_FindReferenceImage_m7B01D81D360921A7DCDFEAA4DDD7710146371ABD,
	ARTrackedImageManager_OnAfterSetSessionRelativeData_m4B0BEBB19A6073F8CFF940C974643B3241539FE5,
	ARTrackedImageManager_OnTrackablesChanged_mCD020C973B99D2842BDCC4723B49DAC2BAD34EF0,
	ARTrackedImageManager_UpdateReferenceImages_mFD7E9622A308CF5AB7005736FF1425FD9EDA6EFB,
	ARTrackedImageManager__ctor_m1F381872541A32AA65A40BDC401754F8C817DB4A,
	ARTrackedImagesChangedEventArgs_get_added_m2929CC85141D13AF05C1484AFB47E043C6D3EE35,
	ARTrackedImagesChangedEventArgs_set_added_m4E298214A1144403A84E0AE63837F2941528F18F,
	ARTrackedImagesChangedEventArgs_get_updated_m0C896E1C21EF35FF4B31B36563838EC2BA3CDFD1,
	ARTrackedImagesChangedEventArgs_set_updated_mBFE8B2D25F10827CC95CA76E4C52D98EFC1131BE,
	ARTrackedImagesChangedEventArgs_get_removed_m062CBBEF163BEE47A673F7B3BDC0DD1C6EAEA185,
	ARTrackedImagesChangedEventArgs_set_removed_mC7F5D25B8E4AD4CBBE6A43D50FAE0456BE3F928B,
	ARTrackedImagesChangedEventArgs__ctor_m22F5D20572E4D17270B7CFBA7F0EA9445DAEE8C3,
	ARTrackedImagesChangedEventArgs_GetHashCode_mEFBDE822EC1AA6B0E388B86ACE4043D66E23A742,
	ARTrackedImagesChangedEventArgs_Equals_m728C2C2B10BA8C4A625C3004C83730589C86C542,
	ARTrackedImagesChangedEventArgs_ToString_m949FF3EE96422AE2776C8FFBBD7CA342E56AE365,
	ARTrackedImagesChangedEventArgs_Equals_m25F0396D7AA82B5E488BEA4129EE00F355EA3919,
	ARTrackedImagesChangedEventArgs_op_Equality_m5D20224CEBE5751DEE808D4037B91FC92BD6FB04,
	ARTrackedImagesChangedEventArgs_op_Inequality_mF9739F81EDDDCFB2FC3FFE197272FBD53A15F1EE,
	ARTrackedObject_get_nativePtr_m15342BE99E069A5B047D184FE30ED47CF7C3A614,
	ARTrackedObject_get_referenceObject_mCD7D3716A01017BE3A0DEF15B8B4D11360679D44,
	ARTrackedObject_set_referenceObject_m44D01DB41972F1860CAAF329F2E3564170577D6B,
	ARTrackedObject__ctor_mCFCC68813EF818A1084A35A8F69E553ACEF45D88,
	ARTrackedObjectManager_get_referenceLibrary_mCAF4DB685114A5401A283897A4CD0C00C7A67762,
	ARTrackedObjectManager_set_referenceLibrary_mDD520435B7F2BECDB8F2FD77CF5D11FAF51B8BD6,
	ARTrackedObjectManager_get_trackedObjectPrefab_m08AA42CA1FC81DC3584E38092F30001F509457C8,
	ARTrackedObjectManager_set_trackedObjectPrefab_m9A5F19F8F5B48C6967E56B45DCF39F3E9334E503,
	ARTrackedObjectManager_GetPrefab_mED6DCAD42BF177A96B0FDA9F2796BF7ABDA7F5BC,
	ARTrackedObjectManager_add_trackedObjectsChanged_m4E28ABA38B522FBE3B8150E1090237954A804FAE,
	ARTrackedObjectManager_remove_trackedObjectsChanged_m8412618ABDA3802160C112C7C884886D8057F41D,
	ARTrackedObjectManager_get_gameObjectName_m3B538A2C8762E66D92FC16DE8F04CC3ECBD059EF,
	ARTrackedObjectManager_OnBeforeStart_m9D10F03CFBD1456E388E140041F2FF8A950D8DEE,
	ARTrackedObjectManager_OnAfterSetSessionRelativeData_m5FB25CD1E84A12863FCD5A29560B5EEDA34C881C,
	ARTrackedObjectManager_OnTrackablesChanged_m618F742518B359861B7DADCFA727F11FD0F03846,
	ARTrackedObjectManager_UpdateReferenceObjects_mE6BEC5C294F6E2D966572EBF0687E81261AE3551,
	ARTrackedObjectManager__ctor_m37B637D2161B6EDDD2BFB8FAC4660CF3B2334073,
	ARTrackedObjectsChangedEventArgs_get_added_m71E8E2055C979657273051DE23FCEB938964169A,
	ARTrackedObjectsChangedEventArgs_set_added_m6DE32B4C3E8C17166AC5DB4C181DE56842BD0922,
	ARTrackedObjectsChangedEventArgs_get_updated_m01050D519E6281EECB48200D952FE1E1FFFE1A60,
	ARTrackedObjectsChangedEventArgs_set_updated_mF2A1424732988AB85FF48E63741896BF61E94B46,
	ARTrackedObjectsChangedEventArgs_get_removed_mDF5FA585D41D4B0D4517D3C5387B254F6EA6DFDA,
	ARTrackedObjectsChangedEventArgs_set_removed_m58FAB3CA4AFD4D9B8D0BA1FD150C8FBF06D30F4E,
	ARTrackedObjectsChangedEventArgs__ctor_mED9F9F2F2307AC78C3580327A6501F5E7ED5060A,
	ARTrackedObjectsChangedEventArgs_GetHashCode_mC0D8DBBE030D0F00D1D0662C2C100FADF4EC429A,
	ARTrackedObjectsChangedEventArgs_Equals_mB6CEB7818A761BB4B5009A280C058522176E7641,
	ARTrackedObjectsChangedEventArgs_ToString_m1E23EE412E0E27AE4186B56B83F4EF1F12FAD5A7,
	ARTrackedObjectsChangedEventArgs_Equals_m822193D19946082B0F5D17078ABD169B4A002EDE,
	ARTrackedObjectsChangedEventArgs_op_Equality_m45210A3221BD9BDDF26391B7B0652E13FD2EBDF1,
	ARTrackedObjectsChangedEventArgs_op_Inequality_m5D1EFCF44E645D67AB54A770E89E3C4A5D541028,
	DebugAssert_That_m8F3B14DDB5DC184A9E5E5E239499CC6F2F7D1FF7,
	Message_WithMessage_m6D980DF1AD1190D26E45B88C3600AF4226FDD63C,
	DebugWarn_WhenFalse_mADECDEF90132C7730D282B7F39723E3C1223C760,
	Message_WithMessage_m7331F24D7470335EC2EEF8FBA56A8D570ACC8FD3,
	CameraBackgroundRenderingModeUtilities_ToXRSupportedCameraBackgroundRenderingMode_m419843F229D4A977DECD405B5A91138BB6C3D8CF,
	CameraBackgroundRenderingModeUtilities_ToBackgroundRenderingMode_mF6F4B035C02786ACBD23668EB7756DED6B70FB19,
	CameraModeExtensions_ToCameraFacingDirection_mE8BDA09B71E726FF7E363A71628B7AF7CD44E715,
	CameraModeExtensions_ToFeature_m43AE9A005CAA4089C520C0A622F1006759B38C6C,
	DebugSlider_OnPointerDown_mB6259788D06531A4A5B2BDA16C97DE0510AC0E72,
	DebugSlider__ctor_m53A63C1A397D2EA62B829FAD9A74B78FE5C0EF7D,
	HashCodeUtil_Combine_m5CA91217A96CB1506F0A9C83ECC4ED11F4EDF39C,
	HashCodeUtil_ReferenceHash_mFDEB5485EAA482CF3FFD30D45BFFAF9D15CACED3,
	HashCodeUtil_Combine_m1FCE36175602D1544BF3DC515483B7B17A9C3356,
	HashCodeUtil_Combine_m4C8CAD427BA28472BA6AA92D62A2D03C5778D4AC,
	HashCodeUtil_Combine_mE51AF1366174B0F2C05662D0D010E5A9506C57EB,
	HashCodeUtil_Combine_mD6780CAC0596A183974DFEF13F0D3876F23FED45,
	HashCodeUtil_Combine_mA931E66011BC522343259C0B46EA40748E93EABE,
	HashCodeUtil_Combine_m9268BF0EF88EB1BF2333A239165C7949F6BF74FB,
	HelpURLAttribute__ctor_mD7EFDC5AF7F7319C616C07A83679302B335BF142,
	NULL,
	LightEstimationExtensions_ToFeature_m0D9030625B25390EADA9BA2A0ABB9E1FD135A0B2,
	LightEstimationExtensions_ToLightEstimation_m99E4653A9A25AF86F23C159949952A9B607C61A5,
	LightEstimationExtensions_ToLightEstimation_m23C075B15237696ED895A71F5DD990F28FD0571E,
	LightEstimationExtensions_ToLightEstimationMode_m73F6397FE2A4534E42DE104592CAD83B178831C5,
	LoaderUtility_GetActiveLoader_mBAEB6B82BB71ED27FB5041D90BE39D0EEFE61603,
	LoaderUtility_Initialize_m0AB24E553DEC4425F9E1EB328FC7F20FFA34AAE8,
	LoaderUtility_Deinitialize_m567824AD533051BA6006043EAB1F94AC76F4847C,
	MeshInfoComparer_Compare_m1A84A2B187F6AF909D794B1F6E2084B91CB6BA84,
	MeshInfoComparer__ctor_m3CA4B312F3F01D87ED687B402A1B2A6E99E21054,
	MeshQueue_EnqueueUnique_m0A135283E10EC5C7EB0BABD7F828838309F76DCF,
	MeshQueue_get_count_mD34731FE94DA122722DBDAD7EED0EE24A3C23A5D,
	MeshQueue_TryDequeue_m4BEA797ABC13CC2A5BC105CBE82492EE213057BB,
	MeshQueue_Remove_mDF7E634D6EED310E75A70E9DACB7FDC968AE0837,
	MeshQueue_InsertNew_m9FCE96265CD46761953063618A3AC85B54DFCC9F,
	MeshQueue_UpdateExisting_m0BF022171FB9EC99619552D30C85BA95B8301E60,
	MeshQueue_Clear_mB28172C35E681281DC31ADBED3023D10DB288057,
	MeshQueue__ctor_m5D8C8E3CC97C1A6590AA3BF954829CE44F5407C5,
	MutableRuntimeReferenceImageLibraryExtensions_ScheduleAddImageJob_mF333C41068A0270A13A2DAAB5956B42514F6A86A,
	MutableRuntimeReferenceImageLibraryExtensions_ScheduleAddImageWithValidationJob_m7FBA37C8669AAED35DB626632C60D68085CF6FED,
	DeallocateJob_Execute_mECC71B15D80D3951E0D0B4B0B7B28A50DBC81D6F,
	PlaneDetectionModeMaskAttribute__ctor_m716EA3E15D951963D85172A4A6B0DACA68FE1D7A,
	PoseExtensions_InverseTransformPosition_mD503BE16CB7C5E4F2DC50E90C3BDD22BC871A6A1,
	PoseExtensions_InverseTransformDirection_mE426CC955182D3C53273AB1DB1E706D432D33160,
	PoseExtensions_InverseTransformPositions_m3C842AB25E8C84CD1A322562F76680CC99B352D1,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	ToolButton_get_buttonHighlights_mC1F0475431AEEC1E84199C35A735DAF0568772FA,
	ToolButton_set_buttonHighlights_m7A153CD59D08BB7368394D34ABD1F5A2F035BB3D,
	ToolButton_Start_m3166B8812FBAF5A30470ED482A8E2FE5928F0583,
	ToolButton_HighlightButton_m6909154958F4992A8643F61958D685D311CDEF67,
	ToolButton__ctor_m32DBB315DF184CBC20B4A4F6DC87E9F4A2C29A41,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	TrackingModeExtensions_ToFeature_m683FFDDC9DDC2DA86001B1CCF0C12D8961639041,
	TrackingModeExtensions_ToTrackingMode_m4896FC7865A6619FB20F1028CF585126F4B3D6A2,
	TransformExtensions_TransformRay_m34136D03EFA2C12718B4F8EC879C293E405D7AB5,
	TransformExtensions_InverseTransformRay_m3417C7D5AFC5CC8B2ADC0F6DCA4BFF3C7E539B3C,
	TransformExtensions_TransformPose_m657A2F269CFE7ABD9E77029577861EA464E2364C,
	TransformExtensions_InverseTransformPose_m3705FF36F67498108EBA26FA116491ED4ECBB1DE,
	TransformExtensions_TransformPointList_m7C213FB0F243A84C8ADA345E7D0600CCBC1A066E,
	TransformExtensions_InverseTransformPointList_m62F86C3FA0F4DCB23357FBCB9E6254A72F502A78,
	TransformExtensions_SetLayerRecursively_m8A1487644AED6A3BE7873728671CB6EE9304EF8C,
};
extern void ARAnchorsChangedEventArgs_get_added_m5F9CEB4587F9496D0DA28880430B69EFDB550600_AdjustorThunk (void);
extern void ARAnchorsChangedEventArgs_set_added_mEE4383EBEAFBE249A99CEAF1F0EA6FDB964BAFEB_AdjustorThunk (void);
extern void ARAnchorsChangedEventArgs_get_updated_m21039F14E472A75F969EE0BA5CBA9007DC12B593_AdjustorThunk (void);
extern void ARAnchorsChangedEventArgs_set_updated_mDA509077951F3AE2F2DC7300937CA5C626BABEF4_AdjustorThunk (void);
extern void ARAnchorsChangedEventArgs_get_removed_mF1E9D61E21153018C87DE99722FF8820C351CFD8_AdjustorThunk (void);
extern void ARAnchorsChangedEventArgs_set_removed_m18D9FF865947D6363BA0989B783B2CC746A7B867_AdjustorThunk (void);
extern void ARAnchorsChangedEventArgs__ctor_m67B444466F34F943C140F8F7EC4959DC1FFDDB8A_AdjustorThunk (void);
extern void ARAnchorsChangedEventArgs_GetHashCode_m278A649D0606054A9B7E7C8AFF08CC99C2685024_AdjustorThunk (void);
extern void ARAnchorsChangedEventArgs_Equals_mC2C108ADF4C9783BD7470E6573196978E61FD49B_AdjustorThunk (void);
extern void ARAnchorsChangedEventArgs_ToString_mA0C838C2F9AB2B51089A47F4CF0E7049F8A86BAC_AdjustorThunk (void);
extern void ARAnchorsChangedEventArgs_Equals_m67E2B32F8DEAB6F7C31BF818E1F42E799B3A0DAF_AdjustorThunk (void);
extern void ARCameraFrameEventArgs_get_lightEstimation_mDAD20A000D180FB372E8214B37A8A4EB4F0F312C_AdjustorThunk (void);
extern void ARCameraFrameEventArgs_set_lightEstimation_mE04691D14C2CD985D757C11AA126AE60BC6DBB51_AdjustorThunk (void);
extern void ARCameraFrameEventArgs_get_timestampNs_m3B9E21BF0B36F035E105ED2B155983E03B21F4E9_AdjustorThunk (void);
extern void ARCameraFrameEventArgs_set_timestampNs_mF3DE3C39790E92B6F2190BEE8DA69568C8A47F20_AdjustorThunk (void);
extern void ARCameraFrameEventArgs_get_projectionMatrix_mA09F2170ACA84AEC8E9407DE70B9CD1D2E443182_AdjustorThunk (void);
extern void ARCameraFrameEventArgs_set_projectionMatrix_m7F7C0173EAC5E149E6A4A6FA9FA85E8C071DF517_AdjustorThunk (void);
extern void ARCameraFrameEventArgs_get_displayMatrix_m3354FFAEF054F0A8F4D19BFD506468A06F3AD65F_AdjustorThunk (void);
extern void ARCameraFrameEventArgs_set_displayMatrix_m4DB301FFAEAC7277FD816737053A2688B9CDE902_AdjustorThunk (void);
extern void ARCameraFrameEventArgs_get_textures_m96823876351BD11C26D78BAA01936FF751182547_AdjustorThunk (void);
extern void ARCameraFrameEventArgs_set_textures_mB9F3D579FB8DA3BDF3F4B85D1E598AE19959BAC7_AdjustorThunk (void);
extern void ARCameraFrameEventArgs_get_propertyNameIds_m9B76BC52DB8349A38D5514528C63A36494283798_AdjustorThunk (void);
extern void ARCameraFrameEventArgs_set_propertyNameIds_m7F4470E1C46E52045645D6C93923F6DB7A92C6B5_AdjustorThunk (void);
extern void ARCameraFrameEventArgs_get_exposureDuration_mC4D1A5D0266949EDB98A74E66564E421A9B2A2F8_AdjustorThunk (void);
extern void ARCameraFrameEventArgs_set_exposureDuration_m48C0FBF3D428A1507BB522D23B2A383B265F4B1B_AdjustorThunk (void);
extern void ARCameraFrameEventArgs_get_exposureOffset_mFDCFBB779CF302DF2159000D328826E7880FDD0F_AdjustorThunk (void);
extern void ARCameraFrameEventArgs_set_exposureOffset_m82EEA2BFFE750731718B4B39FBCA59BC0B4CD2EE_AdjustorThunk (void);
extern void ARCameraFrameEventArgs_get_enabledMaterialKeywords_m76A2E83147560A72FF3834BF0FB7C1B3FEED6240_AdjustorThunk (void);
extern void ARCameraFrameEventArgs_set_enabledMaterialKeywords_mE8FD88DCAEFA52B0B6E05DA72F61AA7727D88E9D_AdjustorThunk (void);
extern void ARCameraFrameEventArgs_get_disabledMaterialKeywords_m254ACC80879CD8B270566B38CCD6ED99D26255A8_AdjustorThunk (void);
extern void ARCameraFrameEventArgs_set_disabledMaterialKeywords_m95D9308AC9AA63B459F87C44AD1B378043C08BDA_AdjustorThunk (void);
extern void ARCameraFrameEventArgs_get_cameraGrainTexture_m0CEACD5C125C07A7D6E73695808202940300B633_AdjustorThunk (void);
extern void ARCameraFrameEventArgs_set_cameraGrainTexture_m2BBDCEB456D16E1AD14D74874CD3F274806F1D61_AdjustorThunk (void);
extern void ARCameraFrameEventArgs_get_noiseIntensity_m441B0EFBF898D8A18AB1E102E41CF1BB33072591_AdjustorThunk (void);
extern void ARCameraFrameEventArgs_set_noiseIntensity_m8AA71321684DA61B4D83D0C2F91C85AA9A2FEE7E_AdjustorThunk (void);
extern void ARCameraFrameEventArgs_get_exifData_mDB0728F64EE619CB4174644B6D58598DAE8EC2F5_AdjustorThunk (void);
extern void ARCameraFrameEventArgs_set_exifData_m1FB5103D69EEE478654B39783FCD7D662F557E55_AdjustorThunk (void);
extern void ARCameraFrameEventArgs_TryGetExifData_m6AD832BCB4867C3D42D03A0CB6B291BAE259B3E0_AdjustorThunk (void);
extern void ARCameraFrameEventArgs_GetHashCode_m13E6D8C9B47ACA833C0D2F23C838883FEF4A464F_AdjustorThunk (void);
extern void ARCameraFrameEventArgs_Equals_m896C0CD5FC48A354B4AE1CA23F7219FD812873CF_AdjustorThunk (void);
extern void ARCameraFrameEventArgs_ToString_m31AA489F1C81FA10709305176CE7EE2676F28CD8_AdjustorThunk (void);
extern void ARCameraFrameEventArgs_Equals_mE86A86B648BFB94A2CB70BE472F974C550AB6CC6_AdjustorThunk (void);
extern void AREnvironmentProbesChangedEvent_get_added_m5EB8CAE059E5A0F0A84A11866F2343F630B7C997_AdjustorThunk (void);
extern void AREnvironmentProbesChangedEvent_set_added_mD254DC5C2E0B0AC8D0F604829177E92AAA36DB98_AdjustorThunk (void);
extern void AREnvironmentProbesChangedEvent_get_updated_mFFE292486A4D2DB4385C70FAE94EB6048CBB99F3_AdjustorThunk (void);
extern void AREnvironmentProbesChangedEvent_set_updated_m8346F2C232C68965428428BFC1D42EF188390D27_AdjustorThunk (void);
extern void AREnvironmentProbesChangedEvent_get_removed_m0D5B42BCAD8408B1FC0DF82E24C4EAF1D8ADF40A_AdjustorThunk (void);
extern void AREnvironmentProbesChangedEvent_set_removed_m1C122637075CAC8EE8D30678068F20C10F9805B8_AdjustorThunk (void);
extern void AREnvironmentProbesChangedEvent__ctor_mA834200E5CC7AEB6D93ED5B987F19D24A9C82944_AdjustorThunk (void);
extern void AREnvironmentProbesChangedEvent_GetHashCode_m1F82F2279F41834A46A159D45D9FA4C37ABDEFF6_AdjustorThunk (void);
extern void AREnvironmentProbesChangedEvent_Equals_m5DC12B1B83E64882261F789A0651ADA40D58D7C3_AdjustorThunk (void);
extern void AREnvironmentProbesChangedEvent_ToString_m66B3C7111A9054FAB69BABF82CF0789974FBA048_AdjustorThunk (void);
extern void AREnvironmentProbesChangedEvent_Equals_mAF37E53C6392E0457299A76EE7CC5D805BDF4D64_AdjustorThunk (void);
extern void ARFacesChangedEventArgs_get_added_m8EA1424F8F262955AFD00664B81C15AB54EFA213_AdjustorThunk (void);
extern void ARFacesChangedEventArgs_set_added_mF3FF69CFE03334FC9582BFF06D5124961F955D43_AdjustorThunk (void);
extern void ARFacesChangedEventArgs_get_updated_m1D3587AA05E5789F796A71C3C87CD6D11BE0005B_AdjustorThunk (void);
extern void ARFacesChangedEventArgs_set_updated_mAD5B3FD1E0A6058BD6328793BA0505E82B476469_AdjustorThunk (void);
extern void ARFacesChangedEventArgs_get_removed_mC31806DE526D3BECAA26C35DAE321F2DE77C335E_AdjustorThunk (void);
extern void ARFacesChangedEventArgs_set_removed_m0A6A558713C45E20F0040C50763F01B1E061E387_AdjustorThunk (void);
extern void ARFacesChangedEventArgs__ctor_m4C7990AA3703F850C5B4D0CCCCEABBC0B58DB940_AdjustorThunk (void);
extern void ARFacesChangedEventArgs_GetHashCode_m67DD1456E147A37E35FF2483BC7053D59B549CF6_AdjustorThunk (void);
extern void ARFacesChangedEventArgs_Equals_m08B2E52D4FD329CAB89DC14BEF84E55274E2EAEB_AdjustorThunk (void);
extern void ARFacesChangedEventArgs_ToString_m0773324E2DC3392C12286F68495590B2F1E7C057_AdjustorThunk (void);
extern void ARFacesChangedEventArgs_Equals_m32B401EBB3F1CFE364F4F2C5B7746F8D48B1DDDE_AdjustorThunk (void);
extern void ARFaceUpdatedEventArgs_get_face_mFD21EF59457212494C80FE998D1CA8AC0F057170_AdjustorThunk (void);
extern void ARFaceUpdatedEventArgs_set_face_m0E59FF874A145B994A77250CEA675FBF8AD3019C_AdjustorThunk (void);
extern void ARFaceUpdatedEventArgs__ctor_m3D4294A61EBEAF3158E5C90C57FB112020A58987_AdjustorThunk (void);
extern void ARFaceUpdatedEventArgs_GetHashCode_mCF1AF45853FADD6B4F52B43038647782B888B700_AdjustorThunk (void);
extern void ARFaceUpdatedEventArgs_Equals_m24F398E98F52EBA0DF689C392CF58291742028F9_AdjustorThunk (void);
extern void ARFaceUpdatedEventArgs_Equals_m50565F99C9CC4FF2F3122EF78D2C4DE1881E1BDC_AdjustorThunk (void);
extern void ARHumanBodiesChangedEventArgs_get_added_m6FD862CC8C8281F9377D754BBAB7F3C616955D4C_AdjustorThunk (void);
extern void ARHumanBodiesChangedEventArgs_set_added_mAC9FB746BD1745CB5BEDE7C2220302035CD63F77_AdjustorThunk (void);
extern void ARHumanBodiesChangedEventArgs_get_updated_mA2FD128E22BDACD09294F107AA3F2A91CFFAA550_AdjustorThunk (void);
extern void ARHumanBodiesChangedEventArgs_set_updated_mB71FDE70AD3CFC1096386A5E4260A9FFBB420C06_AdjustorThunk (void);
extern void ARHumanBodiesChangedEventArgs_get_removed_m613516A989AA48A625C79A3C309D9C1465762BCA_AdjustorThunk (void);
extern void ARHumanBodiesChangedEventArgs_set_removed_m97AC3D82086A3691484D3B587A534B9D6DC385B1_AdjustorThunk (void);
extern void ARHumanBodiesChangedEventArgs__ctor_mA364D7E41518A8059F90E1EFC71FDD9F5F52B7F7_AdjustorThunk (void);
extern void ARHumanBodiesChangedEventArgs_GetHashCode_m7C9E78E03AE212095DDA1B44594F64475937253F_AdjustorThunk (void);
extern void ARHumanBodiesChangedEventArgs_Equals_m0D61EF468E803F4B69514B78E7E47852B9AC39AF_AdjustorThunk (void);
extern void ARHumanBodiesChangedEventArgs_Equals_m2E24676DA642BE3E29F0347454BC4CB993DCF8B4_AdjustorThunk (void);
extern void ARHumanBodiesChangedEventArgs_ToString_m4685EF40034A2625C670EA5813E73ED415B35431_AdjustorThunk (void);
extern void ARLightEstimationData_get_averageBrightness_mCDE95FB42D807C168E187942BD9DDAB65439AE19_AdjustorThunk (void);
extern void ARLightEstimationData_set_averageBrightness_mC057F484D1EA8E3760977FE33297539B665D95CE_AdjustorThunk (void);
extern void ARLightEstimationData_get_averageColorTemperature_m688BB1F18E15D7058FDEFC012451A72CD6D193DC_AdjustorThunk (void);
extern void ARLightEstimationData_set_averageColorTemperature_m2B62048E88C65A904843D4A46BDAC8F764519B79_AdjustorThunk (void);
extern void ARLightEstimationData_get_colorCorrection_mF7D9D83F249587E6A1E18D845C0D521C907DF496_AdjustorThunk (void);
extern void ARLightEstimationData_set_colorCorrection_mD28E53EA20EE633541E1F9AFBB853A10AF3361BA_AdjustorThunk (void);
extern void ARLightEstimationData_get_averageIntensityInLumens_m3D74CD050CDA61A97595AF8854E97C3239F159D1_AdjustorThunk (void);
extern void ARLightEstimationData_set_averageIntensityInLumens_m79B3A12B384470DCCCCEE390DE5ECCC0E1CCCE5E_AdjustorThunk (void);
extern void ARLightEstimationData_get_mainLightIntensityLumens_m618D7C8A4FA8FFC3AB370DFC00596DDFA592288F_AdjustorThunk (void);
extern void ARLightEstimationData_set_mainLightIntensityLumens_m4B7F856E88E7F8247B61EFEA6231720A23340B42_AdjustorThunk (void);
extern void ARLightEstimationData_get_averageMainLightBrightness_mAA37F537DB4589CB01464B6A7E9AA8AF2C6E693F_AdjustorThunk (void);
extern void ARLightEstimationData_set_averageMainLightBrightness_mCE3727F3F32B8EB41503E852CFBCC20898B50D93_AdjustorThunk (void);
extern void ARLightEstimationData_get_mainLightColor_m7A17F97AC5EBEE6B3E3B093AB73F8FB904C79C3E_AdjustorThunk (void);
extern void ARLightEstimationData_set_mainLightColor_m80FE4C8781175687581668E3708A899C15F278FE_AdjustorThunk (void);
extern void ARLightEstimationData_get_mainLightDirection_m49A1B8B5DB38EAE356A22C1AD0A4FD671C6C1427_AdjustorThunk (void);
extern void ARLightEstimationData_set_mainLightDirection_m701A092104413621AC8320050422D4A0C94BE57C_AdjustorThunk (void);
extern void ARLightEstimationData_get_ambientSphericalHarmonics_m34C8C4BB5EFDD7E2DAEDF983F11031ABFCA16C41_AdjustorThunk (void);
extern void ARLightEstimationData_set_ambientSphericalHarmonics_m4DF86D65B2095ADB18B9504CC23518600DDA2D9B_AdjustorThunk (void);
extern void ARLightEstimationData_GetHashCode_m3F60EB276A24D0B1D3D7B74F0C9CBE044CC49595_AdjustorThunk (void);
extern void ARLightEstimationData_Equals_mD33829136230F81948FE58D2EB7A32E53973AD02_AdjustorThunk (void);
extern void ARLightEstimationData_ToString_m57D70F82DD05F504BB4DC8937BAE5E9B0CE686FA_AdjustorThunk (void);
extern void ARLightEstimationData_Equals_m91813FC80F85190AC70FCE2827D4844F15A1BB65_AdjustorThunk (void);
extern void ARLightEstimationData_ConvertBrightnessToLumens_m09279642CD4DFF335987CE9212C0F1E82F4CA4A2_AdjustorThunk (void);
extern void ARLightEstimationData_ConvertLumensToBrightness_mDEC789CC752A2A51FBBB1C8B2C27CB6F0C615D35_AdjustorThunk (void);
extern void ARMeshesChangedEventArgs_get_added_m0D451C7DDD6CE9B7697CF3678D2F1DE440170E6B_AdjustorThunk (void);
extern void ARMeshesChangedEventArgs_set_added_m6A461DFF63F7E28D7A4B02AD47629D95BBAC20B6_AdjustorThunk (void);
extern void ARMeshesChangedEventArgs_get_updated_m06E6CE944759C31B7500D0A4EF8D07D262A0626F_AdjustorThunk (void);
extern void ARMeshesChangedEventArgs_set_updated_m60115D846577E418E88D4D273AD1A2ECE1D1BAAE_AdjustorThunk (void);
extern void ARMeshesChangedEventArgs_get_removed_mB982A0629BEA14EC069263AD7BAB6C43CB187674_AdjustorThunk (void);
extern void ARMeshesChangedEventArgs_set_removed_mA6932589C03B43D48CB7DC1F7AA627BD85F6E396_AdjustorThunk (void);
extern void ARMeshesChangedEventArgs__ctor_m3265D79A775738B64ADFBA5B52C89DCC77B26F2B_AdjustorThunk (void);
extern void ARMeshesChangedEventArgs_GetHashCode_m43CF0D8A78557CE5A2E097DF4DED87531AE07F5E_AdjustorThunk (void);
extern void ARMeshesChangedEventArgs_Equals_m077E07D6856F294A374A0EFE0E38DCB958ADAAFC_AdjustorThunk (void);
extern void ARMeshesChangedEventArgs_ToString_m9249BC2AF8169516627B851472F3DAFA6F067879_AdjustorThunk (void);
extern void ARMeshesChangedEventArgs_Equals_mAC06257880DA02E04E099EDE83361618D0175EEA_AdjustorThunk (void);
extern void AROcclusionFrameEventArgs_get_textures_m71DA887B2AF42DDB9500E82DA7A896B0199C1F74_AdjustorThunk (void);
extern void AROcclusionFrameEventArgs_set_textures_m5BB4042AD796E509530E064073A6AE707FC7DE99_AdjustorThunk (void);
extern void AROcclusionFrameEventArgs_get_propertyNameIds_m44F7274F021BE5B6E973ED4F59E13328656C5CD0_AdjustorThunk (void);
extern void AROcclusionFrameEventArgs_set_propertyNameIds_m597B2EF5AA77522294E0A6B0EE5058BECCF92B99_AdjustorThunk (void);
extern void AROcclusionFrameEventArgs_get_enabledMaterialKeywords_mD4B62FFB1EAFA72FC1EC69A533AE6B726078264E_AdjustorThunk (void);
extern void AROcclusionFrameEventArgs_set_enabledMaterialKeywords_m1F3B100DB81295D6D99FB40477E9B71A597714B1_AdjustorThunk (void);
extern void AROcclusionFrameEventArgs_get_disabledMaterialKeywords_m4348A651EED14C4FE7A9DD5E941ED496FBBFDA8A_AdjustorThunk (void);
extern void AROcclusionFrameEventArgs_set_disabledMaterialKeywords_m1857AC4F635D5122AA0C14D4DD48F6E62506EF46_AdjustorThunk (void);
extern void AROcclusionFrameEventArgs_GetHashCode_m7115792D710F0C7C0FB985857E741C600F170EF5_AdjustorThunk (void);
extern void AROcclusionFrameEventArgs_Equals_m569A3536BE31ECC807124FB95DE73677BB762C52_AdjustorThunk (void);
extern void AROcclusionFrameEventArgs_Equals_mF6121B9B23AF4C030B23B2CCD73F70597C47FE02_AdjustorThunk (void);
extern void ARParticipantsChangedEventArgs_get_added_m06DA0DBF00575BB5D41A8AFE385ADAB9866BA678_AdjustorThunk (void);
extern void ARParticipantsChangedEventArgs_set_added_m166787C372D8632EF3F075B63E1E58DF3665DE4E_AdjustorThunk (void);
extern void ARParticipantsChangedEventArgs_get_updated_mA0EC697655BDFEEB28064579FDD67DD166A2AE65_AdjustorThunk (void);
extern void ARParticipantsChangedEventArgs_set_updated_m082B2C0864682385CDDD7C89DCF8C135A1E2CBAD_AdjustorThunk (void);
extern void ARParticipantsChangedEventArgs_get_removed_m6BBF2ABF2E71DB02A70122927AAD83E3AB35A77F_AdjustorThunk (void);
extern void ARParticipantsChangedEventArgs_set_removed_mD046A770079A3D9635A832DC2A7AA4997FBCCA60_AdjustorThunk (void);
extern void ARParticipantsChangedEventArgs__ctor_m3D9C5565D3F7AB1077A197397CCAF28E0DF360D4_AdjustorThunk (void);
extern void ARParticipantsChangedEventArgs_GetHashCode_m155622653A93F1E5021158B3B506D5D04460D0C5_AdjustorThunk (void);
extern void ARParticipantsChangedEventArgs_Equals_mF5F4985298C2A01B6DA6119757ED5964BA7A1C44_AdjustorThunk (void);
extern void ARParticipantsChangedEventArgs_GetCount_mAAA0EAA40FE5210B3072EC5C8CAA435267B89FA8_AdjustorThunk (void);
extern void ARParticipantsChangedEventArgs_ToString_m6AE6DC3EE171F559B60C06C734EEF2CD39546821_AdjustorThunk (void);
extern void ARParticipantsChangedEventArgs_Equals_mD086A71DE5FE4C60061792F37BC11F17D14871C3_AdjustorThunk (void);
extern void ARPlaneBoundaryChangedEventArgs_get_plane_m4A8050E854AD2A386891D06B8695A83B59C73FDE_AdjustorThunk (void);
extern void ARPlaneBoundaryChangedEventArgs_set_plane_m297AB56CF4E77AF4AE5C4983BD69F576F5487AD7_AdjustorThunk (void);
extern void ARPlaneBoundaryChangedEventArgs__ctor_mD7B4EC2D5BB290541E54078930F949A5F9E34F1B_AdjustorThunk (void);
extern void ARPlaneBoundaryChangedEventArgs_GetHashCode_m1D4324C6E2918575F7DA49CD1FC9EBB96C3C39BC_AdjustorThunk (void);
extern void ARPlaneBoundaryChangedEventArgs_Equals_m0A539DC1276324BD525A49B913A9532301192DD1_AdjustorThunk (void);
extern void ARPlaneBoundaryChangedEventArgs_ToString_m9098FE81C630F76EE51EF044C24673846C930040_AdjustorThunk (void);
extern void ARPlaneBoundaryChangedEventArgs_Equals_m7AA9243101F5680770335ADAD9C11BE43235B861_AdjustorThunk (void);
extern void ARPlanesChangedEventArgs_get_added_m6E00DD5F0B3261BCBAA8A029924A1F3F4179C747_AdjustorThunk (void);
extern void ARPlanesChangedEventArgs_set_added_m86405667F94B610F057F9685D32DFE4D1D58AFB8_AdjustorThunk (void);
extern void ARPlanesChangedEventArgs_get_updated_mE979DAD93445C56FF7BEB732528D6CADAF0B6C22_AdjustorThunk (void);
extern void ARPlanesChangedEventArgs_set_updated_m99AB6C62358CF84214D0ABC7C55246FF04FEF4FD_AdjustorThunk (void);
extern void ARPlanesChangedEventArgs_get_removed_m8CF2AA0278488746F8C55CC6C7DC1870531D63C8_AdjustorThunk (void);
extern void ARPlanesChangedEventArgs_set_removed_mADCA5E7123CC12A874A9B95D189BA621163B7B74_AdjustorThunk (void);
extern void ARPlanesChangedEventArgs__ctor_m20AE62576EED835E5930101056E72092B75CA2F3_AdjustorThunk (void);
extern void ARPlanesChangedEventArgs_GetHashCode_m85C48FB255A115C4E1C8BE91457D6D070C14010E_AdjustorThunk (void);
extern void ARPlanesChangedEventArgs_Equals_m7BFD63AF9035113B2BEBC0ACC86E6A55FA2E9397_AdjustorThunk (void);
extern void ARPlanesChangedEventArgs_ToString_m4A51F333B5B52A6857AD19DADD92B5F437FCDFE1_AdjustorThunk (void);
extern void ARPlanesChangedEventArgs_Equals_m8AA6CA2EBB7D2C3DC8C279D77A5894AA0CCB53EB_AdjustorThunk (void);
extern void ARPointCloudChangedEventArgs_get_added_m74DFD8ED4B3EADB5AC29DD0E748C84C6FA26DDE9_AdjustorThunk (void);
extern void ARPointCloudChangedEventArgs_set_added_m3FE2F4143F780814D136E17E361046408D50C63D_AdjustorThunk (void);
extern void ARPointCloudChangedEventArgs_get_updated_mD69E67FE8B06AB44B15F42F1D6EFA91F7E58F7C6_AdjustorThunk (void);
extern void ARPointCloudChangedEventArgs_set_updated_mC936361996F351B6BF791EB3658C8F62C2210CBF_AdjustorThunk (void);
extern void ARPointCloudChangedEventArgs_get_removed_m64321D4F0D902494EE19EFF1B258981DE5C712B0_AdjustorThunk (void);
extern void ARPointCloudChangedEventArgs_set_removed_m99F84576C91F6538E21C4B33C81F9947A3A095B9_AdjustorThunk (void);
extern void ARPointCloudChangedEventArgs__ctor_m4D0FC76C4C018FCBE287313DC150D23E46295648_AdjustorThunk (void);
extern void ARPointCloudChangedEventArgs_GetHashCode_mF40A348475B5813A13FFE2C8FF2BA433F7CC9EDA_AdjustorThunk (void);
extern void ARPointCloudChangedEventArgs_Equals_m05CA0C188628022AD80F0C83A4BF9D88EC93DF85_AdjustorThunk (void);
extern void ARPointCloudChangedEventArgs_ToString_m9AB26A548169C9B39BA06A1DBAFDD29C280880DB_AdjustorThunk (void);
extern void ARPointCloudChangedEventArgs_Equals_m663B71243F5F1D3A01EF2CCA59420E82ACD796C7_AdjustorThunk (void);
extern void PointCloudRaycastJob_Execute_mCC84BA675585A86C765936270AF8EFD99C3D5C13_AdjustorThunk (void);
extern void PointCloudRaycastCollectResultsJob_Execute_m1AA868B2EEF8C3AAE8838F00B42860FC1F740A78_AdjustorThunk (void);
extern void ARPointCloudUpdatedEventArgs_GetHashCode_m53F08CA39C23B713F1918D21E6AFD78731EACC6F_AdjustorThunk (void);
extern void ARPointCloudUpdatedEventArgs_Equals_m79BA8AC909F4E622B9396930F80B1A900DEDD217_AdjustorThunk (void);
extern void ARPointCloudUpdatedEventArgs_Equals_mD3C036D4490380AE4099AAF2F4D9BA32519159F4_AdjustorThunk (void);
extern void ARRaycastHit__ctor_m0C23F16B12F6D0F71C0B00D48D8BC3271BF7F39D_AdjustorThunk (void);
extern void ARRaycastHit__ctor_m954400C9EFC7F5B5A227276ED8EE2FEF32E6BC48_AdjustorThunk (void);
extern void ARRaycastHit_get_distance_mB761B6EA13AA35393AB92EDD3A82D61659DE3126_AdjustorThunk (void);
extern void ARRaycastHit_get_hitType_m4ACAC8C59DED2EEF01C165D15136A15EBBA996F0_AdjustorThunk (void);
extern void ARRaycastHit_get_pose_m84C13E71E21FE12CBA9AAD98DC28B1E414C9EFFD_AdjustorThunk (void);
extern void ARRaycastHit_get_trackableId_m4E510F2C326AFF23086203E4241C8F9B293616C3_AdjustorThunk (void);
extern void ARRaycastHit_get_sessionRelativePose_mD06C35AA1BE0F142669BACB95F30A059A65D3DF7_AdjustorThunk (void);
extern void ARRaycastHit_get_sessionRelativeDistance_mFD19959FCA30322A0BF427FB0D9C3BD9D4464047_AdjustorThunk (void);
extern void ARRaycastHit_get_trackable_mF8D64EB03AFF2E1D5FC9B88255D2A84130B43D09_AdjustorThunk (void);
extern void ARRaycastHit_GetHashCode_mE1E5375DD029685234B765B1D0C653B048BB8D5E_AdjustorThunk (void);
extern void ARRaycastHit_Equals_m35C3DD720E50D890CA94E70DB77A01DAE0A0A1FB_AdjustorThunk (void);
extern void ARRaycastHit_Equals_m91ECCB5154E5B4B18018C00A7E71129D682DD3B1_AdjustorThunk (void);
extern void ARRaycastHit_CompareTo_m839A11B878030471B287EDA2250807A5A4F3F4D8_AdjustorThunk (void);
extern void ARRaycastUpdatedEventArgs_get_raycast_m3B8C525D8EA47F31AA2492A9797652A9C2BAE8E7_AdjustorThunk (void);
extern void ARRaycastUpdatedEventArgs_set_raycast_m54ED1343FBDD93ED1A45B0A08CCEA53489FEB1C9_AdjustorThunk (void);
extern void ARRaycastUpdatedEventArgs_Equals_mDFA0333A1E3C1F0F909561C790846DE76DD78943_AdjustorThunk (void);
extern void ARRaycastUpdatedEventArgs_Equals_mBC0B147BFAB731AEF33493D1E68B67176F869981_AdjustorThunk (void);
extern void ARRaycastUpdatedEventArgs_GetHashCode_m676A512737D735B0B6B1AAB43095845823F3B49E_AdjustorThunk (void);
extern void ARSessionStateChangedEventArgs_get_state_mC0A6FB4AF08C068BFD5D9730F60215BE662A950B_AdjustorThunk (void);
extern void ARSessionStateChangedEventArgs_set_state_mBEF6AA8214783D771EDF618B3E863D6DBC1FE8A7_AdjustorThunk (void);
extern void ARSessionStateChangedEventArgs__ctor_m1C0C6E6FD7F83932D0780826124568FCC85C888A_AdjustorThunk (void);
extern void ARSessionStateChangedEventArgs_GetHashCode_m58967DAA40E3E7088B23CB434E04B3BA745E3076_AdjustorThunk (void);
extern void ARSessionStateChangedEventArgs_Equals_mDB8C22D91659F1BEA3417585A531A31661D56FD2_AdjustorThunk (void);
extern void ARSessionStateChangedEventArgs_ToString_m1F84E487E601B1FB57C5E6F051262AA9264E66D5_AdjustorThunk (void);
extern void ARSessionStateChangedEventArgs_Equals_mD7EC44B01E9572499F4888DED9885761FCBA47BF_AdjustorThunk (void);
extern void ARTextureInfo_get_descriptor_m42C04EE9D6B420EFFC6002A044CA8B2FB6E27FE2_AdjustorThunk (void);
extern void ARTextureInfo_get_texture_m9A17329F68E159CC5C924956D4258BD26990C40A_AdjustorThunk (void);
extern void ARTextureInfo__ctor_mE3D6856CEA559DD33C81F5E993451608F7515AC1_AdjustorThunk (void);
extern void ARTextureInfo_Reset_m94C8E8348B77C4A7055ABFC20FA8E59632293A59_AdjustorThunk (void);
extern void ARTextureInfo_DestroyTexture_m4A1489A030CA1E43E61CB7F3413AC97B5F5180F9_AdjustorThunk (void);
extern void ARTextureInfo_Dispose_mF92A6E0C4D08205EDC7CF817BC866A9AB179632F_AdjustorThunk (void);
extern void ARTextureInfo_GetHashCode_m85504109E923E3CA1494548DD2C3261C1976E97A_AdjustorThunk (void);
extern void ARTextureInfo_Equals_mFB1B9B103E90F76AE0EB4E2DED235BCF3DCBF034_AdjustorThunk (void);
extern void ARTextureInfo_Equals_m020839E8607BDF4DEFEDBB125778BEFE105F563D_AdjustorThunk (void);
extern void ARTrackedImagesChangedEventArgs_get_added_m2929CC85141D13AF05C1484AFB47E043C6D3EE35_AdjustorThunk (void);
extern void ARTrackedImagesChangedEventArgs_set_added_m4E298214A1144403A84E0AE63837F2941528F18F_AdjustorThunk (void);
extern void ARTrackedImagesChangedEventArgs_get_updated_m0C896E1C21EF35FF4B31B36563838EC2BA3CDFD1_AdjustorThunk (void);
extern void ARTrackedImagesChangedEventArgs_set_updated_mBFE8B2D25F10827CC95CA76E4C52D98EFC1131BE_AdjustorThunk (void);
extern void ARTrackedImagesChangedEventArgs_get_removed_m062CBBEF163BEE47A673F7B3BDC0DD1C6EAEA185_AdjustorThunk (void);
extern void ARTrackedImagesChangedEventArgs_set_removed_mC7F5D25B8E4AD4CBBE6A43D50FAE0456BE3F928B_AdjustorThunk (void);
extern void ARTrackedImagesChangedEventArgs__ctor_m22F5D20572E4D17270B7CFBA7F0EA9445DAEE8C3_AdjustorThunk (void);
extern void ARTrackedImagesChangedEventArgs_GetHashCode_mEFBDE822EC1AA6B0E388B86ACE4043D66E23A742_AdjustorThunk (void);
extern void ARTrackedImagesChangedEventArgs_Equals_m728C2C2B10BA8C4A625C3004C83730589C86C542_AdjustorThunk (void);
extern void ARTrackedImagesChangedEventArgs_ToString_m949FF3EE96422AE2776C8FFBBD7CA342E56AE365_AdjustorThunk (void);
extern void ARTrackedImagesChangedEventArgs_Equals_m25F0396D7AA82B5E488BEA4129EE00F355EA3919_AdjustorThunk (void);
extern void ARTrackedObjectsChangedEventArgs_get_added_m71E8E2055C979657273051DE23FCEB938964169A_AdjustorThunk (void);
extern void ARTrackedObjectsChangedEventArgs_set_added_m6DE32B4C3E8C17166AC5DB4C181DE56842BD0922_AdjustorThunk (void);
extern void ARTrackedObjectsChangedEventArgs_get_updated_m01050D519E6281EECB48200D952FE1E1FFFE1A60_AdjustorThunk (void);
extern void ARTrackedObjectsChangedEventArgs_set_updated_mF2A1424732988AB85FF48E63741896BF61E94B46_AdjustorThunk (void);
extern void ARTrackedObjectsChangedEventArgs_get_removed_mDF5FA585D41D4B0D4517D3C5387B254F6EA6DFDA_AdjustorThunk (void);
extern void ARTrackedObjectsChangedEventArgs_set_removed_m58FAB3CA4AFD4D9B8D0BA1FD150C8FBF06D30F4E_AdjustorThunk (void);
extern void ARTrackedObjectsChangedEventArgs__ctor_mED9F9F2F2307AC78C3580327A6501F5E7ED5060A_AdjustorThunk (void);
extern void ARTrackedObjectsChangedEventArgs_GetHashCode_mC0D8DBBE030D0F00D1D0662C2C100FADF4EC429A_AdjustorThunk (void);
extern void ARTrackedObjectsChangedEventArgs_Equals_mB6CEB7818A761BB4B5009A280C058522176E7641_AdjustorThunk (void);
extern void ARTrackedObjectsChangedEventArgs_ToString_m1E23EE412E0E27AE4186B56B83F4EF1F12FAD5A7_AdjustorThunk (void);
extern void ARTrackedObjectsChangedEventArgs_Equals_m822193D19946082B0F5D17078ABD169B4A002EDE_AdjustorThunk (void);
extern void Message_WithMessage_m6D980DF1AD1190D26E45B88C3600AF4226FDD63C_AdjustorThunk (void);
extern void Message_WithMessage_m7331F24D7470335EC2EEF8FBA56A8D570ACC8FD3_AdjustorThunk (void);
extern void DeallocateJob_Execute_mECC71B15D80D3951E0D0B4B0B7B28A50DBC81D6F_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[232] = 
{
	{ 0x06000017, ARAnchorsChangedEventArgs_get_added_m5F9CEB4587F9496D0DA28880430B69EFDB550600_AdjustorThunk },
	{ 0x06000018, ARAnchorsChangedEventArgs_set_added_mEE4383EBEAFBE249A99CEAF1F0EA6FDB964BAFEB_AdjustorThunk },
	{ 0x06000019, ARAnchorsChangedEventArgs_get_updated_m21039F14E472A75F969EE0BA5CBA9007DC12B593_AdjustorThunk },
	{ 0x0600001A, ARAnchorsChangedEventArgs_set_updated_mDA509077951F3AE2F2DC7300937CA5C626BABEF4_AdjustorThunk },
	{ 0x0600001B, ARAnchorsChangedEventArgs_get_removed_mF1E9D61E21153018C87DE99722FF8820C351CFD8_AdjustorThunk },
	{ 0x0600001C, ARAnchorsChangedEventArgs_set_removed_m18D9FF865947D6363BA0989B783B2CC746A7B867_AdjustorThunk },
	{ 0x0600001D, ARAnchorsChangedEventArgs__ctor_m67B444466F34F943C140F8F7EC4959DC1FFDDB8A_AdjustorThunk },
	{ 0x0600001E, ARAnchorsChangedEventArgs_GetHashCode_m278A649D0606054A9B7E7C8AFF08CC99C2685024_AdjustorThunk },
	{ 0x0600001F, ARAnchorsChangedEventArgs_Equals_mC2C108ADF4C9783BD7470E6573196978E61FD49B_AdjustorThunk },
	{ 0x06000020, ARAnchorsChangedEventArgs_ToString_mA0C838C2F9AB2B51089A47F4CF0E7049F8A86BAC_AdjustorThunk },
	{ 0x06000021, ARAnchorsChangedEventArgs_Equals_m67E2B32F8DEAB6F7C31BF818E1F42E799B3A0DAF_AdjustorThunk },
	{ 0x0600004C, ARCameraFrameEventArgs_get_lightEstimation_mDAD20A000D180FB372E8214B37A8A4EB4F0F312C_AdjustorThunk },
	{ 0x0600004D, ARCameraFrameEventArgs_set_lightEstimation_mE04691D14C2CD985D757C11AA126AE60BC6DBB51_AdjustorThunk },
	{ 0x0600004E, ARCameraFrameEventArgs_get_timestampNs_m3B9E21BF0B36F035E105ED2B155983E03B21F4E9_AdjustorThunk },
	{ 0x0600004F, ARCameraFrameEventArgs_set_timestampNs_mF3DE3C39790E92B6F2190BEE8DA69568C8A47F20_AdjustorThunk },
	{ 0x06000050, ARCameraFrameEventArgs_get_projectionMatrix_mA09F2170ACA84AEC8E9407DE70B9CD1D2E443182_AdjustorThunk },
	{ 0x06000051, ARCameraFrameEventArgs_set_projectionMatrix_m7F7C0173EAC5E149E6A4A6FA9FA85E8C071DF517_AdjustorThunk },
	{ 0x06000052, ARCameraFrameEventArgs_get_displayMatrix_m3354FFAEF054F0A8F4D19BFD506468A06F3AD65F_AdjustorThunk },
	{ 0x06000053, ARCameraFrameEventArgs_set_displayMatrix_m4DB301FFAEAC7277FD816737053A2688B9CDE902_AdjustorThunk },
	{ 0x06000054, ARCameraFrameEventArgs_get_textures_m96823876351BD11C26D78BAA01936FF751182547_AdjustorThunk },
	{ 0x06000055, ARCameraFrameEventArgs_set_textures_mB9F3D579FB8DA3BDF3F4B85D1E598AE19959BAC7_AdjustorThunk },
	{ 0x06000056, ARCameraFrameEventArgs_get_propertyNameIds_m9B76BC52DB8349A38D5514528C63A36494283798_AdjustorThunk },
	{ 0x06000057, ARCameraFrameEventArgs_set_propertyNameIds_m7F4470E1C46E52045645D6C93923F6DB7A92C6B5_AdjustorThunk },
	{ 0x06000058, ARCameraFrameEventArgs_get_exposureDuration_mC4D1A5D0266949EDB98A74E66564E421A9B2A2F8_AdjustorThunk },
	{ 0x06000059, ARCameraFrameEventArgs_set_exposureDuration_m48C0FBF3D428A1507BB522D23B2A383B265F4B1B_AdjustorThunk },
	{ 0x0600005A, ARCameraFrameEventArgs_get_exposureOffset_mFDCFBB779CF302DF2159000D328826E7880FDD0F_AdjustorThunk },
	{ 0x0600005B, ARCameraFrameEventArgs_set_exposureOffset_m82EEA2BFFE750731718B4B39FBCA59BC0B4CD2EE_AdjustorThunk },
	{ 0x0600005C, ARCameraFrameEventArgs_get_enabledMaterialKeywords_m76A2E83147560A72FF3834BF0FB7C1B3FEED6240_AdjustorThunk },
	{ 0x0600005D, ARCameraFrameEventArgs_set_enabledMaterialKeywords_mE8FD88DCAEFA52B0B6E05DA72F61AA7727D88E9D_AdjustorThunk },
	{ 0x0600005E, ARCameraFrameEventArgs_get_disabledMaterialKeywords_m254ACC80879CD8B270566B38CCD6ED99D26255A8_AdjustorThunk },
	{ 0x0600005F, ARCameraFrameEventArgs_set_disabledMaterialKeywords_m95D9308AC9AA63B459F87C44AD1B378043C08BDA_AdjustorThunk },
	{ 0x06000060, ARCameraFrameEventArgs_get_cameraGrainTexture_m0CEACD5C125C07A7D6E73695808202940300B633_AdjustorThunk },
	{ 0x06000061, ARCameraFrameEventArgs_set_cameraGrainTexture_m2BBDCEB456D16E1AD14D74874CD3F274806F1D61_AdjustorThunk },
	{ 0x06000062, ARCameraFrameEventArgs_get_noiseIntensity_m441B0EFBF898D8A18AB1E102E41CF1BB33072591_AdjustorThunk },
	{ 0x06000063, ARCameraFrameEventArgs_set_noiseIntensity_m8AA71321684DA61B4D83D0C2F91C85AA9A2FEE7E_AdjustorThunk },
	{ 0x06000064, ARCameraFrameEventArgs_get_exifData_mDB0728F64EE619CB4174644B6D58598DAE8EC2F5_AdjustorThunk },
	{ 0x06000065, ARCameraFrameEventArgs_set_exifData_m1FB5103D69EEE478654B39783FCD7D662F557E55_AdjustorThunk },
	{ 0x06000066, ARCameraFrameEventArgs_TryGetExifData_m6AD832BCB4867C3D42D03A0CB6B291BAE259B3E0_AdjustorThunk },
	{ 0x06000067, ARCameraFrameEventArgs_GetHashCode_m13E6D8C9B47ACA833C0D2F23C838883FEF4A464F_AdjustorThunk },
	{ 0x06000068, ARCameraFrameEventArgs_Equals_m896C0CD5FC48A354B4AE1CA23F7219FD812873CF_AdjustorThunk },
	{ 0x06000069, ARCameraFrameEventArgs_ToString_m31AA489F1C81FA10709305176CE7EE2676F28CD8_AdjustorThunk },
	{ 0x0600006A, ARCameraFrameEventArgs_Equals_mE86A86B648BFB94A2CB70BE472F974C550AB6CC6_AdjustorThunk },
	{ 0x0600012C, AREnvironmentProbesChangedEvent_get_added_m5EB8CAE059E5A0F0A84A11866F2343F630B7C997_AdjustorThunk },
	{ 0x0600012D, AREnvironmentProbesChangedEvent_set_added_mD254DC5C2E0B0AC8D0F604829177E92AAA36DB98_AdjustorThunk },
	{ 0x0600012E, AREnvironmentProbesChangedEvent_get_updated_mFFE292486A4D2DB4385C70FAE94EB6048CBB99F3_AdjustorThunk },
	{ 0x0600012F, AREnvironmentProbesChangedEvent_set_updated_m8346F2C232C68965428428BFC1D42EF188390D27_AdjustorThunk },
	{ 0x06000130, AREnvironmentProbesChangedEvent_get_removed_m0D5B42BCAD8408B1FC0DF82E24C4EAF1D8ADF40A_AdjustorThunk },
	{ 0x06000131, AREnvironmentProbesChangedEvent_set_removed_m1C122637075CAC8EE8D30678068F20C10F9805B8_AdjustorThunk },
	{ 0x06000132, AREnvironmentProbesChangedEvent__ctor_mA834200E5CC7AEB6D93ED5B987F19D24A9C82944_AdjustorThunk },
	{ 0x06000133, AREnvironmentProbesChangedEvent_GetHashCode_m1F82F2279F41834A46A159D45D9FA4C37ABDEFF6_AdjustorThunk },
	{ 0x06000134, AREnvironmentProbesChangedEvent_Equals_m5DC12B1B83E64882261F789A0651ADA40D58D7C3_AdjustorThunk },
	{ 0x06000135, AREnvironmentProbesChangedEvent_ToString_m66B3C7111A9054FAB69BABF82CF0789974FBA048_AdjustorThunk },
	{ 0x06000136, AREnvironmentProbesChangedEvent_Equals_mAF37E53C6392E0457299A76EE7CC5D805BDF4D64_AdjustorThunk },
	{ 0x0600016A, ARFacesChangedEventArgs_get_added_m8EA1424F8F262955AFD00664B81C15AB54EFA213_AdjustorThunk },
	{ 0x0600016B, ARFacesChangedEventArgs_set_added_mF3FF69CFE03334FC9582BFF06D5124961F955D43_AdjustorThunk },
	{ 0x0600016C, ARFacesChangedEventArgs_get_updated_m1D3587AA05E5789F796A71C3C87CD6D11BE0005B_AdjustorThunk },
	{ 0x0600016D, ARFacesChangedEventArgs_set_updated_mAD5B3FD1E0A6058BD6328793BA0505E82B476469_AdjustorThunk },
	{ 0x0600016E, ARFacesChangedEventArgs_get_removed_mC31806DE526D3BECAA26C35DAE321F2DE77C335E_AdjustorThunk },
	{ 0x0600016F, ARFacesChangedEventArgs_set_removed_m0A6A558713C45E20F0040C50763F01B1E061E387_AdjustorThunk },
	{ 0x06000170, ARFacesChangedEventArgs__ctor_m4C7990AA3703F850C5B4D0CCCCEABBC0B58DB940_AdjustorThunk },
	{ 0x06000171, ARFacesChangedEventArgs_GetHashCode_m67DD1456E147A37E35FF2483BC7053D59B549CF6_AdjustorThunk },
	{ 0x06000172, ARFacesChangedEventArgs_Equals_m08B2E52D4FD329CAB89DC14BEF84E55274E2EAEB_AdjustorThunk },
	{ 0x06000173, ARFacesChangedEventArgs_ToString_m0773324E2DC3392C12286F68495590B2F1E7C057_AdjustorThunk },
	{ 0x06000174, ARFacesChangedEventArgs_Equals_m32B401EBB3F1CFE364F4F2C5B7746F8D48B1DDDE_AdjustorThunk },
	{ 0x06000177, ARFaceUpdatedEventArgs_get_face_mFD21EF59457212494C80FE998D1CA8AC0F057170_AdjustorThunk },
	{ 0x06000178, ARFaceUpdatedEventArgs_set_face_m0E59FF874A145B994A77250CEA675FBF8AD3019C_AdjustorThunk },
	{ 0x06000179, ARFaceUpdatedEventArgs__ctor_m3D4294A61EBEAF3158E5C90C57FB112020A58987_AdjustorThunk },
	{ 0x0600017A, ARFaceUpdatedEventArgs_GetHashCode_mCF1AF45853FADD6B4F52B43038647782B888B700_AdjustorThunk },
	{ 0x0600017B, ARFaceUpdatedEventArgs_Equals_m24F398E98F52EBA0DF689C392CF58291742028F9_AdjustorThunk },
	{ 0x0600017C, ARFaceUpdatedEventArgs_Equals_m50565F99C9CC4FF2F3122EF78D2C4DE1881E1BDC_AdjustorThunk },
	{ 0x0600017F, ARHumanBodiesChangedEventArgs_get_added_m6FD862CC8C8281F9377D754BBAB7F3C616955D4C_AdjustorThunk },
	{ 0x06000180, ARHumanBodiesChangedEventArgs_set_added_mAC9FB746BD1745CB5BEDE7C2220302035CD63F77_AdjustorThunk },
	{ 0x06000181, ARHumanBodiesChangedEventArgs_get_updated_mA2FD128E22BDACD09294F107AA3F2A91CFFAA550_AdjustorThunk },
	{ 0x06000182, ARHumanBodiesChangedEventArgs_set_updated_mB71FDE70AD3CFC1096386A5E4260A9FFBB420C06_AdjustorThunk },
	{ 0x06000183, ARHumanBodiesChangedEventArgs_get_removed_m613516A989AA48A625C79A3C309D9C1465762BCA_AdjustorThunk },
	{ 0x06000184, ARHumanBodiesChangedEventArgs_set_removed_m97AC3D82086A3691484D3B587A534B9D6DC385B1_AdjustorThunk },
	{ 0x06000185, ARHumanBodiesChangedEventArgs__ctor_mA364D7E41518A8059F90E1EFC71FDD9F5F52B7F7_AdjustorThunk },
	{ 0x06000186, ARHumanBodiesChangedEventArgs_GetHashCode_m7C9E78E03AE212095DDA1B44594F64475937253F_AdjustorThunk },
	{ 0x06000187, ARHumanBodiesChangedEventArgs_Equals_m0D61EF468E803F4B69514B78E7E47852B9AC39AF_AdjustorThunk },
	{ 0x06000188, ARHumanBodiesChangedEventArgs_Equals_m2E24676DA642BE3E29F0347454BC4CB993DCF8B4_AdjustorThunk },
	{ 0x0600018B, ARHumanBodiesChangedEventArgs_ToString_m4685EF40034A2625C670EA5813E73ED415B35431_AdjustorThunk },
	{ 0x060001B9, ARLightEstimationData_get_averageBrightness_mCDE95FB42D807C168E187942BD9DDAB65439AE19_AdjustorThunk },
	{ 0x060001BA, ARLightEstimationData_set_averageBrightness_mC057F484D1EA8E3760977FE33297539B665D95CE_AdjustorThunk },
	{ 0x060001BB, ARLightEstimationData_get_averageColorTemperature_m688BB1F18E15D7058FDEFC012451A72CD6D193DC_AdjustorThunk },
	{ 0x060001BC, ARLightEstimationData_set_averageColorTemperature_m2B62048E88C65A904843D4A46BDAC8F764519B79_AdjustorThunk },
	{ 0x060001BD, ARLightEstimationData_get_colorCorrection_mF7D9D83F249587E6A1E18D845C0D521C907DF496_AdjustorThunk },
	{ 0x060001BE, ARLightEstimationData_set_colorCorrection_mD28E53EA20EE633541E1F9AFBB853A10AF3361BA_AdjustorThunk },
	{ 0x060001BF, ARLightEstimationData_get_averageIntensityInLumens_m3D74CD050CDA61A97595AF8854E97C3239F159D1_AdjustorThunk },
	{ 0x060001C0, ARLightEstimationData_set_averageIntensityInLumens_m79B3A12B384470DCCCCEE390DE5ECCC0E1CCCE5E_AdjustorThunk },
	{ 0x060001C1, ARLightEstimationData_get_mainLightIntensityLumens_m618D7C8A4FA8FFC3AB370DFC00596DDFA592288F_AdjustorThunk },
	{ 0x060001C2, ARLightEstimationData_set_mainLightIntensityLumens_m4B7F856E88E7F8247B61EFEA6231720A23340B42_AdjustorThunk },
	{ 0x060001C3, ARLightEstimationData_get_averageMainLightBrightness_mAA37F537DB4589CB01464B6A7E9AA8AF2C6E693F_AdjustorThunk },
	{ 0x060001C4, ARLightEstimationData_set_averageMainLightBrightness_mCE3727F3F32B8EB41503E852CFBCC20898B50D93_AdjustorThunk },
	{ 0x060001C5, ARLightEstimationData_get_mainLightColor_m7A17F97AC5EBEE6B3E3B093AB73F8FB904C79C3E_AdjustorThunk },
	{ 0x060001C6, ARLightEstimationData_set_mainLightColor_m80FE4C8781175687581668E3708A899C15F278FE_AdjustorThunk },
	{ 0x060001C7, ARLightEstimationData_get_mainLightDirection_m49A1B8B5DB38EAE356A22C1AD0A4FD671C6C1427_AdjustorThunk },
	{ 0x060001C8, ARLightEstimationData_set_mainLightDirection_m701A092104413621AC8320050422D4A0C94BE57C_AdjustorThunk },
	{ 0x060001C9, ARLightEstimationData_get_ambientSphericalHarmonics_m34C8C4BB5EFDD7E2DAEDF983F11031ABFCA16C41_AdjustorThunk },
	{ 0x060001CA, ARLightEstimationData_set_ambientSphericalHarmonics_m4DF86D65B2095ADB18B9504CC23518600DDA2D9B_AdjustorThunk },
	{ 0x060001CB, ARLightEstimationData_GetHashCode_m3F60EB276A24D0B1D3D7B74F0C9CBE044CC49595_AdjustorThunk },
	{ 0x060001CC, ARLightEstimationData_Equals_mD33829136230F81948FE58D2EB7A32E53973AD02_AdjustorThunk },
	{ 0x060001CD, ARLightEstimationData_ToString_m57D70F82DD05F504BB4DC8937BAE5E9B0CE686FA_AdjustorThunk },
	{ 0x060001CE, ARLightEstimationData_Equals_m91813FC80F85190AC70FCE2827D4844F15A1BB65_AdjustorThunk },
	{ 0x060001D1, ARLightEstimationData_ConvertBrightnessToLumens_m09279642CD4DFF335987CE9212C0F1E82F4CA4A2_AdjustorThunk },
	{ 0x060001D2, ARLightEstimationData_ConvertLumensToBrightness_mDEC789CC752A2A51FBBB1C8B2C27CB6F0C615D35_AdjustorThunk },
	{ 0x060001D3, ARMeshesChangedEventArgs_get_added_m0D451C7DDD6CE9B7697CF3678D2F1DE440170E6B_AdjustorThunk },
	{ 0x060001D4, ARMeshesChangedEventArgs_set_added_m6A461DFF63F7E28D7A4B02AD47629D95BBAC20B6_AdjustorThunk },
	{ 0x060001D5, ARMeshesChangedEventArgs_get_updated_m06E6CE944759C31B7500D0A4EF8D07D262A0626F_AdjustorThunk },
	{ 0x060001D6, ARMeshesChangedEventArgs_set_updated_m60115D846577E418E88D4D273AD1A2ECE1D1BAAE_AdjustorThunk },
	{ 0x060001D7, ARMeshesChangedEventArgs_get_removed_mB982A0629BEA14EC069263AD7BAB6C43CB187674_AdjustorThunk },
	{ 0x060001D8, ARMeshesChangedEventArgs_set_removed_mA6932589C03B43D48CB7DC1F7AA627BD85F6E396_AdjustorThunk },
	{ 0x060001D9, ARMeshesChangedEventArgs__ctor_m3265D79A775738B64ADFBA5B52C89DCC77B26F2B_AdjustorThunk },
	{ 0x060001DA, ARMeshesChangedEventArgs_GetHashCode_m43CF0D8A78557CE5A2E097DF4DED87531AE07F5E_AdjustorThunk },
	{ 0x060001DB, ARMeshesChangedEventArgs_Equals_m077E07D6856F294A374A0EFE0E38DCB958ADAAFC_AdjustorThunk },
	{ 0x060001DC, ARMeshesChangedEventArgs_ToString_m9249BC2AF8169516627B851472F3DAFA6F067879_AdjustorThunk },
	{ 0x060001DD, ARMeshesChangedEventArgs_Equals_mAC06257880DA02E04E099EDE83361618D0175EEA_AdjustorThunk },
	{ 0x06000209, AROcclusionFrameEventArgs_get_textures_m71DA887B2AF42DDB9500E82DA7A896B0199C1F74_AdjustorThunk },
	{ 0x0600020A, AROcclusionFrameEventArgs_set_textures_m5BB4042AD796E509530E064073A6AE707FC7DE99_AdjustorThunk },
	{ 0x0600020B, AROcclusionFrameEventArgs_get_propertyNameIds_m44F7274F021BE5B6E973ED4F59E13328656C5CD0_AdjustorThunk },
	{ 0x0600020C, AROcclusionFrameEventArgs_set_propertyNameIds_m597B2EF5AA77522294E0A6B0EE5058BECCF92B99_AdjustorThunk },
	{ 0x0600020D, AROcclusionFrameEventArgs_get_enabledMaterialKeywords_mD4B62FFB1EAFA72FC1EC69A533AE6B726078264E_AdjustorThunk },
	{ 0x0600020E, AROcclusionFrameEventArgs_set_enabledMaterialKeywords_m1F3B100DB81295D6D99FB40477E9B71A597714B1_AdjustorThunk },
	{ 0x0600020F, AROcclusionFrameEventArgs_get_disabledMaterialKeywords_m4348A651EED14C4FE7A9DD5E941ED496FBBFDA8A_AdjustorThunk },
	{ 0x06000210, AROcclusionFrameEventArgs_set_disabledMaterialKeywords_m1857AC4F635D5122AA0C14D4DD48F6E62506EF46_AdjustorThunk },
	{ 0x06000211, AROcclusionFrameEventArgs_GetHashCode_m7115792D710F0C7C0FB985857E741C600F170EF5_AdjustorThunk },
	{ 0x06000212, AROcclusionFrameEventArgs_Equals_m569A3536BE31ECC807124FB95DE73677BB762C52_AdjustorThunk },
	{ 0x06000213, AROcclusionFrameEventArgs_Equals_mF6121B9B23AF4C030B23B2CCD73F70597C47FE02_AdjustorThunk },
	{ 0x06000248, ARParticipantsChangedEventArgs_get_added_m06DA0DBF00575BB5D41A8AFE385ADAB9866BA678_AdjustorThunk },
	{ 0x06000249, ARParticipantsChangedEventArgs_set_added_m166787C372D8632EF3F075B63E1E58DF3665DE4E_AdjustorThunk },
	{ 0x0600024A, ARParticipantsChangedEventArgs_get_updated_mA0EC697655BDFEEB28064579FDD67DD166A2AE65_AdjustorThunk },
	{ 0x0600024B, ARParticipantsChangedEventArgs_set_updated_m082B2C0864682385CDDD7C89DCF8C135A1E2CBAD_AdjustorThunk },
	{ 0x0600024C, ARParticipantsChangedEventArgs_get_removed_m6BBF2ABF2E71DB02A70122927AAD83E3AB35A77F_AdjustorThunk },
	{ 0x0600024D, ARParticipantsChangedEventArgs_set_removed_mD046A770079A3D9635A832DC2A7AA4997FBCCA60_AdjustorThunk },
	{ 0x0600024E, ARParticipantsChangedEventArgs__ctor_m3D9C5565D3F7AB1077A197397CCAF28E0DF360D4_AdjustorThunk },
	{ 0x0600024F, ARParticipantsChangedEventArgs_GetHashCode_m155622653A93F1E5021158B3B506D5D04460D0C5_AdjustorThunk },
	{ 0x06000250, ARParticipantsChangedEventArgs_Equals_mF5F4985298C2A01B6DA6119757ED5964BA7A1C44_AdjustorThunk },
	{ 0x06000251, ARParticipantsChangedEventArgs_GetCount_mAAA0EAA40FE5210B3072EC5C8CAA435267B89FA8_AdjustorThunk },
	{ 0x06000252, ARParticipantsChangedEventArgs_ToString_m6AE6DC3EE171F559B60C06C734EEF2CD39546821_AdjustorThunk },
	{ 0x06000253, ARParticipantsChangedEventArgs_Equals_mD086A71DE5FE4C60061792F37BC11F17D14871C3_AdjustorThunk },
	{ 0x0600026E, ARPlaneBoundaryChangedEventArgs_get_plane_m4A8050E854AD2A386891D06B8695A83B59C73FDE_AdjustorThunk },
	{ 0x0600026F, ARPlaneBoundaryChangedEventArgs_set_plane_m297AB56CF4E77AF4AE5C4983BD69F576F5487AD7_AdjustorThunk },
	{ 0x06000270, ARPlaneBoundaryChangedEventArgs__ctor_mD7B4EC2D5BB290541E54078930F949A5F9E34F1B_AdjustorThunk },
	{ 0x06000271, ARPlaneBoundaryChangedEventArgs_GetHashCode_m1D4324C6E2918575F7DA49CD1FC9EBB96C3C39BC_AdjustorThunk },
	{ 0x06000272, ARPlaneBoundaryChangedEventArgs_Equals_m0A539DC1276324BD525A49B913A9532301192DD1_AdjustorThunk },
	{ 0x06000273, ARPlaneBoundaryChangedEventArgs_ToString_m9098FE81C630F76EE51EF044C24673846C930040_AdjustorThunk },
	{ 0x06000274, ARPlaneBoundaryChangedEventArgs_Equals_m7AA9243101F5680770335ADAD9C11BE43235B861_AdjustorThunk },
	{ 0x060002A0, ARPlanesChangedEventArgs_get_added_m6E00DD5F0B3261BCBAA8A029924A1F3F4179C747_AdjustorThunk },
	{ 0x060002A1, ARPlanesChangedEventArgs_set_added_m86405667F94B610F057F9685D32DFE4D1D58AFB8_AdjustorThunk },
	{ 0x060002A2, ARPlanesChangedEventArgs_get_updated_mE979DAD93445C56FF7BEB732528D6CADAF0B6C22_AdjustorThunk },
	{ 0x060002A3, ARPlanesChangedEventArgs_set_updated_m99AB6C62358CF84214D0ABC7C55246FF04FEF4FD_AdjustorThunk },
	{ 0x060002A4, ARPlanesChangedEventArgs_get_removed_m8CF2AA0278488746F8C55CC6C7DC1870531D63C8_AdjustorThunk },
	{ 0x060002A5, ARPlanesChangedEventArgs_set_removed_mADCA5E7123CC12A874A9B95D189BA621163B7B74_AdjustorThunk },
	{ 0x060002A6, ARPlanesChangedEventArgs__ctor_m20AE62576EED835E5930101056E72092B75CA2F3_AdjustorThunk },
	{ 0x060002A7, ARPlanesChangedEventArgs_GetHashCode_m85C48FB255A115C4E1C8BE91457D6D070C14010E_AdjustorThunk },
	{ 0x060002A8, ARPlanesChangedEventArgs_Equals_m7BFD63AF9035113B2BEBC0ACC86E6A55FA2E9397_AdjustorThunk },
	{ 0x060002A9, ARPlanesChangedEventArgs_ToString_m4A51F333B5B52A6857AD19DADD92B5F437FCDFE1_AdjustorThunk },
	{ 0x060002AA, ARPlanesChangedEventArgs_Equals_m8AA6CA2EBB7D2C3DC8C279D77A5894AA0CCB53EB_AdjustorThunk },
	{ 0x060002B7, ARPointCloudChangedEventArgs_get_added_m74DFD8ED4B3EADB5AC29DD0E748C84C6FA26DDE9_AdjustorThunk },
	{ 0x060002B8, ARPointCloudChangedEventArgs_set_added_m3FE2F4143F780814D136E17E361046408D50C63D_AdjustorThunk },
	{ 0x060002B9, ARPointCloudChangedEventArgs_get_updated_mD69E67FE8B06AB44B15F42F1D6EFA91F7E58F7C6_AdjustorThunk },
	{ 0x060002BA, ARPointCloudChangedEventArgs_set_updated_mC936361996F351B6BF791EB3658C8F62C2210CBF_AdjustorThunk },
	{ 0x060002BB, ARPointCloudChangedEventArgs_get_removed_m64321D4F0D902494EE19EFF1B258981DE5C712B0_AdjustorThunk },
	{ 0x060002BC, ARPointCloudChangedEventArgs_set_removed_m99F84576C91F6538E21C4B33C81F9947A3A095B9_AdjustorThunk },
	{ 0x060002BD, ARPointCloudChangedEventArgs__ctor_m4D0FC76C4C018FCBE287313DC150D23E46295648_AdjustorThunk },
	{ 0x060002BE, ARPointCloudChangedEventArgs_GetHashCode_mF40A348475B5813A13FFE2C8FF2BA433F7CC9EDA_AdjustorThunk },
	{ 0x060002BF, ARPointCloudChangedEventArgs_Equals_m05CA0C188628022AD80F0C83A4BF9D88EC93DF85_AdjustorThunk },
	{ 0x060002C0, ARPointCloudChangedEventArgs_ToString_m9AB26A548169C9B39BA06A1DBAFDD29C280880DB_AdjustorThunk },
	{ 0x060002C1, ARPointCloudChangedEventArgs_Equals_m663B71243F5F1D3A01EF2CCA59420E82ACD796C7_AdjustorThunk },
	{ 0x060002D1, PointCloudRaycastJob_Execute_mCC84BA675585A86C765936270AF8EFD99C3D5C13_AdjustorThunk },
	{ 0x060002D2, PointCloudRaycastCollectResultsJob_Execute_m1AA868B2EEF8C3AAE8838F00B42860FC1F740A78_AdjustorThunk },
	{ 0x060002E7, ARPointCloudUpdatedEventArgs_GetHashCode_m53F08CA39C23B713F1918D21E6AFD78731EACC6F_AdjustorThunk },
	{ 0x060002E8, ARPointCloudUpdatedEventArgs_Equals_m79BA8AC909F4E622B9396930F80B1A900DEDD217_AdjustorThunk },
	{ 0x060002E9, ARPointCloudUpdatedEventArgs_Equals_mD3C036D4490380AE4099AAF2F4D9BA32519159F4_AdjustorThunk },
	{ 0x060002FE, ARRaycastHit__ctor_m0C23F16B12F6D0F71C0B00D48D8BC3271BF7F39D_AdjustorThunk },
	{ 0x060002FF, ARRaycastHit__ctor_m954400C9EFC7F5B5A227276ED8EE2FEF32E6BC48_AdjustorThunk },
	{ 0x06000300, ARRaycastHit_get_distance_mB761B6EA13AA35393AB92EDD3A82D61659DE3126_AdjustorThunk },
	{ 0x06000301, ARRaycastHit_get_hitType_m4ACAC8C59DED2EEF01C165D15136A15EBBA996F0_AdjustorThunk },
	{ 0x06000302, ARRaycastHit_get_pose_m84C13E71E21FE12CBA9AAD98DC28B1E414C9EFFD_AdjustorThunk },
	{ 0x06000303, ARRaycastHit_get_trackableId_m4E510F2C326AFF23086203E4241C8F9B293616C3_AdjustorThunk },
	{ 0x06000304, ARRaycastHit_get_sessionRelativePose_mD06C35AA1BE0F142669BACB95F30A059A65D3DF7_AdjustorThunk },
	{ 0x06000305, ARRaycastHit_get_sessionRelativeDistance_mFD19959FCA30322A0BF427FB0D9C3BD9D4464047_AdjustorThunk },
	{ 0x06000306, ARRaycastHit_get_trackable_mF8D64EB03AFF2E1D5FC9B88255D2A84130B43D09_AdjustorThunk },
	{ 0x06000307, ARRaycastHit_GetHashCode_mE1E5375DD029685234B765B1D0C653B048BB8D5E_AdjustorThunk },
	{ 0x06000308, ARRaycastHit_Equals_m35C3DD720E50D890CA94E70DB77A01DAE0A0A1FB_AdjustorThunk },
	{ 0x06000309, ARRaycastHit_Equals_m91ECCB5154E5B4B18018C00A7E71129D682DD3B1_AdjustorThunk },
	{ 0x0600030C, ARRaycastHit_CompareTo_m839A11B878030471B287EDA2250807A5A4F3F4D8_AdjustorThunk },
	{ 0x06000322, ARRaycastUpdatedEventArgs_get_raycast_m3B8C525D8EA47F31AA2492A9797652A9C2BAE8E7_AdjustorThunk },
	{ 0x06000323, ARRaycastUpdatedEventArgs_set_raycast_m54ED1343FBDD93ED1A45B0A08CCEA53489FEB1C9_AdjustorThunk },
	{ 0x06000324, ARRaycastUpdatedEventArgs_Equals_mDFA0333A1E3C1F0F909561C790846DE76DD78943_AdjustorThunk },
	{ 0x06000325, ARRaycastUpdatedEventArgs_Equals_mBC0B147BFAB731AEF33493D1E68B67176F869981_AdjustorThunk },
	{ 0x06000326, ARRaycastUpdatedEventArgs_GetHashCode_m676A512737D735B0B6B1AAB43095845823F3B49E_AdjustorThunk },
	{ 0x0600035F, ARSessionStateChangedEventArgs_get_state_mC0A6FB4AF08C068BFD5D9730F60215BE662A950B_AdjustorThunk },
	{ 0x06000360, ARSessionStateChangedEventArgs_set_state_mBEF6AA8214783D771EDF618B3E863D6DBC1FE8A7_AdjustorThunk },
	{ 0x06000361, ARSessionStateChangedEventArgs__ctor_m1C0C6E6FD7F83932D0780826124568FCC85C888A_AdjustorThunk },
	{ 0x06000362, ARSessionStateChangedEventArgs_GetHashCode_m58967DAA40E3E7088B23CB434E04B3BA745E3076_AdjustorThunk },
	{ 0x06000363, ARSessionStateChangedEventArgs_Equals_mDB8C22D91659F1BEA3417585A531A31661D56FD2_AdjustorThunk },
	{ 0x06000364, ARSessionStateChangedEventArgs_ToString_m1F84E487E601B1FB57C5E6F051262AA9264E66D5_AdjustorThunk },
	{ 0x06000365, ARSessionStateChangedEventArgs_Equals_mD7EC44B01E9572499F4888DED9885761FCBA47BF_AdjustorThunk },
	{ 0x06000368, ARTextureInfo_get_descriptor_m42C04EE9D6B420EFFC6002A044CA8B2FB6E27FE2_AdjustorThunk },
	{ 0x06000369, ARTextureInfo_get_texture_m9A17329F68E159CC5C924956D4258BD26990C40A_AdjustorThunk },
	{ 0x0600036A, ARTextureInfo__ctor_mE3D6856CEA559DD33C81F5E993451608F7515AC1_AdjustorThunk },
	{ 0x0600036B, ARTextureInfo_Reset_m94C8E8348B77C4A7055ABFC20FA8E59632293A59_AdjustorThunk },
	{ 0x0600036C, ARTextureInfo_DestroyTexture_m4A1489A030CA1E43E61CB7F3413AC97B5F5180F9_AdjustorThunk },
	{ 0x06000370, ARTextureInfo_Dispose_mF92A6E0C4D08205EDC7CF817BC866A9AB179632F_AdjustorThunk },
	{ 0x06000371, ARTextureInfo_GetHashCode_m85504109E923E3CA1494548DD2C3261C1976E97A_AdjustorThunk },
	{ 0x06000372, ARTextureInfo_Equals_mFB1B9B103E90F76AE0EB4E2DED235BCF3DCBF034_AdjustorThunk },
	{ 0x06000373, ARTextureInfo_Equals_m020839E8607BDF4DEFEDBB125778BEFE105F563D_AdjustorThunk },
	{ 0x060003BF, ARTrackedImagesChangedEventArgs_get_added_m2929CC85141D13AF05C1484AFB47E043C6D3EE35_AdjustorThunk },
	{ 0x060003C0, ARTrackedImagesChangedEventArgs_set_added_m4E298214A1144403A84E0AE63837F2941528F18F_AdjustorThunk },
	{ 0x060003C1, ARTrackedImagesChangedEventArgs_get_updated_m0C896E1C21EF35FF4B31B36563838EC2BA3CDFD1_AdjustorThunk },
	{ 0x060003C2, ARTrackedImagesChangedEventArgs_set_updated_mBFE8B2D25F10827CC95CA76E4C52D98EFC1131BE_AdjustorThunk },
	{ 0x060003C3, ARTrackedImagesChangedEventArgs_get_removed_m062CBBEF163BEE47A673F7B3BDC0DD1C6EAEA185_AdjustorThunk },
	{ 0x060003C4, ARTrackedImagesChangedEventArgs_set_removed_mC7F5D25B8E4AD4CBBE6A43D50FAE0456BE3F928B_AdjustorThunk },
	{ 0x060003C5, ARTrackedImagesChangedEventArgs__ctor_m22F5D20572E4D17270B7CFBA7F0EA9445DAEE8C3_AdjustorThunk },
	{ 0x060003C6, ARTrackedImagesChangedEventArgs_GetHashCode_mEFBDE822EC1AA6B0E388B86ACE4043D66E23A742_AdjustorThunk },
	{ 0x060003C7, ARTrackedImagesChangedEventArgs_Equals_m728C2C2B10BA8C4A625C3004C83730589C86C542_AdjustorThunk },
	{ 0x060003C8, ARTrackedImagesChangedEventArgs_ToString_m949FF3EE96422AE2776C8FFBBD7CA342E56AE365_AdjustorThunk },
	{ 0x060003C9, ARTrackedImagesChangedEventArgs_Equals_m25F0396D7AA82B5E488BEA4129EE00F355EA3919_AdjustorThunk },
	{ 0x060003DD, ARTrackedObjectsChangedEventArgs_get_added_m71E8E2055C979657273051DE23FCEB938964169A_AdjustorThunk },
	{ 0x060003DE, ARTrackedObjectsChangedEventArgs_set_added_m6DE32B4C3E8C17166AC5DB4C181DE56842BD0922_AdjustorThunk },
	{ 0x060003DF, ARTrackedObjectsChangedEventArgs_get_updated_m01050D519E6281EECB48200D952FE1E1FFFE1A60_AdjustorThunk },
	{ 0x060003E0, ARTrackedObjectsChangedEventArgs_set_updated_mF2A1424732988AB85FF48E63741896BF61E94B46_AdjustorThunk },
	{ 0x060003E1, ARTrackedObjectsChangedEventArgs_get_removed_mDF5FA585D41D4B0D4517D3C5387B254F6EA6DFDA_AdjustorThunk },
	{ 0x060003E2, ARTrackedObjectsChangedEventArgs_set_removed_m58FAB3CA4AFD4D9B8D0BA1FD150C8FBF06D30F4E_AdjustorThunk },
	{ 0x060003E3, ARTrackedObjectsChangedEventArgs__ctor_mED9F9F2F2307AC78C3580327A6501F5E7ED5060A_AdjustorThunk },
	{ 0x060003E4, ARTrackedObjectsChangedEventArgs_GetHashCode_mC0D8DBBE030D0F00D1D0662C2C100FADF4EC429A_AdjustorThunk },
	{ 0x060003E5, ARTrackedObjectsChangedEventArgs_Equals_mB6CEB7818A761BB4B5009A280C058522176E7641_AdjustorThunk },
	{ 0x060003E6, ARTrackedObjectsChangedEventArgs_ToString_m1E23EE412E0E27AE4186B56B83F4EF1F12FAD5A7_AdjustorThunk },
	{ 0x060003E7, ARTrackedObjectsChangedEventArgs_Equals_m822193D19946082B0F5D17078ABD169B4A002EDE_AdjustorThunk },
	{ 0x060003EB, Message_WithMessage_m6D980DF1AD1190D26E45B88C3600AF4226FDD63C_AdjustorThunk },
	{ 0x060003ED, Message_WithMessage_m7331F24D7470335EC2EEF8FBA56A8D570ACC8FD3_AdjustorThunk },
	{ 0x06000411, DeallocateJob_Execute_mECC71B15D80D3951E0D0B4B0B7B28A50DBC81D6F_AdjustorThunk },
};
static const int32_t s_InvokerIndices[1085] = 
{
	11832,
	7776,
	7620,
	7595,
	7776,
	7776,
	7776,
	7776,
	7656,
	6213,
	6213,
	6213,
	5462,
	4450,
	2593,
	4450,
	4450,
	5473,
	7656,
	7656,
	1732,
	7776,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	1732,
	7618,
	4450,
	7656,
	4284,
	10065,
	10065,
	7776,
	7656,
	7656,
	7656,
	7656,
	7540,
	6094,
	7656,
	6213,
	7540,
	7656,
	11574,
	7540,
	7540,
	7776,
	7776,
	7776,
	7776,
	7776,
	7776,
	7776,
	7656,
	6213,
	7776,
	7776,
	7776,
	7776,
	11578,
	6054,
	6094,
	6064,
	9929,
	7776,
	11802,
	11754,
	11754,
	11359,
	11752,
	11752,
	11802,
	7521,
	6062,
	7351,
	5946,
	7352,
	5947,
	7352,
	5947,
	7656,
	6213,
	7656,
	6213,
	7346,
	5943,
	7354,
	5949,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7707,
	6253,
	7784,
	6324,
	4283,
	7618,
	4450,
	7656,
	4285,
	10066,
	10066,
	6213,
	6213,
	7540,
	6094,
	7618,
	6176,
	7540,
	7618,
	6176,
	7618,
	7618,
	6176,
	7618,
	6176,
	7618,
	7540,
	6094,
	7540,
	7540,
	7656,
	7776,
	7776,
	4283,
	3773,
	7358,
	5954,
	4283,
	4283,
	7776,
	7776,
	7776,
	7776,
	7776,
	6323,
	7776,
	11802,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7776,
	7540,
	7776,
	7776,
	7776,
	7776,
	7776,
	7776,
	7776,
	7776,
	6213,
	7776,
	7776,
	6213,
	7776,
	7656,
	7776,
	6213,
	3430,
	7656,
	3139,
	6105,
	7776,
	6067,
	6053,
	6068,
	5461,
	5461,
	6213,
	6213,
	7776,
	3244,
	3435,
	6213,
	7776,
	7776,
	7776,
	7776,
	7776,
	7776,
	6176,
	6253,
	6253,
	6253,
	7776,
	6253,
	6176,
	7776,
	7540,
	7656,
	7776,
	7656,
	7618,
	6176,
	7618,
	6176,
	7767,
	7767,
	7620,
	7798,
	7776,
	7656,
	7776,
	6343,
	7656,
	7776,
	7776,
	7776,
	7776,
	7540,
	6094,
	7540,
	7540,
	6094,
	7540,
	7618,
	6176,
	7540,
	6094,
	7540,
	7540,
	6094,
	7540,
	7656,
	6213,
	6213,
	6213,
	5473,
	1432,
	4450,
	4450,
	4450,
	7656,
	7656,
	7776,
	7776,
	1732,
	6213,
	7776,
	7776,
	7776,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	1732,
	7618,
	4450,
	7656,
	4289,
	10070,
	10070,
	6213,
	6213,
	7315,
	7315,
	7309,
	7314,
	7620,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7776,
	7776,
	0,
	6213,
	7776,
	10793,
	7776,
	5461,
	7656,
	6213,
	7618,
	6176,
	7618,
	6176,
	7618,
	7618,
	6213,
	6213,
	5473,
	7776,
	3453,
	1732,
	7656,
	7656,
	7776,
	7656,
	6213,
	6094,
	7776,
	7776,
	6059,
	6072,
	7776,
	7776,
	7776,
	7776,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	1732,
	7618,
	4450,
	7656,
	4291,
	10072,
	10072,
	7656,
	6213,
	6213,
	7618,
	4450,
	4290,
	10071,
	10071,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	1732,
	7618,
	4292,
	4450,
	10073,
	10073,
	7656,
	7671,
	7707,
	7320,
	6213,
	7656,
	5461,
	7776,
	7776,
	7776,
	7540,
	6094,
	7540,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	7540,
	6094,
	7540,
	6094,
	7540,
	7656,
	6213,
	7656,
	6213,
	6213,
	7656,
	5473,
	3774,
	7776,
	7776,
	3454,
	1732,
	7776,
	7656,
	6213,
	7776,
	7776,
	7776,
	7656,
	7776,
	11802,
	7354,
	5949,
	7354,
	5949,
	7343,
	5941,
	7354,
	5949,
	7354,
	5949,
	7354,
	5949,
	7343,
	5941,
	7357,
	5953,
	7355,
	5950,
	7618,
	4450,
	7656,
	4293,
	10074,
	10074,
	5569,
	5569,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	1732,
	7618,
	4450,
	7656,
	4294,
	10075,
	10075,
	7656,
	6213,
	7707,
	6253,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7540,
	6094,
	7618,
	6176,
	6213,
	6213,
	7656,
	7656,
	7776,
	0,
	7656,
	7776,
	7776,
	11754,
	7776,
	7776,
	7776,
	6201,
	5420,
	10777,
	7776,
	7776,
	7776,
	5473,
	11492,
	11277,
	7776,
	7776,
	11802,
	2370,
	7776,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7618,
	4450,
	4295,
	10076,
	10076,
	6213,
	6213,
	7618,
	6176,
	7618,
	6176,
	7618,
	7618,
	6176,
	7618,
	6176,
	7618,
	7618,
	6176,
	7618,
	7540,
	6094,
	7540,
	7618,
	6176,
	7618,
	7656,
	4283,
	7656,
	4283,
	7656,
	4283,
	7656,
	4283,
	4283,
	4283,
	7776,
	7776,
	7776,
	7776,
	7776,
	7776,
	7776,
	7620,
	7595,
	7776,
	7656,
	6213,
	6213,
	6213,
	5473,
	7656,
	1732,
	7656,
	7776,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	1732,
	7618,
	4450,
	5164,
	7656,
	4296,
	10077,
	10077,
	7707,
	6253,
	6213,
	6213,
	7767,
	7656,
	6213,
	7618,
	7618,
	7765,
	7767,
	7765,
	7765,
	7662,
	7620,
	7314,
	6213,
	7776,
	7776,
	7776,
	7776,
	7776,
	7776,
	7776,
	7656,
	6213,
	6213,
	7618,
	4450,
	7656,
	4297,
	10078,
	10078,
	7656,
	6213,
	7618,
	6176,
	7618,
	6176,
	7618,
	6213,
	6213,
	5473,
	1230,
	10573,
	10397,
	7656,
	7776,
	3417,
	1732,
	7656,
	7776,
	7776,
	7776,
	8692,
	9936,
	9333,
	11802,
	7656,
	6213,
	7618,
	6176,
	7540,
	6094,
	6066,
	7776,
	0,
	6094,
	7776,
	7776,
	7776,
	7776,
	7776,
	7776,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	1732,
	7618,
	4450,
	7656,
	4298,
	10079,
	10079,
	6213,
	6213,
	7340,
	7339,
	7338,
	7776,
	7776,
	7776,
	6213,
	7776,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	1732,
	7618,
	4450,
	7656,
	4299,
	10080,
	10080,
	7656,
	6213,
	6213,
	6213,
	7776,
	7776,
	7656,
	7656,
	3456,
	1732,
	1230,
	0,
	7776,
	6176,
	7776,
	7656,
	6213,
	6069,
	7776,
	7776,
	7776,
	7776,
	7776,
	6094,
	7776,
	11802,
	6069,
	7776,
	7776,
	7776,
	7776,
	7776,
	6094,
	7776,
	11802,
	7618,
	4450,
	4300,
	10081,
	10081,
	7776,
	7776,
	7776,
	7776,
	7776,
	6167,
	2847,
	11816,
	7776,
	7620,
	7707,
	7656,
	6213,
	6213,
	6213,
	7776,
	7776,
	7776,
	1798,
	1208,
	7707,
	7618,
	7671,
	7749,
	7671,
	7707,
	7656,
	7618,
	4450,
	4301,
	10082,
	10082,
	5067,
	7656,
	7656,
	6213,
	1328,
	1311,
	2660,
	6213,
	7656,
	6213,
	6213,
	7776,
	5522,
	1231,
	1231,
	1230,
	10360,
	1230,
	1235,
	3457,
	7776,
	11802,
	7656,
	6213,
	4302,
	4450,
	7618,
	10083,
	10083,
	7540,
	6094,
	7540,
	6094,
	7540,
	7540,
	6094,
	7618,
	6176,
	7618,
	7350,
	11578,
	11578,
	11746,
	11574,
	11746,
	7776,
	6094,
	7776,
	11754,
	11754,
	11754,
	7776,
	7656,
	7776,
	7776,
	7776,
	6094,
	7776,
	7776,
	11802,
	7776,
	6176,
	7776,
	7540,
	7656,
	7776,
	7656,
	6176,
	7776,
	7540,
	7656,
	7776,
	7656,
	6176,
	7776,
	7540,
	7656,
	7776,
	7656,
	7656,
	6213,
	7656,
	7776,
	7618,
	6176,
	6176,
	7618,
	4450,
	7656,
	4303,
	10084,
	10084,
	7798,
	7656,
	6343,
	7776,
	7776,
	10017,
	11369,
	11115,
	7776,
	7618,
	4304,
	4450,
	10085,
	10085,
	7776,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	7765,
	7765,
	7620,
	7796,
	6340,
	7776,
	7656,
	6213,
	5461,
	7618,
	6176,
	7540,
	7618,
	6176,
	7618,
	7656,
	6213,
	7656,
	6213,
	6213,
	7656,
	7776,
	1934,
	3459,
	1732,
	6213,
	7776,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	1732,
	7618,
	4450,
	7656,
	4306,
	10087,
	10087,
	7620,
	7797,
	6341,
	7776,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	6213,
	7656,
	7776,
	3460,
	1732,
	7776,
	7776,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	1732,
	7618,
	4450,
	7656,
	4307,
	10088,
	10088,
	10943,
	6213,
	10945,
	6213,
	11088,
	11088,
	11219,
	11527,
	6213,
	7776,
	10368,
	11213,
	9417,
	8751,
	8250,
	8094,
	8021,
	7983,
	6213,
	0,
	11527,
	11219,
	11209,
	11209,
	11754,
	11724,
	11724,
	2317,
	7776,
	6204,
	7618,
	1999,
	4440,
	6204,
	6204,
	7776,
	7776,
	8384,
	8196,
	7776,
	7776,
	10633,
	10633,
	10815,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	7656,
	6213,
	7776,
	7776,
	7776,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	11527,
	11219,
	10540,
	10540,
	10534,
	10534,
	10792,
	10792,
	10786,
};
static const Il2CppTokenIndexMethodTuple s_reversePInvokeIndices[1] = 
{
	{ 0x0600002F, 0,  (void**)&ARCameraBackground_BeforeBackgroundRenderHandler_m3B215B54CFB6F6F00A724D9A6BE66808593ABFCD_RuntimeMethod_var, 0 },
};
static const Il2CppTokenRangePair s_rgctxIndices[9] = 
{
	{ 0x02000043, { 19, 7 } },
	{ 0x02000044, { 26, 85 } },
	{ 0x02000061, { 111, 18 } },
	{ 0x02000063, { 129, 11 } },
	{ 0x02000064, { 140, 12 } },
	{ 0x06000148, { 0, 6 } },
	{ 0x060001F3, { 6, 2 } },
	{ 0x06000298, { 8, 2 } },
	{ 0x060002CF, { 10, 9 } },
};
extern const uint32_t g_rgctx_NativeArray_1_t262CE931BD65164422132E6752F4D01CFA7DD9CC;
extern const uint32_t g_rgctx_NativeArray_1_get_IsCreated_mACA58B12DCED461866C2FD46B5BDEC51C35304D7;
extern const uint32_t g_rgctx_NativeArray_1_t262CE931BD65164422132E6752F4D01CFA7DD9CC;
extern const uint32_t g_rgctx_NativeArrayUnsafeUtility_GetUnsafePtr_TisT_tDE45727CF7B21C394FF71C50EE7522EACBE2A698_m8B893A9F4FA0804FBF41D4A92CE22B51A73F8141;
extern const uint32_t g_rgctx_NativeArray_1_get_Length_mD420D4707A42B9415E8A4FDD6E2F2D8AE5F07669;
extern const uint32_t g_rgctx_NativeArrayUnsafeUtility_ConvertExistingDataToNativeArray_TisT_tDE45727CF7B21C394FF71C50EE7522EACBE2A698_mCF4AAB4E40435919F3E671BEB952D68181D688F7;
extern const uint32_t g_rgctx_Component_GetComponent_TisT_t3C10516E7FE517FE335EE1B94DCD03C9E52EED3E_m115DF5A16DBF6C4D5B6E6CFE205F013C5AD76FB7;
extern const uint32_t g_rgctx_T_t3C10516E7FE517FE335EE1B94DCD03C9E52EED3E;
extern const uint32_t g_rgctx_Component_GetComponent_TisT_tC3F99F359F71E5A27263B761F008CA1EDD8A0965_mAF59624E7DF0F9A092A0FBE9D9CF8FC94B40D695;
extern const uint32_t g_rgctx_T_tC3F99F359F71E5A27263B761F008CA1EDD8A0965;
extern const uint32_t g_rgctx_NativeArray_1U26_t5DC3E7574ACB2B2AAC22E0E3E0B559E36AD155DE;
extern const uint32_t g_rgctx_NativeArray_1_get_Length_mB4B65FDF52A5F4556392ACA69FC9E3693EAE34BE;
extern const uint32_t g_rgctx_NativeArray_1_t94980EE62E47396FCD34681EFA44D4AA15AA4DC6;
extern const uint32_t g_rgctx_NativeArray_1__ctor_m9525C96639C26FB31A2FBAD28BF20C607AFA262C;
extern const uint32_t g_rgctx_NativeArray_1_t94980EE62E47396FCD34681EFA44D4AA15AA4DC6;
extern const uint32_t g_rgctx_NativeArray_1_Copy_m2848DD336B7AECB42E11BC538612E4E989CFDF95;
extern const uint32_t g_rgctx_NativeArray_1_Copy_m66FCC3A58E10862EF1008CDC047D2A519F2687DD;
extern const uint32_t g_rgctx_NativeArray_1_get_IsCreated_mEA12B238D1118F3F9DE5E7E7AF17620B6A18BDD0;
extern const uint32_t g_rgctx_NativeArray_1_Dispose_m343C00A999763A19C197385E35673C6F415367AD;
extern const uint32_t g_rgctx_ARTrackable_2_t398EA956692BB8DF074BDE4AA2E68414CA1D0BC2;
extern const uint32_t g_rgctx_ARTrackable_2_get_sessionRelativeData_m0A9610B594F1EA6DC4240E261248A2864C1A6151;
extern const uint32_t g_rgctx_TSessionRelativeData_t5C940E460FA6CD53E02E1E59A516CBAC5216B7B7;
extern const Il2CppRGCTXConstrainedData g_rgctx_TSessionRelativeData_t5C940E460FA6CD53E02E1E59A516CBAC5216B7B7_ITrackable_get_trackableId_m7242DF9928E5787565A8DDE0374D160431569206;
extern const Il2CppRGCTXConstrainedData g_rgctx_TSessionRelativeData_t5C940E460FA6CD53E02E1E59A516CBAC5216B7B7_ITrackable_get_trackingState_m9FA2B7E313B660331ABDB286F43D52D06E08DECF;
extern const uint32_t g_rgctx_ARTrackable_2_set_sessionRelativeData_m4807ABFD46BF3BA87380BF6928C4399BF693C194;
extern const Il2CppRGCTXConstrainedData g_rgctx_TSessionRelativeData_t5C940E460FA6CD53E02E1E59A516CBAC5216B7B7_ITrackable_get_pose_mB0BCDCC1F9EBEF11E02A31924C4A61C8ED933400;
extern const uint32_t g_rgctx_ARTrackableManager_5_t241D1602FAE3316FAFD1F7E12F8D62272EA0FA6C;
extern const uint32_t g_rgctx_ARTrackableManager_5_t241D1602FAE3316FAFD1F7E12F8D62272EA0FA6C;
extern const uint32_t g_rgctx_SubsystemLifecycleManager_3_t338D9C9883A04C53DCB5CD1FDBF4FBD6EC04DC6E;
extern const uint32_t g_rgctx_Dictionary_2_tC493ECF7C358FCAA01D72A3302D331A24BF4C46B;
extern const uint32_t g_rgctx_TrackableCollection_1_t3E37DFA951086ECC717DF23818374B5A34C42BCD;
extern const uint32_t g_rgctx_TrackableCollection_1__ctor_m59FFBB950E0ED751619207D342C1D50953C57C23;
extern const uint32_t g_rgctx_ARTrackableManager_5_get_trackables_m437E7058274C80C60A5022BAA5CFBAFA9DDB4A0F;
extern const uint32_t g_rgctx_TrackableCollection_1_GetEnumerator_m1005EA2E32E965A3A9E9379CCBF25E155D224511;
extern const uint32_t g_rgctx_TrackableCollection_1_t3E37DFA951086ECC717DF23818374B5A34C42BCD;
extern const uint32_t g_rgctx_Enumerator_t371914151017EFE08C2EF6C65615B96ECFA25886;
extern const uint32_t g_rgctx_Enumerator_get_Current_m22CE212C2FCDEAB5B7FC18170131DA80422C41AC;
extern const uint32_t g_rgctx_Enumerator_t371914151017EFE08C2EF6C65615B96ECFA25886;
extern const uint32_t g_rgctx_TTrackable_t78ACDD55B8465E4BD8EF3FA977C3F7A2A22DFE24;
extern const uint32_t g_rgctx_Enumerator_MoveNext_m1A0E56A4C06D0B79D2C51917FFD876D4EF1F409A;
extern const uint32_t g_rgctx_ARTrackableManager_5_set_origin_m6BFD02992C49261C3ADDB3349BC795379C24AA41;
extern const uint32_t g_rgctx_SubsystemLifecycleManager_3_OnEnable_m884EBBF0EAF5107C2B7B98C1F8BCFA5DEB99D231;
extern const uint32_t g_rgctx_ARTrackableManager_5_set_instance_m1CE1E3D8E7580914DE119B09531566C73B82202E;
extern const uint32_t g_rgctx_ARTrackableManager_5_get_origin_m47BC05AFCF8EAD66D1644D8C4E800EA49AFA7984;
extern const uint32_t g_rgctx_ARTrackableManager_5_OnTrackablesParentTransformChanged_m72611547D55906C2393DD71968C3E805F54888D9;
extern const uint32_t g_rgctx_SubsystemLifecycleManager_3_OnDisable_m5E5B465682CE7AB7D716B8C32E7B1322F9BACF63;
extern const uint32_t g_rgctx_ARTrackable_2_t34A41EA233B11F2502D0992EE6AFCE849FD17D76;
extern const uint32_t g_rgctx_ARTrackable_2_get_trackableId_m6539F2BC499D92C933CDE50E1AE6E5614901BA5B;
extern const uint32_t g_rgctx_Dictionary_2_ContainsKey_mE1CC8BFA66F76BA34468D9DC427CD1EAF21B4030;
extern const uint32_t g_rgctx_SubsystemLifecycleManager_3_get_subsystem_mCB8C3AC2A6A8357487AC29AB6371E38EF37C30B9;
extern const uint32_t g_rgctx_TSubsystem_t34DD52FED18E224F47FC5E93DB3B54418D972561;
extern const uint32_t g_rgctx_ARTrackable_2_set_pending_mB2DEF80C52A7058AD8BCC14F41676E204D4CA5FA;
extern const uint32_t g_rgctx_ARTrackable_2_get_sessionRelativePose_mB223FFBEC9E1554EDEA1FAEB26616137B7CF473F;
extern const uint32_t g_rgctx_TrackingSubsystem_4_t004A144CA869ACE53EA2071D7E0175FE932219B9;
extern const uint32_t g_rgctx_TrackingSubsystem_4_GetChanges_mEB8DD2EE52E2EA271ADD0C626CE31263F33474EF;
extern const uint32_t g_rgctx_TrackableChanges_1_tBBAAA7568DEEC06F841AD196742FACB3CB55BA98;
extern const uint32_t g_rgctx_List_1_t706CCC8B361457A4080C1052CF2678440D176041;
extern const uint32_t g_rgctx_TrackableChanges_1_get_added_m72F1E632C39B1BA4BF6DD8CA990AE8D834B565DA;
extern const uint32_t g_rgctx_TrackableChanges_1_tBBAAA7568DEEC06F841AD196742FACB3CB55BA98;
extern const uint32_t g_rgctx_NativeArray_1_t107FE741085918B3B219FF1E714B23514A24AD9C;
extern const uint32_t g_rgctx_NativeArray_1_get_Length_m206193B20375B28BAF2CDC961FC4F4444B15B5CF;
extern const uint32_t g_rgctx_NativeArray_1_t107FE741085918B3B219FF1E714B23514A24AD9C;
extern const uint32_t g_rgctx_ARTrackableManager_5_ClearAndSetCapacity_m35635D08639F93A68C840BCE1FBE657D5A12B6A1;
extern const uint32_t g_rgctx_NativeArray_1_GetEnumerator_m008DE0CEBAA83A055A3CA2A73DD5DB82979C173F;
extern const uint32_t g_rgctx_Enumerator_tC7AF2796C161CC547092AF82B19F7115620DF374;
extern const uint32_t g_rgctx_Enumerator_get_Current_m651A21291BADCA7121BE9F51EE5D82444A76A534;
extern const uint32_t g_rgctx_Enumerator_tC7AF2796C161CC547092AF82B19F7115620DF374;
extern const uint32_t g_rgctx_TSessionRelativeData_t08AE590F2AC3BAAB3363F0BEA6DAD30166AA5189;
extern const uint32_t g_rgctx_ARTrackableManager_5_CreateOrUpdateTrackable_mCA5018E0F71D438745BF2412675A18EC17180437;
extern const uint32_t g_rgctx_List_1_Add_m0753D8C8A2A7162DF539911C9F31EC471FEA65FA;
extern const uint32_t g_rgctx_Enumerator_MoveNext_m91154A9B59FE021841D81A8309027E0BEAAE6005;
extern const Il2CppRGCTXConstrainedData g_rgctx_Enumerator_tC7AF2796C161CC547092AF82B19F7115620DF374_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7;
extern const uint32_t g_rgctx_TrackableChanges_1_get_updated_m2A6619A891FB520ECABF1C07E427DF3CBAEE5F22;
extern const uint32_t g_rgctx_TrackableChanges_1_get_removed_m0C71D4E21C6205BA4EA190FC55D1F44777EFA621;
extern const uint32_t g_rgctx_Dictionary_2_TryGetValue_mF96C1F52198428B08187CA59E74794550F1269CC;
extern const uint32_t g_rgctx_TTrackableU26_t63DE2679EFEC0FE464AEDB2101961A358FA0ABAA;
extern const uint32_t g_rgctx_Dictionary_2_Remove_m2C35AB98C1FA6A263CBC1341CA233E38DA2D91AC;
extern const Il2CppRGCTXConstrainedData g_rgctx_TrackableChanges_1_tBBAAA7568DEEC06F841AD196742FACB3CB55BA98_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7;
extern const uint32_t g_rgctx_List_1_get_Count_m164018FF6126172926249F23A5891B5596ED53DD;
extern const uint32_t g_rgctx_ARTrackableManager_5_OnTrackablesChanged_m2517D7F8346C8E7AEDC83B82C812FD6BF296E96F;
extern const uint32_t g_rgctx_List_1_GetEnumerator_m55AFFA19045CBD32657089486BC6AE3C8F047DDE;
extern const uint32_t g_rgctx_Enumerator_tBE254E298B61F79FF8DCFED3B0F5801A631DB0C7;
extern const uint32_t g_rgctx_Enumerator_get_Current_mBFDB086389A6273DEFF662873AF76159F1CECBEE;
extern const uint32_t g_rgctx_Enumerator_tBE254E298B61F79FF8DCFED3B0F5801A631DB0C7;
extern const uint32_t g_rgctx_ARTrackableManager_5_DestroyTrackable_m19BEDABB1FC84B080E3C9EAECC7964A4018B0BAE;
extern const uint32_t g_rgctx_Enumerator_MoveNext_m1A305E3AFB1B9D3F3E403E51B34EA1F4A91B888C;
extern const Il2CppRGCTXConstrainedData g_rgctx_Enumerator_tBE254E298B61F79FF8DCFED3B0F5801A631DB0C7_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7;
extern const uint32_t g_rgctx_Dictionary_2_Add_m858E69E085B428996C653C940E70812A09526641;
extern const uint32_t g_rgctx_List_1_Clear_mB2CED12617205DDB552CE826E28A745C675509C0;
extern const uint32_t g_rgctx_List_1_get_Capacity_m9DD31BFA45308A7D43A7B5B35C053BEB035FF112;
extern const uint32_t g_rgctx_List_1_set_Capacity_m573F157FB16F6FD6B249B525AE0184F08A9BDCAE;
extern const uint32_t g_rgctx_ARTrackableManager_5_get_gameObjectName_m55C745FED94F93714DC139596326F73DE7547F09;
extern const uint32_t g_rgctx_ARTrackableManager_5_GetPrefab_m8E61E6C6DF06D2A6241A000F58D73DBFAECE9F55;
extern const uint32_t g_rgctx_ARTrackableManager_5_CreateGameObjectDeactivated_mCB41EAE67388613AC4EE6D3293F1B09CD5C303E5;
extern const uint32_t g_rgctx_ARTrackableManager_5_GetTrackableName_m2F69792FBB34D92B989E9FC248D03C812A554EF8;
extern const uint32_t g_rgctx_ARTrackableManager_5_CreateGameObjectDeactivated_m3D7515DC93A2364AC915050DC23A177826E8E1FC;
extern const Il2CppRGCTXConstrainedData g_rgctx_TSessionRelativeData_t08AE590F2AC3BAAB3363F0BEA6DAD30166AA5189_ITrackable_get_trackableId_m7242DF9928E5787565A8DDE0374D160431569206;
extern const uint32_t g_rgctx_ARTrackableManager_5_CreateGameObjectDeactivated_mDED7AFFF8F500D77F3E195A57AF9A51389125AAE;
extern const uint32_t g_rgctx_GameObject_GetComponent_TisTTrackable_t78ACDD55B8465E4BD8EF3FA977C3F7A2A22DFE24_m6643E5B2124BA90B143723C19743D1D8706A8C86;
extern const uint32_t g_rgctx_GameObject_AddComponent_TisTTrackable_t78ACDD55B8465E4BD8EF3FA977C3F7A2A22DFE24_mC0A27ACB1E296EFF47BA06CE69F6A8B1B158FD87;
extern const uint32_t g_rgctx_ARTrackableManager_5_SetSessionRelativeData_m9643A7D685AF54C93F16738434DB0B8DFB8FC3BD;
extern const uint32_t g_rgctx_ARTrackable_2_SetSessionRelativeData_m2CC441CA5EDFCBE2100DB9C3049A2579F2A54427;
extern const Il2CppRGCTXConstrainedData g_rgctx_TSessionRelativeData_t08AE590F2AC3BAAB3363F0BEA6DAD30166AA5189_ITrackable_get_pose_mB0BCDCC1F9EBEF11E02A31924C4A61C8ED933400;
extern const uint32_t g_rgctx_ARTrackableManager_5_OnCreateTrackable_mCC607AA18FB176D731BA8E93D4BC59A4FD33F455;
extern const uint32_t g_rgctx_ARTrackableManager_5_OnAfterSetSessionRelativeData_mF3A5FC525D64B10C07C5240B2074F5BFF50533E7;
extern const uint32_t g_rgctx_ARTrackable_2_OnAfterSetSessionRelativeData_mD1B3585460894050B174773587EA5A3C6A0BC158;
extern const uint32_t g_rgctx_ARTrackableManager_5_CreateTrackable_m45D4E33F5F665E853E895E3879050F18A1BD643E;
extern const uint32_t g_rgctx_ARTrackable_2_get_destroyOnRemoval_m5ADF37363CC91AFC03342EF0EF6EF435D54C4727;
extern const uint32_t g_rgctx_Dictionary_2__ctor_mBAB7F30B2606F0CC05EB4FB0483ACA7F80640749;
extern const uint32_t g_rgctx_SubsystemLifecycleManager_3__ctor_mE2146A9868C4058CB7341ED5114B0F953555A3EE;
extern const uint32_t g_rgctx_List_1__ctor_m652C0137EAAC1EFE62F0FE630FE171F589733528;
extern const uint32_t g_rgctx_SubsystemLifecycleManager_3_tB495CB9CAAC08A090DC3E365B5430D9551C852A0;
extern const uint32_t g_rgctx_TSubsystem_t7BC6C280A9558EB361791FF8B4A9DE8AF1F0095E;
extern const uint32_t g_rgctx_SubsystemLifecycleManager_3_get_subsystem_mF82A91BFA86E83393B8C5F39D2CF6D11866D49EB;
extern const uint32_t g_rgctx_TSubsystemDescriptor_t1E3F6627DF333089E24D982EB210FE78DE376E51;
extern const uint32_t g_rgctx_SubsystemWithProvider_3_t93C4A208720883D60870C4FBF5054488C317AFE5;
extern const uint32_t g_rgctx_SubsystemWithProvider_3_get_subsystemDescriptor_m3DE8C23923D5232F1EB3629E108402F9A2ADD258;
extern const uint32_t g_rgctx_XRLoader_GetLoadedSubsystem_TisTSubsystem_t7BC6C280A9558EB361791FF8B4A9DE8AF1F0095E_m4C5BCE1CFBB93EDC2D88A6F94F1126D70AFB43B8;
extern const uint32_t g_rgctx_TSubsystem_t7BC6C280A9558EB361791FF8B4A9DE8AF1F0095E;
extern const uint32_t g_rgctx_SubsystemLifecycleManager_3_GetActiveSubsystemInstance_m7397873372ADDCD59F9F8358FB3D79864CA9CAEE;
extern const uint32_t g_rgctx_SubsystemLifecycleManager_3_set_subsystem_mEDE7AFDBFA51FE51987BFE2D68E4232769A53EAF;
extern const uint32_t g_rgctx_SubsystemLifecycleManager_3_EnsureSubsystemInstanceSet_mE1D010E8F1616F8E4585FFB40B71CA2B16F54912;
extern const uint32_t g_rgctx_SubsystemLifecycleManager_3_OnBeforeStart_m5A7169E6634C968DC793EDA5599C846E701800B7;
extern const uint32_t g_rgctx_SubsystemLifecycleManager_3_OnAfterStart_mA31EF8FDA577088D846FFD069CAEE240039CBC77;
extern const uint32_t g_rgctx_List_1_t63DA32228D1B78DA365C4AC6A25169789E69124B;
extern const uint32_t g_rgctx_List_1__ctor_m8EAB6CEFF8E6DFFA31609CAFA423E689B463D497;
extern const uint32_t g_rgctx_SubsystemLifecycleManager_3_tB495CB9CAAC08A090DC3E365B5430D9551C852A0;
extern const uint32_t g_rgctx_List_1_t724486D869FD403F3936702B9ED8E6CFC489C8DE;
extern const uint32_t g_rgctx_List_1__ctor_mD89A8298AD4FB5EB1E759FDA2B30F186DF676FAB;
extern const uint32_t g_rgctx_TrackableCollection_1_t84E580845660ECFC69BC4C577FA6A5EC9869C413;
extern const uint32_t g_rgctx_Dictionary_2_t553EA056C4435F165EBF046EEDF34553C7FDBBF3;
extern const uint32_t g_rgctx_Enumerator_t6A70F87CCB5A7CBE666E84E0E30D9338EAA3227D;
extern const uint32_t g_rgctx_Enumerator__ctor_m506CE2E27F1C4C56BAA911CE4CC814A9E7C82D93;
extern const uint32_t g_rgctx_Dictionary_2_get_Count_mF2EA4D918C0F3E3835BECFA402AF8A50394620B7;
extern const uint32_t g_rgctx_Dictionary_2_get_Item_m856D9F7CCD2B619266A68EE33665DFB9C924547B;
extern const uint32_t g_rgctx_TTrackable_tFF210CC5FBA40BF6425EAE2987D6BDBA34F47F45;
extern const uint32_t g_rgctx_TrackableCollection_1_Equals_mE82810EF01BF30960F8034C657850F6C9968B4CD;
extern const uint32_t g_rgctx_TrackableCollection_1_t84E580845660ECFC69BC4C577FA6A5EC9869C413;
extern const uint32_t g_rgctx_TTrackableU26_t8D819306250428B4BEACE65EF2340E1F85F24A47;
extern const uint32_t g_rgctx_Dictionary_2_TryGetValue_m6F0F07522379669634AFBF1A39ECB065BC49CECE;
extern const uint32_t g_rgctx_Dictionary_2_t79D760AB6F7C618D9201DEBE364C082BA61D800D;
extern const uint32_t g_rgctx_Dictionary_2_GetEnumerator_m94652D34881801AD1214517D8906BBA11896BCB1;
extern const uint32_t g_rgctx_Enumerator_tA582FFC4C899A8805C484533142EDF7E6BD61E18;
extern const uint32_t g_rgctx_Enumerator_t3A49019B09D3BE7D7A0CD900532C68B79E351E84;
extern const uint32_t g_rgctx_Enumerator_MoveNext_mEC2AEEDA4CF40AA8379C53855A44F019721DB037;
extern const uint32_t g_rgctx_Enumerator_tA582FFC4C899A8805C484533142EDF7E6BD61E18;
extern const uint32_t g_rgctx_Enumerator_get_Current_m5190A263373FB0008A3AA284FDB9062611EF9C9D;
extern const uint32_t g_rgctx_KeyValuePair_2_t90DC86E5EFFD2F85688663A64600E4C8DE32074C;
extern const uint32_t g_rgctx_KeyValuePair_2_get_Value_m286FE25E1C6A797990C9758C7B41D035D225A90D;
extern const uint32_t g_rgctx_KeyValuePair_2_t90DC86E5EFFD2F85688663A64600E4C8DE32074C;
extern const uint32_t g_rgctx_TTrackable_tC4BDB5E70816B97EF820497542BB4C6895556DCE;
extern const uint32_t g_rgctx_Enumerator_Dispose_m3B07DA698622B8157A7748200ED296A22D374108;
static const Il2CppRGCTXDefinition s_rgctxValues[152] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t262CE931BD65164422132E6752F4D01CFA7DD9CC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_IsCreated_mACA58B12DCED461866C2FD46B5BDEC51C35304D7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t262CE931BD65164422132E6752F4D01CFA7DD9CC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArrayUnsafeUtility_GetUnsafePtr_TisT_tDE45727CF7B21C394FF71C50EE7522EACBE2A698_m8B893A9F4FA0804FBF41D4A92CE22B51A73F8141 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Length_mD420D4707A42B9415E8A4FDD6E2F2D8AE5F07669 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArrayUnsafeUtility_ConvertExistingDataToNativeArray_TisT_tDE45727CF7B21C394FF71C50EE7522EACBE2A698_mCF4AAB4E40435919F3E671BEB952D68181D688F7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Component_GetComponent_TisT_t3C10516E7FE517FE335EE1B94DCD03C9E52EED3E_m115DF5A16DBF6C4D5B6E6CFE205F013C5AD76FB7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t3C10516E7FE517FE335EE1B94DCD03C9E52EED3E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Component_GetComponent_TisT_tC3F99F359F71E5A27263B761F008CA1EDD8A0965_mAF59624E7DF0F9A092A0FBE9D9CF8FC94B40D695 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tC3F99F359F71E5A27263B761F008CA1EDD8A0965 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1U26_t5DC3E7574ACB2B2AAC22E0E3E0B559E36AD155DE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Length_mB4B65FDF52A5F4556392ACA69FC9E3693EAE34BE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t94980EE62E47396FCD34681EFA44D4AA15AA4DC6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1__ctor_m9525C96639C26FB31A2FBAD28BF20C607AFA262C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t94980EE62E47396FCD34681EFA44D4AA15AA4DC6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_Copy_m2848DD336B7AECB42E11BC538612E4E989CFDF95 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_Copy_m66FCC3A58E10862EF1008CDC047D2A519F2687DD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_IsCreated_mEA12B238D1118F3F9DE5E7E7AF17620B6A18BDD0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_Dispose_m343C00A999763A19C197385E35673C6F415367AD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ARTrackable_2_t398EA956692BB8DF074BDE4AA2E68414CA1D0BC2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ARTrackable_2_get_sessionRelativeData_m0A9610B594F1EA6DC4240E261248A2864C1A6151 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TSessionRelativeData_t5C940E460FA6CD53E02E1E59A516CBAC5216B7B7 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TSessionRelativeData_t5C940E460FA6CD53E02E1E59A516CBAC5216B7B7_ITrackable_get_trackableId_m7242DF9928E5787565A8DDE0374D160431569206 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TSessionRelativeData_t5C940E460FA6CD53E02E1E59A516CBAC5216B7B7_ITrackable_get_trackingState_m9FA2B7E313B660331ABDB286F43D52D06E08DECF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ARTrackable_2_set_sessionRelativeData_m4807ABFD46BF3BA87380BF6928C4399BF693C194 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TSessionRelativeData_t5C940E460FA6CD53E02E1E59A516CBAC5216B7B7_ITrackable_get_pose_mB0BCDCC1F9EBEF11E02A31924C4A61C8ED933400 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ARTrackableManager_5_t241D1602FAE3316FAFD1F7E12F8D62272EA0FA6C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ARTrackableManager_5_t241D1602FAE3316FAFD1F7E12F8D62272EA0FA6C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_SubsystemLifecycleManager_3_t338D9C9883A04C53DCB5CD1FDBF4FBD6EC04DC6E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Dictionary_2_tC493ECF7C358FCAA01D72A3302D331A24BF4C46B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TrackableCollection_1_t3E37DFA951086ECC717DF23818374B5A34C42BCD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TrackableCollection_1__ctor_m59FFBB950E0ED751619207D342C1D50953C57C23 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ARTrackableManager_5_get_trackables_m437E7058274C80C60A5022BAA5CFBAFA9DDB4A0F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TrackableCollection_1_GetEnumerator_m1005EA2E32E965A3A9E9379CCBF25E155D224511 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TrackableCollection_1_t3E37DFA951086ECC717DF23818374B5A34C42BCD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t371914151017EFE08C2EF6C65615B96ECFA25886 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_get_Current_m22CE212C2FCDEAB5B7FC18170131DA80422C41AC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t371914151017EFE08C2EF6C65615B96ECFA25886 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TTrackable_t78ACDD55B8465E4BD8EF3FA977C3F7A2A22DFE24 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_MoveNext_m1A0E56A4C06D0B79D2C51917FFD876D4EF1F409A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ARTrackableManager_5_set_origin_m6BFD02992C49261C3ADDB3349BC795379C24AA41 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SubsystemLifecycleManager_3_OnEnable_m884EBBF0EAF5107C2B7B98C1F8BCFA5DEB99D231 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ARTrackableManager_5_set_instance_m1CE1E3D8E7580914DE119B09531566C73B82202E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ARTrackableManager_5_get_origin_m47BC05AFCF8EAD66D1644D8C4E800EA49AFA7984 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ARTrackableManager_5_OnTrackablesParentTransformChanged_m72611547D55906C2393DD71968C3E805F54888D9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SubsystemLifecycleManager_3_OnDisable_m5E5B465682CE7AB7D716B8C32E7B1322F9BACF63 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ARTrackable_2_t34A41EA233B11F2502D0992EE6AFCE849FD17D76 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ARTrackable_2_get_trackableId_m6539F2BC499D92C933CDE50E1AE6E5614901BA5B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_ContainsKey_mE1CC8BFA66F76BA34468D9DC427CD1EAF21B4030 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SubsystemLifecycleManager_3_get_subsystem_mCB8C3AC2A6A8357487AC29AB6371E38EF37C30B9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TSubsystem_t34DD52FED18E224F47FC5E93DB3B54418D972561 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ARTrackable_2_set_pending_mB2DEF80C52A7058AD8BCC14F41676E204D4CA5FA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ARTrackable_2_get_sessionRelativePose_mB223FFBEC9E1554EDEA1FAEB26616137B7CF473F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TrackingSubsystem_4_t004A144CA869ACE53EA2071D7E0175FE932219B9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TrackingSubsystem_4_GetChanges_mEB8DD2EE52E2EA271ADD0C626CE31263F33474EF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TrackableChanges_1_tBBAAA7568DEEC06F841AD196742FACB3CB55BA98 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t706CCC8B361457A4080C1052CF2678440D176041 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TrackableChanges_1_get_added_m72F1E632C39B1BA4BF6DD8CA990AE8D834B565DA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TrackableChanges_1_tBBAAA7568DEEC06F841AD196742FACB3CB55BA98 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t107FE741085918B3B219FF1E714B23514A24AD9C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Length_m206193B20375B28BAF2CDC961FC4F4444B15B5CF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t107FE741085918B3B219FF1E714B23514A24AD9C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ARTrackableManager_5_ClearAndSetCapacity_m35635D08639F93A68C840BCE1FBE657D5A12B6A1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_GetEnumerator_m008DE0CEBAA83A055A3CA2A73DD5DB82979C173F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_tC7AF2796C161CC547092AF82B19F7115620DF374 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_get_Current_m651A21291BADCA7121BE9F51EE5D82444A76A534 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_tC7AF2796C161CC547092AF82B19F7115620DF374 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TSessionRelativeData_t08AE590F2AC3BAAB3363F0BEA6DAD30166AA5189 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ARTrackableManager_5_CreateOrUpdateTrackable_mCA5018E0F71D438745BF2412675A18EC17180437 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Add_m0753D8C8A2A7162DF539911C9F31EC471FEA65FA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_MoveNext_m91154A9B59FE021841D81A8309027E0BEAAE6005 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_Enumerator_tC7AF2796C161CC547092AF82B19F7115620DF374_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TrackableChanges_1_get_updated_m2A6619A891FB520ECABF1C07E427DF3CBAEE5F22 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TrackableChanges_1_get_removed_m0C71D4E21C6205BA4EA190FC55D1F44777EFA621 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_TryGetValue_mF96C1F52198428B08187CA59E74794550F1269CC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TTrackableU26_t63DE2679EFEC0FE464AEDB2101961A358FA0ABAA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_Remove_m2C35AB98C1FA6A263CBC1341CA233E38DA2D91AC },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TrackableChanges_1_tBBAAA7568DEEC06F841AD196742FACB3CB55BA98_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Count_m164018FF6126172926249F23A5891B5596ED53DD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ARTrackableManager_5_OnTrackablesChanged_m2517D7F8346C8E7AEDC83B82C812FD6BF296E96F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_GetEnumerator_m55AFFA19045CBD32657089486BC6AE3C8F047DDE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_tBE254E298B61F79FF8DCFED3B0F5801A631DB0C7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_get_Current_mBFDB086389A6273DEFF662873AF76159F1CECBEE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_tBE254E298B61F79FF8DCFED3B0F5801A631DB0C7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ARTrackableManager_5_DestroyTrackable_m19BEDABB1FC84B080E3C9EAECC7964A4018B0BAE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_MoveNext_m1A305E3AFB1B9D3F3E403E51B34EA1F4A91B888C },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_Enumerator_tBE254E298B61F79FF8DCFED3B0F5801A631DB0C7_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_Add_m858E69E085B428996C653C940E70812A09526641 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Clear_mB2CED12617205DDB552CE826E28A745C675509C0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Capacity_m9DD31BFA45308A7D43A7B5B35C053BEB035FF112 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_set_Capacity_m573F157FB16F6FD6B249B525AE0184F08A9BDCAE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ARTrackableManager_5_get_gameObjectName_m55C745FED94F93714DC139596326F73DE7547F09 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ARTrackableManager_5_GetPrefab_m8E61E6C6DF06D2A6241A000F58D73DBFAECE9F55 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ARTrackableManager_5_CreateGameObjectDeactivated_mCB41EAE67388613AC4EE6D3293F1B09CD5C303E5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ARTrackableManager_5_GetTrackableName_m2F69792FBB34D92B989E9FC248D03C812A554EF8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ARTrackableManager_5_CreateGameObjectDeactivated_m3D7515DC93A2364AC915050DC23A177826E8E1FC },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TSessionRelativeData_t08AE590F2AC3BAAB3363F0BEA6DAD30166AA5189_ITrackable_get_trackableId_m7242DF9928E5787565A8DDE0374D160431569206 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ARTrackableManager_5_CreateGameObjectDeactivated_mDED7AFFF8F500D77F3E195A57AF9A51389125AAE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_GetComponent_TisTTrackable_t78ACDD55B8465E4BD8EF3FA977C3F7A2A22DFE24_m6643E5B2124BA90B143723C19743D1D8706A8C86 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_AddComponent_TisTTrackable_t78ACDD55B8465E4BD8EF3FA977C3F7A2A22DFE24_mC0A27ACB1E296EFF47BA06CE69F6A8B1B158FD87 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ARTrackableManager_5_SetSessionRelativeData_m9643A7D685AF54C93F16738434DB0B8DFB8FC3BD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ARTrackable_2_SetSessionRelativeData_m2CC441CA5EDFCBE2100DB9C3049A2579F2A54427 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TSessionRelativeData_t08AE590F2AC3BAAB3363F0BEA6DAD30166AA5189_ITrackable_get_pose_mB0BCDCC1F9EBEF11E02A31924C4A61C8ED933400 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ARTrackableManager_5_OnCreateTrackable_mCC607AA18FB176D731BA8E93D4BC59A4FD33F455 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ARTrackableManager_5_OnAfterSetSessionRelativeData_mF3A5FC525D64B10C07C5240B2074F5BFF50533E7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ARTrackable_2_OnAfterSetSessionRelativeData_mD1B3585460894050B174773587EA5A3C6A0BC158 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ARTrackableManager_5_CreateTrackable_m45D4E33F5F665E853E895E3879050F18A1BD643E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ARTrackable_2_get_destroyOnRemoval_m5ADF37363CC91AFC03342EF0EF6EF435D54C4727 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2__ctor_mBAB7F30B2606F0CC05EB4FB0483ACA7F80640749 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SubsystemLifecycleManager_3__ctor_mE2146A9868C4058CB7341ED5114B0F953555A3EE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1__ctor_m652C0137EAAC1EFE62F0FE630FE171F589733528 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_SubsystemLifecycleManager_3_tB495CB9CAAC08A090DC3E365B5430D9551C852A0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TSubsystem_t7BC6C280A9558EB361791FF8B4A9DE8AF1F0095E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SubsystemLifecycleManager_3_get_subsystem_mF82A91BFA86E83393B8C5F39D2CF6D11866D49EB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TSubsystemDescriptor_t1E3F6627DF333089E24D982EB210FE78DE376E51 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_SubsystemWithProvider_3_t93C4A208720883D60870C4FBF5054488C317AFE5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SubsystemWithProvider_3_get_subsystemDescriptor_m3DE8C23923D5232F1EB3629E108402F9A2ADD258 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_XRLoader_GetLoadedSubsystem_TisTSubsystem_t7BC6C280A9558EB361791FF8B4A9DE8AF1F0095E_m4C5BCE1CFBB93EDC2D88A6F94F1126D70AFB43B8 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TSubsystem_t7BC6C280A9558EB361791FF8B4A9DE8AF1F0095E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SubsystemLifecycleManager_3_GetActiveSubsystemInstance_m7397873372ADDCD59F9F8358FB3D79864CA9CAEE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SubsystemLifecycleManager_3_set_subsystem_mEDE7AFDBFA51FE51987BFE2D68E4232769A53EAF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SubsystemLifecycleManager_3_EnsureSubsystemInstanceSet_mE1D010E8F1616F8E4585FFB40B71CA2B16F54912 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SubsystemLifecycleManager_3_OnBeforeStart_m5A7169E6634C968DC793EDA5599C846E701800B7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SubsystemLifecycleManager_3_OnAfterStart_mA31EF8FDA577088D846FFD069CAEE240039CBC77 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t63DA32228D1B78DA365C4AC6A25169789E69124B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1__ctor_m8EAB6CEFF8E6DFFA31609CAFA423E689B463D497 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_SubsystemLifecycleManager_3_tB495CB9CAAC08A090DC3E365B5430D9551C852A0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t724486D869FD403F3936702B9ED8E6CFC489C8DE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1__ctor_mD89A8298AD4FB5EB1E759FDA2B30F186DF676FAB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TrackableCollection_1_t84E580845660ECFC69BC4C577FA6A5EC9869C413 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Dictionary_2_t553EA056C4435F165EBF046EEDF34553C7FDBBF3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t6A70F87CCB5A7CBE666E84E0E30D9338EAA3227D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator__ctor_m506CE2E27F1C4C56BAA911CE4CC814A9E7C82D93 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_get_Count_mF2EA4D918C0F3E3835BECFA402AF8A50394620B7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_get_Item_m856D9F7CCD2B619266A68EE33665DFB9C924547B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TTrackable_tFF210CC5FBA40BF6425EAE2987D6BDBA34F47F45 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TrackableCollection_1_Equals_mE82810EF01BF30960F8034C657850F6C9968B4CD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TrackableCollection_1_t84E580845660ECFC69BC4C577FA6A5EC9869C413 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TTrackableU26_t8D819306250428B4BEACE65EF2340E1F85F24A47 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_TryGetValue_m6F0F07522379669634AFBF1A39ECB065BC49CECE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Dictionary_2_t79D760AB6F7C618D9201DEBE364C082BA61D800D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_GetEnumerator_m94652D34881801AD1214517D8906BBA11896BCB1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_tA582FFC4C899A8805C484533142EDF7E6BD61E18 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t3A49019B09D3BE7D7A0CD900532C68B79E351E84 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_MoveNext_mEC2AEEDA4CF40AA8379C53855A44F019721DB037 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_tA582FFC4C899A8805C484533142EDF7E6BD61E18 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_get_Current_m5190A263373FB0008A3AA284FDB9062611EF9C9D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_KeyValuePair_2_t90DC86E5EFFD2F85688663A64600E4C8DE32074C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_KeyValuePair_2_get_Value_m286FE25E1C6A797990C9758C7B41D035D225A90D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_KeyValuePair_2_t90DC86E5EFFD2F85688663A64600E4C8DE32074C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TTrackable_tC4BDB5E70816B97EF820497542BB4C6895556DCE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_Dispose_m3B07DA698622B8157A7748200ED296A22D374108 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_XR_ARFoundation_CodeGenModule;
const Il2CppCodeGenModule g_Unity_XR_ARFoundation_CodeGenModule = 
{
	"Unity.XR.ARFoundation.dll",
	1085,
	s_methodPointers,
	232,
	s_adjustorThunks,
	s_InvokerIndices,
	1,
	s_reversePInvokeIndices,
	9,
	s_rgctxIndices,
	152,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
