using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class AnimController : MonoBehaviour
{
    public GameObject body;
    public GameObject kidney;

    public GameObject kidneyArteria;
    public GameObject grenalDer1;
    public GameObject grenalDer2;
    public GameObject kidneyDisectionIz;
    public GameObject kidneysCompleteLeftFront;
    public GameObject kidneysCube2; // main one 
    public GameObject kidneysCube22; 
    public GameObject vena;


    public GameObject stones;

    public ObjectAnimator objectAnimator;

    private bool kinAnimDone = false;

    //public GameObject pulseAnim;


    [SerializeField]
    private AudioPlayer audioPlayer;

    [SerializeField]
    private AudioSource audioSource;



    void OnEnable()
    {
        // Subscribe to the event
        PressStartContoller.onStartButtonPressed.AddListener(StartKinAnim);
    }

    void OnDisable()
    {
        // Always unsubscribe when disabled
        PressStartContoller.onStartButtonPressed.RemoveListener(StartKinAnim);
    }



    void Start()
    {
       
        Screen.sleepTimeout = SleepTimeout.NeverSleep;

        //pulseAnim.SetActive(false);
    }
     
    
  

    public void StartKinAnim()
    {

        audioPlayer.PlaySound();
        Invoke("DelayedStartCoroutine", 50.0f);


       
    }




    private void DelayedStartCoroutine()
    {
        if (kinAnimDone == false)
        {
            Debug.Log("Starting animation!");
            objectAnimator.StartAnimation();
            kinAnimDone = true;

        }



        StartCoroutine(CallFunctionsSequentially());
    }

    IEnumerator CallFunctionsSequentially()
    {
        yield return StartCoroutine(CallFunctionWithDelay(TurnOffGrenalDer1, "TurnOffGrenalDer1", 4.0f));
        yield return StartCoroutine(CallFunctionWithDelay(TurnOffGrenalDer2, "TurnOffGrenalDer2", 2.0f));
        yield return StartCoroutine(CallFunctionWithDelay(TurnOffkidneysCompleteLeftFront, "TurnOffkidneysCompleteLeftFront", 2.0f));
        yield return StartCoroutine(CallFunctionWithDelay(TurnOffKidneysCube22, "TurnOffKidneysCube22", 2.0f));
        yield return StartCoroutine(CallFunctionWithDelay(TurnOffKidneyArteria, "TurnOffKidneyArteria", 2.0f));
        yield return StartCoroutine(CallFunctionWithDelay(TurnOffKidneyVena, "TurnOffKidneyVena", 2.0f));
        yield return StartCoroutine(CallFunctionWithDelay(TurnOnShockWave, "TurnOnShockWave", 2.0f));


    }




        IEnumerator CallFunctionWithDelay(System.Action function, string functionName, float secs)
    {
        Debug.Log($"Waiting 2 seconds before calling {functionName}...");
        yield return new WaitForSeconds(secs);  // 2 second delay using Unity's WaitForSeconds
        Debug.Log($"Executing {functionName}");
        function();
    }



    public void TurnOnShockWave()
    {

        //pulseAnim.SetActive(true);
    }

    public void TurnOffGrenalDer1()
    {
        grenalDer1.SetActive(false);
    }

    public void TurnOffGrenalDer2()
    {
        grenalDer2.SetActive(false);

    }


    //public void AnimTransOfKidtube()
    //{
    //    Material myMaterial = kidneysCube2.GetComponent<Renderer>().material;
    //    MaterialAlphaAnimator.AnimateMaterialAlpha(myMaterial, 1f, 0.3f, 1f, () => {
    //        Debug.Log("Fade complete!");
    //    });
    //}


    //public void TurnOffKidneyDisectionIz()
    //{
    //    kidneyDisectionIz.SetActive(false);
    //}

    public void TurnOffKidneysCube22()
    {

        kidneysCube22.SetActive(false);
    }

    public void TurnOffKidneyArteria()
    {
        kidneyArteria.SetActive(false);
    }


    public void TurnOffKidneyVena()
    {
        vena.SetActive(false);
    }


    public void TurnOffkidneysCompleteLeftFront()
    {
        kidneysCompleteLeftFront.SetActive(false);
    }



   

}
