﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mAE7D02B3CDEA9E9B7FC86BFBBFDF11AE909326C0 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m21A44C012FE21C3D670077F5ACA3BC10CDCF70D3 (void);
extern void ActivationMixerPlayable_Create_mB03A5A5C425D4F901ED4B55E89799A5D207EEC47 (void);
extern void ActivationMixerPlayable_get_postPlaybackState_mF0384E7535C7AEA8617EADC8D9832D8C5CC46D46 (void);
extern void ActivationMixerPlayable_set_postPlaybackState_m5FDC121E23D90F62C75843C5A363DE97E7DD2EB3 (void);
extern void ActivationMixerPlayable_OnPlayableDestroy_mE80DBAA8FAFBA511669B4E78B55751AA5898B074 (void);
extern void ActivationMixerPlayable_ProcessFrame_mDCC3B223FA8AB796778E2E8B979A227BE8DB588C (void);
extern void ActivationMixerPlayable__ctor_m513A8FE0373D3B5A4FFC829ED4DB38735128582E (void);
extern void ActivationPlayableAsset_get_clipCaps_mF49360399D36AEA60F85A01905F9CC9176F32FA2 (void);
extern void ActivationPlayableAsset_CreatePlayable_m5A7D557E44615A717158ADB9CAFAD5C1FA70279A (void);
extern void ActivationPlayableAsset__ctor_m367911F439EC602657B306B2F180B46D959A87B3 (void);
extern void ActivationTrack_CanCompileClips_mA606603F95C9516112052DD4B17136499DE42193 (void);
extern void ActivationTrack_get_postPlaybackState_m1294D74ED475B43256FC9AEB2AB2DEDC2019FB82 (void);
extern void ActivationTrack_set_postPlaybackState_m25320A2912F8EF46DEDDE08963D837CA3B1E55CE (void);
extern void ActivationTrack_CreateTrackMixer_mD0C559021DDE8B870EE86BCA9BE59DF1FB54C38F (void);
extern void ActivationTrack_UpdateTrackMode_mD01BE7C19739DBC651C645F5AD11FB6DAF5EBB1E (void);
extern void ActivationTrack_GatherProperties_m3808D2F7F5C1FAE282906617F42A80448A33F1FA (void);
extern void ActivationTrack_OnCreateClip_m14768AEC5F93456C8BB50050D14D09DF68EDD703 (void);
extern void ActivationTrack__ctor_mC854FF5136E9975AF73188924C9A939FA1B973C4 (void);
extern void AnimationOutputWeightProcessor__ctor_m474D5FD59685D4A13DE5307630091A0414E91E0F (void);
extern void AnimationOutputWeightProcessor_FindMixers_m82DA2CCB4F1B0AA24E3B8CFAB6DDEC8176EE2F65 (void);
extern void AnimationOutputWeightProcessor_FindMixers_mF9681DE5BB91778AFCF883E4732DA6BE1A31A43D (void);
extern void AnimationOutputWeightProcessor_Evaluate_m3C0568570541A5BD6B6405B49B72CCE50714BB17 (void);
extern void AnimationPlayableAsset_get_position_mC75DAA87C62AE6BC462492531727C2E45EC150F0 (void);
extern void AnimationPlayableAsset_set_position_mF1539D8E2BCDC4BFABF2D1683EAD9AE7A91B4DE3 (void);
extern void AnimationPlayableAsset_get_rotation_m1DBB59B5F15442C5CEA8F76CA8B23398459D32D8 (void);
extern void AnimationPlayableAsset_set_rotation_mEE464480D66C9C564AC035507554C340E013EB8C (void);
extern void AnimationPlayableAsset_get_eulerAngles_m1FAFD32D61627D433AC648EDB670431434372123 (void);
extern void AnimationPlayableAsset_set_eulerAngles_mEFBE1E1450804D8A5C2C44D276178B01CBA74F8A (void);
extern void AnimationPlayableAsset_get_useTrackMatchFields_m3CA7C4CD0E3AE6B315028847FD86485139BB1DB2 (void);
extern void AnimationPlayableAsset_set_useTrackMatchFields_m020978DC261F7FBA5BE26BF2E3CCFFE79A7DA27C (void);
extern void AnimationPlayableAsset_get_matchTargetFields_m65CDF95B5D21B931336CDD663309ADC226C666F6 (void);
extern void AnimationPlayableAsset_set_matchTargetFields_mCF18FDE6E3EA660995C822B5678EBB98D374FDB7 (void);
extern void AnimationPlayableAsset_get_removeStartOffset_m38878C3AEB0FF2C0ABAEC4CF6ACA7E640CF896F9 (void);
extern void AnimationPlayableAsset_set_removeStartOffset_mB16879678AA070E3D8A4C58FBA1267867E20B420 (void);
extern void AnimationPlayableAsset_get_applyFootIK_m3D85EDBC855694E23CA22044F83F3EA9918FF6A7 (void);
extern void AnimationPlayableAsset_set_applyFootIK_m2894B7F3E32B0CB12961AEC079B464F5F3195006 (void);
extern void AnimationPlayableAsset_get_loop_mFB0E127EC34FDCB9E706B864733FB45580281A81 (void);
extern void AnimationPlayableAsset_set_loop_mC3AF990F0EDBFF52B6D0C17234144B1999471B6E (void);
extern void AnimationPlayableAsset_get_hasRootTransforms_m74DF6202F2F64346F199101D528591FF02AFE48C (void);
extern void AnimationPlayableAsset_get_appliedOffsetMode_m6A5F22FBF4719ADE8C78BB1A13397E5BA186F908 (void);
extern void AnimationPlayableAsset_set_appliedOffsetMode_m389C789650423968CF01A0830766D2346F2202D1 (void);
extern void AnimationPlayableAsset_get_clip_m0170771CEBEA44040F1857332846B5E12183AF8E (void);
extern void AnimationPlayableAsset_set_clip_mF2E641EB15E80990B4587625F199F681FBFAE752 (void);
extern void AnimationPlayableAsset_get_duration_m7FC1BA9D27EAFC31E19C71EB0AF485A955D8E615 (void);
extern void AnimationPlayableAsset_get_outputs_mD795879A17D0640EDFB055B39B7C832136E216DA (void);
extern void AnimationPlayableAsset_CreatePlayable_mAC380311FD71AB8C793327307A1BCBE8CF152F29 (void);
extern void AnimationPlayableAsset_CreatePlayable_m8B11A8EAC431FCD5E878FABCB683A4F9B74A6998 (void);
extern void AnimationPlayableAsset_ShouldApplyOffset_m7D958577429B51630CAD52246A957F9BD850119D (void);
extern void AnimationPlayableAsset_ShouldApplyScaleRemove_m99FB3C3471F9B6C7A5E595308EF4ABCDB2E63841 (void);
extern void AnimationPlayableAsset_get_clipCaps_m2BAF77E319A9EE382FF20AB13A56907312C064F8 (void);
extern void AnimationPlayableAsset_ResetOffsets_m8DC6D73742DA551BB81E5BDAAC2F186D045D0EC4 (void);
extern void AnimationPlayableAsset_GatherProperties_mB16CF0B019B7CF063D76CA4B69A51C227EB24EF0 (void);
extern void AnimationPlayableAsset_HasRootTransforms_mF8339B890A9F6875852E02AA6FE7EBA7F2CE8161 (void);
extern void AnimationPlayableAsset_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_mE7288B1EA8E582C2661277C75FDE80D8B6CFEBC1 (void);
extern void AnimationPlayableAsset_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_mA584D2BD183C657C1DB990C055CE79E3880C88E2 (void);
extern void AnimationPlayableAsset_OnUpgradeFromVersion_mE19EC4FA01C0D93E8FAC4E4D56FC3B5DBB2C2354 (void);
extern void AnimationPlayableAsset__ctor_m6F298BFCD42F32F57484C3329B147586ECBF141B (void);
extern void AnimationPlayableAsset__cctor_m1748B5EB189D81582B97A7BBD370D03960F1FD7D (void);
extern void AnimationPlayableAssetUpgrade_ConvertRotationToEuler_mB851991DE0D6624E9C49487DFA8630FA6AC08491 (void);
extern void U3Cget_outputsU3Ed__45__ctor_m0E2DA3AD05663EA90B980D7150DDAD589F0336DA (void);
extern void U3Cget_outputsU3Ed__45_System_IDisposable_Dispose_mA7F31F3D37807CE6BA6B290B62C93BEE16B0A6D7 (void);
extern void U3Cget_outputsU3Ed__45_MoveNext_mF982F762F9D3069822C9C69545FA6E439F6F1DC8 (void);
extern void U3Cget_outputsU3Ed__45_System_Collections_Generic_IEnumeratorU3CUnityEngine_Playables_PlayableBindingU3E_get_Current_m7E523BBDF4F714C6737AC3D2709BB75FCF0ED93E (void);
extern void U3Cget_outputsU3Ed__45_System_Collections_IEnumerator_Reset_mC8FF472CBE1E59FE4E740083CA6D09ACF0354C0F (void);
extern void U3Cget_outputsU3Ed__45_System_Collections_IEnumerator_get_Current_mBFDCDFF0341D56EEF2ACE33B8504A53CECE1F7AE (void);
extern void U3Cget_outputsU3Ed__45_System_Collections_Generic_IEnumerableU3CUnityEngine_Playables_PlayableBindingU3E_GetEnumerator_m89F51E97B14897204A10EB33B1DF48AD64260492 (void);
extern void U3Cget_outputsU3Ed__45_System_Collections_IEnumerable_GetEnumerator_m571AD1499FE97BA88AC1996D6DBCB5FB81EA6B64 (void);
extern void AnimationPreviewUpdateCallback__ctor_m9A81A41A73B25FFB193B05DB41AAD480B5019302 (void);
extern void AnimationPreviewUpdateCallback_Evaluate_m4859735BDA333DF67BA53C5157909003103DC4FF (void);
extern void AnimationPreviewUpdateCallback_FetchPreviewComponents_mF1C66A832544073C8DFA760DB6F1910DED0BE2D2 (void);
extern void MatchTargetFieldConstants_HasAny_m73744E801BA5F758254854BF5283EE4A8BFA13B4 (void);
extern void MatchTargetFieldConstants_Toggle_m649C32A5427F1B9959B3D58468956DF2FBE237DD (void);
extern void MatchTargetFieldConstants__cctor_mC761D8CE72745FF29695B219E52D3AF62D2DC5BC (void);
extern void AnimationTrack_get_position_mBF745D40410A1F530116319EDA2ED612851C863E (void);
extern void AnimationTrack_set_position_m58583D926C865297D1D248FF3DB33E9A101115F6 (void);
extern void AnimationTrack_get_rotation_m5A3EC08F5E1AD62FC72BA69294001BAC42C15251 (void);
extern void AnimationTrack_set_rotation_m372BEF805EE2FEA03E5766C853CD934FADAF6FFD (void);
extern void AnimationTrack_get_eulerAngles_m9EDE81D8FD7675470DF4CFA620D911019B56C768 (void);
extern void AnimationTrack_set_eulerAngles_mCF06238E5C41BE05161163DCA33ED817BF1E24CD (void);
extern void AnimationTrack_get_applyOffsets_m10D70F0C85FB9C462B4659813EDB37BF84D86B9A (void);
extern void AnimationTrack_set_applyOffsets_mF644A614EB6FB30B8604D917BA8F4A710983A9DE (void);
extern void AnimationTrack_get_trackOffset_m8D91B82F85D98F276E45FB805628232278AF2D2D (void);
extern void AnimationTrack_set_trackOffset_m66B617D407D7D8CE17EDAD849DEC34FD1E0B1CE5 (void);
extern void AnimationTrack_get_matchTargetFields_m130ABF7AA0D2F38D40127029CA02993A5FA998F3 (void);
extern void AnimationTrack_set_matchTargetFields_m14ED03793080CDD4BDF4D1B91418C430853D9330 (void);
extern void AnimationTrack_get_infiniteClip_mE55BF71562803B5C849A335584AE526A2747E5B2 (void);
extern void AnimationTrack_set_infiniteClip_mA700EED208399A4DCA3314E3F848BBE698DAB6C4 (void);
extern void AnimationTrack_get_infiniteClipRemoveOffset_m18DFC76B9AFEC35654AF299A62264E925202146B (void);
extern void AnimationTrack_set_infiniteClipRemoveOffset_m7999F05C17EE0524CCC197732CADB8927A0D5454 (void);
extern void AnimationTrack_get_avatarMask_m4E6BD1DAC9D92FD6CD663DADEE525A158CFE4F01 (void);
extern void AnimationTrack_set_avatarMask_m97BFF1B75D7E891C249CCE0917F1CBCB886FD30D (void);
extern void AnimationTrack_get_applyAvatarMask_m2FCA8FA363B449E4420E8492F92E5270A2D4FA7E (void);
extern void AnimationTrack_set_applyAvatarMask_mEAEA18A18FB74A33D808B94F20C01A2D880A8606 (void);
extern void AnimationTrack_CanCompileClips_m67F41F2F20E51CB999957ACD3D9C31B799EC3026 (void);
extern void AnimationTrack_get_outputs_mF3776BF93312A616ADC9D18F061AD972626F0EB7 (void);
extern void AnimationTrack_get_inClipMode_m0559987652D8B45851CF6EE37AAE259156007271 (void);
extern void AnimationTrack_get_infiniteClipOffsetPosition_m23355191742B9EF9877193DCD3C77758D9435666 (void);
extern void AnimationTrack_set_infiniteClipOffsetPosition_m74A78994C38BE22C6AD33C5EF4CD710CCAFDD166 (void);
extern void AnimationTrack_get_infiniteClipOffsetRotation_m3A152B45D0F99F716EEFA25BDF0A932FACB02052 (void);
extern void AnimationTrack_set_infiniteClipOffsetRotation_m0B8FF5AB8D6F4EE92C022D40510C5C41AD3896C2 (void);
extern void AnimationTrack_get_infiniteClipOffsetEulerAngles_m0EF73054860F1F705E07F5729C9CB1CE0D4AA6B9 (void);
extern void AnimationTrack_set_infiniteClipOffsetEulerAngles_m958891CA74BDFD73A39E2427084FA3777939C29E (void);
extern void AnimationTrack_get_infiniteClipApplyFootIK_m813FEE3250C60BFEF060210215BD68E3C5CC29CB (void);
extern void AnimationTrack_set_infiniteClipApplyFootIK_mCC194F75CD06FA7E9F0EE890E7A2AADA9C387BAB (void);
extern void AnimationTrack_get_infiniteClipTimeOffset_mC4376105C76EAB9E8139C426D3BD218ED21B4A7C (void);
extern void AnimationTrack_set_infiniteClipTimeOffset_m93F31C8AEFE3DE5FE275F7EB250FA98F5978AD3A (void);
extern void AnimationTrack_get_infiniteClipPreExtrapolation_m4B4906080C16E2D7C07B409787363735F8C8AB85 (void);
extern void AnimationTrack_set_infiniteClipPreExtrapolation_m5A1D2B6511FB01EEDF0405DB5914F43D1289EDE3 (void);
extern void AnimationTrack_get_infiniteClipPostExtrapolation_mE68867202ACB337F11C12FD75C196278FFEA34D9 (void);
extern void AnimationTrack_set_infiniteClipPostExtrapolation_m8197868300D197E73BFB72D95EEC0CA3F3E69D6E (void);
extern void AnimationTrack_get_infiniteClipLoop_m0076C92D7935227835C007059EC228D6ACA5205C (void);
extern void AnimationTrack_set_infiniteClipLoop_mE52F35E3A552CD510D873A9A1865D474974B2750 (void);
extern void AnimationTrack_ResetOffsets_m9CA8762D3880419B7CA71E966228FF801A0F47A4 (void);
extern void AnimationTrack_CreateClip_mC2014488815D2C34B95F739D82F9A93A195FD919 (void);
extern void AnimationTrack_CreateInfiniteClip_mC2EB76F9F2462F3AED209E90018A86DE5D36843B (void);
extern void AnimationTrack_CreateRecordableClip_m2F49C361C87418375F803A4760EDF011A9D960C7 (void);
extern void AnimationTrack_OnCreateClip_m5A7462347F4CAA6EC0B248F534F45C89B8F38786 (void);
extern void AnimationTrack_CalculateItemsHash_m69D9B8299B8E4085DE184A3FC278BB33C6CECF70 (void);
extern void AnimationTrack_UpdateClipOffsets_m0DE8CEBC4F96E1F40669DC8732F2E04EBADA1331 (void);
extern void AnimationTrack_CompileTrackPlayable_m8AEA486513BF42BC4682217FCB37A61218B68094 (void);
extern void AnimationTrack_UnityEngine_Timeline_ILayerable_CreateLayerMixer_m2C90244DFA117930E0911876688435A62CF9C628 (void);
extern void AnimationTrack_CreateMixerPlayableGraph_mC3352DAB3A0AF3D277C78B94D5DC2DB763DFD3F7 (void);
extern void AnimationTrack_GetDefaultBlendCount_m34C24E878EB89D3D5B56AECED494D8375E35A64D (void);
extern void AnimationTrack_AttachDefaultBlend_mAF67FD6982E9EAA8CAFCB0106C7F324547400DFC (void);
extern void AnimationTrack_AttachOffsetPlayable_m854FE97C64D490D94150A604554C01A9433813B8 (void);
extern void AnimationTrack_RequiresMotionXPlayable_m8BF0F53DE406934214E0E402295F8E432E02744C (void);
extern void AnimationTrack_UsesAbsoluteMotion_m305667FA27FD4DFC54406F5A21A4344A069EAF4C (void);
extern void AnimationTrack_HasController_m9DA394CFD76B80E53A80EC26FEEC9F1769BA6684 (void);
extern void AnimationTrack_GetBinding_mFA6516C67603F256A7306024BB0DA86496F42B43 (void);
extern void AnimationTrack_CreateGroupMixer_mF2B248685103E2FBA973109155FE0D5153946B8A (void);
extern void AnimationTrack_CreateInfiniteTrackPlayable_m7951EDDA36CE14693AFFF9C460C0FC3E5B732A88 (void);
extern void AnimationTrack_ApplyTrackOffset_m7ACA50839B41B3AECEA66E60986AE71E9FA3F7A0 (void);
extern void AnimationTrack_GetEvaluationTime_mE2271656B917685A4108E734F77ED34FC75EC3AD (void);
extern void AnimationTrack_GetSequenceTime_m366C5486DCF37C3A41612B08749F063F491ED593 (void);
extern void AnimationTrack_AssignAnimationClip_m26DB250244CE6240EF4F946A4D93F311D0D1F367 (void);
extern void AnimationTrack_GatherProperties_m5FE1175C9AE614E6878B582CEF3DE396837B54C2 (void);
extern void AnimationTrack_GetAnimationClips_m7459AE8A7BE31D25B0625297A0C94173042A0207 (void);
extern void AnimationTrack_GetOffsetMode_m8D2EA6334F72FE079612C5B71B745CAF91111C9D (void);
extern void AnimationTrack_IsRootTransformDisabledByMask_m130CFADD77EB830E9E7EA6DBBE88EFEE73E1D0C7 (void);
extern void AnimationTrack_GetGenericRootNode_m40F3B83DE0D88C67592549EBDFADCD0168D63C3A (void);
extern void AnimationTrack_AnimatesRootTransform_m2D168DCB100F72EFC128C2DEC6B068E2CC216F1F (void);
extern void AnimationTrack_FindInHierarchyBreadthFirst_m021FFC49FDED402415A469E33CC690FFCC7AD1CB (void);
extern void AnimationTrack_get_openClipOffsetPosition_m75E1CFC9D51546A9F24A0EB998B6E87794C3A705 (void);
extern void AnimationTrack_set_openClipOffsetPosition_m9752610280CE6027BAEE683C36C9D290B442A6DB (void);
extern void AnimationTrack_get_openClipOffsetRotation_m10B5894DD11A0AF08D99594D1FB459B6362B9303 (void);
extern void AnimationTrack_set_openClipOffsetRotation_m1D1B9B56375DE34C8BEA905DCC0299B39BFC7B02 (void);
extern void AnimationTrack_get_openClipOffsetEulerAngles_m355BD7957BCD76AC87769CED0927FD9728C3F2EA (void);
extern void AnimationTrack_set_openClipOffsetEulerAngles_m562570438DFB431C65AB322D1753D6210DB62787 (void);
extern void AnimationTrack_get_openClipPreExtrapolation_m27E1C2DE521D2F5B0AB5569695CC1C060416FDB4 (void);
extern void AnimationTrack_set_openClipPreExtrapolation_mC2D775230A229A66B1E98C131FAE66C2312E4BC3 (void);
extern void AnimationTrack_get_openClipPostExtrapolation_m81FD4E8B992C6758D21790E77A57DAFB2CFB903A (void);
extern void AnimationTrack_set_openClipPostExtrapolation_m1C35F5DC4A86ADA4548AFAFA8323A118433B91FC (void);
extern void AnimationTrack_OnUpgradeFromVersion_mB41D3B6A330F4AA2FE59E61632E4B1F99C2866DF (void);
extern void AnimationTrack__ctor_mDB5857315630BBECBD1CD59097456E0F331B9264 (void);
extern void AnimationTrack__cctor_mF7E0C93F99D88D2E284763DB25C116DA566C1135 (void);
extern void AnimationTrackUpgrade_ConvertRotationsToEuler_m4ACA9D918164FEEDBEC2881DE13C779094D253D5 (void);
extern void AnimationTrackUpgrade_ConvertRootMotion_m54DF7747856D06D2179EB95642D299E5D6A97A21 (void);
extern void AnimationTrackUpgrade_ConvertInfiniteTrack_m4006E7259789D3B8829ABC5700DD2828C3EB2A6F (void);
extern void U3Cget_outputsU3Ed__49__ctor_mD93688EDAA61ADD8474DC977257C4865A2EC6389 (void);
extern void U3Cget_outputsU3Ed__49_System_IDisposable_Dispose_m607FCCC43C82ECC93F674CCB5415929336502C51 (void);
extern void U3Cget_outputsU3Ed__49_MoveNext_m7BDA8087249FD461975437E56C9BFE7B80C8A099 (void);
extern void U3Cget_outputsU3Ed__49_System_Collections_Generic_IEnumeratorU3CUnityEngine_Playables_PlayableBindingU3E_get_Current_m8665A050400E9DA6702260117B51F8609806ECBE (void);
extern void U3Cget_outputsU3Ed__49_System_Collections_IEnumerator_Reset_m5D524429CEE49B55F5EE0E3015806B835110C113 (void);
extern void U3Cget_outputsU3Ed__49_System_Collections_IEnumerator_get_Current_mF8DC66EBBBD2A455C22F3B8B229EE3FC09ABC96F (void);
extern void U3Cget_outputsU3Ed__49_System_Collections_Generic_IEnumerableU3CUnityEngine_Playables_PlayableBindingU3E_GetEnumerator_m3B62F5C3F8AF686D249000D59B0052197BECB161 (void);
extern void U3Cget_outputsU3Ed__49_System_Collections_IEnumerable_GetEnumerator_mFC98EDB93155902AC6052BEAEBF678837BD62B2C (void);
extern void TimelineClip_UpgradeToLatestVersion_m3C367DA0661298065465C583C9D33AC4B63ADF0D (void);
extern void TimelineClip__ctor_m69070FAC237B4E3DC873E07AD1C3902E5C031939 (void);
extern void TimelineClip_get_hasPreExtrapolation_m2D086ECAC0C10D46FE58D22AEAA9CC1EAE94C196 (void);
extern void TimelineClip_get_hasPostExtrapolation_mE6565EF983300E526B31AC25C57F8B583C6B7AC6 (void);
extern void TimelineClip_get_timeScale_mF76C0A2D5CDBF201F2CC01967928CA7FFC261474 (void);
extern void TimelineClip_set_timeScale_m29FAE01EF4CF682E2C82169B9D6976289C2665D0 (void);
extern void TimelineClip_get_start_m76BB53BEBD6B700D5A4197F72779A321DE55B296 (void);
extern void TimelineClip_set_start_m476586FED5274C2691B71BC75C76E3F471332BF5 (void);
extern void TimelineClip_get_duration_m4DC76F051723CC7427813C076B255BA8BB4366F7 (void);
extern void TimelineClip_set_duration_m1904C6BD6C64F8CC7AD09256F559C8C62AB97001 (void);
extern void TimelineClip_get_end_m4C3E4DF4B095A1D60694B379EA839A68E3C4217C (void);
extern void TimelineClip_get_clipIn_m0ABA66BE9CAD32C80313321C963C8FA9AB5FC1EB (void);
extern void TimelineClip_set_clipIn_m5ACCEA54D376384ED1264CE7F108BD64F725C67E (void);
extern void TimelineClip_get_displayName_m61712CDBEA102FB64B5E3464E4420843E1FB7111 (void);
extern void TimelineClip_set_displayName_m5F725FB8B45340748ECFAA870D034C85352F54CD (void);
extern void TimelineClip_get_clipAssetDuration_mC3509BB4EF5397A55A42E02BE6A6730998B5EAF5 (void);
extern void TimelineClip_get_curves_m7572F0ACC8D51E605271B6002685740F946FC761 (void);
extern void TimelineClip_set_curves_mDE58D1A741554AF3E95F7BC53625E5D7F3BD212A (void);
extern void TimelineClip_UnityEngine_Timeline_ICurvesOwner_get_defaultCurvesName_m65DD8FEFE6FC8AADA51EECD8906A9066C7EF6C18 (void);
extern void TimelineClip_get_hasCurves_mDB6EF3ADD8FF4693992F7EA092F269A6F3631EFD (void);
extern void TimelineClip_get_asset_m49BF68F5E0C41EBA5145FCA0C97D7146DF016120 (void);
extern void TimelineClip_set_asset_mF8539EA76B6C0F19E2AECBA025C70D605322195E (void);
extern void TimelineClip_UnityEngine_Timeline_ICurvesOwner_get_assetOwner_m542C93F1E1B0382CCA596359A1A96DAC55539D66 (void);
extern void TimelineClip_UnityEngine_Timeline_ICurvesOwner_get_targetTrack_m46FB66ACADD2B522822EB7691A4A5F1C976A368F (void);
extern void TimelineClip_get_underlyingAsset_m1DE5682815DA82061499CCF642ECDC9CAA9C1881 (void);
extern void TimelineClip_set_underlyingAsset_m02D44F445992B5431831BF7C77466164CA527B85 (void);
extern void TimelineClip_get_parentTrack_m3E33146230902DCAFAEAAE034F5DCBB70BE03A8C (void);
extern void TimelineClip_set_parentTrack_mC3646EF3C4198254776CFA1E47CFC2636C1BA69E (void);
extern void TimelineClip_GetParentTrack_m560CB13E873CE39EB7E3754B53B1594D2A58C37D (void);
extern void TimelineClip_SetParentTrack_Internal_m39F7D49888E7EBFC36796EAE0C7BE6C263C0FB02 (void);
extern void TimelineClip_get_easeInDuration_m171A5C1C8BA1392C4CBC7F5C17A90EAF915A2145 (void);
extern void TimelineClip_set_easeInDuration_mF310C781E91789DAA8AE5A593C9BA46FC173E01C (void);
extern void TimelineClip_get_easeOutDuration_mAAE6DEE05138F6902199D985A3EE6E26BCBB35EC (void);
extern void TimelineClip_set_easeOutDuration_m4666F442BADEAFAC3C8E450BCF987E403D1E42ED (void);
extern void TimelineClip_get_eastOutTime_mE21671B32EE9CB06C994A5B91D5E86741F2BB30B (void);
extern void TimelineClip_get_easeOutTime_mC16D31D13CB3953A0ECE61CCD13381318B6AC115 (void);
extern void TimelineClip_get_blendInDuration_m3A18D7C0942B43C5A171422EDC1D41260C1269D7 (void);
extern void TimelineClip_set_blendInDuration_m34F303216992A919EF47D01B874EEEB47C8470E3 (void);
extern void TimelineClip_get_blendOutDuration_mA1BB945DFE74A6AB5EB112587ED3509CFAF43379 (void);
extern void TimelineClip_set_blendOutDuration_m5013A1CF4593F805143A0E665261EBB0B00298F0 (void);
extern void TimelineClip_get_blendInCurveMode_m4C612BAD55BB180A24C9FA67A8F9D84C907E170C (void);
extern void TimelineClip_set_blendInCurveMode_m4138FBEEA1484C9DDAA0C859B505E8E20B4DFE8A (void);
extern void TimelineClip_get_blendOutCurveMode_m106D929CE951F07CB31BF9DEA7B0D2B1D830F093 (void);
extern void TimelineClip_set_blendOutCurveMode_m5CBC0E1382775395757B85824BFBB39571241A9E (void);
extern void TimelineClip_get_hasBlendIn_m9B9C96AAADD801969D9C97D4130428EEB6B6D368 (void);
extern void TimelineClip_get_hasBlendOut_m8444D00A050FD627EA6A7DC834B5B14B2C845E51 (void);
extern void TimelineClip_get_mixInCurve_mCF8C05F353EF6EF3E115DEC17FA10DB481724B45 (void);
extern void TimelineClip_set_mixInCurve_m53265562D7425F388E7DC93717D8707F816E65EF (void);
extern void TimelineClip_get_mixInPercentage_m9120693B790082B5ED9FC988B3C9B5F06C946B10 (void);
extern void TimelineClip_get_mixInDuration_m754EA2369F50687A58E3A883DBA5DA79C090B243 (void);
extern void TimelineClip_get_mixOutCurve_mAA470F75E97A1960E50BFA0B97E4BC2E0873D687 (void);
extern void TimelineClip_set_mixOutCurve_m93EA2EF70C31BC5625A419E6830AFB6DD594A729 (void);
extern void TimelineClip_get_mixOutTime_mD780BF14E8EE91F808CEB62BEE0A93B30C6233A6 (void);
extern void TimelineClip_get_mixOutDuration_m19A9A249A1628E222E534662B0D4CB30F4DBCB0E (void);
extern void TimelineClip_get_mixOutPercentage_m4D9124E362272E0DA49CA228C72E5CEEF39BB3CB (void);
extern void TimelineClip_get_recordable_m2BE0E30776FECD30BAE62D78399B6402682B2001 (void);
extern void TimelineClip_set_recordable_m38B678C027026275DD27636B8AF8345D94D9403A (void);
extern void TimelineClip_get_exposedParameters_mB96B6748027997A9DFEF943387DD86F74876B279 (void);
extern void TimelineClip_get_clipCaps_m11FD6AE29AD801B99B64A71C6C76680FA213FC0A (void);
extern void TimelineClip_Hash_mA9299778B1C618795049AB37ADD4A01A7E5331DE (void);
extern void TimelineClip_EvaluateMixOut_mA36406D289DE5A9831B821AC5A9C51DF21B47C83 (void);
extern void TimelineClip_EvaluateMixIn_m206FBF82C1AF116724AC429CAF7C4759E6D28D60 (void);
extern void TimelineClip_GetDefaultMixInCurve_m90A35726DC5817536F73783D5B1028F64251EDE4 (void);
extern void TimelineClip_GetDefaultMixOutCurve_m07738400AEE1FB893C14562EF2EE11142E767AE6 (void);
extern void TimelineClip_ToLocalTime_m975B84C7F3371F39F73AD8DA1F89C3C825D40E1E (void);
extern void TimelineClip_ToLocalTimeUnbound_m8E307B2FDD49E30A57137FD94BA001B9157DEA6F (void);
extern void TimelineClip_FromLocalTimeUnbound_mBCE77F0E508F86A77DE09CE16623A6FB374700E1 (void);
extern void TimelineClip_get_animationClip_mFDFBA4582868E85C0B56242660C9EF3B4442D07D (void);
extern void TimelineClip_SanitizeTimeValue_m3185743B561388628273A3FBD9AD1C7C4AD3A9DE (void);
extern void TimelineClip_get_postExtrapolationMode_mDF06AB6BEE7906C373109C04775C0B3CA83D8685 (void);
extern void TimelineClip_set_postExtrapolationMode_mD2B003EB90D2C816050F5FF12B662401BB858092 (void);
extern void TimelineClip_get_preExtrapolationMode_mC20AE951A26D337093ABC65FD2DBF9C5CBE8E3FA (void);
extern void TimelineClip_set_preExtrapolationMode_m26E6222B67AFA46CC6F1EE1730C8819B19D035AF (void);
extern void TimelineClip_SetPostExtrapolationTime_m659389D7F8F07AE58FF2D1B4BCFAB9FF13691C35 (void);
extern void TimelineClip_SetPreExtrapolationTime_mE9DAF291C193942B0703ADB10CA1BEF1B9D0C4DA (void);
extern void TimelineClip_IsExtrapolatedTime_m82974EEE2D3C650B90C2F5F5592FB31B7029AB6C (void);
extern void TimelineClip_IsPreExtrapolatedTime_mCA79ABD6E8F0408DA16E8BB57C9509BB092CC753 (void);
extern void TimelineClip_IsPostExtrapolatedTime_m2C8FFEF3F821D6CD45A7D8F5E06F3E7705C6B1FE (void);
extern void TimelineClip_get_extrapolatedStart_m4092C97F1EB880583FA92B8FAAFC208D245630BF (void);
extern void TimelineClip_get_extrapolatedDuration_mFA3957094871DA9151A26AB04A894E771FFC1F21 (void);
extern void TimelineClip_GetExtrapolatedTime_m10D665C5B1EC5CBCF3FC38E10BE6A2276D5E7405 (void);
extern void TimelineClip_CreateCurves_m494AE0E2457ACD87387E039AF081F93442FACBB3 (void);
extern void TimelineClip_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_mDC9EFB3125995E8EFA91EB3CF27E9B2784B60DE8 (void);
extern void TimelineClip_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_mE0860037B34E43C2CB25567793EDB4F48B018D4E (void);
extern void TimelineClip_ToString_m56D472178DA2F04872969618EF5CA3E142E9FD9A (void);
extern void TimelineClip_ConformEaseValues_m6774B17BD4E1E6734FC21CB1F67C267DF92DD623 (void);
extern void TimelineClip_CalculateEasingRatio_mE7960562253BE9447E532E1BA3709AF9B0834D75 (void);
extern void TimelineClip_UpdateDirty_m3F5A0077C88F2C9D481AE93CEF2E16DCD235EC08 (void);
extern void TimelineClip__cctor_mD74B4E06C1F4905731E8C1E06F0BB24BCED88317 (void);
extern void TimelineClipUpgrade_UpgradeClipInFromGlobalToLocal_m59F498F944599E3F1BAF6B0D5AC37E5E38770E7C (void);
extern void TimelineAsset_UpgradeToLatestVersion_m34BA46E2D4643EFB179CBFB81706EC27785CBFA0 (void);
extern void TimelineAsset_get_editorSettings_m3D5DEC0305D9E517E29CB898C789C3B42D3B1CB9 (void);
extern void TimelineAsset_get_duration_m22226DF293F2D489E63D881775112FC26518E297 (void);
extern void TimelineAsset_get_fixedDuration_mB9B8F8FBE5DCF918C17AC705E6D17A730DB9A69C (void);
extern void TimelineAsset_set_fixedDuration_m10FF2CD4C12508C9F4938A17B2EF7E81A5C701AB (void);
extern void TimelineAsset_get_durationMode_m58895CD3D78D4F4A912BD149FD810B3AE6AA0034 (void);
extern void TimelineAsset_set_durationMode_mB082ACE2157EF8472C2C30C07DAE8DDCC6E9008D (void);
extern void TimelineAsset_get_outputs_m232A9F7BCE2E4BCFB2115B9ABB26D53C7D8C478E (void);
extern void TimelineAsset_get_clipCaps_m0F3ACD23FAE67D92F0F67A056F0AC64BEC0A3AA8 (void);
extern void TimelineAsset_get_outputTrackCount_m07CED6FE6B99D62D0E5EB3C2A91863E21E1CB70B (void);
extern void TimelineAsset_get_rootTrackCount_m89C66C83C874A1D60BE068E40C5CB94033F0A7ED (void);
extern void TimelineAsset_OnValidate_m4D4626C6372A467AE9EB04DA835A8CD9EE75EDF0 (void);
extern void TimelineAsset_GetRootTrack_mC41A0CF4127692DDEEBAA8BA973BE4392C49B83A (void);
extern void TimelineAsset_GetRootTracks_m6340C1C261F14F8FBAF7116F775780C75068F05F (void);
extern void TimelineAsset_GetOutputTrack_m8002892E722FE6CA9C8B8EB7D7040BE1E47D672D (void);
extern void TimelineAsset_GetOutputTracks_m324315A337B30921D4B5E740E1FC2BE81563B26F (void);
extern void TimelineAsset_GetValidFrameRate_m5F0BAEC92EDFDBE539E1A0187DC2F185DA8D5E72 (void);
extern void TimelineAsset_UpdateRootTrackCache_mADE060D9C1F785CFA83E43B4CB895D772EF74749 (void);
extern void TimelineAsset_UpdateOutputTrackCache_m4A4249728370EB77115CDCB08D2A08C4D3925818 (void);
extern void TimelineAsset_get_flattenedTracks_m51B0499CC8CD43CB520F6B84D37FCD19C8FE4F18 (void);
extern void TimelineAsset_get_markerTrack_mA1522943FBA0FDFDB0765165F00B6F9373EB01A3 (void);
extern void TimelineAsset_get_trackObjects_mFE1A564170B932E65741C29D0A94B5DA55C7D676 (void);
extern void TimelineAsset_AddTrackInternal_m1699C0B3168FF4D471509AA664AA92BCC3000A57 (void);
extern void TimelineAsset_RemoveTrack_m34E111A2A0D031C27F8EB42DD4CD014A5883ADC2 (void);
extern void TimelineAsset_CreatePlayable_m015AE0523BB6A93F3F0070BF0757ECA3199EB5FE (void);
extern void TimelineAsset_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_m4ED0C35C1033C2177A683A17F97863B2F3A8982F (void);
extern void TimelineAsset_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_m7C5218B558DC68FA00A3A7AC87FAE02F589C2808 (void);
extern void TimelineAsset___internalAwake_m3BDB3D8798B27D100140D4C189285ACCFD116F18 (void);
extern void TimelineAsset_GatherProperties_m966DA31A1B9D51FA370CB0DD4B3FC9036C230EEA (void);
extern void TimelineAsset_CreateMarkerTrack_m959223B301DE70DED79380138F93BF1565D2B586 (void);
extern void TimelineAsset_Invalidate_m601FE157269B617EEB1D840439E36715DC81AFC6 (void);
extern void TimelineAsset_UpdateFixedDurationWithItemsDuration_mE79C311415955F474C4C885E4CF8B7124CBBE008 (void);
extern void TimelineAsset_CalculateItemsDuration_m2904F09544AB814380305EA97D6EB11AFDCFD0BC (void);
extern void TimelineAsset_AddSubTracksRecursive_m03EBD254DE32A5C23CE13E80EFFC4BDC51AD960F (void);
extern void TimelineAsset_CreateTrack_m327D088F33507A544DE566503CDF6593C024C1ED (void);
extern void TimelineAsset_DeleteClip_m61EFBF21AF49C5FE55CD70060C19B81657170B43 (void);
extern void TimelineAsset_DeleteTrack_m80BEC8B29D94214E5663B0F64E725726B7EBAC08 (void);
extern void TimelineAsset_MoveLastTrackBefore_mB1C0BE93519C75B57FC07BCFBCBFA912A61841E4 (void);
extern void TimelineAsset_AllocateTrack_m8DFE21D5BC86310C82E23E43CE3D7D04759EA2F5 (void);
extern void TimelineAsset_DeleteRecordedAnimation_m3FF55ADA0F576F30D7522D85D999E5A2A8824939 (void);
extern void TimelineAsset_DeleteRecordedAnimation_mBE3F874CEAEFD795EC06C74E50F3E007890369C8 (void);
extern void TimelineAsset__ctor_m75D9A08991F60CBFEFDAD23DC01B6A49A4601E4C (void);
extern void EditorSettings_get_fps_m2B7B48B6BBD590B690E91D5C761336A3454796A2 (void);
extern void EditorSettings_set_fps_mC963EB8F7CAFC6A54CBF37278ADFB0311161674F (void);
extern void EditorSettings_get_frameRate_m699A6599212042DC13CB0DB91C7057B9644A0037 (void);
extern void EditorSettings_set_frameRate_mE8F1DFC8C2B4203B48865F22DB7C5309932EB32B (void);
extern void EditorSettings_SetStandardFrameRate_mA67A361C75A563095119DC1E6D20FD455ED1AC1A (void);
extern void EditorSettings_get_scenePreview_m1FF2AB573D0405B69314643070555FFAF997B3C0 (void);
extern void EditorSettings_set_scenePreview_m7996AD908D830ED6DB8D9FF103FE848BA9AEF553 (void);
extern void EditorSettings__ctor_m239B6DF8C75E8C21F4FCA53B668E9C7E64D1A8A3 (void);
extern void EditorSettings__cctor_m4FE2EB891C4975DD9E721DDF925816B926040D89 (void);
extern void U3Cget_outputsU3Ed__27__ctor_mBFD68070C9454051A90D0AA75FB4D2FF2F63466F (void);
extern void U3Cget_outputsU3Ed__27_System_IDisposable_Dispose_m08F64F0AB9B4EFBB4D45A957869E4167332ACD41 (void);
extern void U3Cget_outputsU3Ed__27_MoveNext_mA380099DD3A55548271DBAD6F80E42AA990F778C (void);
extern void U3Cget_outputsU3Ed__27_U3CU3Em__Finally1_m7E65569ADF800B81563544B8D264833E518A3C7C (void);
extern void U3Cget_outputsU3Ed__27_U3CU3Em__Finally2_m85B6B1B2A51FA070A90244EC2076C5E22F7CD920 (void);
extern void U3Cget_outputsU3Ed__27_System_Collections_Generic_IEnumeratorU3CUnityEngine_Playables_PlayableBindingU3E_get_Current_m22E55FE2DC239357C1BEBBB551C332C97F221F31 (void);
extern void U3Cget_outputsU3Ed__27_System_Collections_IEnumerator_Reset_m334D7D0581BE692B7764A7F29E4C53B6BD3D8350 (void);
extern void U3Cget_outputsU3Ed__27_System_Collections_IEnumerator_get_Current_m2F264D817E0F1C5AE475AD9282977DEC88875D14 (void);
extern void U3Cget_outputsU3Ed__27_System_Collections_Generic_IEnumerableU3CUnityEngine_Playables_PlayableBindingU3E_GetEnumerator_m22938CACD8363F1D9EF4B4B7BCC37B3ECF0554C0 (void);
extern void U3Cget_outputsU3Ed__27_System_Collections_IEnumerable_GetEnumerator_m54F9DE0351DB259B654FB5C81A2B12FABB8DA0CE (void);
extern void TrackAsset_OnBeforeTrackSerialize_m5BCB0C082629BB807C2BC9F8E1623AA84EF3CDD8 (void);
extern void TrackAsset_OnAfterTrackDeserialize_mF383ADAF4C3070BFB81F0478237946621E4FC850 (void);
extern void TrackAsset_OnUpgradeFromVersion_m5A0523EA6441B741A60768840C9A6493DB682D22 (void);
extern void TrackAsset_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_m965E10D67C9FEE0BDE0BB2887C4A24B6BDEC755B (void);
extern void TrackAsset_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_m6A13E9CC4CBECD81002067EAD4B9FF875071F0D1 (void);
extern void TrackAsset_UpgradeToLatestVersion_m70FA58294D201156412BA790FD158D2029FDB505 (void);
extern void TrackAsset_add_OnClipPlayableCreate_m7E859C1C550253310D9D6B34427DDAF0325AC228 (void);
extern void TrackAsset_remove_OnClipPlayableCreate_mE0AE4E40FC4B75040AFEB6AF08B55FFA6F72CAEE (void);
extern void TrackAsset_add_OnTrackAnimationPlayableCreate_mCBC06DE4F00D3DFF308397D76B49D4F4C71058B0 (void);
extern void TrackAsset_remove_OnTrackAnimationPlayableCreate_mA022F7131A984ECB7D002069487AC63834290FA1 (void);
extern void TrackAsset_get_start_mA37EAE96A7EB745D0BC538509B78AEDEF05208E1 (void);
extern void TrackAsset_get_end_m283E3BCE09D393EFA56FB26D5AD68326EECFF312 (void);
extern void TrackAsset_get_duration_m3A3B2F6AD1F1945549C459B432CD4E469FAAECEF (void);
extern void TrackAsset_get_muted_mBA0D78639ED5132C019E69AC51A5BE56478C7A66 (void);
extern void TrackAsset_set_muted_m9FCF9772AA42FB22761AA468792BE272258E426E (void);
extern void TrackAsset_get_mutedInHierarchy_m14257BCE9CD51D5A3C086E465F035A7707D666F7 (void);
extern void TrackAsset_get_timelineAsset_m969726B43E66370FB81F1CCB6C012BCAD2B112A8 (void);
extern void TrackAsset_get_parent_m4B5D7DF104388286053C4BE8D5ECB28615FE7DD9 (void);
extern void TrackAsset_set_parent_mE5000EE0C72D1A0E0A6B916B7E0B94F85192D138 (void);
extern void TrackAsset_GetClips_m467A7BE887049F3CC0F411AB220F488D1230FA76 (void);
extern void TrackAsset_get_clips_m033A1CF810A017C6BD6F52190421C1474648BEB8 (void);
extern void TrackAsset_get_isEmpty_m269392C25CDC0E0179FEFD5322476B8BEF7FC86B (void);
extern void TrackAsset_get_hasClips_m552EDF37A12F12705B5A016D9351380BF08D25ED (void);
extern void TrackAsset_get_hasCurves_m0F0AF8869F6E78DB23C129134B11A39E9CA4208E (void);
extern void TrackAsset_get_isSubTrack_mB3337FCA3D035259F1A6F5B9CFD6D9B6E2EDF05E (void);
extern void TrackAsset_get_outputs_mF8F332F69DBE3F1DF5C24DF6AFEE25B14F6ED4F7 (void);
extern void TrackAsset_GetChildTracks_m34EE35A341030F99020F56267BAFC8FF4E98477C (void);
extern void TrackAsset_get_customPlayableTypename_m5E5465F06E569999A2839AAA5B29B6907C74718D (void);
extern void TrackAsset_set_customPlayableTypename_m091B3CFE1EEBAFC3508726A51254C6090CF84A4A (void);
extern void TrackAsset_get_curves_m3068225198516361CFB374B26B71E266079D1031 (void);
extern void TrackAsset_set_curves_m61933C531CDE903FD6DCB11ABB1DEA36F6C844B6 (void);
extern void TrackAsset_UnityEngine_Timeline_ICurvesOwner_get_defaultCurvesName_m56DD3624C90F3BDDD8AC804AD16A80B4D1D5D542 (void);
extern void TrackAsset_UnityEngine_Timeline_ICurvesOwner_get_asset_mCB4DB0FF33D5C0E1A701D53C1CA54386A1DAF291 (void);
extern void TrackAsset_UnityEngine_Timeline_ICurvesOwner_get_assetOwner_m9E12631082B8FD221B0C93F5EA88E8926D629537 (void);
extern void TrackAsset_UnityEngine_Timeline_ICurvesOwner_get_targetTrack_m34F7F32EC8BF3AA9219D1B02A6E8BE32D2B0BD50 (void);
extern void TrackAsset_get_subTracksObjects_m18E7B9EEC20905CBE5D35805FAA819CB663ECC43 (void);
extern void TrackAsset_get_locked_mCEBC35262FB276860A374085F2626DCE333FC466 (void);
extern void TrackAsset_set_locked_mA8190606E6AC618DCF1CF211AD648C15E67D0F87 (void);
extern void TrackAsset_get_lockedInHierarchy_mB8B7847141ACB7B33320D85B456F7B7FAD155298 (void);
extern void TrackAsset_get_supportsNotifications_mFAD0EDA9BAD3DDD341161C02D5DDC658F1184729 (void);
extern void TrackAsset___internalAwake_mBBCB93A3A0959E0B82B795E298DCD8B3C5E67FAB (void);
extern void TrackAsset_CreateCurves_m4EA9E9F12B65A9584E97158527FA7DC7BD11BAB2 (void);
extern void TrackAsset_CreateTrackMixer_m088F7E10980285D4DFD092CFDFB0A34B9AB78DAF (void);
extern void TrackAsset_CreatePlayable_mD0CEA49410876109A48E365380586AD0E5F0CEBF (void);
extern void TrackAsset_CreateDefaultClip_m1AC3502758E2185D0747626A32D27B513AC85E0F (void);
extern void TrackAsset_DeleteClip_mA02A503841BACD54C29CCAF816A917F1018E1FAE (void);
extern void TrackAsset_CreateMarker_mD4F5715387220B12D0EF244C7C02F83F6040638A (void);
extern void TrackAsset_DeleteMarker_mA491061D745EB9073AB46DABEDDA9B4BF8963347 (void);
extern void TrackAsset_GetMarkers_m4FE387892A6434D0DDD3535BD974E276372B7ADA (void);
extern void TrackAsset_GetMarkerCount_mE3D948E944448243CAC284D1D358C9A889FB44B7 (void);
extern void TrackAsset_GetMarker_m2CB75ED400AECE525785B9E58020CC8EF0669A0A (void);
extern void TrackAsset_CreateClip_mA7D1A7B6ACCF5CCF9FB416E86C483BD2EC31A45F (void);
extern void TrackAsset_CreateAndAddNewClipOfType_mF1CA85A2B77A5CF003685CEBBF0A7551CCFA05E9 (void);
extern void TrackAsset_CreateClipOfType_m62E140A7CBDF66DE0643A1D65601E6ECCC6DA0B1 (void);
extern void TrackAsset_CreateClipFromPlayableAsset_mFCC78BCEE93E36BB92F1CC13A08CA7E11592CB44 (void);
extern void TrackAsset_CreateClipFromAsset_mC48C4A19A082D69011DE9A64D73F05160DD1BD55 (void);
extern void TrackAsset_GetMarkersRaw_m31260E1A58B86C26BD0E4EE80F5DC22FA64FBC14 (void);
extern void TrackAsset_ClearMarkers_m4F8DA7A63459F34E3561E03BB1D198D63B0435ED (void);
extern void TrackAsset_AddMarker_m9B5E8B481DA0086DB46B0FE6219231F2B8FE63E8 (void);
extern void TrackAsset_DeleteMarkerRaw_mC80F55242BE94323E2C826A8C9CD6E7C8ECA7540 (void);
extern void TrackAsset_GetTimeRangeHash_m6A9D09CE4BE80DBBF10AD763E66944FBB37C33D4 (void);
extern void TrackAsset_AddClip_mC0AD6626726FE95A50EFEC2DAF26D9625210FE09 (void);
extern void TrackAsset_CreateNotificationsPlayable_mA46A9A1F92F6DBB21E6AA080A0491AF04F326D9B (void);
extern void TrackAsset_CreatePlayableGraph_mB09B3F258784F140E7D3F33ED4029056EA220371 (void);
extern void TrackAsset_CompileClips_mAC9B684ADF9F1E0AB784A283BF350D3B5AC3D7AA (void);
extern void TrackAsset_GatherCompilableTracks_m0494F6912D73193408CB13F43099A6D5F2665C14 (void);
extern void TrackAsset_GatherNotifications_mF5DBDCF22D9CD5ED6FE6D868583BB6D332AD6BFA (void);
extern void TrackAsset_CreateMixerPlayableGraph_m38CCE75D2966419D1FB327209C2320F36FB19222 (void);
extern void TrackAsset_ConfigureTrackAnimation_mF10DDD7B2768DEDFDB46752A7E7A43F7E149EBBC (void);
extern void TrackAsset_SortClips_m4FE3C4820022ECF050815215CEB982DE373F85D2 (void);
extern void TrackAsset_ClearClipsInternal_m60A468FD1AF2A83A9C8BB65376464216878BE994 (void);
extern void TrackAsset_ClearSubTracksInternal_m8638EC88D91AC3AA5F80BF38C92E16BD67D994DE (void);
extern void TrackAsset_OnClipMove_m9FB2E0FF1003CA992D3F6841D13418262C8BF341 (void);
extern void TrackAsset_CreateNewClipContainerInternal_m7DD56E4426DDBA57B25BF61EAF45938324FA9F2B (void);
extern void TrackAsset_AddChild_m208FDE1AB513103FCFA9F1C8174B976E9D1DAE76 (void);
extern void TrackAsset_MoveLastTrackBefore_m4619211278763606F6FE3FC90665837417BCD966 (void);
extern void TrackAsset_RemoveSubTrack_m7F68C7D6AF1FB79584D154B357F47ACDF29FDAA8 (void);
extern void TrackAsset_RemoveClip_m1D64D42648A148BAE62836E46FC57C25EC9D1A60 (void);
extern void TrackAsset_GetEvaluationTime_m30B94204B96340B1C2810E05607E34A4DB85067F (void);
extern void TrackAsset_GetSequenceTime_m618B6BD38BE9B1136E8405B1BEDFBAFD898896A9 (void);
extern void TrackAsset_GatherProperties_m09C1A335FCE1ABA158748583AF4A641FF2EBB09D (void);
extern void TrackAsset_GetGameObjectBinding_mF2D645FA74007FD6EA6B337298F939FFB4A5B853 (void);
extern void TrackAsset_ValidateClipType_mD794D7B040F112C3E8ACBF9FB601414D08B97A8F (void);
extern void TrackAsset_OnCreateClip_m0FF04313EBF6CE614CAB3C124B1F4D11B0E3AF94 (void);
extern void TrackAsset_UpdateDuration_mCC46D1145F6BCB2B5DB28211C23E7E12702F7035 (void);
extern void TrackAsset_CalculateItemsHash_m2928153E88198C690E2CB8486AC483555276546E (void);
extern void TrackAsset_CreatePlayable_mDD98D897F11DDC7593DF7F3029ED7A1B63285D0C (void);
extern void TrackAsset_Invalidate_m3CBA531307AEE6181C9938F174AE6D1A977115BF (void);
extern void TrackAsset_GetNotificationDuration_m8F9FB2B13DC1C6CC88617A18535730D1E012FC13 (void);
extern void TrackAsset_CanCompileClips_mB7B603BB1D6782D00C14752530F66C638C174505 (void);
extern void TrackAsset_CanCreateTrackMixer_mA825C739B3AA34B9F0F36C7534AAC4E84073F261 (void);
extern void TrackAsset_IsCompilable_mD65DF730D54F4D1F0F0E13D42A009FDF8C59F284 (void);
extern void TrackAsset_UpdateChildTrackCache_mDEEB1C096FDD5B0D035CD5C699119CD913BC6BB0 (void);
extern void TrackAsset_Hash_m11C239AAFB9C1FFB1035A0B0DC5271A62CA3AD78 (void);
extern void TrackAsset_GetClipsHash_mF0B665CDE32E5BEA60FD6EC88F7272CEA6A83115 (void);
extern void TrackAsset_GetAnimationClipHash_m9A2D605C95C224426E2263A8693E578B77633EE7 (void);
extern void TrackAsset_HasNotifications_mE386A9FE718BC4C8D368E8BA5B0FFC3BC50C5E06 (void);
extern void TrackAsset_CanCompileNotifications_mEA94C527EED8871F7AB1CC74C3487ABC204C708A (void);
extern void TrackAsset_CanCreateMixerRecursive_m31242CE6BCCB6DA4F24690457585789405645FFC (void);
extern void TrackAsset__ctor_mC05CAAD737449BAF26721F82EA1972843F86FE9A (void);
extern void TrackAsset__cctor_m9E9CC7F378B533931B19CC75ACD04B10793C18BA (void);
extern void TransientBuildData_Create_m4A6E221013EDF3015DB6842AC1A51882A0E74F86 (void);
extern void TransientBuildData_Clear_mA1B052EF3A05C723008A58559E4C77A989ED2FD3 (void);
extern void U3CU3Ec__cctor_m4CF50A8244F0E8B9535D4C7B45D8D6B2D4C3F07A (void);
extern void U3CU3Ec__ctor_m0071EE643C6662AFC30FD5F241BD4F6B05382B91 (void);
extern void U3CU3Ec_U3CSortClipsU3Eb__121_0_mBD715AA0E013972CC995FFB813CBACEAFD120E71 (void);
extern void U3Cget_outputsU3Ed__65__ctor_m36D55B6998402316E9C824384D2823CF2887FA99 (void);
extern void U3Cget_outputsU3Ed__65_System_IDisposable_Dispose_m1E8207D4EAFDAEFB62061AD6D689431BD40999AB (void);
extern void U3Cget_outputsU3Ed__65_MoveNext_mB5F1A5655A42CB9A832A2B7B941A3DA555E65F36 (void);
extern void U3Cget_outputsU3Ed__65_System_Collections_Generic_IEnumeratorU3CUnityEngine_Playables_PlayableBindingU3E_get_Current_mA6C35B64EBEC6BDA1221AC7F991E746D1729589D (void);
extern void U3Cget_outputsU3Ed__65_System_Collections_IEnumerator_Reset_m322763B2D18B71E3A120A80829AB3CBE23709A03 (void);
extern void U3Cget_outputsU3Ed__65_System_Collections_IEnumerator_get_Current_mF3FCDDFE0515DC769DD6EF0A8D6ABADA7BEE9302 (void);
extern void U3Cget_outputsU3Ed__65_System_Collections_Generic_IEnumerableU3CUnityEngine_Playables_PlayableBindingU3E_GetEnumerator_m1EFEC630120231AFC3D898AFCDD0756F9561543D (void);
extern void U3Cget_outputsU3Ed__65_System_Collections_IEnumerable_GetEnumerator_mD5E8220BC3E60F92FA1CFDD991151AAE34C519B2 (void);
extern void TimelineHelpURLAttribute__ctor_mEB91B85CCCBEC196457E5FCB18A6C9C13E48CEBC (void);
extern void TrackColorAttribute_get_color_m5AC7558C98A60D996F1C3A9F1EB6F180FB45FFE4 (void);
extern void TrackColorAttribute__ctor_mBC50DD796426AAD66A6A8BB7BE46EBB7873AA006 (void);
extern void AudioClipProperties__ctor_m044736544A12AD2A937225432C158B5BDA240B83 (void);
extern void AudioMixerProperties_PrepareFrame_m84E02C27BA11487134C9360A3A3F2BF90A3CDAD8 (void);
extern void AudioMixerProperties__ctor_mB4EC402EBE2B6C8F3CF268598319F789AF837592 (void);
extern void AudioPlayableAsset_get_bufferingTime_mE7FFA2B05A64D29C552930DFAA17A57CFE397031 (void);
extern void AudioPlayableAsset_set_bufferingTime_mDA418332D841FE5667E21538BBAEC16041A07032 (void);
extern void AudioPlayableAsset_get_clip_m49311E74FD58B38CC0D8ED16A0D8869F4ADAB720 (void);
extern void AudioPlayableAsset_set_clip_m2A3379A7D58655D8C6B0395F262B6829FDCAD6EB (void);
extern void AudioPlayableAsset_get_loop_m579753DC7E58ACCA36A44E217C28EBF40CC3C294 (void);
extern void AudioPlayableAsset_set_loop_m594EA5EE80017E2F1C15FE170CAD6F22D01EE7DD (void);
extern void AudioPlayableAsset_get_duration_m44FF0A8D8526B9EFBA604BFCDF79DB380E5B76BB (void);
extern void AudioPlayableAsset_get_outputs_m40AC20DF1E9E352D9C2EA015742D42E7F9C73E6E (void);
extern void AudioPlayableAsset_CreatePlayable_mBFEC2FDD97D15CA9376CAAF65FE217AAC6E854BB (void);
extern void AudioPlayableAsset_get_clipCaps_m7DF29DF049D9F0407E9AFD7D209AB825A8743F80 (void);
extern void AudioPlayableAsset__ctor_mF10BFB16BB64A6F8D0FE62D7A0B8335ADB119A76 (void);
extern void U3Cget_outputsU3Ed__16__ctor_m61F54FD6956E98C4C644EC8189368AB432C0F0D4 (void);
extern void U3Cget_outputsU3Ed__16_System_IDisposable_Dispose_m72E1B27D3C90C82B4A415D51E3B2177CED2BE178 (void);
extern void U3Cget_outputsU3Ed__16_MoveNext_m6D77A67836D44DDA516C67755527D4DD24E1F9A5 (void);
extern void U3Cget_outputsU3Ed__16_System_Collections_Generic_IEnumeratorU3CUnityEngine_Playables_PlayableBindingU3E_get_Current_mBE6C905012CCD38CF8A2DD6A4815FDE3772370D2 (void);
extern void U3Cget_outputsU3Ed__16_System_Collections_IEnumerator_Reset_m026B2E69F6C94660273E8386AC24B908344881CE (void);
extern void U3Cget_outputsU3Ed__16_System_Collections_IEnumerator_get_Current_m582D28A2EE588AF6E3ACADE39F5EE74A2B852D1B (void);
extern void U3Cget_outputsU3Ed__16_System_Collections_Generic_IEnumerableU3CUnityEngine_Playables_PlayableBindingU3E_GetEnumerator_m8038F14452BCFDB7F80F35661C0BAB7CB2201C4B (void);
extern void U3Cget_outputsU3Ed__16_System_Collections_IEnumerable_GetEnumerator_mD88200543F030A8F6F9ADFBCA257B6DB811ED84E (void);
extern void AudioTrack_CreateClip_m8CBE84BCC1FF99D6E93FD790F9BAD277F2BC6D9E (void);
extern void AudioTrack_CompileClips_m641F177F94C9BA21B517CCDBF43C25ABC605BF28 (void);
extern void AudioTrack_get_outputs_m8B9BE825525351885F322DCEBB5A8FA7D6AFEE89 (void);
extern void AudioTrack_OnValidate_mB46DE622AEA6652C4248854BF0DB7607FA704960 (void);
extern void AudioTrack__ctor_m6EEC48668D6F9248F48B2B0686291560444B8F35 (void);
extern void U3Cget_outputsU3Ed__4__ctor_m190710D72768E977B81519D942515031DCF91C88 (void);
extern void U3Cget_outputsU3Ed__4_System_IDisposable_Dispose_m5824B5C4F4CA22007A3FEEE9AB75AA4BA9AEC83C (void);
extern void U3Cget_outputsU3Ed__4_MoveNext_m636D185FBF8DAF5943220DF0FD5AE6FA2C7C7999 (void);
extern void U3Cget_outputsU3Ed__4_System_Collections_Generic_IEnumeratorU3CUnityEngine_Playables_PlayableBindingU3E_get_Current_m75314CB64DC2DC82417162EFFB901A50D6E60C51 (void);
extern void U3Cget_outputsU3Ed__4_System_Collections_IEnumerator_Reset_mBE20566179B7912EC30C3BFFC5A8781486E25E58 (void);
extern void U3Cget_outputsU3Ed__4_System_Collections_IEnumerator_get_Current_m2F2377F982B6CA15109F25D7E87D68030B432BA5 (void);
extern void U3Cget_outputsU3Ed__4_System_Collections_Generic_IEnumerableU3CUnityEngine_Playables_PlayableBindingU3E_GetEnumerator_m7C348C673AFB4692FE68B667BF09E0F8C8B3E6A1 (void);
extern void U3Cget_outputsU3Ed__4_System_Collections_IEnumerable_GetEnumerator_mD0AB6E0CA93EA531B030519FAB433F4F25D27CD0 (void);
extern void TimelineClipCapsExtensions_SupportsLooping_m149B53ABA1636A092CD5BB849C19277D93D4B935 (void);
extern void TimelineClipCapsExtensions_SupportsExtrapolation_m3C0E25676F88C9DEEF4149DC2158A4A18711EE3A (void);
extern void TimelineClipCapsExtensions_SupportsClipIn_m318FDA5D82ED5E932A5E9C195668685F4571EEF9 (void);
extern void TimelineClipCapsExtensions_SupportsSpeedMultiplier_mC1AAE95266F6201D78F17EBAB47D7923341BBD30 (void);
extern void TimelineClipCapsExtensions_SupportsBlending_m569C3ED6D06142794DA7B1FD6C00062A209E7885 (void);
extern void TimelineClipCapsExtensions_HasAll_m2596307B9954E6FCEFDDFD4A0BE87D936DE31BF9 (void);
extern void TimelineClipCapsExtensions_HasAny_mA750DF1B0964785A2FC8DC2F9A05CF03D27F1670 (void);
extern void ControlPlayableAsset_get_controllingDirectors_m0370250573BBDA45A8E8B086FFDE90ED5B1961CD (void);
extern void ControlPlayableAsset_set_controllingDirectors_m8D263E063F860FDF4E832CC9C284C45D49FA9270 (void);
extern void ControlPlayableAsset_get_controllingParticles_mEDF1CC5E356EA9B115EF4F35F3509ABB93F5D44A (void);
extern void ControlPlayableAsset_set_controllingParticles_m31709F8C09DB20C7551FDC488A0127295FD0B219 (void);
extern void ControlPlayableAsset_OnEnable_m63F67A81F41C02520284308515022DF19FA2FBE4 (void);
extern void ControlPlayableAsset_get_duration_m0C2033B0C5C3E6DE7385797DE1B07F0263521878 (void);
extern void ControlPlayableAsset_get_clipCaps_m09BC8BAA1440D221483F653285DA52715346F22A (void);
extern void ControlPlayableAsset_CreatePlayable_m88A68ADB8119BC891B37E1B9CCA7FD26090914D8 (void);
extern void ControlPlayableAsset_ConnectPlayablesToMixer_m8020203CFDC98E5674106B99938017E8D9E798CC (void);
extern void ControlPlayableAsset_CreateActivationPlayable_m8BC4706CA44F6216AE3C3169D03BEE15BC819111 (void);
extern void ControlPlayableAsset_SearchHierarchyAndConnectParticleSystem_m6D97733D38AAE740196B5AFA9C75A45AB54114B4 (void);
extern void ControlPlayableAsset_SearchHierarchyAndConnectDirector_m9AD1670D0949F2D418DBF34152170810855A18AB (void);
extern void ControlPlayableAsset_SearchHierarchyAndConnectControlableScripts_m46A0974334C5CAB121787DC91ED6C1A9E07B33BC (void);
extern void ControlPlayableAsset_ConnectMixerAndPlayable_m8727A31B27ECFA1AF0B7478B8514F3D7B27C8459 (void);
extern void ControlPlayableAsset_GetControlableScripts_mDAC0BC709F7024BAC5F520AA31F565824AE542A2 (void);
extern void ControlPlayableAsset_UpdateDurationAndLoopFlag_m3B3976CA44009F140D54203A5A696A341A815669 (void);
extern void ControlPlayableAsset_GetControllableParticleSystems_m182DE87B71BF287B5D828C7D48D099C0C3B4F85A (void);
extern void ControlPlayableAsset_GetControllableParticleSystems_mB7DA5089B5C09BC5876DEA587D20550184A69BC4 (void);
extern void ControlPlayableAsset_CacheSubEmitters_m823886C0C0F1C8AA13129B3A9810CDA8AAF2EE7B (void);
extern void ControlPlayableAsset_GatherProperties_mEB211D7180EBC912CAEE8C6521F67AE4AC240FDB (void);
extern void ControlPlayableAsset_PreviewParticles_m611AFD61ACF20FDD50C82D09230C32B7D0E3D94E (void);
extern void ControlPlayableAsset_PreviewActivation_m0FAEA1926A9ACFC55CE3CF14992F5A4DE1F1368E (void);
extern void ControlPlayableAsset_PreviewTimeControl_m38FFD36A41ECA2BA874D7A7DA234E15EFAB2A043 (void);
extern void ControlPlayableAsset_PreviewDirectors_mA5DF7CF7A9F2ECFAE8C59FEDD944BDD4C25AE118 (void);
extern void ControlPlayableAsset__ctor_mCB87B77766491D451517D1082079113798B8518A (void);
extern void ControlPlayableAsset__cctor_m1343221BA07342B50EF52CA584CAFF1508E50BA5 (void);
extern void U3CGetControlableScriptsU3Ed__39__ctor_m61DC7C12C95430EF9C16D27768169F05CC5784D0 (void);
extern void U3CGetControlableScriptsU3Ed__39_System_IDisposable_Dispose_mF25100DBDEB7B203BC1DD6CCBE34A94797D68A46 (void);
extern void U3CGetControlableScriptsU3Ed__39_MoveNext_m0A28A750413795A869B753439AC20C1E8AC2E33A (void);
extern void U3CGetControlableScriptsU3Ed__39_System_Collections_Generic_IEnumeratorU3CUnityEngine_MonoBehaviourU3E_get_Current_m288919711BCB540205566CDF9B9339EC8EBB2DB1 (void);
extern void U3CGetControlableScriptsU3Ed__39_System_Collections_IEnumerator_Reset_mBCFCCD39392B14C63C54D9079BB6E3302DC858B0 (void);
extern void U3CGetControlableScriptsU3Ed__39_System_Collections_IEnumerator_get_Current_m0C00993614DC7FD44F070DD625C5325A84301309 (void);
extern void U3CGetControlableScriptsU3Ed__39_System_Collections_Generic_IEnumerableU3CUnityEngine_MonoBehaviourU3E_GetEnumerator_mAC5B005DF8CED15F0652A85E921526C4E9E245C9 (void);
extern void U3CGetControlableScriptsU3Ed__39_System_Collections_IEnumerable_GetEnumerator_m51F55F60727D8A0C7F5CD7F862853E8B041F1910 (void);
extern void ControlTrack__ctor_m0B4C0633844F60987B07ABC4467976B46AB0975D (void);
extern void DiscreteTime_get_tickValue_m0ACC9CCCEB39D170368ACD3FD094781F6D01EAFD (void);
extern void DiscreteTime__ctor_mA6613BBA0DEB0A0BCCED50838378D9D9971B9F62 (void);
extern void DiscreteTime__ctor_mF5CD806CC7273F8C5CC80053A78D09604DED0491 (void);
extern void DiscreteTime__ctor_mF858F11921F841F599EF51C7975430BEA8EC3B52 (void);
extern void DiscreteTime__ctor_m7D7C3098B16F74497C5FC907B364C50A667EB2C8 (void);
extern void DiscreteTime__ctor_mCD4A3BE5EB3142C4247190F7896378B68F428F69 (void);
extern void DiscreteTime__ctor_m5C84429A52611671EE6F2D7BCD1399335D8242F5 (void);
extern void DiscreteTime_OneTickBefore_m06F6D6A227D8356FCD3283CD59571DFA12943D1A (void);
extern void DiscreteTime_OneTickAfter_mD2C58DF00885E44EA0D7AF8C9F56C14F77E53EB7 (void);
extern void DiscreteTime_GetTick_m52C7EE5F63087831CF4B21EF76A00655181349AA (void);
extern void DiscreteTime_FromTicks_m39AD4CB20BF1DA744A07F93B9FCA9F0F75DE9BDD (void);
extern void DiscreteTime_CompareTo_m9A2C892A6E4097A5FBC0C2D5D666E01CE12427EA (void);
extern void DiscreteTime_Equals_m3D269E4164E6E705E6DD19B557F9DBFC2CB226FA (void);
extern void DiscreteTime_Equals_m42B27C6DDF993E36A0C3D20B708986277AE6B7FD (void);
extern void DiscreteTime_DoubleToDiscreteTime_m8D83412A88CCAC93ABD5070209109957FCA85762 (void);
extern void DiscreteTime_FloatToDiscreteTime_m0F57E8699783D9B2A48381F34D2116E71F3EA559 (void);
extern void DiscreteTime_IntToDiscreteTime_m17C3BF4B229B41B91CCACB7F465628A9ACA4736D (void);
extern void DiscreteTime_ToDouble_m319F0D5276BC26C56CDC124BC7878DB535351348 (void);
extern void DiscreteTime_ToFloat_mA77F564AE95883062A631DAF86C4F401567CEB0E (void);
extern void DiscreteTime_op_Explicit_m801A8089D31AA0C435943A08C89A83607FACD52B (void);
extern void DiscreteTime_op_Explicit_m08D29220F020CB6A40CC9D59B5B4104BBA7599D0 (void);
extern void DiscreteTime_op_Explicit_m7C11B47C356E41AB9F96ED46AE8BBFA05C1B3941 (void);
extern void DiscreteTime_op_Explicit_mE186D14B06F947B0B372625D4E927566B0161874 (void);
extern void DiscreteTime_op_Explicit_mCA9C09F74934D46B5635C42C9984C8C84589CD30 (void);
extern void DiscreteTime_op_Implicit_m8EEF2A52EFFBFC375CB1D8171DC439A22DC35ECB (void);
extern void DiscreteTime_op_Explicit_m42F903A62DF258BD11463C461991AA5DE89AA71D (void);
extern void DiscreteTime_op_Equality_m55FD471B465FFDAC78BE2413701203194CFEADC4 (void);
extern void DiscreteTime_op_Inequality_m19BD9CC31D1A2FBDA0CC50BA96EA37463F404AF6 (void);
extern void DiscreteTime_op_GreaterThan_m3F023E3705B46C7E548730E10C543416EACEBC45 (void);
extern void DiscreteTime_op_LessThan_m3CD38C215D6813414BFB1AC88BAF5BF1796C49D1 (void);
extern void DiscreteTime_op_LessThanOrEqual_m8E8286335E92963611D6C0F6598B20E61CBE2F14 (void);
extern void DiscreteTime_op_GreaterThanOrEqual_m04A7C9DC1DACEBE2FF6201F7B81C5CE59518A57D (void);
extern void DiscreteTime_op_Addition_mCE0392D2D1FB0BD53AD56C9A287810D1672B2BC8 (void);
extern void DiscreteTime_op_Subtraction_mA27858E3E6CA4BBD578B1CE193504549AA916FC5 (void);
extern void DiscreteTime_ToString_mFE5EEC6255B3AA617F692DA9EB7DF4A5AD71FD83 (void);
extern void DiscreteTime_GetHashCode_m59E889384FD1F78DC2A1220C228EAB164F8995CE (void);
extern void DiscreteTime_Min_m6FF92F93C9C652F5794A44BCADF4C1F6D15A3140 (void);
extern void DiscreteTime_Max_mE6741CA89EADA7D3EF47C29C8C9D0D57CC8597B6 (void);
extern void DiscreteTime_SnapToNearestTick_mBA4C390A09ED03D90BE0D8C0CD39949AAC76AF03 (void);
extern void DiscreteTime_SnapToNearestTick_m563A71A5A432C7676FC6692CF68DD81FF2020AB1 (void);
extern void DiscreteTime_GetNearestTick_mC4A04B29410685B1864597BCB83664F6B2F95367 (void);
extern void DiscreteTime__cctor_m23DF1F1B6C69F8785475607E6188362FE65E123F (void);
extern void InfiniteRuntimeClip__ctor_m807AE0572975FDC87883AC4877442F7E986B7812 (void);
extern void InfiniteRuntimeClip_get_intervalStart_m9F26CE71EA9F461A924BF1AF6892154B8077FF8D (void);
extern void InfiniteRuntimeClip_get_intervalEnd_mB05A86E64F77260F1CC9EF60285E9985106A7D41 (void);
extern void InfiniteRuntimeClip_set_enable_m950C18673BCCA53BA644B8998BE3A00FC37CBE2D (void);
extern void InfiniteRuntimeClip_EvaluateAt_m3A6147F9BB6B625BA721C1C2FCE0DFCBBDB1DF8E (void);
extern void InfiniteRuntimeClip_DisableAt_mBE37CED8ED2A4643EDC99BADD76AF0E344A4929E (void);
extern void InfiniteRuntimeClip__cctor_mE142C87FB9D4550C966F87FB581863C614E7CD8A (void);
extern void RuntimeClip_get_start_m6204EC5ADD90B46E23FFE1436928152BF15326EC (void);
extern void RuntimeClip_get_duration_mBA27B08B52BD7B402C449A7733AA7755FA987F6C (void);
extern void RuntimeClip__ctor_m98046934573D3967A198B431434524D25C76F43C (void);
extern void RuntimeClip_Create_m9464E5BA8F8A29AEA85775728362F8B72A742D10 (void);
extern void RuntimeClip_get_clip_m6208BBDD36E3E3EA97000F4CEC9BB1B879A1824D (void);
extern void RuntimeClip_get_mixer_mA42F77ACA8B17C58C502EC06C7930F2033AA0589 (void);
extern void RuntimeClip_get_playable_mD336A90E2A444F4B50F3B02FABC5C6ECDE3E21BE (void);
extern void RuntimeClip_set_enable_m61A322D87BF4A75D804C2C82FD59CACA871B8FA1 (void);
extern void RuntimeClip_SetTime_m7ECE29A44A0DA3625276C56999A7D293CF642ED1 (void);
extern void RuntimeClip_SetDuration_m4AB935A8C5F597184D3F20A714704C11458FAE40 (void);
extern void RuntimeClip_EvaluateAt_m6D08332D99A261EE232EFFE9F94ADEC3B5E66BDE (void);
extern void RuntimeClip_DisableAt_mE25279C9BD90378393EC0E8CB029EDFECE7CC243 (void);
extern void RuntimeClipBase_get_intervalStart_m599D96633271890B892A9D48325531F28DDE1BAD (void);
extern void RuntimeClipBase_get_intervalEnd_mA0819F921B8AA4BFEBF31419858BA2C975BE174B (void);
extern void RuntimeClipBase__ctor_m53B7986F56314C13A22B079840C7DA6D36501958 (void);
extern void RuntimeElement_get_intervalBit_mD5C7E7CDA66C4B1888D529E9344679FF88DE5A70 (void);
extern void RuntimeElement_set_intervalBit_mD6D67EA4A982521E16CDF3CAC295EED22CA65C75 (void);
extern void RuntimeElement__ctor_mB4868C6FD6BE7F80F182AE2961580F1E31A60F68 (void);
extern void ScheduleRuntimeClip_get_start_mC7AEF6AF42593F08CD3403B98132D3AAEDD898D8 (void);
extern void ScheduleRuntimeClip_get_duration_mE5950845592C0E89A8F622204CD37D7ABCFD62D0 (void);
extern void ScheduleRuntimeClip_SetTime_m38A382F75037FC1430B6067DAC94848D7043D1DF (void);
extern void ScheduleRuntimeClip_get_clip_mC24FB5C0451219E6A3CF888B81B0099A34443A3B (void);
extern void ScheduleRuntimeClip_get_mixer_m5FFC2C73903CC0B038A4F76EB1A4F99B912FC54C (void);
extern void ScheduleRuntimeClip_get_playable_mF7988FDD9350B256DD307DE9F9957AA1A44D9EED (void);
extern void ScheduleRuntimeClip__ctor_m83C75C594A568AE694E42341685277CFB517568F (void);
extern void ScheduleRuntimeClip_Create_mA8ADCDBD0B9C32427F6EB006FFB9057989981C18 (void);
extern void ScheduleRuntimeClip_set_enable_mA74C59B6CDC243221219534B77FA5B140A4E5989 (void);
extern void ScheduleRuntimeClip_EvaluateAt_m3C3C92A67AB06B22C1B3E22B884F78A3BA42F6D8 (void);
extern void ScheduleRuntimeClip_DisableAt_m57233707606DE133E713FB3B89BB867BE65A8CA7 (void);
extern void Marker_get_parent_mE72E59DAF1DBC598885CD3CA9D0A55CB68CE8510 (void);
extern void Marker_set_parent_mCAB1A605E39909E42D623D48037DC43141173A34 (void);
extern void Marker_get_time_mB0E90DB26C36F73A6AFB126761F1FF144A145BF6 (void);
extern void Marker_set_time_mF28306664964EF628F2D6D9F9F9DB2EB7DF89DED (void);
extern void Marker_UnityEngine_Timeline_IMarker_Initialize_mA5ED18EB0857836506FD03FB6C7E04C06AC6F320 (void);
extern void Marker_OnInitialize_m95F020C003B8C4C5072BF891C9539CF0D6A2F550 (void);
extern void Marker__ctor_m74124BFBCDCAFE96C2A9CE59CA215B80590F6A56 (void);
extern void MarkerList_get_markers_m5376FA1EA20DEC72CE47369DD8CD407869B0A2F7 (void);
extern void MarkerList__ctor_m2D55037070301E459BF9CAB83756B81183C6A6B8 (void);
extern void MarkerList_Add_m401FA50EA0C98293DD36147CFA9DC8F637636128 (void);
extern void MarkerList_Remove_m986C1ECBCA1A1DE79FCD97D7D44EC91C19D79F64 (void);
extern void MarkerList_Remove_mF1626830F54C37B15C3DBEB6044B92CBF6439790 (void);
extern void MarkerList_Clear_m260B8EDEF14AC94F4BE5F10018F2215BF11927E7 (void);
extern void MarkerList_Contains_m97FF1ADAEFBD92A7F3E66B0A7C85950224B62989 (void);
extern void MarkerList_GetMarkers_mC440918CEEC3E0A8E872510EDEAEA0FB8832E680 (void);
extern void MarkerList_get_Count_mC37C47376A0DCC203DBD965E961A923F06E0082B (void);
extern void MarkerList_get_Item_m76D3839ACDBAB9F00DFD9CE68C63DE26050961FE (void);
extern void MarkerList_GetRawMarkerList_m272F6FFA3A887E15FDEFA8AAC07F1A6A0BD54A0D (void);
extern void MarkerList_CreateMarker_m404F50BDABD541CF30E2F03DC6C22F917E02130B (void);
extern void MarkerList_HasNotifications_mF2F94CCD1C286DB7264738F287486890D2497124 (void);
extern void MarkerList_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_m6E97ABA4A5ED5881C2FC7F356FCAF68FCC45B710 (void);
extern void MarkerList_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_m1175CE30FB38D95504747F73994F2C86DC0E8928 (void);
extern void MarkerList_BuildCache_m8EFF6B73A205DC746231385CAC726A74A2B485F5 (void);
extern void MarkerTrack_get_outputs_mCC74870CF1C68FA4B6DC21EDADEDE9936A6F21AC (void);
extern void MarkerTrack__ctor_mAF73693D259F16A0F2DBF71313BA854F5C71A1E8 (void);
extern void CustomSignalEventDrawer__ctor_m6E78EBC2A826450A4F05E0127A6672F07D84CC0A (void);
extern void SignalAsset_add_OnEnableCallback_m554767948986531C6B7B0C14286F651B6B7C7203 (void);
extern void SignalAsset_remove_OnEnableCallback_m99D77B52734D3A480E21F6E09918CB302409CBFB (void);
extern void SignalAsset_OnEnable_mF4113987FFFF4ECE91B0DFD3506EFF02E76DE29C (void);
extern void SignalAsset__ctor_m4F0FF5113CFB816B5FF981FA2A308AEC282D6E9B (void);
extern void SignalEmitter_get_retroactive_m05405283A19896F77F109BE0E4DF0380CAE909EA (void);
extern void SignalEmitter_set_retroactive_mC90E249F02530DA76969DA20208123CCE8A11D4F (void);
extern void SignalEmitter_get_emitOnce_m8A10F3E7A7F2BF66F52B5A418632E1CCA1B613A5 (void);
extern void SignalEmitter_set_emitOnce_mB64C9B47B124F8FEAE68C41BA3CED422A96E6628 (void);
extern void SignalEmitter_get_asset_m80647A00BC1BE61E0FBD0F2CE626B0997E04C140 (void);
extern void SignalEmitter_set_asset_mE5074F7D8760F624046A0E1B7C2D3E2662A7283C (void);
extern void SignalEmitter_UnityEngine_Playables_INotification_get_id_mFE1FFFDDE0AA375A400B8ED622D47E3C4A3462F1 (void);
extern void SignalEmitter_UnityEngine_Timeline_INotificationOptionProvider_get_flags_m440E69F2CB8F13BDB1D4D082E88A8708E1FEC98C (void);
extern void SignalEmitter__ctor_m6A45F5A0D313B5184EA3D27F89DAD8126178E327 (void);
extern void SignalReceiver_OnNotify_m2A3A5A1DB5A3FACAF334771A74677D1FB08FFBF4 (void);
extern void SignalReceiver_AddReaction_m24F66AE62396EEC75BB4F5074246F39FDF6884B6 (void);
extern void SignalReceiver_AddEmptyReaction_m8A3DAC1ED52B65292D89FD40DEAE359D29491EFC (void);
extern void SignalReceiver_Remove_mC5F9BBA8D959629B62B7A7F5B2C2E1A684E857CF (void);
extern void SignalReceiver_GetRegisteredSignals_m0834695F8B630B69EC4A606956C24ABD9FBFA953 (void);
extern void SignalReceiver_GetReaction_m8B5FBD0171FB06EAE16E9A99F2E209829552EFF8 (void);
extern void SignalReceiver_Count_mB3916E584C177A2D47AF76296FB0C59987A1F694 (void);
extern void SignalReceiver_ChangeSignalAtIndex_m66463FEE943CC55C585628B993F64C78257CA1FD (void);
extern void SignalReceiver_RemoveAtIndex_m01EE2CBC7A406CA4A1D13F4F72D591347D02DBC5 (void);
extern void SignalReceiver_ChangeReactionAtIndex_m803D743382D58A2637C8BA1FF00116310AFEC472 (void);
extern void SignalReceiver_GetReactionAtIndex_m3F36C2C9E39F3F39679B4AE052305DDFFC8887A1 (void);
extern void SignalReceiver_GetSignalAssetAtIndex_mAB31FED39823D7220E727A204F128AD9C0B12A82 (void);
extern void SignalReceiver_OnEnable_mB77BD34EC8FE8B41A627585E4739083B7F85F3C1 (void);
extern void SignalReceiver__ctor_m46381605B40F21C06F4125C7D8F45232F4D74CE6 (void);
extern void EventKeyValue_TryGetValue_m7190DE6E665F07018730BAF3288F22CDFEEB9B6D (void);
extern void EventKeyValue_Append_mEE3BEB022A04AA95F1B2B56D3F8AA5F5ECCA5BD3 (void);
extern void EventKeyValue_Remove_mEC52E595AEC1BFAB97DB2567AC42CE0D7C968863 (void);
extern void EventKeyValue_Remove_m3B8982FC179C5DD1853FC4846106B1CD23F57C7D (void);
extern void EventKeyValue_get_signals_m3F5CB2EDFE24DEAAE67DF04898223ACBDB3CB3BD (void);
extern void EventKeyValue_get_events_mB7945E299C4249CB0BD5D2EBAB8D0DEE455FFAE3 (void);
extern void EventKeyValue__ctor_m7AE5F9F6453AA2F9DB8A1612D334D1F4CB3CC549 (void);
extern void SignalTrack__ctor_m92CAC598FA9027691117E52EB8247D2D79D0FBA1 (void);
extern void TrackAssetExtensions_GetGroup_m9261D7FF986D8DBAB89F142B5B2F4357F1C995B9 (void);
extern void TrackAssetExtensions_SetGroup_m0525D6627035D09A0943ADC00AAA03CC68495435 (void);
extern void GroupTrack_CanCompileClips_mF95369ECAEA83FA55A283FB1425F00FBCE9D2F23 (void);
extern void GroupTrack_get_outputs_m1CF0F26206E1943E4C4202F7849408E9E8CA2403 (void);
extern void GroupTrack__ctor_m2612CD9E072D91E3F5C21F8D4F156F96A33CDBBC (void);
extern void ActivationControlPlayable_Create_m61DD26626E7A44339EA08D2E15498E1897FAC23E (void);
extern void ActivationControlPlayable_OnBehaviourPlay_m32D87ABF400DD235012889AEB82EBD1757AE745C (void);
extern void ActivationControlPlayable_OnBehaviourPause_mB2E17A057A698FA2366269460A306B281808748F (void);
extern void ActivationControlPlayable_ProcessFrame_mC388A38FD777597B3E79B41A5DA4286DCA0E73BE (void);
extern void ActivationControlPlayable_OnGraphStart_mF0475EF758222C98681FAB8A1E175C4A0095AE31 (void);
extern void ActivationControlPlayable_OnPlayableDestroy_mF39699D518A1C2075AB6BCD1ECB1996704A28838 (void);
extern void ActivationControlPlayable__ctor_m50572F236B93063098D17B06E2662F10A75F5E0C (void);
extern void BasicPlayableBehaviour_get_duration_m000CA8660FB9136C46ABD05B9ACB9B7BE28C1F95 (void);
extern void BasicPlayableBehaviour_get_outputs_mF90ACBACF585238FF0623BD70C14483DF91D21AE (void);
extern void BasicPlayableBehaviour_OnGraphStart_m921FCF14B857F191628EBB43D79A8CB674EAB8AA (void);
extern void BasicPlayableBehaviour_OnGraphStop_m261E6FFF9FCCA3380C3B1844ADE470605B616FFC (void);
extern void BasicPlayableBehaviour_OnPlayableCreate_m956EA471E10F9B20B77365A935A1C473E5DAD25D (void);
extern void BasicPlayableBehaviour_OnPlayableDestroy_mE70F15932202ABBF11E850199EB2DADFC6B5955E (void);
extern void BasicPlayableBehaviour_OnBehaviourPlay_m1F24D0A73D669B74C455DB02363EF2BB473FBF65 (void);
extern void BasicPlayableBehaviour_OnBehaviourPause_m2372D83D9E18CF06F9121017970D8295412E216D (void);
extern void BasicPlayableBehaviour_PrepareFrame_mE55DA9150027CA503D1495E3E50A9568EAA1F5E7 (void);
extern void BasicPlayableBehaviour_ProcessFrame_m413A95F400514A22EF4630952D0498DBC5347ED2 (void);
extern void BasicPlayableBehaviour_CreatePlayable_m0F957E711BFF407616E35025646C83FA4DD90C25 (void);
extern void BasicPlayableBehaviour__ctor_mF8CCC76427C0B3A8E0F88C4B1853A80E5EA33C1F (void);
extern void DirectorControlPlayable_Create_mA64246490438157CCBE867DF9CFD0F0CD7133DE6 (void);
extern void DirectorControlPlayable_OnPlayableDestroy_m9426CD97470CA7619A971B2A0E8942BC6BE7AD19 (void);
extern void DirectorControlPlayable_PrepareFrame_m0F8F6FBBB5483EBB76332AE013321FC6201893E9 (void);
extern void DirectorControlPlayable_OnBehaviourPlay_m452302B8000575C35BF262260D72F789CA03DD60 (void);
extern void DirectorControlPlayable_OnBehaviourPause_m2DDA62A73F0C95CA0B220637599FD2DBD224E632 (void);
extern void DirectorControlPlayable_ProcessFrame_mAC2E676C1932306AE076F222A074E37BFAA8B6E7 (void);
extern void DirectorControlPlayable_SyncSpeed_m39C659CC0373FBC0829B8343F49F44E852729734 (void);
extern void DirectorControlPlayable_SyncStart_m07109F4B30C707D4B2450C00A0675192A69260E2 (void);
extern void DirectorControlPlayable_SyncStop_mB49F56038E68681AB73BA59A96E3A2F3348868DB (void);
extern void DirectorControlPlayable_DetectDiscontinuity_m5884117E980A5F64A18127E0CC14BD64B12B286D (void);
extern void DirectorControlPlayable_DetectOutOfSync_mAEB0E3DA34B2A5C7F7BBE40144198F9762D07370 (void);
extern void DirectorControlPlayable_UpdateTime_mF24AE595B9899ABED3928F15FAD03CBBB5CBE809 (void);
extern void DirectorControlPlayable__ctor_m89F5CEC0EBACD98DFF7D480BEFEA15DD637F870C (void);
extern void ParticleControlPlayable_Create_m8F03536B0CC6B66B506836058AA37A08EDCD5338 (void);
extern void ParticleControlPlayable_get_particleSystem_m63954D25FBB179CA655D5739DCB07C7DBA99C7A5 (void);
extern void ParticleControlPlayable_set_particleSystem_m6B3EED45B18C3B80D641D969D2601ABBE34BA4C1 (void);
extern void ParticleControlPlayable_Initialize_mCA4BB21915939852339DCECDE34EDB1B4ED36E92 (void);
extern void ParticleControlPlayable_SetRandomSeed_m0D41AD36E12BF013CE346F2A562A717721FB87F9 (void);
extern void ParticleControlPlayable_PrepareFrame_m959C915531B064E7A7AC30B01E8299B21A139EF6 (void);
extern void ParticleControlPlayable_OnBehaviourPlay_mB1B514A71494493FB7A2B8B1F6B5F1455907B761 (void);
extern void ParticleControlPlayable_OnBehaviourPause_m295DA59C6C8E31854EA9009A709FFFAF663428B9 (void);
extern void ParticleControlPlayable_Simulate_mCDD453B5F1A231F1FD9744B19EDE4386E4B3D7C6 (void);
extern void ParticleControlPlayable__ctor_m0E1086350E57B8CA51874D8AE9FDFF1B0AB89C66 (void);
extern void PrefabControlPlayable_Create_mE744622E91538A0C13C1D453F207A32956F35297 (void);
extern void PrefabControlPlayable_get_prefabInstance_mE06FADC9D1367D2606277D5D1F8577C6CA837B4A (void);
extern void PrefabControlPlayable_Initialize_mC8F44778C03DAEFFE8F22B14216C6E838E224483 (void);
extern void PrefabControlPlayable_OnPlayableDestroy_mC7498F993D6766BC8F1963BC770DACBE7AC09DCA (void);
extern void PrefabControlPlayable_OnBehaviourPlay_m25045FBF8EF4CE540108857ED00A5E8CF44EC66D (void);
extern void PrefabControlPlayable_OnBehaviourPause_m9015806A18FE1AF828ED2757E7F83BD499C3C09F (void);
extern void PrefabControlPlayable_SetHideFlagsRecursive_mCA466033B6F14AC5C663F848791C0CC6C6B92716 (void);
extern void PrefabControlPlayable__ctor_m0850EAD5A2F49DAF2B6D56326ED235D8042C9C90 (void);
extern void TimeControlPlayable_Create_m45018C6B659C011B90908D65862ADF68E9F9F251 (void);
extern void TimeControlPlayable_Initialize_m7CE2820058BE601A316CCE5964E4CE3445E9980A (void);
extern void TimeControlPlayable_PrepareFrame_mF122A36FEFFA9BE1BC74FB551225652874D82E83 (void);
extern void TimeControlPlayable_OnBehaviourPlay_mBFF77E285E53483F14E06EE0B56CFECD7CE13C31 (void);
extern void TimeControlPlayable_OnBehaviourPause_m9648D5058F2EC8BFC260AC603B5C03883AD284E1 (void);
extern void TimeControlPlayable__ctor_m2324993C8F5009CC0336BB9D9396F453529CD5C7 (void);
extern void TimeNotificationBehaviour_set_timeSource_mD0096011A303EB4C84B3DE3AAE908C51955E2F8E (void);
extern void TimeNotificationBehaviour_Create_mF24D48476C110D3158F32421C168A5171F4613A0 (void);
extern void TimeNotificationBehaviour_AddNotification_mB502CDA1135E3A3F543B7B24224BB95F986EAD97 (void);
extern void TimeNotificationBehaviour_OnGraphStart_m8C925120572F3A69DA5E9B3DB127F1C506CE436E (void);
extern void TimeNotificationBehaviour_OnBehaviourPause_m6FCF029B2259978A231F78DEC1E4F57E2D88E7C1 (void);
extern void TimeNotificationBehaviour_PrepareFrame_m8C38D54B9B061C4B57098EA0458E768974E193DE (void);
extern void TimeNotificationBehaviour_SortNotifications_m91493C483D46116C8DEE5C25AEE2F9872B3DB86C (void);
extern void TimeNotificationBehaviour_CanRestoreNotification_m7390D6FA66D1786D1AF1412D37D74FD9EE661E2E (void);
extern void TimeNotificationBehaviour_TriggerNotificationsInRange_m9607548147744987224ADD3FE6E3F339D2337284 (void);
extern void TimeNotificationBehaviour_SyncDurationWithExternalSource_mEE92F71E0867E713E58EDF71B9DEE1A1E2F25925 (void);
extern void TimeNotificationBehaviour_Trigger_internal_m87169DADFCE06BC37BA28CFED3B0A6174DFFEAB7 (void);
extern void TimeNotificationBehaviour_Restore_internal_m25E1C8121200E214969CDD9A753D6357D440726A (void);
extern void TimeNotificationBehaviour__ctor_m82EC1218499AA0C697D1005358DA66DB1908FFAC (void);
extern void NotificationEntry_get_triggerInEditor_m0F467B8FC45EDED62344F2EB66D91998C330FC7A (void);
extern void NotificationEntry_get_prewarm_mDF7D568CD5A14E53E9E11DAF6AD33F0FE7ED29DE (void);
extern void NotificationEntry_get_triggerOnce_m40147F02406594EFEA328E29EDC9C3110E9A7654 (void);
extern void U3CU3Ec__cctor_m6669D2494CB2B140029B26B2BE183C4A8DEDB476 (void);
extern void U3CU3Ec__ctor_m8358614F1AC3F7F79522CD7795964B99E574C428 (void);
extern void U3CU3Ec_U3CSortNotificationsU3Eb__12_0_mCFEAA6C512A546FDC4CC69A23EF75844DFDB9189 (void);
extern void PlayableTrack_OnCreateClip_mB016C731FC0C8567F707C53BAD5B9E9B99DE2BE7 (void);
extern void PlayableTrack__ctor_m1ADC6FA394F1E5B1A484480F7306E35A401A2A22 (void);
extern void TrackMediaType__ctor_m1357FFFF3A466DC18E97A85F4DF7577A145FFED1 (void);
extern void TrackClipTypeAttribute__ctor_m32F3A6AF3F0CCD2AC80A08276A2C84DAD6B59ADA (void);
extern void TrackClipTypeAttribute__ctor_m6A7E59068246D7C95D43A99B54403748FE2E5F5F (void);
extern void NotKeyableAttribute__ctor_m11E8B4B00459AEB31D3345A5A8045E5D21CC35CB (void);
extern void TrackBindingTypeAttribute__ctor_m905B5F0071EF43101C6DE52F5383F8B15FF66176 (void);
extern void TrackBindingTypeAttribute__ctor_m38D2D803DD43575FBB29F858067DF10911CF7D95 (void);
extern void SupportsChildTracksAttribute__ctor_m3FE00570A7899A3F7CF0FB8522DE0C4C21694E51 (void);
extern void IgnoreOnPlayableTrackAttribute__ctor_mAC076C2CED5738D08949C3F1BEB855D761191297 (void);
extern void TimeFieldAttribute_get_useEditMode_m029ADEC4C39E6BC43084274528B66A956BDD1E15 (void);
extern void TimeFieldAttribute__ctor_m91C16D6CAE58639D71BE206FFA6011A56BC9A53B (void);
extern void FrameRateFieldAttribute__ctor_m1312B76429B8D8A394C3C28BC001E22877F3ABB2 (void);
extern void HideInMenuAttribute__ctor_m3F3AFC914006D5CFC30B844C14E2E5BE671C1C20 (void);
extern void CustomStyleAttribute__ctor_m171564751F00A5A77E810C78FAB06AF76EFEAE23 (void);
extern void MenuCategoryAttribute__ctor_m0AE9C26FDCD04C039FA1F3995969A6822B9957C6 (void);
extern void TimelinePlayable_Create_mDBD16A025F217757ED3E323A2B2292B836B7849D (void);
extern void TimelinePlayable_Compile_mCCE0D60E8AD0D45A14341C0FC4FA055633BE7D25 (void);
extern void TimelinePlayable_CompileTrackList_m94B0B3D833D78C5A070DD587705272E8D25EBB63 (void);
extern void TimelinePlayable_CreateTrackOutput_mAD0C4882569BA478C8D59AA5130F8901BADE5DCF (void);
extern void TimelinePlayable_EvaluateWeightsForAnimationPlayableOutput_m7684913A2AF5D8265AE45AAAF46A9B006C028040 (void);
extern void TimelinePlayable_EvaluateAnimationPreviewUpdateCallback_m55098F133C54B87A45ABAF36903EAE57CDAA16F4 (void);
extern void TimelinePlayable_CreateTrackPlayable_m18539F554343F8FF12D84137E5A0CA8FFA7E8B34 (void);
extern void TimelinePlayable_PrepareFrame_m9124521CCE2CBF49C19EA1669DE2BAE6632C5153 (void);
extern void TimelinePlayable_Evaluate_m1CB17400BD2E1FE1757F8A106F67451D039C325F (void);
extern void TimelinePlayable_CacheTrack_m8C7FF43B821AC07FED21AD8C1537815FA0113B1C (void);
extern void TimelinePlayable_ForAOTCompilationOnly_m6130D5C91CE6D75036FBE258FC54A70A89345C20 (void);
extern void TimelinePlayable__ctor_m9A86459D9944225AE074C58C85B7F798E089FD5C (void);
extern void TimelinePlayable__cctor_m0800C83345E9CC0DED4AB906A15ADE0859A1123D (void);
extern void Extrapolation_CalculateExtrapolationTimes_m607FBFE8FBB128DA7AC236DA75A9097CA959BEE7 (void);
extern void Extrapolation_SortClipsByStartTime_mCDB181CE76C4633A881347AB015409999918BF0F (void);
extern void Extrapolation__cctor_mEED3F9AE8423558EE03C4A949B2954DD93925066 (void);
extern void U3CU3Ec__cctor_m68412E65388EDDBD5C698C3B363320588FC91F3F (void);
extern void U3CU3Ec__ctor_m4BEA68C8BAF3AD6FA1854F77A8584537339195A3 (void);
extern void U3CU3Ec_U3CSortClipsByStartTimeU3Eb__2_0_mAC857C3158459F813EB90B7EC80CC57E002F4562 (void);
extern void HashUtility_CombineHash_mDB3BFB2F0222F9DFEA5A43CC152AF988732681DE (void);
extern void HashUtility_CombineHash_mCD4724834FE836194D0AABEAF856DA6C625A73F6 (void);
extern void HashUtility_CombineHash_m288F5449B09EB88B46E520229DFF7C7F18CA51C0 (void);
extern void HashUtility_CombineHash_mBB79C3AEE25C2E5895BA6F85AECCFF5B215005B6 (void);
extern void HashUtility_CombineHash_mCA939EC20F8AE7BBABD2D433D3A1FB770AC30BF7 (void);
extern void HashUtility_CombineHash_m1367FE812F3C9670BD59ECF0007F2383C469A29A (void);
extern void HashUtility_CombineHash_m8C98B06FEB8B88A93104DE6E8D02B30470718E5A (void);
extern void NotificationUtilities_CreateNotificationsPlayable_mF665AFFEFDFDBD2572948D0A9998348CC268A28E (void);
extern void NotificationUtilities_CreateNotificationsPlayable_mEBCA07E594A6E0979666BF7C3397ACC7A5D4753E (void);
extern void NotificationUtilities_CreateNotificationsPlayable_m3173F7D8999AA856892F6556F20735C8B24AB231 (void);
extern void NotificationUtilities_TrackTypeSupportsNotifications_mA844D8C5CD9E01E806EFA9A16F37BD7575DB6012 (void);
extern void TimelineClipExtensions_MoveToTrack_m7B9992AD171A490F566871C4856C71E6DC1D28D9 (void);
extern void TimelineClipExtensions_TryMoveToTrack_m88EB39C25E4B43EF0A5F6DE62A576B6D3F6AE958 (void);
extern void TimelineClipExtensions_MoveToTrack_Impl_mA57D81A0076D26140DF918D05C680BF0591F50BB (void);
extern void TimelineClipExtensions__cctor_mB59452B0FD743296682255CC66A9B41704B2EA3C (void);
extern void TimelineCreateUtilities_GenerateUniqueActorName_mA256C1FED6CBEA0A7A41C3CD66E3617D70E7C554 (void);
extern void TimelineCreateUtilities_SaveAssetIntoObject_m08F1B9275C4A4893711EA4281BBB3BBBEA9EEE0A (void);
extern void TimelineCreateUtilities_RemoveAssetFromObject_mDDE3FBE78C7CBC5F771632F41076E68108475013 (void);
extern void TimelineCreateUtilities_CreateAnimationClipForTrack_m499C0BAB55D23B8B1759B4EFFCA6EC100B056770 (void);
extern void TimelineCreateUtilities_ValidateParentTrack_mDE431017F5F40FDE73B7CDFACADDD1F484316CE6 (void);
extern void U3CU3Ec__DisplayClass0_0__ctor_m8C4942004C3562D0E7CEF1A8EBA62FE27E649328 (void);
extern void U3CU3Ec__DisplayClass0_0_U3CGenerateUniqueActorNameU3Eb__0_m06DEF88A9AE88C0A9FEA42106BBB9D0E79B92D50 (void);
extern void U3CU3Ec__DisplayClass0_1__ctor_mE96B2A914FE0820EA130DFB44B1E713AD3FBFB90 (void);
extern void U3CU3Ec__DisplayClass0_1_U3CGenerateUniqueActorNameU3Eb__1_m63691DF652C67A4727D3AD671C58FA56CA3FAEDB (void);
extern void TimelineUndo_get_undoEnabled_mB97DE8A738C3CBAB1B3BC7607D78EF45BD173AF1 (void);
extern void TimelineUndo_PushDestroyUndo_m502EE75014700236190DF20E9B5743E100E4E840 (void);
extern void TimelineUndo_PushUndo_mDAB595E45185F92C279C990AE284325133967B04 (void);
extern void TimelineUndo_PushUndo_m1FD04DF7E2F50E65198221CDDB7B770C566037AE (void);
extern void TimelineUndo_RegisterCreatedObjectUndo_mDB8ECC4BFD7A3491605CA1F89CE349ED35750365 (void);
extern void TimelineUndo_UndoName_m0E19A1C9B54C2A0735D35FF4F8DFC92EDA12E192 (void);
extern void TimeUtility_ValidateFrameRate_mFDD6870A06AB0E87193D1152E370161AF173992D (void);
extern void TimeUtility_ToFrames_m42B1DBF623EBD722B8B393C34A3F5FF33D5F6188 (void);
extern void TimeUtility_ToExactFrames_m442908215ADBB76C41A4CC2C031320379E50095C (void);
extern void TimeUtility_FromFrames_m0D428831B080A1E23EEF38CD7F15461D7FD651CC (void);
extern void TimeUtility_FromFrames_m90B7D5F87C01001AE4B9F712E8B7F517CAAF91F8 (void);
extern void TimeUtility_OnFrameBoundary_mE89A73D9D109EFCAAB028DEA3A545276EC8A3A68 (void);
extern void TimeUtility_GetEpsilon_m2DDCC59890F0A007D609E29CD1BC62B04EF6FB7B (void);
extern void TimeUtility_PreviousFrame_m28CFEC3FC57D3B1EC62729D95CE831F6A7126FE7 (void);
extern void TimeUtility_NextFrame_mA59CE7A5E228C54649536FB4E6D29D83B5226BD4 (void);
extern void TimeUtility_PreviousFrameTime_mD8E8E61C5D3DDA489AF1FA8A24CEC01504A21098 (void);
extern void TimeUtility_NextFrameTime_mAAD5C5D9755F54EB1214A083894ED5DC4BE63C0E (void);
extern void TimeUtility_OnFrameBoundary_mAC72C8A3F1E3E3F05F03BE07CDCAD5DE7808421B (void);
extern void TimeUtility_RoundToFrame_m9A9CF7FB93ABCBE05A4987D766A6640B5FCE02BD (void);
extern void TimeUtility_TimeAsFrames_mD7686EA3126E3A791789F999B9B9E25B6F2178AD (void);
extern void TimeUtility_TimeAsTimeCode_m448D4FC6FD14FD5816A4DA71F82218C4121ADFA1 (void);
extern void TimeUtility_ParseTimeCode_mC51B6AEBBABD417C83090B0A8C1DFE5BE133A438 (void);
extern void TimeUtility_ParseTimeSeconds_m28AB4715C13959B4AB0829540082001D158335DC (void);
extern void TimeUtility_GetAnimationClipLength_m0C35066397FC1474ED4B04934DBAB064CC1891DB (void);
extern void TimeUtility_RemoveChar_mB4D81436DF7CAC6C2768E463808F0CDB4D4D7D91 (void);
extern void TimeUtility_GetClosestFrameRate_m777D1AA22EBF63B3F327B4744C8F304F5E3F59C9 (void);
extern void TimeUtility_ToFrameRate_mB8BD26B2A1C5BF1DBAC8B55BB3CCB4D86294E1A4 (void);
extern void TimeUtility_ToStandardFrameRate_mE55BCB25BFF3ED034F50D98E31D2595C0C8D8C90 (void);
extern void TimeUtility__cctor_m49E229AEEAC66C8DE40593C47BBC3F9552CBDA1B (void);
extern void U3CU3Ec__cctor_m59AEBAC840298E7D2900A2B3BF20699F650D2477 (void);
extern void U3CU3Ec__ctor_m56DDC30169ADA8554EE8A0BC9D347AD024BD36A7 (void);
extern void U3CU3Ec_U3CParseTimeCodeU3Eb__19_0_m53BDD117A1B72C2F098A9DA0ACA32170E2BDD953 (void);
extern void U3CU3Ec_U3CParseTimeCodeU3Eb__19_1_m0A1AEDE68065D8D17AB8C877AD0A248E91AD316C (void);
extern void U3CU3Ec_U3CParseTimeSecondsU3Eb__20_0_m263DF6B0C61B2D416F0CBE1BB77E39AA7ED3BF00 (void);
extern void WeightUtility_NormalizeMixer_m6EE0D0701870481B80B9B8AA1F259C1A786379CA (void);
static Il2CppMethodPointer s_methodPointers[885] = 
{
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mAE7D02B3CDEA9E9B7FC86BFBBFDF11AE909326C0,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m21A44C012FE21C3D670077F5ACA3BC10CDCF70D3,
	ActivationMixerPlayable_Create_mB03A5A5C425D4F901ED4B55E89799A5D207EEC47,
	ActivationMixerPlayable_get_postPlaybackState_mF0384E7535C7AEA8617EADC8D9832D8C5CC46D46,
	ActivationMixerPlayable_set_postPlaybackState_m5FDC121E23D90F62C75843C5A363DE97E7DD2EB3,
	ActivationMixerPlayable_OnPlayableDestroy_mE80DBAA8FAFBA511669B4E78B55751AA5898B074,
	ActivationMixerPlayable_ProcessFrame_mDCC3B223FA8AB796778E2E8B979A227BE8DB588C,
	ActivationMixerPlayable__ctor_m513A8FE0373D3B5A4FFC829ED4DB38735128582E,
	ActivationPlayableAsset_get_clipCaps_mF49360399D36AEA60F85A01905F9CC9176F32FA2,
	ActivationPlayableAsset_CreatePlayable_m5A7D557E44615A717158ADB9CAFAD5C1FA70279A,
	ActivationPlayableAsset__ctor_m367911F439EC602657B306B2F180B46D959A87B3,
	ActivationTrack_CanCompileClips_mA606603F95C9516112052DD4B17136499DE42193,
	ActivationTrack_get_postPlaybackState_m1294D74ED475B43256FC9AEB2AB2DEDC2019FB82,
	ActivationTrack_set_postPlaybackState_m25320A2912F8EF46DEDDE08963D837CA3B1E55CE,
	ActivationTrack_CreateTrackMixer_mD0C559021DDE8B870EE86BCA9BE59DF1FB54C38F,
	ActivationTrack_UpdateTrackMode_mD01BE7C19739DBC651C645F5AD11FB6DAF5EBB1E,
	ActivationTrack_GatherProperties_m3808D2F7F5C1FAE282906617F42A80448A33F1FA,
	ActivationTrack_OnCreateClip_m14768AEC5F93456C8BB50050D14D09DF68EDD703,
	ActivationTrack__ctor_mC854FF5136E9975AF73188924C9A939FA1B973C4,
	AnimationOutputWeightProcessor__ctor_m474D5FD59685D4A13DE5307630091A0414E91E0F,
	AnimationOutputWeightProcessor_FindMixers_m82DA2CCB4F1B0AA24E3B8CFAB6DDEC8176EE2F65,
	AnimationOutputWeightProcessor_FindMixers_mF9681DE5BB91778AFCF883E4732DA6BE1A31A43D,
	AnimationOutputWeightProcessor_Evaluate_m3C0568570541A5BD6B6405B49B72CCE50714BB17,
	AnimationPlayableAsset_get_position_mC75DAA87C62AE6BC462492531727C2E45EC150F0,
	AnimationPlayableAsset_set_position_mF1539D8E2BCDC4BFABF2D1683EAD9AE7A91B4DE3,
	AnimationPlayableAsset_get_rotation_m1DBB59B5F15442C5CEA8F76CA8B23398459D32D8,
	AnimationPlayableAsset_set_rotation_mEE464480D66C9C564AC035507554C340E013EB8C,
	AnimationPlayableAsset_get_eulerAngles_m1FAFD32D61627D433AC648EDB670431434372123,
	AnimationPlayableAsset_set_eulerAngles_mEFBE1E1450804D8A5C2C44D276178B01CBA74F8A,
	AnimationPlayableAsset_get_useTrackMatchFields_m3CA7C4CD0E3AE6B315028847FD86485139BB1DB2,
	AnimationPlayableAsset_set_useTrackMatchFields_m020978DC261F7FBA5BE26BF2E3CCFFE79A7DA27C,
	AnimationPlayableAsset_get_matchTargetFields_m65CDF95B5D21B931336CDD663309ADC226C666F6,
	AnimationPlayableAsset_set_matchTargetFields_mCF18FDE6E3EA660995C822B5678EBB98D374FDB7,
	AnimationPlayableAsset_get_removeStartOffset_m38878C3AEB0FF2C0ABAEC4CF6ACA7E640CF896F9,
	AnimationPlayableAsset_set_removeStartOffset_mB16879678AA070E3D8A4C58FBA1267867E20B420,
	AnimationPlayableAsset_get_applyFootIK_m3D85EDBC855694E23CA22044F83F3EA9918FF6A7,
	AnimationPlayableAsset_set_applyFootIK_m2894B7F3E32B0CB12961AEC079B464F5F3195006,
	AnimationPlayableAsset_get_loop_mFB0E127EC34FDCB9E706B864733FB45580281A81,
	AnimationPlayableAsset_set_loop_mC3AF990F0EDBFF52B6D0C17234144B1999471B6E,
	AnimationPlayableAsset_get_hasRootTransforms_m74DF6202F2F64346F199101D528591FF02AFE48C,
	AnimationPlayableAsset_get_appliedOffsetMode_m6A5F22FBF4719ADE8C78BB1A13397E5BA186F908,
	AnimationPlayableAsset_set_appliedOffsetMode_m389C789650423968CF01A0830766D2346F2202D1,
	AnimationPlayableAsset_get_clip_m0170771CEBEA44040F1857332846B5E12183AF8E,
	AnimationPlayableAsset_set_clip_mF2E641EB15E80990B4587625F199F681FBFAE752,
	AnimationPlayableAsset_get_duration_m7FC1BA9D27EAFC31E19C71EB0AF485A955D8E615,
	AnimationPlayableAsset_get_outputs_mD795879A17D0640EDFB055B39B7C832136E216DA,
	AnimationPlayableAsset_CreatePlayable_mAC380311FD71AB8C793327307A1BCBE8CF152F29,
	AnimationPlayableAsset_CreatePlayable_m8B11A8EAC431FCD5E878FABCB683A4F9B74A6998,
	AnimationPlayableAsset_ShouldApplyOffset_m7D958577429B51630CAD52246A957F9BD850119D,
	AnimationPlayableAsset_ShouldApplyScaleRemove_m99FB3C3471F9B6C7A5E595308EF4ABCDB2E63841,
	AnimationPlayableAsset_get_clipCaps_m2BAF77E319A9EE382FF20AB13A56907312C064F8,
	AnimationPlayableAsset_ResetOffsets_m8DC6D73742DA551BB81E5BDAAC2F186D045D0EC4,
	AnimationPlayableAsset_GatherProperties_mB16CF0B019B7CF063D76CA4B69A51C227EB24EF0,
	AnimationPlayableAsset_HasRootTransforms_mF8339B890A9F6875852E02AA6FE7EBA7F2CE8161,
	AnimationPlayableAsset_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_mE7288B1EA8E582C2661277C75FDE80D8B6CFEBC1,
	AnimationPlayableAsset_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_mA584D2BD183C657C1DB990C055CE79E3880C88E2,
	AnimationPlayableAsset_OnUpgradeFromVersion_mE19EC4FA01C0D93E8FAC4E4D56FC3B5DBB2C2354,
	AnimationPlayableAsset__ctor_m6F298BFCD42F32F57484C3329B147586ECBF141B,
	AnimationPlayableAsset__cctor_m1748B5EB189D81582B97A7BBD370D03960F1FD7D,
	AnimationPlayableAssetUpgrade_ConvertRotationToEuler_mB851991DE0D6624E9C49487DFA8630FA6AC08491,
	U3Cget_outputsU3Ed__45__ctor_m0E2DA3AD05663EA90B980D7150DDAD589F0336DA,
	U3Cget_outputsU3Ed__45_System_IDisposable_Dispose_mA7F31F3D37807CE6BA6B290B62C93BEE16B0A6D7,
	U3Cget_outputsU3Ed__45_MoveNext_mF982F762F9D3069822C9C69545FA6E439F6F1DC8,
	U3Cget_outputsU3Ed__45_System_Collections_Generic_IEnumeratorU3CUnityEngine_Playables_PlayableBindingU3E_get_Current_m7E523BBDF4F714C6737AC3D2709BB75FCF0ED93E,
	U3Cget_outputsU3Ed__45_System_Collections_IEnumerator_Reset_mC8FF472CBE1E59FE4E740083CA6D09ACF0354C0F,
	U3Cget_outputsU3Ed__45_System_Collections_IEnumerator_get_Current_mBFDCDFF0341D56EEF2ACE33B8504A53CECE1F7AE,
	U3Cget_outputsU3Ed__45_System_Collections_Generic_IEnumerableU3CUnityEngine_Playables_PlayableBindingU3E_GetEnumerator_m89F51E97B14897204A10EB33B1DF48AD64260492,
	U3Cget_outputsU3Ed__45_System_Collections_IEnumerable_GetEnumerator_m571AD1499FE97BA88AC1996D6DBCB5FB81EA6B64,
	AnimationPreviewUpdateCallback__ctor_m9A81A41A73B25FFB193B05DB41AAD480B5019302,
	AnimationPreviewUpdateCallback_Evaluate_m4859735BDA333DF67BA53C5157909003103DC4FF,
	AnimationPreviewUpdateCallback_FetchPreviewComponents_mF1C66A832544073C8DFA760DB6F1910DED0BE2D2,
	MatchTargetFieldConstants_HasAny_m73744E801BA5F758254854BF5283EE4A8BFA13B4,
	MatchTargetFieldConstants_Toggle_m649C32A5427F1B9959B3D58468956DF2FBE237DD,
	MatchTargetFieldConstants__cctor_mC761D8CE72745FF29695B219E52D3AF62D2DC5BC,
	AnimationTrack_get_position_mBF745D40410A1F530116319EDA2ED612851C863E,
	AnimationTrack_set_position_m58583D926C865297D1D248FF3DB33E9A101115F6,
	AnimationTrack_get_rotation_m5A3EC08F5E1AD62FC72BA69294001BAC42C15251,
	AnimationTrack_set_rotation_m372BEF805EE2FEA03E5766C853CD934FADAF6FFD,
	AnimationTrack_get_eulerAngles_m9EDE81D8FD7675470DF4CFA620D911019B56C768,
	AnimationTrack_set_eulerAngles_mCF06238E5C41BE05161163DCA33ED817BF1E24CD,
	AnimationTrack_get_applyOffsets_m10D70F0C85FB9C462B4659813EDB37BF84D86B9A,
	AnimationTrack_set_applyOffsets_mF644A614EB6FB30B8604D917BA8F4A710983A9DE,
	AnimationTrack_get_trackOffset_m8D91B82F85D98F276E45FB805628232278AF2D2D,
	AnimationTrack_set_trackOffset_m66B617D407D7D8CE17EDAD849DEC34FD1E0B1CE5,
	AnimationTrack_get_matchTargetFields_m130ABF7AA0D2F38D40127029CA02993A5FA998F3,
	AnimationTrack_set_matchTargetFields_m14ED03793080CDD4BDF4D1B91418C430853D9330,
	AnimationTrack_get_infiniteClip_mE55BF71562803B5C849A335584AE526A2747E5B2,
	AnimationTrack_set_infiniteClip_mA700EED208399A4DCA3314E3F848BBE698DAB6C4,
	AnimationTrack_get_infiniteClipRemoveOffset_m18DFC76B9AFEC35654AF299A62264E925202146B,
	AnimationTrack_set_infiniteClipRemoveOffset_m7999F05C17EE0524CCC197732CADB8927A0D5454,
	AnimationTrack_get_avatarMask_m4E6BD1DAC9D92FD6CD663DADEE525A158CFE4F01,
	AnimationTrack_set_avatarMask_m97BFF1B75D7E891C249CCE0917F1CBCB886FD30D,
	AnimationTrack_get_applyAvatarMask_m2FCA8FA363B449E4420E8492F92E5270A2D4FA7E,
	AnimationTrack_set_applyAvatarMask_mEAEA18A18FB74A33D808B94F20C01A2D880A8606,
	AnimationTrack_CanCompileClips_m67F41F2F20E51CB999957ACD3D9C31B799EC3026,
	AnimationTrack_get_outputs_mF3776BF93312A616ADC9D18F061AD972626F0EB7,
	AnimationTrack_get_inClipMode_m0559987652D8B45851CF6EE37AAE259156007271,
	AnimationTrack_get_infiniteClipOffsetPosition_m23355191742B9EF9877193DCD3C77758D9435666,
	AnimationTrack_set_infiniteClipOffsetPosition_m74A78994C38BE22C6AD33C5EF4CD710CCAFDD166,
	AnimationTrack_get_infiniteClipOffsetRotation_m3A152B45D0F99F716EEFA25BDF0A932FACB02052,
	AnimationTrack_set_infiniteClipOffsetRotation_m0B8FF5AB8D6F4EE92C022D40510C5C41AD3896C2,
	AnimationTrack_get_infiniteClipOffsetEulerAngles_m0EF73054860F1F705E07F5729C9CB1CE0D4AA6B9,
	AnimationTrack_set_infiniteClipOffsetEulerAngles_m958891CA74BDFD73A39E2427084FA3777939C29E,
	AnimationTrack_get_infiniteClipApplyFootIK_m813FEE3250C60BFEF060210215BD68E3C5CC29CB,
	AnimationTrack_set_infiniteClipApplyFootIK_mCC194F75CD06FA7E9F0EE890E7A2AADA9C387BAB,
	AnimationTrack_get_infiniteClipTimeOffset_mC4376105C76EAB9E8139C426D3BD218ED21B4A7C,
	AnimationTrack_set_infiniteClipTimeOffset_m93F31C8AEFE3DE5FE275F7EB250FA98F5978AD3A,
	AnimationTrack_get_infiniteClipPreExtrapolation_m4B4906080C16E2D7C07B409787363735F8C8AB85,
	AnimationTrack_set_infiniteClipPreExtrapolation_m5A1D2B6511FB01EEDF0405DB5914F43D1289EDE3,
	AnimationTrack_get_infiniteClipPostExtrapolation_mE68867202ACB337F11C12FD75C196278FFEA34D9,
	AnimationTrack_set_infiniteClipPostExtrapolation_m8197868300D197E73BFB72D95EEC0CA3F3E69D6E,
	AnimationTrack_get_infiniteClipLoop_m0076C92D7935227835C007059EC228D6ACA5205C,
	AnimationTrack_set_infiniteClipLoop_mE52F35E3A552CD510D873A9A1865D474974B2750,
	AnimationTrack_ResetOffsets_m9CA8762D3880419B7CA71E966228FF801A0F47A4,
	AnimationTrack_CreateClip_mC2014488815D2C34B95F739D82F9A93A195FD919,
	AnimationTrack_CreateInfiniteClip_mC2EB76F9F2462F3AED209E90018A86DE5D36843B,
	AnimationTrack_CreateRecordableClip_m2F49C361C87418375F803A4760EDF011A9D960C7,
	AnimationTrack_OnCreateClip_m5A7462347F4CAA6EC0B248F534F45C89B8F38786,
	AnimationTrack_CalculateItemsHash_m69D9B8299B8E4085DE184A3FC278BB33C6CECF70,
	AnimationTrack_UpdateClipOffsets_m0DE8CEBC4F96E1F40669DC8732F2E04EBADA1331,
	AnimationTrack_CompileTrackPlayable_m8AEA486513BF42BC4682217FCB37A61218B68094,
	AnimationTrack_UnityEngine_Timeline_ILayerable_CreateLayerMixer_m2C90244DFA117930E0911876688435A62CF9C628,
	AnimationTrack_CreateMixerPlayableGraph_mC3352DAB3A0AF3D277C78B94D5DC2DB763DFD3F7,
	AnimationTrack_GetDefaultBlendCount_m34C24E878EB89D3D5B56AECED494D8375E35A64D,
	AnimationTrack_AttachDefaultBlend_mAF67FD6982E9EAA8CAFCB0106C7F324547400DFC,
	AnimationTrack_AttachOffsetPlayable_m854FE97C64D490D94150A604554C01A9433813B8,
	AnimationTrack_RequiresMotionXPlayable_m8BF0F53DE406934214E0E402295F8E432E02744C,
	AnimationTrack_UsesAbsoluteMotion_m305667FA27FD4DFC54406F5A21A4344A069EAF4C,
	AnimationTrack_HasController_m9DA394CFD76B80E53A80EC26FEEC9F1769BA6684,
	AnimationTrack_GetBinding_mFA6516C67603F256A7306024BB0DA86496F42B43,
	AnimationTrack_CreateGroupMixer_mF2B248685103E2FBA973109155FE0D5153946B8A,
	AnimationTrack_CreateInfiniteTrackPlayable_m7951EDDA36CE14693AFFF9C460C0FC3E5B732A88,
	AnimationTrack_ApplyTrackOffset_m7ACA50839B41B3AECEA66E60986AE71E9FA3F7A0,
	AnimationTrack_GetEvaluationTime_mE2271656B917685A4108E734F77ED34FC75EC3AD,
	AnimationTrack_GetSequenceTime_m366C5486DCF37C3A41612B08749F063F491ED593,
	AnimationTrack_AssignAnimationClip_m26DB250244CE6240EF4F946A4D93F311D0D1F367,
	AnimationTrack_GatherProperties_m5FE1175C9AE614E6878B582CEF3DE396837B54C2,
	AnimationTrack_GetAnimationClips_m7459AE8A7BE31D25B0625297A0C94173042A0207,
	AnimationTrack_GetOffsetMode_m8D2EA6334F72FE079612C5B71B745CAF91111C9D,
	AnimationTrack_IsRootTransformDisabledByMask_m130CFADD77EB830E9E7EA6DBBE88EFEE73E1D0C7,
	AnimationTrack_GetGenericRootNode_m40F3B83DE0D88C67592549EBDFADCD0168D63C3A,
	AnimationTrack_AnimatesRootTransform_m2D168DCB100F72EFC128C2DEC6B068E2CC216F1F,
	AnimationTrack_FindInHierarchyBreadthFirst_m021FFC49FDED402415A469E33CC690FFCC7AD1CB,
	AnimationTrack_get_openClipOffsetPosition_m75E1CFC9D51546A9F24A0EB998B6E87794C3A705,
	AnimationTrack_set_openClipOffsetPosition_m9752610280CE6027BAEE683C36C9D290B442A6DB,
	AnimationTrack_get_openClipOffsetRotation_m10B5894DD11A0AF08D99594D1FB459B6362B9303,
	AnimationTrack_set_openClipOffsetRotation_m1D1B9B56375DE34C8BEA905DCC0299B39BFC7B02,
	AnimationTrack_get_openClipOffsetEulerAngles_m355BD7957BCD76AC87769CED0927FD9728C3F2EA,
	AnimationTrack_set_openClipOffsetEulerAngles_m562570438DFB431C65AB322D1753D6210DB62787,
	AnimationTrack_get_openClipPreExtrapolation_m27E1C2DE521D2F5B0AB5569695CC1C060416FDB4,
	AnimationTrack_set_openClipPreExtrapolation_mC2D775230A229A66B1E98C131FAE66C2312E4BC3,
	AnimationTrack_get_openClipPostExtrapolation_m81FD4E8B992C6758D21790E77A57DAFB2CFB903A,
	AnimationTrack_set_openClipPostExtrapolation_m1C35F5DC4A86ADA4548AFAFA8323A118433B91FC,
	AnimationTrack_OnUpgradeFromVersion_mB41D3B6A330F4AA2FE59E61632E4B1F99C2866DF,
	AnimationTrack__ctor_mDB5857315630BBECBD1CD59097456E0F331B9264,
	AnimationTrack__cctor_mF7E0C93F99D88D2E284763DB25C116DA566C1135,
	AnimationTrackUpgrade_ConvertRotationsToEuler_m4ACA9D918164FEEDBEC2881DE13C779094D253D5,
	AnimationTrackUpgrade_ConvertRootMotion_m54DF7747856D06D2179EB95642D299E5D6A97A21,
	AnimationTrackUpgrade_ConvertInfiniteTrack_m4006E7259789D3B8829ABC5700DD2828C3EB2A6F,
	U3Cget_outputsU3Ed__49__ctor_mD93688EDAA61ADD8474DC977257C4865A2EC6389,
	U3Cget_outputsU3Ed__49_System_IDisposable_Dispose_m607FCCC43C82ECC93F674CCB5415929336502C51,
	U3Cget_outputsU3Ed__49_MoveNext_m7BDA8087249FD461975437E56C9BFE7B80C8A099,
	U3Cget_outputsU3Ed__49_System_Collections_Generic_IEnumeratorU3CUnityEngine_Playables_PlayableBindingU3E_get_Current_m8665A050400E9DA6702260117B51F8609806ECBE,
	U3Cget_outputsU3Ed__49_System_Collections_IEnumerator_Reset_m5D524429CEE49B55F5EE0E3015806B835110C113,
	U3Cget_outputsU3Ed__49_System_Collections_IEnumerator_get_Current_mF8DC66EBBBD2A455C22F3B8B229EE3FC09ABC96F,
	U3Cget_outputsU3Ed__49_System_Collections_Generic_IEnumerableU3CUnityEngine_Playables_PlayableBindingU3E_GetEnumerator_m3B62F5C3F8AF686D249000D59B0052197BECB161,
	U3Cget_outputsU3Ed__49_System_Collections_IEnumerable_GetEnumerator_mFC98EDB93155902AC6052BEAEBF678837BD62B2C,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	TimelineClip_UpgradeToLatestVersion_m3C367DA0661298065465C583C9D33AC4B63ADF0D,
	TimelineClip__ctor_m69070FAC237B4E3DC873E07AD1C3902E5C031939,
	TimelineClip_get_hasPreExtrapolation_m2D086ECAC0C10D46FE58D22AEAA9CC1EAE94C196,
	TimelineClip_get_hasPostExtrapolation_mE6565EF983300E526B31AC25C57F8B583C6B7AC6,
	TimelineClip_get_timeScale_mF76C0A2D5CDBF201F2CC01967928CA7FFC261474,
	TimelineClip_set_timeScale_m29FAE01EF4CF682E2C82169B9D6976289C2665D0,
	TimelineClip_get_start_m76BB53BEBD6B700D5A4197F72779A321DE55B296,
	TimelineClip_set_start_m476586FED5274C2691B71BC75C76E3F471332BF5,
	TimelineClip_get_duration_m4DC76F051723CC7427813C076B255BA8BB4366F7,
	TimelineClip_set_duration_m1904C6BD6C64F8CC7AD09256F559C8C62AB97001,
	TimelineClip_get_end_m4C3E4DF4B095A1D60694B379EA839A68E3C4217C,
	TimelineClip_get_clipIn_m0ABA66BE9CAD32C80313321C963C8FA9AB5FC1EB,
	TimelineClip_set_clipIn_m5ACCEA54D376384ED1264CE7F108BD64F725C67E,
	TimelineClip_get_displayName_m61712CDBEA102FB64B5E3464E4420843E1FB7111,
	TimelineClip_set_displayName_m5F725FB8B45340748ECFAA870D034C85352F54CD,
	TimelineClip_get_clipAssetDuration_mC3509BB4EF5397A55A42E02BE6A6730998B5EAF5,
	TimelineClip_get_curves_m7572F0ACC8D51E605271B6002685740F946FC761,
	TimelineClip_set_curves_mDE58D1A741554AF3E95F7BC53625E5D7F3BD212A,
	TimelineClip_UnityEngine_Timeline_ICurvesOwner_get_defaultCurvesName_m65DD8FEFE6FC8AADA51EECD8906A9066C7EF6C18,
	TimelineClip_get_hasCurves_mDB6EF3ADD8FF4693992F7EA092F269A6F3631EFD,
	TimelineClip_get_asset_m49BF68F5E0C41EBA5145FCA0C97D7146DF016120,
	TimelineClip_set_asset_mF8539EA76B6C0F19E2AECBA025C70D605322195E,
	TimelineClip_UnityEngine_Timeline_ICurvesOwner_get_assetOwner_m542C93F1E1B0382CCA596359A1A96DAC55539D66,
	TimelineClip_UnityEngine_Timeline_ICurvesOwner_get_targetTrack_m46FB66ACADD2B522822EB7691A4A5F1C976A368F,
	TimelineClip_get_underlyingAsset_m1DE5682815DA82061499CCF642ECDC9CAA9C1881,
	TimelineClip_set_underlyingAsset_m02D44F445992B5431831BF7C77466164CA527B85,
	TimelineClip_get_parentTrack_m3E33146230902DCAFAEAAE034F5DCBB70BE03A8C,
	TimelineClip_set_parentTrack_mC3646EF3C4198254776CFA1E47CFC2636C1BA69E,
	TimelineClip_GetParentTrack_m560CB13E873CE39EB7E3754B53B1594D2A58C37D,
	TimelineClip_SetParentTrack_Internal_m39F7D49888E7EBFC36796EAE0C7BE6C263C0FB02,
	TimelineClip_get_easeInDuration_m171A5C1C8BA1392C4CBC7F5C17A90EAF915A2145,
	TimelineClip_set_easeInDuration_mF310C781E91789DAA8AE5A593C9BA46FC173E01C,
	TimelineClip_get_easeOutDuration_mAAE6DEE05138F6902199D985A3EE6E26BCBB35EC,
	TimelineClip_set_easeOutDuration_m4666F442BADEAFAC3C8E450BCF987E403D1E42ED,
	TimelineClip_get_eastOutTime_mE21671B32EE9CB06C994A5B91D5E86741F2BB30B,
	TimelineClip_get_easeOutTime_mC16D31D13CB3953A0ECE61CCD13381318B6AC115,
	TimelineClip_get_blendInDuration_m3A18D7C0942B43C5A171422EDC1D41260C1269D7,
	TimelineClip_set_blendInDuration_m34F303216992A919EF47D01B874EEEB47C8470E3,
	TimelineClip_get_blendOutDuration_mA1BB945DFE74A6AB5EB112587ED3509CFAF43379,
	TimelineClip_set_blendOutDuration_m5013A1CF4593F805143A0E665261EBB0B00298F0,
	TimelineClip_get_blendInCurveMode_m4C612BAD55BB180A24C9FA67A8F9D84C907E170C,
	TimelineClip_set_blendInCurveMode_m4138FBEEA1484C9DDAA0C859B505E8E20B4DFE8A,
	TimelineClip_get_blendOutCurveMode_m106D929CE951F07CB31BF9DEA7B0D2B1D830F093,
	TimelineClip_set_blendOutCurveMode_m5CBC0E1382775395757B85824BFBB39571241A9E,
	TimelineClip_get_hasBlendIn_m9B9C96AAADD801969D9C97D4130428EEB6B6D368,
	TimelineClip_get_hasBlendOut_m8444D00A050FD627EA6A7DC834B5B14B2C845E51,
	TimelineClip_get_mixInCurve_mCF8C05F353EF6EF3E115DEC17FA10DB481724B45,
	TimelineClip_set_mixInCurve_m53265562D7425F388E7DC93717D8707F816E65EF,
	TimelineClip_get_mixInPercentage_m9120693B790082B5ED9FC988B3C9B5F06C946B10,
	TimelineClip_get_mixInDuration_m754EA2369F50687A58E3A883DBA5DA79C090B243,
	TimelineClip_get_mixOutCurve_mAA470F75E97A1960E50BFA0B97E4BC2E0873D687,
	TimelineClip_set_mixOutCurve_m93EA2EF70C31BC5625A419E6830AFB6DD594A729,
	TimelineClip_get_mixOutTime_mD780BF14E8EE91F808CEB62BEE0A93B30C6233A6,
	TimelineClip_get_mixOutDuration_m19A9A249A1628E222E534662B0D4CB30F4DBCB0E,
	TimelineClip_get_mixOutPercentage_m4D9124E362272E0DA49CA228C72E5CEEF39BB3CB,
	TimelineClip_get_recordable_m2BE0E30776FECD30BAE62D78399B6402682B2001,
	TimelineClip_set_recordable_m38B678C027026275DD27636B8AF8345D94D9403A,
	TimelineClip_get_exposedParameters_mB96B6748027997A9DFEF943387DD86F74876B279,
	TimelineClip_get_clipCaps_m11FD6AE29AD801B99B64A71C6C76680FA213FC0A,
	TimelineClip_Hash_mA9299778B1C618795049AB37ADD4A01A7E5331DE,
	TimelineClip_EvaluateMixOut_mA36406D289DE5A9831B821AC5A9C51DF21B47C83,
	TimelineClip_EvaluateMixIn_m206FBF82C1AF116724AC429CAF7C4759E6D28D60,
	TimelineClip_GetDefaultMixInCurve_m90A35726DC5817536F73783D5B1028F64251EDE4,
	TimelineClip_GetDefaultMixOutCurve_m07738400AEE1FB893C14562EF2EE11142E767AE6,
	TimelineClip_ToLocalTime_m975B84C7F3371F39F73AD8DA1F89C3C825D40E1E,
	TimelineClip_ToLocalTimeUnbound_m8E307B2FDD49E30A57137FD94BA001B9157DEA6F,
	TimelineClip_FromLocalTimeUnbound_mBCE77F0E508F86A77DE09CE16623A6FB374700E1,
	TimelineClip_get_animationClip_mFDFBA4582868E85C0B56242660C9EF3B4442D07D,
	TimelineClip_SanitizeTimeValue_m3185743B561388628273A3FBD9AD1C7C4AD3A9DE,
	TimelineClip_get_postExtrapolationMode_mDF06AB6BEE7906C373109C04775C0B3CA83D8685,
	TimelineClip_set_postExtrapolationMode_mD2B003EB90D2C816050F5FF12B662401BB858092,
	TimelineClip_get_preExtrapolationMode_mC20AE951A26D337093ABC65FD2DBF9C5CBE8E3FA,
	TimelineClip_set_preExtrapolationMode_m26E6222B67AFA46CC6F1EE1730C8819B19D035AF,
	TimelineClip_SetPostExtrapolationTime_m659389D7F8F07AE58FF2D1B4BCFAB9FF13691C35,
	TimelineClip_SetPreExtrapolationTime_mE9DAF291C193942B0703ADB10CA1BEF1B9D0C4DA,
	TimelineClip_IsExtrapolatedTime_m82974EEE2D3C650B90C2F5F5592FB31B7029AB6C,
	TimelineClip_IsPreExtrapolatedTime_mCA79ABD6E8F0408DA16E8BB57C9509BB092CC753,
	TimelineClip_IsPostExtrapolatedTime_m2C8FFEF3F821D6CD45A7D8F5E06F3E7705C6B1FE,
	TimelineClip_get_extrapolatedStart_m4092C97F1EB880583FA92B8FAAFC208D245630BF,
	TimelineClip_get_extrapolatedDuration_mFA3957094871DA9151A26AB04A894E771FFC1F21,
	TimelineClip_GetExtrapolatedTime_m10D665C5B1EC5CBCF3FC38E10BE6A2276D5E7405,
	TimelineClip_CreateCurves_m494AE0E2457ACD87387E039AF081F93442FACBB3,
	TimelineClip_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_mDC9EFB3125995E8EFA91EB3CF27E9B2784B60DE8,
	TimelineClip_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_mE0860037B34E43C2CB25567793EDB4F48B018D4E,
	TimelineClip_ToString_m56D472178DA2F04872969618EF5CA3E142E9FD9A,
	TimelineClip_ConformEaseValues_m6774B17BD4E1E6734FC21CB1F67C267DF92DD623,
	TimelineClip_CalculateEasingRatio_mE7960562253BE9447E532E1BA3709AF9B0834D75,
	TimelineClip_UpdateDirty_m3F5A0077C88F2C9D481AE93CEF2E16DCD235EC08,
	TimelineClip__cctor_mD74B4E06C1F4905731E8C1E06F0BB24BCED88317,
	TimelineClipUpgrade_UpgradeClipInFromGlobalToLocal_m59F498F944599E3F1BAF6B0D5AC37E5E38770E7C,
	TimelineAsset_UpgradeToLatestVersion_m34BA46E2D4643EFB179CBFB81706EC27785CBFA0,
	TimelineAsset_get_editorSettings_m3D5DEC0305D9E517E29CB898C789C3B42D3B1CB9,
	TimelineAsset_get_duration_m22226DF293F2D489E63D881775112FC26518E297,
	TimelineAsset_get_fixedDuration_mB9B8F8FBE5DCF918C17AC705E6D17A730DB9A69C,
	TimelineAsset_set_fixedDuration_m10FF2CD4C12508C9F4938A17B2EF7E81A5C701AB,
	TimelineAsset_get_durationMode_m58895CD3D78D4F4A912BD149FD810B3AE6AA0034,
	TimelineAsset_set_durationMode_mB082ACE2157EF8472C2C30C07DAE8DDCC6E9008D,
	TimelineAsset_get_outputs_m232A9F7BCE2E4BCFB2115B9ABB26D53C7D8C478E,
	TimelineAsset_get_clipCaps_m0F3ACD23FAE67D92F0F67A056F0AC64BEC0A3AA8,
	TimelineAsset_get_outputTrackCount_m07CED6FE6B99D62D0E5EB3C2A91863E21E1CB70B,
	TimelineAsset_get_rootTrackCount_m89C66C83C874A1D60BE068E40C5CB94033F0A7ED,
	TimelineAsset_OnValidate_m4D4626C6372A467AE9EB04DA835A8CD9EE75EDF0,
	TimelineAsset_GetRootTrack_mC41A0CF4127692DDEEBAA8BA973BE4392C49B83A,
	TimelineAsset_GetRootTracks_m6340C1C261F14F8FBAF7116F775780C75068F05F,
	TimelineAsset_GetOutputTrack_m8002892E722FE6CA9C8B8EB7D7040BE1E47D672D,
	TimelineAsset_GetOutputTracks_m324315A337B30921D4B5E740E1FC2BE81563B26F,
	TimelineAsset_GetValidFrameRate_m5F0BAEC92EDFDBE539E1A0187DC2F185DA8D5E72,
	TimelineAsset_UpdateRootTrackCache_mADE060D9C1F785CFA83E43B4CB895D772EF74749,
	TimelineAsset_UpdateOutputTrackCache_m4A4249728370EB77115CDCB08D2A08C4D3925818,
	TimelineAsset_get_flattenedTracks_m51B0499CC8CD43CB520F6B84D37FCD19C8FE4F18,
	TimelineAsset_get_markerTrack_mA1522943FBA0FDFDB0765165F00B6F9373EB01A3,
	TimelineAsset_get_trackObjects_mFE1A564170B932E65741C29D0A94B5DA55C7D676,
	TimelineAsset_AddTrackInternal_m1699C0B3168FF4D471509AA664AA92BCC3000A57,
	TimelineAsset_RemoveTrack_m34E111A2A0D031C27F8EB42DD4CD014A5883ADC2,
	TimelineAsset_CreatePlayable_m015AE0523BB6A93F3F0070BF0757ECA3199EB5FE,
	TimelineAsset_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_m4ED0C35C1033C2177A683A17F97863B2F3A8982F,
	TimelineAsset_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_m7C5218B558DC68FA00A3A7AC87FAE02F589C2808,
	TimelineAsset___internalAwake_m3BDB3D8798B27D100140D4C189285ACCFD116F18,
	TimelineAsset_GatherProperties_m966DA31A1B9D51FA370CB0DD4B3FC9036C230EEA,
	TimelineAsset_CreateMarkerTrack_m959223B301DE70DED79380138F93BF1565D2B586,
	TimelineAsset_Invalidate_m601FE157269B617EEB1D840439E36715DC81AFC6,
	TimelineAsset_UpdateFixedDurationWithItemsDuration_mE79C311415955F474C4C885E4CF8B7124CBBE008,
	TimelineAsset_CalculateItemsDuration_m2904F09544AB814380305EA97D6EB11AFDCFD0BC,
	TimelineAsset_AddSubTracksRecursive_m03EBD254DE32A5C23CE13E80EFFC4BDC51AD960F,
	TimelineAsset_CreateTrack_m327D088F33507A544DE566503CDF6593C024C1ED,
	NULL,
	NULL,
	NULL,
	TimelineAsset_DeleteClip_m61EFBF21AF49C5FE55CD70060C19B81657170B43,
	TimelineAsset_DeleteTrack_m80BEC8B29D94214E5663B0F64E725726B7EBAC08,
	TimelineAsset_MoveLastTrackBefore_mB1C0BE93519C75B57FC07BCFBCBFA912A61841E4,
	TimelineAsset_AllocateTrack_m8DFE21D5BC86310C82E23E43CE3D7D04759EA2F5,
	TimelineAsset_DeleteRecordedAnimation_m3FF55ADA0F576F30D7522D85D999E5A2A8824939,
	TimelineAsset_DeleteRecordedAnimation_mBE3F874CEAEFD795EC06C74E50F3E007890369C8,
	TimelineAsset__ctor_m75D9A08991F60CBFEFDAD23DC01B6A49A4601E4C,
	EditorSettings_get_fps_m2B7B48B6BBD590B690E91D5C761336A3454796A2,
	EditorSettings_set_fps_mC963EB8F7CAFC6A54CBF37278ADFB0311161674F,
	EditorSettings_get_frameRate_m699A6599212042DC13CB0DB91C7057B9644A0037,
	EditorSettings_set_frameRate_mE8F1DFC8C2B4203B48865F22DB7C5309932EB32B,
	EditorSettings_SetStandardFrameRate_mA67A361C75A563095119DC1E6D20FD455ED1AC1A,
	EditorSettings_get_scenePreview_m1FF2AB573D0405B69314643070555FFAF997B3C0,
	EditorSettings_set_scenePreview_m7996AD908D830ED6DB8D9FF103FE848BA9AEF553,
	EditorSettings__ctor_m239B6DF8C75E8C21F4FCA53B668E9C7E64D1A8A3,
	EditorSettings__cctor_m4FE2EB891C4975DD9E721DDF925816B926040D89,
	U3Cget_outputsU3Ed__27__ctor_mBFD68070C9454051A90D0AA75FB4D2FF2F63466F,
	U3Cget_outputsU3Ed__27_System_IDisposable_Dispose_m08F64F0AB9B4EFBB4D45A957869E4167332ACD41,
	U3Cget_outputsU3Ed__27_MoveNext_mA380099DD3A55548271DBAD6F80E42AA990F778C,
	U3Cget_outputsU3Ed__27_U3CU3Em__Finally1_m7E65569ADF800B81563544B8D264833E518A3C7C,
	U3Cget_outputsU3Ed__27_U3CU3Em__Finally2_m85B6B1B2A51FA070A90244EC2076C5E22F7CD920,
	U3Cget_outputsU3Ed__27_System_Collections_Generic_IEnumeratorU3CUnityEngine_Playables_PlayableBindingU3E_get_Current_m22E55FE2DC239357C1BEBBB551C332C97F221F31,
	U3Cget_outputsU3Ed__27_System_Collections_IEnumerator_Reset_m334D7D0581BE692B7764A7F29E4C53B6BD3D8350,
	U3Cget_outputsU3Ed__27_System_Collections_IEnumerator_get_Current_m2F264D817E0F1C5AE475AD9282977DEC88875D14,
	U3Cget_outputsU3Ed__27_System_Collections_Generic_IEnumerableU3CUnityEngine_Playables_PlayableBindingU3E_GetEnumerator_m22938CACD8363F1D9EF4B4B7BCC37B3ECF0554C0,
	U3Cget_outputsU3Ed__27_System_Collections_IEnumerable_GetEnumerator_m54F9DE0351DB259B654FB5C81A2B12FABB8DA0CE,
	TrackAsset_OnBeforeTrackSerialize_m5BCB0C082629BB807C2BC9F8E1623AA84EF3CDD8,
	TrackAsset_OnAfterTrackDeserialize_mF383ADAF4C3070BFB81F0478237946621E4FC850,
	TrackAsset_OnUpgradeFromVersion_m5A0523EA6441B741A60768840C9A6493DB682D22,
	TrackAsset_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_m965E10D67C9FEE0BDE0BB2887C4A24B6BDEC755B,
	TrackAsset_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_m6A13E9CC4CBECD81002067EAD4B9FF875071F0D1,
	TrackAsset_UpgradeToLatestVersion_m70FA58294D201156412BA790FD158D2029FDB505,
	TrackAsset_add_OnClipPlayableCreate_m7E859C1C550253310D9D6B34427DDAF0325AC228,
	TrackAsset_remove_OnClipPlayableCreate_mE0AE4E40FC4B75040AFEB6AF08B55FFA6F72CAEE,
	TrackAsset_add_OnTrackAnimationPlayableCreate_mCBC06DE4F00D3DFF308397D76B49D4F4C71058B0,
	TrackAsset_remove_OnTrackAnimationPlayableCreate_mA022F7131A984ECB7D002069487AC63834290FA1,
	TrackAsset_get_start_mA37EAE96A7EB745D0BC538509B78AEDEF05208E1,
	TrackAsset_get_end_m283E3BCE09D393EFA56FB26D5AD68326EECFF312,
	TrackAsset_get_duration_m3A3B2F6AD1F1945549C459B432CD4E469FAAECEF,
	TrackAsset_get_muted_mBA0D78639ED5132C019E69AC51A5BE56478C7A66,
	TrackAsset_set_muted_m9FCF9772AA42FB22761AA468792BE272258E426E,
	TrackAsset_get_mutedInHierarchy_m14257BCE9CD51D5A3C086E465F035A7707D666F7,
	TrackAsset_get_timelineAsset_m969726B43E66370FB81F1CCB6C012BCAD2B112A8,
	TrackAsset_get_parent_m4B5D7DF104388286053C4BE8D5ECB28615FE7DD9,
	TrackAsset_set_parent_mE5000EE0C72D1A0E0A6B916B7E0B94F85192D138,
	TrackAsset_GetClips_m467A7BE887049F3CC0F411AB220F488D1230FA76,
	TrackAsset_get_clips_m033A1CF810A017C6BD6F52190421C1474648BEB8,
	TrackAsset_get_isEmpty_m269392C25CDC0E0179FEFD5322476B8BEF7FC86B,
	TrackAsset_get_hasClips_m552EDF37A12F12705B5A016D9351380BF08D25ED,
	TrackAsset_get_hasCurves_m0F0AF8869F6E78DB23C129134B11A39E9CA4208E,
	TrackAsset_get_isSubTrack_mB3337FCA3D035259F1A6F5B9CFD6D9B6E2EDF05E,
	TrackAsset_get_outputs_mF8F332F69DBE3F1DF5C24DF6AFEE25B14F6ED4F7,
	TrackAsset_GetChildTracks_m34EE35A341030F99020F56267BAFC8FF4E98477C,
	TrackAsset_get_customPlayableTypename_m5E5465F06E569999A2839AAA5B29B6907C74718D,
	TrackAsset_set_customPlayableTypename_m091B3CFE1EEBAFC3508726A51254C6090CF84A4A,
	TrackAsset_get_curves_m3068225198516361CFB374B26B71E266079D1031,
	TrackAsset_set_curves_m61933C531CDE903FD6DCB11ABB1DEA36F6C844B6,
	TrackAsset_UnityEngine_Timeline_ICurvesOwner_get_defaultCurvesName_m56DD3624C90F3BDDD8AC804AD16A80B4D1D5D542,
	TrackAsset_UnityEngine_Timeline_ICurvesOwner_get_asset_mCB4DB0FF33D5C0E1A701D53C1CA54386A1DAF291,
	TrackAsset_UnityEngine_Timeline_ICurvesOwner_get_assetOwner_m9E12631082B8FD221B0C93F5EA88E8926D629537,
	TrackAsset_UnityEngine_Timeline_ICurvesOwner_get_targetTrack_m34F7F32EC8BF3AA9219D1B02A6E8BE32D2B0BD50,
	TrackAsset_get_subTracksObjects_m18E7B9EEC20905CBE5D35805FAA819CB663ECC43,
	TrackAsset_get_locked_mCEBC35262FB276860A374085F2626DCE333FC466,
	TrackAsset_set_locked_mA8190606E6AC618DCF1CF211AD648C15E67D0F87,
	TrackAsset_get_lockedInHierarchy_mB8B7847141ACB7B33320D85B456F7B7FAD155298,
	TrackAsset_get_supportsNotifications_mFAD0EDA9BAD3DDD341161C02D5DDC658F1184729,
	TrackAsset___internalAwake_mBBCB93A3A0959E0B82B795E298DCD8B3C5E67FAB,
	TrackAsset_CreateCurves_m4EA9E9F12B65A9584E97158527FA7DC7BD11BAB2,
	TrackAsset_CreateTrackMixer_m088F7E10980285D4DFD092CFDFB0A34B9AB78DAF,
	TrackAsset_CreatePlayable_mD0CEA49410876109A48E365380586AD0E5F0CEBF,
	TrackAsset_CreateDefaultClip_m1AC3502758E2185D0747626A32D27B513AC85E0F,
	NULL,
	TrackAsset_DeleteClip_mA02A503841BACD54C29CCAF816A917F1018E1FAE,
	TrackAsset_CreateMarker_mD4F5715387220B12D0EF244C7C02F83F6040638A,
	NULL,
	TrackAsset_DeleteMarker_mA491061D745EB9073AB46DABEDDA9B4BF8963347,
	TrackAsset_GetMarkers_m4FE387892A6434D0DDD3535BD974E276372B7ADA,
	TrackAsset_GetMarkerCount_mE3D948E944448243CAC284D1D358C9A889FB44B7,
	TrackAsset_GetMarker_m2CB75ED400AECE525785B9E58020CC8EF0669A0A,
	TrackAsset_CreateClip_mA7D1A7B6ACCF5CCF9FB416E86C483BD2EC31A45F,
	TrackAsset_CreateAndAddNewClipOfType_mF1CA85A2B77A5CF003685CEBBF0A7551CCFA05E9,
	TrackAsset_CreateClipOfType_m62E140A7CBDF66DE0643A1D65601E6ECCC6DA0B1,
	TrackAsset_CreateClipFromPlayableAsset_mFCC78BCEE93E36BB92F1CC13A08CA7E11592CB44,
	TrackAsset_CreateClipFromAsset_mC48C4A19A082D69011DE9A64D73F05160DD1BD55,
	TrackAsset_GetMarkersRaw_m31260E1A58B86C26BD0E4EE80F5DC22FA64FBC14,
	TrackAsset_ClearMarkers_m4F8DA7A63459F34E3561E03BB1D198D63B0435ED,
	TrackAsset_AddMarker_m9B5E8B481DA0086DB46B0FE6219231F2B8FE63E8,
	TrackAsset_DeleteMarkerRaw_mC80F55242BE94323E2C826A8C9CD6E7C8ECA7540,
	TrackAsset_GetTimeRangeHash_m6A9D09CE4BE80DBBF10AD763E66944FBB37C33D4,
	TrackAsset_AddClip_mC0AD6626726FE95A50EFEC2DAF26D9625210FE09,
	TrackAsset_CreateNotificationsPlayable_mA46A9A1F92F6DBB21E6AA080A0491AF04F326D9B,
	TrackAsset_CreatePlayableGraph_mB09B3F258784F140E7D3F33ED4029056EA220371,
	TrackAsset_CompileClips_mAC9B684ADF9F1E0AB784A283BF350D3B5AC3D7AA,
	TrackAsset_GatherCompilableTracks_m0494F6912D73193408CB13F43099A6D5F2665C14,
	TrackAsset_GatherNotifications_mF5DBDCF22D9CD5ED6FE6D868583BB6D332AD6BFA,
	TrackAsset_CreateMixerPlayableGraph_m38CCE75D2966419D1FB327209C2320F36FB19222,
	TrackAsset_ConfigureTrackAnimation_mF10DDD7B2768DEDFDB46752A7E7A43F7E149EBBC,
	TrackAsset_SortClips_m4FE3C4820022ECF050815215CEB982DE373F85D2,
	TrackAsset_ClearClipsInternal_m60A468FD1AF2A83A9C8BB65376464216878BE994,
	TrackAsset_ClearSubTracksInternal_m8638EC88D91AC3AA5F80BF38C92E16BD67D994DE,
	TrackAsset_OnClipMove_m9FB2E0FF1003CA992D3F6841D13418262C8BF341,
	TrackAsset_CreateNewClipContainerInternal_m7DD56E4426DDBA57B25BF61EAF45938324FA9F2B,
	TrackAsset_AddChild_m208FDE1AB513103FCFA9F1C8174B976E9D1DAE76,
	TrackAsset_MoveLastTrackBefore_m4619211278763606F6FE3FC90665837417BCD966,
	TrackAsset_RemoveSubTrack_m7F68C7D6AF1FB79584D154B357F47ACDF29FDAA8,
	TrackAsset_RemoveClip_m1D64D42648A148BAE62836E46FC57C25EC9D1A60,
	TrackAsset_GetEvaluationTime_m30B94204B96340B1C2810E05607E34A4DB85067F,
	TrackAsset_GetSequenceTime_m618B6BD38BE9B1136E8405B1BEDFBAFD898896A9,
	TrackAsset_GatherProperties_m09C1A335FCE1ABA158748583AF4A641FF2EBB09D,
	TrackAsset_GetGameObjectBinding_mF2D645FA74007FD6EA6B337298F939FFB4A5B853,
	TrackAsset_ValidateClipType_mD794D7B040F112C3E8ACBF9FB601414D08B97A8F,
	TrackAsset_OnCreateClip_m0FF04313EBF6CE614CAB3C124B1F4D11B0E3AF94,
	TrackAsset_UpdateDuration_mCC46D1145F6BCB2B5DB28211C23E7E12702F7035,
	TrackAsset_CalculateItemsHash_m2928153E88198C690E2CB8486AC483555276546E,
	TrackAsset_CreatePlayable_mDD98D897F11DDC7593DF7F3029ED7A1B63285D0C,
	TrackAsset_Invalidate_m3CBA531307AEE6181C9938F174AE6D1A977115BF,
	TrackAsset_GetNotificationDuration_m8F9FB2B13DC1C6CC88617A18535730D1E012FC13,
	TrackAsset_CanCompileClips_mB7B603BB1D6782D00C14752530F66C638C174505,
	TrackAsset_CanCreateTrackMixer_mA825C739B3AA34B9F0F36C7534AAC4E84073F261,
	TrackAsset_IsCompilable_mD65DF730D54F4D1F0F0E13D42A009FDF8C59F284,
	TrackAsset_UpdateChildTrackCache_mDEEB1C096FDD5B0D035CD5C699119CD913BC6BB0,
	TrackAsset_Hash_m11C239AAFB9C1FFB1035A0B0DC5271A62CA3AD78,
	TrackAsset_GetClipsHash_mF0B665CDE32E5BEA60FD6EC88F7272CEA6A83115,
	TrackAsset_GetAnimationClipHash_m9A2D605C95C224426E2263A8693E578B77633EE7,
	TrackAsset_HasNotifications_mE386A9FE718BC4C8D368E8BA5B0FFC3BC50C5E06,
	TrackAsset_CanCompileNotifications_mEA94C527EED8871F7AB1CC74C3487ABC204C708A,
	TrackAsset_CanCreateMixerRecursive_m31242CE6BCCB6DA4F24690457585789405645FFC,
	TrackAsset__ctor_mC05CAAD737449BAF26721F82EA1972843F86FE9A,
	TrackAsset__cctor_m9E9CC7F378B533931B19CC75ACD04B10793C18BA,
	TransientBuildData_Create_m4A6E221013EDF3015DB6842AC1A51882A0E74F86,
	TransientBuildData_Clear_mA1B052EF3A05C723008A58559E4C77A989ED2FD3,
	U3CU3Ec__cctor_m4CF50A8244F0E8B9535D4C7B45D8D6B2D4C3F07A,
	U3CU3Ec__ctor_m0071EE643C6662AFC30FD5F241BD4F6B05382B91,
	U3CU3Ec_U3CSortClipsU3Eb__121_0_mBD715AA0E013972CC995FFB813CBACEAFD120E71,
	U3Cget_outputsU3Ed__65__ctor_m36D55B6998402316E9C824384D2823CF2887FA99,
	U3Cget_outputsU3Ed__65_System_IDisposable_Dispose_m1E8207D4EAFDAEFB62061AD6D689431BD40999AB,
	U3Cget_outputsU3Ed__65_MoveNext_mB5F1A5655A42CB9A832A2B7B941A3DA555E65F36,
	U3Cget_outputsU3Ed__65_System_Collections_Generic_IEnumeratorU3CUnityEngine_Playables_PlayableBindingU3E_get_Current_mA6C35B64EBEC6BDA1221AC7F991E746D1729589D,
	U3Cget_outputsU3Ed__65_System_Collections_IEnumerator_Reset_m322763B2D18B71E3A120A80829AB3CBE23709A03,
	U3Cget_outputsU3Ed__65_System_Collections_IEnumerator_get_Current_mF3FCDDFE0515DC769DD6EF0A8D6ABADA7BEE9302,
	U3Cget_outputsU3Ed__65_System_Collections_Generic_IEnumerableU3CUnityEngine_Playables_PlayableBindingU3E_GetEnumerator_m1EFEC630120231AFC3D898AFCDD0756F9561543D,
	U3Cget_outputsU3Ed__65_System_Collections_IEnumerable_GetEnumerator_mD5E8220BC3E60F92FA1CFDD991151AAE34C519B2,
	TimelineHelpURLAttribute__ctor_mEB91B85CCCBEC196457E5FCB18A6C9C13E48CEBC,
	TrackColorAttribute_get_color_m5AC7558C98A60D996F1C3A9F1EB6F180FB45FFE4,
	TrackColorAttribute__ctor_mBC50DD796426AAD66A6A8BB7BE46EBB7873AA006,
	AudioClipProperties__ctor_m044736544A12AD2A937225432C158B5BDA240B83,
	AudioMixerProperties_PrepareFrame_m84E02C27BA11487134C9360A3A3F2BF90A3CDAD8,
	AudioMixerProperties__ctor_mB4EC402EBE2B6C8F3CF268598319F789AF837592,
	AudioPlayableAsset_get_bufferingTime_mE7FFA2B05A64D29C552930DFAA17A57CFE397031,
	AudioPlayableAsset_set_bufferingTime_mDA418332D841FE5667E21538BBAEC16041A07032,
	AudioPlayableAsset_get_clip_m49311E74FD58B38CC0D8ED16A0D8869F4ADAB720,
	AudioPlayableAsset_set_clip_m2A3379A7D58655D8C6B0395F262B6829FDCAD6EB,
	AudioPlayableAsset_get_loop_m579753DC7E58ACCA36A44E217C28EBF40CC3C294,
	AudioPlayableAsset_set_loop_m594EA5EE80017E2F1C15FE170CAD6F22D01EE7DD,
	AudioPlayableAsset_get_duration_m44FF0A8D8526B9EFBA604BFCDF79DB380E5B76BB,
	AudioPlayableAsset_get_outputs_m40AC20DF1E9E352D9C2EA015742D42E7F9C73E6E,
	AudioPlayableAsset_CreatePlayable_mBFEC2FDD97D15CA9376CAAF65FE217AAC6E854BB,
	AudioPlayableAsset_get_clipCaps_m7DF29DF049D9F0407E9AFD7D209AB825A8743F80,
	AudioPlayableAsset__ctor_mF10BFB16BB64A6F8D0FE62D7A0B8335ADB119A76,
	U3Cget_outputsU3Ed__16__ctor_m61F54FD6956E98C4C644EC8189368AB432C0F0D4,
	U3Cget_outputsU3Ed__16_System_IDisposable_Dispose_m72E1B27D3C90C82B4A415D51E3B2177CED2BE178,
	U3Cget_outputsU3Ed__16_MoveNext_m6D77A67836D44DDA516C67755527D4DD24E1F9A5,
	U3Cget_outputsU3Ed__16_System_Collections_Generic_IEnumeratorU3CUnityEngine_Playables_PlayableBindingU3E_get_Current_mBE6C905012CCD38CF8A2DD6A4815FDE3772370D2,
	U3Cget_outputsU3Ed__16_System_Collections_IEnumerator_Reset_m026B2E69F6C94660273E8386AC24B908344881CE,
	U3Cget_outputsU3Ed__16_System_Collections_IEnumerator_get_Current_m582D28A2EE588AF6E3ACADE39F5EE74A2B852D1B,
	U3Cget_outputsU3Ed__16_System_Collections_Generic_IEnumerableU3CUnityEngine_Playables_PlayableBindingU3E_GetEnumerator_m8038F14452BCFDB7F80F35661C0BAB7CB2201C4B,
	U3Cget_outputsU3Ed__16_System_Collections_IEnumerable_GetEnumerator_mD88200543F030A8F6F9ADFBCA257B6DB811ED84E,
	AudioTrack_CreateClip_m8CBE84BCC1FF99D6E93FD790F9BAD277F2BC6D9E,
	AudioTrack_CompileClips_m641F177F94C9BA21B517CCDBF43C25ABC605BF28,
	AudioTrack_get_outputs_m8B9BE825525351885F322DCEBB5A8FA7D6AFEE89,
	AudioTrack_OnValidate_mB46DE622AEA6652C4248854BF0DB7607FA704960,
	AudioTrack__ctor_m6EEC48668D6F9248F48B2B0686291560444B8F35,
	U3Cget_outputsU3Ed__4__ctor_m190710D72768E977B81519D942515031DCF91C88,
	U3Cget_outputsU3Ed__4_System_IDisposable_Dispose_m5824B5C4F4CA22007A3FEEE9AB75AA4BA9AEC83C,
	U3Cget_outputsU3Ed__4_MoveNext_m636D185FBF8DAF5943220DF0FD5AE6FA2C7C7999,
	U3Cget_outputsU3Ed__4_System_Collections_Generic_IEnumeratorU3CUnityEngine_Playables_PlayableBindingU3E_get_Current_m75314CB64DC2DC82417162EFFB901A50D6E60C51,
	U3Cget_outputsU3Ed__4_System_Collections_IEnumerator_Reset_mBE20566179B7912EC30C3BFFC5A8781486E25E58,
	U3Cget_outputsU3Ed__4_System_Collections_IEnumerator_get_Current_m2F2377F982B6CA15109F25D7E87D68030B432BA5,
	U3Cget_outputsU3Ed__4_System_Collections_Generic_IEnumerableU3CUnityEngine_Playables_PlayableBindingU3E_GetEnumerator_m7C348C673AFB4692FE68B667BF09E0F8C8B3E6A1,
	U3Cget_outputsU3Ed__4_System_Collections_IEnumerable_GetEnumerator_mD0AB6E0CA93EA531B030519FAB433F4F25D27CD0,
	TimelineClipCapsExtensions_SupportsLooping_m149B53ABA1636A092CD5BB849C19277D93D4B935,
	TimelineClipCapsExtensions_SupportsExtrapolation_m3C0E25676F88C9DEEF4149DC2158A4A18711EE3A,
	TimelineClipCapsExtensions_SupportsClipIn_m318FDA5D82ED5E932A5E9C195668685F4571EEF9,
	TimelineClipCapsExtensions_SupportsSpeedMultiplier_mC1AAE95266F6201D78F17EBAB47D7923341BBD30,
	TimelineClipCapsExtensions_SupportsBlending_m569C3ED6D06142794DA7B1FD6C00062A209E7885,
	TimelineClipCapsExtensions_HasAll_m2596307B9954E6FCEFDDFD4A0BE87D936DE31BF9,
	TimelineClipCapsExtensions_HasAny_mA750DF1B0964785A2FC8DC2F9A05CF03D27F1670,
	ControlPlayableAsset_get_controllingDirectors_m0370250573BBDA45A8E8B086FFDE90ED5B1961CD,
	ControlPlayableAsset_set_controllingDirectors_m8D263E063F860FDF4E832CC9C284C45D49FA9270,
	ControlPlayableAsset_get_controllingParticles_mEDF1CC5E356EA9B115EF4F35F3509ABB93F5D44A,
	ControlPlayableAsset_set_controllingParticles_m31709F8C09DB20C7551FDC488A0127295FD0B219,
	ControlPlayableAsset_OnEnable_m63F67A81F41C02520284308515022DF19FA2FBE4,
	ControlPlayableAsset_get_duration_m0C2033B0C5C3E6DE7385797DE1B07F0263521878,
	ControlPlayableAsset_get_clipCaps_m09BC8BAA1440D221483F653285DA52715346F22A,
	ControlPlayableAsset_CreatePlayable_m88A68ADB8119BC891B37E1B9CCA7FD26090914D8,
	ControlPlayableAsset_ConnectPlayablesToMixer_m8020203CFDC98E5674106B99938017E8D9E798CC,
	ControlPlayableAsset_CreateActivationPlayable_m8BC4706CA44F6216AE3C3169D03BEE15BC819111,
	ControlPlayableAsset_SearchHierarchyAndConnectParticleSystem_m6D97733D38AAE740196B5AFA9C75A45AB54114B4,
	ControlPlayableAsset_SearchHierarchyAndConnectDirector_m9AD1670D0949F2D418DBF34152170810855A18AB,
	ControlPlayableAsset_SearchHierarchyAndConnectControlableScripts_m46A0974334C5CAB121787DC91ED6C1A9E07B33BC,
	ControlPlayableAsset_ConnectMixerAndPlayable_m8727A31B27ECFA1AF0B7478B8514F3D7B27C8459,
	NULL,
	ControlPlayableAsset_GetControlableScripts_mDAC0BC709F7024BAC5F520AA31F565824AE542A2,
	ControlPlayableAsset_UpdateDurationAndLoopFlag_m3B3976CA44009F140D54203A5A696A341A815669,
	ControlPlayableAsset_GetControllableParticleSystems_m182DE87B71BF287B5D828C7D48D099C0C3B4F85A,
	ControlPlayableAsset_GetControllableParticleSystems_mB7DA5089B5C09BC5876DEA587D20550184A69BC4,
	ControlPlayableAsset_CacheSubEmitters_m823886C0C0F1C8AA13129B3A9810CDA8AAF2EE7B,
	ControlPlayableAsset_GatherProperties_mEB211D7180EBC912CAEE8C6521F67AE4AC240FDB,
	ControlPlayableAsset_PreviewParticles_m611AFD61ACF20FDD50C82D09230C32B7D0E3D94E,
	ControlPlayableAsset_PreviewActivation_m0FAEA1926A9ACFC55CE3CF14992F5A4DE1F1368E,
	ControlPlayableAsset_PreviewTimeControl_m38FFD36A41ECA2BA874D7A7DA234E15EFAB2A043,
	ControlPlayableAsset_PreviewDirectors_mA5DF7CF7A9F2ECFAE8C59FEDD944BDD4C25AE118,
	ControlPlayableAsset__ctor_mCB87B77766491D451517D1082079113798B8518A,
	ControlPlayableAsset__cctor_m1343221BA07342B50EF52CA584CAFF1508E50BA5,
	U3CGetControlableScriptsU3Ed__39__ctor_m61DC7C12C95430EF9C16D27768169F05CC5784D0,
	U3CGetControlableScriptsU3Ed__39_System_IDisposable_Dispose_mF25100DBDEB7B203BC1DD6CCBE34A94797D68A46,
	U3CGetControlableScriptsU3Ed__39_MoveNext_m0A28A750413795A869B753439AC20C1E8AC2E33A,
	U3CGetControlableScriptsU3Ed__39_System_Collections_Generic_IEnumeratorU3CUnityEngine_MonoBehaviourU3E_get_Current_m288919711BCB540205566CDF9B9339EC8EBB2DB1,
	U3CGetControlableScriptsU3Ed__39_System_Collections_IEnumerator_Reset_mBCFCCD39392B14C63C54D9079BB6E3302DC858B0,
	U3CGetControlableScriptsU3Ed__39_System_Collections_IEnumerator_get_Current_m0C00993614DC7FD44F070DD625C5325A84301309,
	U3CGetControlableScriptsU3Ed__39_System_Collections_Generic_IEnumerableU3CUnityEngine_MonoBehaviourU3E_GetEnumerator_mAC5B005DF8CED15F0652A85E921526C4E9E245C9,
	U3CGetControlableScriptsU3Ed__39_System_Collections_IEnumerable_GetEnumerator_m51F55F60727D8A0C7F5CD7F862853E8B041F1910,
	ControlTrack__ctor_m0B4C0633844F60987B07ABC4467976B46AB0975D,
	DiscreteTime_get_tickValue_m0ACC9CCCEB39D170368ACD3FD094781F6D01EAFD,
	DiscreteTime__ctor_mA6613BBA0DEB0A0BCCED50838378D9D9971B9F62,
	DiscreteTime__ctor_mF5CD806CC7273F8C5CC80053A78D09604DED0491,
	DiscreteTime__ctor_mF858F11921F841F599EF51C7975430BEA8EC3B52,
	DiscreteTime__ctor_m7D7C3098B16F74497C5FC907B364C50A667EB2C8,
	DiscreteTime__ctor_mCD4A3BE5EB3142C4247190F7896378B68F428F69,
	DiscreteTime__ctor_m5C84429A52611671EE6F2D7BCD1399335D8242F5,
	DiscreteTime_OneTickBefore_m06F6D6A227D8356FCD3283CD59571DFA12943D1A,
	DiscreteTime_OneTickAfter_mD2C58DF00885E44EA0D7AF8C9F56C14F77E53EB7,
	DiscreteTime_GetTick_m52C7EE5F63087831CF4B21EF76A00655181349AA,
	DiscreteTime_FromTicks_m39AD4CB20BF1DA744A07F93B9FCA9F0F75DE9BDD,
	DiscreteTime_CompareTo_m9A2C892A6E4097A5FBC0C2D5D666E01CE12427EA,
	DiscreteTime_Equals_m3D269E4164E6E705E6DD19B557F9DBFC2CB226FA,
	DiscreteTime_Equals_m42B27C6DDF993E36A0C3D20B708986277AE6B7FD,
	DiscreteTime_DoubleToDiscreteTime_m8D83412A88CCAC93ABD5070209109957FCA85762,
	DiscreteTime_FloatToDiscreteTime_m0F57E8699783D9B2A48381F34D2116E71F3EA559,
	DiscreteTime_IntToDiscreteTime_m17C3BF4B229B41B91CCACB7F465628A9ACA4736D,
	DiscreteTime_ToDouble_m319F0D5276BC26C56CDC124BC7878DB535351348,
	DiscreteTime_ToFloat_mA77F564AE95883062A631DAF86C4F401567CEB0E,
	DiscreteTime_op_Explicit_m801A8089D31AA0C435943A08C89A83607FACD52B,
	DiscreteTime_op_Explicit_m08D29220F020CB6A40CC9D59B5B4104BBA7599D0,
	DiscreteTime_op_Explicit_m7C11B47C356E41AB9F96ED46AE8BBFA05C1B3941,
	DiscreteTime_op_Explicit_mE186D14B06F947B0B372625D4E927566B0161874,
	DiscreteTime_op_Explicit_mCA9C09F74934D46B5635C42C9984C8C84589CD30,
	DiscreteTime_op_Implicit_m8EEF2A52EFFBFC375CB1D8171DC439A22DC35ECB,
	DiscreteTime_op_Explicit_m42F903A62DF258BD11463C461991AA5DE89AA71D,
	DiscreteTime_op_Equality_m55FD471B465FFDAC78BE2413701203194CFEADC4,
	DiscreteTime_op_Inequality_m19BD9CC31D1A2FBDA0CC50BA96EA37463F404AF6,
	DiscreteTime_op_GreaterThan_m3F023E3705B46C7E548730E10C543416EACEBC45,
	DiscreteTime_op_LessThan_m3CD38C215D6813414BFB1AC88BAF5BF1796C49D1,
	DiscreteTime_op_LessThanOrEqual_m8E8286335E92963611D6C0F6598B20E61CBE2F14,
	DiscreteTime_op_GreaterThanOrEqual_m04A7C9DC1DACEBE2FF6201F7B81C5CE59518A57D,
	DiscreteTime_op_Addition_mCE0392D2D1FB0BD53AD56C9A287810D1672B2BC8,
	DiscreteTime_op_Subtraction_mA27858E3E6CA4BBD578B1CE193504549AA916FC5,
	DiscreteTime_ToString_mFE5EEC6255B3AA617F692DA9EB7DF4A5AD71FD83,
	DiscreteTime_GetHashCode_m59E889384FD1F78DC2A1220C228EAB164F8995CE,
	DiscreteTime_Min_m6FF92F93C9C652F5794A44BCADF4C1F6D15A3140,
	DiscreteTime_Max_mE6741CA89EADA7D3EF47C29C8C9D0D57CC8597B6,
	DiscreteTime_SnapToNearestTick_mBA4C390A09ED03D90BE0D8C0CD39949AAC76AF03,
	DiscreteTime_SnapToNearestTick_m563A71A5A432C7676FC6692CF68DD81FF2020AB1,
	DiscreteTime_GetNearestTick_mC4A04B29410685B1864597BCB83664F6B2F95367,
	DiscreteTime__cctor_m23DF1F1B6C69F8785475607E6188362FE65E123F,
	InfiniteRuntimeClip__ctor_m807AE0572975FDC87883AC4877442F7E986B7812,
	InfiniteRuntimeClip_get_intervalStart_m9F26CE71EA9F461A924BF1AF6892154B8077FF8D,
	InfiniteRuntimeClip_get_intervalEnd_mB05A86E64F77260F1CC9EF60285E9985106A7D41,
	InfiniteRuntimeClip_set_enable_m950C18673BCCA53BA644B8998BE3A00FC37CBE2D,
	InfiniteRuntimeClip_EvaluateAt_m3A6147F9BB6B625BA721C1C2FCE0DFCBBDB1DF8E,
	InfiniteRuntimeClip_DisableAt_mBE37CED8ED2A4643EDC99BADD76AF0E344A4929E,
	InfiniteRuntimeClip__cctor_mE142C87FB9D4550C966F87FB581863C614E7CD8A,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	RuntimeClip_get_start_m6204EC5ADD90B46E23FFE1436928152BF15326EC,
	RuntimeClip_get_duration_mBA27B08B52BD7B402C449A7733AA7755FA987F6C,
	RuntimeClip__ctor_m98046934573D3967A198B431434524D25C76F43C,
	RuntimeClip_Create_m9464E5BA8F8A29AEA85775728362F8B72A742D10,
	RuntimeClip_get_clip_m6208BBDD36E3E3EA97000F4CEC9BB1B879A1824D,
	RuntimeClip_get_mixer_mA42F77ACA8B17C58C502EC06C7930F2033AA0589,
	RuntimeClip_get_playable_mD336A90E2A444F4B50F3B02FABC5C6ECDE3E21BE,
	RuntimeClip_set_enable_m61A322D87BF4A75D804C2C82FD59CACA871B8FA1,
	RuntimeClip_SetTime_m7ECE29A44A0DA3625276C56999A7D293CF642ED1,
	RuntimeClip_SetDuration_m4AB935A8C5F597184D3F20A714704C11458FAE40,
	RuntimeClip_EvaluateAt_m6D08332D99A261EE232EFFE9F94ADEC3B5E66BDE,
	RuntimeClip_DisableAt_mE25279C9BD90378393EC0E8CB029EDFECE7CC243,
	NULL,
	NULL,
	RuntimeClipBase_get_intervalStart_m599D96633271890B892A9D48325531F28DDE1BAD,
	RuntimeClipBase_get_intervalEnd_mA0819F921B8AA4BFEBF31419858BA2C975BE174B,
	RuntimeClipBase__ctor_m53B7986F56314C13A22B079840C7DA6D36501958,
	NULL,
	NULL,
	RuntimeElement_get_intervalBit_mD5C7E7CDA66C4B1888D529E9344679FF88DE5A70,
	RuntimeElement_set_intervalBit_mD6D67EA4A982521E16CDF3CAC295EED22CA65C75,
	NULL,
	NULL,
	NULL,
	RuntimeElement__ctor_mB4868C6FD6BE7F80F182AE2961580F1E31A60F68,
	ScheduleRuntimeClip_get_start_mC7AEF6AF42593F08CD3403B98132D3AAEDD898D8,
	ScheduleRuntimeClip_get_duration_mE5950845592C0E89A8F622204CD37D7ABCFD62D0,
	ScheduleRuntimeClip_SetTime_m38A382F75037FC1430B6067DAC94848D7043D1DF,
	ScheduleRuntimeClip_get_clip_mC24FB5C0451219E6A3CF888B81B0099A34443A3B,
	ScheduleRuntimeClip_get_mixer_m5FFC2C73903CC0B038A4F76EB1A4F99B912FC54C,
	ScheduleRuntimeClip_get_playable_mF7988FDD9350B256DD307DE9F9957AA1A44D9EED,
	ScheduleRuntimeClip__ctor_m83C75C594A568AE694E42341685277CFB517568F,
	ScheduleRuntimeClip_Create_mA8ADCDBD0B9C32427F6EB006FFB9057989981C18,
	ScheduleRuntimeClip_set_enable_mA74C59B6CDC243221219534B77FA5B140A4E5989,
	ScheduleRuntimeClip_EvaluateAt_m3C3C92A67AB06B22C1B3E22B884F78A3BA42F6D8,
	ScheduleRuntimeClip_DisableAt_m57233707606DE133E713FB3B89BB867BE65A8CA7,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	Marker_get_parent_mE72E59DAF1DBC598885CD3CA9D0A55CB68CE8510,
	Marker_set_parent_mCAB1A605E39909E42D623D48037DC43141173A34,
	Marker_get_time_mB0E90DB26C36F73A6AFB126761F1FF144A145BF6,
	Marker_set_time_mF28306664964EF628F2D6D9F9F9DB2EB7DF89DED,
	Marker_UnityEngine_Timeline_IMarker_Initialize_mA5ED18EB0857836506FD03FB6C7E04C06AC6F320,
	Marker_OnInitialize_m95F020C003B8C4C5072BF891C9539CF0D6A2F550,
	Marker__ctor_m74124BFBCDCAFE96C2A9CE59CA215B80590F6A56,
	MarkerList_get_markers_m5376FA1EA20DEC72CE47369DD8CD407869B0A2F7,
	MarkerList__ctor_m2D55037070301E459BF9CAB83756B81183C6A6B8,
	MarkerList_Add_m401FA50EA0C98293DD36147CFA9DC8F637636128,
	MarkerList_Remove_m986C1ECBCA1A1DE79FCD97D7D44EC91C19D79F64,
	MarkerList_Remove_mF1626830F54C37B15C3DBEB6044B92CBF6439790,
	MarkerList_Clear_m260B8EDEF14AC94F4BE5F10018F2215BF11927E7,
	MarkerList_Contains_m97FF1ADAEFBD92A7F3E66B0A7C85950224B62989,
	MarkerList_GetMarkers_mC440918CEEC3E0A8E872510EDEAEA0FB8832E680,
	MarkerList_get_Count_mC37C47376A0DCC203DBD965E961A923F06E0082B,
	MarkerList_get_Item_m76D3839ACDBAB9F00DFD9CE68C63DE26050961FE,
	MarkerList_GetRawMarkerList_m272F6FFA3A887E15FDEFA8AAC07F1A6A0BD54A0D,
	MarkerList_CreateMarker_m404F50BDABD541CF30E2F03DC6C22F917E02130B,
	MarkerList_HasNotifications_mF2F94CCD1C286DB7264738F287486890D2497124,
	MarkerList_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_m6E97ABA4A5ED5881C2FC7F356FCAF68FCC45B710,
	MarkerList_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_m1175CE30FB38D95504747F73994F2C86DC0E8928,
	MarkerList_BuildCache_m8EFF6B73A205DC746231385CAC726A74A2B485F5,
	MarkerTrack_get_outputs_mCC74870CF1C68FA4B6DC21EDADEDE9936A6F21AC,
	MarkerTrack__ctor_mAF73693D259F16A0F2DBF71313BA854F5C71A1E8,
	CustomSignalEventDrawer__ctor_m6E78EBC2A826450A4F05E0127A6672F07D84CC0A,
	SignalAsset_add_OnEnableCallback_m554767948986531C6B7B0C14286F651B6B7C7203,
	SignalAsset_remove_OnEnableCallback_m99D77B52734D3A480E21F6E09918CB302409CBFB,
	SignalAsset_OnEnable_mF4113987FFFF4ECE91B0DFD3506EFF02E76DE29C,
	SignalAsset__ctor_m4F0FF5113CFB816B5FF981FA2A308AEC282D6E9B,
	SignalEmitter_get_retroactive_m05405283A19896F77F109BE0E4DF0380CAE909EA,
	SignalEmitter_set_retroactive_mC90E249F02530DA76969DA20208123CCE8A11D4F,
	SignalEmitter_get_emitOnce_m8A10F3E7A7F2BF66F52B5A418632E1CCA1B613A5,
	SignalEmitter_set_emitOnce_mB64C9B47B124F8FEAE68C41BA3CED422A96E6628,
	SignalEmitter_get_asset_m80647A00BC1BE61E0FBD0F2CE626B0997E04C140,
	SignalEmitter_set_asset_mE5074F7D8760F624046A0E1B7C2D3E2662A7283C,
	SignalEmitter_UnityEngine_Playables_INotification_get_id_mFE1FFFDDE0AA375A400B8ED622D47E3C4A3462F1,
	SignalEmitter_UnityEngine_Timeline_INotificationOptionProvider_get_flags_m440E69F2CB8F13BDB1D4D082E88A8708E1FEC98C,
	SignalEmitter__ctor_m6A45F5A0D313B5184EA3D27F89DAD8126178E327,
	SignalReceiver_OnNotify_m2A3A5A1DB5A3FACAF334771A74677D1FB08FFBF4,
	SignalReceiver_AddReaction_m24F66AE62396EEC75BB4F5074246F39FDF6884B6,
	SignalReceiver_AddEmptyReaction_m8A3DAC1ED52B65292D89FD40DEAE359D29491EFC,
	SignalReceiver_Remove_mC5F9BBA8D959629B62B7A7F5B2C2E1A684E857CF,
	SignalReceiver_GetRegisteredSignals_m0834695F8B630B69EC4A606956C24ABD9FBFA953,
	SignalReceiver_GetReaction_m8B5FBD0171FB06EAE16E9A99F2E209829552EFF8,
	SignalReceiver_Count_mB3916E584C177A2D47AF76296FB0C59987A1F694,
	SignalReceiver_ChangeSignalAtIndex_m66463FEE943CC55C585628B993F64C78257CA1FD,
	SignalReceiver_RemoveAtIndex_m01EE2CBC7A406CA4A1D13F4F72D591347D02DBC5,
	SignalReceiver_ChangeReactionAtIndex_m803D743382D58A2637C8BA1FF00116310AFEC472,
	SignalReceiver_GetReactionAtIndex_m3F36C2C9E39F3F39679B4AE052305DDFFC8887A1,
	SignalReceiver_GetSignalAssetAtIndex_mAB31FED39823D7220E727A204F128AD9C0B12A82,
	SignalReceiver_OnEnable_mB77BD34EC8FE8B41A627585E4739083B7F85F3C1,
	SignalReceiver__ctor_m46381605B40F21C06F4125C7D8F45232F4D74CE6,
	EventKeyValue_TryGetValue_m7190DE6E665F07018730BAF3288F22CDFEEB9B6D,
	EventKeyValue_Append_mEE3BEB022A04AA95F1B2B56D3F8AA5F5ECCA5BD3,
	EventKeyValue_Remove_mEC52E595AEC1BFAB97DB2567AC42CE0D7C968863,
	EventKeyValue_Remove_m3B8982FC179C5DD1853FC4846106B1CD23F57C7D,
	EventKeyValue_get_signals_m3F5CB2EDFE24DEAAE67DF04898223ACBDB3CB3BD,
	EventKeyValue_get_events_mB7945E299C4249CB0BD5D2EBAB8D0DEE455FFAE3,
	EventKeyValue__ctor_m7AE5F9F6453AA2F9DB8A1612D334D1F4CB3CC549,
	SignalTrack__ctor_m92CAC598FA9027691117E52EB8247D2D79D0FBA1,
	TrackAssetExtensions_GetGroup_m9261D7FF986D8DBAB89F142B5B2F4357F1C995B9,
	TrackAssetExtensions_SetGroup_m0525D6627035D09A0943ADC00AAA03CC68495435,
	GroupTrack_CanCompileClips_mF95369ECAEA83FA55A283FB1425F00FBCE9D2F23,
	GroupTrack_get_outputs_m1CF0F26206E1943E4C4202F7849408E9E8CA2403,
	GroupTrack__ctor_m2612CD9E072D91E3F5C21F8D4F156F96A33CDBBC,
	NULL,
	ActivationControlPlayable_Create_m61DD26626E7A44339EA08D2E15498E1897FAC23E,
	ActivationControlPlayable_OnBehaviourPlay_m32D87ABF400DD235012889AEB82EBD1757AE745C,
	ActivationControlPlayable_OnBehaviourPause_mB2E17A057A698FA2366269460A306B281808748F,
	ActivationControlPlayable_ProcessFrame_mC388A38FD777597B3E79B41A5DA4286DCA0E73BE,
	ActivationControlPlayable_OnGraphStart_mF0475EF758222C98681FAB8A1E175C4A0095AE31,
	ActivationControlPlayable_OnPlayableDestroy_mF39699D518A1C2075AB6BCD1ECB1996704A28838,
	ActivationControlPlayable__ctor_m50572F236B93063098D17B06E2662F10A75F5E0C,
	BasicPlayableBehaviour_get_duration_m000CA8660FB9136C46ABD05B9ACB9B7BE28C1F95,
	BasicPlayableBehaviour_get_outputs_mF90ACBACF585238FF0623BD70C14483DF91D21AE,
	BasicPlayableBehaviour_OnGraphStart_m921FCF14B857F191628EBB43D79A8CB674EAB8AA,
	BasicPlayableBehaviour_OnGraphStop_m261E6FFF9FCCA3380C3B1844ADE470605B616FFC,
	BasicPlayableBehaviour_OnPlayableCreate_m956EA471E10F9B20B77365A935A1C473E5DAD25D,
	BasicPlayableBehaviour_OnPlayableDestroy_mE70F15932202ABBF11E850199EB2DADFC6B5955E,
	BasicPlayableBehaviour_OnBehaviourPlay_m1F24D0A73D669B74C455DB02363EF2BB473FBF65,
	BasicPlayableBehaviour_OnBehaviourPause_m2372D83D9E18CF06F9121017970D8295412E216D,
	BasicPlayableBehaviour_PrepareFrame_mE55DA9150027CA503D1495E3E50A9568EAA1F5E7,
	BasicPlayableBehaviour_ProcessFrame_m413A95F400514A22EF4630952D0498DBC5347ED2,
	BasicPlayableBehaviour_CreatePlayable_m0F957E711BFF407616E35025646C83FA4DD90C25,
	BasicPlayableBehaviour__ctor_mF8CCC76427C0B3A8E0F88C4B1853A80E5EA33C1F,
	DirectorControlPlayable_Create_mA64246490438157CCBE867DF9CFD0F0CD7133DE6,
	DirectorControlPlayable_OnPlayableDestroy_m9426CD97470CA7619A971B2A0E8942BC6BE7AD19,
	DirectorControlPlayable_PrepareFrame_m0F8F6FBBB5483EBB76332AE013321FC6201893E9,
	DirectorControlPlayable_OnBehaviourPlay_m452302B8000575C35BF262260D72F789CA03DD60,
	DirectorControlPlayable_OnBehaviourPause_m2DDA62A73F0C95CA0B220637599FD2DBD224E632,
	DirectorControlPlayable_ProcessFrame_mAC2E676C1932306AE076F222A074E37BFAA8B6E7,
	DirectorControlPlayable_SyncSpeed_m39C659CC0373FBC0829B8343F49F44E852729734,
	DirectorControlPlayable_SyncStart_m07109F4B30C707D4B2450C00A0675192A69260E2,
	DirectorControlPlayable_SyncStop_mB49F56038E68681AB73BA59A96E3A2F3348868DB,
	DirectorControlPlayable_DetectDiscontinuity_m5884117E980A5F64A18127E0CC14BD64B12B286D,
	DirectorControlPlayable_DetectOutOfSync_mAEB0E3DA34B2A5C7F7BBE40144198F9762D07370,
	DirectorControlPlayable_UpdateTime_mF24AE595B9899ABED3928F15FAD03CBBB5CBE809,
	DirectorControlPlayable__ctor_m89F5CEC0EBACD98DFF7D480BEFEA15DD637F870C,
	NULL,
	NULL,
	NULL,
	ParticleControlPlayable_Create_m8F03536B0CC6B66B506836058AA37A08EDCD5338,
	ParticleControlPlayable_get_particleSystem_m63954D25FBB179CA655D5739DCB07C7DBA99C7A5,
	ParticleControlPlayable_set_particleSystem_m6B3EED45B18C3B80D641D969D2601ABBE34BA4C1,
	ParticleControlPlayable_Initialize_mCA4BB21915939852339DCECDE34EDB1B4ED36E92,
	ParticleControlPlayable_SetRandomSeed_m0D41AD36E12BF013CE346F2A562A717721FB87F9,
	ParticleControlPlayable_PrepareFrame_m959C915531B064E7A7AC30B01E8299B21A139EF6,
	ParticleControlPlayable_OnBehaviourPlay_mB1B514A71494493FB7A2B8B1F6B5F1455907B761,
	ParticleControlPlayable_OnBehaviourPause_m295DA59C6C8E31854EA9009A709FFFAF663428B9,
	ParticleControlPlayable_Simulate_mCDD453B5F1A231F1FD9744B19EDE4386E4B3D7C6,
	ParticleControlPlayable__ctor_m0E1086350E57B8CA51874D8AE9FDFF1B0AB89C66,
	PrefabControlPlayable_Create_mE744622E91538A0C13C1D453F207A32956F35297,
	PrefabControlPlayable_get_prefabInstance_mE06FADC9D1367D2606277D5D1F8577C6CA837B4A,
	PrefabControlPlayable_Initialize_mC8F44778C03DAEFFE8F22B14216C6E838E224483,
	PrefabControlPlayable_OnPlayableDestroy_mC7498F993D6766BC8F1963BC770DACBE7AC09DCA,
	PrefabControlPlayable_OnBehaviourPlay_m25045FBF8EF4CE540108857ED00A5E8CF44EC66D,
	PrefabControlPlayable_OnBehaviourPause_m9015806A18FE1AF828ED2757E7F83BD499C3C09F,
	PrefabControlPlayable_SetHideFlagsRecursive_mCA466033B6F14AC5C663F848791C0CC6C6B92716,
	PrefabControlPlayable__ctor_m0850EAD5A2F49DAF2B6D56326ED235D8042C9C90,
	TimeControlPlayable_Create_m45018C6B659C011B90908D65862ADF68E9F9F251,
	TimeControlPlayable_Initialize_m7CE2820058BE601A316CCE5964E4CE3445E9980A,
	TimeControlPlayable_PrepareFrame_mF122A36FEFFA9BE1BC74FB551225652874D82E83,
	TimeControlPlayable_OnBehaviourPlay_mBFF77E285E53483F14E06EE0B56CFECD7CE13C31,
	TimeControlPlayable_OnBehaviourPause_m9648D5058F2EC8BFC260AC603B5C03883AD284E1,
	TimeControlPlayable__ctor_m2324993C8F5009CC0336BB9D9396F453529CD5C7,
	TimeNotificationBehaviour_set_timeSource_mD0096011A303EB4C84B3DE3AAE908C51955E2F8E,
	TimeNotificationBehaviour_Create_mF24D48476C110D3158F32421C168A5171F4613A0,
	TimeNotificationBehaviour_AddNotification_mB502CDA1135E3A3F543B7B24224BB95F986EAD97,
	TimeNotificationBehaviour_OnGraphStart_m8C925120572F3A69DA5E9B3DB127F1C506CE436E,
	TimeNotificationBehaviour_OnBehaviourPause_m6FCF029B2259978A231F78DEC1E4F57E2D88E7C1,
	TimeNotificationBehaviour_PrepareFrame_m8C38D54B9B061C4B57098EA0458E768974E193DE,
	TimeNotificationBehaviour_SortNotifications_m91493C483D46116C8DEE5C25AEE2F9872B3DB86C,
	TimeNotificationBehaviour_CanRestoreNotification_m7390D6FA66D1786D1AF1412D37D74FD9EE661E2E,
	TimeNotificationBehaviour_TriggerNotificationsInRange_m9607548147744987224ADD3FE6E3F339D2337284,
	TimeNotificationBehaviour_SyncDurationWithExternalSource_mEE92F71E0867E713E58EDF71B9DEE1A1E2F25925,
	TimeNotificationBehaviour_Trigger_internal_m87169DADFCE06BC37BA28CFED3B0A6174DFFEAB7,
	TimeNotificationBehaviour_Restore_internal_m25E1C8121200E214969CDD9A753D6357D440726A,
	TimeNotificationBehaviour__ctor_m82EC1218499AA0C697D1005358DA66DB1908FFAC,
	NotificationEntry_get_triggerInEditor_m0F467B8FC45EDED62344F2EB66D91998C330FC7A,
	NotificationEntry_get_prewarm_mDF7D568CD5A14E53E9E11DAF6AD33F0FE7ED29DE,
	NotificationEntry_get_triggerOnce_m40147F02406594EFEA328E29EDC9C3110E9A7654,
	U3CU3Ec__cctor_m6669D2494CB2B140029B26B2BE183C4A8DEDB476,
	U3CU3Ec__ctor_m8358614F1AC3F7F79522CD7795964B99E574C428,
	U3CU3Ec_U3CSortNotificationsU3Eb__12_0_mCFEAA6C512A546FDC4CC69A23EF75844DFDB9189,
	PlayableTrack_OnCreateClip_mB016C731FC0C8567F707C53BAD5B9E9B99DE2BE7,
	PlayableTrack__ctor_m1ADC6FA394F1E5B1A484480F7306E35A401A2A22,
	TrackMediaType__ctor_m1357FFFF3A466DC18E97A85F4DF7577A145FFED1,
	TrackClipTypeAttribute__ctor_m32F3A6AF3F0CCD2AC80A08276A2C84DAD6B59ADA,
	TrackClipTypeAttribute__ctor_m6A7E59068246D7C95D43A99B54403748FE2E5F5F,
	NotKeyableAttribute__ctor_m11E8B4B00459AEB31D3345A5A8045E5D21CC35CB,
	TrackBindingTypeAttribute__ctor_m905B5F0071EF43101C6DE52F5383F8B15FF66176,
	TrackBindingTypeAttribute__ctor_m38D2D803DD43575FBB29F858067DF10911CF7D95,
	SupportsChildTracksAttribute__ctor_m3FE00570A7899A3F7CF0FB8522DE0C4C21694E51,
	IgnoreOnPlayableTrackAttribute__ctor_mAC076C2CED5738D08949C3F1BEB855D761191297,
	TimeFieldAttribute_get_useEditMode_m029ADEC4C39E6BC43084274528B66A956BDD1E15,
	TimeFieldAttribute__ctor_m91C16D6CAE58639D71BE206FFA6011A56BC9A53B,
	FrameRateFieldAttribute__ctor_m1312B76429B8D8A394C3C28BC001E22877F3ABB2,
	HideInMenuAttribute__ctor_m3F3AFC914006D5CFC30B844C14E2E5BE671C1C20,
	CustomStyleAttribute__ctor_m171564751F00A5A77E810C78FAB06AF76EFEAE23,
	MenuCategoryAttribute__ctor_m0AE9C26FDCD04C039FA1F3995969A6822B9957C6,
	NULL,
	NULL,
	TimelinePlayable_Create_mDBD16A025F217757ED3E323A2B2292B836B7849D,
	TimelinePlayable_Compile_mCCE0D60E8AD0D45A14341C0FC4FA055633BE7D25,
	TimelinePlayable_CompileTrackList_m94B0B3D833D78C5A070DD587705272E8D25EBB63,
	TimelinePlayable_CreateTrackOutput_mAD0C4882569BA478C8D59AA5130F8901BADE5DCF,
	TimelinePlayable_EvaluateWeightsForAnimationPlayableOutput_m7684913A2AF5D8265AE45AAAF46A9B006C028040,
	TimelinePlayable_EvaluateAnimationPreviewUpdateCallback_m55098F133C54B87A45ABAF36903EAE57CDAA16F4,
	TimelinePlayable_CreateTrackPlayable_m18539F554343F8FF12D84137E5A0CA8FFA7E8B34,
	TimelinePlayable_PrepareFrame_m9124521CCE2CBF49C19EA1669DE2BAE6632C5153,
	TimelinePlayable_Evaluate_m1CB17400BD2E1FE1757F8A106F67451D039C325F,
	TimelinePlayable_CacheTrack_m8C7FF43B821AC07FED21AD8C1537815FA0113B1C,
	TimelinePlayable_ForAOTCompilationOnly_m6130D5C91CE6D75036FBE258FC54A70A89345C20,
	TimelinePlayable__ctor_m9A86459D9944225AE074C58C85B7F798E089FD5C,
	TimelinePlayable__cctor_m0800C83345E9CC0DED4AB906A15ADE0859A1123D,
	Extrapolation_CalculateExtrapolationTimes_m607FBFE8FBB128DA7AC236DA75A9097CA959BEE7,
	Extrapolation_SortClipsByStartTime_mCDB181CE76C4633A881347AB015409999918BF0F,
	Extrapolation__cctor_mEED3F9AE8423558EE03C4A949B2954DD93925066,
	U3CU3Ec__cctor_m68412E65388EDDBD5C698C3B363320588FC91F3F,
	U3CU3Ec__ctor_m4BEA68C8BAF3AD6FA1854F77A8584537339195A3,
	U3CU3Ec_U3CSortClipsByStartTimeU3Eb__2_0_mAC857C3158459F813EB90B7EC80CC57E002F4562,
	HashUtility_CombineHash_mDB3BFB2F0222F9DFEA5A43CC152AF988732681DE,
	HashUtility_CombineHash_mCD4724834FE836194D0AABEAF856DA6C625A73F6,
	HashUtility_CombineHash_m288F5449B09EB88B46E520229DFF7C7F18CA51C0,
	HashUtility_CombineHash_mBB79C3AEE25C2E5895BA6F85AECCFF5B215005B6,
	HashUtility_CombineHash_mCA939EC20F8AE7BBABD2D433D3A1FB770AC30BF7,
	HashUtility_CombineHash_m1367FE812F3C9670BD59ECF0007F2383C469A29A,
	HashUtility_CombineHash_m8C98B06FEB8B88A93104DE6E8D02B30470718E5A,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NotificationUtilities_CreateNotificationsPlayable_mF665AFFEFDFDBD2572948D0A9998348CC268A28E,
	NotificationUtilities_CreateNotificationsPlayable_mEBCA07E594A6E0979666BF7C3397ACC7A5D4753E,
	NotificationUtilities_CreateNotificationsPlayable_m3173F7D8999AA856892F6556F20735C8B24AB231,
	NotificationUtilities_TrackTypeSupportsNotifications_mA844D8C5CD9E01E806EFA9A16F37BD7575DB6012,
	TimelineClipExtensions_MoveToTrack_m7B9992AD171A490F566871C4856C71E6DC1D28D9,
	TimelineClipExtensions_TryMoveToTrack_m88EB39C25E4B43EF0A5F6DE62A576B6D3F6AE958,
	TimelineClipExtensions_MoveToTrack_Impl_mA57D81A0076D26140DF918D05C680BF0591F50BB,
	TimelineClipExtensions__cctor_mB59452B0FD743296682255CC66A9B41704B2EA3C,
	TimelineCreateUtilities_GenerateUniqueActorName_mA256C1FED6CBEA0A7A41C3CD66E3617D70E7C554,
	TimelineCreateUtilities_SaveAssetIntoObject_m08F1B9275C4A4893711EA4281BBB3BBBEA9EEE0A,
	TimelineCreateUtilities_RemoveAssetFromObject_mDDE3FBE78C7CBC5F771632F41076E68108475013,
	TimelineCreateUtilities_CreateAnimationClipForTrack_m499C0BAB55D23B8B1759B4EFFCA6EC100B056770,
	TimelineCreateUtilities_ValidateParentTrack_mDE431017F5F40FDE73B7CDFACADDD1F484316CE6,
	U3CU3Ec__DisplayClass0_0__ctor_m8C4942004C3562D0E7CEF1A8EBA62FE27E649328,
	U3CU3Ec__DisplayClass0_0_U3CGenerateUniqueActorNameU3Eb__0_m06DEF88A9AE88C0A9FEA42106BBB9D0E79B92D50,
	U3CU3Ec__DisplayClass0_1__ctor_mE96B2A914FE0820EA130DFB44B1E713AD3FBFB90,
	U3CU3Ec__DisplayClass0_1_U3CGenerateUniqueActorNameU3Eb__1_m63691DF652C67A4727D3AD671C58FA56CA3FAEDB,
	TimelineUndo_get_undoEnabled_mB97DE8A738C3CBAB1B3BC7607D78EF45BD173AF1,
	TimelineUndo_PushDestroyUndo_m502EE75014700236190DF20E9B5743E100E4E840,
	TimelineUndo_PushUndo_mDAB595E45185F92C279C990AE284325133967B04,
	TimelineUndo_PushUndo_m1FD04DF7E2F50E65198221CDDB7B770C566037AE,
	TimelineUndo_RegisterCreatedObjectUndo_mDB8ECC4BFD7A3491605CA1F89CE349ED35750365,
	TimelineUndo_UndoName_m0E19A1C9B54C2A0735D35FF4F8DFC92EDA12E192,
	TimeUtility_ValidateFrameRate_mFDD6870A06AB0E87193D1152E370161AF173992D,
	TimeUtility_ToFrames_m42B1DBF623EBD722B8B393C34A3F5FF33D5F6188,
	TimeUtility_ToExactFrames_m442908215ADBB76C41A4CC2C031320379E50095C,
	TimeUtility_FromFrames_m0D428831B080A1E23EEF38CD7F15461D7FD651CC,
	TimeUtility_FromFrames_m90B7D5F87C01001AE4B9F712E8B7F517CAAF91F8,
	TimeUtility_OnFrameBoundary_mE89A73D9D109EFCAAB028DEA3A545276EC8A3A68,
	TimeUtility_GetEpsilon_m2DDCC59890F0A007D609E29CD1BC62B04EF6FB7B,
	TimeUtility_PreviousFrame_m28CFEC3FC57D3B1EC62729D95CE831F6A7126FE7,
	TimeUtility_NextFrame_mA59CE7A5E228C54649536FB4E6D29D83B5226BD4,
	TimeUtility_PreviousFrameTime_mD8E8E61C5D3DDA489AF1FA8A24CEC01504A21098,
	TimeUtility_NextFrameTime_mAAD5C5D9755F54EB1214A083894ED5DC4BE63C0E,
	TimeUtility_OnFrameBoundary_mAC72C8A3F1E3E3F05F03BE07CDCAD5DE7808421B,
	TimeUtility_RoundToFrame_m9A9CF7FB93ABCBE05A4987D766A6640B5FCE02BD,
	TimeUtility_TimeAsFrames_mD7686EA3126E3A791789F999B9B9E25B6F2178AD,
	TimeUtility_TimeAsTimeCode_m448D4FC6FD14FD5816A4DA71F82218C4121ADFA1,
	TimeUtility_ParseTimeCode_mC51B6AEBBABD417C83090B0A8C1DFE5BE133A438,
	TimeUtility_ParseTimeSeconds_m28AB4715C13959B4AB0829540082001D158335DC,
	TimeUtility_GetAnimationClipLength_m0C35066397FC1474ED4B04934DBAB064CC1891DB,
	TimeUtility_RemoveChar_mB4D81436DF7CAC6C2768E463808F0CDB4D4D7D91,
	TimeUtility_GetClosestFrameRate_m777D1AA22EBF63B3F327B4744C8F304F5E3F59C9,
	TimeUtility_ToFrameRate_mB8BD26B2A1C5BF1DBAC8B55BB3CCB4D86294E1A4,
	TimeUtility_ToStandardFrameRate_mE55BCB25BFF3ED034F50D98E31D2595C0C8D8C90,
	TimeUtility__cctor_m49E229AEEAC66C8DE40593C47BBC3F9552CBDA1B,
	U3CU3Ec__cctor_m59AEBAC840298E7D2900A2B3BF20699F650D2477,
	U3CU3Ec__ctor_m56DDC30169ADA8554EE8A0BC9D347AD024BD36A7,
	U3CU3Ec_U3CParseTimeCodeU3Eb__19_0_m53BDD117A1B72C2F098A9DA0ACA32170E2BDD953,
	U3CU3Ec_U3CParseTimeCodeU3Eb__19_1_m0A1AEDE68065D8D17AB8C877AD0A248E91AD316C,
	U3CU3Ec_U3CParseTimeSecondsU3Eb__20_0_m263DF6B0C61B2D416F0CBE1BB77E39AA7ED3BF00,
	WeightUtility_NormalizeMixer_m6EE0D0701870481B80B9B8AA1F259C1A786379CA,
};
extern void TransientBuildData_Clear_mA1B052EF3A05C723008A58559E4C77A989ED2FD3_AdjustorThunk (void);
extern void DiscreteTime__ctor_mA6613BBA0DEB0A0BCCED50838378D9D9971B9F62_AdjustorThunk (void);
extern void DiscreteTime__ctor_mF5CD806CC7273F8C5CC80053A78D09604DED0491_AdjustorThunk (void);
extern void DiscreteTime__ctor_mF858F11921F841F599EF51C7975430BEA8EC3B52_AdjustorThunk (void);
extern void DiscreteTime__ctor_m7D7C3098B16F74497C5FC907B364C50A667EB2C8_AdjustorThunk (void);
extern void DiscreteTime__ctor_mCD4A3BE5EB3142C4247190F7896378B68F428F69_AdjustorThunk (void);
extern void DiscreteTime__ctor_m5C84429A52611671EE6F2D7BCD1399335D8242F5_AdjustorThunk (void);
extern void DiscreteTime_OneTickBefore_m06F6D6A227D8356FCD3283CD59571DFA12943D1A_AdjustorThunk (void);
extern void DiscreteTime_OneTickAfter_mD2C58DF00885E44EA0D7AF8C9F56C14F77E53EB7_AdjustorThunk (void);
extern void DiscreteTime_GetTick_m52C7EE5F63087831CF4B21EF76A00655181349AA_AdjustorThunk (void);
extern void DiscreteTime_CompareTo_m9A2C892A6E4097A5FBC0C2D5D666E01CE12427EA_AdjustorThunk (void);
extern void DiscreteTime_Equals_m3D269E4164E6E705E6DD19B557F9DBFC2CB226FA_AdjustorThunk (void);
extern void DiscreteTime_Equals_m42B27C6DDF993E36A0C3D20B708986277AE6B7FD_AdjustorThunk (void);
extern void DiscreteTime_ToString_mFE5EEC6255B3AA617F692DA9EB7DF4A5AD71FD83_AdjustorThunk (void);
extern void DiscreteTime_GetHashCode_m59E889384FD1F78DC2A1220C228EAB164F8995CE_AdjustorThunk (void);
extern void MarkerList_get_markers_m5376FA1EA20DEC72CE47369DD8CD407869B0A2F7_AdjustorThunk (void);
extern void MarkerList__ctor_m2D55037070301E459BF9CAB83756B81183C6A6B8_AdjustorThunk (void);
extern void MarkerList_Add_m401FA50EA0C98293DD36147CFA9DC8F637636128_AdjustorThunk (void);
extern void MarkerList_Remove_m986C1ECBCA1A1DE79FCD97D7D44EC91C19D79F64_AdjustorThunk (void);
extern void MarkerList_Remove_mF1626830F54C37B15C3DBEB6044B92CBF6439790_AdjustorThunk (void);
extern void MarkerList_Clear_m260B8EDEF14AC94F4BE5F10018F2215BF11927E7_AdjustorThunk (void);
extern void MarkerList_Contains_m97FF1ADAEFBD92A7F3E66B0A7C85950224B62989_AdjustorThunk (void);
extern void MarkerList_GetMarkers_mC440918CEEC3E0A8E872510EDEAEA0FB8832E680_AdjustorThunk (void);
extern void MarkerList_get_Count_mC37C47376A0DCC203DBD965E961A923F06E0082B_AdjustorThunk (void);
extern void MarkerList_get_Item_m76D3839ACDBAB9F00DFD9CE68C63DE26050961FE_AdjustorThunk (void);
extern void MarkerList_GetRawMarkerList_m272F6FFA3A887E15FDEFA8AAC07F1A6A0BD54A0D_AdjustorThunk (void);
extern void MarkerList_CreateMarker_m404F50BDABD541CF30E2F03DC6C22F917E02130B_AdjustorThunk (void);
extern void MarkerList_HasNotifications_mF2F94CCD1C286DB7264738F287486890D2497124_AdjustorThunk (void);
extern void MarkerList_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_m6E97ABA4A5ED5881C2FC7F356FCAF68FCC45B710_AdjustorThunk (void);
extern void MarkerList_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_m1175CE30FB38D95504747F73994F2C86DC0E8928_AdjustorThunk (void);
extern void MarkerList_BuildCache_m8EFF6B73A205DC746231385CAC726A74A2B485F5_AdjustorThunk (void);
extern void NotificationEntry_get_triggerInEditor_m0F467B8FC45EDED62344F2EB66D91998C330FC7A_AdjustorThunk (void);
extern void NotificationEntry_get_prewarm_mDF7D568CD5A14E53E9E11DAF6AD33F0FE7ED29DE_AdjustorThunk (void);
extern void NotificationEntry_get_triggerOnce_m40147F02406594EFEA328E29EDC9C3110E9A7654_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[34] = 
{
	{ 0x060001B2, TransientBuildData_Clear_mA1B052EF3A05C723008A58559E4C77A989ED2FD3_AdjustorThunk },
	{ 0x06000210, DiscreteTime__ctor_mA6613BBA0DEB0A0BCCED50838378D9D9971B9F62_AdjustorThunk },
	{ 0x06000211, DiscreteTime__ctor_mF5CD806CC7273F8C5CC80053A78D09604DED0491_AdjustorThunk },
	{ 0x06000212, DiscreteTime__ctor_mF858F11921F841F599EF51C7975430BEA8EC3B52_AdjustorThunk },
	{ 0x06000213, DiscreteTime__ctor_m7D7C3098B16F74497C5FC907B364C50A667EB2C8_AdjustorThunk },
	{ 0x06000214, DiscreteTime__ctor_mCD4A3BE5EB3142C4247190F7896378B68F428F69_AdjustorThunk },
	{ 0x06000215, DiscreteTime__ctor_m5C84429A52611671EE6F2D7BCD1399335D8242F5_AdjustorThunk },
	{ 0x06000216, DiscreteTime_OneTickBefore_m06F6D6A227D8356FCD3283CD59571DFA12943D1A_AdjustorThunk },
	{ 0x06000217, DiscreteTime_OneTickAfter_mD2C58DF00885E44EA0D7AF8C9F56C14F77E53EB7_AdjustorThunk },
	{ 0x06000218, DiscreteTime_GetTick_m52C7EE5F63087831CF4B21EF76A00655181349AA_AdjustorThunk },
	{ 0x0600021A, DiscreteTime_CompareTo_m9A2C892A6E4097A5FBC0C2D5D666E01CE12427EA_AdjustorThunk },
	{ 0x0600021B, DiscreteTime_Equals_m3D269E4164E6E705E6DD19B557F9DBFC2CB226FA_AdjustorThunk },
	{ 0x0600021C, DiscreteTime_Equals_m42B27C6DDF993E36A0C3D20B708986277AE6B7FD_AdjustorThunk },
	{ 0x06000231, DiscreteTime_ToString_mFE5EEC6255B3AA617F692DA9EB7DF4A5AD71FD83_AdjustorThunk },
	{ 0x06000232, DiscreteTime_GetHashCode_m59E889384FD1F78DC2A1220C228EAB164F8995CE_AdjustorThunk },
	{ 0x0600027E, MarkerList_get_markers_m5376FA1EA20DEC72CE47369DD8CD407869B0A2F7_AdjustorThunk },
	{ 0x0600027F, MarkerList__ctor_m2D55037070301E459BF9CAB83756B81183C6A6B8_AdjustorThunk },
	{ 0x06000280, MarkerList_Add_m401FA50EA0C98293DD36147CFA9DC8F637636128_AdjustorThunk },
	{ 0x06000281, MarkerList_Remove_m986C1ECBCA1A1DE79FCD97D7D44EC91C19D79F64_AdjustorThunk },
	{ 0x06000282, MarkerList_Remove_mF1626830F54C37B15C3DBEB6044B92CBF6439790_AdjustorThunk },
	{ 0x06000283, MarkerList_Clear_m260B8EDEF14AC94F4BE5F10018F2215BF11927E7_AdjustorThunk },
	{ 0x06000284, MarkerList_Contains_m97FF1ADAEFBD92A7F3E66B0A7C85950224B62989_AdjustorThunk },
	{ 0x06000285, MarkerList_GetMarkers_mC440918CEEC3E0A8E872510EDEAEA0FB8832E680_AdjustorThunk },
	{ 0x06000286, MarkerList_get_Count_mC37C47376A0DCC203DBD965E961A923F06E0082B_AdjustorThunk },
	{ 0x06000287, MarkerList_get_Item_m76D3839ACDBAB9F00DFD9CE68C63DE26050961FE_AdjustorThunk },
	{ 0x06000288, MarkerList_GetRawMarkerList_m272F6FFA3A887E15FDEFA8AAC07F1A6A0BD54A0D_AdjustorThunk },
	{ 0x06000289, MarkerList_CreateMarker_m404F50BDABD541CF30E2F03DC6C22F917E02130B_AdjustorThunk },
	{ 0x0600028A, MarkerList_HasNotifications_mF2F94CCD1C286DB7264738F287486890D2497124_AdjustorThunk },
	{ 0x0600028B, MarkerList_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_m6E97ABA4A5ED5881C2FC7F356FCAF68FCC45B710_AdjustorThunk },
	{ 0x0600028C, MarkerList_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_m1175CE30FB38D95504747F73994F2C86DC0E8928_AdjustorThunk },
	{ 0x0600028D, MarkerList_BuildCache_m8EFF6B73A205DC746231385CAC726A74A2B485F5_AdjustorThunk },
	{ 0x06000302, NotificationEntry_get_triggerInEditor_m0F467B8FC45EDED62344F2EB66D91998C330FC7A_AdjustorThunk },
	{ 0x06000303, NotificationEntry_get_prewarm_mDF7D568CD5A14E53E9E11DAF6AD33F0FE7ED29DE_AdjustorThunk },
	{ 0x06000304, NotificationEntry_get_triggerOnce_m40147F02406594EFEA328E29EDC9C3110E9A7654_AdjustorThunk },
};
static const int32_t s_InvokerIndices[885] = 
{
	11828,
	7776,
	9999,
	7618,
	6176,
	6219,
	1758,
	7776,
	7618,
	2672,
	7776,
	7540,
	7618,
	6176,
	1479,
	7776,
	3435,
	6213,
	7776,
	6079,
	7776,
	1759,
	7776,
	7767,
	6309,
	7676,
	6229,
	7767,
	6309,
	7540,
	6094,
	7618,
	6176,
	7540,
	6094,
	7540,
	6094,
	7618,
	6176,
	7540,
	7618,
	6176,
	7656,
	6213,
	7566,
	7656,
	2672,
	7999,
	10142,
	11093,
	7618,
	7776,
	3435,
	11096,
	7776,
	7776,
	6176,
	7776,
	11802,
	11578,
	6176,
	7776,
	7540,
	7664,
	7776,
	7656,
	7656,
	7656,
	6079,
	7776,
	7776,
	10140,
	10368,
	11802,
	7767,
	6309,
	7676,
	6229,
	7767,
	6309,
	7540,
	6094,
	7618,
	6176,
	7618,
	6176,
	7656,
	6213,
	7540,
	6094,
	7656,
	6213,
	7540,
	6094,
	7540,
	7656,
	7540,
	7767,
	6309,
	7676,
	6229,
	7767,
	6309,
	7540,
	6094,
	7566,
	6125,
	7618,
	6176,
	7618,
	6176,
	7618,
	6176,
	7776,
	5461,
	6213,
	5461,
	6213,
	7618,
	7776,
	487,
	1479,
	1480,
	7618,
	1761,
	993,
	1955,
	11093,
	4450,
	5461,
	9265,
	988,
	991,
	2802,
	2802,
	3435,
	3435,
	6213,
	2326,
	2019,
	5461,
	7540,
	10481,
	7767,
	6309,
	7676,
	6229,
	7767,
	6309,
	7618,
	6176,
	7618,
	6176,
	6176,
	7776,
	11802,
	11578,
	11578,
	11578,
	6176,
	7776,
	7540,
	7664,
	7776,
	7656,
	7656,
	7656,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	7776,
	6213,
	7540,
	7540,
	7566,
	6125,
	7566,
	6125,
	7566,
	6125,
	7566,
	7566,
	6125,
	7656,
	6213,
	7566,
	7656,
	6213,
	7656,
	7540,
	7656,
	6213,
	7656,
	7656,
	7656,
	6213,
	7656,
	6213,
	7656,
	6213,
	7566,
	6125,
	7566,
	6125,
	7566,
	7566,
	7566,
	6125,
	7566,
	6125,
	7618,
	6176,
	7618,
	6176,
	7540,
	7540,
	7656,
	6213,
	7707,
	7566,
	7656,
	6213,
	7566,
	7566,
	7707,
	7540,
	6094,
	7656,
	7618,
	7618,
	5561,
	5561,
	11754,
	11754,
	4766,
	4766,
	4766,
	7656,
	10314,
	7618,
	6176,
	7618,
	6176,
	6125,
	6125,
	4370,
	4370,
	4370,
	7566,
	7566,
	9377,
	6213,
	7776,
	7776,
	7656,
	7776,
	10314,
	2835,
	11802,
	11578,
	7776,
	7656,
	7566,
	7566,
	6125,
	7618,
	6176,
	7656,
	7618,
	7618,
	7618,
	7776,
	5455,
	7656,
	5455,
	7656,
	11152,
	7776,
	7776,
	7656,
	7656,
	7656,
	6213,
	6213,
	2672,
	7776,
	7776,
	7776,
	3435,
	7776,
	7776,
	7776,
	7565,
	10777,
	1426,
	0,
	0,
	0,
	4450,
	4450,
	6213,
	1426,
	6213,
	6213,
	7776,
	7707,
	6253,
	7566,
	6125,
	6176,
	7540,
	6094,
	7776,
	11802,
	6176,
	7776,
	7540,
	7776,
	7776,
	7664,
	7776,
	7656,
	7656,
	7656,
	7776,
	7776,
	6176,
	7776,
	7776,
	7776,
	11578,
	11578,
	11578,
	11578,
	7566,
	7566,
	7566,
	7540,
	6094,
	7540,
	7656,
	7656,
	6213,
	7656,
	7656,
	7540,
	7540,
	7540,
	7540,
	7656,
	7656,
	7656,
	6213,
	7656,
	6213,
	7656,
	7656,
	7656,
	7656,
	7656,
	7540,
	6094,
	7540,
	7540,
	7776,
	6213,
	1479,
	2672,
	7656,
	0,
	4450,
	2589,
	0,
	4450,
	7656,
	7618,
	5455,
	5461,
	5461,
	5461,
	5461,
	5461,
	7656,
	7776,
	6213,
	4450,
	7618,
	6213,
	992,
	990,
	989,
	6213,
	6213,
	1480,
	1733,
	7776,
	7776,
	7776,
	7776,
	7656,
	6213,
	6213,
	4450,
	6213,
	2802,
	2802,
	3435,
	5461,
	4450,
	6213,
	7776,
	7618,
	1480,
	7776,
	7566,
	7540,
	7540,
	7540,
	7776,
	7618,
	7618,
	11213,
	7540,
	7540,
	7540,
	7776,
	11802,
	11822,
	7776,
	11802,
	7776,
	2329,
	6176,
	7776,
	7540,
	7664,
	7776,
	7656,
	7656,
	7656,
	6213,
	7542,
	1769,
	7776,
	3473,
	7776,
	7707,
	6253,
	7656,
	6213,
	7540,
	6094,
	7566,
	7656,
	2672,
	7618,
	7776,
	6176,
	7776,
	7540,
	7664,
	7776,
	7656,
	7656,
	7656,
	5461,
	989,
	7656,
	7776,
	7776,
	6176,
	7776,
	7540,
	7664,
	7776,
	7656,
	7656,
	7656,
	11096,
	11096,
	11096,
	11096,
	11096,
	10140,
	10140,
	7540,
	6094,
	7540,
	6094,
	7776,
	7566,
	7618,
	2672,
	10528,
	1741,
	1741,
	1168,
	9935,
	9184,
	0,
	11351,
	3435,
	5461,
	9929,
	10792,
	3435,
	10792,
	10792,
	9929,
	10792,
	7776,
	11802,
	6176,
	7776,
	7540,
	7656,
	7776,
	7656,
	7656,
	7656,
	7776,
	11732,
	6124,
	6177,
	6125,
	6253,
	6176,
	3111,
	7565,
	7565,
	7619,
	11145,
	5164,
	4369,
	4450,
	11227,
	11234,
	11229,
	11155,
	11454,
	11151,
	11450,
	11226,
	11143,
	11146,
	11144,
	11145,
	10115,
	10115,
	10115,
	10115,
	10115,
	10115,
	10311,
	10311,
	7656,
	7618,
	10311,
	10311,
	11152,
	11459,
	11227,
	11802,
	6219,
	7619,
	7619,
	6094,
	2836,
	1608,
	11802,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	7566,
	7566,
	1740,
	1740,
	7656,
	7663,
	7663,
	6094,
	6125,
	6125,
	2836,
	1608,
	0,
	0,
	7619,
	7619,
	7776,
	0,
	0,
	7618,
	6176,
	0,
	0,
	0,
	7776,
	7566,
	7566,
	6125,
	7656,
	7663,
	7663,
	572,
	572,
	6094,
	2836,
	1608,
	0,
	0,
	0,
	0,
	0,
	7656,
	6213,
	7566,
	6125,
	6213,
	6213,
	7776,
	7656,
	6176,
	6213,
	4450,
	1297,
	7776,
	4450,
	7656,
	7618,
	5455,
	7656,
	1415,
	7540,
	7776,
	7776,
	7776,
	7656,
	7776,
	7776,
	11578,
	11578,
	7776,
	7776,
	7540,
	6094,
	7540,
	6094,
	7656,
	6213,
	7675,
	7617,
	7776,
	1760,
	3435,
	5164,
	6213,
	7656,
	5461,
	7618,
	3173,
	6176,
	3173,
	5455,
	5455,
	7776,
	7776,
	1999,
	3435,
	6176,
	6213,
	7656,
	7656,
	7776,
	7776,
	11351,
	10792,
	7540,
	7656,
	7776,
	0,
	9250,
	3473,
	3473,
	1758,
	6219,
	6219,
	7776,
	7566,
	7656,
	6219,
	6219,
	6219,
	6219,
	3473,
	3473,
	3473,
	1758,
	2672,
	7776,
	10000,
	6219,
	3473,
	3473,
	3473,
	1758,
	6125,
	3474,
	3474,
	2036,
	4457,
	6219,
	7776,
	0,
	0,
	0,
	9252,
	7656,
	6213,
	3446,
	10799,
	3473,
	3473,
	3473,
	3493,
	7776,
	9253,
	7656,
	2592,
	6219,
	3473,
	3473,
	11578,
	7776,
	10002,
	6213,
	3473,
	3473,
	3473,
	7776,
	6219,
	9254,
	1610,
	6219,
	3473,
	3473,
	7776,
	8713,
	501,
	6219,
	9955,
	11558,
	7776,
	7540,
	7540,
	7540,
	11802,
	7776,
	2454,
	6213,
	7776,
	6176,
	6213,
	3418,
	7776,
	6213,
	3430,
	3430,
	7776,
	7618,
	6176,
	7776,
	7776,
	6213,
	6213,
	0,
	0,
	8195,
	312,
	579,
	578,
	3416,
	3416,
	488,
	3473,
	3473,
	1167,
	11802,
	7776,
	11802,
	11578,
	11351,
	11802,
	11802,
	7776,
	2329,
	10368,
	9417,
	8751,
	8250,
	8094,
	8021,
	11213,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	9255,
	9255,
	8628,
	11096,
	10792,
	10162,
	9165,
	11802,
	10481,
	10792,
	10792,
	9542,
	10162,
	7776,
	4450,
	7776,
	4450,
	11724,
	9929,
	10792,
	10792,
	10792,
	11351,
	11567,
	10365,
	10314,
	10316,
	10314,
	10116,
	10314,
	10365,
	10365,
	10314,
	10314,
	9299,
	10314,
	9502,
	9502,
	9382,
	9382,
	11157,
	10481,
	11171,
	11172,
	10122,
	11802,
	11802,
	7776,
	4552,
	4552,
	4552,
	11457,
};
static const Il2CppTokenRangePair s_rgctxIndices[7] = 
{
	{ 0x0200003B, { 14, 21 } },
	{ 0x0600012D, { 0, 2 } },
	{ 0x0600012E, { 2, 2 } },
	{ 0x0600012F, { 4, 2 } },
	{ 0x06000177, { 6, 1 } },
	{ 0x0600017A, { 7, 2 } },
	{ 0x060001F9, { 9, 5 } },
};
extern const uint32_t g_rgctx_T_t4F48D32C1E4625B636F11ED1885363AA741A5046;
extern const uint32_t g_rgctx_T_t4F48D32C1E4625B636F11ED1885363AA741A5046;
extern const uint32_t g_rgctx_T_t732DF6F42B3C639217FA152C6E8D4FBDA2B3002E;
extern const uint32_t g_rgctx_T_t732DF6F42B3C639217FA152C6E8D4FBDA2B3002E;
extern const uint32_t g_rgctx_T_t480A926730D3A28EF9ECE9D8D79A8DB512EB7754;
extern const uint32_t g_rgctx_T_t480A926730D3A28EF9ECE9D8D79A8DB512EB7754;
extern const uint32_t g_rgctx_T_t9832385973A3EB9D96FE5E2790FF45442C77D6A9;
extern const uint32_t g_rgctx_T_tD2104479CD2F140BE83C8868F7EE97C2A9E53995;
extern const uint32_t g_rgctx_T_tD2104479CD2F140BE83C8868F7EE97C2A9E53995;
extern const uint32_t g_rgctx_List_1_tAD9DBB7BDE3B42EC3C56D93670D2D6CAEEDF9578;
extern const uint32_t g_rgctx_List_1__ctor_m1E34F738C6B5185DA9FF6D9A4248F95E4789C49F;
extern const uint32_t g_rgctx_GameObject_GetComponentsInChildren_TisT_t9134924AD4E7BC6F3582AEC4CA36B7C5AC61FA66_m5232098D970480BC9A464746926D056B36E9795F;
extern const uint32_t g_rgctx_GameObject_GetComponents_TisT_t9134924AD4E7BC6F3582AEC4CA36B7C5AC61FA66_m308523092FF6ED5A1656DAB4CFEB1A612823C426;
extern const uint32_t g_rgctx_IList_1_t34811725E30193E8BFCDE584AE6EE66FCFCF383B;
extern const uint32_t g_rgctx_IntervalTree_1_t1B06BF198AFDC06DCD0731C6BD6A9AD1AC55B0FE;
extern const uint32_t g_rgctx_T_t053A107E867E3D8222FB71CF0C88EBAB22227F7B;
extern const uint32_t g_rgctx_List_1_tE2C44929BD9BB68129C86E5DDC9FBAFC630E57D6;
extern const uint32_t g_rgctx_Entry_t6F8CC2AB7F3B3405B6034C79867428AAA7B1DDC3;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t053A107E867E3D8222FB71CF0C88EBAB22227F7B_IInterval_get_intervalStart_m75AB28EF8036396E98453DBAA806435F99F35B40;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t053A107E867E3D8222FB71CF0C88EBAB22227F7B_IInterval_get_intervalEnd_m24B1F744B434ABA16174C6B9DDCF352DD8343CBA;
extern const uint32_t g_rgctx_List_1_Add_m83B97DD971361719E1C334A5189B347BE7945151;
extern const uint32_t g_rgctx_IntervalTree_1_set_dirty_m8BDA66BB88FD28A517136C7F8A7213BACD2403D3;
extern const uint32_t g_rgctx_List_1_get_Count_m41B978D0FD028DC1663D3154429E07AE7839E790;
extern const uint32_t g_rgctx_IntervalTree_1_get_dirty_m9722685FB044E0DD9DCD6B82CF77C6BB1D34EFF3;
extern const uint32_t g_rgctx_IntervalTree_1_Rebuild_mF3F832F8E3FE3A7C670154D8EC3B2297406B6706;
extern const uint32_t g_rgctx_List_1_tB14B3D5B8D94A8EB8EA9575345F99EFAD2A36B41;
extern const uint32_t g_rgctx_IntervalTree_1_Query_mD8C8CFF57210BA3DF42738B21D2D0A200CE51FD9;
extern const uint32_t g_rgctx_IntervalTree_1_QueryRange_m84E84E5B3AF621D082ED6B2037174FA5ADAA654C;
extern const uint32_t g_rgctx_List_1_get_Item_mF18FAA34A7F4F2F4481999AC4C521EFCD33F3CDD;
extern const uint32_t g_rgctx_List_1_set_Item_m919FD10B9B1EB10EA65B1C6562ADDF7C508E5223;
extern const uint32_t g_rgctx_List_1_Add_mDC5EF750DF195A5F845D3FAC5EDEE73E2D63E6F1;
extern const uint32_t g_rgctx_List_1_get_Capacity_m1C43465AC495490CB38ED647CE049FAB7537A15B;
extern const uint32_t g_rgctx_IntervalTree_1_Rebuild_mF3EB594B6C2E8D62DB87A40C0B7BBA84795010D6;
extern const uint32_t g_rgctx_List_1_Clear_m7A85665AFF0C6EA2D8F53E78A18E025173414D8B;
extern const uint32_t g_rgctx_List_1__ctor_m9D0FAC1D82CC29F417002AF02CF7D49AF48F5E3D;
static const Il2CppRGCTXDefinition s_rgctxValues[35] = 
{
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t4F48D32C1E4625B636F11ED1885363AA741A5046 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t4F48D32C1E4625B636F11ED1885363AA741A5046 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t732DF6F42B3C639217FA152C6E8D4FBDA2B3002E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t732DF6F42B3C639217FA152C6E8D4FBDA2B3002E },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t480A926730D3A28EF9ECE9D8D79A8DB512EB7754 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t480A926730D3A28EF9ECE9D8D79A8DB512EB7754 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t9832385973A3EB9D96FE5E2790FF45442C77D6A9 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tD2104479CD2F140BE83C8868F7EE97C2A9E53995 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tD2104479CD2F140BE83C8868F7EE97C2A9E53995 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tAD9DBB7BDE3B42EC3C56D93670D2D6CAEEDF9578 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1__ctor_m1E34F738C6B5185DA9FF6D9A4248F95E4789C49F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_GetComponentsInChildren_TisT_t9134924AD4E7BC6F3582AEC4CA36B7C5AC61FA66_m5232098D970480BC9A464746926D056B36E9795F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_GetComponents_TisT_t9134924AD4E7BC6F3582AEC4CA36B7C5AC61FA66_m308523092FF6ED5A1656DAB4CFEB1A612823C426 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IList_1_t34811725E30193E8BFCDE584AE6EE66FCFCF383B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IntervalTree_1_t1B06BF198AFDC06DCD0731C6BD6A9AD1AC55B0FE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t053A107E867E3D8222FB71CF0C88EBAB22227F7B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tE2C44929BD9BB68129C86E5DDC9FBAFC630E57D6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Entry_t6F8CC2AB7F3B3405B6034C79867428AAA7B1DDC3 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t053A107E867E3D8222FB71CF0C88EBAB22227F7B_IInterval_get_intervalStart_m75AB28EF8036396E98453DBAA806435F99F35B40 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t053A107E867E3D8222FB71CF0C88EBAB22227F7B_IInterval_get_intervalEnd_m24B1F744B434ABA16174C6B9DDCF352DD8343CBA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Add_m83B97DD971361719E1C334A5189B347BE7945151 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IntervalTree_1_set_dirty_m8BDA66BB88FD28A517136C7F8A7213BACD2403D3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Count_m41B978D0FD028DC1663D3154429E07AE7839E790 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IntervalTree_1_get_dirty_m9722685FB044E0DD9DCD6B82CF77C6BB1D34EFF3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IntervalTree_1_Rebuild_mF3F832F8E3FE3A7C670154D8EC3B2297406B6706 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tB14B3D5B8D94A8EB8EA9575345F99EFAD2A36B41 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IntervalTree_1_Query_mD8C8CFF57210BA3DF42738B21D2D0A200CE51FD9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IntervalTree_1_QueryRange_m84E84E5B3AF621D082ED6B2037174FA5ADAA654C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Item_mF18FAA34A7F4F2F4481999AC4C521EFCD33F3CDD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_set_Item_m919FD10B9B1EB10EA65B1C6562ADDF7C508E5223 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Add_mDC5EF750DF195A5F845D3FAC5EDEE73E2D63E6F1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Capacity_m1C43465AC495490CB38ED647CE049FAB7537A15B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IntervalTree_1_Rebuild_mF3EB594B6C2E8D62DB87A40C0B7BBA84795010D6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Clear_m7A85665AFF0C6EA2D8F53E78A18E025173414D8B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1__ctor_m9D0FAC1D82CC29F417002AF02CF7D49AF48F5E3D },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_Timeline_CodeGenModule;
const Il2CppCodeGenModule g_Unity_Timeline_CodeGenModule = 
{
	"Unity.Timeline.dll",
	885,
	s_methodPointers,
	34,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	7,
	s_rgctxIndices,
	35,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
