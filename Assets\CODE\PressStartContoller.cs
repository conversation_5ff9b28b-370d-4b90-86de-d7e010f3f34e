using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.Events;


public class PressStartContoller : MonoBehaviour
{

    public TextMeshProUGUI helpText;

    public MeshRenderer planeMeshFloor;

    public GameObject button;

    public static UnityEvent onStartButtonPressed = new UnityEvent();


    void Awake()
    {
        helpText.gameObject.SetActive(true);

        button.SetActive(true);

        planeMeshFloor.enabled = true;

    }



    public void ButtonPressToStart()
    {
        Debug.Log("Button pressed!");


        helpText.gameObject.SetActive(false);
        // Invoke the event - any subscribers will be notified
        onStartButtonPressed.Invoke();

        button.SetActive(false);

        planeMeshFloor.enabled = false;

    }

        


    private void Update()
    {
      
    }
}