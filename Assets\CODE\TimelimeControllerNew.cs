using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Playables;

public class TimelimeControllerNew : MonoBehaviour
{
    public PlayableDirector thisPlayerDirector;

    public GameObject pulseAnim;


    void OnEnable()
    {
        // Subscribe to the event
        Debug.Log("TimelimeControllerNew: Subscribing to events");
        PressStartContollerNew.onStartButtonPressedNew.AddListener(StartTimeline);

        PressStartContollerNew.onReplayButton.AddListener(ReplayTimeline);

        PressStartContollerNew.onPauseButton.AddListener(PauseTimeline);

        PressStartContollerNew.onUnPauseButton.AddListener(UnPauseTimeline);
    }

    void OnDisable()
    {
        // Always unsubscribe when disabled
        PressStartContollerNew.onStartButtonPressedNew.RemoveListener(StartTimeline);

        PressStartContollerNew.onReplayButton.RemoveListener(ReplayTimeline);

        PressStartContollerNew.onPauseButton.RemoveListener(PauseTimeline);

        PressStartContollerNew.onUnPauseButton.RemoveListener(UnPauseTimeline);
    }

    public void Start()
    {
        pulseAnim.SetActive(false);
    }


    public void StartTimeline()
    {
        Debug.Log("StartTimeline method called!");

        if (thisPlayerDirector == null)
        {
            Debug.LogError("thisPlayerDirector is null! Please assign a PlayableDirector in the inspector.");
            return;
        }

        if (thisPlayerDirector.playableAsset == null)
        {
            Debug.LogError("PlayableDirector has no timeline asset assigned!");
            return;
        }

        Debug.Log("Playing timeline: " + thisPlayerDirector.playableAsset.name);
        thisPlayerDirector.Play();
    }

    public void ReplayTimeline()
    {
        Debug.Log("ReplayTimeline method called!");
        if (thisPlayerDirector != null)
        {
            thisPlayerDirector.time = 0;  
            thisPlayerDirector.Play();    
            Debug.Log("Timeline restarted from beginning");
        }
    }


    public void PauseTimeline()
    {
        Debug.Log("PauseTimeline method called!");
        if (thisPlayerDirector != null)
        {
            thisPlayerDirector.Pause();
            Debug.Log("Timeline paused");
        }
    }

    public void UnPauseTimeline()
    {
        Debug.Log("UnPauseTimeline method called!");
        if (thisPlayerDirector != null)
        {
            thisPlayerDirector.Resume();
            Debug.Log("Timeline resumed");
        }
    }
}
