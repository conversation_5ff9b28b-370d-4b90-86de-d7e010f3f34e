using UnityEngine;

public class FaceCamera : MonoBehaviour
{
    private Camera mainCamera;

    // Optional settings
    [Tooltip("If true, object will only rotate around Y axis (billboarding)")]
    public bool lockYAxisOnly = true;

    [Tooltip("If true, will use camera forward direction instead of looking directly at camera")]
    public bool useForwardDirection = false;

    void Start()
    {
        // Find the main camera
        mainCamera = Camera.main;

        // If no main camera is tagged, give a warning
        if (mainCamera == null)
        {
            Debug.LogWarning("No main camera found. Please tag a camera as MainCamera.");
        }
    }

    void Update()
    {
        if (mainCamera == null)
            return;

        if (useForwardDirection)
        {
            // Use camera's forward direction
            if (lockYAxisOnly)
            {
                // Only rotate around Y axis
                Vector3 direction = mainCamera.transform.forward;
                direction.y = 0; // Remove vertical component

                if (direction != Vector3.zero) // Avoid "look rotation viewing vector is zero" error
                    transform.rotation = Quaternion.LookRotation(direction);
            }
            else
            {
                // Full rotation to match camera direction
                transform.rotation = mainCamera.transform.rotation;
            }
        }
        else
        {
            // Look directly at camera position
            Vector3 directionToCamera = mainCamera.transform.position - transform.position;

            if (lockYAxisOnly)
            {
                // Only rotate around Y axis (billboard effect)
                directionToCamera.y = 0; // Remove vertical component

                if (directionToCamera != Vector3.zero) // Avoid "look rotation viewing vector is zero" error
                    transform.rotation = Quaternion.LookRotation(directionToCamera);
            }
            else
            {
                // Full rotation to face camera
                transform.LookAt(mainCamera.transform);
            }
        }
    }
}